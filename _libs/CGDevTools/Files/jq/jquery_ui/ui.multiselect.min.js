(function(b){b.widget("ui.multiselectV1",{_init:function(){this.element.hide();this.id=this.element.attr("id");this.container=b('<div class="ui-multiselectV1 ui-helper-clearfix ui-widget"></div>').insertAfter(this.element);this.count=0;this.selectedContainer=b('<div class="selected"></div>').appendTo(this.container);this.availableContainer=b('<div class="available"></div>').appendTo(this.container);this.selectedActions=b('<div class="actions ui-widget-header ui-helper-clearfix"><span class="count">0 '+
b.ui.multiselectV1.locale.itemsCount+'</span><a href="#" class="remove-all">'+b.ui.multiselectV1.locale.removeAll+"</a></div>").appendTo(this.selectedContainer);this.availableActions=b('<div class="actions ui-widget-header ui-helper-clearfix"><input type="text" class="search empty ui-widget-content ui-corner-all"/><a href="#" class="add-all">'+b.ui.multiselectV1.locale.addAll+"</a></div>").appendTo(this.availableContainer);this.selectedList=b('<ul class="selected connected-list"><li class="ui-helper-hidden-accessible"></li></ul>').bind("selectstart",
function(){return!1}).appendTo(this.selectedContainer);this.availableList=b('<ul class="available connected-list"><li class="ui-helper-hidden-accessible"></li></ul>').bind("selectstart",function(){return!1}).appendTo(this.availableContainer);var a=this;this.container.width(this.element.width()+1);this.selectedContainer.width(Math.floor(this.element.width()*this.options.dividerLocation));this.availableContainer.width(Math.floor(this.element.width()*(1-this.options.dividerLocation)));this.selectedList.height(Math.max(this.element.height()-
this.selectedActions.height(),1));this.availableList.height(Math.max(this.element.height()-this.availableActions.height(),1));this.options.animated||(this.options.show="show",this.options.hide="hide");this._populateLists(this.element.find("option"));this.options.sortable&&b("ul.selected").sortable({placeholder:"ui-state-highlight",axis:"y",update:function(c,d){a.selectedList.find("li").each(function(){b(this).data("optionLink")&&b(this).data("optionLink").remove().appendTo(a.element)})},receive:function(c,
d){d.item.data("optionLink").attr("selected",!0);a.count+=1;a._updateCount();a.selectedList.children(".ui-draggable").each(function(){b(this).removeClass("ui-draggable");b(this).data("optionLink",d.item.data("optionLink"));b(this).data("idx",d.item.data("idx"));a._applyItemState(b(this),!0)});setTimeout(function(){d.item.remove()},1)}});this.options.searchable?this._registerSearchEvents(this.availableContainer.find("input.search")):b(".search").hide();b(".remove-all").click(function(){a._populateLists(a.element.find("option").removeAttr("selected"));
return!1});b(".add-all").click(function(){a._populateLists(a.element.find("option").attr("selected","selected"));return!1})},destroy:function(){this.element.show();this.container.remove();b.widget.prototype.destroy.apply(this,arguments)},_populateLists:function(a){this.selectedList.children(".ui-element").remove();this.availableList.children(".ui-element").remove();this.count=0;var c=this;b(a.map(function(a){var b=c._getOptionNode(this).appendTo(this.selected?c.selectedList:c.availableList).show();
this.selected&&(c.count+=1);c._applyItemState(b,this.selected);b.data("idx",a);return b[0]}));this._updateCount()},_updateCount:function(){this.selectedContainer.find("span.count").text(this.count+" "+b.ui.multiselectV1.locale.itemsCount)},_getOptionNode:function(a){a=b(a);var c=b('<li class="ui-state-default ui-element" title="'+a.text()+'"><span class="ui-icon"/>'+a.text()+'<a href="#" class="action"><span class="ui-corner-all ui-icon"/></a></li>').hide();c.data("optionLink",a);return c},_cloneWithData:function(a){var b=
a.clone();b.data("optionLink",a.data("optionLink"));b.data("idx",a.data("idx"));return b},_setSelected:function(a,c){a.data("optionLink").attr("selected",c);if(c){var d=this._cloneWithData(a);a[this.options.hide](this.options.animated,function(){b(this).remove()});d.appendTo(this.selectedList).hide()[this.options.show](this.options.animated);this._applyItemState(d,!0);return d}var e=this.availableList.find("li"),g=this.options.nodeComparator,d=null,f=a.data("idx"),h=g(a,b(e[f]));if(h)for(;0<=f&&f<
e.length;){if(0<h?f++:f--,h!=g(a,b(e[f]))){d=e[0<h?f:f+1];break}}else d=e[f];e=this._cloneWithData(a);d?e.insertBefore(b(d)):e.appendTo(this.availableList);a[this.options.hide](this.options.animated,function(){b(this).remove()});e.hide()[this.options.show](this.options.animated);this._applyItemState(e,!1);return e},_applyItemState:function(a,b){b?(this.options.sortable?a.children("span").addClass("ui-icon-arrowthick-2-n-s").removeClass("ui-helper-hidden").addClass("ui-icon"):a.children("span").removeClass("ui-icon-arrowthick-2-n-s").addClass("ui-helper-hidden").removeClass("ui-icon"),
a.find("a.action span").addClass("ui-icon-minus").removeClass("ui-icon-plus"),this._registerRemoveEvents(a.find("a.action"))):(a.children("span").removeClass("ui-icon-arrowthick-2-n-s").addClass("ui-helper-hidden").removeClass("ui-icon"),a.find("a.action span").addClass("ui-icon-plus").removeClass("ui-icon-minus"),this._registerAddEvents(a.find("a.action")));this._registerHoverEvents(a)},_filter:function(a){var c=b(this),d=a.children("li");a=d.map(function(){return b(this).text().toLowerCase()});
var e=b.trim(c.val().toLowerCase()),g=[];e?(d.hide(),a.each(function(a){-1<this.indexOf(e)&&g.push(a)}),b.each(g,function(){b(d[this]).show()})):d.show()},_registerHoverEvents:function(a){a.removeClass("ui-state-hover");a.mouseover(function(){b(this).addClass("ui-state-hover")});a.mouseout(function(){b(this).removeClass("ui-state-hover")})},_registerAddEvents:function(a){var c=this;a.click(function(){c._setSelected(b(this).parent(),!0);c.count+=1;c._updateCount();return!1}).each(function(){b(this).parent().draggable({connectToSortable:"ul.selected",
helper:function(){var a=c._cloneWithData(b(this)).width(b(this).width()-50);a.width(b(this).width());return a},appendTo:".ui-multiselectV1",containment:".ui-multiselectV1",revert:"invalid"})})},_registerRemoveEvents:function(a){var c=this;a.click(function(){c._setSelected(b(this).parent(),!1);c.count-=1;c._updateCount();return!1})},_registerSearchEvents:function(a){var c=this;a.focus(function(){b(this).addClass("ui-state-active")}).blur(function(){b(this).removeClass("ui-state-active")}).keypress(function(a){if(13==
a.keyCode)return!1}).keyup(function(){c._filter.apply(this,[c.availableList])})}});b.extend(b.ui.multiselectV1,{defaults:{sortable:!0,searchable:!0,animated:"fast",show:"slideDown",hide:"slideUp",dividerLocation:0.6,nodeComparator:function(a,b){var d=a.text(),e=b.text();return d==e?0:d<e?-1:1}},locale:{addAll:"Add all",removeAll:"Remove all",itemsCount:"items selected"}})})(jQuery);
