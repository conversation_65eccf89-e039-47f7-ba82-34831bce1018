(function(a,p){function c(d,c){var f=d.nodeName.toLowerCase();if("area"===f){var f=d.parentNode,e=f.name;if(!d.href||!e||"map"!==f.nodeName.toLowerCase())return!1;f=a("img[usemap=#"+e+"]")[0];return!!f&&b(f)}return(/input|select|textarea|button|object/.test(f)?!d.disabled:"a"==f?d.href||c:c)&&b(d)}function b(d){return!a(d).parents().andSelf().filter(function(){return"hidden"===a.curCSS(this,"visibility")||a.expr.filters.hidden(this)}).length}a.ui=a.ui||{};a.ui.version||(a.extend(a.ui,{version:"1.8.23",
keyCode:{ALT:18,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91}}),a.fn.extend({propAttr:a.fn.prop||a.fn.attr,_focus:a.fn.focus,focus:function(d,b){return"number"===typeof d?this.each(function(){var c=
this;setTimeout(function(){a(c).focus();b&&b.call(c)},d)}):this._focus.apply(this,arguments)},scrollParent:function(){var d;d=a.browser.msie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?this.parents().filter(function(){return/(relative|absolute|fixed)/.test(a.curCSS(this,"position",1))&&/(auto|scroll)/.test(a.curCSS(this,"overflow",1)+a.curCSS(this,"overflow-y",1)+a.curCSS(this,"overflow-x",1))}).eq(0):this.parents().filter(function(){return/(auto|scroll)/.test(a.curCSS(this,
"overflow",1)+a.curCSS(this,"overflow-y",1)+a.curCSS(this,"overflow-x",1))}).eq(0);return/fixed/.test(this.css("position"))||!d.length?a(document):d},zIndex:function(d){if(d!==p)return this.css("zIndex",d);if(this.length){d=a(this[0]);for(var b;d.length&&d[0]!==document;){b=d.css("position");if("absolute"===b||"relative"===b||"fixed"===b)if(b=parseInt(d.css("zIndex"),10),!isNaN(b)&&0!==b)return b;d=d.parent()}}return 0},disableSelection:function(){return this.bind((a.support.selectstart?"selectstart":
"mousedown")+".ui-disableSelection",function(a){a.preventDefault()})},enableSelection:function(){return this.unbind(".ui-disableSelection")}}),a("<a>").outerWidth(1).jquery||a.each(["Width","Height"],function(d,b){function c(d,b,h,f){a.each(e,function(){b-=parseFloat(a.curCSS(d,"padding"+this,!0))||0;h&&(b-=parseFloat(a.curCSS(d,"border"+this+"Width",!0))||0);f&&(b-=parseFloat(a.curCSS(d,"margin"+this,!0))||0)});return b}var e="Width"===b?["Left","Right"]:["Top","Bottom"],h=b.toLowerCase(),k={innerWidth:a.fn.innerWidth,
innerHeight:a.fn.innerHeight,outerWidth:a.fn.outerWidth,outerHeight:a.fn.outerHeight};a.fn["inner"+b]=function(e){return e===p?k["inner"+b].call(this):this.each(function(){a(this).css(h,c(this,e)+"px")})};a.fn["outer"+b]=function(e,d){return"number"!==typeof e?k["outer"+b].call(this,e):this.each(function(){a(this).css(h,c(this,e,!0,d)+"px")})}}),a.extend(a.expr[":"],{data:a.expr.createPseudo?a.expr.createPseudo(function(d){return function(b){return!!a.data(b,d)}}):function(d,b,c){return!!a.data(d,
c[3])},focusable:function(d){return c(d,!isNaN(a.attr(d,"tabindex")))},tabbable:function(d){var b=a.attr(d,"tabindex"),f=isNaN(b);return(f||0<=b)&&c(d,!f)}}),a(function(){var d=document.body,b=d.appendChild(b=document.createElement("div"));b.offsetHeight;a.extend(b.style,{minHeight:"100px",height:"auto",padding:0,borderWidth:0});a.support.minHeight=100===b.offsetHeight;a.support.selectstart="onselectstart"in b;d.removeChild(b).style.display="none"}),a.curCSS||(a.curCSS=a.css),a.extend(a.ui,{plugin:{add:function(d,
b,c){d=a.ui[d].prototype;for(var e in c)d.plugins[e]=d.plugins[e]||[],d.plugins[e].push([b,c[e]])},call:function(a,b,c){if((b=a.plugins[b])&&a.element[0].parentNode)for(var e=0;e<b.length;e++)a.options[b[e][0]]&&b[e][1].apply(a.element,c)}},contains:function(a,b){return document.compareDocumentPosition?a.compareDocumentPosition(b)&16:a!==b&&a.contains(b)},hasScroll:function(d,b){if("hidden"===a(d).css("overflow"))return!1;var c=b&&"left"===b?"scrollLeft":"scrollTop",e=!1;if(0<d[c])return!0;d[c]=1;
e=0<d[c];d[c]=0;return e},isOverAxis:function(a,b,c){return a>b&&a<b+c},isOver:function(d,b,c,e,h,k){return a.ui.isOverAxis(d,c,h)&&a.ui.isOverAxis(b,e,k)}}))})(jQuery);
(function(a,p){if(a.cleanData){var c=a.cleanData;a.cleanData=function(d){for(var b=0,f;null!=(f=d[b]);b++)try{a(f).triggerHandler("remove")}catch(e){}c(d)}}else{var b=a.fn.remove;a.fn.remove=function(d,c){return this.each(function(){c||d&&!a.filter(d,[this]).length||a("*",this).add([this]).each(function(){try{a(this).triggerHandler("remove")}catch(d){}});return b.call(a(this),d,c)})}}a.widget=function(d,b,c){var e=d.split(".")[0],h;d=d.split(".")[1];h=e+"-"+d;c||(c=b,b=a.Widget);a.expr[":"][h]=function(e){return!!a.data(e,
d)};a[e]=a[e]||{};a[e][d]=function(a,e){arguments.length&&this._createWidget(a,e)};b=new b;b.options=a.extend(!0,{},b.options);a[e][d].prototype=a.extend(!0,b,{namespace:e,widgetName:d,widgetEventPrefix:a[e][d].prototype.widgetEventPrefix||d,widgetBaseClass:h},c);a.widget.bridge(d,a[e][d])};a.widget.bridge=function(d,b){a.fn[d]=function(c){var e="string"===typeof c,h=Array.prototype.slice.call(arguments,1),k=this;c=!e&&h.length?a.extend.apply(null,[!0,c].concat(h)):c;if(e&&"_"===c.charAt(0))return k;
e?this.each(function(){var e=a.data(this,d),b=e&&a.isFunction(e[c])?e[c].apply(e,h):e;if(b!==e&&b!==p)return k=b,!1}):this.each(function(){var e=a.data(this,d);e?e.option(c||{})._init():a.data(this,d,new b(c,this))});return k}};a.Widget=function(a,b){arguments.length&&this._createWidget(a,b)};a.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",options:{disabled:!1},_createWidget:function(d,b){a.data(b,this.widgetName,this);this.element=a(b);this.options=a.extend(!0,{},this.options,this._getCreateOptions(),
d);var c=this;this.element.bind("remove."+this.widgetName,function(){c.destroy()});this._create();this._trigger("create");this._init()},_getCreateOptions:function(){return a.metadata&&a.metadata.get(this.element[0])[this.widgetName]},_create:function(){},_init:function(){},destroy:function(){this.element.unbind("."+this.widgetName).removeData(this.widgetName);this.widget().unbind("."+this.widgetName).removeAttr("aria-disabled").removeClass(this.widgetBaseClass+"-disabled ui-state-disabled")},widget:function(){return this.element},
option:function(d,b){var c=d;if(0===arguments.length)return a.extend({},this.options);if("string"===typeof d){if(b===p)return this.options[d];c={};c[d]=b}this._setOptions(c);return this},_setOptions:function(d){var b=this;a.each(d,function(a,e){b._setOption(a,e)});return this},_setOption:function(a,b){this.options[a]=b;"disabled"===a&&this.widget()[b?"addClass":"removeClass"](this.widgetBaseClass+"-disabled ui-state-disabled").attr("aria-disabled",b);return this},enable:function(){return this._setOption("disabled",
!1)},disable:function(){return this._setOption("disabled",!0)},_trigger:function(d,b,c){var e,h=this.options[d];c=c||{};b=a.Event(b);b.type=(d===this.widgetEventPrefix?d:this.widgetEventPrefix+d).toLowerCase();b.target=this.element[0];if(d=b.originalEvent)for(e in d)e in b||(b[e]=d[e]);this.element.trigger(b,c);return!(a.isFunction(h)&&!1===h.call(this.element[0],b,c)||b.isDefaultPrevented())}}})(jQuery);
(function(a,p){var c=!1;a(document).mouseup(function(a){c=!1});a.widget("ui.mouse",{options:{cancel:":input,option",distance:1,delay:0},_mouseInit:function(){var b=this;this.element.bind("mousedown."+this.widgetName,function(a){return b._mouseDown(a)}).bind("click."+this.widgetName,function(d){if(!0===a.data(d.target,b.widgetName+".preventClickEvent"))return a.removeData(d.target,b.widgetName+".preventClickEvent"),d.stopImmediatePropagation(),!1});this.started=!1},_mouseDestroy:function(){this.element.unbind("."+
this.widgetName);this._mouseMoveDelegate&&a(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(b){if(!c){this._mouseStarted&&this._mouseUp(b);this._mouseDownEvent=b;var d=this,g=1==b.which,f="string"==typeof this.options.cancel&&b.target.nodeName?a(b.target).closest(this.options.cancel).length:!1;if(!g||f||!this._mouseCapture(b))return!0;this.mouseDelayMet=!this.options.delay;this.mouseDelayMet||(this._mouseDelayTimer=
setTimeout(function(){d.mouseDelayMet=!0},this.options.delay));if(this._mouseDistanceMet(b)&&this._mouseDelayMet(b)&&(this._mouseStarted=!1!==this._mouseStart(b),!this._mouseStarted))return b.preventDefault(),!0;!0===a.data(b.target,this.widgetName+".preventClickEvent")&&a.removeData(b.target,this.widgetName+".preventClickEvent");this._mouseMoveDelegate=function(a){return d._mouseMove(a)};this._mouseUpDelegate=function(a){return d._mouseUp(a)};a(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+
this.widgetName,this._mouseUpDelegate);b.preventDefault();return c=!0}},_mouseMove:function(b){if(a.browser.msie&&!(9<=document.documentMode)&&!b.button)return this._mouseUp(b);if(this._mouseStarted)return this._mouseDrag(b),b.preventDefault();this._mouseDistanceMet(b)&&this._mouseDelayMet(b)&&((this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,b))?this._mouseDrag(b):this._mouseUp(b));return!this._mouseStarted},_mouseUp:function(b){a(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+
this.widgetName,this._mouseUpDelegate);this._mouseStarted&&(this._mouseStarted=!1,b.target==this._mouseDownEvent.target&&a.data(b.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(b));return!1},_mouseDistanceMet:function(a){return Math.max(Math.abs(this._mouseDownEvent.pageX-a.pageX),Math.abs(this._mouseDownEvent.pageY-a.pageY))>=this.options.distance},_mouseDelayMet:function(a){return this.mouseDelayMet},_mouseStart:function(a){},_mouseDrag:function(a){},_mouseStop:function(a){},_mouseCapture:function(a){return!0}})})(jQuery);
(function(a,p){a.widget("ui.draggable",a.ui.mouse,{widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1},_create:function(){"original"!=this.options.helper||/^(?:r|a|f)/.test(this.element.css("position"))||
(this.element[0].style.position="relative");this.options.addClasses&&this.element.addClass("ui-draggable");this.options.disabled&&this.element.addClass("ui-draggable-disabled");this._mouseInit()},destroy:function(){if(this.element.data("draggable"))return this.element.removeData("draggable").unbind(".draggable").removeClass("ui-draggable ui-draggable-dragging ui-draggable-disabled"),this._mouseDestroy(),this},_mouseCapture:function(c){var b=this.options;if(this.helper||b.disabled||a(c.target).is(".ui-resizable-handle"))return!1;
this.handle=this._getHandle(c);if(!this.handle)return!1;b.iframeFix&&a(!0===b.iframeFix?"iframe":b.iframeFix).each(function(){a('<div class="ui-draggable-iframeFix" style="background: #fff;"></div>').css({width:this.offsetWidth+"px",height:this.offsetHeight+"px",position:"absolute",opacity:"0.001",zIndex:1E3}).css(a(this).offset()).appendTo("body")});return!0},_mouseStart:function(c){var b=this.options;this.helper=this._createHelper(c);this.helper.addClass("ui-draggable-dragging");this._cacheHelperProportions();
a.ui.ddmanager&&(a.ui.ddmanager.current=this);this._cacheMargins();this.cssPosition=this.helper.css("position");this.scrollParent=this.helper.scrollParent();this.offset=this.positionAbs=this.element.offset();this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left};a.extend(this.offset,{click:{left:c.pageX-this.offset.left,top:c.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()});this.originalPosition=this.position=this._generatePosition(c);
this.originalPageX=c.pageX;this.originalPageY=c.pageY;b.cursorAt&&this._adjustOffsetFromHelper(b.cursorAt);b.containment&&this._setContainment();if(!1===this._trigger("start",c))return this._clear(),!1;this._cacheHelperProportions();a.ui.ddmanager&&!b.dropBehaviour&&a.ui.ddmanager.prepareOffsets(this,c);this._mouseDrag(c,!0);a.ui.ddmanager&&a.ui.ddmanager.dragStart(this,c);return!0},_mouseDrag:function(c,b){this.position=this._generatePosition(c);this.positionAbs=this._convertPositionTo("absolute");
if(!b){var d=this._uiHash();if(!1===this._trigger("drag",c,d))return this._mouseUp({}),!1;this.position=d.position}this.options.axis&&"y"==this.options.axis||(this.helper[0].style.left=this.position.left+"px");this.options.axis&&"x"==this.options.axis||(this.helper[0].style.top=this.position.top+"px");a.ui.ddmanager&&a.ui.ddmanager.drag(this,c);return!1},_mouseStop:function(c){var b=!1;a.ui.ddmanager&&!this.options.dropBehaviour&&(b=a.ui.ddmanager.drop(this,c));this.dropped&&(b=this.dropped,this.dropped=
!1);for(var d=this.element[0],g=!1;d&&(d=d.parentNode);)d==document&&(g=!0);if(!g&&"original"===this.options.helper)return!1;if("invalid"==this.options.revert&&!b||"valid"==this.options.revert&&b||!0===this.options.revert||a.isFunction(this.options.revert)&&this.options.revert.call(this.element,b)){var f=this;a(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){!1!==f._trigger("stop",c)&&f._clear()})}else!1!==this._trigger("stop",c)&&this._clear();return!1},
_mouseUp:function(c){!0===this.options.iframeFix&&a("div.ui-draggable-iframeFix").each(function(){this.parentNode.removeChild(this)});a.ui.ddmanager&&a.ui.ddmanager.dragStop(this,c);return a.ui.mouse.prototype._mouseUp.call(this,c)},cancel:function(){this.helper.is(".ui-draggable-dragging")?this._mouseUp({}):this._clear();return this},_getHandle:function(c){var b=this.options.handle&&a(this.options.handle,this.element).length?!1:!0;a(this.options.handle,this.element).find("*").andSelf().each(function(){this==
c.target&&(b=!0)});return b},_createHelper:function(c){var b=this.options;c=a.isFunction(b.helper)?a(b.helper.apply(this.element[0],[c])):"clone"==b.helper?this.element.clone().removeAttr("id"):this.element;c.parents("body").length||c.appendTo("parent"==b.appendTo?this.element[0].parentNode:b.appendTo);c[0]==this.element[0]||/(fixed|absolute)/.test(c.css("position"))||c.css("position","absolute");return c},_adjustOffsetFromHelper:function(c){"string"==typeof c&&(c=c.split(" "));a.isArray(c)&&(c={left:+c[0],
top:+c[1]||0});"left"in c&&(this.offset.click.left=c.left+this.margins.left);"right"in c&&(this.offset.click.left=this.helperProportions.width-c.right+this.margins.left);"top"in c&&(this.offset.click.top=c.top+this.margins.top);"bottom"in c&&(this.offset.click.top=this.helperProportions.height-c.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var c=this.offsetParent.offset();"absolute"==this.cssPosition&&this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],
this.offsetParent[0])&&(c.left+=this.scrollParent.scrollLeft(),c.top+=this.scrollParent.scrollTop());if(this.offsetParent[0]==document.body||this.offsetParent[0].tagName&&"html"==this.offsetParent[0].tagName.toLowerCase()&&a.browser.msie)c={top:0,left:0};return{top:c.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:c.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"==this.cssPosition){var a=this.element.position();return{top:a.top-
(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:a.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),
height:this.helper.outerHeight()}},_setContainment:function(){var c=this.options;"parent"==c.containment&&(c.containment=this.helper[0].parentNode);if("document"==c.containment||"window"==c.containment)this.containment=["document"==c.containment?0:a(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,"document"==c.containment?0:a(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,("document"==c.containment?0:a(window).scrollLeft())+a("document"==c.containment?document:
window).width()-this.helperProportions.width-this.margins.left,("document"==c.containment?0:a(window).scrollTop())+(a("document"==c.containment?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top];if(/^(document|window|parent)$/.test(c.containment)||c.containment.constructor==Array)c.containment.constructor==Array&&(this.containment=c.containment);else{var c=a(c.containment),b=c[0];if(b){c.offset();var d="hidden"!=a(b).css("overflow");this.containment=
[(parseInt(a(b).css("borderLeftWidth"),10)||0)+(parseInt(a(b).css("paddingLeft"),10)||0),(parseInt(a(b).css("borderTopWidth"),10)||0)+(parseInt(a(b).css("paddingTop"),10)||0),(d?Math.max(b.scrollWidth,b.offsetWidth):b.offsetWidth)-(parseInt(a(b).css("borderLeftWidth"),10)||0)-(parseInt(a(b).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(d?Math.max(b.scrollHeight,b.offsetHeight):b.offsetHeight)-(parseInt(a(b).css("borderTopWidth"),10)||0)-(parseInt(a(b).css("paddingBottom"),
10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom];this.relative_container=c}}},_convertPositionTo:function(c,b){b||(b=this.position);var d="absolute"==c?1:-1,g="absolute"!=this.cssPosition||this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,f=/(html|body)/i.test(g[0].tagName);return{top:b.top+this.offset.relative.top*d+this.offset.parent.top*d-(a.browser.safari&&526>a.browser.version&&"fixed"==this.cssPosition?
0:("fixed"==this.cssPosition?-this.scrollParent.scrollTop():f?0:g.scrollTop())*d),left:b.left+this.offset.relative.left*d+this.offset.parent.left*d-(a.browser.safari&&526>a.browser.version&&"fixed"==this.cssPosition?0:("fixed"==this.cssPosition?-this.scrollParent.scrollLeft():f?0:g.scrollLeft())*d)}},_generatePosition:function(c){var b=this.options,d="absolute"!=this.cssPosition||this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,
g=/(html|body)/i.test(d[0].tagName),f=c.pageX,e=c.pageY;if(this.originalPosition){var h;this.containment&&(this.relative_container?(h=this.relative_container.offset(),h=[this.containment[0]+h.left,this.containment[1]+h.top,this.containment[2]+h.left,this.containment[3]+h.top]):h=this.containment,c.pageX-this.offset.click.left<h[0]&&(f=h[0]+this.offset.click.left),c.pageY-this.offset.click.top<h[1]&&(e=h[1]+this.offset.click.top),c.pageX-this.offset.click.left>h[2]&&(f=h[2]+this.offset.click.left),
c.pageY-this.offset.click.top>h[3]&&(e=h[3]+this.offset.click.top));b.grid&&(e=b.grid[1]?this.originalPageY+Math.round((e-this.originalPageY)/b.grid[1])*b.grid[1]:this.originalPageY,e=h?e-this.offset.click.top<h[1]||e-this.offset.click.top>h[3]?e-this.offset.click.top<h[1]?e+b.grid[1]:e-b.grid[1]:e:e,f=b.grid[0]?this.originalPageX+Math.round((f-this.originalPageX)/b.grid[0])*b.grid[0]:this.originalPageX,f=h?f-this.offset.click.left<h[0]||f-this.offset.click.left>h[2]?f-this.offset.click.left<h[0]?
f+b.grid[0]:f-b.grid[0]:f:f)}return{top:e-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+(a.browser.safari&&526>a.browser.version&&"fixed"==this.cssPosition?0:"fixed"==this.cssPosition?-this.scrollParent.scrollTop():g?0:d.scrollTop()),left:f-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+(a.browser.safari&&526>a.browser.version&&"fixed"==this.cssPosition?0:"fixed"==this.cssPosition?-this.scrollParent.scrollLeft():g?0:d.scrollLeft())}},_clear:function(){this.helper.removeClass("ui-draggable-dragging");
this.helper[0]==this.element[0]||this.cancelHelperRemoval||this.helper.remove();this.helper=null;this.cancelHelperRemoval=!1},_trigger:function(c,b,d){d=d||this._uiHash();a.ui.plugin.call(this,c,[b,d]);"drag"==c&&(this.positionAbs=this._convertPositionTo("absolute"));return a.Widget.prototype._trigger.call(this,c,b,d)},plugins:{},_uiHash:function(a){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}});a.extend(a.ui.draggable,{version:"1.8.23"});
a.ui.plugin.add("draggable","connectToSortable",{start:function(c,b){var d=a(this).data("draggable"),g=d.options,f=a.extend({},b,{item:d.element});d.sortables=[];a(g.connectToSortable).each(function(){var e=a.data(this,"sortable");e&&!e.options.disabled&&(d.sortables.push({instance:e,shouldRevert:e.options.revert}),e.refreshPositions(),e._trigger("activate",c,f))})},stop:function(c,b){var d=a(this).data("draggable"),g=a.extend({},b,{item:d.element});a.each(d.sortables,function(){this.instance.isOver?
(this.instance.isOver=0,d.cancelHelperRemoval=!0,this.instance.cancelHelperRemoval=!1,this.shouldRevert&&(this.instance.options.revert=!0),this.instance._mouseStop(c),this.instance.options.helper=this.instance.options._helper,"original"==d.options.helper&&this.instance.currentItem.css({top:"auto",left:"auto"})):(this.instance.cancelHelperRemoval=!1,this.instance._trigger("deactivate",c,g))})},drag:function(c,b){var d=a(this).data("draggable"),g=this;a.each(d.sortables,function(f){this.instance.positionAbs=
d.positionAbs;this.instance.helperProportions=d.helperProportions;this.instance.offset.click=d.offset.click;this.instance._intersectsWith(this.instance.containerCache)?(this.instance.isOver||(this.instance.isOver=1,this.instance.currentItem=a(g).clone().removeAttr("id").appendTo(this.instance.element).data("sortable-item",!0),this.instance.options._helper=this.instance.options.helper,this.instance.options.helper=function(){return b.helper[0]},c.target=this.instance.currentItem[0],this.instance._mouseCapture(c,
!0),this.instance._mouseStart(c,!0,!0),this.instance.offset.click.top=d.offset.click.top,this.instance.offset.click.left=d.offset.click.left,this.instance.offset.parent.left-=d.offset.parent.left-this.instance.offset.parent.left,this.instance.offset.parent.top-=d.offset.parent.top-this.instance.offset.parent.top,d._trigger("toSortable",c),d.dropped=this.instance.element,d.currentItem=d.element,this.instance.fromOutside=d),this.instance.currentItem&&this.instance._mouseDrag(c)):this.instance.isOver&&
(this.instance.isOver=0,this.instance.cancelHelperRemoval=!0,this.instance.options.revert=!1,this.instance._trigger("out",c,this.instance._uiHash(this.instance)),this.instance._mouseStop(c,!0),this.instance.options.helper=this.instance.options._helper,this.instance.currentItem.remove(),this.instance.placeholder&&this.instance.placeholder.remove(),d._trigger("fromSortable",c),d.dropped=!1)})}});a.ui.plugin.add("draggable","cursor",{start:function(c,b){var d=a("body"),g=a(this).data("draggable").options;
d.css("cursor")&&(g._cursor=d.css("cursor"));d.css("cursor",g.cursor)},stop:function(c,b){var d=a(this).data("draggable").options;d._cursor&&a("body").css("cursor",d._cursor)}});a.ui.plugin.add("draggable","opacity",{start:function(c,b){var d=a(b.helper),g=a(this).data("draggable").options;d.css("opacity")&&(g._opacity=d.css("opacity"));d.css("opacity",g.opacity)},stop:function(c,b){var d=a(this).data("draggable").options;d._opacity&&a(b.helper).css("opacity",d._opacity)}});a.ui.plugin.add("draggable",
"scroll",{start:function(c,b){var d=a(this).data("draggable");d.scrollParent[0]!=document&&"HTML"!=d.scrollParent[0].tagName&&(d.overflowOffset=d.scrollParent.offset())},drag:function(c,b){var d=a(this).data("draggable"),g=d.options,f=!1;d.scrollParent[0]!=document&&"HTML"!=d.scrollParent[0].tagName?(g.axis&&"x"==g.axis||(d.overflowOffset.top+d.scrollParent[0].offsetHeight-c.pageY<g.scrollSensitivity?d.scrollParent[0].scrollTop=f=d.scrollParent[0].scrollTop+g.scrollSpeed:c.pageY-d.overflowOffset.top<
g.scrollSensitivity&&(d.scrollParent[0].scrollTop=f=d.scrollParent[0].scrollTop-g.scrollSpeed)),g.axis&&"y"==g.axis||(d.overflowOffset.left+d.scrollParent[0].offsetWidth-c.pageX<g.scrollSensitivity?d.scrollParent[0].scrollLeft=f=d.scrollParent[0].scrollLeft+g.scrollSpeed:c.pageX-d.overflowOffset.left<g.scrollSensitivity&&(d.scrollParent[0].scrollLeft=f=d.scrollParent[0].scrollLeft-g.scrollSpeed))):(g.axis&&"x"==g.axis||(c.pageY-a(document).scrollTop()<g.scrollSensitivity?f=a(document).scrollTop(a(document).scrollTop()-
g.scrollSpeed):a(window).height()-(c.pageY-a(document).scrollTop())<g.scrollSensitivity&&(f=a(document).scrollTop(a(document).scrollTop()+g.scrollSpeed))),g.axis&&"y"==g.axis||(c.pageX-a(document).scrollLeft()<g.scrollSensitivity?f=a(document).scrollLeft(a(document).scrollLeft()-g.scrollSpeed):a(window).width()-(c.pageX-a(document).scrollLeft())<g.scrollSensitivity&&(f=a(document).scrollLeft(a(document).scrollLeft()+g.scrollSpeed))));!1!==f&&a.ui.ddmanager&&!g.dropBehaviour&&a.ui.ddmanager.prepareOffsets(d,
c)}});a.ui.plugin.add("draggable","snap",{start:function(c,b){var d=a(this).data("draggable"),g=d.options;d.snapElements=[];a(g.snap.constructor!=String?g.snap.items||":data(draggable)":g.snap).each(function(){var b=a(this),e=b.offset();this!=d.element[0]&&d.snapElements.push({item:this,width:b.outerWidth(),height:b.outerHeight(),top:e.top,left:e.left})})},drag:function(c,b){for(var d=a(this).data("draggable"),g=d.options,f=g.snapTolerance,e=b.offset.left,h=e+d.helperProportions.width,k=b.offset.top,
l=k+d.helperProportions.height,m=d.snapElements.length-1;0<=m;m--){var n=d.snapElements[m].left,u=n+d.snapElements[m].width,q=d.snapElements[m].top,v=q+d.snapElements[m].height;if(n-f<e&&e<u+f&&q-f<k&&k<v+f||n-f<e&&e<u+f&&q-f<l&&l<v+f||n-f<h&&h<u+f&&q-f<k&&k<v+f||n-f<h&&h<u+f&&q-f<l&&l<v+f){if("inner"!=g.snapMode){var r=Math.abs(q-l)<=f,G=Math.abs(v-k)<=f,w=Math.abs(n-h)<=f,y=Math.abs(u-e)<=f;r&&(b.position.top=d._convertPositionTo("relative",{top:q-d.helperProportions.height,left:0}).top-d.margins.top);
G&&(b.position.top=d._convertPositionTo("relative",{top:v,left:0}).top-d.margins.top);w&&(b.position.left=d._convertPositionTo("relative",{top:0,left:n-d.helperProportions.width}).left-d.margins.left);y&&(b.position.left=d._convertPositionTo("relative",{top:0,left:u}).left-d.margins.left)}var p=r||G||w||y;"outer"!=g.snapMode&&(r=Math.abs(q-k)<=f,G=Math.abs(v-l)<=f,w=Math.abs(n-e)<=f,y=Math.abs(u-h)<=f,r&&(b.position.top=d._convertPositionTo("relative",{top:q,left:0}).top-d.margins.top),G&&(b.position.top=
d._convertPositionTo("relative",{top:v-d.helperProportions.height,left:0}).top-d.margins.top),w&&(b.position.left=d._convertPositionTo("relative",{top:0,left:n}).left-d.margins.left),y&&(b.position.left=d._convertPositionTo("relative",{top:0,left:u-d.helperProportions.width}).left-d.margins.left));!d.snapElements[m].snapping&&(r||G||w||y||p)&&d.options.snap.snap&&d.options.snap.snap.call(d.element,c,a.extend(d._uiHash(),{snapItem:d.snapElements[m].item}));d.snapElements[m].snapping=r||G||w||y||p}else d.snapElements[m].snapping&&
d.options.snap.release&&d.options.snap.release.call(d.element,c,a.extend(d._uiHash(),{snapItem:d.snapElements[m].item})),d.snapElements[m].snapping=!1}}});a.ui.plugin.add("draggable","stack",{start:function(c,b){var d=a(this).data("draggable").options,d=a.makeArray(a(d.stack)).sort(function(d,e){return(parseInt(a(d).css("zIndex"),10)||0)-(parseInt(a(e).css("zIndex"),10)||0)});if(d.length){var g=parseInt(d[0].style.zIndex)||0;a(d).each(function(a){this.style.zIndex=g+a});this[0].style.zIndex=g+d.length}}});
a.ui.plugin.add("draggable","zIndex",{start:function(c,b){var d=a(b.helper),g=a(this).data("draggable").options;d.css("zIndex")&&(g._zIndex=d.css("zIndex"));d.css("zIndex",g.zIndex)},stop:function(c,b){var d=a(this).data("draggable").options;d._zIndex&&a(b.helper).css("zIndex",d._zIndex)}})})(jQuery);
(function(a,p){a.widget("ui.droppable",{widgetEventPrefix:"drop",options:{accept:"*",activeClass:!1,addClasses:!0,greedy:!1,hoverClass:!1,scope:"default",tolerance:"intersect"},_create:function(){var c=this.options,b=c.accept;this.isover=0;this.isout=1;this.accept=a.isFunction(b)?b:function(a){return a.is(b)};this.proportions={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight};a.ui.ddmanager.droppables[c.scope]=a.ui.ddmanager.droppables[c.scope]||[];a.ui.ddmanager.droppables[c.scope].push(this);
c.addClasses&&this.element.addClass("ui-droppable")},destroy:function(){for(var c=a.ui.ddmanager.droppables[this.options.scope],b=0;b<c.length;b++)c[b]==this&&c.splice(b,1);this.element.removeClass("ui-droppable ui-droppable-disabled").removeData("droppable").unbind(".droppable");return this},_setOption:function(c,b){"accept"==c&&(this.accept=a.isFunction(b)?b:function(a){return a.is(b)});a.Widget.prototype._setOption.apply(this,arguments)},_activate:function(c){var b=a.ui.ddmanager.current;this.options.activeClass&&
this.element.addClass(this.options.activeClass);b&&this._trigger("activate",c,this.ui(b))},_deactivate:function(c){var b=a.ui.ddmanager.current;this.options.activeClass&&this.element.removeClass(this.options.activeClass);b&&this._trigger("deactivate",c,this.ui(b))},_over:function(c){var b=a.ui.ddmanager.current;b&&(b.currentItem||b.element)[0]!=this.element[0]&&this.accept.call(this.element[0],b.currentItem||b.element)&&(this.options.hoverClass&&this.element.addClass(this.options.hoverClass),this._trigger("over",
c,this.ui(b)))},_out:function(c){var b=a.ui.ddmanager.current;b&&(b.currentItem||b.element)[0]!=this.element[0]&&this.accept.call(this.element[0],b.currentItem||b.element)&&(this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("out",c,this.ui(b)))},_drop:function(c,b){var d=b||a.ui.ddmanager.current;if(!d||(d.currentItem||d.element)[0]==this.element[0])return!1;var g=!1;this.element.find(":data(droppable)").not(".ui-draggable-dragging").each(function(){var b=a.data(this,
"droppable");if(b.options.greedy&&!b.options.disabled&&b.options.scope==d.options.scope&&b.accept.call(b.element[0],d.currentItem||d.element)&&a.ui.intersect(d,a.extend(b,{offset:b.element.offset()}),b.options.tolerance))return g=!0,!1});return g?!1:this.accept.call(this.element[0],d.currentItem||d.element)?(this.options.activeClass&&this.element.removeClass(this.options.activeClass),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass),this._trigger("drop",c,this.ui(d)),this.element):
!1},ui:function(a){return{draggable:a.currentItem||a.element,helper:a.helper,position:a.position,offset:a.positionAbs}}});a.extend(a.ui.droppable,{version:"1.8.23"});a.ui.intersect=function(c,b,d){if(!b.offset)return!1;var g=(c.positionAbs||c.position.absolute).left,f=g+c.helperProportions.width,e=(c.positionAbs||c.position.absolute).top,h=e+c.helperProportions.height,k=b.offset.left,l=k+b.proportions.width,m=b.offset.top,n=m+b.proportions.height;switch(d){case "fit":return k<=g&&f<=l&&m<=e&&h<=n;
case "intersect":return k<g+c.helperProportions.width/2&&f-c.helperProportions.width/2<l&&m<e+c.helperProportions.height/2&&h-c.helperProportions.height/2<n;case "pointer":return a.ui.isOver((c.positionAbs||c.position.absolute).top+(c.clickOffset||c.offset.click).top,(c.positionAbs||c.position.absolute).left+(c.clickOffset||c.offset.click).left,m,k,b.proportions.height,b.proportions.width);case "touch":return(e>=m&&e<=n||h>=m&&h<=n||e<m&&h>n)&&(g>=k&&g<=l||f>=k&&f<=l||g<k&&f>l);default:return!1}};
a.ui.ddmanager={current:null,droppables:{"default":[]},prepareOffsets:function(c,b){var d=a.ui.ddmanager.droppables[c.options.scope]||[],g=b?b.type:null,f=(c.currentItem||c.element).find(":data(droppable)").andSelf(),e=0;a:for(;e<d.length;e++)if(!(d[e].options.disabled||c&&!d[e].accept.call(d[e].element[0],c.currentItem||c.element))){for(var h=0;h<f.length;h++)if(f[h]==d[e].element[0]){d[e].proportions.height=0;continue a}d[e].visible="none"!=d[e].element.css("display");d[e].visible&&("mousedown"==
g&&d[e]._activate.call(d[e],b),d[e].offset=d[e].element.offset(),d[e].proportions={width:d[e].element[0].offsetWidth,height:d[e].element[0].offsetHeight})}},drop:function(c,b){var d=!1;a.each(a.ui.ddmanager.droppables[c.options.scope]||[],function(){this.options&&(!this.options.disabled&&this.visible&&a.ui.intersect(c,this,this.options.tolerance)&&(d=this._drop.call(this,b)||d),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],c.currentItem||c.element)&&(this.isout=1,this.isover=
0,this._deactivate.call(this,b)))});return d},dragStart:function(c,b){c.element.parents(":not(body,html)").bind("scroll.droppable",function(){c.options.refreshPositions||a.ui.ddmanager.prepareOffsets(c,b)})},drag:function(c,b){c.options.refreshPositions&&a.ui.ddmanager.prepareOffsets(c,b);a.each(a.ui.ddmanager.droppables[c.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var d=a.ui.intersect(c,this,this.options.tolerance);if(d=d||1!=this.isover?d&&0==this.isover?
"isover":null:"isout"){var g;if(this.options.greedy){var f=this.element.parents(":data(droppable):eq(0)");f.length&&(g=a.data(f[0],"droppable"),g.greedyChild="isover"==d?1:0)}g&&"isover"==d&&(g.isover=0,g.isout=1,g._out.call(g,b));this[d]=1;this["isout"==d?"isover":"isout"]=0;this["isover"==d?"_over":"_out"].call(this,b);g&&"isout"==d&&(g.isout=0,g.isover=1,g._over.call(g,b))}}})},dragStop:function(c,b){c.element.parents(":not(body,html)").unbind("scroll.droppable");c.options.refreshPositions||a.ui.ddmanager.prepareOffsets(c,
b)}}})(jQuery);
(function(a,p){a.widget("ui.resizable",a.ui.mouse,{widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:1E3},_create:function(){var d=this,b=this.options;this.element.addClass("ui-resizable");a.extend(this,{_aspectRatio:!!b.aspectRatio,aspectRatio:b.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],
_helper:b.helper||b.ghost||b.animate?b.helper||"ui-resizable-helper":null});this.element[0].nodeName.match(/canvas|textarea|input|select|button|img/i)&&(this.element.wrap(a('<div class="ui-wrapper" style="overflow: hidden;"></div>').css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("resizable",this.element.data("resizable")),this.elementIsWrapper=
!0,this.element.css({marginLeft:this.originalElement.css("marginLeft"),marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom")}),this.originalElement.css({marginLeft:0,marginTop:0,marginRight:0,marginBottom:0}),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,
display:"block"})),this.originalElement.css({margin:this.originalElement.css("margin")}),this._proportionallyResize());this.handles=b.handles||(a(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se");if(this.handles.constructor==String){"all"==this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw");var c=this.handles.split(",");this.handles=
{};for(var e=0;e<c.length;e++){var h=a.trim(c[e]),k=a('<div class="ui-resizable-handle ui-resizable-'+h+'"></div>');k.css({zIndex:b.zIndex});"se"==h&&k.addClass("ui-icon ui-icon-gripsmall-diagonal-se");this.handles[h]=".ui-resizable-"+h;this.element.append(k)}}this._renderAxis=function(e){e=e||this.element;for(var d in this.handles){this.handles[d].constructor==String&&(this.handles[d]=a(this.handles[d],this.element).show());if(this.elementIsWrapper&&this.originalElement[0].nodeName.match(/textarea|input|select|button/i)){var b=
a(this.handles[d],this.element),c=0,c=/sw|ne|nw|se|n|s/.test(d)?b.outerHeight():b.outerWidth(),b=["padding",/ne|nw|n/.test(d)?"Top":/se|sw|s/.test(d)?"Bottom":/^e$/.test(d)?"Right":"Left"].join("");e.css(b,c);this._proportionallyResize()}a(this.handles[d])}};this._renderAxis(this.element);this._handles=a(".ui-resizable-handle",this.element).disableSelection();this._handles.mouseover(function(){if(!d.resizing){if(this.className)var a=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i);d.axis=
a&&a[1]?a[1]:"se"}});b.autoHide&&(this._handles.hide(),a(this.element).addClass("ui-resizable-autohide").hover(function(){b.disabled||(a(this).removeClass("ui-resizable-autohide"),d._handles.show())},function(){b.disabled||d.resizing||(a(this).addClass("ui-resizable-autohide"),d._handles.hide())}));this._mouseInit()},destroy:function(){this._mouseDestroy();var d=function(d){a(d).removeClass("ui-resizable ui-resizable-disabled ui-resizable-resizing").removeData("resizable").unbind(".resizable").find(".ui-resizable-handle").remove()};
if(this.elementIsWrapper){d(this.element);var b=this.element;b.after(this.originalElement.css({position:b.css("position"),width:b.outerWidth(),height:b.outerHeight(),top:b.css("top"),left:b.css("left")})).remove()}this.originalElement.css("resize",this.originalResizeStyle);d(this.originalElement);return this},_mouseCapture:function(d){var b=!1,c;for(c in this.handles)a(this.handles[c])[0]==d.target&&(b=!0);return!this.options.disabled&&b},_mouseStart:function(d){var b=this.options,f=this.element.position(),
e=this.element;this.resizing=!0;this.documentScroll={top:a(document).scrollTop(),left:a(document).scrollLeft()};(e.is(".ui-draggable")||/absolute/.test(e.css("position")))&&e.css({position:"absolute",top:f.top,left:f.left});this._renderProxy();var f=c(this.helper.css("left")),h=c(this.helper.css("top"));b.containment&&(f+=a(b.containment).scrollLeft()||0,h+=a(b.containment).scrollTop()||0);this.offset=this.helper.offset();this.position={left:f,top:h};this.size=this._helper?{width:e.outerWidth(),height:e.outerHeight()}:
{width:e.width(),height:e.height()};this.originalSize=this._helper?{width:e.outerWidth(),height:e.outerHeight()}:{width:e.width(),height:e.height()};this.originalPosition={left:f,top:h};this.sizeDiff={width:e.outerWidth()-e.width(),height:e.outerHeight()-e.height()};this.originalMousePosition={left:d.pageX,top:d.pageY};this.aspectRatio="number"==typeof b.aspectRatio?b.aspectRatio:this.originalSize.width/this.originalSize.height||1;b=a(".ui-resizable-"+this.axis).css("cursor");a("body").css("cursor",
"auto"==b?this.axis+"-resize":b);e.addClass("ui-resizable-resizing");this._propagate("start",d);return!0},_mouseDrag:function(a){var b=this.helper,c=this.originalMousePosition,e=this._change[this.axis];if(!e)return!1;c=e.apply(this,[a,a.pageX-c.left||0,a.pageY-c.top||0]);this._updateVirtualBoundaries(a.shiftKey);if(this._aspectRatio||a.shiftKey)c=this._updateRatio(c,a);c=this._respectSize(c,a);this._propagate("resize",a);b.css({top:this.position.top+"px",left:this.position.left+"px",width:this.size.width+
"px",height:this.size.height+"px"});!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize();this._updateCache(c);this._trigger("resize",a,this.ui());return!1},_mouseStop:function(d){this.resizing=!1;var b=this.options;if(this._helper){var c=this._proportionallyResizeElements,e=c.length&&/textarea/i.test(c[0].nodeName),c=e&&a.ui.hasScroll(c[0],"left")?0:this.sizeDiff.height,e=e?0:this.sizeDiff.width,e={width:this.helper.width()-e,height:this.helper.height()-c},c=parseInt(this.element.css("left"),
10)+(this.position.left-this.originalPosition.left)||null,h=parseInt(this.element.css("top"),10)+(this.position.top-this.originalPosition.top)||null;b.animate||this.element.css(a.extend(e,{top:h,left:c}));this.helper.height(this.size.height);this.helper.width(this.size.width);this._helper&&!b.animate&&this._proportionallyResize()}a("body").css("cursor","auto");this.element.removeClass("ui-resizable-resizing");this._propagate("stop",d);this._helper&&this.helper.remove();return!1},_updateVirtualBoundaries:function(a){var c=
this.options,f,e,h,c={minWidth:b(c.minWidth)?c.minWidth:0,maxWidth:b(c.maxWidth)?c.maxWidth:Infinity,minHeight:b(c.minHeight)?c.minHeight:0,maxHeight:b(c.maxHeight)?c.maxHeight:Infinity};if(this._aspectRatio||a)a=c.minHeight*this.aspectRatio,e=c.minWidth/this.aspectRatio,f=c.maxHeight*this.aspectRatio,h=c.maxWidth/this.aspectRatio,a>c.minWidth&&(c.minWidth=a),e>c.minHeight&&(c.minHeight=e),f<c.maxWidth&&(c.maxWidth=f),h<c.maxHeight&&(c.maxHeight=h);this._vBoundaries=c},_updateCache:function(a){this.offset=
this.helper.offset();b(a.left)&&(this.position.left=a.left);b(a.top)&&(this.position.top=a.top);b(a.height)&&(this.size.height=a.height);b(a.width)&&(this.size.width=a.width)},_updateRatio:function(a,c){var f=this.position,e=this.size,h=this.axis;b(a.height)?a.width=a.height*this.aspectRatio:b(a.width)&&(a.height=a.width/this.aspectRatio);"sw"==h&&(a.left=f.left+(e.width-a.width),a.top=null);"nw"==h&&(a.top=f.top+(e.height-a.height),a.left=f.left+(e.width-a.width));return a},_respectSize:function(a,
c){var f=this._vBoundaries,e=this.axis,h=b(a.width)&&f.maxWidth&&f.maxWidth<a.width,k=b(a.height)&&f.maxHeight&&f.maxHeight<a.height,l=b(a.width)&&f.minWidth&&f.minWidth>a.width,m=b(a.height)&&f.minHeight&&f.minHeight>a.height;l&&(a.width=f.minWidth);m&&(a.height=f.minHeight);h&&(a.width=f.maxWidth);k&&(a.height=f.maxHeight);var n=this.originalPosition.left+this.originalSize.width,u=this.position.top+this.size.height,q=/sw|nw|w/.test(e),e=/nw|ne|n/.test(e);l&&q&&(a.left=n-f.minWidth);h&&q&&(a.left=
n-f.maxWidth);m&&e&&(a.top=u-f.minHeight);k&&e&&(a.top=u-f.maxHeight);(f=!a.width&&!a.height)&&!a.left&&a.top?a.top=null:f&&!a.top&&a.left&&(a.left=null);return a},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var b=this.helper||this.element,c=0;c<this._proportionallyResizeElements.length;c++){var f=this._proportionallyResizeElements[c];if(!this.borderDif){var e=[f.css("borderTopWidth"),f.css("borderRightWidth"),f.css("borderBottomWidth"),f.css("borderLeftWidth")],
h=[f.css("paddingTop"),f.css("paddingRight"),f.css("paddingBottom"),f.css("paddingLeft")];this.borderDif=a.map(e,function(a,e){var b=parseInt(a,10)||0,c=parseInt(h[e],10)||0;return b+c})}a.browser.msie&&(a(b).is(":hidden")||a(b).parents(":hidden").length)||f.css({height:b.height()-this.borderDif[0]-this.borderDif[2]||0,width:b.width()-this.borderDif[1]-this.borderDif[3]||0})}},_renderProxy:function(){var b=this.options;this.elementOffset=this.element.offset();if(this._helper){this.helper=this.helper||
a('<div style="overflow:hidden;"></div>');var c=a.browser.msie&&7>a.browser.version,f=c?1:0,c=c?2:-1;this.helper.addClass(this._helper).css({width:this.element.outerWidth()+c,height:this.element.outerHeight()+c,position:"absolute",left:this.elementOffset.left-f+"px",top:this.elementOffset.top-f+"px",zIndex:++b.zIndex});this.helper.appendTo("body").disableSelection()}else this.helper=this.element},_change:{e:function(a,b,c){return{width:this.originalSize.width+b}},w:function(a,b,c){return{left:this.originalPosition.left+
b,width:this.originalSize.width-b}},n:function(a,b,c){return{top:this.originalPosition.top+c,height:this.originalSize.height-c}},s:function(a,b,c){return{height:this.originalSize.height+c}},se:function(b,c,f){return a.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[b,c,f]))},sw:function(b,c,f){return a.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[b,c,f]))},ne:function(b,c,f){return a.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,
[b,c,f]))},nw:function(b,c,f){return a.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[b,c,f]))}},_propagate:function(b,c){a.ui.plugin.call(this,b,[c,this.ui()]);"resize"!=b&&this._trigger(b,c,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}});a.extend(a.ui.resizable,{version:"1.8.23"});a.ui.plugin.add("resizable",
"alsoResize",{start:function(b,c){var f=a(this).data("resizable").options,e=function(e){a(e).each(function(){var e=a(this);e.data("resizable-alsoresize",{width:parseInt(e.width(),10),height:parseInt(e.height(),10),left:parseInt(e.css("left"),10),top:parseInt(e.css("top"),10)})})};"object"!=typeof f.alsoResize||f.alsoResize.parentNode?e(f.alsoResize):f.alsoResize.length?(f.alsoResize=f.alsoResize[0],e(f.alsoResize)):a.each(f.alsoResize,function(a){e(a)})},resize:function(b,c){var f=a(this).data("resizable"),
e=f.options,h=f.originalSize,k=f.originalPosition,l={height:f.size.height-h.height||0,width:f.size.width-h.width||0,top:f.position.top-k.top||0,left:f.position.left-k.left||0},m=function(e,b){a(e).each(function(){var e=a(this),d=a(this).data("resizable-alsoresize"),h={},f=b&&b.length?b:e.parents(c.originalElement[0]).length?["width","height"]:["width","height","top","left"];a.each(f,function(a,e){var b=(d[e]||0)+(l[e]||0);b&&0<=b&&(h[e]=b||null)});e.css(h)})};"object"!=typeof e.alsoResize||e.alsoResize.nodeType?
m(e.alsoResize):a.each(e.alsoResize,function(a,e){m(a,e)})},stop:function(b,c){a(this).removeData("resizable-alsoresize")}});a.ui.plugin.add("resizable","animate",{stop:function(b,c){var f=a(this).data("resizable"),e=f.options,h=f._proportionallyResizeElements,k=h.length&&/textarea/i.test(h[0].nodeName),l=k&&a.ui.hasScroll(h[0],"left")?0:f.sizeDiff.height,k={width:f.size.width-(k?0:f.sizeDiff.width),height:f.size.height-l},l=parseInt(f.element.css("left"),10)+(f.position.left-f.originalPosition.left)||
null,m=parseInt(f.element.css("top"),10)+(f.position.top-f.originalPosition.top)||null;f.element.animate(a.extend(k,m&&l?{top:m,left:l}:{}),{duration:e.animateDuration,easing:e.animateEasing,step:function(){var e={width:parseInt(f.element.css("width"),10),height:parseInt(f.element.css("height"),10),top:parseInt(f.element.css("top"),10),left:parseInt(f.element.css("left"),10)};h&&h.length&&a(h[0]).css({width:e.width,height:e.height});f._updateCache(e);f._propagate("resize",b)}})}});a.ui.plugin.add("resizable",
"containment",{start:function(b,g){var f=a(this).data("resizable"),e=f.element,h=f.options.containment;if(e=h instanceof a?h.get(0):/parent/.test(h)?e.parent().get(0):h)if(f.containerElement=a(e),/document/.test(h)||h==document)f.containerOffset={left:0,top:0},f.containerPosition={left:0,top:0},f.parentData={element:a(document),left:0,top:0,width:a(document).width(),height:a(document).height()||document.body.parentNode.scrollHeight};else{var k=a(e),l=[];a(["Top","Right","Left","Bottom"]).each(function(a,
e){l[a]=c(k.css("padding"+e))});f.containerOffset=k.offset();f.containerPosition=k.position();f.containerSize={height:k.innerHeight()-l[3],width:k.innerWidth()-l[1]};var h=f.containerOffset,m=f.containerSize.height,n=f.containerSize.width,n=a.ui.hasScroll(e,"left")?e.scrollWidth:n,m=a.ui.hasScroll(e)?e.scrollHeight:m;f.parentData={element:e,left:h.left,top:h.top,width:n,height:m}}},resize:function(b,c){var f=a(this).data("resizable"),e=f.options,h=f.containerOffset,k=f.position,l=f._aspectRatio||
b.shiftKey,m={top:0,left:0},n=f.containerElement;n[0]!=document&&/static/.test(n.css("position"))&&(m=h);k.left<(f._helper?h.left:0)&&(f.size.width+=f._helper?f.position.left-h.left:f.position.left-m.left,l&&(f.size.height=f.size.width/f.aspectRatio),f.position.left=e.helper?h.left:0);k.top<(f._helper?h.top:0)&&(f.size.height+=f._helper?f.position.top-h.top:f.position.top,l&&(f.size.width=f.size.height*f.aspectRatio),f.position.top=f._helper?h.top:0);f.offset.left=f.parentData.left+f.position.left;
f.offset.top=f.parentData.top+f.position.top;e=Math.abs(f.offset.left-m.left+f.sizeDiff.width);h=Math.abs((f._helper?f.offset.top-m.top:f.offset.top-h.top)+f.sizeDiff.height);m=f.containerElement.get(0)==f.element.parent().get(0);k=/relative|absolute/.test(f.containerElement.css("position"));m&&k&&(e-=f.parentData.left);e+f.size.width>=f.parentData.width&&(f.size.width=f.parentData.width-e,l&&(f.size.height=f.size.width/f.aspectRatio));h+f.size.height>=f.parentData.height&&(f.size.height=f.parentData.height-
h,l&&(f.size.width=f.size.height*f.aspectRatio))},stop:function(b,c){var f=a(this).data("resizable"),e=f.options,h=f.containerOffset,k=f.containerPosition,l=f.containerElement,m=a(f.helper),n=m.offset(),u=m.outerWidth()-f.sizeDiff.width,m=m.outerHeight()-f.sizeDiff.height;f._helper&&!e.animate&&/relative/.test(l.css("position"))&&a(this).css({left:n.left-k.left-h.left,width:u,height:m});f._helper&&!e.animate&&/static/.test(l.css("position"))&&a(this).css({left:n.left-k.left-h.left,width:u,height:m})}});
a.ui.plugin.add("resizable","ghost",{start:function(b,c){var f=a(this).data("resizable"),e=f.options,h=f.size;f.ghost=f.originalElement.clone();f.ghost.css({opacity:.25,display:"block",position:"relative",height:h.height,width:h.width,margin:0,left:0,top:0}).addClass("ui-resizable-ghost").addClass("string"==typeof e.ghost?e.ghost:"");f.ghost.appendTo(f.helper)},resize:function(b,c){var f=a(this).data("resizable");f.ghost&&f.ghost.css({position:"relative",height:f.size.height,width:f.size.width})},
stop:function(b,c){var f=a(this).data("resizable");f.ghost&&f.helper&&f.helper.get(0).removeChild(f.ghost.get(0))}});a.ui.plugin.add("resizable","grid",{resize:function(b,c){var f=a(this).data("resizable"),e=f.options,h=f.size,k=f.originalSize,l=f.originalPosition,m=f.axis;e.grid="number"==typeof e.grid?[e.grid,e.grid]:e.grid;var n=Math.round((h.width-k.width)/(e.grid[0]||1))*(e.grid[0]||1),e=Math.round((h.height-k.height)/(e.grid[1]||1))*(e.grid[1]||1);/^(se|s|e)$/.test(m)?(f.size.width=k.width+
n,f.size.height=k.height+e):/^(ne)$/.test(m)?(f.size.width=k.width+n,f.size.height=k.height+e,f.position.top=l.top-e):(/^(sw)$/.test(m)?(f.size.width=k.width+n,f.size.height=k.height+e):(f.size.width=k.width+n,f.size.height=k.height+e,f.position.top=l.top-e),f.position.left=l.left-n)}});var c=function(a){return parseInt(a,10)||0},b=function(a){return!isNaN(parseInt(a,10))}})(jQuery);
(function(a,p){a.widget("ui.selectable",a.ui.mouse,{options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch"},_create:function(){var c=this;this.element.addClass("ui-selectable");this.dragged=!1;var b;this.refresh=function(){b=a(c.options.filter,c.element[0]);b.addClass("ui-selectee");b.each(function(){var b=a(this),c=b.offset();a.data(this,"selectable-item",{element:this,$element:b,left:c.left,top:c.top,right:c.left+b.outerWidth(),bottom:c.top+b.outerHeight(),startselected:!1,
selected:b.hasClass("ui-selected"),selecting:b.hasClass("ui-selecting"),unselecting:b.hasClass("ui-unselecting")})})};this.refresh();this.selectees=b.addClass("ui-selectee");this._mouseInit();this.helper=a("<div class='ui-selectable-helper'></div>")},destroy:function(){this.selectees.removeClass("ui-selectee").removeData("selectable-item");this.element.removeClass("ui-selectable ui-selectable-disabled").removeData("selectable").unbind(".selectable");this._mouseDestroy();return this},_mouseStart:function(c){var b=
this;this.opos=[c.pageX,c.pageY];if(!this.options.disabled){var d=this.options;this.selectees=a(d.filter,this.element[0]);this._trigger("start",c);a(d.appendTo).append(this.helper);this.helper.css({left:c.clientX,top:c.clientY,width:0,height:0});d.autoRefresh&&this.refresh();this.selectees.filter(".ui-selected").each(function(){var d=a.data(this,"selectable-item");d.startselected=!0;c.metaKey||c.ctrlKey||(d.$element.removeClass("ui-selected"),d.selected=!1,d.$element.addClass("ui-unselecting"),d.unselecting=
!0,b._trigger("unselecting",c,{unselecting:d.element}))});a(c.target).parents().andSelf().each(function(){var d=a.data(this,"selectable-item");if(d){var f=!c.metaKey&&!c.ctrlKey||!d.$element.hasClass("ui-selected");d.$element.removeClass(f?"ui-unselecting":"ui-selected").addClass(f?"ui-selecting":"ui-unselecting");d.unselecting=!f;d.selecting=f;(d.selected=f)?b._trigger("selecting",c,{selecting:d.element}):b._trigger("unselecting",c,{unselecting:d.element});return!1}})}},_mouseDrag:function(c){var b=
this;this.dragged=!0;if(!this.options.disabled){var d=this.options,g=this.opos[0],f=this.opos[1],e=c.pageX,h=c.pageY;if(g>e)var k=e,e=g,g=k;f>h&&(k=h,h=f,f=k);this.helper.css({left:g,top:f,width:e-g,height:h-f});this.selectees.each(function(){var k=a.data(this,"selectable-item");if(k&&k.element!=b.element[0]){var m=!1;"touch"==d.tolerance?m=!(k.left>e||k.right<g||k.top>h||k.bottom<f):"fit"==d.tolerance&&(m=k.left>g&&k.right<e&&k.top>f&&k.bottom<h);m?(k.selected&&(k.$element.removeClass("ui-selected"),
k.selected=!1),k.unselecting&&(k.$element.removeClass("ui-unselecting"),k.unselecting=!1),k.selecting||(k.$element.addClass("ui-selecting"),k.selecting=!0,b._trigger("selecting",c,{selecting:k.element}))):(k.selecting&&((c.metaKey||c.ctrlKey)&&k.startselected?(k.$element.removeClass("ui-selecting"),k.selecting=!1,k.$element.addClass("ui-selected"),k.selected=!0):(k.$element.removeClass("ui-selecting"),k.selecting=!1,k.startselected&&(k.$element.addClass("ui-unselecting"),k.unselecting=!0),b._trigger("unselecting",
c,{unselecting:k.element}))),!k.selected||c.metaKey||c.ctrlKey||k.startselected||(k.$element.removeClass("ui-selected"),k.selected=!1,k.$element.addClass("ui-unselecting"),k.unselecting=!0,b._trigger("unselecting",c,{unselecting:k.element})))}});return!1}},_mouseStop:function(c){var b=this;this.dragged=!1;a(".ui-unselecting",this.element[0]).each(function(){var d=a.data(this,"selectable-item");d.$element.removeClass("ui-unselecting");d.unselecting=!1;d.startselected=!1;b._trigger("unselected",c,{unselected:d.element})});
a(".ui-selecting",this.element[0]).each(function(){var d=a.data(this,"selectable-item");d.$element.removeClass("ui-selecting").addClass("ui-selected");d.selecting=!1;d.selected=!0;d.startselected=!0;b._trigger("selected",c,{selected:d.element})});this._trigger("stop",c);this.helper.remove();return!1}});a.extend(a.ui.selectable,{version:"1.8.23"})})(jQuery);
(function(a,p){a.widget("ui.sortable",a.ui.mouse,{widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1E3},_create:function(){var a=this.options;this.containerCache={};this.element.addClass("ui-sortable");
this.refresh();this.floating=this.items.length?"x"===a.axis||/left|right/.test(this.items[0].item.css("float"))||/inline|table-cell/.test(this.items[0].item.css("display")):!1;this.offset=this.element.offset();this._mouseInit();this.ready=!0},destroy:function(){a.Widget.prototype.destroy.call(this);this.element.removeClass("ui-sortable ui-sortable-disabled");this._mouseDestroy();for(var c=this.items.length-1;0<=c;c--)this.items[c].item.removeData(this.widgetName+"-item");return this},_setOption:function(c,
b){"disabled"===c?(this.options[c]=b,this.widget()[b?"addClass":"removeClass"]("ui-sortable-disabled")):a.Widget.prototype._setOption.apply(this,arguments)},_mouseCapture:function(c,b){var d=this;if(this.reverting||this.options.disabled||"static"==this.options.type)return!1;this._refreshItems(c);var g=null,f=this;a(c.target).parents().each(function(){if(a.data(this,d.widgetName+"-item")==f)return g=a(this),!1});a.data(c.target,d.widgetName+"-item")==f&&(g=a(c.target));if(!g)return!1;if(this.options.handle&&
!b){var e=!1;a(this.options.handle,g).find("*").andSelf().each(function(){this==c.target&&(e=!0)});if(!e)return!1}this.currentItem=g;this._removeCurrentsFromItems();return!0},_mouseStart:function(c,b,d){b=this.options;this.currentContainer=this;this.refreshPositions();this.helper=this._createHelper(c);this._cacheHelperProportions();this._cacheMargins();this.scrollParent=this.helper.scrollParent();this.offset=this.currentItem.offset();this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-
this.margins.left};a.extend(this.offset,{click:{left:c.pageX-this.offset.left,top:c.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()});this.helper.css("position","absolute");this.cssPosition=this.helper.css("position");this.originalPosition=this._generatePosition(c);this.originalPageX=c.pageX;this.originalPageY=c.pageY;b.cursorAt&&this._adjustOffsetFromHelper(b.cursorAt);this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]};
this.helper[0]!=this.currentItem[0]&&this.currentItem.hide();this._createPlaceholder();b.containment&&this._setContainment();b.cursor&&(a("body").css("cursor")&&(this._storedCursor=a("body").css("cursor")),a("body").css("cursor",b.cursor));b.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",b.opacity));b.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",b.zIndex));this.scrollParent[0]!=
document&&"HTML"!=this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset());this._trigger("start",c,this._uiHash());this._preserveHelperProportions||this._cacheHelperProportions();if(!d)for(d=this.containers.length-1;0<=d;d--)this.containers[d]._trigger("activate",c,this._uiHash(this));a.ui.ddmanager&&(a.ui.ddmanager.current=this);a.ui.ddmanager&&!b.dropBehaviour&&a.ui.ddmanager.prepareOffsets(this,c);this.dragging=!0;this.helper.addClass("ui-sortable-helper");this._mouseDrag(c);
return!0},_mouseDrag:function(c){this.position=this._generatePosition(c);this.positionAbs=this._convertPositionTo("absolute");this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs);if(this.options.scroll){var b=this.options,d=!1;this.scrollParent[0]!=document&&"HTML"!=this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-c.pageY<b.scrollSensitivity?this.scrollParent[0].scrollTop=d=this.scrollParent[0].scrollTop+b.scrollSpeed:c.pageY-this.overflowOffset.top<
b.scrollSensitivity&&(this.scrollParent[0].scrollTop=d=this.scrollParent[0].scrollTop-b.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-c.pageX<b.scrollSensitivity?this.scrollParent[0].scrollLeft=d=this.scrollParent[0].scrollLeft+b.scrollSpeed:c.pageX-this.overflowOffset.left<b.scrollSensitivity&&(this.scrollParent[0].scrollLeft=d=this.scrollParent[0].scrollLeft-b.scrollSpeed)):(c.pageY-a(document).scrollTop()<b.scrollSensitivity?d=a(document).scrollTop(a(document).scrollTop()-
b.scrollSpeed):a(window).height()-(c.pageY-a(document).scrollTop())<b.scrollSensitivity&&(d=a(document).scrollTop(a(document).scrollTop()+b.scrollSpeed)),c.pageX-a(document).scrollLeft()<b.scrollSensitivity?d=a(document).scrollLeft(a(document).scrollLeft()-b.scrollSpeed):a(window).width()-(c.pageX-a(document).scrollLeft())<b.scrollSensitivity&&(d=a(document).scrollLeft(a(document).scrollLeft()+b.scrollSpeed)));!1!==d&&a.ui.ddmanager&&!b.dropBehaviour&&a.ui.ddmanager.prepareOffsets(this,c)}this.positionAbs=
this._convertPositionTo("absolute");this.options.axis&&"y"==this.options.axis||(this.helper[0].style.left=this.position.left+"px");this.options.axis&&"x"==this.options.axis||(this.helper[0].style.top=this.position.top+"px");for(b=this.items.length-1;0<=b;b--){var d=this.items[b],g=d.item[0],f=this._intersectsWithPointer(d);if(f&&g!=this.currentItem[0]&&this.placeholder[1==f?"next":"prev"]()[0]!=g&&!a.ui.contains(this.placeholder[0],g)&&("semi-dynamic"==this.options.type?!a.ui.contains(this.element[0],
g):1)){this.direction=1==f?"down":"up";if("pointer"==this.options.tolerance||this._intersectsWithSides(d))this._rearrange(c,d);else break;this._trigger("change",c,this._uiHash());break}}this._contactContainers(c);a.ui.ddmanager&&a.ui.ddmanager.drag(this,c);this._trigger("sort",c,this._uiHash());this.lastPositionAbs=this.positionAbs;return!1},_mouseStop:function(c,b){if(c){a.ui.ddmanager&&!this.options.dropBehaviour&&a.ui.ddmanager.drop(this,c);if(this.options.revert){var d=this,g=d.placeholder.offset();
d.reverting=!0;a(this.helper).animate({left:g.left-this.offset.parent.left-d.margins.left+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft),top:g.top-this.offset.parent.top-d.margins.top+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)},parseInt(this.options.revert,10)||500,function(){d._clear(c)})}else this._clear(c,b);return!1}},cancel:function(){if(this.dragging){this._mouseUp({target:null});"original"==this.options.helper?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):
this.currentItem.show();for(var c=this.containers.length-1;0<=c;c--)this.containers[c]._trigger("deactivate",null,this._uiHash(this)),this.containers[c].containerCache.over&&(this.containers[c]._trigger("out",null,this._uiHash(this)),this.containers[c].containerCache.over=0)}this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!=this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),a.extend(this,{helper:null,
dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?a(this.domPosition.prev).after(this.currentItem):a(this.domPosition.parent).prepend(this.currentItem));return this},serialize:function(c){var b=this._getItemsAsjQuery(c&&c.connected),d=[];c=c||{};a(b).each(function(){var b=(a(c.item||this).attr(c.attribute||"id")||"").match(c.expression||/(.+)[-=_](.+)/);b&&d.push((c.key||b[1]+"[]")+"="+(c.key&&c.expression?b[1]:b[2]))});!d.length&&c.key&&d.push(c.key+"=");return d.join("&")},toArray:function(c){var b=
this._getItemsAsjQuery(c&&c.connected),d=[];c=c||{};b.each(function(){d.push(a(c.item||this).attr(c.attribute||"id")||"")});return d},_intersectsWith:function(a){var b=this.positionAbs.left,d=b+this.helperProportions.width,g=this.positionAbs.top,f=g+this.helperProportions.height,e=a.left,h=e+a.width,k=a.top,l=k+a.height,m=this.offset.click.top,n=this.offset.click.left;return"pointer"==this.options.tolerance||this.options.forcePointerForContainers||"pointer"!=this.options.tolerance&&this.helperProportions[this.floating?
"width":"height"]>a[this.floating?"width":"height"]?g+m>k&&g+m<l&&b+n>e&&b+n<h:e<b+this.helperProportions.width/2&&d-this.helperProportions.width/2<h&&k<g+this.helperProportions.height/2&&f-this.helperProportions.height/2<l},_intersectsWithPointer:function(c){var b="x"===this.options.axis||a.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,c.top,c.height);c="y"===this.options.axis||a.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,c.left,c.width);b=b&&c;c=this._getDragVerticalDirection();
var d=this._getDragHorizontalDirection();return b?this.floating?d&&"right"==d||"down"==c?2:1:c&&("down"==c?2:1):!1},_intersectsWithSides:function(c){var b=a.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,c.top+c.height/2,c.height);c=a.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,c.left+c.width/2,c.width);var d=this._getDragVerticalDirection(),g=this._getDragHorizontalDirection();return this.floating&&g?"right"==g&&c||"left"==g&&!c:d&&("down"==d&&b||"up"==d&&!b)},_getDragVerticalDirection:function(){var a=
this.positionAbs.top-this.lastPositionAbs.top;return 0!=a&&(0<a?"down":"up")},_getDragHorizontalDirection:function(){var a=this.positionAbs.left-this.lastPositionAbs.left;return 0!=a&&(0<a?"right":"left")},refresh:function(a){this._refreshItems(a);this.refreshPositions();return this},_connectWith:function(){var a=this.options;return a.connectWith.constructor==String?[a.connectWith]:a.connectWith},_getItemsAsjQuery:function(c){var b=[],d=[],g=this._connectWith();if(g&&c)for(c=g.length-1;0<=c;c--)for(var f=
a(g[c]),e=f.length-1;0<=e;e--){var h=a.data(f[e],this.widgetName);h&&h!=this&&!h.options.disabled&&d.push([a.isFunction(h.options.items)?h.options.items.call(h.element):a(h.options.items,h.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),h])}d.push([a.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):a(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]);for(c=d.length-
1;0<=c;c--)d[c][0].each(function(){b.push(this)});return a(b)},_removeCurrentsFromItems:function(){for(var a=this.currentItem.find(":data("+this.widgetName+"-item)"),b=0;b<this.items.length;b++)for(var d=0;d<a.length;d++)a[d]==this.items[b].item[0]&&this.items.splice(b,1)},_refreshItems:function(c){this.items=[];this.containers=[this];var b=this.items,d=[[a.isFunction(this.options.items)?this.options.items.call(this.element[0],c,{item:this.currentItem}):a(this.options.items,this.element),this]],g=
this._connectWith();if(g&&this.ready)for(var f=g.length-1;0<=f;f--)for(var e=a(g[f]),h=e.length-1;0<=h;h--){var k=a.data(e[h],this.widgetName);k&&k!=this&&!k.options.disabled&&(d.push([a.isFunction(k.options.items)?k.options.items.call(k.element[0],c,{item:this.currentItem}):a(k.options.items,k.element),k]),this.containers.push(k))}for(f=d.length-1;0<=f;f--)for(c=d[f][1],g=d[f][0],h=0,e=g.length;h<e;h++)k=a(g[h]),k.data(this.widgetName+"-item",c),b.push({item:k,instance:c,width:0,height:0,left:0,
top:0})},refreshPositions:function(c){this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset());for(var b=this.items.length-1;0<=b;b--){var d=this.items[b];if(d.instance==this.currentContainer||!this.currentContainer||d.item[0]==this.currentItem[0]){var g=this.options.toleranceElement?a(this.options.toleranceElement,d.item):d.item;c||(d.width=g.outerWidth(),d.height=g.outerHeight());g=g.offset();d.left=g.left;d.top=g.top}}if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);
else for(b=this.containers.length-1;0<=b;b--)g=this.containers[b].element.offset(),this.containers[b].containerCache.left=g.left,this.containers[b].containerCache.top=g.top,this.containers[b].containerCache.width=this.containers[b].element.outerWidth(),this.containers[b].containerCache.height=this.containers[b].element.outerHeight();return this},_createPlaceholder:function(c){var b=c||this,d=b.options;if(!d.placeholder||d.placeholder.constructor==String){var g=d.placeholder;d.placeholder={element:function(){var c=
a(document.createElement(b.currentItem[0].nodeName)).addClass(g||b.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper")[0];g||(c.style.visibility="hidden");return c},update:function(a,e){if(!g||d.forcePlaceholderSize)e.height()||e.height(b.currentItem.innerHeight()-parseInt(b.currentItem.css("paddingTop")||0,10)-parseInt(b.currentItem.css("paddingBottom")||0,10)),e.width()||e.width(b.currentItem.innerWidth()-parseInt(b.currentItem.css("paddingLeft")||0,10)-parseInt(b.currentItem.css("paddingRight")||
0,10))}}}b.placeholder=a(d.placeholder.element.call(b.element,b.currentItem));b.currentItem.after(b.placeholder);d.placeholder.update(b,b.placeholder)},_contactContainers:function(c){for(var b=null,d=null,g=this.containers.length-1;0<=g;g--)a.ui.contains(this.currentItem[0],this.containers[g].element[0])||(this._intersectsWith(this.containers[g].containerCache)?b&&a.ui.contains(this.containers[g].element[0],b.element[0])||(b=this.containers[g],d=g):this.containers[g].containerCache.over&&(this.containers[g]._trigger("out",
c,this._uiHash(this)),this.containers[g].containerCache.over=0));if(b)if(1===this.containers.length)this.containers[d]._trigger("over",c,this._uiHash(this)),this.containers[d].containerCache.over=1;else if(this.currentContainer!=this.containers[d]){for(var b=1E4,g=null,f=this.positionAbs[this.containers[d].floating?"left":"top"],e=this.items.length-1;0<=e;e--)if(a.ui.contains(this.containers[d].element[0],this.items[e].item[0])){var h=this.containers[d].floating?this.items[e].item.offset().left:this.items[e].item.offset().top;
Math.abs(h-f)<b&&(b=Math.abs(h-f),g=this.items[e],this.direction=0<h-f?"down":"up")}if(g||this.options.dropOnEmpty)this.currentContainer=this.containers[d],g?this._rearrange(c,g,null,!0):this._rearrange(c,null,this.containers[d].element,!0),this._trigger("change",c,this._uiHash()),this.containers[d]._trigger("change",c,this._uiHash(this)),this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[d]._trigger("over",c,this._uiHash(this)),this.containers[d].containerCache.over=
1}},_createHelper:function(c){var b=this.options;c=a.isFunction(b.helper)?a(b.helper.apply(this.element[0],[c,this.currentItem])):"clone"==b.helper?this.currentItem.clone():this.currentItem;c.parents("body").length||a("parent"!=b.appendTo?b.appendTo:this.currentItem[0].parentNode)[0].appendChild(c[0]);c[0]==this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")});
(""==c[0].style.width||b.forceHelperSize)&&c.width(this.currentItem.width());(""==c[0].style.height||b.forceHelperSize)&&c.height(this.currentItem.height());return c},_adjustOffsetFromHelper:function(c){"string"==typeof c&&(c=c.split(" "));a.isArray(c)&&(c={left:+c[0],top:+c[1]||0});"left"in c&&(this.offset.click.left=c.left+this.margins.left);"right"in c&&(this.offset.click.left=this.helperProportions.width-c.right+this.margins.left);"top"in c&&(this.offset.click.top=c.top+this.margins.top);"bottom"in
c&&(this.offset.click.top=this.helperProportions.height-c.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var c=this.offsetParent.offset();"absolute"==this.cssPosition&&this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],this.offsetParent[0])&&(c.left+=this.scrollParent.scrollLeft(),c.top+=this.scrollParent.scrollTop());if(this.offsetParent[0]==document.body||this.offsetParent[0].tagName&&"html"==this.offsetParent[0].tagName.toLowerCase()&&
a.browser.msie)c={top:0,left:0};return{top:c.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:c.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"==this.cssPosition){var a=this.currentItem.position();return{top:a.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:a.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins=
{left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var c=this.options;"parent"==c.containment&&(c.containment=this.helper[0].parentNode);if("document"==c.containment||"window"==c.containment)this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-
this.offset.parent.top,a("document"==c.containment?document:window).width()-this.helperProportions.width-this.margins.left,(a("document"==c.containment?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top];if(!/^(document|window|parent)$/.test(c.containment)){var b=a(c.containment)[0],c=a(c.containment).offset(),d="hidden"!=a(b).css("overflow");this.containment=[c.left+(parseInt(a(b).css("borderLeftWidth"),10)||0)+(parseInt(a(b).css("paddingLeft"),
10)||0)-this.margins.left,c.top+(parseInt(a(b).css("borderTopWidth"),10)||0)+(parseInt(a(b).css("paddingTop"),10)||0)-this.margins.top,c.left+(d?Math.max(b.scrollWidth,b.offsetWidth):b.offsetWidth)-(parseInt(a(b).css("borderLeftWidth"),10)||0)-(parseInt(a(b).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,c.top+(d?Math.max(b.scrollHeight,b.offsetHeight):b.offsetHeight)-(parseInt(a(b).css("borderTopWidth"),10)||0)-(parseInt(a(b).css("paddingBottom"),10)||0)-this.helperProportions.height-
this.margins.top]}},_convertPositionTo:function(c,b){b||(b=this.position);var d="absolute"==c?1:-1,g="absolute"!=this.cssPosition||this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,f=/(html|body)/i.test(g[0].tagName);return{top:b.top+this.offset.relative.top*d+this.offset.parent.top*d-(a.browser.safari&&"fixed"==this.cssPosition?0:("fixed"==this.cssPosition?-this.scrollParent.scrollTop():f?0:g.scrollTop())*d),left:b.left+this.offset.relative.left*
d+this.offset.parent.left*d-(a.browser.safari&&"fixed"==this.cssPosition?0:("fixed"==this.cssPosition?-this.scrollParent.scrollLeft():f?0:g.scrollLeft())*d)}},_generatePosition:function(c){var b=this.options,d="absolute"!=this.cssPosition||this.scrollParent[0]!=document&&a.ui.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,g=/(html|body)/i.test(d[0].tagName);"relative"!=this.cssPosition||this.scrollParent[0]!=document&&this.scrollParent[0]!=this.offsetParent[0]||
(this.offset.relative=this._getRelativeOffset());var f=c.pageX,e=c.pageY;this.originalPosition&&(this.containment&&(c.pageX-this.offset.click.left<this.containment[0]&&(f=this.containment[0]+this.offset.click.left),c.pageY-this.offset.click.top<this.containment[1]&&(e=this.containment[1]+this.offset.click.top),c.pageX-this.offset.click.left>this.containment[2]&&(f=this.containment[2]+this.offset.click.left),c.pageY-this.offset.click.top>this.containment[3]&&(e=this.containment[3]+this.offset.click.top)),
b.grid&&(e=this.originalPageY+Math.round((e-this.originalPageY)/b.grid[1])*b.grid[1],e=this.containment?e-this.offset.click.top<this.containment[1]||e-this.offset.click.top>this.containment[3]?e-this.offset.click.top<this.containment[1]?e+b.grid[1]:e-b.grid[1]:e:e,f=this.originalPageX+Math.round((f-this.originalPageX)/b.grid[0])*b.grid[0],f=this.containment?f-this.offset.click.left<this.containment[0]||f-this.offset.click.left>this.containment[2]?f-this.offset.click.left<this.containment[0]?f+b.grid[0]:
f-b.grid[0]:f:f));return{top:e-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+(a.browser.safari&&"fixed"==this.cssPosition?0:"fixed"==this.cssPosition?-this.scrollParent.scrollTop():g?0:d.scrollTop()),left:f-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+(a.browser.safari&&"fixed"==this.cssPosition?0:"fixed"==this.cssPosition?-this.scrollParent.scrollLeft():g?0:d.scrollLeft())}},_rearrange:function(a,b,d,g){d?d[0].appendChild(this.placeholder[0]):b.item[0].parentNode.insertBefore(this.placeholder[0],
"down"==this.direction?b.item[0]:b.item[0].nextSibling);this.counter=this.counter?++this.counter:1;var f=this,e=this.counter;window.setTimeout(function(){e==f.counter&&f.refreshPositions(!g)},0)},_clear:function(c,b){this.reverting=!1;var d=[];!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem);this._noFinalSort=null;if(this.helper[0]==this.currentItem[0]){for(var g in this._storedCSS)if("auto"==this._storedCSS[g]||"static"==this._storedCSS[g])this._storedCSS[g]=
"";this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();this.fromOutside&&!b&&d.push(function(a){this._trigger("receive",a,this._uiHash(this.fromOutside))});!this.fromOutside&&this.domPosition.prev==this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent==this.currentItem.parent()[0]||b||d.push(function(a){this._trigger("update",a,this._uiHash())});if(!a.ui.contains(this.element[0],this.currentItem[0]))for(b||d.push(function(a){this._trigger("remove",
a,this._uiHash())}),g=this.containers.length-1;0<=g;g--)a.ui.contains(this.containers[g].element[0],this.currentItem[0])&&!b&&(d.push(function(a){return function(e){a._trigger("receive",e,this._uiHash(this))}}.call(this,this.containers[g])),d.push(function(a){return function(e){a._trigger("update",e,this._uiHash(this))}}.call(this,this.containers[g])));for(g=this.containers.length-1;0<=g;g--)b||d.push(function(a){return function(e){a._trigger("deactivate",e,this._uiHash(this))}}.call(this,this.containers[g])),
this.containers[g].containerCache.over&&(d.push(function(a){return function(e){a._trigger("out",e,this._uiHash(this))}}.call(this,this.containers[g])),this.containers[g].containerCache.over=0);this._storedCursor&&a("body").css("cursor",this._storedCursor);this._storedOpacity&&this.helper.css("opacity",this._storedOpacity);this._storedZIndex&&this.helper.css("zIndex","auto"==this._storedZIndex?"":this._storedZIndex);this.dragging=!1;if(this.cancelHelperRemoval){if(!b){this._trigger("beforeStop",c,
this._uiHash());for(g=0;g<d.length;g++)d[g].call(this,c);this._trigger("stop",c,this._uiHash())}return this.fromOutside=!1}b||this._trigger("beforeStop",c,this._uiHash());this.placeholder[0].parentNode.removeChild(this.placeholder[0]);this.helper[0]!=this.currentItem[0]&&this.helper.remove();this.helper=null;if(!b){for(g=0;g<d.length;g++)d[g].call(this,c);this._trigger("stop",c,this._uiHash())}this.fromOutside=!1;return!0},_trigger:function(){!1===a.Widget.prototype._trigger.apply(this,arguments)&&
this.cancel()},_uiHash:function(c){var b=c||this;return{helper:b.helper,placeholder:b.placeholder||a([]),position:b.position,originalPosition:b.originalPosition,offset:b.positionAbs,item:b.currentItem,sender:c?c.element:null}}});a.extend(a.ui.sortable,{version:"1.8.23"})})(jQuery);
jQuery.effects||function(a,p){function c(e){var b;return e&&e.constructor==Array&&3==e.length?e:(b=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(e))?[parseInt(b[1],10),parseInt(b[2],10),parseInt(b[3],10)]:(b=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(e))?[2.55*parseFloat(b[1]),2.55*parseFloat(b[2]),2.55*parseFloat(b[3])]:(b=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(e))?[parseInt(b[1],16),parseInt(b[2],
16),parseInt(b[3],16)]:(b=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(e))?[parseInt(b[1]+b[1],16),parseInt(b[2]+b[2],16),parseInt(b[3]+b[3],16)]:/rgba\(0, 0, 0, 0\)/.exec(e)?h.transparent:h[a.trim(e).toLowerCase()]}function b(){var a=document.defaultView?document.defaultView.getComputedStyle(this,null):this.currentStyle,e={},b,c;if(a&&a.length&&a[0]&&a[a[0]])for(var d=a.length;d--;)b=a[d],"string"==typeof a[b]&&(c=b.replace(/\-(\w)/g,function(a,e){return e.toUpperCase()}),e[c]=a[b]);else for(b in a)"string"===
typeof a[b]&&(e[b]=a[b]);return e}function d(e){var b,c;for(b in e)c=e[b],(null==c||a.isFunction(c)||b in l||/scrollbar/.test(b)||!/color/i.test(b)&&isNaN(parseFloat(c)))&&delete e[b];return e}function g(a,e){var b={_:0},c;for(c in e)a[c]!=e[c]&&(b[c]=e[c]);return b}function f(e,b,c,d){"object"==typeof e&&(d=b,c=null,b=e,e=b.effect);a.isFunction(b)&&(d=b,c=null,b={});if("number"==typeof b||a.fx.speeds[b])d=c,c=b,b={};a.isFunction(c)&&(d=c,c=null);b=b||{};c=c||b.duration;c=a.fx.off?0:"number"==typeof c?
c:c in a.fx.speeds?a.fx.speeds[c]:a.fx.speeds._default;d=d||b.complete;return[e,b,c,d]}function e(e){return!e||"number"===typeof e||a.fx.speeds[e]||"string"===typeof e&&!a.effects[e]?!0:!1}a.effects={};a.each("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor borderColor color outlineColor".split(" "),function(e,b){a.fx.step[b]=function(e){if(!e.colorInit){var d;d=e.elem;var h=b,f;do{f=(a.curCSS||a.css)(d,h);if(""!=f&&"transparent"!=f||a.nodeName(d,"body"))break;h=
"backgroundColor"}while(d=d.parentNode);d=c(f);e.start=d;e.end=c(e.end);e.colorInit=!0}e.elem.style[b]="rgb("+Math.max(Math.min(parseInt(e.pos*(e.end[0]-e.start[0])+e.start[0],10),255),0)+","+Math.max(Math.min(parseInt(e.pos*(e.end[1]-e.start[1])+e.start[1],10),255),0)+","+Math.max(Math.min(parseInt(e.pos*(e.end[2]-e.start[2])+e.start[2],10),255),0)+")"}});var h={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,
139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,
255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0],transparent:[255,255,255]},k=["add","remove","toggle"],l={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};a.effects.animateClass=function(e,c,h,f){a.isFunction(h)&&(f=h,h=null);return this.queue(function(){var l=
a(this),m=l.attr("style")||" ",p=d(b.call(this)),y,A=l.attr("class")||"";a.each(k,function(a,b){if(e[b])l[b+"Class"](e[b])});y=d(b.call(this));l.attr("class",A);l.animate(g(p,y),{queue:!1,duration:c,easing:h,complete:function(){a.each(k,function(a,b){if(e[b])l[b+"Class"](e[b])});"object"==typeof l.attr("style")?(l.attr("style").cssText="",l.attr("style").cssText=m):l.attr("style",m);f&&f.apply(this,arguments);a.dequeue(this)}})})};a.fn.extend({_addClass:a.fn.addClass,addClass:function(e,b,c,d){return b?
a.effects.animateClass.apply(this,[{add:e},b,c,d]):this._addClass(e)},_removeClass:a.fn.removeClass,removeClass:function(e,b,c,d){return b?a.effects.animateClass.apply(this,[{remove:e},b,c,d]):this._removeClass(e)},_toggleClass:a.fn.toggleClass,toggleClass:function(e,b,c,d,h){return"boolean"==typeof b||b===p?c?a.effects.animateClass.apply(this,[b?{add:e}:{remove:e},c,d,h]):this._toggleClass(e,b):a.effects.animateClass.apply(this,[{toggle:e},b,c,d])},switchClass:function(e,b,c,d,h){return a.effects.animateClass.apply(this,
[{add:b,remove:e},c,d,h])}});a.extend(a.effects,{version:"1.8.23",save:function(a,e){for(var b=0;b<e.length;b++)null!==e[b]&&a.data("ec.storage."+e[b],a[0].style[e[b]])},restore:function(a,e){for(var b=0;b<e.length;b++)null!==e[b]&&a.css(e[b],a.data("ec.storage."+e[b]))},setMode:function(a,e){"toggle"==e&&(e=a.is(":hidden")?"show":"hide");return e},getBaseline:function(a,e){var b,c;switch(a[0]){case "top":b=0;break;case "middle":b=.5;break;case "bottom":b=1;break;default:b=a[0]/e.height}switch(a[1]){case "left":c=
0;break;case "center":c=.5;break;case "right":c=1;break;default:c=a[1]/e.width}return{x:c,y:b}},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var b={width:e.outerWidth(!0),height:e.outerHeight(!0),"float":e.css("float")},c=a("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),d=document.activeElement;try{d.id}catch(h){d=document.body}e.wrap(c);(e[0]===d||a.contains(e[0],d))&&a(d).focus();
c=e.parent();"static"==e.css("position")?(c.css({position:"relative"}),e.css({position:"relative"})):(a.extend(b,{position:e.css("position"),zIndex:e.css("z-index")}),a.each(["top","left","bottom","right"],function(a,c){b[c]=e.css(c);isNaN(parseInt(b[c],10))&&(b[c]="auto")}),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"}));return c.css(b).show()},removeWrapper:function(e){var b,c=document.activeElement;return e.parent().is(".ui-effects-wrapper")?(b=e.parent().replaceWith(e),(e[0]===
c||a.contains(e[0],c))&&a(c).focus(),b):e},setTransition:function(e,b,c,d){d=d||{};a.each(b,function(a,b){var h=e.cssUnit(b);0<h[0]&&(d[b]=h[0]*c+h[1])});return d}});a.fn.extend({effect:function(e,b,c,d){var h=f.apply(this,arguments),k={options:h[1],duration:h[2],callback:h[3]},h=k.options.mode,g=a.effects[e];return a.fx.off||!g?h?this[h](k.duration,k.callback):this.each(function(){k.callback&&k.callback.call(this)}):g.call(this,k)},_show:a.fn.show,show:function(a){if(e(a))return this._show.apply(this,
arguments);var b=f.apply(this,arguments);b[1].mode="show";return this.effect.apply(this,b)},_hide:a.fn.hide,hide:function(a){if(e(a))return this._hide.apply(this,arguments);var b=f.apply(this,arguments);b[1].mode="hide";return this.effect.apply(this,b)},__toggle:a.fn.toggle,toggle:function(b){if(e(b)||"boolean"===typeof b||a.isFunction(b))return this.__toggle.apply(this,arguments);var c=f.apply(this,arguments);c[1].mode="toggle";return this.effect.apply(this,c)},cssUnit:function(e){var b=this.css(e),
c=[];a.each(["em","px","%","pt"],function(a,e){0<b.indexOf(e)&&(c=[parseFloat(b),e])});return c}});var m={};a.each(["Quad","Cubic","Quart","Quint","Expo"],function(a,e){m[e]=function(e){return Math.pow(e,a+2)}});a.extend(m,{Sine:function(a){return 1-Math.cos(a*Math.PI/2)},Circ:function(a){return 1-Math.sqrt(1-a*a)},Elastic:function(a){return 0===a||1===a?a:-Math.pow(2,8*(a-1))*Math.sin((80*(a-1)-7.5)*Math.PI/15)},Back:function(a){return a*a*(3*a-2)},Bounce:function(a){for(var e,b=4;a<((e=Math.pow(2,
--b))-1)/11;);return 1/Math.pow(4,3-b)-7.5625*Math.pow((3*e-2)/22-a,2)}});a.each(m,function(e,b){a.easing["easeIn"+e]=b;a.easing["easeOut"+e]=function(a){return 1-b(1-a)};a.easing["easeInOut"+e]=function(a){return.5>a?b(2*a)/2:b(-2*a+2)/-2+1}})}(jQuery);
(function(a,p){a.effects.blind=function(c){return this.queue(function(){var b=a(this),d=["position","top","bottom","left","right"],g=a.effects.setMode(b,c.options.mode||"hide"),f=c.options.direction||"vertical";a.effects.save(b,d);b.show();var e=a.effects.createWrapper(b).css({overflow:"hidden"}),h="vertical"==f?"height":"width",f="vertical"==f?e.height():e.width();"show"==g&&e.css(h,0);var k={};k[h]="show"==g?f:0;e.animate(k,c.duration,c.options.easing,function(){"hide"==g&&b.hide();a.effects.restore(b,
d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(b[0],arguments);b.dequeue()})})}})(jQuery);
(function(a,p){a.effects.bounce=function(c){return this.queue(function(){var b=a(this),d=["position","top","bottom","left","right"],g=a.effects.setMode(b,c.options.mode||"effect"),f=c.options.direction||"up",e=c.options.distance||20,h=c.options.times||5,k=c.duration||250;/show|hide/.test(g)&&d.push("opacity");a.effects.save(b,d);b.show();a.effects.createWrapper(b);var l="up"==f||"down"==f?"top":"left",f="up"==f||"left"==f?"pos":"neg",e=c.options.distance||("top"==l?b.outerHeight(!0)/3:b.outerWidth(!0)/
3);"show"==g&&b.css("opacity",0).css(l,"pos"==f?-e:e);"hide"==g&&(e/=2*h);"hide"!=g&&h--;if("show"==g){var m={opacity:1};m[l]=("pos"==f?"+=":"-=")+e;b.animate(m,k/2,c.options.easing);e/=2;h--}for(m=0;m<h;m++){var n={},u={};n[l]=("pos"==f?"-=":"+=")+e;u[l]=("pos"==f?"+=":"-=")+e;b.animate(n,k/2,c.options.easing).animate(u,k/2,c.options.easing);e="hide"==g?2*e:e/2}"hide"==g?(m={opacity:0},m[l]=("pos"==f?"-=":"+=")+e,b.animate(m,k/2,c.options.easing,function(){b.hide();a.effects.restore(b,d);a.effects.removeWrapper(b);
c.callback&&c.callback.apply(this,arguments)})):(n={},u={},n[l]=("pos"==f?"-=":"+=")+e,u[l]=("pos"==f?"+=":"-=")+e,b.animate(n,k/2,c.options.easing).animate(u,k/2,c.options.easing,function(){a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(this,arguments)}));b.queue("fx",function(){b.dequeue()});b.dequeue()})}})(jQuery);
(function(a,p){a.effects.clip=function(c){return this.queue(function(){var b=a(this),d="position top bottom left right height width".split(" "),g=a.effects.setMode(b,c.options.mode||"hide"),f=c.options.direction||"vertical";a.effects.save(b,d);b.show();var e=a.effects.createWrapper(b).css({overflow:"hidden"}),e="IMG"==b[0].tagName?e:b,h="vertical"==f?"height":"width",k="vertical"==f?"top":"left",f="vertical"==f?e.height():e.width();"show"==g&&(e.css(h,0),e.css(k,f/2));var l={};l[h]="show"==g?f:0;
l[k]="show"==g?0:f/2;e.animate(l,{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){"hide"==g&&b.hide();a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(b[0],arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.drop=function(c){return this.queue(function(){var b=a(this),d="position top bottom left right opacity".split(" "),g=a.effects.setMode(b,c.options.mode||"hide"),f=c.options.direction||"left";a.effects.save(b,d);b.show();a.effects.createWrapper(b);var e="up"==f||"down"==f?"top":"left",f="up"==f||"left"==f?"pos":"neg",h=c.options.distance||("top"==e?b.outerHeight(!0)/2:b.outerWidth(!0)/2);"show"==g&&b.css("opacity",0).css(e,"pos"==f?-h:h);var k={opacity:"show"==g?1:0};k[e]=("show"==
g?"pos"==f?"+=":"-=":"pos"==f?"-=":"+=")+h;b.animate(k,{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){"hide"==g&&b.hide();a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(this,arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.explode=function(c){return this.queue(function(){var b=c.options.pieces?Math.round(Math.sqrt(c.options.pieces)):3,d=c.options.pieces?Math.round(Math.sqrt(c.options.pieces)):3;c.options.mode="toggle"==c.options.mode?a(this).is(":visible")?"hide":"show":c.options.mode;var g=a(this).show().css("visibility","hidden"),f=g.offset();f.top-=parseInt(g.css("marginTop"),10)||0;f.left-=parseInt(g.css("marginLeft"),10)||0;for(var e=g.outerWidth(!0),h=g.outerHeight(!0),k=0;k<b;k++)for(var l=
0;l<d;l++)g.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:e/d*-l,top:h/b*-k}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:e/d,height:h/b,left:f.left+e/d*l+("show"==c.options.mode?e/d*(l-Math.floor(d/2)):0),top:f.top+h/b*k+("show"==c.options.mode?h/b*(k-Math.floor(b/2)):0),opacity:"show"==c.options.mode?0:1}).animate({left:f.left+e/d*l+("show"==c.options.mode?0:e/d*(l-Math.floor(d/2))),top:f.top+h/b*k+("show"==
c.options.mode?0:h/b*(k-Math.floor(b/2))),opacity:"show"==c.options.mode?1:0},c.duration||500);setTimeout(function(){"show"==c.options.mode?g.css({visibility:"visible"}):g.css({visibility:"visible"}).hide();c.callback&&c.callback.apply(g[0]);g.dequeue();a("div.ui-effects-explode").remove()},c.duration||500)})}})(jQuery);
(function(a,p){a.effects.fade=function(c){return this.queue(function(){var b=a(this),d=a.effects.setMode(b,c.options.mode||"hide");b.animate({opacity:d},{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){c.callback&&c.callback.apply(this,arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.fold=function(c){return this.queue(function(){var b=a(this),d=["position","top","bottom","left","right"],g=a.effects.setMode(b,c.options.mode||"hide"),f=c.options.size||15,e=!!c.options.horizFirst,h=c.duration?c.duration/2:a.fx.speeds._default/2;a.effects.save(b,d);b.show();var k=a.effects.createWrapper(b).css({overflow:"hidden"}),l="show"==g!=e,m=l?["width","height"]:["height","width"],l=l?[k.width(),k.height()]:[k.height(),k.width()],n=/([0-9]+)%/.exec(f);n&&(f=parseInt(n[1],
10)/100*l["hide"==g?0:1]);"show"==g&&k.css(e?{height:0,width:f}:{height:f,width:0});e={};n={};e[m[0]]="show"==g?l[0]:f;n[m[1]]="show"==g?l[1]:0;k.animate(e,h,c.options.easing).animate(n,h,c.options.easing,function(){"hide"==g&&b.hide();a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(b[0],arguments);b.dequeue()})})}})(jQuery);
(function(a,p){a.effects.highlight=function(c){return this.queue(function(){var b=a(this),d=["backgroundImage","backgroundColor","opacity"],g=a.effects.setMode(b,c.options.mode||"show"),f={backgroundColor:b.css("backgroundColor")};"hide"==g&&(f.opacity=0);a.effects.save(b,d);b.show().css({backgroundImage:"none",backgroundColor:c.options.color||"#ffff99"}).animate(f,{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){"hide"==g&&b.hide();a.effects.restore(b,d);"show"==g&&!a.support.opacity&&
this.style.removeAttribute("filter");c.callback&&c.callback.apply(this,arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.pulsate=function(c){return this.queue(function(){var b=a(this),d=a.effects.setMode(b,c.options.mode||"show"),g=2*(c.options.times||5)-1,f=c.duration?c.duration/2:a.fx.speeds._default/2,e=b.is(":visible"),h=0;e||(b.css("opacity",0).show(),h=1);("hide"==d&&e||"show"==d&&!e)&&g--;for(d=0;d<g;d++)b.animate({opacity:h},f,c.options.easing),h=(h+1)%2;b.animate({opacity:h},f,c.options.easing,function(){0==h&&b.hide();c.callback&&c.callback.apply(this,arguments)});b.queue("fx",function(){b.dequeue()}).dequeue()})}})(jQuery);
(function(a,p){a.effects.puff=function(c){return this.queue(function(){var b=a(this),d=a.effects.setMode(b,c.options.mode||"hide"),g=parseInt(c.options.percent,10)||150,f=g/100,e={height:b.height(),width:b.width()};a.extend(c.options,{fade:!0,mode:d,percent:"hide"==d?g:100,from:"hide"==d?e:{height:e.height*f,width:e.width*f}});b.effect("scale",c.options,c.duration,c.callback);b.dequeue()})};a.effects.scale=function(c){return this.queue(function(){var b=a(this),d=a.extend(!0,{},c.options),g=a.effects.setMode(b,
c.options.mode||"effect"),f=parseInt(c.options.percent,10)||(0==parseInt(c.options.percent,10)?0:"hide"==g?0:100),e=c.options.direction||"both",h=c.options.origin;"effect"!=g&&(d.origin=h||["middle","center"],d.restore=!0);h={height:b.height(),width:b.width()};b.from=c.options.from||("show"==g?{height:0,width:0}:h);b.to={height:h.height*("horizontal"!=e?f/100:1),width:h.width*("vertical"!=e?f/100:1)};c.options.fade&&("show"==g&&(b.from.opacity=0,b.to.opacity=1),"hide"==g&&(b.from.opacity=1,b.to.opacity=
0));d.from=b.from;d.to=b.to;d.mode=g;b.effect("size",d,c.duration,c.callback);b.dequeue()})};a.effects.size=function(c){return this.queue(function(){var b=a(this),d="position top bottom left right width height overflow opacity".split(" "),g="position top bottom left right overflow opacity".split(" "),f=["width","height","overflow"],e=["fontSize"],h=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],k=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],l=a.effects.setMode(b,
c.options.mode||"effect"),m=c.options.restore||!1,n=c.options.scale||"both",u=c.options.origin,q={height:b.height(),width:b.width()};b.from=c.options.from||q;b.to=c.options.to||q;u&&(u=a.effects.getBaseline(u,q),b.from.top=(q.height-b.from.height)*u.y,b.from.left=(q.width-b.from.width)*u.x,b.to.top=(q.height-b.to.height)*u.y,b.to.left=(q.width-b.to.width)*u.x);var v=b.from.height/q.height,r=b.from.width/q.width,p=b.to.height/q.height,w=b.to.width/q.width;if("box"==n||"both"==n)v!=p&&(d=d.concat(h),
b.from=a.effects.setTransition(b,h,v,b.from),b.to=a.effects.setTransition(b,h,p,b.to)),r!=w&&(d=d.concat(k),b.from=a.effects.setTransition(b,k,r,b.from),b.to=a.effects.setTransition(b,k,w,b.to));"content"!=n&&"both"!=n||v==p||(d=d.concat(e),b.from=a.effects.setTransition(b,e,v,b.from),b.to=a.effects.setTransition(b,e,p,b.to));a.effects.save(b,m?d:g);b.show();a.effects.createWrapper(b);b.css("overflow","hidden").css(b.from);if("content"==n||"both"==n)h=h.concat(["marginTop","marginBottom"]).concat(e),
k=k.concat(["marginLeft","marginRight"]),f=d.concat(h).concat(k),b.find("*[width]").each(function(){var e=a(this);m&&a.effects.save(e,f);var b=e.height(),d=e.width();e.from={height:b*v,width:d*r};e.to={height:b*p,width:d*w};v!=p&&(e.from=a.effects.setTransition(e,h,v,e.from),e.to=a.effects.setTransition(e,h,p,e.to));r!=w&&(e.from=a.effects.setTransition(e,k,r,e.from),e.to=a.effects.setTransition(e,k,w,e.to));e.css(e.from);e.animate(e.to,c.duration,c.options.easing,function(){m&&a.effects.restore(e,
f)})});b.animate(b.to,{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){0===b.to.opacity&&b.css("opacity",b.from.opacity);"hide"==l&&b.hide();a.effects.restore(b,m?d:g);a.effects.removeWrapper(b);c.callback&&c.callback.apply(this,arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.shake=function(c){return this.queue(function(){var b=a(this),d=["position","top","bottom","left","right"];a.effects.setMode(b,c.options.mode||"effect");var g=c.options.direction||"left",f=c.options.distance||20,e=c.options.times||3,h=c.duration||c.options.duration||140;a.effects.save(b,d);b.show();a.effects.createWrapper(b);var k="up"==g||"down"==g?"top":"left",l="up"==g||"left"==g?"pos":"neg",g={},m={},n={};g[k]=("pos"==l?"-=":"+=")+f;m[k]=("pos"==l?"+=":"-=")+2*f;n[k]=("pos"==
l?"-=":"+=")+2*f;b.animate(g,h,c.options.easing);for(f=1;f<e;f++)b.animate(m,h,c.options.easing).animate(n,h,c.options.easing);b.animate(m,h,c.options.easing).animate(g,h/2,c.options.easing,function(){a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(this,arguments)});b.queue("fx",function(){b.dequeue()});b.dequeue()})}})(jQuery);
(function(a,p){a.effects.slide=function(c){return this.queue(function(){var b=a(this),d=["position","top","bottom","left","right"],g=a.effects.setMode(b,c.options.mode||"show"),f=c.options.direction||"left";a.effects.save(b,d);b.show();a.effects.createWrapper(b).css({overflow:"hidden"});var e="up"==f||"down"==f?"top":"left",f="up"==f||"left"==f?"pos":"neg",h=c.options.distance||("top"==e?b.outerHeight(!0):b.outerWidth(!0));"show"==g&&b.css(e,"pos"==f?isNaN(h)?"-"+h:-h:h);var k={};k[e]=("show"==g?
"pos"==f?"+=":"-=":"pos"==f?"-=":"+=")+h;b.animate(k,{queue:!1,duration:c.duration,easing:c.options.easing,complete:function(){"hide"==g&&b.hide();a.effects.restore(b,d);a.effects.removeWrapper(b);c.callback&&c.callback.apply(this,arguments);b.dequeue()}})})}})(jQuery);
(function(a,p){a.effects.transfer=function(c){return this.queue(function(){var b=a(this),d=a(c.options.to),g=d.offset(),d={top:g.top,left:g.left,height:d.innerHeight(),width:d.innerWidth()},g=b.offset(),f=a('<div class="ui-effects-transfer"></div>').appendTo(document.body).addClass(c.options.className).css({top:g.top,left:g.left,height:b.innerHeight(),width:b.innerWidth(),position:"absolute"}).animate(d,c.duration,c.options.easing,function(){f.remove();c.callback&&c.callback.apply(b[0],arguments);
b.dequeue()})})}})(jQuery);
(function(a,p){a.widget("ui.accordion",{options:{active:0,animated:"slide",autoHeight:!0,clearStyle:!1,collapsible:!1,event:"click",fillSpace:!1,header:"> li > :first-child,> :not(li):even",icons:{header:"ui-icon-triangle-1-e",headerSelected:"ui-icon-triangle-1-s"},navigation:!1,navigationFilter:function(){return this.href.toLowerCase()===location.href.toLowerCase()}},_create:function(){var c=this,b=c.options;c.running=0;c.element.addClass("ui-accordion ui-widget ui-helper-reset").children("li").addClass("ui-accordion-li-fix");c.headers=
c.element.find(b.header).addClass("ui-accordion-header ui-helper-reset ui-state-default ui-corner-all").bind("mouseenter.accordion",function(){b.disabled||a(this).addClass("ui-state-hover")}).bind("mouseleave.accordion",function(){b.disabled||a(this).removeClass("ui-state-hover")}).bind("focus.accordion",function(){b.disabled||a(this).addClass("ui-state-focus")}).bind("blur.accordion",function(){b.disabled||a(this).removeClass("ui-state-focus")});c.headers.next().addClass("ui-accordion-content ui-helper-reset ui-widget-content ui-corner-bottom");
if(b.navigation){var d=c.element.find("a").filter(b.navigationFilter).eq(0);if(d.length){var g=d.closest(".ui-accordion-header");c.active=g.length?g:d.closest(".ui-accordion-content").prev()}}c.active=c._findActive(c.active||b.active).addClass("ui-state-default ui-state-active").toggleClass("ui-corner-all").toggleClass("ui-corner-top");c.active.next().addClass("ui-accordion-content-active");c._createIcons();c.resize();c.element.attr("role","tablist");c.headers.attr("role","tab").bind("keydown.accordion",
function(a){return c._keydown(a)}).next().attr("role","tabpanel");c.headers.not(c.active||"").attr({"aria-expanded":"false","aria-selected":"false",tabIndex:-1}).next().hide();c.active.length?c.active.attr({"aria-expanded":"true","aria-selected":"true",tabIndex:0}):c.headers.eq(0).attr("tabIndex",0);a.browser.safari||c.headers.find("a").attr("tabIndex",-1);b.event&&c.headers.bind(b.event.split(" ").join(".accordion ")+".accordion",function(a){c._clickHandler.call(c,a,this);a.preventDefault()})},_createIcons:function(){var c=
this.options;c.icons&&(a("<span></span>").addClass("ui-icon "+c.icons.header).prependTo(this.headers),this.active.children(".ui-icon").toggleClass(c.icons.header).toggleClass(c.icons.headerSelected),this.element.addClass("ui-accordion-icons"))},_destroyIcons:function(){this.headers.children(".ui-icon").remove();this.element.removeClass("ui-accordion-icons")},destroy:function(){var c=this.options;this.element.removeClass("ui-accordion ui-widget ui-helper-reset").removeAttr("role");this.headers.unbind(".accordion").removeClass("ui-accordion-header ui-accordion-disabled ui-helper-reset ui-state-default ui-corner-all ui-state-active ui-state-disabled ui-corner-top").removeAttr("role").removeAttr("aria-expanded").removeAttr("aria-selected").removeAttr("tabIndex");
this.headers.find("a").removeAttr("tabIndex");this._destroyIcons();var b=this.headers.next().css("display","").removeAttr("role").removeClass("ui-helper-reset ui-widget-content ui-corner-bottom ui-accordion-content ui-accordion-content-active ui-accordion-disabled ui-state-disabled");(c.autoHeight||c.fillHeight)&&b.css("height","");return a.Widget.prototype.destroy.call(this)},_setOption:function(c,b){a.Widget.prototype._setOption.apply(this,arguments);"active"==c&&this.activate(b);"icons"==c&&(this._destroyIcons(),
b&&this._createIcons());if("disabled"==c)this.headers.add(this.headers.next())[b?"addClass":"removeClass"]("ui-accordion-disabled ui-state-disabled")},_keydown:function(c){if(!(this.options.disabled||c.altKey||c.ctrlKey)){var b=a.ui.keyCode,d=this.headers.length,g=this.headers.index(c.target),f=!1;switch(c.keyCode){case b.RIGHT:case b.DOWN:f=this.headers[(g+1)%d];break;case b.LEFT:case b.UP:f=this.headers[(g-1+d)%d];break;case b.SPACE:case b.ENTER:this._clickHandler({target:c.target},c.target),c.preventDefault()}return f?
(a(c.target).attr("tabIndex",-1),a(f).attr("tabIndex",0),f.focus(),!1):!0}},resize:function(){var c=this.options,b;if(c.fillSpace){if(a.browser.msie){var d=this.element.parent().css("overflow");this.element.parent().css("overflow","hidden")}b=this.element.parent().height();a.browser.msie&&this.element.parent().css("overflow",d);this.headers.each(function(){b-=a(this).outerHeight(!0)});this.headers.next().each(function(){a(this).height(Math.max(0,b-a(this).innerHeight()+a(this).height()))}).css("overflow",
"auto")}else c.autoHeight&&(b=0,this.headers.next().each(function(){b=Math.max(b,a(this).height("").height())}).height(b));return this},activate:function(a){this.options.active=a;a=this._findActive(a)[0];this._clickHandler({target:a},a);return this},_findActive:function(c){return c?"number"===typeof c?this.headers.filter(":eq("+c+")"):this.headers.not(this.headers.not(c)):!1===c?a([]):this.headers.filter(":eq(0)")},_clickHandler:function(c,b){var d=this.options;if(!d.disabled)if(c.target){var g=a(c.currentTarget||
b),f=g[0]===this.active[0];d.active=d.collapsible&&f?!1:this.headers.index(g);if(!(this.running||!d.collapsible&&f)){var e=this.active,h=g.next(),k=this.active.next(),l={options:d,newHeader:f&&d.collapsible?a([]):g,oldHeader:this.active,newContent:f&&d.collapsible?a([]):h,oldContent:k},m=this.headers.index(this.active[0])>this.headers.index(g[0]);this.active=f?a([]):g;this._toggle(h,k,l,f,m);e.removeClass("ui-state-active ui-corner-top").addClass("ui-state-default ui-corner-all").children(".ui-icon").removeClass(d.icons.headerSelected).addClass(d.icons.header);
f||(g.removeClass("ui-state-default ui-corner-all").addClass("ui-state-active ui-corner-top").children(".ui-icon").removeClass(d.icons.header).addClass(d.icons.headerSelected),g.next().addClass("ui-accordion-content-active"))}}else if(d.collapsible){this.active.removeClass("ui-state-active ui-corner-top").addClass("ui-state-default ui-corner-all").children(".ui-icon").removeClass(d.icons.headerSelected).addClass(d.icons.header);this.active.next().addClass("ui-accordion-content-active");var k=this.active.next(),
l={options:d,newHeader:a([]),oldHeader:d.active,newContent:a([]),oldContent:k},h=this.active=a([]);this._toggle(h,k,l)}},_toggle:function(c,b,d,g,f){var e=this,h=e.options;e.toShow=c;e.toHide=b;e.data=d;var k=function(){if(e)return e._completed.apply(e,arguments)};e._trigger("changestart",null,e.data);e.running=0===b.size()?c.size():b.size();if(h.animated){d={};d=h.collapsible&&g?{toShow:a([]),toHide:b,complete:k,down:f,autoHeight:h.autoHeight||h.fillSpace}:{toShow:c,toHide:b,complete:k,down:f,autoHeight:h.autoHeight||
h.fillSpace};h.proxied||(h.proxied=h.animated);h.proxiedDuration||(h.proxiedDuration=h.duration);h.animated=a.isFunction(h.proxied)?h.proxied(d):h.proxied;h.duration=a.isFunction(h.proxiedDuration)?h.proxiedDuration(d):h.proxiedDuration;g=a.ui.accordion.animations;var l=h.duration,m=h.animated;!m||g[m]||a.easing[m]||(m="slide");g[m]||(g[m]=function(a){this.slide(a,{easing:m,duration:l||700})});g[m](d)}else h.collapsible&&g?c.toggle():(b.hide(),c.show()),k(!0);b.prev().attr({"aria-expanded":"false",
"aria-selected":"false",tabIndex:-1}).blur();c.prev().attr({"aria-expanded":"true","aria-selected":"true",tabIndex:0}).focus()},_completed:function(a){this.running=a?0:--this.running;this.running||(this.options.clearStyle&&this.toShow.add(this.toHide).css({height:"",overflow:""}),this.toHide.removeClass("ui-accordion-content-active"),this.toHide.length&&(this.toHide.parent()[0].className=this.toHide.parent()[0].className),this._trigger("change",null,this.data))}});a.extend(a.ui.accordion,{version:"1.8.23",
animations:{slide:function(c,b){c=a.extend({easing:"swing",duration:300},c,b);if(c.toHide.size())if(c.toShow.size()){var d=c.toShow.css("overflow"),g=0,f={},e={},h,k=c.toShow;h=k[0].style.width;k.width(k.parent().width()-parseFloat(k.css("paddingLeft"))-parseFloat(k.css("paddingRight"))-(parseFloat(k.css("borderLeftWidth"))||0)-(parseFloat(k.css("borderRightWidth"))||0));a.each(["height","paddingTop","paddingBottom"],function(b,d){e[d]="hide";var h=(""+a.css(c.toShow[0],d)).match(/^([\d+-.]+)(.*)$/);
f[d]={value:h[1],unit:h[2]||"px"}});c.toShow.css({height:0,overflow:"hidden"}).show();c.toHide.filter(":hidden").each(c.complete).end().filter(":visible").animate(e,{step:function(a,e){"height"==e.prop&&(g=0===e.end-e.start?0:(e.now-e.start)/(e.end-e.start));c.toShow[0].style[e.prop]=g*f[e.prop].value+f[e.prop].unit},duration:c.duration,easing:c.easing,complete:function(){c.autoHeight||c.toShow.css("height","");c.toShow.css({width:h,overflow:d});c.complete()}})}else c.toHide.animate({height:"hide",
paddingTop:"hide",paddingBottom:"hide"},c);else c.toShow.animate({height:"show",paddingTop:"show",paddingBottom:"show"},c)},bounceslide:function(a){this.slide(a,{easing:a.down?"easeOutBounce":"swing",duration:a.down?1E3:200})}}})})(jQuery);
(function(a,p){var c=0;a.widget("ui.autocomplete",{options:{appendTo:"body",autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null},pending:0,_create:function(){var b=this,c=this.element[0].ownerDocument,g;this.isMultiLine=this.element.is("textarea");this.element.addClass("ui-autocomplete-input").attr("autocomplete","off").attr({role:"textbox","aria-autocomplete":"list","aria-haspopup":"true"}).bind("keydown.autocomplete",function(c){if(!b.options.disabled&&
!b.element.propAttr("readOnly")){g=!1;var e=a.ui.keyCode;switch(c.keyCode){case e.PAGE_UP:b._move("previousPage",c);break;case e.PAGE_DOWN:b._move("nextPage",c);break;case e.UP:b._keyEvent("previous",c);break;case e.DOWN:b._keyEvent("next",c);break;case e.ENTER:case e.NUMPAD_ENTER:b.menu.active&&(g=!0,c.preventDefault());case e.TAB:if(!b.menu.active)break;b.menu.select(c);break;case e.ESCAPE:b.element.val(b.term);b.close(c);break;default:clearTimeout(b.searching),b.searching=setTimeout(function(){b.term!=
b.element.val()&&(b.selectedItem=null,b.search(null,c))},b.options.delay)}}}).bind("keypress.autocomplete",function(a){g&&(g=!1,a.preventDefault())}).bind("focus.autocomplete",function(){b.options.disabled||(b.selectedItem=null,b.previous=b.element.val())}).bind("blur.autocomplete",function(a){b.options.disabled||(clearTimeout(b.searching),b.closing=setTimeout(function(){b.close(a);b._change(a)},150))});this._initSource();this.menu=a("<ul></ul>").addClass("ui-autocomplete").appendTo(a(this.options.appendTo||
"body",c)[0]).mousedown(function(c){var e=b.menu.element[0];a(c.target).closest(".ui-menu-item").length||setTimeout(function(){a(document).one("mousedown",function(c){c.target===b.element[0]||c.target===e||a.ui.contains(e,c.target)||b.close()})},1);setTimeout(function(){clearTimeout(b.closing)},13)}).menu({focus:function(a,e){var c=e.item.data("item.autocomplete");!1!==b._trigger("focus",a,{item:c})&&/^key/.test(a.originalEvent.type)&&b.element.val(c.value)},selected:function(a,e){var h=e.item.data("item.autocomplete"),
k=b.previous;b.element[0]!==c.activeElement&&(b.element.focus(),b.previous=k,setTimeout(function(){b.previous=k;b.selectedItem=h},1));!1!==b._trigger("select",a,{item:h})&&b.element.val(h.value);b.term=b.element.val();b.close(a);b.selectedItem=h},blur:function(a,e){b.menu.element.is(":visible")&&b.element.val()!==b.term&&b.element.val(b.term)}}).zIndex(this.element.zIndex()+1).css({top:0,left:0}).hide().data("menu");a.fn.bgiframe&&this.menu.element.bgiframe();b.beforeunloadHandler=function(){b.element.removeAttr("autocomplete")};
a(window).bind("beforeunload",b.beforeunloadHandler)},destroy:function(){this.element.removeClass("ui-autocomplete-input").removeAttr("autocomplete").removeAttr("role").removeAttr("aria-autocomplete").removeAttr("aria-haspopup");this.menu.element.remove();a(window).unbind("beforeunload",this.beforeunloadHandler);a.Widget.prototype.destroy.call(this)},_setOption:function(b,c){a.Widget.prototype._setOption.apply(this,arguments);"source"===b&&this._initSource();"appendTo"===b&&this.menu.element.appendTo(a(c||
"body",this.element[0].ownerDocument)[0]);"disabled"===b&&c&&this.xhr&&this.xhr.abort()},_initSource:function(){var b=this,c,g;a.isArray(this.options.source)?(c=this.options.source,this.source=function(b,e){e(a.ui.autocomplete.filter(c,b.term))}):"string"===typeof this.options.source?(g=this.options.source,this.source=function(c,e){b.xhr&&b.xhr.abort();b.xhr=a.ajax({url:g,data:c,dataType:"json",success:function(a,b){e(a)},error:function(){e([])}})}):this.source=this.options.source},search:function(a,
c){a=null!=a?a:this.element.val();this.term=this.element.val();if(a.length<this.options.minLength)return this.close(c);clearTimeout(this.closing);if(!1!==this._trigger("search",c))return this._search(a)},_search:function(a){this.pending++;this.element.addClass("ui-autocomplete-loading");this.source({term:a},this._response())},_response:function(){var a=this,d=++c;return function(g){d===c&&a.__response(g);a.pending--;a.pending||a.element.removeClass("ui-autocomplete-loading")}},__response:function(a){!this.options.disabled&&
a&&a.length?(a=this._normalize(a),this._suggest(a),this._trigger("open")):this.close()},close:function(a){clearTimeout(this.closing);this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.deactivate(),this._trigger("close",a))},_change:function(a){this.previous!==this.element.val()&&this._trigger("change",a,{item:this.selectedItem})},_normalize:function(b){return b.length&&b[0].label&&b[0].value?b:a.map(b,function(b){return"string"===typeof b?{label:b,value:b}:a.extend({label:b.label||
b.value,value:b.value||b.label},b)})},_suggest:function(b){var c=this.menu.element.empty().zIndex(this.element.zIndex()+1);this._renderMenu(c,b);this.menu.deactivate();this.menu.refresh();c.show();this._resizeMenu();c.position(a.extend({of:this.element},this.options.position));this.options.autoFocus&&this.menu.next(new a.Event("mouseover"))},_resizeMenu:function(){var a=this.menu.element;a.outerWidth(Math.max(a.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(b,c){var g=
this;a.each(c,function(a,e){g._renderItem(b,e)})},_renderItem:function(b,c){return a("<li></li>").data("item.autocomplete",c).append(a("<a></a>").text(c.label)).appendTo(b)},_move:function(a,c){if(this.menu.element.is(":visible"))if(this.menu.first()&&/^previous/.test(a)||this.menu.last()&&/^next/.test(a))this.element.val(this.term),this.menu.deactivate();else this.menu[a](c);else this.search(null,c)},widget:function(){return this.menu.element},_keyEvent:function(a,c){if(!this.isMultiLine||this.menu.element.is(":visible"))this._move(a,
c),c.preventDefault()}});a.extend(a.ui.autocomplete,{escapeRegex:function(a){return a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")},filter:function(b,c){var g=new RegExp(a.ui.autocomplete.escapeRegex(c),"i");return a.grep(b,function(a){return g.test(a.label||a.value||a)})}})})(jQuery);
(function(a){a.widget("ui.menu",{_create:function(){var p=this;this.element.addClass("ui-menu ui-widget ui-widget-content ui-corner-all").attr({role:"listbox","aria-activedescendant":"ui-active-menuitem"}).click(function(c){a(c.target).closest(".ui-menu-item a").length&&(c.preventDefault(),p.select(c))});this.refresh()},refresh:function(){var p=this;this.element.children("li:not(.ui-menu-item):has(a)").addClass("ui-menu-item").attr("role","menuitem").children("a").addClass("ui-corner-all").attr("tabindex",
-1).mouseenter(function(c){p.activate(c,a(this).parent())}).mouseleave(function(){p.deactivate()})},activate:function(a,c){this.deactivate();if(this.hasScroll()){var b=c.offset().top-this.element.offset().top,d=this.element.scrollTop(),g=this.element.height();0>b?this.element.scrollTop(d+b):b>=g&&this.element.scrollTop(d+b-g+c.height())}this.active=c.eq(0).children("a").addClass("ui-state-hover").attr("id","ui-active-menuitem").end();this._trigger("focus",a,{item:c})},deactivate:function(){this.active&&
(this.active.children("a").removeClass("ui-state-hover").removeAttr("id"),this._trigger("blur"),this.active=null)},next:function(a){this.move("next",".ui-menu-item:first",a)},previous:function(a){this.move("prev",".ui-menu-item:last",a)},first:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},last:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},move:function(a,c,b){this.active?(a=this.active[a+"All"](".ui-menu-item").eq(0),a.length?this.activate(b,
a):this.activate(b,this.element.children(c))):this.activate(b,this.element.children(c))},nextPage:function(p){if(this.hasScroll())if(!this.active||this.last())this.activate(p,this.element.children(".ui-menu-item:first"));else{var c=this.active.offset().top,b=this.element.height(),d=this.element.children(".ui-menu-item").filter(function(){var d=a(this).offset().top-c-b+a(this).height();return 10>d&&-10<d});d.length||(d=this.element.children(".ui-menu-item:last"));this.activate(p,d)}else this.activate(p,
this.element.children(".ui-menu-item").filter(!this.active||this.last()?":first":":last"))},previousPage:function(p){if(this.hasScroll())if(!this.active||this.first())this.activate(p,this.element.children(".ui-menu-item:last"));else{var c=this.active.offset().top,b=this.element.height(),d=this.element.children(".ui-menu-item").filter(function(){var d=a(this).offset().top-c+b-a(this).height();return 10>d&&-10<d});d.length||(d=this.element.children(".ui-menu-item:first"));this.activate(p,d)}else this.activate(p,
this.element.children(".ui-menu-item").filter(!this.active||this.first()?":last":":first"))},hasScroll:function(){return this.element.height()<this.element[a.fn.prop?"prop":"attr"]("scrollHeight")},select:function(a){this._trigger("selected",a,{item:this.active})}})})(jQuery);
(function(a,p){var c,b,d,g,f=function(){var e=a(this).find(":ui-button");setTimeout(function(){e.button("refresh")},1)},e=function(e){var b=e.name,c=e.form,d=a([]);b&&(d=c?a(c).find("[name='"+b+"']"):a("[name='"+b+"']",e.ownerDocument).filter(function(){return!this.form}));return d};a.widget("ui.button",{options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset.button").bind("reset.button",f);"boolean"!==typeof this.options.disabled?
this.options.disabled=!!this.element.propAttr("disabled"):this.element.propAttr("disabled",this.options.disabled);this._determineButtonType();this.hasTitle=!!this.buttonElement.attr("title");var h=this,k=this.options,l="checkbox"===this.type||"radio"===this.type,m="ui-state-hover"+(l?"":" ui-state-active");null===k.label&&(k.label=this.buttonElement.html());this.buttonElement.addClass("ui-button ui-widget ui-state-default ui-corner-all").attr("role","button").bind("mouseenter.button",function(){k.disabled||
(a(this).addClass("ui-state-hover"),this===c&&a(this).addClass("ui-state-active"))}).bind("mouseleave.button",function(){k.disabled||a(this).removeClass(m)}).bind("click.button",function(a){k.disabled&&(a.preventDefault(),a.stopImmediatePropagation())});this.element.bind("focus.button",function(){h.buttonElement.addClass("ui-state-focus")}).bind("blur.button",function(){h.buttonElement.removeClass("ui-state-focus")});l&&(this.element.bind("change.button",function(){g||h.refresh()}),this.buttonElement.bind("mousedown.button",
function(a){k.disabled||(g=!1,b=a.pageX,d=a.pageY)}).bind("mouseup.button",function(a){k.disabled||b===a.pageX&&d===a.pageY||(g=!0)}));"checkbox"===this.type?this.buttonElement.bind("click.button",function(){if(k.disabled||g)return!1;a(this).toggleClass("ui-state-active");h.buttonElement.attr("aria-pressed",h.element[0].checked)}):"radio"===this.type?this.buttonElement.bind("click.button",function(){if(k.disabled||g)return!1;a(this).addClass("ui-state-active");h.buttonElement.attr("aria-pressed",
"true");var b=h.element[0];e(b).not(b).map(function(){return a(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown.button",function(){if(k.disabled)return!1;a(this).addClass("ui-state-active");c=this;a(document).one("mouseup",function(){c=null})}).bind("mouseup.button",function(){if(k.disabled)return!1;a(this).removeClass("ui-state-active")}).bind("keydown.button",function(e){if(k.disabled)return!1;e.keyCode!=a.ui.keyCode.SPACE&&
e.keyCode!=a.ui.keyCode.ENTER||a(this).addClass("ui-state-active")}).bind("keyup.button",function(){a(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(e){e.keyCode===a.ui.keyCode.SPACE&&a(this).click()}));this._setOption("disabled",k.disabled);this._resetButton()},_determineButtonType:function(){this.element.is(":checkbox")?this.type="checkbox":this.element.is(":radio")?this.type="radio":this.element.is("input")?this.type="input":this.type="button";
if("checkbox"===this.type||"radio"===this.type){var a=this.element.parents().filter(":last"),e="label[for='"+this.element.attr("id")+"']";this.buttonElement=a.find(e);this.buttonElement.length||(a=a.length?a.siblings():this.element.siblings(),this.buttonElement=a.filter(e),this.buttonElement.length||(this.buttonElement=a.find(e)));this.element.addClass("ui-helper-hidden-accessible");(a=this.element.is(":checked"))&&this.buttonElement.addClass("ui-state-active");this.buttonElement.attr("aria-pressed",
a)}else this.buttonElement=this.element},widget:function(){return this.buttonElement},destroy:function(){this.element.removeClass("ui-helper-hidden-accessible");this.buttonElement.removeClass("ui-button ui-widget ui-state-default ui-corner-all ui-state-hover ui-state-active  ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only").removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html());
this.hasTitle||this.buttonElement.removeAttr("title");a.Widget.prototype.destroy.call(this)},_setOption:function(e,b){a.Widget.prototype._setOption.apply(this,arguments);"disabled"===e?b?this.element.propAttr("disabled",!0):this.element.propAttr("disabled",!1):this._resetButton()},refresh:function(){var b=this.element.is(":disabled");b!==this.options.disabled&&this._setOption("disabled",b);"radio"===this.type?e(this.element[0]).each(function(){a(this).is(":checked")?a(this).button("widget").addClass("ui-state-active").attr("aria-pressed",
"true"):a(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"===this.type)this.options.label&&this.element.val(this.options.label);else{var e=this.buttonElement.removeClass("ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only"),
b=a("<span></span>",this.element[0].ownerDocument).addClass("ui-button-text").html(this.options.label).appendTo(e.empty()).text(),c=this.options.icons,d=c.primary&&c.secondary,f=[];c.primary||c.secondary?(this.options.text&&f.push("ui-button-text-icon"+(d?"s":c.primary?"-primary":"-secondary")),c.primary&&e.prepend("<span class='ui-button-icon-primary ui-icon "+c.primary+"'></span>"),c.secondary&&e.append("<span class='ui-button-icon-secondary ui-icon "+c.secondary+"'></span>"),this.options.text||
(f.push(d?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||e.attr("title",b))):f.push("ui-button-text-only");e.addClass(f.join(" "))}}});a.widget("ui.buttonset",{options:{items:":button, :submit, :reset, :checkbox, :radio, a, :data(button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(e,b){"disabled"===e&&this.buttons.button("option",e,b);a.Widget.prototype._setOption.apply(this,arguments)},refresh:function(){var e="rtl"===
this.element.css("direction");this.buttons=this.element.find(this.options.items).filter(":ui-button").button("refresh").end().not(":ui-button").button().end().map(function(){return a(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(e?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(e?"ui-corner-left":"ui-corner-right").end().end()},destroy:function(){this.element.removeClass("ui-buttonset");this.buttons.map(function(){return a(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy");
a.Widget.prototype.destroy.call(this)}})})(jQuery);
(function(a,p){function c(){this.debug=!1;this._curInst=null;this._keyEvent=!1;this._disabledInputs=[];this._inDialog=this._datepickerShowing=!1;this._mainDivId="ui-datepicker-div";this._inlineClass="ui-datepicker-inline";this._appendClass="ui-datepicker-append";this._triggerClass="ui-datepicker-trigger";this._dialogClass="ui-datepicker-dialog";this._disableClass="ui-datepicker-disabled";this._unselectableClass="ui-datepicker-unselectable";this._currentClass="ui-datepicker-current-day";this._dayOverClass=
"ui-datepicker-days-cell-over";this.regional=[];this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:"January February March April May June July August September October November December".split(" "),monthNamesShort:"Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),dayNames:"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),dayNamesShort:"Sun Mon Tue Wed Thu Fri Sat".split(" "),dayNamesMin:"Su Mo Tu We Th Fr Sa".split(" "),
weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""};this._defaults={calendar:Date,showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",
beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1};a.extend(this._defaults,this.regional[""]);this.dpDiv=b(a('<div id="'+this._mainDivId+'" class="ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all"></div>'));this.Date=this._defaults.calendar}function b(e){return e.bind("mouseout",function(e){e=
a(e.target).closest("button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a");e.length&&e.removeClass("ui-state-hover ui-datepicker-prev-hover ui-datepicker-next-hover")}).bind("mouseover",function(b){b=a(b.target).closest("button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a");!a.datepicker._isDisabledDatepicker(f.inline?e.parent()[0]:f.input[0])&&b.length&&(b.parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),b.addClass("ui-state-hover"),
b.hasClass("ui-datepicker-prev")&&b.addClass("ui-datepicker-prev-hover"),b.hasClass("ui-datepicker-next")&&b.addClass("ui-datepicker-next-hover"))})}function d(e,b){a.extend(e,b);for(var c in b)if(null==b[c]||b[c]==p)e[c]=b[c];return e}a.extend(a.ui,{datepicker:{version:"1.8.23"}});var g=(new Date).getTime(),f;a.extend(c.prototype,{markerClassName:"hasDatepicker",maxRows:4,log:function(){this.debug&&console.log.apply("",arguments)},_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(a){d(this._defaults,
a||{});this.Date=this._defaults.calendar;return this},_attachDatepicker:function(e,b){var c=null,d;for(d in this._defaults){var f=e.getAttribute("date:"+d);if(f){c=c||{};try{c[d]=eval(f)}catch(g){c[d]=f}}}d=e.nodeName.toLowerCase();f="div"==d||"span"==d;e.id||(this.uuid+=1,e.id="dp"+this.uuid);var n=this._newInst(a(e),f);n.settings=a.extend({},b||{},c||{});"input"==d?this._connectDatepicker(e,n):f&&this._inlineDatepicker(e,n)},_newInst:function(e,c){return{id:e[0].id.replace(/([^A-Za-z0-9_-])/g,"\\\\$1"),
input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:c,dpDiv:c?b(a('<div class="'+this._inlineClass+' ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all"></div>')):this.dpDiv}},_connectDatepicker:function(e,b){var c=a(e);b.append=a([]);b.trigger=a([]);c.hasClass(this.markerClassName)||(this._attachments(c,b),c.addClass(this.markerClassName).keydown(this._doKeyDown).keypress(this._doKeyPress).keyup(this._doKeyUp).bind("setData.datepicker",function(a,
e,c){b.settings[e]=c}).bind("getData.datepicker",function(a,e){return this._get(b,e)}),this._autoSize(b),a.data(e,"datepicker",b),b.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,b){var c=this._get(b,"appendText"),d=this._get(b,"isRTL");b.append&&b.append.remove();c&&(b.append=a('<span class="'+this._appendClass+'">'+c+"</span>"),e[d?"before":"after"](b.append));e.unbind("focus",this._showDatepicker);b.trigger&&b.trigger.remove();c=this._get(b,"showOn");"focus"!=c&&"both"!=
c||e.focus(this._showDatepicker);if("button"==c||"both"==c){var c=this._get(b,"buttonText"),f=this._get(b,"buttonImage");b.trigger=a(this._get(b,"buttonImageOnly")?a("<img/>").addClass(this._triggerClass).attr({src:f,alt:c,title:c}):a('<button type="button"></button>').addClass(this._triggerClass).html(""==f?c:a("<img/>").attr({src:f,alt:c,title:c})));e[d?"before":"after"](b.trigger);b.trigger.click(function(){a.datepicker._datepickerShowing&&a.datepicker._lastInput==e[0]?a.datepicker._hideDatepicker():
(a.datepicker._datepickerShowing&&a.datepicker._lastInput!=e[0]&&a.datepicker._hideDatepicker(),a.datepicker._showDatepicker(e[0]));return!1})}},_autoSize:function(a){if(this._get(a,"autoSize")&&!a.inline){var b=new this.Date(2009,11,20),c=this._get(a,"dateFormat");if(c.match(/[DM]/)){var d=function(a){for(var e=0,b=0,c=0;c<a.length;c++)a[c].length>e&&(e=a[c].length,b=c);return b};b.setMonth(d(this._get(a,c.match(/MM/)?"monthNames":"monthNamesShort")));b.setDate(d(this._get(a,c.match(/DD/)?"dayNames":
"dayNamesShort"))+20-b.getDay())}a.input.attr("size",this._formatDate(a,b).length)}},_inlineDatepicker:function(e,b){var c=a(e);c.hasClass(this.markerClassName)||(c.addClass(this.markerClassName).append(b.dpDiv).bind("setData.datepicker",function(a,e,c){b.settings[e]=c}).bind("getData.datepicker",function(a,e){return this._get(b,e)}),a.data(e,"datepicker",b),this._setDate(b,this._getDefaultDate(b),!0),this._updateDatepicker(b),this._updateAlternate(b),b.settings.disabled&&this._disableDatepicker(e),
b.dpDiv.css("display","block"))},_dialogDatepicker:function(e,b,c,f,g){e=this._dialogInst;e||(this.uuid+=1,this._dialogInput=a('<input type="text" id="dp'+this.uuid+'" style="position: absolute; top: -100px; width: 0px;"/>'),this._dialogInput.keydown(this._doKeyDown),a("body").append(this._dialogInput),e=this._dialogInst=this._newInst(this._dialogInput,!1),e.settings={},a.data(this._dialogInput[0],"datepicker",e));d(e.settings,f||{});b=b&&b.constructor==Date?this._formatDate(e,b):b;this._dialogInput.val(b);
this._pos=g?g.length?g:[g.pageX,g.pageY]:null;this._pos||(this._pos=[document.documentElement.clientWidth/2-100+(document.documentElement.scrollLeft||document.body.scrollLeft),document.documentElement.clientHeight/2-150+(document.documentElement.scrollTop||document.body.scrollTop)]);this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px");e.settings.onSelect=c;this._inDialog=!0;this.dpDiv.addClass(this._dialogClass);this._showDatepicker(this._dialogInput[0]);a.blockUI&&a.blockUI(this.dpDiv);
a.data(this._dialogInput[0],"datepicker",e);return this},_destroyDatepicker:function(e){var b=a(e),c=a.data(e,"datepicker");if(b.hasClass(this.markerClassName)){var d=e.nodeName.toLowerCase();a.removeData(e,"datepicker");"input"==d?(c.append.remove(),c.trigger.remove(),b.removeClass(this.markerClassName).unbind("focus",this._showDatepicker).unbind("keydown",this._doKeyDown).unbind("keypress",this._doKeyPress).unbind("keyup",this._doKeyUp)):"div"!=d&&"span"!=d||b.removeClass(this.markerClassName).empty()}},
_enableDatepicker:function(e){var b=a(e),c=a.data(e,"datepicker");if(b.hasClass(this.markerClassName)){var d=e.nodeName.toLowerCase();if("input"==d)e.disabled=!1,c.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""});else if("div"==d||"span"==d)b=b.children("."+this._inlineClass),b.children().removeClass("ui-state-disabled"),b.find("select.ui-datepicker-month, select.ui-datepicker-year").removeAttr("disabled");this._disabledInputs=a.map(this._disabledInputs,
function(a){return a==e?null:a})}},_disableDatepicker:function(e){var b=a(e),c=a.data(e,"datepicker");if(b.hasClass(this.markerClassName)){var d=e.nodeName.toLowerCase();if("input"==d)e.disabled=!0,c.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"});else if("div"==d||"span"==d)b=b.children("."+this._inlineClass),b.children().addClass("ui-state-disabled"),b.find("select.ui-datepicker-month, select.ui-datepicker-year").attr("disabled",
"disabled");this._disabledInputs=a.map(this._disabledInputs,function(a){return a==e?null:a});this._disabledInputs[this._disabledInputs.length]=e}},_isDisabledDatepicker:function(a){if(!a)return!1;for(var b=0;b<this._disabledInputs.length;b++)if(this._disabledInputs[b]==a)return!0;return!1},_getInst:function(e){try{return a.data(e,"datepicker")}catch(b){throw"Missing instance data for this datepicker";}},_optionDatepicker:function(e,b,c){var f=this._getInst(e);if(2==arguments.length&&"string"==typeof b)return"defaults"==
b?a.extend({},a.datepicker._defaults):f?"all"==b?a.extend({},f.settings):this._get(f,b):null;var g=b||{};"string"==typeof b&&(g={},g[b]=c);if(f){this._curInst==f&&this._hideDatepicker();var n=this._getDateDatepicker(e,!0),u=this._getMinMaxDate(f,"min"),q=this._getMinMaxDate(f,"max");d(f.settings,g);null!==u&&g.dateFormat!==p&&g.minDate===p&&(f.settings.minDate=this._formatDate(f,u));null!==q&&g.dateFormat!==p&&g.maxDate===p&&(f.settings.maxDate=this._formatDate(f,q));this._attachments(a(e),f);this._autoSize(f);
this._setDate(f,n);this._updateAlternate(f);this._updateDatepicker(f)}},_changeDatepicker:function(a,b,c){this._optionDatepicker(a,b,c)},_refreshDatepicker:function(a){(a=this._getInst(a))&&this._updateDatepicker(a)},_setDateDatepicker:function(a,b){var c=this._getInst(a);c&&(this._setDate(c,b),this._updateDatepicker(c),this._updateAlternate(c))},_getDateDatepicker:function(a,b){var c=this._getInst(a);c&&!c.inline&&this._setDateFromField(c,b);return c?this._getDate(c):null},_doKeyDown:function(e){var b=
a.datepicker._getInst(e.target),c=!0,d=b.dpDiv.is(".ui-datepicker-rtl");b._keyEvent=!0;if(a.datepicker._datepickerShowing)switch(e.keyCode){case 9:a.datepicker._hideDatepicker();c=!1;break;case 13:return c=a("td."+a.datepicker._dayOverClass+":not(."+a.datepicker._currentClass+")",b.dpDiv),c[0]&&a.datepicker._selectDay(e.target,b.selectedMonth,b.selectedYear,c[0]),(e=a.datepicker._get(b,"onSelect"))?(c=a.datepicker._formatDate(b),e.apply(b.input?b.input[0]:null,[c,b])):a.datepicker._hideDatepicker(),
!1;case 27:a.datepicker._hideDatepicker();break;case 33:a.datepicker._adjustDate(e.target,e.ctrlKey?-a.datepicker._get(b,"stepBigMonths"):-a.datepicker._get(b,"stepMonths"),"M");break;case 34:a.datepicker._adjustDate(e.target,e.ctrlKey?+a.datepicker._get(b,"stepBigMonths"):+a.datepicker._get(b,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&a.datepicker._clearDate(e.target);c=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&a.datepicker._gotoToday(e.target);c=e.ctrlKey||e.metaKey;
break;case 37:(e.ctrlKey||e.metaKey)&&a.datepicker._adjustDate(e.target,d?1:-1,"D");c=e.ctrlKey||e.metaKey;e.originalEvent.altKey&&a.datepicker._adjustDate(e.target,e.ctrlKey?-a.datepicker._get(b,"stepBigMonths"):-a.datepicker._get(b,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&a.datepicker._adjustDate(e.target,-7,"D");c=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&a.datepicker._adjustDate(e.target,d?-1:1,"D");c=e.ctrlKey||e.metaKey;e.originalEvent.altKey&&a.datepicker._adjustDate(e.target,
e.ctrlKey?+a.datepicker._get(b,"stepBigMonths"):+a.datepicker._get(b,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&a.datepicker._adjustDate(e.target,7,"D");c=e.ctrlKey||e.metaKey;break;default:c=!1}else 36==e.keyCode&&e.ctrlKey?a.datepicker._showDatepicker(this):c=!1;c&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var b=a.datepicker._getInst(e.target);if(a.datepicker._get(b,"constrainInput")){var b=a.datepicker._possibleChars(a.datepicker._get(b,"dateFormat")),c=String.fromCharCode(e.charCode==
p?e.keyCode:e.charCode);return e.ctrlKey||e.metaKey||" ">c||!b||-1<b.indexOf(c)}},_doKeyUp:function(e){e=a.datepicker._getInst(e.target);if(e.input.val()!=e.lastVal)try{a.datepicker.parseDate(a.datepicker._get(e,"dateFormat"),e.input?e.input.val():null,a.datepicker._getFormatConfig(e))&&(a.datepicker._setDateFromField(e),a.datepicker._updateAlternate(e),a.datepicker._updateDatepicker(e))}catch(b){a.datepicker.log(b)}return!0},_showDatepicker:function(e){e=e.target||e;"input"!=e.nodeName.toLowerCase()&&
(e=a("input",e.parentNode)[0]);if(!a.datepicker._isDisabledDatepicker(e)&&a.datepicker._lastInput!=e){var b=a.datepicker._getInst(e);a.datepicker._curInst&&a.datepicker._curInst!=b&&(a.datepicker._curInst.dpDiv.stop(!0,!0),b&&a.datepicker._datepickerShowing&&a.datepicker._hideDatepicker(a.datepicker._curInst.input[0]));var c=a.datepicker._get(b,"beforeShow"),c=c?c.apply(e,[e,b]):{};if(!1!==c){d(b.settings,c);b.lastVal=null;a.datepicker._lastInput=e;a.datepicker._setDateFromField(b);a.datepicker._inDialog&&
(e.value="");a.datepicker._pos||(a.datepicker._pos=a.datepicker._findPos(e),a.datepicker._pos[1]+=e.offsetHeight);var f=!1;a(e).parents().each(function(){f|="fixed"==a(this).css("position");return!f});f&&a.browser.opera&&(a.datepicker._pos[0]-=document.documentElement.scrollLeft,a.datepicker._pos[1]-=document.documentElement.scrollTop);c={left:a.datepicker._pos[0],top:a.datepicker._pos[1]};a.datepicker._pos=null;b.dpDiv.empty();b.dpDiv.css({position:"absolute",display:"block",top:"-1000px"});a.datepicker._updateDatepicker(b);
c=a.datepicker._checkOffset(b,c,f);b.dpDiv.css({position:a.datepicker._inDialog&&a.blockUI?"static":f?"fixed":"absolute",display:"none",left:c.left+"px",top:c.top+"px"});if(!b.inline){var c=a.datepicker._get(b,"showAnim"),g=a.datepicker._get(b,"duration"),n=function(){var e=b.dpDiv.find("iframe.ui-datepicker-cover");if(e.length){var c=a.datepicker._getBorders(b.dpDiv);e.css({left:-c[0],top:-c[1],width:b.dpDiv.outerWidth(),height:b.dpDiv.outerHeight()})}};b.useMaxZ?b.dpDiv.zIndex(a.ui.dialog.maxZ+
1):b.dpDiv.zIndex(a(e).zIndex()+1);a.datepicker._datepickerShowing=!0;if(a.effects&&a.effects[c])b.dpDiv.show(c,a.datepicker._get(b,"showOptions"),g,n);else b.dpDiv[c||"show"](c?g:null,n);c&&g||n();b.input.is(":visible")&&!b.input.is(":disabled")&&b.input.focus();a.datepicker._curInst=b}}}},_updateDatepicker:function(b){this.maxRows=4;var c=a.datepicker._getBorders(b.dpDiv);f=b;b.dpDiv.empty().append(this._generateHTML(b));this._attachHandlers(b);var d=b.dpDiv.find("iframe.ui-datepicker-cover");d.length&&
d.css({left:-c[0],top:-c[1],width:b.dpDiv.outerWidth(),height:b.dpDiv.outerHeight()});b.dpDiv.find("."+this._dayOverClass+" a").mouseover();c=this._getNumberOfMonths(b);d=c[1];b.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width("");1<d&&b.dpDiv.addClass("ui-datepicker-multi-"+d).css("width",17*d+"em");b.dpDiv[(1!=c[0]||1!=c[1]?"add":"remove")+"Class"]("ui-datepicker-multi");b.dpDiv[(this._get(b,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl");b==a.datepicker._curInst&&
a.datepicker._datepickerShowing&&b.input&&b.input.is(":visible")&&!b.input.is(":disabled")&&b.input[0]!=document.activeElement&&b.input.focus();if(b.yearshtml){var g=b.yearshtml;setTimeout(function(){g===b.yearshtml&&b.yearshtml&&b.dpDiv.find("select.ui-datepicker-year:first").replaceWith(b.yearshtml);g=b.yearshtml=null},0)}},_getBorders:function(a){var b=function(a){return{thin:1,medium:2,thick:3}[a]||a};return[parseFloat(b(a.css("border-left-width"))),parseFloat(b(a.css("border-top-width")))]},
_checkOffset:function(b,c,d){var f=b.dpDiv.outerWidth(),g=b.dpDiv.outerHeight(),n=b.input?b.input.outerWidth():0,u=b.input?b.input.outerHeight():0,q=document.documentElement.clientWidth+(d?0:a(document).scrollLeft()),v=document.documentElement.clientHeight+(d?0:a(document).scrollTop());c.left-=this._get(b,"isRTL")?f-n:0;c.left-=d&&c.left==b.input.offset().left?a(document).scrollLeft():0;c.top-=d&&c.top==b.input.offset().top+u?a(document).scrollTop():0;c.left-=Math.min(c.left,c.left+f>q&&q>f?Math.abs(c.left+
f-q):0);c.top-=Math.min(c.top,c.top+g>v&&v>g?Math.abs(g+u):0);return c},_findPos:function(b){for(var c=this._getInst(b),c=this._get(c,"isRTL");b&&("hidden"==b.type||1!=b.nodeType||a.expr.filters.hidden(b));)b=b[c?"previousSibling":"nextSibling"];b=a(b).offset();return[b.left,b.top]},_hideDatepicker:function(b){var c=this._curInst;if(c&&(!b||c==a.data(b,"datepicker"))&&this._datepickerShowing){b=this._get(c,"showAnim");var d=this._get(c,"duration"),f=function(){a.datepicker._tidyDialog(c)};if(a.effects&&
a.effects[b])c.dpDiv.hide(b,a.datepicker._get(c,"showOptions"),d,f);else c.dpDiv["slideDown"==b?"slideUp":"fadeIn"==b?"fadeOut":"hide"](b?d:null,f);b||f();this._datepickerShowing=!1;(b=this._get(c,"onClose"))&&b.apply(c.input?c.input[0]:null,[c.input?c.input.val():"",c]);this._lastInput=null;this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),a.blockUI&&(a.unblockUI(),a("body").append(this.dpDiv)));this._inDialog=!1}},_tidyDialog:function(a){a.dpDiv.removeClass(this._dialogClass).unbind(".ui-datepicker-calendar")},
_checkExternalClick:function(b){if(a.datepicker._curInst){b=a(b.target);var c=a.datepicker._getInst(b[0]);(!(b[0].id==a.datepicker._mainDivId||0!=b.parents("#"+a.datepicker._mainDivId).length||b.hasClass(a.datepicker.markerClassName)||b.closest("."+a.datepicker._triggerClass).length||!a.datepicker._datepickerShowing||a.datepicker._inDialog&&a.blockUI)||b.hasClass(a.datepicker.markerClassName)&&a.datepicker._curInst!=c)&&a.datepicker._hideDatepicker()}},_adjustDate:function(b,c,d){b=a(b);var f=this._getInst(b[0]);
this._isDisabledDatepicker(b[0])||(this._adjustInstDate(f,c+("M"==d?this._get(f,"showCurrentAtPos"):0),d),this._updateDatepicker(f))},_gotoToday:function(b){b=a(b);var c=this._getInst(b[0]);if(this._get(c,"gotoCurrent")&&c.currentDay)c.selectedDay=c.currentDay,c.drawMonth=c.selectedMonth=c.currentMonth,c.drawYear=c.selectedYear=c.currentYear;else{var d=new this.Date;c.selectedDay=d.getDate();c.drawMonth=c.selectedMonth=d.getMonth();c.drawYear=c.selectedYear=d.getFullYear()}this._notifyChange(c);this._adjustDate(b)},
_selectMonthYear:function(b,c,d){b=a(b);var f=this._getInst(b[0]);f["selected"+("M"==d?"Month":"Year")]=f["draw"+("M"==d?"Month":"Year")]=parseInt(c.options[c.selectedIndex].value,10);this._notifyChange(f);this._adjustDate(b)},_selectDay:function(b,c,d,f){var g=a(b);a(f).hasClass(this._unselectableClass)||this._isDisabledDatepicker(g[0])||(g=this._getInst(g[0]),g.selectedDay=g.currentDay=a("a",f).html(),g.selectedMonth=g.currentMonth=c,g.selectedYear=g.currentYear=d,this._selectDate(b,this._formatDate(g,
g.currentDay,g.currentMonth,g.currentYear)))},_clearDate:function(b){b=a(b);this._getInst(b[0]);this._selectDate(b,"")},_selectDate:function(b,c){var d=a(b),d=this._getInst(d[0]);c=null!=c?c:this._formatDate(d);d.input&&d.input.val(c);this._updateAlternate(d);var f=this._get(d,"onSelect");f?f.apply(d.input?d.input[0]:null,[c,d]):d.input&&d.input.trigger("change");d.inline?this._updateDatepicker(d):(this._hideDatepicker(),this._lastInput=d.input[0],"object"!=typeof d.input[0]&&d.input.focus(),this._lastInput=
null)},_updateAlternate:function(b){var c=this._get(b,"altField");if(c){var d=this._get(b,"altFormat")||this._get(b,"dateFormat"),f=this._getDate(b),g=this.formatDate(d,f,this._getFormatConfig(b));a(c).each(function(){a(this).val(g)})}},noWeekends:function(a){a=a.getDay();return[0<a&&6>a,""]},iso8601Week:function(a){a=new this.Date(a.getTime());a.setDate(a.getDate()+4-(a.getDay()||7));var b=a.getTime();a.setMonth(0);a.setDate(1);return Math.floor(Math.round((b-a)/864E5)/7)+1},parseDate:function(b,
c,d){if(null==b||null==c)throw"Invalid arguments";c="object"==typeof c?c.toString():c+"";if(""==c)return null;for(var f=(d?d.shortYearCutoff:null)||this._defaults.shortYearCutoff,f="string"!=typeof f?f:(new this.Date).getFullYear()%100+parseInt(f,10),g=(d?d.dayNamesShort:null)||this._defaults.dayNamesShort,n=(d?d.dayNames:null)||this._defaults.dayNames,u=(d?d.monthNamesShort:null)||this._defaults.monthNamesShort,q=(d?d.monthNames:null)||this._defaults.monthNames,v=d=-1,r=-1,p=-1,w=!1,y=function(a){(a=
I+1<b.length&&b.charAt(I+1)==a)&&I++;return a},A=function(a){var b=y(a);a=new RegExp("^\\d{1,"+("@"==a?14:"!"==a?20:"y"==a&&b?4:"o"==a?3:2)+"}");a=c.substring(D).match(a);if(!a)throw"Missing number at position "+D;D+=a[0].length;return parseInt(a[0],10)},z=function(b,e,d){b=a.map(y(b)?d:e,function(a,b){return[[b,a]]}).sort(function(a,b){return-(a[1].length-b[1].length)});var f=-1;a.each(b,function(a,b){var e=b[1];if(c.substr(D,e.length).toLowerCase()==e.toLowerCase())return f=b[0],D+=e.length,!1});
if(-1!=f)return f+1;throw"Unknown name at position "+D;},x=function(){if(c.charAt(D)!=b.charAt(I))throw"Unexpected literal at position "+D;D++},D=0,I=0;I<b.length;I++)if(w)"'"!=b.charAt(I)||y("'")?x():w=!1;else switch(b.charAt(I)){case "d":r=A("d");break;case "D":z("D",g,n);break;case "o":p=A("o");break;case "m":v=A("m");break;case "M":v=z("M",u,q);break;case "y":d=A("y");break;case "@":var F=new this.Date(A("@"));d=F.getFullYear();v=F.getMonth()+1;r=F.getDate();break;case "!":F=new this.Date((A("!")-
this._ticksTo1970)/1E4);d=F.getFullYear();v=F.getMonth()+1;r=F.getDate();break;case "'":y("'")?x():w=!0;break;default:x()}if(D<c.length)throw"Extra/unparsed characters found in date: "+c.substring(D);-1==d?d=(new this.Date).getFullYear():100>d&&(d+=(new this.Date).getFullYear()-(new this.Date).getFullYear()%100+(d<=f?0:-100));if(-1<p){v=1;r=p;do{f=this._getDaysInMonth(d,v-1);if(r<=f)break;v++;r-=f}while(1)}F=this._daylightSavingAdjust(new this.Date(d,v-1,r));if(F.getFullYear()!=d||F.getMonth()+1!=
v||F.getDate()!=r)throw"Invalid date";return F},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:864E9*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925)),formatDate:function(a,b,c){if(!b)return"";var d=(c?c.dayNamesShort:null)||this._defaults.dayNamesShort,f=(c?c.dayNames:null)||this._defaults.dayNames,g=(c?c.monthNamesShort:
null)||this._defaults.monthNamesShort;c=(c?c.monthNames:null)||this._defaults.monthNames;var u=function(b){(b=w+1<a.length&&a.charAt(w+1)==b)&&w++;return b},q=function(a,b,c){b=""+b;if(u(a))for(;b.length<c;)b="0"+b;return b},v=function(a,b,c,e){return u(a)?e[b]:c[b]},r="",p=!1;if(b)for(var w=0;w<a.length;w++)if(p)"'"!=a.charAt(w)||u("'")?r+=a.charAt(w):p=!1;else switch(a.charAt(w)){case "d":r+=q("d",b.getDate(),2);break;case "D":r+=v("D",b.getDay(),d,f);break;case "o":r+=q("o",Math.round(((new this.Date(b.getFullYear(),
b.getMonth(),b.getDate())).getTime()-(new this.Date(b.getFullYear(),0,0)).getTime())/864E5),3);break;case "m":r+=q("m",b.getMonth()+1,2);break;case "M":r+=v("M",b.getMonth(),g,c);break;case "y":r+=u("y")?b.getFullYear():(10>b.getYear()%100?"0":"")+b.getYear()%100;break;case "@":r+=b.getTime();break;case "!":r+=1E4*b.getTime()+this._ticksTo1970;break;case "'":u("'")?r+="'":p=!0;break;default:r+=a.charAt(w)}return r},_possibleChars:function(a){for(var b="",c=!1,d=function(b){(b=f+1<a.length&&a.charAt(f+
1)==b)&&f++;return b},f=0;f<a.length;f++)if(c)"'"!=a.charAt(f)||d("'")?b+=a.charAt(f):c=!1;else switch(a.charAt(f)){case "d":case "m":case "y":case "@":b+="0123456789";break;case "D":case "M":return null;case "'":d("'")?b+="'":c=!0;break;default:b+=a.charAt(f)}return b},_get:function(a,b){return a.settings[b]!==p?a.settings[b]:this._defaults[b]},_setDateFromField:function(a,b){if(a.input.val()!=a.lastVal){var c=this._get(a,"dateFormat"),d=a.lastVal=a.input?a.input.val():null,f,g;f=g=this._getDefaultDate(a);
var u=this._getFormatConfig(a);try{f=this.parseDate(c,d,u)||g}catch(q){this.log(q),d=b?"":d}a.selectedDay=f.getDate();a.drawMonth=a.selectedMonth=f.getMonth();a.drawYear=a.selectedYear=f.getFullYear();a.currentDay=d?f.getDate():0;a.currentMonth=d?f.getMonth():0;a.currentYear=d?f.getFullYear():0;this._adjustInstDate(a)}},_getDefaultDate:function(a){return this._restrictMinMax(a,this._determineDate(a,this._get(a,"defaultDate"),new this.Date))},_determineDate:function(b,c,d){var f=function(a){var b=
new this.Date;b.setDate(b.getDate()+a);return b},g=function(c){try{return a.datepicker.parseDate(a.datepicker._get(b,"dateFormat"),c,a.datepicker._getFormatConfig(b))}catch(d){}for(var f=(c.toLowerCase().match(/^c/)?a.datepicker._getDate(b):null)||new this.Date,g=f.getFullYear(),h=f.getMonth(),f=f.getDate(),k=/([+-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,l=k.exec(c);l;){switch(l[2]||"d"){case "d":case "D":f+=parseInt(l[1],10);break;case "w":case "W":f+=7*parseInt(l[1],10);break;case "m":case "M":h+=parseInt(l[1],
10);f=Math.min(f,a.datepicker._getDaysInMonth(g,h));break;case "y":case "Y":g+=parseInt(l[1],10),f=Math.min(f,a.datepicker._getDaysInMonth(g,h))}l=k.exec(c)}return new this.Date(g,h,f)};if(c=(c=null==c||""===c?d:"string"==typeof c?g(c):"number"==typeof c?isNaN(c)?d:f(c):new this.Date(c.getTime()))&&"Invalid Date"==c.toString()?d:c)c.setHours(0),c.setMinutes(0),c.setSeconds(0),c.setMilliseconds(0);return this._daylightSavingAdjust(c)},_daylightSavingAdjust:function(a){if(!a)return null;a.setHours(12<
a.getHours()?a.getHours()+2:0);return a},_setDate:function(a,b,c){var d=!b,f=a.selectedMonth,g=a.selectedYear;b=this._restrictMinMax(a,this._determineDate(a,b,new this.Date));a.selectedDay=a.currentDay=b.getDate();a.drawMonth=a.selectedMonth=a.currentMonth=b.getMonth();a.drawYear=a.selectedYear=a.currentYear=b.getFullYear();f==a.selectedMonth&&g==a.selectedYear||c||this._notifyChange(a);this._adjustInstDate(a);a.input&&a.input.val(d?"":this._formatDate(a))},_getDate:function(a){return!a.currentYear||
a.input&&""==a.input.val()?null:this._daylightSavingAdjust(new this.Date(a.currentYear,a.currentMonth,a.currentDay))},_attachHandlers:function(b){var c=this._get(b,"stepMonths"),d="#"+b.id.replace(/\\\\/g,"\\");b.dpDiv.find("[data-handler]").map(function(){a(this).bind(this.getAttribute("data-event"),{prev:function(){window["DP_jQuery_"+g].datepicker._adjustDate(d,-c,"M")},next:function(){window["DP_jQuery_"+g].datepicker._adjustDate(d,+c,"M")},hide:function(){window["DP_jQuery_"+g].datepicker._hideDatepicker()},
today:function(){window["DP_jQuery_"+g].datepicker._gotoToday(d)},selectDay:function(){window["DP_jQuery_"+g].datepicker._selectDay(d,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this);return!1},selectMonth:function(){window["DP_jQuery_"+g].datepicker._selectMonthYear(d,this,"M");return!1},selectYear:function(){window["DP_jQuery_"+g].datepicker._selectMonthYear(d,this,"Y");return!1}}[this.getAttribute("data-handler")])})},_generateHTML:function(b){var c=new this.Date,c=this._daylightSavingAdjust(new this.Date(c.getFullYear(),
c.getMonth(),c.getDate())),d=this._get(b,"isRTL"),f=this._get(b,"showButtonPanel"),g=this._get(b,"hideIfNoPrevNext"),n=this._get(b,"navigationAsDateFormat"),p=this._getNumberOfMonths(b),q=this._get(b,"showCurrentAtPos"),v=this._get(b,"stepMonths"),r=1!=p[0]||1!=p[1],G=this._daylightSavingAdjust(b.currentDay?new this.Date(b.currentYear,b.currentMonth,b.currentDay):new this.Date(9999,9,9)),w=this._getMinMaxDate(b,"min"),y=this._getMinMaxDate(b,"max"),q=b.drawMonth-q,A=b.drawYear;0>q&&(q+=12,A--);if(y)for(var z=
this._daylightSavingAdjust(new this.Date(y.getFullYear(),y.getMonth()-p[0]*p[1]+1,y.getDate())),z=w&&z<w?w:z;this._daylightSavingAdjust(new this.Date(A,q,1))>z;)q--,0>q&&(q=11,A--);b.drawMonth=q;b.drawYear=A;var z=this._get(b,"prevText"),z=n?this.formatDate(z,this._daylightSavingAdjust(new this.Date(A,q-v,1)),this._getFormatConfig(b)):z,z=this._canAdjustMonth(b,-1,A,q)?'<a class="ui-datepicker-prev ui-corner-all" data-handler="prev" data-event="click" title="'+z+'"><span class="ui-icon ui-icon-circle-triangle-'+
(d?"e":"w")+'">'+z+"</span></a>":g?"":'<a class="ui-datepicker-prev ui-corner-all ui-state-disabled" title="'+z+'"><span class="ui-icon ui-icon-circle-triangle-'+(d?"e":"w")+'">'+z+"</span></a>",x=this._get(b,"nextText"),x=n?this.formatDate(x,this._daylightSavingAdjust(new this.Date(A,q+v,1)),this._getFormatConfig(b)):x,g=this._canAdjustMonth(b,1,A,q)?'<a class="ui-datepicker-next ui-corner-all" data-handler="next" data-event="click" title="'+x+'"><span class="ui-icon ui-icon-circle-triangle-'+(d?
"w":"e")+'">'+x+"</span></a>":g?"":'<a class="ui-datepicker-next ui-corner-all ui-state-disabled" title="'+x+'"><span class="ui-icon ui-icon-circle-triangle-'+(d?"w":"e")+'">'+x+"</span></a>",v=this._get(b,"currentText"),x=this._get(b,"gotoCurrent")&&b.currentDay?G:c,v=n?this.formatDate(v,x,this._getFormatConfig(b)):v,n=b.inline?"":'<button type="button" class="ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all" data-handler="hide" data-event="click">'+this._get(b,"closeText")+
"</button>",f=f?'<div class="ui-datepicker-buttonpane ui-widget-content">'+(d?n:"")+(this._isInRange(b,x)?'<button type="button" class="ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all" data-handler="today" data-event="click">'+v+"</button>":"")+(d?"":n)+"</div>":"",n=parseInt(this._get(b,"firstDay"),10),n=isNaN(n)?0:n,v=this._get(b,"showWeek"),x=this._get(b,"dayNames");this._get(b,"dayNamesShort");var D=this._get(b,"dayNamesMin"),I=this._get(b,"monthNames"),F=this._get(b,
"monthNamesShort"),S=this._get(b,"beforeShowDay"),O=this._get(b,"showOtherMonths"),W=this._get(b,"selectOtherMonths");this._get(b,"calculateWeek");for(var T=this._getDefaultDate(b),K="",L=0;L<p[0];L++){var P="";this.maxRows=4;for(var M=0;M<p[1];M++){var U=this._daylightSavingAdjust(new this.Date(A,q,b.selectedDay)),E=" ui-corner-all",C="";if(r){C+='<div class="ui-datepicker-group';if(1<p[1])switch(M){case 0:C+=" ui-datepicker-group-first";E=" ui-corner-"+(d?"right":"left");break;case p[1]-1:C+=" ui-datepicker-group-last";
E=" ui-corner-"+(d?"left":"right");break;default:C+=" ui-datepicker-group-middle",E=""}C+='">'}for(var C=C+('<div class="ui-datepicker-header ui-widget-header ui-helper-clearfix'+E+'">'+(/all|left/.test(E)&&0==L?d?g:z:"")+(/all|right/.test(E)&&0==L?d?z:g:"")+this._generateMonthYearHeader(b,q,A,w,y,0<L||0<M,I,F)+'</div><table class="ui-datepicker-calendar"><thead><tr>'),H=v?'<th class="ui-datepicker-week-col">'+this._get(b,"weekHeader")+"</th>":"",E=0;7>E;E++)var B=(E+n)%7,H=H+("<th"+(5<=(E+n+6)%7?
' class="ui-datepicker-week-end"':"")+'><span title="'+x[B]+'">'+D[B]+"</span></th>");C+=H+"</tr></thead><tbody>";H=this._getDaysInMonth(A,q);A==b.selectedYear&&q==b.selectedMonth&&(b.selectedDay=Math.min(b.selectedDay,H));E=(this._getFirstDayOfMonth(A,q)-n+7)%7;H=Math.ceil((E+H)/7);this.maxRows=H=r?this.maxRows>H?this.maxRows:H:H;for(var B=this._daylightSavingAdjust(new this.Date(A,q,1-E)),V=0;V<H;V++){for(var C=C+"<tr>",Q=v?'<td class="ui-datepicker-week-col">'+this._get(b,"calculateWeek")(B)+"</td>":
"",E=0;7>E;E++){var N=S?S.apply(b.input?b.input[0]:null,[B]):[!0,""],J=B.getMonth()!=q,R=J&&!W||!N[0]||w&&B<w||y&&B>y,Q=Q+('<td class="'+(5<=(E+n+6)%7?" ui-datepicker-week-end":"")+(J?" ui-datepicker-other-month":"")+(B.getTime()==U.getTime()&&q==b.selectedMonth&&b._keyEvent||T.getTime()==B.getTime()&&T.getTime()==U.getTime()?" "+this._dayOverClass:"")+(R?" "+this._unselectableClass+" ui-state-disabled":"")+(J&&!O?"":" "+N[1]+(B.getTime()==G.getTime()?" "+this._currentClass:"")+(B.getTime()==c.getTime()?
" ui-datepicker-today":""))+'"'+(J&&!O||!N[2]?"":' title="'+N[2]+'"')+(R?"":' data-handler="selectDay" data-event="click" data-month="'+B.getMonth()+'" data-year="'+B.getFullYear()+'"')+">"+(J&&!O?"&#xa0;":R?'<span class="ui-state-default">'+B.getDate()+"</span>":'<a class="ui-state-default'+(B.getTime()==c.getTime()?" ui-state-highlight":"")+(B.getTime()==G.getTime()?" ui-state-active":"")+(J?" ui-priority-secondary":"")+'" href="#">'+B.getDate()+"</a>")+"</td>");B.setDate(B.getDate()+1);B=this._daylightSavingAdjust(B)}C+=
Q+"</tr>"}q++;11<q&&(q=0,A++);C+="</tbody></table>"+(r?"</div>"+(0<p[0]&&M==p[1]-1?'<div class="ui-datepicker-row-break"></div>':""):"");P+=C}K+=P}K+=f+(a.browser.msie&&7>parseInt(a.browser.version,10)&&!b.inline?'<iframe src="javascript:false;" class="ui-datepicker-cover" frameborder="0"></iframe>':"");b._keyEvent=!1;return K},_generateMonthYearHeader:function(a,b,c,d,f,g,p,q){var v=this._get(a,"changeMonth"),r=this._get(a,"changeYear"),G=this._get(a,"showMonthAfterYear"),w='<div class="ui-datepicker-title">',
y="";if(g||!v)y+='<span class="ui-datepicker-month">'+p[b]+"</span>";else{p=d&&d.getFullYear()==c;for(var A=f&&f.getFullYear()==c,y=y+'<select class="ui-datepicker-month" data-handler="selectMonth" data-event="change">',z=0;12>z;z++)(!p||z>=d.getMonth())&&(!A||z<=f.getMonth())&&(y+='<option value="'+z+'"'+(z==b?' selected="selected"':"")+">"+q[z]+"</option>");y+="</select>"}G||(w+=y+(!g&&v&&r?"":"&#xa0;"));if(!a.yearshtml)if(a.yearshtml="",g||!r)w+='<span class="ui-datepicker-year">'+c+"</span>";
else{q=this._get(a,"yearRange").split(":");var x=(new this.Date).getFullYear();p=function(a){a=a.match(/c[+-].*/)?c+parseInt(a.substring(1),10):a.match(/[+-].*/)?x+parseInt(a,10):parseInt(a,10);return isNaN(a)?x:a};b=p(q[0]);q=Math.max(b,p(q[1]||""));b=d?Math.max(b,d.getFullYear()):b;q=f?Math.min(q,f.getFullYear()):q;for(a.yearshtml+='<select class="ui-datepicker-year" data-handler="selectYear" data-event="change">';b<=q;b++)a.yearshtml+='<option value="'+b+'"'+(b==c?' selected="selected"':"")+">"+
b+"</option>";a.yearshtml+="</select>";w+=a.yearshtml;a.yearshtml=null}w+=this._get(a,"yearSuffix");G&&(w+=(!g&&v&&r?"":"&#xa0;")+y);return w+"</div>"},_adjustInstDate:function(a,b,c){var d=a.drawYear+("Y"==c?b:0),f=a.drawMonth+("M"==c?b:0);b=Math.min(a.selectedDay,this._getDaysInMonth(d,f))+("D"==c?b:0);d=this._restrictMinMax(a,this._daylightSavingAdjust(new this.Date(d,f,b)));a.selectedDay=d.getDate();a.drawMonth=a.selectedMonth=d.getMonth();a.drawYear=a.selectedYear=d.getFullYear();"M"!=c&&"Y"!=
c||this._notifyChange(a)},_restrictMinMax:function(a,b){var c=this._getMinMaxDate(a,"min"),d=this._getMinMaxDate(a,"max"),c=c&&b<c?c:b;return d&&c>d?d:c},_notifyChange:function(a){var b=this._get(a,"onChangeMonthYear");b&&b.apply(a.input?a.input[0]:null,[a.selectedYear,a.selectedMonth+1,a])},_getNumberOfMonths:function(a){a=this._get(a,"numberOfMonths");return null==a?[1,1]:"number"==typeof a?[1,a]:a},_getMinMaxDate:function(a,b){return this._determineDate(a,this._get(a,b+"Date"),null)},_getDaysInMonth:function(a,
b){return 32-this._daylightSavingAdjust(new this.Date(a,b,32)).getDate()},_getFirstDayOfMonth:function(a,b){return(new this.Date(a,b,1)).getDay()},_canAdjustMonth:function(a,b,c,d){var f=this._getNumberOfMonths(a);c=this._daylightSavingAdjust(new this.Date(c,d+(0>b?b:f[0]*f[1]),1));0>b&&c.setDate(this._getDaysInMonth(c.getFullYear(),c.getMonth()));return this._isInRange(a,c)},_isInRange:function(a,b){var c=this._getMinMaxDate(a,"min"),d=this._getMinMaxDate(a,"max");return(!c||b.getTime()>=c.getTime())&&
(!d||b.getTime()<=d.getTime())},_getFormatConfig:function(a){var b=this._get(a,"shortYearCutoff"),b="string"!=typeof b?b:(new this.Date).getFullYear()%100+parseInt(b,10);return{shortYearCutoff:b,dayNamesShort:this._get(a,"dayNamesShort"),dayNames:this._get(a,"dayNames"),monthNamesShort:this._get(a,"monthNamesShort"),monthNames:this._get(a,"monthNames")}},_formatDate:function(a,b,c,d){b||(a.currentDay=a.selectedDay,a.currentMonth=a.selectedMonth,a.currentYear=a.selectedYear);b=b?"object"==typeof b?
b:this._daylightSavingAdjust(new this.Date(d,c,b)):this._daylightSavingAdjust(new this.Date(a.currentYear,a.currentMonth,a.currentDay));return this.formatDate(this._get(a,"dateFormat"),b,this._getFormatConfig(a))}});a.fn.datepicker=function(b){if(!this.length)return this;a.datepicker.initialized||(a(document).mousedown(a.datepicker._checkExternalClick).find("body").append(a.datepicker.dpDiv),a.datepicker.initialized=!0);var c=Array.prototype.slice.call(arguments,1);return"string"==typeof b&&("isDisabled"==
b||"getDate"==b||"widget"==b)||"option"==b&&2==arguments.length&&"string"==typeof arguments[1]?a.datepicker["_"+b+"Datepicker"].apply(a.datepicker,[this[0]].concat(c)):this.each(function(){"string"==typeof b?a.datepicker["_"+b+"Datepicker"].apply(a.datepicker,[this].concat(c)):a.datepicker._attachDatepicker(this,b)})};a.datepicker=new c;a.datepicker.initialized=!1;a.datepicker.uuid=(new Date).getTime();a.datepicker.version="1.8.23";window["DP_jQuery_"+g]=a})(jQuery);
(function(a,p){var c={buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},b={maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0};a.widget("ui.dialog",{options:{autoOpen:!0,buttons:{},closeOnEscape:!0,closeText:"close",dialogClass:"",draggable:!0,hide:null,height:"auto",maxHeight:!1,maxWidth:!1,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",collision:"fit",using:function(b){var c=a(this).css(b).offset().top;0>c&&a(this).css("top",b.top-c)}},resizable:!0,
show:null,stack:!0,title:"",width:300,zIndex:1E3,useMaxZ:!0},_create:function(){this.originalTitle=this.element.attr("title");"string"!==typeof this.originalTitle&&(this.originalTitle="");this.options.title=this.options.title||this.originalTitle;var b=this,c=b.options,f=c.title||"&#160;",e=a.ui.dialog.getTitleId(b.element),h=(b.uiDialog=a("<div></div>")).appendTo(document.body).hide().addClass("ui-dialog ui-widget ui-widget-content ui-corner-all "+c.dialogClass).css({zIndex:c.zIndex}).attr("tabIndex",
-1).css("outline",0).keydown(function(e){c.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===a.ui.keyCode.ESCAPE&&(b.close(e),e.preventDefault())}).attr({role:"dialog","aria-labelledby":e}).mousedown(function(a){b.moveToTop(!1,a)});b.element.show().removeAttr("title").addClass("ui-dialog-content ui-widget-content").appendTo(h);var k=(b.uiDialogTitlebar=a("<div></div>")).addClass("ui-dialog-titlebar ui-widget-header ui-corner-all ui-helper-clearfix").prependTo(h),l=a('<a href="#"></a>').addClass("ui-dialog-titlebar-close ui-corner-all").attr("role",
"button").hover(function(){l.addClass("ui-state-hover")},function(){l.removeClass("ui-state-hover")}).focus(function(){l.addClass("ui-state-focus")}).blur(function(){l.removeClass("ui-state-focus")}).click(function(a){b.close(a);return!1}).appendTo(k);(b.uiDialogTitlebarCloseText=a("<span></span>")).addClass("ui-icon ui-icon-closethick").text(c.closeText).appendTo(l);a("<span></span>").addClass("ui-dialog-title").attr("id",e).html(f).prependTo(k);a.isFunction(c.beforeclose)&&!a.isFunction(c.beforeClose)&&
(c.beforeClose=c.beforeclose);k.find("*").add(k).disableSelection();c.draggable&&a.fn.draggable&&b._makeDraggable();c.resizable&&a.fn.resizable&&b._makeResizable();b._createButtons(c.buttons);b._isOpen=!1;a.fn.bgiframe&&h.bgiframe()},_init:function(){this.options.autoOpen&&this.open()},destroy:function(){this.overlay&&this.overlay.destroy();this.uiDialog.hide();this.element.unbind(".dialog").removeData("dialog").removeClass("ui-dialog-content ui-widget-content").hide().appendTo("body");this.uiDialog.remove();
this.originalTitle&&this.element.attr("title",this.originalTitle);return this},widget:function(){return this.uiDialog},close:function(b){var c=this,f,e;if(!1!==c._trigger("beforeClose",b))return c.overlay&&c.overlay.destroy(),c.uiDialog.unbind("keypress.ui-dialog"),c._isOpen=!1,c.options.hide?c.uiDialog.hide(c.options.hide,function(){c._trigger("close",b)}):(c.uiDialog.hide(),c._trigger("close",b)),a.ui.dialog.overlay.resize(),c.options.modal&&(f=0,a(".ui-dialog").each(function(){var b=a(this);this!==
c.uiDialog[0]&&b.attr("role")&&"dialog"==b.attr("role")&&(e=b.css("z-index"),isNaN(e)||(f=Math.max(f,e)))}),a.ui.dialog.maxZ=f),c},isOpen:function(){return this._isOpen},moveToTop:function(b,c){var f=this.options,e,h;if(f.modal&&!b||!f.stack&&!f.modal)return this._trigger("focus",c);f.useMaxZ?(f.zIndex>a.ui.dialog.maxZ&&(a.ui.dialog.maxZ=f.zIndex),this.overlay&&(a.ui.dialog.maxZ+=1,this.overlay.$el.css("z-index",a.ui.dialog.overlay.maxZ=a.ui.dialog.maxZ))):this.overlay&&(h=f.zIndex,a.ui.dialog.overlay.maxZ=
h-1,this.overlay.$el.css("z-index",a.ui.dialog.overlay.maxZ));e={scrollTop:this.element.scrollTop(),scrollLeft:this.element.scrollLeft()};f.useMaxZ&&(a.ui.dialog.maxZ+=1);this.uiDialog.css("z-index",f.useMaxZ?a.ui.dialog.maxZ:h);this.element.attr(e);this._trigger("focus",c);return this},_focusTabbable:function(){var a=this.element.find("[autofocus]");a.length||(a=this.element.find(":tabbable"));!a.length&&this.uiDialogButtonPane&&this.uiDialogButtonPane.length&&(a=this.uiDialogButtonPane.find(":tabbable"));
!a.length&&this.uiDialogTitlebarClose&&this.uiDialogTitlebarClose.length&&(a=this.uiDialogTitlebarClose.filter(":tabbable"));a.length||(a=this.uiDialog);a.eq(0).focus()},open:function(){if(!this._isOpen){var b=this.options,c=this.uiDialog;this.overlay=b.modal?new a.ui.dialog.overlay(this):null;this._size();this._position(b.position);c.show(b.show);this.moveToTop(!0);b.disabled&&(this._contentOverlay(!1),c.addClass("ui-state-disabled ui-dialog-disabled"));b.modal&&c.bind("keydown.ui-dialog",function(b){if(b.keyCode===
a.ui.keyCode.TAB){var d=a(":tabbable",this),h=d.filter(":first"),d=d.filter(":last"),d=c.find(":tabbable"),h=d.filter(":first"),d=d.filter(":last");b.target!==d[0]&&b.target!==c[0]||b.shiftKey?b.target!==h[0]&&b.target!==c[0]||!b.shiftKey||(d.focus(1),b.preventDefault()):(h.focus(1),b.preventDefault())}});a(this.element.find(":tabbable").get().concat(c.find(".ui-dialog-buttonpane :tabbable").get().concat(c.get()))).eq(0).focus();this._isOpen=!0;this._trigger("open");return this}},_createButtons:function(b){var c=
this,f=!1,e=a("<div></div>").addClass("ui-dialog-buttonpane ui-widget-content ui-helper-clearfix"),h=a("<div></div>").addClass("ui-dialog-buttonset").appendTo(e);c.uiDialog.find(".ui-dialog-buttonpane").remove();"object"===typeof b&&null!==b&&a.each(b,function(){return!(f=!0)});f&&(a.each(b,function(b,d){d=a.isFunction(d)?{click:d,text:b}:d;var e=a('<button type="button"></button>').click(function(){d.click.apply(c.element[0],arguments)}).appendTo(h);a.each(d,function(a,b){if("click"!==a)if(a in e)e[a](b);
else e.attr(a,b)});a.fn.button&&e.button()}),e.appendTo(c.uiDialog))},_makeDraggable:function(){function b(a){return{position:a.position,offset:a.offset}}var c=this,f=c.options,e=a(document),h;c.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(e,l){h="auto"===f.height?"auto":a(this).height();a(this).height(a(this).height()).addClass("ui-dialog-dragging");c._trigger("dragStart",e,b(l))},drag:function(a,e){c._trigger("drag",
a,b(e))},stop:function(k,l){f.position=[l.position.left-e.scrollLeft(),l.position.top-e.scrollTop()];a(this).removeClass("ui-dialog-dragging").height(h);c._trigger("dragStop",k,b(l));a.ui.dialog.overlay.resize()}})},_makeResizable:function(b){function c(a){return{originalPosition:a.originalPosition,originalSize:a.originalSize,position:a.position,size:a.size}}b=b===p?this.options.resizable:b;var f=this,e=f.options,h=f.uiDialog.css("position");b="string"===typeof b?b:"n,e,s,w,se,sw,ne,nw";f.uiDialog.resizable({cancel:".ui-dialog-content",
containment:"document",alsoResize:f.element,maxWidth:e.maxWidth,maxHeight:e.maxHeight,minWidth:e.minWidth,minHeight:f._minHeight(),handles:b,start:function(b,d){a(this).addClass("ui-dialog-resizing");f._trigger("resizeStart",b,c(d))},resize:function(a,b){f._trigger("resize",a,c(b))},stop:function(b,d){a(this).removeClass("ui-dialog-resizing");e.height=a(this).height();e.width=a(this).width();f._trigger("resizeStop",b,c(d));a.ui.dialog.overlay.resize()}}).css("position",h).find(".ui-resizable-se").addClass("ui-icon ui-icon-grip-diagonal-se")},
_minHeight:function(){var a=this.options;return"auto"===a.height?a.minHeight:Math.min(a.minHeight,a.height)},_position:function(b){var c=[],f=[0,0],e;if(b){if("string"===typeof b||"object"===typeof b&&"0"in b)c=b.split?b.split(" "):[b[0],b[1]],1===c.length&&(c[1]=c[0]),a.each(["left","top"],function(a,b){+c[a]===c[a]&&(f[a]=c[a],c[a]=b)}),b={my:c.join(" "),at:c.join(" "),offset:f.join(" ")};b=a.extend({},a.ui.dialog.prototype.options.position,b)}else b=a.ui.dialog.prototype.options.position;(e=this.uiDialog.is(":visible"))||
this.uiDialog.show();this.uiDialog.css({top:0,left:0}).position(a.extend({of:window},b));e||this.uiDialog.hide()},_contentOverlay:function(b){t=this.uiDialogTitlebar.outerHeight();b?(this.uiDialogOverlay||(this.uiDialogOverlay=a("<div>").css({left:0,top:t,position:"absolute",height:"100%",width:"100%","z-index":"999999"}).appendTo(this.uiDialog)),this.uiDialogOverlay.show()):this.uiDialogOverlay&&this.uiDialogOverlay.hide()},_setOptions:function(d){var g=this,f={},e=!1;a.each(d,function(a,d){g._setOption(a,
d);a in c&&(e=!0);a in b&&(f[a]=d)});e&&this._size();this.uiDialog.is(":data(resizable)")&&this.uiDialog.resizable("option",f)},_setOption:function(b,c){var f=this.uiDialog;switch(b){case "beforeclose":b="beforeClose";break;case "buttons":this._createButtons(c);break;case "closeText":this.uiDialogTitlebarCloseText.text(""+c);break;case "dialogClass":f.removeClass(this.options.dialogClass).addClass("ui-dialog ui-widget ui-widget-content ui-corner-all "+c);break;case "disabled":this._contentOverlay(c);
c?f.addClass("ui-dialog-disabled"):f.removeClass("ui-dialog-disabled");break;case "draggable":var e=f.is(":data(draggable)");e&&!c&&f.draggable("destroy");!e&&c&&this._makeDraggable();break;case "position":this._position(c);break;case "resizable":(e=f.is(":data(resizable)"))&&!c&&f.resizable("destroy");e&&"string"===typeof c&&f.resizable("option","handles",c);e||!1===c||this._makeResizable(c);break;case "title":a(".ui-dialog-title",this.uiDialogTitlebar).html(""+(c||"&#160;"))}a.Widget.prototype._setOption.apply(this,
arguments)},_size:function(){var b=this.options,c,f,e=this.uiDialog.is(":visible");this.element.show().css({width:"auto",minHeight:0,height:0});b.minWidth>b.width&&(b.width=b.minWidth);c=this.uiDialog.css({height:"auto",width:b.width}).height();f=Math.max(0,b.minHeight-c);"auto"===b.height?a.support.minHeight?this.element.css({minHeight:f,height:"auto"}):(this.uiDialog.show(),b=this.element.css("height","auto").height(),e||this.uiDialog.hide(),this.element.height(Math.round(Math.max(b,f)))):this.element.height(Math.round(Math.max(b.height-
c,0)));this.uiDialog.is(":data(resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())}});a.extend(a.ui.dialog,{version:"1.8.23",uuid:0,maxZ:0,getTitleId:function(a){a=a.attr("id");a||(a=this.uuid+=1);return"ui-dialog-title-"+a},overlay:function(b){this.$el=a.ui.dialog.overlay.create(b)}});a.extend(a.ui.dialog.overlay,{instances:[],oldInstances:[],maxZ:0,events:a.map("focus mousedown mouseup keydown keypress click".split(" "),function(a){return a+".dialog-overlay"}).join(" "),
create:function(b){var c=this;0===this.instances.length&&(setTimeout(function(){a.ui.dialog.overlay.instances.length&&(a(document).bind(a.ui.dialog.overlay.events,function(b){if(a(b.target).zIndex()<a.ui.dialog.overlay.maxZ)return!1}),a(document).bind("focusin.dialog",function(b){c._allowInteraction(b)||(b.preventDefault(),a(".ui-dialog:visible:last .ui-dialog-content").data("dialog")._focusTabbable())}))},1),a(document).bind("keydown.dialog-overlay",function(c){var g=a.ui.dialog.overlay.instances;
0!==g.length&&g[g.length-1]===f&&b.options.closeOnEscape&&!c.isDefaultPrevented()&&c.keyCode&&c.keyCode===a.ui.keyCode.ESCAPE&&(b.close(c),c.preventDefault())}),a(window).bind("resize.dialog-overlay",a.ui.dialog.overlay.resize));var f=(this.oldInstances.pop()||a("<div></div>").addClass("ui-widget-overlay")).appendTo(document.body).css({width:this.width(),height:this.height()});a.fn.bgiframe&&f.bgiframe();this.instances.push(f);return f},_allowInteraction:function(a){return!0},destroy:function(b){var c=
a.inArray(b,this.instances);-1!=c&&this.oldInstances.push(this.instances.splice(c,1)[0]);0===this.instances.length&&a([document,window]).unbind(".dialog-overlay");b.remove();a(document).unbind("focusin.dialog");var f=0;a.each(this.instances,function(){f=Math.max(f,this.css("z-index"))});this.maxZ=f},height:function(){var b,c;return a.browser.msie&&7>a.browser.version?(b=Math.max(document.documentElement.scrollHeight,document.body.scrollHeight),c=Math.max(document.documentElement.offsetHeight,document.body.offsetHeight),
b<c?a(window).height()+"px":b+"px"):a(document).height()+"px"},width:function(){var b,c;return a.browser.msie?(b=Math.max(document.documentElement.scrollWidth,document.body.scrollWidth),c=Math.max(document.documentElement.offsetWidth,document.body.offsetWidth),b<c?a(window).width()+"px":b+"px"):a(document).width()+"px"},resize:function(){var b=a([]);a.each(a.ui.dialog.overlay.instances,function(){b=b.add(this)});b.css({width:0,height:0}).css({width:a.ui.dialog.overlay.width(),height:a.ui.dialog.overlay.height()})}});
a.extend(a.ui.dialog.overlay.prototype,{destroy:function(){a.ui.dialog.overlay.destroy(this.$el)}})})(jQuery);
(function(a,p){a.ui=a.ui||{};var c=/left|center|right/,b=/top|center|bottom/,d={},g=a.fn.position,f=a.fn.offset;a.fn.position=function(e){if(!e||!e.of)return g.apply(this,arguments);e=a.extend({},e);var f=a(e.of),k=f[0],l=(e.collision||"flip").split(" "),m=e.offset?e.offset.split(" "):[0,0],n,p,q;9===k.nodeType?(n=f.width(),p=f.height(),q={top:0,left:0}):k.setTimeout?(n=f.width(),p=f.height(),q={top:f.scrollTop(),left:f.scrollLeft()}):k.preventDefault?(e.at="left top",n=p=0,q={top:e.of.pageY,left:e.of.pageX}):
(n=f.outerWidth(),p=f.outerHeight(),q=f.offset());a.each(["my","at"],function(){var a=(e[this]||"").split(" ");1===a.length&&(a=c.test(a[0])?a.concat(["center"]):b.test(a[0])?["center"].concat(a):["center","center"]);a[0]=c.test(a[0])?a[0]:"center";a[1]=b.test(a[1])?a[1]:"center";e[this]=a});1===l.length&&(l[1]=l[0]);m[0]=parseInt(m[0],10)||0;1===m.length&&(m[1]=m[0]);m[1]=parseInt(m[1],10)||0;"right"===e.at[0]?q.left+=n:"center"===e.at[0]&&(q.left+=n/2);"bottom"===e.at[1]?q.top+=p:"center"===e.at[1]&&
(q.top+=p/2);q.left+=m[0];q.top+=m[1];return this.each(function(){var b=a(this),c=b.outerWidth(),f=b.outerHeight(),g=parseInt(a.curCSS(this,"marginLeft",!0))||0,h=parseInt(a.curCSS(this,"marginTop",!0))||0,k=c+g+(parseInt(a.curCSS(this,"marginRight",!0))||0),z=f+h+(parseInt(a.curCSS(this,"marginBottom",!0))||0),x=a.extend({},q),D;"right"===e.my[0]?x.left-=c:"center"===e.my[0]&&(x.left-=c/2);"bottom"===e.my[1]?x.top-=f:"center"===e.my[1]&&(x.top-=f/2);d.fractions||(x.left=Math.round(x.left),x.top=
Math.round(x.top));D={left:x.left-g,top:x.top-h};a.each(["left","top"],function(b,d){if(a.ui.position[l[b]])a.ui.position[l[b]][d](x,{targetWidth:n,targetHeight:p,elemWidth:c,elemHeight:f,collisionPosition:D,collisionWidth:k,collisionHeight:z,offset:m,my:e.my,at:e.at})});a.fn.bgiframe&&b.bgiframe();b.offset(a.extend(x,{using:e.using}))})};a.ui.position={fit:{left:function(b,c){var d=a(window),d=c.collisionPosition.left+c.collisionWidth-d.width()-d.scrollLeft();b.left=0<d?b.left-d:Math.max(b.left-
c.collisionPosition.left,b.left)},top:function(b,c){var d=a(window),d=c.collisionPosition.top+c.collisionHeight-d.height()-d.scrollTop();b.top=0<d?b.top-d:Math.max(b.top-c.collisionPosition.top,b.top)}},flip:{left:function(b,c){if("center"!==c.at[0]){var d=a(window),d=c.collisionPosition.left+c.collisionWidth-d.width()-d.scrollLeft(),f="left"===c.my[0]?-c.elemWidth:"right"===c.my[0]?c.elemWidth:0,g="left"===c.at[0]?c.targetWidth:-c.targetWidth,n=-2*c.offset[0];b.left+=0>c.collisionPosition.left?f+
g+n:0<d?f+g+n:0}},top:function(b,c){if("center"!==c.at[1]){var d=a(window),d=c.collisionPosition.top+c.collisionHeight-d.height()-d.scrollTop(),f="top"===c.my[1]?-c.elemHeight:"bottom"===c.my[1]?c.elemHeight:0,g="top"===c.at[1]?c.targetHeight:-c.targetHeight,n=-2*c.offset[1];b.top+=0>c.collisionPosition.top?f+g+n:0<d?f+g+n:0}}}};a.offset.setOffset||(a.offset.setOffset=function(b,c){/static/.test(a.curCSS(b,"position"))&&(b.style.position="relative");var d=a(b),f=d.offset(),g=parseInt(a.curCSS(b,"top",
!0),10)||0,n=parseInt(a.curCSS(b,"left",!0),10)||0,f={top:c.top-f.top+g,left:c.left-f.left+n};"using"in c?c.using.call(b,f):d.css(f)},a.fn.offset=function(b){var c=this[0];return c&&c.ownerDocument?b?a.isFunction(b)?this.each(function(c){a(this).offset(b.call(this,c,a(this).offset()))}):this.each(function(){a.offset.setOffset(this,b)}):f.call(this):null});a.curCSS||(a.curCSS=a.css);(function(){var b=document.getElementsByTagName("body")[0],c=document.createElement("div"),f,g;f=document.createElement(b?
"div":"body");g={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"};b&&a.extend(g,{position:"absolute",left:"-1000px",top:"-1000px"});for(var m in g)f.style[m]=g[m];f.appendChild(c);g=b||document.documentElement;g.insertBefore(f,g.firstChild);c.style.cssText="position: absolute; left: 10.7432222px; top: 10.432325px; height: 30px; width: 201px;";c=a(c).offset(function(b,a){return a}).offset();f.innerHTML="";g.removeChild(f);b=c.top+c.left+(b?2E3:0);d.fractions=21<b&&22>b})()})(jQuery);
(function(a,p){a.widget("ui.progressbar",{options:{value:0,max:100},min:0,_create:function(){this.element.addClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").attr({role:"progressbar","aria-valuemin":this.min,"aria-valuemax":this.options.max,"aria-valuenow":this._value()});this.valueDiv=a("<div class='ui-progressbar-value ui-widget-header ui-corner-left'></div>").appendTo(this.element);this.oldValue=this._value();this._refreshValue()},destroy:function(){this.element.removeClass("ui-progressbar ui-widget ui-widget-content ui-corner-all").removeAttr("role").removeAttr("aria-valuemin").removeAttr("aria-valuemax").removeAttr("aria-valuenow");
this.valueDiv.remove();a.Widget.prototype.destroy.apply(this,arguments)},value:function(a){if(a===p)return this._value();this._setOption("value",a);return this},_setOption:function(c,b){"value"===c&&(this.options.value=b,this._refreshValue(),this._value()===this.options.max&&this._trigger("complete"));a.Widget.prototype._setOption.apply(this,arguments)},_value:function(){var a=this.options.value;"number"!==typeof a&&(a=0);return Math.min(this.options.max,Math.max(this.min,a))},_percentage:function(){return 100*
this._value()/this.options.max},_refreshValue:function(){var a=this.value(),b=this._percentage();this.oldValue!==a&&(this.oldValue=a,this._trigger("change"));this.valueDiv.toggle(a>this.min).toggleClass("ui-corner-right",a===this.options.max).width(b.toFixed(0)+"%");this.element.attr("aria-valuenow",a)}});a.extend(a.ui.progressbar,{version:"1.8.23"})})(jQuery);
(function(a,p){a.widget("ui.slider",a.ui.mouse,{widgetEventPrefix:"slide",options:{animate:!1,distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null},_create:function(){var c=this,b=this.options,d=this.element.find(".ui-slider-handle").addClass("ui-state-default ui-corner-all"),g=b.values&&b.values.length||1,f=[];this._mouseSliding=this._keySliding=!1;this._animateOff=!0;this._handleIndex=null;this._detectOrientation();this._mouseInit();this.element.addClass("ui-slider ui-slider-"+
this.orientation+" ui-widget ui-widget-content ui-corner-all"+(b.disabled?" ui-slider-disabled ui-disabled":""));this.range=a([]);b.range&&(!0===b.range&&(b.values||(b.values=[this._valueMin(),this._valueMin()]),b.values.length&&2!==b.values.length&&(b.values=[b.values[0],b.values[0]])),this.range=a("<div></div>").appendTo(this.element).addClass("ui-slider-range ui-widget-header"+("min"===b.range||"max"===b.range?" ui-slider-range-"+b.range:"")));for(var e=d.length;e<g;e+=1)f.push("<a class='ui-slider-handle ui-state-default ui-corner-all' href='#'></a>");
this.handles=d.add(a(f.join("")).appendTo(c.element));this.handle=this.handles.eq(0);this.handles.add(this.range).filter("a").click(function(a){a.preventDefault()}).hover(function(){b.disabled||a(this).addClass("ui-state-hover")},function(){a(this).removeClass("ui-state-hover")}).focus(function(){b.disabled?a(this).blur():(a(".ui-slider .ui-state-focus").removeClass("ui-state-focus"),a(this).addClass("ui-state-focus"))}).blur(function(){a(this).removeClass("ui-state-focus")});this.handles.each(function(b){a(this).data("index.ui-slider-handle",
b)});this.handles.keydown(function(b){var d=a(this).data("index.ui-slider-handle"),e,f,g;if(!c.options.disabled){switch(b.keyCode){case a.ui.keyCode.HOME:case a.ui.keyCode.END:case a.ui.keyCode.PAGE_UP:case a.ui.keyCode.PAGE_DOWN:case a.ui.keyCode.UP:case a.ui.keyCode.RIGHT:case a.ui.keyCode.DOWN:case a.ui.keyCode.LEFT:if(b.preventDefault(),!c._keySliding&&(c._keySliding=!0,a(this).addClass("ui-state-active"),e=c._start(b,d),!1===e))return}g=c.options.step;e=c.options.values&&c.options.values.length?
f=c.values(d):f=c.value();switch(b.keyCode){case a.ui.keyCode.HOME:f=c._valueMin();break;case a.ui.keyCode.END:f=c._valueMax();break;case a.ui.keyCode.PAGE_UP:f=c._trimAlignValue(e+(c._valueMax()-c._valueMin())/5);break;case a.ui.keyCode.PAGE_DOWN:f=c._trimAlignValue(e-(c._valueMax()-c._valueMin())/5);break;case a.ui.keyCode.UP:case a.ui.keyCode.RIGHT:if(e===c._valueMax())return;f=c._trimAlignValue(e+g);break;case a.ui.keyCode.DOWN:case a.ui.keyCode.LEFT:if(e===c._valueMin())return;f=c._trimAlignValue(e-
g)}c._slide(b,d,f)}}).keyup(function(b){var d=a(this).data("index.ui-slider-handle");c._keySliding&&(c._keySliding=!1,c._stop(b,d),c._change(b,d),a(this).removeClass("ui-state-active"))});this._refreshValue();this._animateOff=!1},destroy:function(){this.handles.remove();this.range.remove();this.element.removeClass("ui-slider ui-slider-horizontal ui-slider-vertical ui-slider-disabled ui-widget ui-widget-content ui-corner-all").removeData("slider").unbind(".slider");this._mouseDestroy();return this},
_mouseCapture:function(c){var b=this.options,d,g,f,e,h;if(b.disabled)return!1;this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()};this.elementOffset=this.element.offset();d=this._normValueFromMouse({x:c.pageX,y:c.pageY});g=this._valueMax()-this._valueMin()+1;e=this;this.handles.each(function(b){var c=Math.abs(d-e.values(b));g>c&&(g=c,f=a(this),h=b)});!0===b.range&&this.values(1)===b.min&&(h+=1,f=a(this.handles[h]));if(!1===this._start(c,h))return!1;this._mouseSliding=
!0;e._handleIndex=h;f.addClass("ui-state-active").focus();b=f.offset();this._clickOffset=a(c.target).parents().andSelf().is(".ui-slider-handle")?{left:c.pageX-b.left-f.width()/2,top:c.pageY-b.top-f.height()/2-(parseInt(f.css("borderTopWidth"),10)||0)-(parseInt(f.css("borderBottomWidth"),10)||0)+(parseInt(f.css("marginTop"),10)||0)}:{left:0,top:0};this.handles.hasClass("ui-state-hover")||this._slide(c,h,d);return this._animateOff=!0},_mouseStart:function(a){return!0},_mouseDrag:function(a){var b=this._normValueFromMouse({x:a.pageX,
y:a.pageY});this._slide(a,this._handleIndex,b);return!1},_mouseStop:function(a){this.handles.removeClass("ui-state-active");this._mouseSliding=!1;this._stop(a,this._handleIndex);this._change(a,this._handleIndex);this._clickOffset=this._handleIndex=null;return this._animateOff=!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(a){var b;"horizontal"===this.orientation?(b=this.elementSize.width,a=a.x-this.elementOffset.left-
(this._clickOffset?this._clickOffset.left:0)):(b=this.elementSize.height,a=a.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0));b=a/b;1<b&&(b=1);0>b&&(b=0);"vertical"===this.orientation&&(b=1-b);a=this._valueMax()-this._valueMin();b=this._valueMin()+b*a;return this._trimAlignValue(b)},_start:function(a,b){var d={handle:this.handles[b],value:this.value()};this.options.values&&this.options.values.length&&(d.value=this.values(b),d.values=this.values());return this._trigger("start",
a,d)},_slide:function(a,b,d){var g;this.options.values&&this.options.values.length?(g=this.values(b?0:1),2===this.options.values.length&&!0===this.options.range&&(0===b&&d>g||1===b&&d<g)&&(d=g),d!==this.values(b)&&(g=this.values(),g[b]=d,a=this._trigger("slide",a,{handle:this.handles[b],value:d,values:g}),this.values(b?0:1),!1!==a&&this.values(b,d,!0))):d!==this.value()&&(a=this._trigger("slide",a,{handle:this.handles[b],value:d}),!1!==a&&this.value(d))},_stop:function(a,b){var d={handle:this.handles[b],
value:this.value()};this.options.values&&this.options.values.length&&(d.value=this.values(b),d.values=this.values());this._trigger("stop",a,d)},_change:function(a,b){if(!this._keySliding&&!this._mouseSliding){var d={handle:this.handles[b],value:this.value()};this.options.values&&this.options.values.length&&(d.value=this.values(b),d.values=this.values());this._trigger("change",a,d)}},value:function(a){if(arguments.length)this.options.value=this._trimAlignValue(a),this._refreshValue(),this._change(null,
0);else return this._value()},values:function(c,b){var d,g,f;if(1<arguments.length)this.options.values[c]=this._trimAlignValue(b),this._refreshValue(),this._change(null,c);else if(arguments.length)if(a.isArray(arguments[0])){d=this.options.values;g=arguments[0];for(f=0;f<d.length;f+=1)d[f]=this._trimAlignValue(g[f]),this._change(null,f);this._refreshValue()}else return this.options.values&&this.options.values.length?this._values(c):this.value();else return this._values()},_setOption:function(c,b){var d,
g=0;a.isArray(this.options.values)&&(g=this.options.values.length);a.Widget.prototype._setOption.apply(this,arguments);switch(c){case "disabled":b?(this.handles.filter(".ui-state-focus").blur(),this.handles.removeClass("ui-state-hover"),this.handles.propAttr("disabled",!0),this.element.addClass("ui-disabled")):(this.handles.propAttr("disabled",!1),this.element.removeClass("ui-disabled"));break;case "orientation":this._detectOrientation();this.element.removeClass("ui-slider-horizontal ui-slider-vertical").addClass("ui-slider-"+
this.orientation);this._refreshValue();break;case "value":this._animateOff=!0;this._refreshValue();this._change(null,0);this._animateOff=!1;break;case "values":this._animateOff=!0;this._refreshValue();for(d=0;d<g;d+=1)this._change(null,d);this._animateOff=!1}},_value:function(){var a=this.options.value;return a=this._trimAlignValue(a)},_values:function(a){var b,d;if(arguments.length)return b=this.options.values[a],b=this._trimAlignValue(b);b=this.options.values.slice();for(d=0;d<b.length;d+=1)b[d]=
this._trimAlignValue(b[d]);return b},_trimAlignValue:function(a){if(a<=this._valueMin())return this._valueMin();if(a>=this._valueMax())return this._valueMax();var b=0<this.options.step?this.options.step:1,d=(a-this._valueMin())%b;a-=d;2*Math.abs(d)>=b&&(a+=0<d?b:-b);return parseFloat(a.toFixed(5))},_valueMin:function(){return this.options.min},_valueMax:function(){return this.options.max},_refreshValue:function(){var c=this.options.range,b=this.options,d=this,g=this._animateOff?!1:b.animate,f,e={},
h,k,l,m;if(this.options.values&&this.options.values.length)this.handles.each(function(c,k){f=(d.values(c)-d._valueMin())/(d._valueMax()-d._valueMin())*100;e["horizontal"===d.orientation?"left":"bottom"]=f+"%";a(this).stop(1,1)[g?"animate":"css"](e,b.animate);if(!0===d.options.range)if("horizontal"===d.orientation){if(0===c)d.range.stop(1,1)[g?"animate":"css"]({left:f+"%"},b.animate);if(1===c)d.range[g?"animate":"css"]({width:f-h+"%"},{queue:!1,duration:b.animate})}else{if(0===c)d.range.stop(1,1)[g?
"animate":"css"]({bottom:f+"%"},b.animate);if(1===c)d.range[g?"animate":"css"]({height:f-h+"%"},{queue:!1,duration:b.animate})}h=f});else{k=this.value();l=this._valueMin();m=this._valueMax();f=m!==l?(k-l)/(m-l)*100:0;e["horizontal"===d.orientation?"left":"bottom"]=f+"%";this.handle.stop(1,1)[g?"animate":"css"](e,b.animate);if("min"===c&&"horizontal"===this.orientation)this.range.stop(1,1)[g?"animate":"css"]({width:f+"%"},b.animate);if("max"===c&&"horizontal"===this.orientation)this.range[g?"animate":
"css"]({width:100-f+"%"},{queue:!1,duration:b.animate});if("min"===c&&"vertical"===this.orientation)this.range.stop(1,1)[g?"animate":"css"]({height:f+"%"},b.animate);if("max"===c&&"vertical"===this.orientation)this.range[g?"animate":"css"]({height:100-f+"%"},{queue:!1,duration:b.animate})}}});a.extend(a.ui.slider,{version:"1.8.23"})})(jQuery);
(function(a,p){var c=0,b=0;a.widget("ui.tabs",{options:{add:null,ajaxOptions:null,cache:!1,cookie:null,collapsible:!1,disable:null,disabled:[],enable:null,event:"click",fx:null,idPrefix:"ui-tabs-",load:null,panelTemplate:"<div></div>",remove:null,select:null,show:null,spinner:"<em>Loading&#8230;</em>",tabTemplate:"<li><a href='#{href}'><span>#{label}</span></a></li>"},_create:function(){this._tabify(!0)},_setOption:function(a,b){"selected"==a?this.options.collapsible&&b==this.options.selected||this.select(b):
(this.options[a]=b,this._tabify())},_tabId:function(a){return a.title&&a.title.replace(/\s/g,"_").replace(/[^\w\u00c0-\uFFFF-]/g,"")||this.options.idPrefix+ ++c},_sanitizeSelector:function(a){return a.replace(/:/g,"\\:")},_cookie:function(){var c=this.cookie||(this.cookie=this.options.cookie.name||"ui-tabs-"+ ++b);return a.cookie.apply(null,[c].concat(a.makeArray(arguments)))},_ui:function(a,b){return{tab:a,panel:b,index:this.anchors.index(a)}},_cleanup:function(){this.lis.filter(".ui-state-processing").removeClass("ui-state-processing").find("span:data(label.tabs)").each(function(){var b=
a(this);b.html(b.data("label.tabs")).removeData("label.tabs")})},_tabify:function(b){function c(b,d){b.css("display","");!a.support.opacity&&d.opacity&&b[0].style.removeAttribute("filter")}var f=this,e=this.options,h=/^#.+/;this.list=this.element.find("ol,ul").eq(0);this.lis=a(" > li:has(a[href])",this.list);this.anchors=this.lis.map(function(){return a("a",this)[0]});this.panels=a([]);this.anchors.each(function(b,c){var d=a(c).attr("href"),g=d.split("#")[0],k;g&&(g===location.toString().split("#")[0]||
(k=a("base")[0])&&g===k.href)&&(d=c.hash,c.href=d);h.test(d)?f.panels=f.panels.add(f.element.find(f._sanitizeSelector(d))):d&&"#"!==d?(a.data(c,"href.tabs",d),a.data(c,"load.tabs",d.replace(/#.*$/,"")),d=f._tabId(c),c.href="#"+d,g=f.element.find("#"+d),g.length||(g=a(e.panelTemplate).attr("id",d).addClass("ui-tabs-panel ui-widget-content ui-corner-bottom").insertAfter(f.panels[b-1]||f.list),g.data("destroy.tabs",!0)),f.panels=f.panels.add(g)):e.disabled.push(b)});b?(this.element.addClass("ui-tabs ui-widget ui-widget-content ui-corner-all"),
this.list.addClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all"),this.lis.addClass("ui-state-default ui-corner-top"),this.panels.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom"),e.selected===p?(location.hash&&this.anchors.each(function(a,b){if(b.hash==location.hash)return e.selected=a,!1}),"number"!==typeof e.selected&&e.cookie&&(e.selected=parseInt(f._cookie(),10)),"number"!==typeof e.selected&&this.lis.filter(".ui-tabs-selected").length&&(e.selected=
this.lis.index(this.lis.filter(".ui-tabs-selected"))),e.selected=e.selected||(this.lis.length?0:-1)):null===e.selected&&(e.selected=-1),e.selected=0<=e.selected&&this.anchors[e.selected]||0>e.selected?e.selected:0,e.disabled=a.unique(e.disabled.concat(a.map(this.lis.filter(".ui-state-disabled"),function(a,b){return f.lis.index(a)}))).sort(),-1!=a.inArray(e.selected,e.disabled)&&e.disabled.splice(a.inArray(e.selected,e.disabled),1),this.panels.addClass("ui-tabs-hide"),this.lis.removeClass("ui-tabs-selected ui-state-active"),
0<=e.selected&&this.anchors.length&&(f.element.find(f._sanitizeSelector(f.anchors[e.selected].hash)).removeClass("ui-tabs-hide"),this.lis.eq(e.selected).addClass("ui-tabs-selected ui-state-active"),f.element.queue("tabs",function(){f._trigger("show",null,f._ui(f.anchors[e.selected],f.element.find(f._sanitizeSelector(f.anchors[e.selected].hash))[0]))}),this.load(e.selected)),a(window).bind("unload",function(){f.lis.add(f.anchors).unbind(".tabs");f.lis=f.anchors=f.panels=null})):e.selected=this.lis.index(this.lis.filter(".ui-tabs-selected"));
this.element[e.collapsible?"addClass":"removeClass"]("ui-tabs-collapsible");e.cookie&&this._cookie(e.selected,e.cookie);b=0;for(var k;k=this.lis[b];b++)a(k)[-1==a.inArray(b,e.disabled)||a(k).hasClass("ui-tabs-selected")?"removeClass":"addClass"]("ui-state-disabled");!1===e.cache&&this.anchors.removeData("cache.tabs");this.lis.add(this.anchors).unbind(".tabs");if("mouseover"!==e.event){var l=function(a,b){b.is(":not(.ui-state-disabled)")&&b.addClass("ui-state-"+a)};this.lis.bind("mouseover.tabs",function(){l("hover",
a(this))});this.lis.bind("mouseout.tabs",function(){a(this).removeClass("ui-state-hover")});this.anchors.bind("focus.tabs",function(){l("focus",a(this).closest("li"))});this.anchors.bind("blur.tabs",function(){a(this).closest("li").removeClass("ui-state-focus")})}var m,n;e.fx&&(a.isArray(e.fx)?(m=e.fx[0],n=e.fx[1]):m=n=e.fx);var u=n?function(b,d){a(b).closest("li").addClass("ui-tabs-selected ui-state-active");d.hide().removeClass("ui-tabs-hide").animate(n,n.duration||"normal",function(){c(d,n);f._trigger("show",
null,f._ui(b,d[0]))})}:function(b,c){a(b).closest("li").addClass("ui-tabs-selected ui-state-active");c.removeClass("ui-tabs-hide");f._trigger("show",null,f._ui(b,c[0]))},q=m?function(a,b){b.animate(m,m.duration||"normal",function(){f.lis.removeClass("ui-tabs-selected ui-state-active");b.addClass("ui-tabs-hide");c(b,m);f.element.dequeue("tabs")})}:function(a,b,c){f.lis.removeClass("ui-tabs-selected ui-state-active");b.addClass("ui-tabs-hide");f.element.dequeue("tabs")};this.anchors.bind(e.event+".tabs",
function(){var b=this,c=a(b).closest("li"),d=f.panels.filter(":not(.ui-tabs-hide)"),g=f.element.find(f._sanitizeSelector(b.hash));if(c.hasClass("ui-tabs-selected")&&!e.collapsible||c.hasClass("ui-state-disabled")||c.hasClass("ui-state-processing")||f.panels.filter(":animated").length||!1===f._trigger("select",null,f._ui(this,g[0])))return this.blur(),!1;e.selected=f.anchors.index(this);f.abort();if(e.collapsible){if(c.hasClass("ui-tabs-selected"))return e.selected=-1,e.cookie&&f._cookie(e.selected,
e.cookie),f.element.queue("tabs",function(){q(b,d)}).dequeue("tabs"),this.blur(),!1;if(!d.length)return e.cookie&&f._cookie(e.selected,e.cookie),f.element.queue("tabs",function(){u(b,g)}),f.load(f.anchors.index(this)),this.blur(),!1}e.cookie&&f._cookie(e.selected,e.cookie);if(g.length)d.length&&f.element.queue("tabs",function(){q(b,d)}),f.element.queue("tabs",function(){u(b,g)}),f.load(f.anchors.index(this));else throw"jQuery UI Tabs: Mismatching fragment identifier.";a.browser.msie&&this.blur()});
this.anchors.bind("click.tabs",function(){return!1})},_getIndex:function(a){"string"==typeof a&&(a=this.anchors.index(this.anchors.filter("[href$='"+a+"']")));return a},destroy:function(){var b=this.options;this.abort();this.element.unbind(".tabs").removeClass("ui-tabs ui-widget ui-widget-content ui-corner-all ui-tabs-collapsible").removeData("tabs");this.list.removeClass("ui-tabs-nav ui-helper-reset ui-helper-clearfix ui-widget-header ui-corner-all");this.anchors.each(function(){var b=a.data(this,
"href.tabs");b&&(this.href=b);var c=a(this).unbind(".tabs");a.each(["href","load","cache"],function(a,b){c.removeData(b+".tabs")})});this.lis.unbind(".tabs").add(this.panels).each(function(){a.data(this,"destroy.tabs")?a(this).remove():a(this).removeClass("ui-state-default ui-corner-top ui-tabs-selected ui-state-active ui-state-hover ui-state-focus ui-state-disabled ui-tabs-panel ui-widget-content ui-corner-bottom ui-tabs-hide")});b.cookie&&this._cookie(null,b.cookie);return this},add:function(b,
c,f){f===p&&(f=this.anchors.length);var e=this,h=this.options;c=a(h.tabTemplate.replace(/#\{href\}/g,b).replace(/#\{label\}/g,c));b=b.indexOf("#")?this._tabId(a("a",c)[0]):b.replace("#","");c.addClass("ui-state-default ui-corner-top").data("destroy.tabs",!0);var k=e.element.find("#"+b);k.length||(k=a(h.panelTemplate).attr("id",b).data("destroy.tabs",!0));k.addClass("ui-tabs-panel ui-widget-content ui-corner-bottom ui-tabs-hide");f>=this.lis.length?(c.appendTo(this.list),k.appendTo(this.list[0].parentNode)):
(c.insertBefore(this.lis[f]),k.insertBefore(this.panels[f]));h.disabled=a.map(h.disabled,function(a,b){return a>=f?++a:a});this._tabify();1==this.anchors.length&&(h.selected=0,c.addClass("ui-tabs-selected ui-state-active"),k.removeClass("ui-tabs-hide"),this.element.queue("tabs",function(){e._trigger("show",null,e._ui(e.anchors[0],e.panels[0]))}),this.load(0));this._trigger("add",null,this._ui(this.anchors[f],this.panels[f]));return this},remove:function(b){b=this._getIndex(b);var c=this.options,f=
this.lis.eq(b).remove(),e=this.panels.eq(b).remove();f.hasClass("ui-tabs-selected")&&1<this.anchors.length&&this.select(b+(b+1<this.anchors.length?1:-1));c.disabled=a.map(a.grep(c.disabled,function(a,c){return a!=b}),function(a,c){return a>=b?--a:a});this._tabify();this._trigger("remove",null,this._ui(f.find("a")[0],e[0]));return this},enable:function(b){b=this._getIndex(b);var c=this.options;if(-1!=a.inArray(b,c.disabled))return this.lis.eq(b).removeClass("ui-state-disabled"),c.disabled=a.grep(c.disabled,
function(a,c){return a!=b}),this._trigger("enable",null,this._ui(this.anchors[b],this.panels[b])),this},disable:function(a){a=this._getIndex(a);var b=this.options;a!=b.selected&&(this.lis.eq(a).addClass("ui-state-disabled"),b.disabled.push(a),b.disabled.sort(),this._trigger("disable",null,this._ui(this.anchors[a],this.panels[a])));return this},select:function(a){a=this._getIndex(a);if(-1==a)if(this.options.collapsible&&-1!=this.options.selected)a=this.options.selected;else return this;this.anchors.eq(a).trigger(this.options.event+
".tabs");return this},load:function(b){b=this._getIndex(b);var c=this,f=this.options,e=this.anchors.eq(b)[0],h=a.data(e,"load.tabs");this.abort();if(!h||0!==this.element.queue("tabs").length&&a.data(e,"cache.tabs"))this.element.dequeue("tabs");else{this.lis.eq(b).addClass("ui-state-processing");if(f.spinner){var k=a("span",e);k.data("label.tabs",k.html()).html(f.spinner)}this.xhr=a.ajax(a.extend({},f.ajaxOptions,{url:h,success:function(h,k){c.element.find(c._sanitizeSelector(e.hash)).html(h);c._cleanup();
f.cache&&a.data(e,"cache.tabs",!0);c._trigger("load",null,c._ui(c.anchors[b],c.panels[b]));try{f.ajaxOptions.success(h,k)}catch(n){}},error:function(a,h,k){c._cleanup();c._trigger("load",null,c._ui(c.anchors[b],c.panels[b]));try{f.ajaxOptions.error(a,h,b,e)}catch(p){}}}));c.element.dequeue("tabs");return this}},abort:function(){this.element.queue([]);this.panels.stop(!1,!0);this.element.queue("tabs",this.element.queue("tabs").splice(-2,2));this.xhr&&(this.xhr.abort(),delete this.xhr);this._cleanup();
return this},url:function(a,b){this.anchors.eq(a).removeData("cache.tabs").data("load.tabs",b);return this},length:function(){return this.anchors.length}});a.extend(a.ui.tabs,{version:"1.8.23"});a.extend(a.ui.tabs.prototype,{rotation:null,rotate:function(a,b){var c=this,e=this.options,h=c._rotate||(c._rotate=function(b){clearTimeout(c.rotation);c.rotation=setTimeout(function(){var a=e.selected;c.select(++a<c.anchors.length?a:0)},a);b&&b.stopPropagation()}),k=c._unrotate||(c._unrotate=b?function(a){h()}:
function(a){a.clientX&&c.rotate(null)});a?(this.element.bind("tabsshow",h),this.anchors.bind(e.event+".tabs",k),h()):(clearTimeout(c.rotation),this.element.unbind("tabsshow",h),this.anchors.unbind(e.event+".tabs",k),delete this._rotate,delete this._unrotate);return this}})})(jQuery);
