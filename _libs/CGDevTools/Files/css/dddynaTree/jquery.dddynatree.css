.dddynatree-combo {
	display: block;
	overflow: hidden;
	text-decoration: none;
	white-space: nowrap;
	cursor: pointer;
	padding: 0 20px 0 3px;	
}

.dddynatree-combo-icon{
	position: absolute;
	top: 50%;
	margin-top: -8px;
	right: 3px;	
}

.dddynatree-combo-title{
	display: block;
	margin-right: 20px;
	overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;	
}

.dddynatree-popup-container{
    z-index: 99999;
    margin-top: -1px;
}

.dddynatree-search-panel{
    height: 30px;    
    display: inline-block;
    width: 100%;
    min-height: 26px;
    margin-top: 3px;
    padding-left: 4px;
    padding-right: 4px;
    position: relative;
    /*z-index: 10000;*/
    white-space: nowrap;    
}

.dddynatree-search-input {
    width: 100%;
    height: auto !important;
    min-height: 26px;
    padding: 4px 20px 4px 5px;
    margin: 0;

    outline: 0;
    font-family: sans-serif;
    font-size: 1em;

    border: 1px solid #aaa;    
    -webkit-border-radius: 0 4px 4px 0;
       -moz-border-radius: 0 4px 4px 0;
            border-radius: 0 4px 4px 0;

    -webkit-box-shadow: none;
       -moz-box-shadow: none;
            box-shadow: none;

    background: #fff url('jquery.dddynatree.png') no-repeat 100% -22px;
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, -o-linear-gradient(bottom, white 85%, #eeeeee 99%);
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, -ms-linear-gradient(top, #ffffff 85%, #eeeeee 99%);
    background: url('jquery.dddynatree.png') no-repeat 100% -22px, linear-gradient(top, #ffffff 85%, #eeeeee 99%);
}

.dddynatree-search-input, .dddynatree-search-panel, .dddynatree-combo-container, .dddynatree-filter{
	-webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
	-moz-box-sizing: border-box;    /* Firefox, other Gecko */
	box-sizing: border-box;         /* Opera/IE 8+ */	
}

.dddynatree-combo, .dddynatree-filter-span{
	-webkit-user-select: none; /* Chrome/Safari */        
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE10+ */	
	-o-user-select: none;
	user-select: none;	
}

.dddynatree-treeview{
    border: none !important;
}

.dddynatree-filter{
    width: 30px;
    min-height: 26px;
    border: 1px solid #AAAAAA;
    top: 3px;
    position: absolute;
    left: 3px;
    text-align: center;
    -webkit-border-radius: 4px 0 0 4px;
       -moz-border-radius: 4px 0 0 4px;
            border-radius: 4px 0 0 4px;
}

.dddynatree-filter-span{
    display: block;
    padding-top: 4px;
    cursor: pointer;
}

.dddynatree-filters-ul{
    margin: 0;    
    list-style: none;
    display: block;
    position: absolute;   
    font-size: 1em !important;
    min-width: 100px;
    padding: 2px 5px;
    z-index: 99999;
}

.dddynatree-filters-ul a{
    text-decoration: none;
    display: block;
    padding: .2em .4em;
    line-height: 1.5;
    zoom: 1;
    cursor: pointer;
}

.dddynatree-filters-ul a:hover{
    font-weight: normal;
    margin: -1px;
}

.dddynatree-filters-title {
    width: 25px;
    display: inline-block;
    text-align: center;
}