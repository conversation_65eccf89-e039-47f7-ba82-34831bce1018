.ui-trtimepicker-div .ui-widget-header { margin-bottom: 8px; }
.ui-trtimepicker-div dl { text-align: left; }
.ui-trtimepicker-div dl dt { float: left; clear:left; padding: 0 0 0 5px; }
.ui-trtimepicker-div dl dd { margin: 0 10px 10px 40%; }
.ui-trtimepicker-div td { font-size: 90%; }
.ui-tpicker-grid-label { background: none; border: none; margin: 0; padding: 0; }
.ui-trtimepicker-div .ui_tpicker_unit_hide{ display: none; }

.ui-trtimepicker-div .ui_tpicker_time .ui_tpicker_time_input { background: none; color: inherit; border: none; outline: none; border-bottom: solid 1px #555; width: 95%; }
.ui-trtimepicker-div .ui_tpicker_time .ui_tpicker_time_input:focus { border-bottom-color: #aaa; }

.ui-trtimepicker-rtl{ direction: rtl; }
.ui-trtimepicker-rtl dl { text-align: right; padding: 0 5px 0 0; }
.ui-trtimepicker-rtl dl dt{ float: right; clear: right; }
.ui-trtimepicker-rtl dl dd { margin: 0 40% 10px 10px; }
/* Shortened version style */
.ui-trtimepicker-div.ui-timepicker-oneLine { padding-right: 2px; }
.ui-trtimepicker-div.ui-timepicker-oneLine .ui_tpicker_time, 
.ui-trtimepicker-div.ui-timepicker-oneLine dt { display: none; }
.ui-trtimepicker-div.ui-timepicker-oneLine .ui_tpicker_time_label { display: block; padding-top: 2px; }
.ui-trtimepicker-div.ui-timepicker-oneLine dl { text-align: right; }
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd, 
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd > div { display:inline-block; margin:0; }
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_minute:before,
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_second:before { content:':'; display:inline-block; }
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_millisec:before,
.ui-trtimepicker-div.ui-timepicker-oneLine dl dd.ui_tpicker_microsec:before { content:'.'; display:inline-block; }
.ui-trtimepicker-div.ui-timepicker-oneLine .ui_tpicker_unit_hide,
.ui-trtimepicker-div.ui-timepicker-oneLine .ui_tpicker_unit_hide:before{ display: none; }