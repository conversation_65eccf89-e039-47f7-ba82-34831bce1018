/*!
 * Bootstrap v2.3.2 - Icons
 *
 * Copyright 2012 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world @twitter by @mdo and @fat.
 */

.bs-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-top: 1px;
  *margin-right: .3em;
  line-height: 14px;
  vertical-align: text-top;
  background-image: url("glyphicons-halflings.png");
  background-position: 14px 14px;
  background-repeat: no-repeat;
}

/* White icons with optional class, or on hover/focus/active states of certain elements */

.bs-icon-white,
.nav-pills > .active > a > [class^="icon-"],
.nav-pills > .active > a > [class*=" icon-"],
.nav-list > .active > a > [class^="icon-"],
.nav-list > .active > a > [class*=" icon-"],
.navbar-inverse .nav > .active > a > [class^="icon-"],
.navbar-inverse .nav > .active > a > [class*=" icon-"],
.dropdown-menu > li > a:hover > [class^="icon-"],
.dropdown-menu > li > a:focus > [class^="icon-"],
.dropdown-menu > li > a:hover > [class*=" icon-"],
.dropdown-menu > li > a:focus > [class*=" icon-"],
.dropdown-menu > .active > a > [class^="icon-"],
.dropdown-menu > .active > a > [class*=" icon-"],
.dropdown-submenu:hover > a > [class^="icon-"],
.dropdown-submenu:focus > a > [class^="icon-"],
.dropdown-submenu:hover > a > [class*=" icon-"],
.dropdown-submenu:focus > a > [class*=" icon-"] {
  background-image: url("glyphicons-halflings-white.png");
}

.bs-icon-glass {
  background-position: 0      0;
}

.bs-icon-music {
  background-position: -24px 0;
}

.bs-icon-search {
  background-position: -48px 0;
}

.bs-icon-envelope {
  background-position: -72px 0;
}

.bs-icon-heart {
  background-position: -96px 0;
}

.bs-icon-star {
  background-position: -120px 0;
}

.bs-icon-star-empty {
  background-position: -144px 0;
}

.bs-icon-user {
  background-position: -168px 0;
}

.bs-icon-film {
  background-position: -192px 0;
}

.bs-icon-th-large {
  background-position: -216px 0;
}

.bs-icon-th {
  background-position: -240px 0;
}

.bs-icon-th-list {
  background-position: -264px 0;
}

.bs-icon-ok {
  background-position: -288px 0;
}

.bs-icon-remove {
  background-position: -312px 0;
}

.bs-icon-zoom-in {
  background-position: -336px 0;
}

.bs-icon-zoom-out {
  background-position: -360px 0;
}

.bs-icon-off {
  background-position: -384px 0;
}

.bs-icon-signal {
  background-position: -408px 0;
}

.bs-icon-cog {
  background-position: -432px 0;
}

.bs-icon-trash {
  background-position: -456px 0;
}

.bs-icon-home {
  background-position: 0 -24px;
}

.bs-icon-file {
  background-position: -24px -24px;
}

.bs-icon-time {
  background-position: -48px -24px;
}

.bs-icon-road {
  background-position: -72px -24px;
}

.bs-icon-download-alt {
  background-position: -96px -24px;
}

.bs-icon-download {
  background-position: -120px -24px;
}

.bs-icon-upload {
  background-position: -144px -24px;
}

.bs-icon-inbox {
  background-position: -168px -24px;
}

.bs-icon-play-circle {
  background-position: -192px -24px;
}

.bs-icon-repeat {
  background-position: -216px -24px;
}

.bs-icon-refresh {
  background-position: -240px -24px;
}

.bs-icon-list-alt {
  background-position: -264px -24px;
}

.bs-icon-lock {
  background-position: -287px -24px;
}

.bs-icon-flag {
  background-position: -312px -24px;
}

.bs-icon-headphones {
  background-position: -336px -24px;
}

.bs-icon-volume-off {
  background-position: -360px -24px;
}

.bs-icon-volume-down {
  background-position: -384px -24px;
}

.bs-icon-volume-up {
  background-position: -408px -24px;
}

.bs-icon-qrcode {
  background-position: -432px -24px;
}

.bs-icon-barcode {
  background-position: -456px -24px;
}

.bs-icon-tag {
  background-position: 0 -48px;
}

.bs-icon-tags {
  background-position: -25px -48px;
}

.bs-icon-book {
  background-position: -48px -48px;
}

.bs-icon-bookmark {
  background-position: -72px -48px;
}

.bs-icon-print {
  background-position: -96px -48px;
}

.bs-icon-camera {
  background-position: -120px -48px;
}

.bs-icon-font {
  background-position: -144px -48px;
}

.bs-icon-bold {
  background-position: -167px -48px;
}

.bs-icon-italic {
  background-position: -192px -48px;
}

.bs-icon-text-height {
  background-position: -216px -48px;
}

.bs-icon-text-width {
  background-position: -240px -48px;
}

.bs-icon-align-left {
  background-position: -264px -48px;
}

.bs-icon-align-center {
  background-position: -288px -48px;
}

.bs-icon-align-right {
  background-position: -312px -48px;
}

.bs-icon-align-justify {
  background-position: -336px -48px;
}

.bs-icon-list {
  background-position: -360px -48px;
}

.bs-icon-indent-left {
  background-position: -384px -48px;
}

.bs-icon-indent-right {
  background-position: -408px -48px;
}

.bs-icon-facetime-video {
  background-position: -432px -48px;
}

.bs-icon-picture {
  background-position: -456px -48px;
}

.bs-icon-pencil {
  background-position: 0 -72px;
}

.bs-icon-map-marker {
  background-position: -24px -72px;
}

.bs-icon-adjust {
  background-position: -48px -72px;
}

.bs-icon-tint {
  background-position: -72px -72px;
}

.bs-icon-edit {
  background-position: -96px -72px;
}

.bs-icon-share {
  background-position: -120px -72px;
}

.bs-icon-check {
  background-position: -144px -72px;
}

.bs-icon-move {
  background-position: -168px -72px;
}

.bs-icon-step-backward {
  background-position: -192px -72px;
}

.bs-icon-fast-backward {
  background-position: -216px -72px;
}

.bs-icon-backward {
  background-position: -240px -72px;
}

.bs-icon-play {
  background-position: -264px -72px;
}

.bs-icon-pause {
  background-position: -288px -72px;
}

.bs-icon-stop {
  background-position: -312px -72px;
}

.bs-icon-forward {
  background-position: -336px -72px;
}

.bs-icon-fast-forward {
  background-position: -360px -72px;
}

.bs-icon-step-forward {
  background-position: -384px -72px;
}

.bs-icon-eject {
  background-position: -408px -72px;
}

.bs-icon-chevron-left {
  background-position: -432px -72px;
}

.bs-icon-chevron-right {
  background-position: -456px -72px;
}

.bs-icon-plus-sign {
  background-position: 0 -96px;
}

.bs-icon-minus-sign {
  background-position: -24px -96px;
}

.bs-icon-remove-sign {
  background-position: -48px -96px;
}

.bs-icon-ok-sign {
  background-position: -72px -96px;
}

.bs-icon-question-sign {
  background-position: -96px -96px;
}

.bs-icon-info-sign {
  background-position: -120px -96px;
}

.bs-icon-screenshot {
  background-position: -144px -96px;
}

.bs-icon-remove-circle {
  background-position: -168px -96px;
}

.bs-icon-ok-circle {
  background-position: -192px -96px;
}

.bs-icon-ban-circle {
  background-position: -216px -96px;
}

.bs-icon-arrow-left {
  background-position: -240px -96px;
}

.bs-icon-arrow-right {
  background-position: -264px -96px;
}

.bs-icon-arrow-up {
  background-position: -289px -96px;
}

.bs-icon-arrow-down {
  background-position: -312px -96px;
}

.bs-icon-share-alt {
  background-position: -336px -96px;
}

.bs-icon-resize-full {
  background-position: -360px -96px;
}

.bs-icon-resize-small {
  background-position: -384px -96px;
}

.bs-icon-plus {
  background-position: -408px -96px;
}

.bs-icon-minus {
  background-position: -433px -96px;
}

.bs-icon-asterisk {
  background-position: -456px -96px;
}

.bs-icon-exclamation-sign {
  background-position: 0 -120px;
}

.bs-icon-gift {
  background-position: -24px -120px;
}

.bs-icon-leaf {
  background-position: -48px -120px;
}

.bs-icon-fire {
  background-position: -72px -120px;
}

.bs-icon-eye-open {
  background-position: -96px -120px;
}

.bs-icon-eye-close {
  background-position: -120px -120px;
}

.bs-icon-warning-sign {
  background-position: -144px -120px;
}

.bs-icon-plane {
  background-position: -168px -120px;
}

.bs-icon-calendar {
  background-position: -192px -120px;
}

.bs-icon-random {
  width: 16px;
  background-position: -216px -120px;
}

.bs-icon-comment {
  background-position: -240px -120px;
}

.bs-icon-magnet {
  background-position: -264px -120px;
}

.bs-icon-chevron-up {
  background-position: -288px -120px;
}

.bs-icon-chevron-down {
  background-position: -313px -119px;
}

.bs-icon-retweet {
  background-position: -336px -120px;
}

.bs-icon-shopping-cart {
  background-position: -360px -120px;
}

.bs-icon-folder-close {
  width: 16px;
  background-position: -384px -120px;
}

.bs-icon-folder-open {
  width: 16px;
  background-position: -408px -120px;
}

.bs-icon-resize-vertical {
  background-position: -432px -119px;
}

.bs-icon-resize-horizontal {
  background-position: -456px -118px;
}

.bs-icon-hdd {
  background-position: 0 -144px;
}

.bs-icon-bullhorn {
  background-position: -24px -144px;
}

.bs-icon-bell {
  background-position: -48px -144px;
}

.bs-icon-certificate {
  background-position: -72px -144px;
}

.bs-icon-thumbs-up {
  background-position: -96px -144px;
}

.bs-icon-thumbs-down {
  background-position: -120px -144px;
}

.bs-icon-hand-right {
  background-position: -144px -144px;
}

.bs-icon-hand-left {
  background-position: -168px -144px;
}

.bs-icon-hand-up {
  background-position: -192px -144px;
}

.bs-icon-hand-down {
  background-position: -216px -144px;
}

.bs-icon-circle-arrow-right {
  background-position: -240px -144px;
}

.bs-icon-circle-arrow-left {
  background-position: -264px -144px;
}

.bs-icon-circle-arrow-up {
  background-position: -288px -144px;
}

.bs-icon-circle-arrow-down {
  background-position: -312px -144px;
}

.bs-icon-globe {
  background-position: -336px -144px;
}

.bs-icon-wrench {
  background-position: -360px -144px;
}

.bs-icon-tasks {
  background-position: -384px -144px;
}

.bs-icon-filter {
  background-position: -408px -144px;
}

.bs-icon-briefcase {
  background-position: -432px -144px;
}

.bs-icon-fullscreen {
  background-position: -456px -144px;
}
