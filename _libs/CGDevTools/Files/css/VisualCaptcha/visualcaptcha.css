/*! visualCaptcha - v0.0.8 - 2016-01-23
* http://visualcaptcha.net
* Copyright (c) 2016 emotionLoop; Licensed MIT */

/*---------------------------------------------------
    LESS Elements 0.9
  ---------------------------------------------------
    A set of useful LESS mixins
    More info at: http://lesselements.com
  ---------------------------------------------------*/
.visualCaptcha {
  margin-bottom: 20px;
  min-height: 120px;
}
@media only screen and (min-width: 750px) {
  .visualCaptcha {
    margin-bottom: 50px;
  }
}
.visualCaptcha * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.visualCaptcha .audioField {
  font-size: 15px;
  color: #495e62;
  font-weight: 100;
  padding: 5px;
  border: 1px solid #3bb6e4;
  height: 40px;
  width: 100%;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
}
@media only screen and (max-width: 360px) {
  .visualCaptcha .audioField {
    margin-bottom: 15px;
  }
}
@media only screen and (min-width: 360px) {
  .visualCaptcha .audioField {
    width: 240px;
    float: left;
  }
}
.accessibility-description,
.visualCaptcha-explanation,
.status.valid {
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  border-radius: 7px;
  background-color: #c1f7e8;
  padding: 10px;
  text-align: center;
  font-size: 18px;
/*  
  color: #219589;
  font-family: 'Oxygen', sans-serif;
*/  
  font-weight: 100;
  margin: 20px 0 24px;
}
@media only screen and (max-width: 360px) {
  .accessibility-description,
  .visualCaptcha-explanation,
  .status.valid {
    font-size: 16px;
  }
}
.visualCaptcha-possibilities,
.visualCaptcha-refresh-button,
.visualCaptcha-accessibility-button,
.visualCaptcha-button-group {
  display: inline-block;
}
.visualCaptcha-possibilities {
  vertical-align: middle;
}
@media only screen and (min-width: 750px) {
  .visualCaptcha-possibilities {
    margin-right: 15px;
  }
}
.visualCaptcha-possibilities .img {
  padding: 4px;
  border: 1px solid #ffffff;
  display: inline-block;
}
@media only screen and (min-width: 750px) {
  .visualCaptcha-possibilities .img {
    padding: 10px;
    border: 3px solid #ffffff;
  }
}
.visualCaptcha-possibilities img {
  width: 32px;
  height: 32px;
  z-index: 5;
  transition: all 200ms;
  -webkit-transition: all 200ms;
}
.visualCaptcha-possibilities img:hover {
  cursor: pointer;
}
@media only screen and (max-width: 360px) {
  .visualCaptcha-possibilities .visualCaptcha-button-group {
    text-align: center;
    margin-top: 15px;
  }
}
@media only screen and (min-width: 750px) {
  .visualCaptcha-possibilities .visualCaptcha-button-group {
    float: right;
  }
}
.visualCaptcha-possibilities .visualCaptcha-selected {
  background-color: #c1f7e8;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  padding: 4px;
  border: 1px dashed #099a9f;
}
@media only screen and (min-width: 750px) {
  .visualCaptcha-possibilities .visualCaptcha-selected {
    padding: 10px;
    border: 3px dashed #099a9f;
  }
}
.visualCaptcha-refresh-button {
  padding: 9px 10px 7px;
  margin: 0 5px;
  background-color: #f87056;
  border: 1px solid #f87056;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
}
.visualCaptcha-refresh-button img {
  width: 16px;
  height: 16px;
}
.visualCaptcha-refresh-button:hover {
  cursor: pointer;
}
.visualCaptcha-accessibility-button {
  padding: 9px 10px 7px;
  border: 1px solid black;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  border-radius: 100px;
}
.visualCaptcha-accessibility-button img {
  width: 16px;
  height: 16px;
}
.visualCaptcha-accessibility-button:hover {
  cursor: pointer;
}
/* Animations for hiding/showing accessibility option and images */
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
div.visualCaptcha .visualCaptcha-show {
  -webkit-animation: fadeIn 300ms;
  animation: fadeIn 300ms;
}
div.visualCaptcha .visualCaptcha-hide {
  display: none !important;
  -webkit-animation: fadeOut 300ms;
  animation: fadeOut 300ms;
}
