/*
 * jQuery Mobile Framework : plugin to provide a simple Dialog widget.
 * Copyright (c) JTSage
 * CC 3.0 Attribution.  May be relicensed without permission/notifcation.
 * https://github.com/jtsage/jquery-mobile-simpledialog
 */

/* Shared Styles */

.ui-simpledialog-header h4 { margin-top: 5px; margin-bottom: 5px; text-align: center; }
.ui-simpledialog-container { border: 5px solid #111 !important; width:85%; max-width:500px;}
.ui-simpledialog-screen { position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; }
.ui-simpledialog-hidden { display: none; }
.ui-simpledialog-input { width: 85% !important; display: block !important; margin-left: auto; margin-right: auto;}
.ui-simpledialog-screen-modal { background-color: black; -moz-opacity: 0.8; opacity:.80; filter: alpha(opacity=80); }
.ui-simpledialog-subtitle { text-align: center; }
.ui-simpledialog-controls .buttons-separator {min-height: .6em;}
.ui-simpledialog-controls .button-hidden { display:none; }

.ui-dialog .ui-simpledialog-container { border: none !important; }
.ui-dialog-simpledialog .ui-content { padding: 5px !important;}
