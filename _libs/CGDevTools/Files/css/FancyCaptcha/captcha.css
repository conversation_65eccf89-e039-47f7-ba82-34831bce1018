/*img { behavior: url(captcha/iepngfix.htc); }


input, select {
    background: url("imgs/input_bg.gif") repeat-x scroll left top transparent;
	border: 1px solid #ADB8C1;
    color: #ADB8C1;
    display: block;
    font-family: Arial;
    height: 32px;
    margin: 3px 0 1px;
    padding: 7px 10px;
	width: 316px;
}

input#submit {
    border: 0 none;
    cursor: pointer;
    display: block;
    height: 32px;
    margin-top: 25px;
    width: 117px;
}
*/

/* FANCY CAPTCHA STARTS */
.ajax-fc-container { width: 316px; background:#a8d7ea;  color:#fff;  margin:15px 0 0; }

/* rounded corners */
.ajax-fc-rtop, .ajax-fc-rbottom{ display:block; background: white; }
.ajax-fc-rtop *, .ajax-fc-rbottom *{ display: block; height: 1px; overflow: hidden; background:#a8d7ea; }

/* describes left and right boxes */
div#ajax-fc-content { position: absolute; margin-top: -3px; margin-left: 9px; width: 298px; height: 105px; background: url(imgs/bg-content-div.png); }
div#ajax-fc-content div#ajax-fc-left { width: 200px; height: 100px; float: left; }

div#ajax-fc-content div#ajax-fc-right { width: 98px; height: 100px; float: right; }

/* block left: task and pictures */
p#ajax-fc-task { line-height: 16px; margin: 0; padding: 11px 0 0 11px; font-size: 10px; font-family: Verdana, Tahoma, Arial; color: #18779d; }
p#ajax-fc-task span { color: #66a70b; font-weight: bold; }

ul#ajax-fc-task { width: 190px; height: 18px; list-style-type: none; background: url(imgs/bg-items.png) -4px 0px; background-repeat: no-repeat; margin: 6px 0 0 0; padding: 5px 0 0 2px; }

/* block right: cycle and backlink */
ul#ajax-fc-task li { float: left; margin-right: 1px; margin-top: 6px;}
p#ajax-fc-circle { position: absolute; margin: 18px 0 0 15px; width: 79px; height: 79px; background: url(imgs/circle.png); background-repeat: no-repeat; }
img#ajax-fc-backlink { position: absolute; margin-top: 1px; margin-left: 6px; border: 0px; z-index: 101; }

/* left and right png borders */
div#ajax-fc-corner-spacer { height: 99px; }
img.ajax-fc-border { position: absolute; margin-top: -3px; }
img.ajax-fc-border#ajax-fc-left { margin-left: 2px; }
img.ajax-fc-border#ajax-fc-right { margin-left: 307px; }

/* items */
.ajax-fc-highlighted { cursor: pointer; z-index: 102}

/* rounded corners */
.ajax-fc-r1{ margin: 0 5px } .ajax-fc-r2{ margin: 0 3px } .ajax-fc-r3{ margin: 0 2px } .ajax-fc-r4{ margin: 0 1px; height: 2px }

/* FANCY CAPTCHA ENDS */