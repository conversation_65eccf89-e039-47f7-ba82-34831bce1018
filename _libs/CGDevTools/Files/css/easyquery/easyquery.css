.eqjs-qp-addrow a, .eqjs-qc-addrow a{
    margin: 0 10px;
    display: inline-block;
    *display:inline;
}

.eqjs-qp-addrow-empty, .eqjs-qc-addrow-empty{
    padding: 5px 0 0 0;
}

.eqjs-qp-addrow a, .eqjs-qc-addrow a{
    font-weight: bold;
    font-size: 13px;
    color: #333333;
    text-decoration: none;
    border-bottom: 1px dashed;
}

.eqjs-qp-addrow > a:hover, .eqjs-qc-addrow > a:hover{
    border-bottom: 1px solid;
}

.eqjs-qc-header {
    font: normal 15px Calibri;
    border-bottom: 1px solid silver;
    padding: 0 0 2px 0;
    overflow: hidden !IE7;
}

.eqjs-qc-header div {
    font: normal 15px Calibri;
    display: inline-block;
    *display: inline;
    float: left !IE7;
}

.eqjs-qc-header-expression{
    margin-left: 38px;
    min-width: 300px;
    width:expression(($.browser.msie  && parseInt($.browser.version, 10) === 7)? "300px": "auto" );  /* IE7 */
}

.eqjs-qp-row {
    padding: 0px 0 0px 10px;
    height: 27px;
    position:relative;
}

.eqjs-qp-level-offset {
    display: inline-block;
    *display: inline;
    width: 20px;
    *float: left;
}

.eqjs-qc-row{
    height: 27px;
    cursor: pointer;
    position:relative;
}

.eqjs-qp-row:hover, .eqjs-qc-row:hover{
    background: #F1F1F1;
}

.eqjs-qp-row.active, .eqjs-qc-row.active{
    background: #F1F1F1;
}

.eqjs-qp-row-predicate {
    margin-left:0px;
}

.eqjs-qp-row-predicate-root{
    font-weight: bold;    
    padding: 0 0 0 10px;
}

.eqjs-qp-row-predicate-root .eqjs-qp-predvalueelement a{    
    font-weight: bold;
}

.eqjs-qp-predicate-root > .eqjs-qp-conditions{
    max-height: 131px;
    overflow-y: auto;
    padding: 0 0 6px 0;
}

.eq-js-dialog .eqjs-qp-conditions{
    width: 100%;
}

.eqjs-qp-condelement, .eqjs-qc-colelement {
    display : inline-block;
    *display:inline;
    margin: 3px 8px 0 0;
    vertical-align: top;
    float: left !IE7;
    font-size: 1.1em;
    font-weight: normal;
}

.eqjs-qc-expr-block{
    display:inline-block;
    *display:inline;
    min-width:300px;
    float: left !IE7;
    width: 300px !IE7;
}

.eqjs-qc-captionelement{
    float: left !IE7;    
}
    
.eqjs-qp-predelement {
    display : inline-block;
    display: inline !IE7;
    float: left !IE7;
    vertical-align: top;
    margin: 3px 8px 0 0;
    font-size: 1.1em;
}

.eqjs-qp-condelement > a, .eqjs-qp-predelement > a, .eqjs-qc-colelement > a {
    border-bottom: 1px dotted #AEAEAE;
    color: #0099CC;
    display: inline-block;
    *display:inline;
    overflow: hidden;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.eqjs-qp-condelement > a:hover, .eqjs-qp-predelement > a:hover, .eqjs-qc-colelement > a:hover {
    border-bottom: 1px solid #aeaeae;
}

.eqjs-qp-attrelement > a {
    max-width: 250px;
}

.eqjs-qp-operelement > a {
    max-width: 180px;
}

.eqjs-qp-valueelement a{
    color: #074B7A;
    display: inline-block;
    max-width: 200px;
    vertical-align: top;
    *display:inline;
}

.eqjs-qc-attrelement > a {
    max-width: 285px;
}

.eqjs-qc-captionelement > a {
    max-width: 285px;
}

.eqjs-qp-disabled .eqjs-qp-valueelement span, .eqjs-qp-readonly .eqjs-qp-valueelement span{
    max-width: 167px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    *display:inline;
}

.eqjs-qp-valueelement input[type="text"]{    
    padding: 1px 5px 0;    
    outline: none;    
}

.eqjs-qp-disabled span, .eqjs-qc-disabled span  {
    color: #9E9E9E;
    font-weight: normal;
    margin-bottom: 2px;
}

.eqjs-qp-readonly span {
    color: #333333;
    font: 15px Calibri;
}

.eqjs-qp-error {
    color: red;
    font-weight:bold;
}

.eqjs-qp-ve-loader {
    background: url('images/ve-loader.gif') no-repeat;
    width:16px;
    height:11px;
}

/* dialog */
.ui-dialog{
    overflow: visible !important;    
}

.eqjs-qp-ve-subquery{
    overflow: visible !important;
}

.eqjs-qp-condition-buttonsBlock{
    position: absolute;
    right: 0;
    top: 0;
    width: 120px;
}

.eqjs-qc-column-buttonsBlock{
    margin-top: -25px;
    max-width: 90px;
    position: absolute;
    right: 3px;
    top: auto;
    width: auto;
}

.eqjs-qp-condition-button, .eqjs-qc-column-button {
    float: left;
    margin: 2px 0 2px 6px;
    cursor: pointer;
    width: 23px;
    height: 22px;
}

.eqjs-qp-condition-button:hover, .eqjs-qc-column-button:hover{
    background-position: -25px 0;
}

.eqjs-qp-condition-button-enable{
    background: url('images/condbtn-enabled.png') no-repeat;
    background-position: 0 0;
}

.eqjs-qp-condition-button-enable.enabled{
    background: url('images/condbtn-enabled.png') no-repeat;
    background-position: -51px 0;
}

.eqjs-qp-condition-button-enable.enabled:hover{
    background-position: -76px 0;
}

.eqjs-qp-condition-button-delete, .eqjs-qc-column-button-delete{
    background: url('images/condbtn-delete.png') no-repeat;
}

.eqjs-qp-condition-button-addPredicate{
    background: url('images/condbtn-addpredicate.png') no-repeat;
}

.eqjs-qp-condition-button-addCondition{
    background: url('images/condbtn-addcondition.png') no-repeat;
    background-position: 0 0;
}

.eqjs-qp-condition-checkbox{
    width: 16px;
    height: 16px;
    background: url('images/checkbox_unchecked.png') no-repeat;
}

.eqjs-qp-condition-checkbox.enabled{
    width: 16px;
    height: 16px;
    background: url('images/checkbox_checked.png') no-repeat;
}


.eqjs-qc-column-button-type{
    background: url('images/colbtn-aggrfunc.png') no-repeat;
}

.eqjs-qc-column-button-type.aggregated:hover{
    background-position: -25px 0;
}

.eqjs-qc-sortbutton{
    cursor: pointer;
    width: 23px;
    height: 22px;
    float: left;
    margin: 2px 8px 0px !important;
}

.eqjs-qc-sortbutton:hover{
    background-position: -25px 0;
}

.eqjs-qc-sortbutton-none{
    background: url('images/sorting_none.png') no-repeat;
}

.eqjs-qc-sortbutton-asc{
    background: url('images/sorting_asc.png') no-repeat;
    background-position: -51px 0;
}

.eqjs-qc-row.active .eqjs-qc-sortbutton-asc{
    background: url('images/sorting_asc.png') no-repeat;
    background-position: 0 0;
}

.eqjs-qc-row.active .eqjs-qc-sortbutton-asc:hover{
    background-position: -25px 0;
}

.eqjs-qc-sortbutton-desc{
    background: url('images/sorting_desc.png') no-repeat;
    background-position: -51px 0;
}

.eqjs-qc-row.active .eqjs-qc-sortbutton-desc{
    background: url('images/sorting_desc.png') no-repeat;
    background-position: 0 0;
}

.eqjs-qc-row.active .eqjs-qc-sortbutton-desc:hover{
    background-position: -25px 0;
}



/*.eqjs-ep-node-block*/
.eqjs-ep-entity-block{
    display: block;
    color: black;
    font: normal 12px Arial;
    margin: 0 10px;
}

.eqjs-ep-entity-node{
    border-bottom: 1px solid #E4E4E4;
    padding: 5px 0;
}

/*.eqjs-ep-node-label*/
.eqjs-ep-entity-node-label{
    display: inline-block;
/*    *display:inline; */
    cursor: pointer;    
	font-weight: bold;
    white-space:nowrap;
}

.eqjs-ep-entity-node-label input{
    margin: 0 5px 0 0;
}

.eqjs-ep-entity-block input{
    margin: 0 5px;
}

.eqjs-ep-entity-offset{
    display: inline-block;
/*    *display:inline; */
    width: 15px;
    *float:left;
}

.ui-draggable-dragging{
    z-index: 100;
}

.eqjs-ep-entity-attr-label{    
    padding: 5px 0px 5px 5px;
    display: inline-block;
    cursor: move;
    white-space:nowrap;
}

/*
.eqjs-ep-entity-attr-label:hover{
    box-shadow: 0px 0px 20px 0px silver;
    -webkit-box-shadow: 0px 0px 20px 0px silver;
    -moz-box-shadow: 0px 0px 20px 0px silver;
    padding: 9px 0px 9px 20px;
    z-index: 1;
    margin: -2px 0 2px 10px;
}*/

/* root entity attribute class */
.eqjs-ep-entity-attr-root{
    position: relative;
}

.eqjs-ep-entity-attr-label input{
    margin: 0 5px 0 0;
}

/*.eqjs-ep-node-button*/
.eqjs-ep-entity-node-button{
    background: url('images/node-close.png') no-repeat center;
    display:inline-block;
/*    *display:inline; */
    padding: 11px 10px 7px;
    padding: 1px 5px 4px !IE7;
    margin: 0 0 0 5px !IE7;
}

.eqjs-ep-entity-node-button-open{
    background: url('images/node-open.png') no-repeat center;
}

.eqjs-ep-tool-panel{
    margin: 8px 0;
}

.eqjs-ep-tool-panel div.eqjs-ep-tool-panel-left-side{
    float: left;
    width: 50%;
}

.eqjs-ep-tool-panel div.eqjs-ep-tool-panel-right-side{
    float: right;
    width: 50%;
}

.eqjs-ep-tool-panel div div{
    float: left;
    width: 24px;
    height: 24px;
    cursor: pointer;
    margin: 0 10%;
}

.eqjs-ep-tool-panel-select-all{
    background: url('images/eqjs-ep-select-all.png') no-repeat;
}

.eqjs-ep-tool-panel-select-all:hover{
    background: url('images/eqjs-ep-select-all-hover.png') no-repeat;
}

.eqjs-ep-tool-panel-deselect-all{
    background: url('images/eqjs-ep-deselect-all.png') no-repeat;
}

.eqjs-ep-tool-panel-deselect-all:hover{
    background: url('images/eqjs-ep-deselect-all-hover.png') no-repeat;
}
.eqjs-ep-tool-panel-add-columns{
    background: url('images/eqjs-ep-add-columns.png') no-repeat;
}

.eqjs-ep-tool-panel-add-columns:hover{
    background: url('images/eqjs-ep-add-columns-hover.png') no-repeat;
}

.eqjs-ep-tool-panel-add-cond{
    background: url('images/eqjs-ep-add-cond.png') no-repeat;
}

.eqjs-ep-tool-panel-add-cond:hover{
    background: url('images/eqjs-ep-add-cond-hover.png') no-repeat;
}

.eqjs-highlight{
    height: 20px;
}

.eqjs-drophover {
    border: 1px dotted black;
}

.eqjs-ep-entity > .eqjs-ep-entity-children{
    float: left;
    width: 100%;
}

.eqjs-ep-entity{
    clear: both;    
}


/**/
.eqjs-menu-rootLevel{
    background-color: #888;
}

.eqjs-menu-levelDiv{
    background: #fff;
    border: 1px solid silver;
    margin: -2px 2px 2px -2px;
    padding: 5px 0;
    position: relative;
}

.eqjs-menu-searchDiv{
    border-bottom: 1px solid silver;
    background-color: white;
    cursor: pointer;
    padding: 5px 10px;
}

.eqjs-menu-searchDiv input{
    width: 93%;
    padding: 2px 5px;
    font-weight: normal;    
}

.eqjs-menu-scrollDiv{
    min-width: 140px;
    white-space: nowrap;
    overflow: auto;
}

.eqjs-menu-itemDiv input{
    margin: 4px 10px 0 0;
    vertical-align: top;
}

.eqjs-menu-itemDiv{
    padding: 5px 20px 5px 10px;
    cursor: pointer;
}

.eqjs-menu-itemDiv-hasChildren{
    text-align:right;
}

.eqjs-menu-itemDiv:hover{
    background: #f1f1f1;
}

.eqjs-menu-itemDiv-text{
    float: left;
}

.eqjs-menu-scrollDiv .active{
    background: #f1f1f1;
}

.eqjs-menu-applyDiv{
    padding: 5px;
    border-bottom: 1px solid silver;
    margin: 0 0 5px 0;
}

.eqjs-menu-applyDiv button{
    padding: 0 5px;
    cursor: pointer;
}

.eqjs-menu-cancel{
    margin: 0 0 0 15px;
}

/* dialog */
.eq-js-dialog-overlay{
    background: #fff;
    opacity: 0.75;
    position: fixed;
}

.eq-js-dialog{
    -webkit-box-shadow: 0 4px 16px #DDD;
    -moz-box-shadow: 0 4px 16px #DDD;
    box-shadow: 0 4px 16px #DDD;
    background: #fff;
    background-clip: padding-box;
    border: 1px solid #ACACAC;
    border-radius: 0;
    outline: none;
}

.eq-js-dialog .ui-widget-header{
    border: 0;
    background: #fff;
}

.eq-js-dialog .ui-dialog-title{
    font: bold 14px Segoe ui;
}

.eq-js-dialog p{
    font: normal 14px Segoe ui;
}

.eq-js-dialog .ui-icon-closethick{
    text-indent: 0;
    background: none;
}

.eq-js-dialog .ui-dialog-titlebar-close{
    background: none;
    border: 0;
    text-decoration: none;
    padding: 0;
    color: #888;
    font: normal 13px Tahoma;
    cursor: pointer;
}

.ui-button-icon-primary{
    display: none !important;
}

.eq-js-dialog .ui-dialog-titlebar-close:hover{
    color: #222;
}

.eq-js-dialog .eqjs-qp-ve-subquery-qp{
    
}

.eq-js-dialog .eqjs-qp-ve-subquery-column{
    border-bottom: 1px solid #ACACAC;
}

.eq-js-dialog .eqjs-qp-ve-subquery-column-element{
    margin: 0 10px;
    display: inline;
}

.eq-js-dialog .eqjs-qp-ve-subquery-qp{
    border: 1px solid #ACACAC;
    padding: 5px;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 180px;
    min-height: 180px;
}

.eq-js-dialog .eqjs-qp-ve-subquery-qp .eqjs-qp-ve-editbox{
    min-height: 16px;
}

.eq-js-dialog .eqjs-qp-valueelement{
    max-width: 200px;
}

.eq-js-dialog .eqjs-qp-ve-subquery-column-title, .ui-dialog .eqjs-qp-ve-subquery-qp-caption{
    font-size: 1.1em;
    color: #4F4F4F;
    display: inline;
    margin: 0 5px;
}

.eq-js-dialog .eqjs-qp-ve-subquery-column-element a{
    color: #4676AE;
    text-decoration: none;
    /*font: normal 15px Calibri;*/
    outline: none;
}

.eq-js-dialog .eqjs-qp-ve-subquery-column-element a:hover{
    text-decoration: underline;
}

.eq-js-dialog .eqjs-qp-ve-subquery-qp-caption{
    display: block;
    margin: 20px 0 0 5px;
}

.eq-js-dialog .ui-dialog-buttonpane{
    background: none;
    border: 0;
    text-align: center;
}

.eq-js-dialog .ui-dialog-buttonset{
    float: none !important;
}

.eq-js-dialog .ui-dialog-buttonset .disabled, .eq-js-dialog .ui-dialog-buttonset .disabled:hover, .eq-js-dialog .ui-dialog-buttonset .disabled:active{
    opacity: 0.2;
    color: black;
    box-shadow: none !important;
    cursor: default !important;
}
.eq-js-dialog .ui-dialog-buttonset .disabled:active{
    border: 1px solid whiteSmoke !important;
}

.eq-js-dialog .ui-dialog-buttonset button, .eq-js-dialog .ui-dialog-buttonset .ui-button-disabled:hover{
    color: #222;
    padding: 5px 30px !important;
    font: bold 11px Lucida Grande, Lucida Sans, Arial, sans-serif;
    outline: none;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    background-color: #f5f5f5;
    background-image: -webkit-linear-gradient(top,#f5f5f5,#f1f1f1);
    background-image: -moz-linear-gradient(top,#f5f5f5,#f1f1f1);
    background-image: -ms-linear-gradient(top,#f5f5f5,#f1f1f1);
    background-image: -o-linear-gradient(top,#f5f5f5,#f1f1f1);
    background-image: linear-gradient(top,#f5f5f5,#f1f1f1);
    border: 1px solid #dcdcdc;
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: none;
}

.eq-js-dialog .ui-dialog-buttonset button:hover{
    box-shadow: 0px 0px 3px 0px #BEBEBE;
}

.eq-js-dialog .ui-dialog-buttonset button:active{
    box-shadow: inset 0px 0px 3px 0px #BEBEBE;
    border: 1px solid #B1ABAB;
}

.eqjs-qc-columns{
    max-height: 85px;
    max-height: 80px !IE7;
    overflow-y: auto;
    padding: 4px 0;
}

.eqjs-qc-column-drag{
    opacity: 0.7;
    cursor: n-resize !important;
    background: #F1F1F1;
    width: 664px;
}

.eqjs-qc-column-drag-forbidden{
    background: #F7819F;
}

.eqjs-qc-column-sort{
    opacity: 0.7;
    cursor: n-resize !important;
    background: #E8F3FF;
    padding: 0 20px !important;
}
