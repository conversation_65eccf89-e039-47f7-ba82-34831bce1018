/*

This stylesheet provides some sample styles to use with Freeow! You may use
these in your project or create your own!

For my samples, I'm assuming that you will call freeow() on a container with
the class .freeow applied to it.

The default template function produces markup matching this outline:

	div (classes passed in options get applied here)
		div.background
			div.content
				h2
				p
		span.icon
		span.close

Don't forget: You can make your own template function if this markup doesn't work for your project. The function must accept the title and message as arguments and return a DOM element (the message box).

Update $.fn.freeow.defaults.template or pass the function as the template
member of the options object when calling freeow().

*/


/* Boilerplate -------------------------------------------------------------- */

/* Outermost Container */
.freeow {
    position: fixed;
    width: 300px;
    z-index: 9999;
}
.freeow-top-right {
    top: 10px;
    right: 10px;
}
.freeow-bottom-right {
    bottom: 10px;
    right: 10px;
}

.freeow-top-left {
    top: 10px;
    left: 10px;
}
.freeow-bottom-left {
    bottom: 10px;
    left: 10px;
}

/* Message */
.freeow > div {
    position: relative;
    margin-bottom: 5px;
	cursor: pointer;
}
.freeow .content {
    margin: 5px 5px 5px 69px;
}
.freeow h2,
.freeow p {
    margin: 0;
    padding: 0;

}
.freeow .icon {
    position: absolute;
    display: block;
    width: 48px;
    height: 48px;
    top: 5px;
    left: 10px;
    background: transparent url(images/notice.png) no-repeat 0 0;
    z-index: 1;
}
.freeow .close {
    position: absolute;
    display: block;
    width: 24px;
    height: 24px;
    top: 8px;
    left: 8px;
    background: none;
    z-index: 2;
}
.freeow div:hover .close {
    background: transparent url(images/close.png) no-repeat 0 0;
}


/* Icons -------------------------------------------------------------------- */

.freeow .slide .icon   { background-image: url(images/slide.png); }
.freeow .pushpin .icon { background-image: url(images/pushpin.png); }
.freeow .error .icon   { background-image: url(images/error.png); }


/* Specific Styles ---------------------------------------------------------- */

/* Smokey */
.freeow .smokey {
	color: white;
}
.freeow .smokey .background {
    border: 3px solid #000;
    -moz-border-radius: 12px;
	border-radius: 12px;
    background: #000;
    opacity: .65;
    -moz-box-shadow: 2px 2px 3px #888;
    -webkit-box-shadow: 2px 2px 3px #888;
    box-shadow: 2px 2px 3px #888;
}
.freeow .smokey:hover .background {
    border-color: #fff;
}
.freeow .smokey .content {
    margin: 5px 5px 5px 69px;
}
.freeow .smokey h2 {
    font-family: "Lucida Grande", Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}
.freeow .smokey p {
    padding-top: 8px;
    font-family: Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 16px;
}

/* Gray */
.freeow .gray {
	color: black;
}
.freeow .gray .background {
	border: 3px solid #eee;
    -moz-border-radius: 12px;
	border-radius: 12px;
    background: #eee;
    -moz-box-shadow: 2px 2px 3px #888;
    -webkit-box-shadow: 2px 2px 3px #888;
    box-shadow: 2px 2px 3px #888;
}
.freeow .gray:hover .background {
    border-color: #009bc5;
}
.freeow .gray .content {
    margin: 5px 5px 5px 69px;
}
.freeow .gray h2 {
    font-family: "Lucida Grande", Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}
.freeow .gray p {
    padding-top: 8px;
    font-family: Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 16px;
}

/* OSX */
.freeow .osx .background {
	border: 1px solid #939393;
	background: #eee url(images/osx/background.png) repeat-y top left;
    -moz-box-shadow: 2px 2px 3px #888;
    -webkit-box-shadow: 2px 2px 3px #888;
    box-shadow: 2px 2px 3px #888;
}
.freeow .osx .content {
    margin: 10px 5px 15px 87px;
}
.freeow .osx .icon {
	left: 28px;
}
.freeow .osx .close {
    position: absolute;
    display: block;
    width: 16px;
    height: 16px;
    top: 1px;
    left: 1px;
    background: transparent url(images/osx/close.png) no-repeat 0 0;
    z-index: 2;
}
.freeow .osx:hover .close {
    background: transparent url(images/osx/close.png) no-repeat -16px 0;
}
.freeow .osx h2 {
    font-family: "Lucida Grande", Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
}
.freeow .osx p {
    font-family: Helvetica, arial, sans-serif;
    font-weight: normal;
    font-size: 11px;
    line-height: 16px;
}

/* Simple */
.freeow .simple .background {
	border: 2px solid #ccc;
	background: #eee;
}
.freeow .simple .content {
    margin: 10px 5px 10px 69px;
}
