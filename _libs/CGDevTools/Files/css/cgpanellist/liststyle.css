/**
* CSS styles for CGDevTools IWCGPanelList component.
* Author: <PERSON>
* Copyright (c) 2013 <PERSON>
**/

/* COMMON CLASSES */

.cgdev-pl-header{
	margin: 0;
	cursor: default;
	position: relative;
	 padding: 0.5em 0.5em 0.5em 0.7em;
}

.cgdev-pl-header .ui-icon{
    left: 0.5em;
    margin-top: -8px;
    position: absolute;
    top: 50%;
}

.cgdev-pl-header a{
	display: block;
}

.cgdev-pl-span-container{
	display: table;
	width: 100%;
}

.cgdev-pl-span-text{
	display: table-cell;
	vertical-align: middle;		
}

.cgdev-founded-item{
	font-weight: bold !important;
	background: none repeat scroll 0 0 rgb(135, 191, 213) !important;
	color: white !important;
}

/* DEFAULT STYLE */
.list-default ul{
  list-style: none outside none;
  margin: 0;
  padding: 0;	
	font-size: 0.9em !important;
  border: 0;
}

.list-default ul li.panellist-nav-item-li{	
    font-weight:normal !important;	
	padding-left: 10px;	
	text-decoration: none;	
    border-width: 0 0 1px;
	display: block;
	padding: 4px 8px;
	cursor: auto;
	position: relative;
	background-image: none !important;
    
    border-width: 0 0 1px;
    border-bottom-style: dotted;
    border-bottom-color: #b3b3b3;
    
	-moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    user-select: none;			
}

.list-default ul li.panellist-nav-item-li > a{
    text-decoration: none;
}

.list-default ul li.panellist-nav-item-li-subheader{
	border: 0px !important;	
	font-weight: bold !important;
}

/* STYLE 1*/
.list-style1 ul{
	list-style: none outside none;
    margin: 0;
    overflow-x: hidden;
    overflow-y: hidden;
    padding: 0;    
    position: relative;
}

.list-style1 ul li.panellist-nav-item-li:nth-child(2n+1) {
    background: none repeat scroll 0 0 #FFFFFF;
}

.list-style1 ul li.panellist-nav-item-li{	
    background: none repeat scroll 0 0 #EEEEEE;
    border-bottom: 0 none;
    color: #252525;
    padding: 16px;
    position: relative;    
    z-index: 2;		
}

.list-style1 ul li.panellist-nav-item-li-subheader{
	border: 0px !important;	
	font-weight: bold !important;
}

.list-style1 ul li.panellist-nav-li-hover{
	border: 0px !important;
	font-weight: bold !important;
}

/* STYLE 2*/
.list-style2 ul{
    list-style: none outside none;
    padding: 0;
}

.list-style2 ul li.panellist-nav-item-li{	
    background: linear-gradient(#FFFFFF, #F1F1F1) repeat scroll 0 0 #EEEEEE;
    border: 1px solid #CCCCCC;
    color: #222222;    
    text-shadow: 0 1px 0 #FFFFFF;
	position: relative;
	padding: 16px;
	border-width: 1px 0 0;	
}

.list-style2 ul li.panellist-nav-item-li-subheader{
	border: 0px !important;	
	font-weight: bold !important;
}

.list-style2 ul li.panellist-nav-li-hover{
	font-weight: normal !important;
    background: linear-gradient(#F6F6F6, #E0E0E0) repeat scroll 0 0 #DFDFDF;    
    color: #222222;    
    text-shadow: 0 1px 0 #FFFFFF;	
}