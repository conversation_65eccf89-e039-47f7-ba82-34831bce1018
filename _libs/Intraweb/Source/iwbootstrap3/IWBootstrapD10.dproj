﻿	<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
		<PropertyGroup>
			<ProjectGuid>{CD83096B-2583-4462-B79D-EE0FF82291E6}</ProjectGuid>
			<MainSource>IWBootstrapD10.dpk</MainSource>
			<Base>True</Base>
			<Config Condition="'$(Config)'==''">Debug</Config>
			<TargetedPlatforms>1</TargetedPlatforms>
			<AppType>Package</AppType>
			<FrameworkType>VCL</FrameworkType>
			<ProjectVersion>15.3</ProjectVersion>
			<Platform Condition="'$(Platform)'==''">Win32</Platform>
			<DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
			<Cfg_1>true</Cfg_1>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
			<Cfg_2>true</Cfg_2>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base)'!=''">
			<DCC_OutputNeverBuildDcps>true</DCC_OutputNeverBuildDcps>
			<DesignOnlyPackage>false</DesignOnlyPackage>
			<DCC_DependencyCheckOutputName>C:\Users\<USER>\Documents\RAD Studio\7.0\Bpl\IWBootstrapD10.bpl</DCC_DependencyCheckOutputName>
			<RuntimeOnlyPackage>true</RuntimeOnlyPackage>
			<DCC_Description>IntraWeb Bootstrap 3 Framework</DCC_Description>
			<VerInfo_Locale>11274</VerInfo_Locale>
			<DCC_N>false</DCC_N>
			<GenPackage>true</GenPackage>
			<DCC_F>false</DCC_F>
			<DCC_K>false</DCC_K>
			<DCC_E>false</DCC_E>
			<DCC_S>false</DCC_S>
			<VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=;CFBundleName=;CFBundleDisplayName=;UIDeviceFamily=;CFBundleIdentifier=;CFBundleVersion=;CFBundlePackageType=;CFBundleSignature=;CFBundleAllowMixedLocalizations=;UISupportedInterfaceOrientations=;CFBundleExecutable=;CFBundleResourceSpecification=;LSRequiresIPhoneOS=;CFBundleInfoDictionaryVersion=;CFBundleDevelopmentRegion=;package=;label=;versionCode=;versionName=;persistent=;restoreAnyVersion=;installLocation=;largeHeap=;theme=</VerInfo_Keys>
			<DCC_ImageBase>00400000</DCC_ImageBase>
			<GenDll>true</GenDll>
			<DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;$(DCC_Namespace)</DCC_Namespace>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_1)'!=''">
			<DCC_DebugInformation>0</DCC_DebugInformation>
			<DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
			<DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
			<DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_2)'!=''">
			<DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
			<DCC_Optimize>false</DCC_Optimize>
			<DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
		</PropertyGroup>
		<ItemGroup>
			<DelphiCompile Include="IWBootstrapD10.dpk">
				<MainSource>MainSource</MainSource>
			</DelphiCompile>
			<BuildConfiguration Include="Base">
				<Key>Base</Key>
			</BuildConfiguration>
			<BuildConfiguration Include="Debug">
				<Key>Cfg_2</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
			<BuildConfiguration Include="Release">
				<Key>Cfg_1</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
		</ItemGroup>
		<Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
		<ProjectExtensions>
			<Borland.Personality>Delphi.Personality.12</Borland.Personality>
			<Borland.ProjectType>Package</Borland.ProjectType>
			<BorlandProject>
				<Delphi.Personality>
					<Source>
						<Source Name="MainSource">IWBootstrapD10.dpk</Source>
					</Source>
					<VersionInfo>
						<VersionInfo Name="IncludeVerInfo">True</VersionInfo>
						<VersionInfo Name="AutoIncBuild">False</VersionInfo>
						<VersionInfo Name="MajorVer">1</VersionInfo>
						<VersionInfo Name="MinorVer">0</VersionInfo>
						<VersionInfo Name="Release">0</VersionInfo>
						<VersionInfo Name="Build">0</VersionInfo>
						<VersionInfo Name="Debug">False</VersionInfo>
						<VersionInfo Name="PreRelease">False</VersionInfo>
						<VersionInfo Name="Special">False</VersionInfo>
						<VersionInfo Name="Private">False</VersionInfo>
						<VersionInfo Name="DLL">False</VersionInfo>
						<VersionInfo Name="Locale">11274</VersionInfo>
						<VersionInfo Name="CodePage">1252</VersionInfo>
					</VersionInfo>
					<VersionInfoKeys>
						<VersionInfoKeys Name="CompanyName"/>
						<VersionInfoKeys Name="FileDescription"/>
						<VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="InternalName"/>
						<VersionInfoKeys Name="LegalCopyright"/>
						<VersionInfoKeys Name="LegalTrademarks"/>
						<VersionInfoKeys Name="OriginalFilename"/>
						<VersionInfoKeys Name="ProductName"/>
						<VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="Comments"/>
						<VersionInfoKeys Name="CFBundleName"/>
						<VersionInfoKeys Name="CFBundleDisplayName"/>
						<VersionInfoKeys Name="UIDeviceFamily"/>
						<VersionInfoKeys Name="CFBundleIdentifier"/>
						<VersionInfoKeys Name="CFBundleVersion"/>
						<VersionInfoKeys Name="CFBundlePackageType"/>
						<VersionInfoKeys Name="CFBundleSignature"/>
						<VersionInfoKeys Name="CFBundleAllowMixedLocalizations"/>
						<VersionInfoKeys Name="UISupportedInterfaceOrientations"/>
						<VersionInfoKeys Name="CFBundleExecutable"/>
						<VersionInfoKeys Name="CFBundleResourceSpecification"/>
						<VersionInfoKeys Name="LSRequiresIPhoneOS"/>
						<VersionInfoKeys Name="CFBundleInfoDictionaryVersion"/>
						<VersionInfoKeys Name="CFBundleDevelopmentRegion"/>
						<VersionInfoKeys Name="package"/>
						<VersionInfoKeys Name="label"/>
						<VersionInfoKeys Name="versionCode"/>
						<VersionInfoKeys Name="versionName"/>
						<VersionInfoKeys Name="persistent"/>
						<VersionInfoKeys Name="restoreAnyVersion"/>
						<VersionInfoKeys Name="installLocation"/>
						<VersionInfoKeys Name="largeHeap"/>
						<VersionInfoKeys Name="theme"/>
					</VersionInfoKeys>
					<Parameters>
						<Parameters Name="UseLauncher">False</Parameters>
						<Parameters Name="LoadAllSymbols">True</Parameters>
						<Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
					</Parameters>
				</Delphi.Personality>
				<Platforms>
					<Platform value="Win32">True</Platform>
					<Platform value="Win64">False</Platform>
				</Platforms>
			</BorlandProject>
			<ProjectFileVersion>12</ProjectFileVersion>
		</ProjectExtensions>
	</Project>
