package IWBootstrapD101;

{$R *.res}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO ON}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DESCRIPTION 'IntraWeb Bootstrap 3 Framework'}
{$RUNONLY}
{$IMPLICITBUILD OFF}

requires
  rtl,
  vcl,
  vclimg,
  inet,
  Intraweb_15_D10_1;

contains
  IWBSUtils in 'IWBSUtils.pas',
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in 'IWBSCommon.pas',
  IW<PERSON>Reg<PERSON> in 'IWBSRegion.pas',
  IWBSInput in 'IWBSInput.pas',
  IWBSLayoutMgr in 'IWBSLayoutMgr.pas',
  IWBSTabControl in 'IWBSTabControl.pas',
  IWBSControls in 'IWBSControls.pas',
  IWBSCustomInput in 'IWBSCustomInput.pas',
  IWBSInputCommon in 'IWBSInputCommon.pas',
  IWBSButton in 'IWBSButton.pas',
  IWBSCustomControl in 'IWBSCustomControl.pas',
  IWBSScriptEvents in 'IWBSScriptEvents.pas',
  IWBSImage in 'IWBSImage.pas',
  IWBSRestServer in 'IWBSRestServer.pas',
  IWBSCustomEvents in 'IWBSCustomEvents.pas',
  IWBSGlobal in 'IWBSGlobal.pas',
  IWBSDialogs in 'IWBSDialogs.pas',
  IWBSDropDown in 'IWBSDropDown.pas',
  IWBSJsonDataObjects in 'IWBSJsonDataObjects.pas',
  IWBSImageUtils in 'IWBSImageUtils.pas',
  IWBSMemoHtml in 'IWBSMemoHtml.pas',
  IWBSModal in 'IWBSModal.pas',
  IWBSList in 'IWBSList.pas',
  IWBSButtonGroup in 'IWBSButtonGroup.pas',
  IWBSInputForm in 'IWBSInputForm.pas',
  IWBSNavBar in 'IWBSNavBar.pas',
  IWBSCustomRegion in 'IWBSCustomRegion.pas',
  IWBSCommonInterfaces in 'IWBSCommonInterfaces.pas';

end.



