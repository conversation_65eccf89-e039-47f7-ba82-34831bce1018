.dynamic-tabs {
	width: 100%;
	float: left;
}
.dynamic-tabs-container {
	position: relative;
}
.dynamic-tabs-container .dynamic-tabs {
    padding-right: 50px;
}
.dynamic-tabs-container .tabs-dropdown {
    position: absolute;
    right: 0;
    margin-right: 0 !important;
}
.dynamic-tabs-container .tabs-dropdown.navbar-nav {
    margin: 0 !important;
}
@media only screen and (max-width: 767px) {
    .dynamic-tabs-container .tabs-dropdown .dropdown-menu {
        position: fixed;
        top: 20px;
        right: 20px;
        bottom: 20px;
        left: 20px;
        padding-top: 50px;
	}
    .dynamic-tabs-container .tabs-dropdown .dropdown-menu .dropdown-header {
        position: fixed;
        left: 21px;
        right: 21px;
        background: #FFF;
        margin-top: -50px;
        padding-top: 18px;
        border-radius: 4px 4px 0 0;
	}
    .dynamic-tabs-container .tabs-dropdown .dropdown-menu .close {
        position: absolute;
        top: 14px;
        right: 20px;
	}
    .dynamic-tabs-container .tabs-dropdown .dropdown-menu .divider {
          margin: 0;
	}
	.dynamic-tab {
		width: 100%;
	}
}
.dynamic-tabs-container .tabs-dropdown .dropdown-toggle {
    position: relative;
    display: block;
    padding: 10px 15px;
}
.dynamic-tabs-container .tabs-dropdown .dropdown-toggle .count {
    margin-right: 5px;
}
.dynamic-tabs-container .tabs-dropdown .dropdown-toggle .caret {
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 6px solid;
    margin-left: 0;
    vertical-align: initial;
}
