unit Forms.ReportGenerator;

interface

uses
  System.SysUtils, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls;

type
  TFormReportGenerator = class(TForm)
    GroupBox1: TGroupBox;
    ButtonAMABewertungsblatt: TButton;
    EditAuftrag: TLabeledEdit;
    EditBundesland: TLabeledEdit;
    EditUser: TLabeledEdit;
    GroupBox2: TGroupBox;
    BProbenbegleichtschreiben: TButton;
    BKontrollbericht: TButton;
    EditKontrollberichtsID: TEdit;
    Button1: TButton;
    procedure BKontrollberichtClick(Sender: TObject);
    procedure BProbenbegleichtschreibenClick(Sender: TObject);
    procedure Button1Click(Sender: TObject);
    procedure ButtonAMABewertungsblattClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  FormReportGenerator: TFormReportGenerator;

implementation

uses
  Modules.Reports.AMA, Modules.Reports.ELKE;

{$R *.dfm}


// ELKE
procedure TFormReportGenerator.BKontrollberichtClick(Sender: TObject);
begin
  TModuleReports.PdfKontrollberichtErzeugen(StrToIntDef(EditKontrollberichtsID.Text, 0), 300, 'Test-Benutzer');
end;

procedure TFormReportGenerator.BProbenbegleichtschreibenClick(Sender: TObject);
begin
//  TModuleReports.PdfProbenbegleitscheinErzeugen(4128, 1341, 300, 'Test-Benutzer');
  TModuleReports.PdfProbenbegleitscheinErzeugen(11551, 2487, 300, 'Test-Benutzer');
end;

procedure TFormReportGenerator.Button1Click(Sender: TObject);
begin
  TModuleReports.CC_PdfKontrollberichtErzeugen(StrToIntDef(EditKontrollberichtsID.Text, 0), 300, 'Test-Benutzer');
end;

// AMA

procedure TFormReportGenerator.ButtonAMABewertungsblattClick(Sender: TObject);
begin
  TModuleAMAReports.PdfBewertungsblattErzeugen(
    StrToInt(EditAuftrag.Text),
    1,
    StrToInt(EditBundesland.Text),
    EditUser.Text);
end;

end.
