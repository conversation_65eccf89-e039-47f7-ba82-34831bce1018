﻿unit Modules.Reports.ELKE;

interface

uses
  System.SysUtils, System.Classes, System.Variants, System.UITypes,
  VCL.Graphics, // Fonts!
  FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.MSSQL,
  FireDAC.Phys.MSSQLDef, FireDAC.VCLUI.Wait, Data.DB, FireDAC.Comp.Client,
  FireDAC.Stan.Param, FireDAC.DatS, FireDAC.DApt.Intf, FireDAC.DApt, FireDAC.Comp.DataSet,
  frxClass, frxExportBaseDialog, frxExportPDF, frxDBSet,
  ELKE.Classes.Generated, ELKE.Classes.Generated.Dictionary, ELKE.Classes,

  Aurelius.Drivers.Interfaces, Aurelius.Sql.MSSQL, Aurelius.Schema.MSSQL, Aurelius.Drivers.FireDAC,
  Aurelius.Comp.Connection, Aurelius.Engine.ObjectManager, Modules.Reports.Base,
  frCoreClasses, frxHTML;

const
  VERSION_KONTROLLBERICHT = '2.2.2021';
  VERSION_PROBENBGLEITSCHEIN = '2.2.2021';

  EMAIL_KONTROLLBERICHT_BODYx =
    'Sehr geehrte Damen und Herren!'#13#10 +
    #13#10 +
    'Anbei erhalten Sie die Unterlagen zur Kontrolle %BKB%.'#13#10 +
    'Durchgeführt am: %DATUM_ENDE%'#13#10 +
    'Betrieb: %BETRIEB_NAME% '#13#10 +
    #13#10 +
    'Mit freundlichen Grüßen'#13#10 +
    'Ihre ELKE Administration';

  EMAIL_POSTVERSAND_BODYx =
    'Sehr geehrte Damen und Herren!'#13#10 +
    #13#10 +
    'Zur folgenden Kontrolle konnte keine E-Mail Adresse einer kommunikationsberechtigten Person ermittelt werden.'#13#10
    +
    #13#10 +
    'Bitte lösen Sie den Postversand aus.'#13#10 +
    #13#10 +
    'Kontrolle: %BKB%'#13#10 +
    'Durchgeführt am: %DATUM_ENDE%'#13#10 +
    'Betrieb: %BETRIEB_NAME%'#13#10 +
    'Adresse: %BETRIEB_ADDRESSE%'#13#10 +
    #13#10 +
    'Mit freundlichen Grüßen'#13#10 +
    'Ihre ELKE Administration';

  EMAIL_KONTROLLBERICHT_BODY_BETRIEBx =
    'Sehr geehrte Damen und Herren!'#13#10 +
    #13#10 +
    'Anbei erhalten Sie die Unterlagen zur Kontrolle %BKB%.'#13#10 +
    'Durchgeführt am: %DATUM_ENDE%'#13#10 +
    'Betrieb: %BETRIEB_NAME%'#13#10 +
    #13#10 +
    'Sie haben die Möglichkeit, binnen 14 Tagen zu den Kontrollfeststellungen Stellung zu nehmen. Eine eventuelle ' +
    'Stellungnahme ist mit allfälligen Nachweisen unter Angabe des oben angeführten Aktenzeichens, der Betriebsnummer ' +
    'sowie - bei mehreren Kontrollberichten - des betroffenen Kontrollberichts und des Kontrolldatums an die ' +
    'zuständige Kontrollstelle zu richten.'#13#10 +
    #13#10 +
    'Mit freundlichen Grüßen'#13#10 +
    'Ihre ELKE Administration';

type
  EReportException = class(Exception);
  EReportTemplateNotFound = class(EReportException);
  EKeinCCBericht = class(EReportException);

  TModuleReports = class(TModuleReportBase)
    Connection: TFDConnection;
    Anwesender: TFDQuery;
    BetriebProben: TFDQuery;
    Bundesland: TFDQuery;
    frxAnwesender: TfrxDBDataset;
    frxBetriebProbe: TfrxDBDataset;
    frxBundesland: TfrxDBDataset;
    frxKbProben: TfrxDBDataset;
    frxKontrollorgan: TfrxDBDataset;
    frxProbenbegleitschreiben: TfrxReport;
    frxProbenBilder: TfrxDBDataset;
    frxUnterschriften: TfrxDBDataset;
    KbProben: TFDQuery;
    Kontrollorgan: TFDQuery;
    ProbenBilder: TFDQuery;
    quKontrollDetails: TFDQuery;
    Unterschriften: TFDQuery;
    frxKontrollbericht: TfrxReport;
    FDQueryKontrollbericht: TFDQuery;
    frxDBD_Kontrollbericht: TfrxDBDataset;
    frxDBD_Systemdaten: TfrxDBDataset;
    FDMemTableSystemdaten: TFDMemTable;
    FDQueryChecklisten: TFDQuery;
    FDQueryFragen: TFDQuery;
    frxDBD_Chechlisten: TfrxDBDataset;
    frxDBD_Fragen: TfrxDBDataset;
    ds_Checklisten: TDataSource;
    FDQueryMassnahmen: TFDQuery;
    ds_Kontrollbericht: TDataSource;
    frxDBD_Massnahmen: TfrxDBDataset;
    FDQ_KontrollberichtOertlichkeiten: TFDQuery;
    ds_KontrollberichtOertlichekiten: TfrxDBDataset;
    FDQ_MassnahmenOertlichkeiten: TFDQuery;
    frxDBMassnahmenOertlichkeiten: TfrxDBDataset;
    ds_Massnahmen: TDataSource;
    FDQ_Probenbilder: TFDQuery;
    frxDB_ProbenBilder: TfrxDBDataset;
    FDQ_FragenBilder: TFDQuery;
    frxDBD_FragenBilder: TfrxDBDataset;
    FDQ_MangelBilder: TFDQuery;
    frxDBD_MangelBilder: TfrxDBDataset;
    FDQ_CCKdaten: TFDQuery;
    frxDBD_CCKDaten: TfrxDBDataset;
    FDQ_CCKAuswahldaten: TFDQuery;
    frxDBD_cckAuswahldaten: TfrxDBDataset;
    FDQueryAnwesende: TFDQuery;
    frxDBDsAnwesende: TfrxDBDataset;
    frxKontrollbericht_CC: TfrxReport;
    FDQueryKontrollbericht_CC: TFDQuery;
    frxDBD_Kontrollbericht_CC: TfrxDBDataset;
    ds_Kontrollbericht_CC: TDataSource;
    FDQueryChecklisten_CC: TFDQuery;
    frxDBD_Chechlisten_CC: TfrxDBDataset;
    ds_Checklisten_cc: TDataSource;
    FDQueryFragen_CC: TFDQuery;
    frxDBD_Fragen_CC: TfrxDBDataset;
    FDQueryMassnahmen_CC: TFDQuery;
    frxDBD_Massnahmen_CC: TfrxDBDataset;
    ds_Massnahmen_CC: TDataSource;
    FDQ_KontrollberichtOertlichkeiten_CC: TFDQuery;
    ds_KontrollberichtOertlichekiten_CC: TfrxDBDataset;
    FDQ_CCKdaten_CC: TFDQuery;
    frxDBD_CCKDaten_CC: TfrxDBDataset;
    FDQ_CCKAuswahldaten_CC: TFDQuery;
    frxDBD_cckAuswahldaten_CC: TfrxDBDataset;
    FDQ_MassnahmenOertlichkeiten_CC: TFDQuery;
    frxDBMassnahmenOertlichkeiten_CC: TfrxDBDataset;
    FDQ_FragenBilder_CC: TFDQuery;
    frxDBD_FragenBilder_CC: TfrxDBDataset;
    FDQ_MangelBilder_CC: TFDQuery;
    frxDBD_MangelBilder_CC: TfrxDBDataset;
    FDQueryAnwesende_CC: TFDQuery;
    frxDBDsAnwesende_CC: TfrxDBDataset;
    FDQuery1: TFDQuery;
    frxDBDataset1: TfrxDBDataset;
    frxHTMLObject1: TfrxHTMLObject;
    FDQ_Behoerden: TFDQuery;
    frxDBD_Behoerden: TfrxDBDataset;
    procedure DataModuleCreate(Sender: TObject);
    procedure ConnectionBeforeConnect(Sender: TObject);
    procedure FrxProbenbegleitscheinOnBeforePrint(Sender: TfrxReportComponent);
  strict private
    class var
      FLogProc: TProc<string>;
    procedure ReportVorlageLaden(AReport: TfrxReport; AKontrolleID: integer; AReportTyp: string);

    /// <summary>
    /// Erzeugt die PDF Datei für den Kontrollbericht. Die Datei wird lokal
    /// gespeichert und der Pafd zur Datei wird zurück gegeben
    /// </summary>
    /// <param name="AKontrollberichtID">
    /// Id des Kontrollberichts
    /// </param>
    /// <param name="AChecklisteId">
    /// Id der zugehörigen Checkluste
    /// </param>
    /// <param name="ABldcode">
    /// Bundesland
    /// </param>
    /// <param name="AUser">
    /// User, der den Kontrollbericht erstellt
    /// </param>
    function PdfKontrollberichtErzeugenInternal(
      AKontrollberichtID: integer;
      ABldcode: integer;
      AUser: string
      ): string;

    function CC_PdfKontrollberichtErzeugenInternal(AKontrollberichtID, ABldcode: integer; AUser: string): string;

    function PdfProbenbegleitschreibenErzeugenInternal(
      AKontrollberichtID: integer;
      AProbeId: integer;
      ABldcode: integer;
      AUser: string
      ): string;

    class procedure Log(AMessage: string);

  strict private
    /// <summary>
    /// Erzeugt die PDF Datei für den Kontrollbericht. Die Datei wird lokal
    /// gespeichert und der Pafd zur Datei wird zurück gegeben
    /// </summary>
    /// <param name="AKontrollberichtID">
    /// Id des Kontrollberichts
    /// </param>
    /// <param name="ABldcode">
    /// Bundesland
    /// </param>
    /// <param name="AUser">
    /// User, der den Kontrollbericht erstellt
    /// </param>
    class procedure PdfProbenbegleitscheineErzeugenUndSpeichern(AKontrollbericht: TKontrollbericht; AUser: TUser);

    /// <summary>
    /// Erzeugt einen Aurelius Context aus der Connection des Datenmoduls.
    /// </summary>
    function GetContext: TObjectManager;
    class function GetEMAIL_KONTROLLBERICHT_BODY(AKontrollbericht: TKontrollbericht): string;
    class function GetEMAIL_KONTROLLBERICHT_BODY_BETRIEB(AKontrollbericht: TKontrollbericht): string;
    class function GetEMAIL_POSTVERSAND_BODY(AKontrollbericht: TKontrollbericht): string;
    class function ReplaceEmailVariables(AEmailBody: string; AKontrollbericht: TKontrollbericht): string;

    procedure SendSupportMail(ASubject, AMessage: string);

  public

    class property LogProc: TProc<string> read FLogProc write FLogProc;

    /// <summary>
    /// Erzeugt den Probenbegleitschein für die angegebene Probe und
    /// speichert diesen in der Datenbank.
    /// </summary>
    class procedure PdfProbenbegleitscheinErzeugenUndSpeichern(AProbe: TKBProbe; AUser: TUser);

    /// <summary>
    /// Versendet den Kontrollbericht und die Probenbegleitscheine.
    /// </summary>
    class procedure PdfBerichteVersenden(AKontrollbericht: TKontrollbericht);

    /// <summary>
    /// Erzeugt die PDF Dateien für den Kontrollbericht,sowie alle
    /// Probenbegleitscheine und versendet diese.
    /// </summary>
    class procedure PdfBerichteVerarbeiten(AKontrollbericht: TKontrollbericht; AUser: TUser);

    /// <summary>
    /// Erzeugt die PDF-Datei mit dem Kontrollbericht
    /// </summary>
    /// <returns>
    /// Der Dateiname der erzeugten PDF-Datei
    /// </returns>
    /// <remarks>
    /// Ist Public damti das zum lokalen Testen verwendet werden kann!
    /// </remarks>
    class function PdfKontrollberichtErzeugen(
      AKontrollberichtID: integer;
      ABldcode: integer;
      AUser: string): string; overload;

    /// <summary>
    /// Erzeugt den Probenbegleitschein zu der angegebenen Probe.
    /// </summary>
    /// <returns>
    /// Der Dateiname der erzeugten PDF-Datei.
    /// </returns>
    /// <remarks>
    /// Ist Public damti das zum lokalen Testen verwendet werden kann!
    /// </remarks>
    class function CC_PdfKontrollberichtErzeugen(AKontrollberichtID, ABldcode: integer; AUser: string): string;
      overload;

    /// <summary>
    /// Erzeugt die PDF Datei für den Kontrollbericht. Die Datei wird lokal
    /// gespeichert und der Pafd zur Datei wird zurück gegeben
    /// </summary>
    /// <param name="AKontrollberichtID">
    /// Id des Kontrollberichts
    /// </param>
    /// <param name="ABldcode">
    /// Bundesland
    /// </param>
    /// <param name="AUser">
    /// User, der den Kontrollbericht erstellt
    /// </param>
    class procedure PdfKontrollberichtErzeugenUndSpeichern(AKontrollbericht: TKontrollbericht; AUser: TUser);

    /// <summary>
    /// Erzeugt die PDF Datei für den Kontrollbericht in der CC Version, wenn
    /// möglich.
    /// </summary>
    /// <param name="AKontrollbericht">
    /// Die Kontrolle
    /// </param>
    /// <param name="AUser">
    /// User, der den Kontrollbericht erstellt
    /// </param>
    /// <param name="AKontrollberichtID">
    /// Id des Kontrollberichts
    /// </param>
    /// <param name="ABldcode">
    /// Bundesland
    /// </param>
    class procedure PdfKontrollberichtCCErzeugenUndSpeichern(AKontrollbericht: TKontrollbericht; AUser: TUser);

    /// <summary>
    /// Erzeugt den Probenbegleitschein zu der angegebenen Probe.
    /// </summary>
    /// <returns>
    /// Der Dateiname der erzeugten PDF-Datei.
    /// </returns>
    /// <remarks>
    /// Ist Public damti das zum lokalen Testen verwendet werden kann!
    /// </remarks>
    class function PdfProbenbegleitscheinErzeugen(AKontrollberichtID: integer; AProbeId: integer; ABldcode: integer;
      AUser:
      string): string; overload;
  end;

implementation

uses
  DX.Utils.Windows, System.IOUtils, DX.Data.Utils, DX.Utils.Logger, ELKE.Mail,
  frxFDComponents,
  System.RegularExpressions, ELKE.Server.Configuration, Aurelius.Mapping.Explorer;
{%CLASSGROUP 'Vcl.Controls.TControl'}

{$R *.dfm}


procedure TModuleReports.DataModuleCreate(Sender: TObject);
begin
  FDMemTableSystemdaten.Active := false;
  FDMemTableSystemdaten.Active := true;
  FDMemTableSystemdaten.Append;
  FDMemTableSystemdaten.FieldByName('ELKEversion').AsString := TPath.GetFileNameWithoutExtension(ParamStr(0)) + ' ' +
    DX.Utils.Windows.GetExeVersion;
  FDMemTableSystemdaten.Post;

end;

function TModuleReports.CC_PdfKontrollberichtErzeugenInternal(AKontrollberichtID, ABldcode: integer; AUser: string):
  string;
var
  LFilename: string;
  LDate: string;
begin
  Log(Format('PDF für CC-Kontrollbericht %d wird erzeugt...', [AKontrollberichtID]));

  FDMemTableSystemdaten.edit;
  FDMemTableSystemdaten.FieldByName('AngemeldeterUser').AsString := AUser;
  FDMemTableSystemdaten.Post;

  FDQueryKontrollbericht_CC.Close;
  FDQueryChecklisten_CC.Close;
  FDQueryFragen_CC.Close;
  FDQueryMassnahmen_CC.Close;
  FDQ_KontrollberichtOertlichkeiten_CC.Close;
  FDQ_MassnahmenOertlichkeiten_CC.Close;
  FDQueryKontrollbericht_CC.ParamByName('id_kontrollbericht').AsInteger := AKontrollberichtID;
  FDQueryKontrollbericht_CC.Open;
  Bundesland.Close;
  Bundesland.ParamByName('bldcode').AsSmallInt := FDQueryKontrollbericht_CC.FieldByName('bldcode').AsVariant;
  Bundesland.Open;
  FDQ_Behoerden.Close;
  FDQ_Behoerden.ParamByName('GRUPPEN_ID').AsInteger := FDQueryKontrollbericht_CC.FieldByName('ID_GRUPPE_QUELLE').Asinteger;
  FDQ_Behoerden.Open;
  var
  LBKBNr := FDQueryKontrollbericht_CC.FieldByName('BKB').AsString;

  FDQueryChecklisten_CC.ParamByName('id_kontrollbericht').AsInteger := AKontrollberichtID;
  FDQueryChecklisten_CC.Open;
  FDQueryFragen_CC.Open;
  FDQueryMassnahmen_CC.Open;

  FDQ_MassnahmenOertlichkeiten_CC.Open;
  FDQ_KontrollberichtOertlichkeiten_CC.Open;

  FDQ_FragenBilder_CC.Close;
  FDQ_FragenBilder_CC.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_FragenBilder_CC.Open;
  FDQ_MangelBilder_CC.Close;
  FDQ_MangelBilder_CC.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_MangelBilder_CC.Open;
  FDQ_CCKdaten_CC.Close;
  FDQ_CCKdaten_CC.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_CCKdaten_CC.Open;
  FDQueryAnwesende_CC.Close;
  FDQueryAnwesende_CC.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQueryAnwesende_CC.Open;

  FDQ_CCKAuswahldaten_CC.Close;
  FDQ_CCKAuswahldaten_CC.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_CCKAuswahldaten_CC.Open;

  // Für lokale Tests
{$IFNDEF REST}
  frxKontrollbericht_CC.PrepareReport(true);
  frxKontrollbericht_CC.ShowReport;
  exit;
{$ENDIF}
  ReportVorlageLaden(frxKontrollbericht_CC, AKontrollberichtID, 'CC');
  // Den Kontrollbericht als PDF exportieren
  if not frxKontrollbericht_CC.PrepareReport then
    raise Exception.Create('Report CC-Kontrollbericht konnte nicht erzeugt werden!');
  var
  LFolder := CreateReportsFolder;
  // Todo: Endedatum des Kontrollberichts einfügen
  LDate := FormatDateTime('DD.MM.YYYY hh:nn', Now);
  LFilename := Format('%s.pdf', [LBKBNr]);
  frxPDFExport.Title := Format('CC-Kontrollbericht %s vom %s', [LBKBNr, LDate]);
  frxPDFExport.Author := REPORTS_AUTHOR;
  frxPDFExport.Subject := Format('CC-Kontrollbericht %s', [LBKBNr]);
  frxPDFExport.Producer := TPath.GetFileNameWithoutExtension(ParamStr(0)) + ' ' + DX.Utils.Windows.GetExeVersion;
  LFilename := TPath.Combine(LFolder, LFilename);
  frxPDFExport.filename := LFilename;
  result := LFilename;
  frxPDFExport.OpenAfterExport := false;
  frxKontrollbericht_CC.Export(frxPDFExport);

end;

procedure TModuleReports.ConnectionBeforeConnect(Sender: TObject);
begin
  Connection.SetParamsFromConnectionString(TConfiguration.Default.DBConnectionString);
end;

function TModuleReports.PdfKontrollberichtErzeugenInternal(AKontrollberichtID: integer;
  ABldcode: integer; AUser: string): string;
var
  LFilename: string;
  LDate: string;
begin
  Log(Format('PDF für Kontrollbericht %d wird erzeugt...', [AKontrollberichtID]));

  Bundesland.Close;
  Bundesland.ParamByName('bldcode').AsSmallInt := ABldcode;
  Bundesland.Open;

  FDMemTableSystemdaten.edit;
  FDMemTableSystemdaten.FieldByName('AngemeldeterUser').AsString := AUser;
  FDMemTableSystemdaten.Post;

  FDQueryKontrollbericht.Close;
  FDQueryChecklisten.Close;
  FDQueryFragen.Close;
  FDQueryMassnahmen.Close;
  FDQ_KontrollberichtOertlichkeiten.Close;
  FDQ_MassnahmenOertlichkeiten.Close;

  FDQueryKontrollbericht.ParamByName('id_kontrollbericht').AsInteger := AKontrollberichtID;
  FDQueryKontrollbericht.Open;
  Bundesland.Close;
  Bundesland.ParamByName('bldcode').AsSmallInt := FDQueryKontrollbericht.FieldByName('bldcode').AsVariant;
  Bundesland.Open;

  FDQ_Behoerden.Close;
  FDQ_Behoerden.ParamByName('GRUPPEN_ID').AsInteger := FDQueryKontrollbericht.FieldByName('ID_GRUPPE_QUELLE').Asinteger;
  FDQ_Behoerden.Open;


  FDQueryChecklisten.ParamByName('id_kontrollbericht').AsInteger := AKontrollberichtID;
  FDQueryChecklisten.Open;
  FDQueryFragen.Open;
  FDQueryMassnahmen.Open;

  // FDQ_MassnahmenOertlichkeiten.ParamByName('ID_BERICHT').AsInteger := AKontrollberichtID;
  FDQ_MassnahmenOertlichkeiten.Open;
  FDQ_KontrollberichtOertlichkeiten.Open;

  FDQ_FragenBilder.Close;
  FDQ_FragenBilder.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_FragenBilder.Open;
  FDQ_MangelBilder.Close;
  FDQ_MangelBilder.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_MangelBilder.Open;
  FDQ_CCKdaten.Close;
  FDQ_CCKdaten.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_CCKdaten.Open;
  FDQueryAnwesende.Close;
  FDQueryAnwesende.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQueryAnwesende.Open;

  FDQ_CCKAuswahldaten.Close;
  FDQ_CCKAuswahldaten.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_CCKAuswahldaten.Open;


  // Für lokale Tests
{$IFNDEF REST}
  frxKontrollbericht.PrepareReport(true);
  frxKontrollbericht.ShowReport;
  exit;
{$ENDIF}
  ReportVorlageLaden(frxKontrollbericht, AKontrollberichtID, 'KB');
  // Den Kontrollbericht als PDF exportieren
  if not frxKontrollbericht.PrepareReport then
    raise Exception.Create('Report Kontrollbericht konnte nicht erzeugt werden!');
  var
  LFolder := CreateReportsFolder;
  // Todo: Endedatum des Kontrollberichts einfügen
  LDate := FormatDateTime('DD.MM.YYYY hh:nn', Now);
  LFilename := Format('Kontrollbericht_%d.pdf', [AKontrollberichtID]);
  frxPDFExport.Title := Format('Kontrollbericht %d vom %s', [AKontrollberichtID, LDate]);
  frxPDFExport.Author := REPORTS_AUTHOR;
  frxPDFExport.Subject := Format('Kontrollbericht %d', [AKontrollberichtID]);
  frxPDFExport.Producer := TPath.GetFileNameWithoutExtension(ParamStr(0)) + ' ' + DX.Utils.Windows.GetExeVersion;
  LFilename := TPath.Combine(LFolder, LFilename);
  frxPDFExport.filename := LFilename;
  result := LFilename;
  frxPDFExport.OpenAfterExport := false;
  frxKontrollbericht.Export(frxPDFExport);
end;

function TModuleReports.PdfProbenbegleitschreibenErzeugenInternal(
  AKontrollberichtID: integer;
  AProbeId: integer;
  ABldcode: integer;
  AUser: string): string;
var
  LFilename: String;
  LDate: String;
begin
  Log(Format('PDF für Probenbegleitschein %d/%d wird erzeugt...', [AKontrollberichtID, AProbeId]));

  FDMemTableSystemdaten.edit;
  FDMemTableSystemdaten.FieldByName('AngemeldeterUser').AsString := AUser;
  FDMemTableSystemdaten.Post;
  // Queries vorbereiten
  quKontrollDetails.Close;
  quKontrollDetails.ParamByName('id').AsInteger := AKontrollberichtID;
  quKontrollDetails.Open;

  KbProben.Close;
  KbProben.ParamByName('id').AsInteger := AProbeId;
  KbProben.Open;

  BetriebProben.Close;
  BetriebProben.ParamByName('id').AsInteger :=
    quKontrollDetails.FieldByName('BetriebID').AsInteger;
  BetriebProben.Open;

  Kontrollorgan.Close;
  Kontrollorgan.ParamByName('id').AsInteger := KbProben.FieldByName('ID_EINSENDER').AsInteger;
  // quKontrollDetails.FieldByName('KontrollorganID').AsInteger;
  Kontrollorgan.Open;

  ProbenBilder.Close;
  ProbenBilder.ParamByName('id').AsInteger := AProbeId;
  ProbenBilder.Open;

  Unterschriften.Close;
  Unterschriften.ParamByName('id').AsInteger := AKontrollberichtID;
  Unterschriften.Open;

  Bundesland.Close;
  Bundesland.ParamByName('bldcode').AsSmallInt := Kontrollorgan.FieldByName('bldcode').AsVariant; // ; ABldcode;
  Bundesland.Open;

  Anwesender.Close;
  Anwesender.ParamByName('id').AsInteger := AKontrollberichtID;
  Anwesender.Open;

  FDQ_Probenbilder.Close;
  FDQ_Probenbilder.ParamByName('ID_KONTROLLBERICHT').AsInteger := AKontrollberichtID;
  FDQ_Probenbilder.Open;

//  FDQ_Behoerden.Close;
//  FDQ_Behoerden.ParamByName('GRUPPEN_ID').AsInteger := FDQueryKontrollbericht.FieldByName('ID_GRUPPE_QUELLE').Asinteger;
 // FDQ_Behoerden.Open;
  // Variablen für den Probenbegleitschein zur Verfügung stellen
  // frxProbenbegleitschreiben.Variables['ELKEVersion'] := '''' + DX.Utils.Windows.GetExeVersion + '''';
  // frxProbenbegleitschreiben.Variables['AngemeldeterUser'] := '''' + AUser +   '''';
  // frxProbenbegleitschreiben.Variables['ReportVersion'] := '''' + VERSION_PROBENBGLEITSCHEIN + '''';

{$IFNDEF REST}
  // Für lokale Tests
  frxProbenbegleitschreiben.PrepareReport(true);
  frxProbenbegleitschreiben.ShowReport;
  exit;
{$ENDIF}
  ReportVorlageLaden(frxProbenbegleitschreiben, AKontrollberichtID, 'PB');
  // Probenbegleitschein als PDF exportieren
  if not frxProbenbegleitschreiben.PrepareReport then
    raise Exception.Create('Report Probenbegleitschein konnte nicht erzeugt werden!');
  var
  LFolder := CreateReportsFolder;
  // Todo: Endedatum des Kontrollberichts einfügen
  LDate := FormatDateTime('DD.MM.YYYY hh:nn', Now);
  LFilename := Format('Probenbegleitschein_%d_%d.pdf', [AKontrollberichtID, AProbeId]);
  frxPDFExport.Title := Format('Probenbegleitschein %d vom %s', [AKontrollberichtID, LDate]);
  frxPDFExport.Author := REPORTS_AUTHOR;
  frxPDFExport.Subject := Format('Probenbegleitschein %d', [AKontrollberichtID]);
  frxPDFExport.Producer := TPath.GetFileNameWithoutExtension(ParamStr(0)) + ' ' + DX.Utils.Windows.GetExeVersion;
  LFilename := TPath.Combine(LFolder, LFilename);
  frxPDFExport.filename := LFilename;
  result := LFilename;
  frxPDFExport.OpenAfterExport := false;
  frxProbenbegleitschreiben.Export(frxPDFExport);
end;

class function TModuleReports.ReplaceEmailVariables(AEmailBody: string; AKontrollbericht: TKontrollbericht): string;
begin
  result := AEmailBody;
  result := result.Replace('%BKB%', AKontrollbericht.Bkb, [rfReplaceAll, rfIgnoreCase]);
  result := result.Replace('%DATUM_ENDE%', FormatDateTime(FormatSettings.ShortDateFormat, AKontrollbericht.Endezeit),
    [rfReplaceAll, rfIgnoreCase]);
  result := result.Replace('%BETRIEB_NAME%', AKontrollbericht.Betrieb.Name, [rfReplaceAll, rfIgnoreCase]);

  var
  LAdresse := Format('%s %s %s %s',
    [AKontrollbericht.Betrieb.Adresse.Plz,
    AKontrollbericht.Betrieb.Adresse.Ort,
    AKontrollbericht.Betrieb.Adresse.Strasse,
    AKontrollbericht.Betrieb.Adresse.Adresszusatz.ValueOrDefault]).Trim;

  result := result.Replace('%BETRIEB_ADDRESSE%', LAdresse, [rfReplaceAll, rfIgnoreCase]);
end;

procedure TModuleReports.ReportVorlageLaden(AReport: TfrxReport; AKontrolleID: integer; AReportTyp: string);
begin
  Assert(Assigned(AReport));
  Assert(AKontrolleID > 0);
  var
  LContext := GetContext;
  var
  LReportVorlage := '';
  var
  LKontrolle := LContext.Find<TKontrollbericht>(AKontrolleID);
  if LKontrolle = nil then
    raise Exception.CreateFmt('Kontrolle %d konnte nicht geladen werden!', [AKontrolleID]);

  var
  LPerson := LKontrolle.Kontrollorgan;
  if LPerson = nil then
  begin
    LPerson := LKontrolle.Erfasser;
  end;
  if LPerson = nil then
    raise Exception.CreateFmt('Kontrolle %d hat weder Erfasser noch Kontrollorgan!', [AKontrolleID]);
  var
  LBundesland := LPerson.Users.First.Bundesland.Bldcode;
  for var LKontrolltypReport in LKontrolle.Kontrolltyp.KontrolltypReports do
  begin
    if (LKontrolltypReport.ReportTyp.Typ.ToUpper = AReportTyp.ToUpper) and
      (LKontrolltypReport.Bundesland.Bldcode = LBundesland) then
    begin
      LReportVorlage := LKontrolltypReport.Report.Reportvorlage.AsUnicodeString;
      // TEncoding.UTF8.GetString(LKontrolltypReport.Report.Reportvorlage.AsBytes);
    end;
  end;

  // Wenn es für den Kontrolltyp der Kontrolle keinen CC Report gibt, dann wird as mit einer Exception quittiert
  if (LReportVorlage.Length = 0) and (AReportTyp = 'CC') then
    raise EKeinCCBericht.Create('Error Message');

  // Wenn es eine Vorlage in der DB gibt, dann laden wir diese. Wenn nicht wir der eingebaute Report verwendet.
  if LReportVorlage.Length > 0 then
  begin
    var
    LVorlageStream := TStringStream.Create(LReportVorlage, TEncoding.UTF8);
    try
      AReport.LoadFromStream(LVorlageStream);
      // Nun noch die ggfs vorhandene Connection im Report parametrisieren
      var
      LConnection := AReport.FindObject('DBConnection');
      if LConnection <> nil then
      begin
        Assert(LConnection is TfrxFDDatabase);
        TfrxFDDatabase(LConnection).Params.Assign(Connection.Params);
      end;
    finally
      FreeAndNil(LVorlageStream);
    end;
  end
  else
  begin
    var
    LMessage := Format('Es konnte keine Reportvorlage vom Typ %s für die Kontrolle %d geladen werden.',
      [AReportTyp, AKontrolleID]);
    Log(LMessage);
    SendSupportMail('Report-Vorlage nicht gefunden', LMessage);
    raise EReportTemplateNotFound.Create('Report-Vorlage nicht gefunden!');
  end;
end;

procedure TModuleReports.SendSupportMail(ASubject, AMessage: string);
begin
  var
  LDBConnection := Connection.Params.Values['Server'] + '/' + Connection.Params.Values['Database'];
  TELKEMail.SendMail(
    '[ELKE REST] ACHTUNG: ' + ASubject,
    AMessage + #13#10 + ParamStr(0) + #13#10 + 'Version ' + GetExeVersion + #13#10 + LDBConnection + #13#10 +
    TConfiguration.Instance.BaseURL,
    // '<EMAIL>',
    '<EMAIL>',
    [],
    0 // Kein Kontroll-Kontext
    );
end;

// Wird beim Probenbegleitschein einmal pro Element ausgeführt
// BVerdacht, BBeimischrate und BHerkaufZukauf wird ausgeblendet, wenn sie für
// den Report nicht relevant/leer sind.
procedure TModuleReports.FrxProbenbegleitscheinOnBeforePrint(Sender: TfrxReportComponent);
var
  text: string;
begin
  if SameText(Sender.Name, 'BVerdacht') then
  begin
    text := AnsiLowerCase(frxKbProben.Value['Probenart']);
    Sender.Visible := text.Contains('verdacht');
  end
  else if SameText(Sender.Name, 'BBeimischrate') then
  begin
    text := AnsiLowerCase(frxKbProben.Value['verwendungszweck']);
    Sender.Visible := text.Contains('ergänzungsfuttermittel');
  end
  else if SameText(Sender.Name, 'futtertyp') then
  begin
    text := AnsiLowerCase(frxKbProben.Value['BHerkZukauf']);
    Sender.Visible := text.Contains('zugekaufte');
  end
  else if SameText(Sender.Name, 'tfVerweigerungsgrund') then
  begin
    Sender.Visible := not
      VarIsNull(frxKbProben.Value['VERWEIGERUNGSGRUND_UNTERSCHRIFT']);
  end;
end;

function TModuleReports.GetContext: TObjectManager;
begin
  // Wir nutzen für Aurelius hier die Connection aus dem Datenmodul.
  var
  LConnection := TFireDacConnectionAdapter.Create(Connection, false);

  result := TObjectManager.Create(LConnection, TMappingExplorer.Get('ELKE'));
end;

class function TModuleReports.GetEMAIL_KONTROLLBERICHT_BODY(AKontrollbericht: TKontrollbericht): string;
begin
  result := AKontrollbericht.GetEMailText('KB_ORGAN', EMAIL_KONTROLLBERICHT_BODYx);

  result := ReplaceEmailVariables(result, AKontrollbericht);

end;

class function TModuleReports.GetEMAIL_KONTROLLBERICHT_BODY_BETRIEB(AKontrollbericht: TKontrollbericht): string;
begin
  result := AKontrollbericht.GetEMailText('KB_BETRIEB', EMAIL_KONTROLLBERICHT_BODY_BETRIEBx);
  result := ReplaceEmailVariables(result, AKontrollbericht);
end;

class function TModuleReports.GetEMAIL_POSTVERSAND_BODY(AKontrollbericht: TKontrollbericht): string;
begin
  result := AKontrollbericht.GetEMailText('KB_POSTVERSAND', EMAIL_POSTVERSAND_BODYx);
  result := ReplaceEmailVariables(result, AKontrollbericht);
end;

class
  procedure TModuleReports.Log(AMessage: string);
begin
  // Das loggen kann von aussen eingefügt werden, damit bleibt die Unit vom eigentlichen Logmechanismus unabhängig
  // Minimal loggen wir über DXLog in einer Datei
  if Assigned(FLogProc) then
  begin
    FLogProc(AMessage);
  end
  else
  begin
    DXLog(AMessage);
  end;
end;

class function TModuleReports.PdfKontrollberichtErzeugen(
  AKontrollberichtID: integer;
  ABldcode: integer;
  AUser: string): string;
begin
  var
  LModule := TModuleReports.Create(nil);
  try
    result := LModule.PdfKontrollberichtErzeugenInternal(AKontrollberichtID, ABldcode, AUser);
  finally
    FreeAndNil(LModule);
  end;
end;

class function TModuleReports.CC_PdfKontrollberichtErzeugen(AKontrollberichtID, ABldcode: integer; AUser: string):
  string;
begin
  var
  LModule := TModuleReports.Create(nil);
  try
    result := LModule.CC_PdfKontrollberichtErzeugenInternal(AKontrollberichtID, ABldcode, AUser);
  finally
    FreeAndNil(LModule);
  end;
end;

class function TModuleReports.PdfProbenbegleitscheinErzeugen(
  AKontrollberichtID: integer;
  AProbeId: integer;
  ABldcode: integer;
  AUser: string): string;
begin
  var
  LModule := TModuleReports.Create(nil);
  try
    result := LModule.PdfProbenbegleitschreibenErzeugenInternal(AKontrollberichtID, AProbeId, ABldcode, AUser);
  finally
    FreeAndNil(LModule);
  end;
end;

class procedure TModuleReports.PdfBerichteVerarbeiten(AKontrollbericht: TKontrollbericht; AUser: TUser);
begin
  Log('PDF-Dateien für Kontrollbericht ' + AKontrollbericht.Id.ToString + ' verarbeiten...');
  try
    PdfKontrollberichtErzeugenUndSpeichern(AKontrollbericht, AUser);
    PdfKontrollberichtCCErzeugenUndSpeichern(AKontrollbericht, AUser);
    PdfProbenbegleitscheineErzeugenUndSpeichern(AKontrollbericht, AUser);
    PdfBerichteVersenden(AKontrollbericht);
    Log('PDF-Dateien für Kontrollbericht ' + AKontrollbericht.Id.ToString + 'wurden verarbeitet.');
  except
    on E: EReportTemplateNotFound do
    begin
      Log('Mindestens eine der PDF-Reportvorlagen für KB ' + AKontrollbericht.Id.ToString +
        ' konnte nicht geladen werden.' +
        ' Es wurde kein E-Mailversand ausgelöst');
      raise
    end;
  end;
end;

class
  procedure TModuleReports.PdfBerichteVersenden(AKontrollbericht: TKontrollbericht);
var
  LPDFFiles: Tarray<string>;
begin
  // Hier werden für einen Kontrollbericht die PDF Datei für den Bericht selber und alle PDF Dateien für die Proben genmailt
  Assert(Assigned(AKontrollbericht));
  var
  LId := AKontrollbericht.Id;
  try
    // Alle PDF Dateien werden als Datei physisch abgelegt
    var
    LFolder := CreateReportsFolder;
    SetLength(LPDFFiles, 0);
    try
      // Der Kontrollbericht
      if AKontrollbericht.Dokument <> nil then
      begin
        var
        LFile := TPath.Combine(LFolder, AKontrollbericht.Dokument.Dateiname);
        SetLength(LPDFFiles, Length(LPDFFiles) + 1);
        TFile.WriteAllBytes(LFile, AKontrollbericht.Dokument.Dokument.AsBytes);
        LPDFFiles[Length(LPDFFiles) - 1] := LFile;
      end;

      // Die Proben
      if AKontrollbericht.Proben <> nil then
      begin
        for var LProbe in AKontrollbericht.Proben do
        begin
          if LProbe.Dokument <> nil then
          begin
            var
            LFile := TPath.Combine(LFolder, LProbe.Dokument.Dateiname);
            SetLength(LPDFFiles, Length(LPDFFiles) + 1);
            TFile.WriteAllBytes(LFile, LProbe.Dokument.Dokument.AsBytes);
            LPDFFiles[Length(LPDFFiles) - 1] := LFile;
          end;
        end;
      end;

      if AKontrollbericht.Kontrollorgan = nil then
        raise Exception.Create('Der Bericht hat kein Kontrollorgan zugewiesen.');

      if not AKontrollbericht.Kontrollorgan.Email.HasValue then
        raise Exception.Create('Kontrollorgan des Berichts hat keine EMail Adresse.');

      // E-Mail Kontrollorgan
      TELKEMail.SendMail(
        Format('[ELKE] Kontrollbericht %s', [AKontrollbericht.Bkb]),
        GetEMAIL_KONTROLLBERICHT_BODY(AKontrollbericht),
        AKontrollbericht.Kontrollorgan.Email,
        LPDFFiles,
        AKontrollbericht.Id
        );

      // E-Mail an die Quell-Gruppe, wenn vorhanden
      if AKontrollbericht.GruppeQuelle <> nil then
      begin
        var
        LGruppenEmail := AKontrollbericht.GruppeQuelle.Email.ValueOrDefault;
        if LGruppenEmail.Trim.ToUpper <> 'KEIN' then
        begin
          // Wenn die GruppeQuelle keine Email hat, dann an den Hauptverantwortlichen
          if (LGruppenEmail = '') and (AKontrollbericht.GruppeQuelle <> nil) then
          begin
            // Der Hauptverantwortliche sollte eine EMail haben
            if AKontrollbericht.GruppeQuelle.Hauptverantwortlicher <> nil then
            begin
              LGruppenEmail := AKontrollbericht.GruppeQuelle.Hauptverantwortlicher.Email.ValueOrDefault;
            end;
          end;

          if LGruppenEmail > '' then
          begin
            Log('Kontrolle hat eine Quellgruppe - Email an Gruppe wird versandt...');
            TELKEMail.SendMail(
              Format('[ELKE] Kontrollbericht %s', [AKontrollbericht.Bkb]),
              GetEMAIL_KONTROLLBERICHT_BODY(AKontrollbericht),
              LGruppenEmail,
              LPDFFiles,
              AKontrollbericht.Id
              );
          end;
        end;
      end;

      // Nur wenn es Anwesende gab, dann wird ein Bericht an den Betieb verschickt. Bei einem Storno z.b. gibt es keine Anwesenden
      if AKontrollbericht.Anwesende.Count > 0 then
      begin
        // Email and Kommunikationsberechtigte(n) des Betriebs ermitteln.
        // Es wird an ALLE Kommunikationsberechtigten versendet!
        // Gffs. Kontrollorgan Postversand triggern, wenn kein Email ermittelt werden kann
        var
        LEmailAnBetriebVersandt := false;
        var
          LKommunikationsberechtigter: TAnwesender;
        for var LAnwesender in AKontrollbericht.Anwesende do
        begin
          if LAnwesender.Kommunikationsberechtigt then
          begin
            LKommunikationsberechtigter := LAnwesender;
            var
            LEmailKommunikationsberechtigter := '';

            if (LKommunikationsberechtigter <> nil) and LKommunikationsberechtigter.Email.HasValue then
            begin
              LEmailKommunikationsberechtigter := LKommunikationsberechtigter.Email.ValueOrDefault.Trim.ToLower;
              if not TELKEMail.IsValidEmailAddress(LEmailKommunikationsberechtigter) then
              begin
                LEmailKommunikationsberechtigter := '';
              end;
            end;
            if LEmailKommunikationsberechtigter > '' then
            begin
              TELKEMail.SendMail(
                Format('[ELKE] Kontrollbericht %s', [AKontrollbericht.Bkb]),
                GetEMAIL_KONTROLLBERICHT_BODY_BETRIEB(AKontrollbericht),
                LEmailKommunikationsberechtigter,
                LPDFFiles,
                AKontrollbericht.Id
                );
              LEmailAnBetriebVersandt := true;
            end;
          end;
        end;
        if not LEmailAnBetriebVersandt then
        begin
          // Keinen Kommunikationsberechtigten mit EMail gefunden: Zusätzliche E-Mail für Postversand an Kontrollorgan
          TELKEMail.SendMail(
            Format('[ELKE] POSTVERSAND auslösen für Kontrollbericht %s', [AKontrollbericht.Bkb]),
            GetEMAIL_POSTVERSAND_BODY(AKontrollbericht),
            AKontrollbericht.Kontrollorgan.Email,
            LPDFFiles,
            AKontrollbericht.Id
            );
        end;
      end;
    finally
      for var LFile in LPDFFiles do
      begin
        try
          TFile.Delete(LFile);
        except
          Log('Temporäre Datei konnte nicht gelöscht werden!');
        end;
      end;
      SetLength(LPDFFiles, 0);
    end;
  Except
    on E: Exception do
    begin
      Log('Email-Versand der PDF Berichte (' + LId.ToString + ') fehlgeschlagen: ' + E.Message);
    end;
  end;
end;

class procedure TModuleReports.PdfKontrollberichtErzeugenUndSpeichern(AKontrollbericht: TKontrollbericht; AUser: TUser);
begin
  var
  LFile := PdfKontrollberichtErzeugen(AKontrollbericht.Id, AKontrollbericht.Bundesland.Bldcode, AUser.Person.Fullname);
  try
    if AKontrollbericht.Dokument = nil then
    begin
      AKontrollbericht.Dokument := TDokument.Create;
    end;
    AKontrollbericht.Dokument.Bldcode := AKontrollbericht.Bundesland.Bldcode;
    AKontrollbericht.Dokument.Dateiname := TPath.GetFileName(LFile);
    AKontrollbericht.Dokument.Typ := TPath.GetExtension(LFile).Replace('.', '').ToUpper;
    AKontrollbericht.Dokument.ErstelltAm := TFile.GetCreationTime(LFile);
    AKontrollbericht.Dokument.Bezeichnung := 'Kontrollbericht ' + AKontrollbericht.Id.ToString;
    AKontrollbericht.Dokument.Dokument.AsBytes := TFile.ReadAllBytes(LFile);
  finally
    TFile.Delete(LFile);
  end;
end;

class procedure TModuleReports.PdfKontrollberichtCCErzeugenUndSpeichern(AKontrollbericht: TKontrollbericht;
  AUser: TUser);
begin
  // Wenn es für den zughörigen Kontrolltyp keinen CC Report gibt, dann löst PdfKontrollbericht eine Exception aus
  try
    var
    LFile := CC_PdfKontrollberichtErzeugen(AKontrollbericht.Id, AKontrollbericht.Bundesland.Bldcode,
      AUser.Person.Fullname);
    try
      if AKontrollbericht.DokumentCC = nil then
      begin
        AKontrollbericht.DokumentCC := TDokument.Create;
      end;
      AKontrollbericht.DokumentCC.Bldcode := AKontrollbericht.Bundesland.Bldcode;
      AKontrollbericht.DokumentCC.Dateiname := TPath.GetFileName(LFile);
      AKontrollbericht.DokumentCC.Typ := TPath.GetExtension(LFile).Replace('.', '').ToUpper;
      AKontrollbericht.DokumentCC.ErstelltAm := TFile.GetCreationTime(LFile);
      AKontrollbericht.DokumentCC.Bezeichnung := 'Kontrollbericht CC ' + AKontrollbericht.Bkb;
      AKontrollbericht.DokumentCC.Dokument.AsBytes := TFile.ReadAllBytes(LFile);
    finally
      TFile.Delete(LFile);
    end;
  except
    on E: EKeinCCBericht do
    begin
      Log('Keine CC-Version des Kontrollberichts für Kontrolle ' + AKontrollbericht.Id.ToString +
        ' vorhanden/erzeugt. ');
    end
    else
      raise;
  end;
end;

class
  procedure TModuleReports.PdfProbenbegleitscheineErzeugenUndSpeichern(
  AKontrollbericht: TKontrollbericht;
  AUser:
  TUser);
begin
  Log(Format('Für Kontrollbericht %d werden %d Probenbegleitscheine erzeugt...',
    [AKontrollbericht.Id, AKontrollbericht.Proben.Count]));

  for var LProbe in AKontrollbericht.Proben do
  begin
    PdfProbenbegleitscheinErzeugenUndSpeichern(LProbe, AUser);
  end;
end;

class
  procedure TModuleReports.PdfProbenbegleitscheinErzeugenUndSpeichern(AProbe: TKBProbe;
  AUser:
  TUser);
begin
  var
  LFile := PdfProbenbegleitscheinErzeugen(
    AProbe.Kontrollbericht.Id, AProbe.Id, AUser.Bundesland.Bldcode, AUser.Person.Fullname);
  try
    if AProbe.Dokument = nil then
    begin
      AProbe.Dokument := TDokument.Create;
    end;
    AProbe.Dokument.Bldcode := AProbe.Kontrollbericht.Bundesland.Bldcode;
    AProbe.Dokument.Dateiname := TPath.GetFileName(LFile);
    AProbe.Dokument.Typ := TPath.GetExtension(LFile).Replace('.', '').ToUpper;
    AProbe.Dokument.ErstelltAm := TFile.GetCreationTime(LFile);
    AProbe.Dokument.Bezeichnung := Format('Probenbegleitschein %d/%d', [AProbe.Kontrollbericht.Id, AProbe.Id]);
    AProbe.Dokument.Dokument.AsBytes := TFile.ReadAllBytes(LFile);
  finally
    TFile.Delete(LFile);
  end;
end;

end.
