program ELKEReportGenerator;



uses
  Vcl.Forms,
  Forms.ReportGenerator in 'Forms.ReportGenerator.pas' {FormReportGenerator},
  Modules.Reports.AMA in 'Modules.Reports.AMA.pas' {ModuleAMAReports},
  Modules.Reports.ELKE in 'Modules.Reports.ELKE.pas',
  Modules.Reports.Base in 'Modules.Reports.Base.pas';

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TFormReportGenerator, FormReportGenerator);
  Application.Run;
end.
