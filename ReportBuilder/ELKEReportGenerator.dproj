﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Application</AppType>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>ELKEReportGenerator.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{E3A22BDF-D553-441A-9321-10BFFA1E7B00}</ProjectGuid>
        <ProjectName Condition="'$(ProjectName)'==''">ELKEReportGenerator</ProjectName>
        <ProjectVersion>20.2</ProjectVersion>
        <TargetedPlatforms>1</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>ELKEReportGenerator</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)\DCU</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UnitSearchPath>..\ESCore\classes;..\ESCore\model;..\_libs\dx-library;..\_libs\FastMM4;..\_libs\TMSAurelius\source;..\_libs\TMSAurelius\source\core;..\_libs\TMSAurelius\source\drivers;..\_libs\TMSBCL\source;..\_libs\TMSBCL\source\core;..\_libs\TMSBCL\source\core\common;..\_libs\TMSBCL\source\extra;..\_libs\TMSXData\source;..\_libs\TMSXData\source\core;..\_libs\TMSXData\source\core\common;..\_libs\TMSSparkle\source;..\_libs\TMSSparkle\source\core;..\_libs\TMSSparkle\source\core\common;..\_libs\TMSSparkle\source\extra;..\_libs\TMSSparkle\source\app;..\_libs\FastReport\FastCore\VCL\Sources;..\_libs\FastReport\FastGraphics\VCL\Sources;..\_libs\FastReport\FastLocalization\VCL\Sources;..\_libs\FastReport\FastQueryBuilder\VCL\Sources;..\_libs\FastReport\FastReport\VCL\Sources;..\_libs\FastReport\FastScript\VCL\Sources;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1031</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>DBXSqliteDriver;RESTComponents;fmxase;DBXDb2Driver;DBXInterBaseDriver;vclactnband;vclFireDAC;IWBootstrapD103;emsclientfiredac;tethering;svnui;DataSnapFireDAC;rbBDE2026;FireDACADSDriver;rbRAP2026;FMXfrxDB26;DBXMSSQLDriver;rbRest2026;DatasnapConnectorsFreePascal;FireDACMSSQLDriver;rbDBE2026;vcltouch;vcldb;bindcompfmx;svn;DBXOracleDriver;inetdb;rbDB2026;FmxTeeUI;emsedge;IWCGJQMobile_150_260;fmx;FireDACIBDriver;fmxdae;FMXfrx26;vclib;frxTee26;rbTC2026;rbIBE2026;FireDACDBXDriver;dbexpress;IndyCore;vclx;dsnap;emsclient;DataSnapCommon;FireDACCommon;rbRIDE2026;RESTBackendComponents;DataSnapConnectors;VCLRESTComponents;soapserver;IWCGBootstrap_150_260;IWCGJQCore_150_260;vclie;bindengine;DBXMySQLDriver;CloudService;FireDACOracleDriver;FireDACMySQLDriver;DBXFirebirdDriver;rbRCL2026;frx26;FireDACCommonODBC;FireDACCommonDriver;rbDIDE2026;DataSnapClient;rbCIDE2026;dclRBE2026;inet;IndyIPCommon;bindcompdbx;dclRBFireDAC2026;vcl;IndyIPServer;DBXSybaseASEDriver;frxDB26;IndySystem;FireDACDb2Driver;dsnapcon;rbADO2026;FireDACMSAccDriver;fmxFireDAC;FireDACInfxDriver;vclimg;rbTCUI2026;TeeDB;FireDAC;dclRBDBE2026;emshosting;frxe26;FireDACSqliteDriver;FireDACPgDriver;ibmonitor;FireDACASADriver;DBXOdbcDriver;IWBootstrap4D103;FireDACTDataDriver;FMXTee;rbRTL2026;soaprtl;DbxCommonDriver;ibxpress;Tee;DataSnapServer;xmlrtl;soapmidas;DataSnapNativeClient;fmxobj;vclwinx;FireDACDSDriver;rtl;emsserverresource;DbxClientDriver;ibxbindings;DBXSybaseASADriver;rbDAD2026;CustomIPTransport;vcldsnap;rbUSER2026;dclRBADO2026;bindcomp;appanalytics;DBXInformixDriver;IndyIPClient;bindcompvcl;TeeUI;dbxcds;VclSmp;Intraweb_15_D10_3;FireDACODBCDriver;adortl;IWCGJQComps_150_260;rbUSERDesign2026;DataSnapIndy10ServerTransport;dsnapxml;DataSnapProviderClient;dbrtl;IndyProtocols;inetdbxpress;FireDACMongoDBDriver;rbFireDAC2026;dclRBIBE2026;rbIDE2026;DataSnapServerMidas;$(DCC_UsePackage)</DCC_UsePackage>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="Forms.ReportGenerator.pas">
            <Form>FormReportGenerator</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="Modules.Reports.AMA.pas">
            <Form>ModuleAMAReports</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="Modules.Reports.ELKE.pas"/>
        <DCCReference Include="Modules.Reports.Base.pas">
            <FormType>dfm</FormType>
        </DCCReference>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Application</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">ELKEReportGenerator.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">False</Platform>
                <Platform value="iOSDevice64">False</Platform>
                <Platform value="Linux64">False</Platform>
                <Platform value="OSX64">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
