inherited ModuleAMAReports: TModuleAMAReports
  OldCreateOrder = True
  OnCreate = DataModuleCreate
  Height = 456
  Width = 742
  object frxDBD_Systemdaten: TfrxDBDataset
    UserName = 'Systemdaten'
    CloseDataSource = False
    FieldAliases.Strings = (
      'ELKEversion=ELKEversion'
      'AngemeldeterUser=AngemeldeterUser')
    DataSet = FDMemTableSystemdaten
    BCDToCurrency = False
    DataSetOptions = []
    Left = 232
    Top = 72
  end
  object FDMemTableSystemdaten: TFDMemTable
    Active = True
    FieldDefs = <
      item
        Name = 'ELKEversion'
        DataType = ftString
        Size = 20
      end
      item
        Name = 'AngemeldeterUser'
        DataType = ftString
        Size = 50
      end>
    IndexDefs = <>
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvSilentMode]
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    StoreDefs = True
    Left = 232
    Top = 24
  end
  object FDQueryCCK_Auftrag: TFDQuery
    ActiveStoredUsage = []
    Connection = Connection
    SQL.Strings = (
      
        'select BEWEGUNGSDATEN.CCK_AUFTRAG.*, BEWEGUNGSDATEN.CCK_BETRIEBS' +
        'DATEN.*, BEWEGUNGSDATEN.KONTROLLBERICHT.*, '
      
        'BEARBEITER.TITEL as TITEL_BEARB,BEARBEITER.VORNAME as VORNAME_BE' +
        'ARB, BEARBEITER.NACHNAME as NACHNAME_BEARB,'
      
        'BEARBEITER.TITEL as TITEL_BEWERT,BEARBEITER.VORNAME as VORNAME_B' +
        'EWERT, BEARBEITER.NACHNAME as NACHNAME_BEWERT from BEWEGUNGSDATE' +
        'N.CCK_AUFTRAG'
      
        '/*join BEWEGUNGSDATEN.CCK_BETRIEBSDATEN on BEWEGUNGSDATEN.CCK_BE' +
        'TRIEBSDATEN.ID_CCK_AUFTRAG = BEWEGUNGSDATEN.CCK_AUFTRAG.ID*/'
      
        'join BEWEGUNGSDATEN.CCK_BETRIEBSDATEN on BEWEGUNGSDATEN.CCK_AUFT' +
        'RAG.LFBIS_HAUPTBETRIEB = BEWEGUNGSDATEN.CCK_BETRIEBSDATEN.LFBIS'
      
        'join BEWEGUNGSDATEN.KONTROLLBERICHT on BEWEGUNGSDATEN.KONTROLLBE' +
        'RICHT.ID = BEWEGUNGSDATEN.CCK_BETRIEBSDATEN.ID_KONTROLLBERICHT'
      
        'left join SYSTEMSTAMMDATEN.[USER] US_BEARBEITER  on BEWEGUNGSDAT' +
        'EN.CCK_AUFTRAG.ID_BEWERTER = US_BEARBEITER.ID'
      
        'left join SYSTEMSTAMMDATEN.[USER] US_BEWERTER on BEWEGUNGSDATEN.' +
        'CCK_AUFTRAG.ID_BEWERTER = US_BEWERTER.ID'
      
        'left join STAMMDATEN.PERSONEN BEARBEITER  on US_BEARBEITER .ID_P' +
        'ERSON = BEARBEITER .ID'
      
        'left join STAMMDATEN.PERSONEN BEWERTER on US_BEWERTER.ID_PERSON ' +
        '= BEWERTER.ID'
      'where BEWEGUNGSDATEN.CCK_AUFTRAG.ID = :ID_Auftrag'
      'and BEWEGUNGSDATEN.KONTROLLBERICHT.ID = :ID_KONTROLLBERICHT')
    Left = 208
    Top = 136
    ParamData = <
      item
        Name = 'ID_AUFTRAG'
        DataType = ftInteger
        ParamType = ptInput
        Value = 6
      end
      item
        Name = 'ID_KONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object ds_CCK_Auftrag: TDataSource
    DataSet = FDQueryCCK_Auftrag
    Left = 208
    Top = 187
  end
  object frxDBD_CCK_Auftrag: TfrxDBDataset
    UserName = 'FDQueryCCK_Auftrag'
    CloseDataSource = False
    FieldAliases.Strings = (
      'ID=ID'
      'AUFTRAGSJAHR=AUFTRAGSJAHR'
      'AUFTRAGSART=AUFTRAGSART'
      'LFBIS_HAUPTBETRIEB=LFBIS_HAUPTBETRIEB'
      'AUFTRAGSID=AUFTRAGSID'
      'BKB=BKB'
      'BBKNr=BBKNr'
      'BLDCODE=BLDCODE'
      'VORNAME=VORNAME'
      'NACHNAME=NACHNAME'
      'PLZ_BEW=PLZ_BEW'
      'ORT_BEW=ORT_BEW'
      'ADRESSE_BEW=ADRESSE_BEW'
      'GEMEINDEKZ_BEW=GEMEINDEKZ_BEW'
      'TEL_FESTNETZ=TEL_FESTNETZ'
      'FLAG_1=FLAG_1'
      'FLAG_2=FLAG_2'
      'FLAG_3=FLAG_3'
      'INFO_1=INFO_1'
      'INFO_2=INFO_2'
      'INFO_3=INFO_3'
      'ABGESCHLOSSEN_AM=ABGESCHLOSSEN_AM'
      'GUID_DOKUMENT=GUID_DOKUMENT'
      'ID_BEARBEITER=ID_BEARBEITER'
      'ID_BEWERTER=ID_BEWERTER'
      'LETZTE_RESTABFRAGE=LETZTE_RESTABFRAGE'
      'TSTAMP_INSERT=TSTAMP_INSERT'
      'TSTAMP_UPDATE=TSTAMP_UPDATE'
      'VERSION=VERSION'
      'ID_1=ID_1'
      'ID_CCK_AUFTRAG=ID_CCK_AUFTRAG'
      'BETRIEBSTYP=BETRIEBSTYP'
      'BETRIEBSART=BETRIEBSART'
      'LFBIS=LFBIS'
      'PLZ_BETR=PLZ_BETR'
      'ORT_BETR=ORT_BETR'
      'ADRESSE_BETR=ADRESSE_BETR'
      'GEMEINDEKZ_BETR=GEMEINDEKZ_BETR'
      'LN_FLAECHE=LN_FLAECHE'
      'TGD=TGD'
      'TIERHALTER=TIERHALTER'
      'ID_KONTROLLBERICHT=ID_KONTROLLBERICHT'
      'ID_BETRIEB=ID_BETRIEB'
      'ID_2=ID_2'
      'GUID=GUID'
      'BKB_1=BKB_1'
      'BKBTYP=BKBTYP'
      'KONTROLLTYP=KONTROLLTYP'
      'DATUM=DATUM'
      'ERFASSER_PSKEY=ERFASSER_PSKEY'
      'KONTROLLORGAN_PSKEY=KONTROLLORGAN_PSKEY'
      'ID_PERSON_ERFASSER=ID_PERSON_ERFASSER'
      'ID_PERSON_KONTROLLORGAN=ID_PERSON_KONTROLLORGAN'
      'REF_BKB=REF_BKB'
      'PROBENZIEHUNG=PROBENZIEHUNG'
      'ID_BETRIEB_1=ID_BETRIEB_1'
      'REGNR_ORT=REGNR_ORT'
      'KURZBEMERKUNG=KURZBEMERKUNG'
      'STARTZEIT=STARTZEIT'
      'ENDEZEIT=ENDEZEIT'
      'BESTAETIGT_UM=BESTAETIGT_UM'
      'ID_RECHTSGRUNDLAGE=ID_RECHTSGRUNDLAGE'
      'STATUS=STATUS'
      'ANGEMELDET_UM=ANGEMELDET_UM'
      'TSTAMP_INSERT_1=TSTAMP_INSERT_1'
      'LASTCHANGE=LASTCHANGE'
      'BETRIEBSTYP_1=BETRIEBSTYP_1'
      
        'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB=GUID_UNTERSCHRIFT_ANWESENDE' +
        'R_BETRIEB'
      'GUID_UNTERSCHRIFT_KONTROLLORGAN=GUID_UNTERSCHRIFT_KONTROLLORGAN'
      'VERWEIGERUNGSGRUND_UNTERSCHRIFT=VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      'Verweigerunggrund=Verweigerunggrund'
      'GUID_DOKUMENT_1=GUID_DOKUMENT_1'
      'FEHLERHAFT_GESETZT_AM=FEHLERHAFT_GESETZT_AM'
      'STORNIERT_AM=STORNIERT_AM'
      'STORNOGRUND=STORNOGRUND'
      'VERWEIGERT_AM=VERWEIGERT_AM'
      'TITEL_BEARB=TITEL_BEARB'
      'VORNAME_BEARB=VORNAME_BEARB'
      'NACHNAME_BEARB=NACHNAME_BEARB'
      'TITEL_BEWERT=TITEL_BEWERT'
      'VORNAME_BEWERT=VORNAME_BEWERT'
      'NACHNAME_BEWERT=NACHNAME_BEWERT')
    DataSet = FDQueryCCK_Auftrag
    BCDToCurrency = False
    DataSetOptions = []
    Left = 208
    Top = 243
  end
  object frxBundesland: TfrxDBDataset
    UserName = 'frxBundesland'
    CloseDataSource = False
    DataSet = Bundesland
    BCDToCurrency = False
    DataSetOptions = []
    Left = 326
    Top = 73
  end
  object Bundesland: TFDQuery
    ActiveStoredUsage = []
    Connection = Connection
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Bundeslaender '
      'WHERE  Bldcode = :bldcode;')
    Left = 326
    Top = 25
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
  end
  object frxRichObject1: TfrxRichObject
    Left = 516
    Top = 32
  end
  object FDQuery_Bewertungsdaten: TFDQuery
    ActiveStoredUsage = []
    MasterSource = ds_CCK_Auftrag
    MasterFields = 'ID'
    Connection = Connection
    FetchOptions.AssignedValues = [evCache]
    FetchOptions.Cache = [fiBlobs, fiMeta]
    SQL.Strings = (
      'select BEWEGUNGSDATEN.CCK_AUFTRAGSBEWERTUNG.*'
      'from BEWEGUNGSDATEN.CCK_AUFTRAG'
      '         join BEWEGUNGSDATEN.CCK_BETRIEBSDATEN'
      
        '              on BEWEGUNGSDATEN.CCK_BETRIEBSDATEN.ID_CCK_AUFTRAG' +
        ' = BEWEGUNGSDATEN.CCK_AUFTRAG.ID'
      '         join BEWEGUNGSDATEN.CCK_AUSWAHLDATEN'
      
        '              on BEWEGUNGSDATEN.CCK_AUSWAHLDATEN.ID_CCK_BETRIEBS' +
        'DATEN = BEWEGUNGSDATEN.CCK_BETRIEBSDATEN.ID'
      '         join BEWEGUNGSDATEN.CCK_AUFTRAGSBEWERTUNG'
      
        '              on BEWEGUNGSDATEN.CCK_AUFTRAGSBEWERTUNG.ID_AUSWAHL' +
        'DATEN = BEWEGUNGSDATEN.CCK_AUSWAHLDATEN.ID'
      '         join BEWEGUNGSDATEN.KONTROLLBERICHT'
      
        '              on BEWEGUNGSDATEN.KONTROLLBERICHT.ID = BEWEGUNGSDA' +
        'TEN.CCK_BETRIEBSDATEN.ID_KONTROLLBERICHT'
      'where BEWEGUNGSDATEN.CCK_AUFTRAG.ID = :ID'
      'order by Anforderung')
    Left = 344
    Top = 136
    ParamData = <
      item
        Name = 'ID'
        DataType = ftAutoInc
        ParamType = ptInput
        Value = 6
      end>
  end
  object frxDB_Bewertungsdaten: TfrxDBDataset
    UserName = 'FDQueryBewertungsdaten'
    CloseDataSource = False
    FieldAliases.Strings = (
      'ID=ID'
      'ID_AUSWAHLDATEN=ID_AUSWAHLDATEN'
      'Anforderung=Anforderung'
      'BewertetAm=BewertetAm'
      'Kontrolliert=Kontrolliert'
      'Ok=Ok'
      'Auffaellig=Auffaellig'
      'GerVerstossOk=GerVerstossOk'
      'Vorsatz=Vorsatz'
      'Ausmass=Ausmass'
      'Schwere=Schwere'
      'Dauer=Dauer'
      'Bemerkung=Bemerkung'
      'STATUS=STATUS'
      'KZ_HV=KZ_HV')
    DataSet = FDQuery_Bewertungsdaten
    BCDToCurrency = False
    DataSetOptions = []
    Left = 344
    Top = 243
  end
  object frxbewertungsblatt: TfrxReport
    Version = '2023.2.1'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick, pbCopy, pbSelection]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Default'
    PrintOptions.PrintOnSheet = 0
    ReportOptions.CreateDate = 44382.561478784700000000
    ReportOptions.LastChange = 45406.363855451390000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      ''
      
        'procedure FDQueryCCK_AuftragVERWEIGERT_AMOnBeforePrint(Sender: T' +
        'frxComponent);'
      'begin'
      '  if <FDQueryCCK_Auftrag."VERWEIGERT_AM"> = '#39'0'#39' then'
      '    FDQueryCCK_AuftragVERWEIGERT_AM.visible := false'
      '  else                  '
      '    FDQueryCCK_AuftragVERWEIGERT_AM.visible := true;  '
      '        '
      'end;'
      ''
      'procedure Memo22OnBeforePrint(Sender: TfrxComponent);'
      'begin'
      '  if<frxBundesland."BLDCODE"> = 100 then Memo22.text := '#39'Wien'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 200 then Memo22.text := '#39'Amt der' +
        ' Burgenl. Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 300 then Memo22.text := '#39'Amt der' +
        ' N'#214' Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 400 then Memo22.text := '#39'Amt der' +
        ' O'#214' Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 500 then Memo22.text := '#39'Amt der' +
        ' Salzb. Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 600 then Memo22.text := '#39'Amt der' +
        ' Tiroler Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 700 then Memo22.text := '#39'Amt der' +
        ' Vorarlberger Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 800 then Memo22.text := '#39'Amt der' +
        ' Stierm'#228'rkischen Landesregierung'#39';'
      
        '  if<frxBundesland."BLDCODE"> = 900 then Memo22.text := '#39'Amt der' +
        ' K'#228'rntner Landesregierung'#39';'
      'end;'
      ''
      'begin'
      ''
      'end.')
    Left = 56
    Top = 136
    Datasets = <
      item
        DataSet = frxDB_Bewertungsdaten
        DataSetName = 'FDQueryBewertungsdaten'
      end
      item
        DataSet = frxDBD_CCK_Auftrag
        DataSetName = 'FDQueryCCK_Auftrag'
      end
      item
        DataSet = frxBundesland
        DataSetName = 'frxBundesland'
      end
      item
        DataSet = frxDBD_Systemdaten
        DataSetName = 'Systemdaten'
      end>
    Variables = <>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      Orientation = poLandscape
      PaperWidth = 297.000000000000000000
      PaperHeight = 210.000000000000000000
      PaperSize = 9
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      Frame.Typ = []
      MirrorMode = []
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
        Height = 102.047310000000000000
        Top = 16.000000000000000000
        Width = 1046.929810000000000000
        object Memo2: TfrxMemoView
          AllowVectorExport = True
          Left = 7.559060000000000000
          Top = 26.456710000000000000
          Width = 635.527830000000000000
          Height = 52.913420000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -19
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = []
          Memo.UTF8W = (
            'Bewertungsblatt'
            'zur Vor-Ort-Kontrolle Konditionalit'#228't und Tierschutzgesetz')
          ParentFont = False
        end
        object Picture4: TfrxPictureView
          AllowVectorExport = True
          Left = 657.638220000000000000
          Width = 385.512060000000000000
          Height = 94.488250000000000000
          Center = True
          DataField = 'BLDLOGO'
          DataSet = frxBundesland
          DataSetName = 'frxBundesland'
          Frame.Typ = []
          HightQuality = False
          Transparent = False
          TransparentColor = clWhite
        end
      end
      object MasterData1: TfrxMasterData
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 181.417440000000000000
        Top = 180.000000000000000000
        Width = 1046.929810000000000000
        DataSet = frxDBD_CCK_Auftrag
        DataSetName = 'FDQueryCCK_Auftrag'
        RowCount = 0
        object FDQueryCCK_AuftragBBKNr: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 835.276130000000000000
          Top = 7.559059999999990000
          Width = 211.653680000000000000
          Height = 30.236240000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -24
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'BBK-NR: [FDQueryCCK_Auftrag."BBKNr"]')
          ParentFont = False
        end
        object FDQueryCCK_AuftragLFBIS_HAUPTBETRIEB: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 461.102660000000000000
          Top = 7.559059999999990000
          Width = 374.173470000000000000
          Height = 30.236240000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -24
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'Hauptbetriebs-Nr.: [FDQueryCCK_Auftrag."LFBIS_HAUPTBETRIEB"]')
          ParentFont = False
        end
        object FDQueryCCK_AuftragBKB: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 461.102660000000000000
          Top = 37.795300000000000000
          Width = 374.173470000000000000
          Height = 22.677180000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'BKB: [FDQueryCCK_Auftrag."BKB"]')
          ParentFont = False
        end
        object FDQueryCCK_AuftragABGESCHLOSSEN_AM: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 162.519790000000000000
          Top = 7.559059999999990000
          Width = 298.582870000000000000
          Height = 18.897650000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'Bewertung am: [FDQueryCCK_Auftrag."ABGESCHLOSSEN_AM"]')
          ParentFont = False
        end
        object FDQueryCCK_AuftragSTARTZEIT: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 34.015770000000000000
          Top = 60.472480000000000000
          Width = 427.086890000000000000
          Height = 18.897650000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          DisplayFormat.FormatStr = 'dd.mm.yyyy'
          DisplayFormat.Kind = fkDateTime
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'Datum der Kontrolle: [FDQueryCCK_Auftrag."ENDEZEIT"]')
          ParentFont = False
        end
        object FDQueryCCK_AuftragVERWEIGERT_AM: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 461.102660000000000000
          Top = 60.472480000000000000
          Width = 585.827150000000000000
          Height = 18.897650000000000000
          OnBeforePrint = 'FDQueryCCK_AuftragVERWEIGERT_AMOnBeforePrint'
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftBottom]
          Memo.UTF8W = (
            'Verweigerung: [FDQueryCCK_Auftrag."VERWEIGERT_AM"]')
          ParentFont = False
        end
        object Memo4: TfrxMemoView
          AllowVectorExport = True
          Left = 75.590600000000000000
          Top = 94.488250000000000000
          Width = 34.015748030000000000
          Height = 86.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'kontroliert')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo5: TfrxMemoView
          AllowVectorExport = True
          Top = 94.488250000000000000
          Width = 75.590600000000000000
          Height = 86.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Anforderung')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo6: TfrxMemoView
          AllowVectorExport = True
          Left = 109.606370000000000000
          Top = 94.488250000000000000
          Width = 34.015748030000000000
          Height = 86.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'ok')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo7: TfrxMemoView
          AllowVectorExport = True
          Left = 143.622140000000000000
          Top = 94.488250000000000000
          Width = 34.015748030000000000
          Height = 86.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'auff'#228'llig')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo8: TfrxMemoView
          AllowVectorExport = True
          Left = 177.637910000000000000
          Top = 94.488250000000000000
          Width = 34.015748030000000000
          Height = 86.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Gerinf'#252'giger'
            'Versto'#223' ok')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo9: TfrxMemoView
          AllowVectorExport = True
          Left = 211.653680000000000000
          Top = 113.385900000000000000
          Width = 34.015748030000000000
          Height = 68.031540000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Vorsatz')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object Memo10: TfrxMemoView
          AllowVectorExport = True
          Left = 211.653680000000000000
          Top = 94.488250000000000000
          Width = 831.496600000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = [fsBold]
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Bewertung Versto'#223' gem. '#167' 106 (1) GSP-AV')
          ParentFont = False
        end
        object Memo11: TfrxMemoView
          AllowVectorExport = True
          Left = 245.669450000000000000
          Top = 113.385900000000000000
          Width = 98.267780000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Ausma'#223)
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo12: TfrxMemoView
          AllowVectorExport = True
          Left = 245.669450000000000000
          Top = 132.283550000000000000
          Width = 98.267780000000000000
          Height = 49.133890000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'innerbetrieblich (1)'
            'betriebs'#252'bergr. (3)'
            'regional (5)')
          ParentFont = False
        end
        object Memo17: TfrxMemoView
          AllowVectorExport = True
          Left = 343.937230000000000000
          Top = 113.385900000000000000
          Width = 98.267780000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Schwere')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo18: TfrxMemoView
          AllowVectorExport = True
          Left = 343.937230000000000000
          Top = 132.283550000000000000
          Width = 98.267780000000000000
          Height = 49.133890000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'leicht (1)'
            'mittel (3)'
            'schwer (5, 10)')
          ParentFont = False
        end
        object Memo19: TfrxMemoView
          AllowVectorExport = True
          Left = 442.205010000000000000
          Top = 113.385900000000000000
          Width = 98.267780000000000000
          Height = 18.897650000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Dauer')
          ParentFont = False
          VAlign = vaCenter
        end
        object Memo20: TfrxMemoView
          AllowVectorExport = True
          Left = 442.205010000000000000
          Top = 132.283550000000000000
          Width = 98.267780000000000000
          Height = 49.133890000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'kurzfristig (1)'
            'dauerhaft (5)')
          ParentFont = False
        end
        object Memo21: TfrxMemoView
          AllowVectorExport = True
          Left = 569.472790000000000000
          Top = 113.385900000000000000
          Width = 474.677490000000000000
          Height = 68.031540000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Bemerkungen')
          ParentFont = False
          VAlign = vaCenter
        end
        object FDQueryCCK_AuftragTITEL_BEWERT: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 162.519790000000000000
          Top = 26.456710000000000000
          Width = 298.582870000000000000
          Height = 34.015770000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -13
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'Bewertet von: '
            
              '[FDQueryCCK_Auftrag."TITEL_BEWERT"] [FDQueryCCK_Auftrag."VORNAME' +
              '_BEWERT"] [FDQueryCCK_Auftrag."NACHNAME_BEWERT"]')
          ParentFont = False
          Formats = <
            item
            end
            item
            end>
        end
        object Memo23: TfrxMemoView
          AllowVectorExport = True
          Top = 7.559059999999990000
          Width = 34.015748030000000000
          Height = 71.811070000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          HAlign = haCenter
          Memo.UTF8W = (
            'Bearbeitung')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
        object md_version: TfrxMemoView
          AllowVectorExport = True
          Left = 835.276130000000000000
          Top = 37.795300000000000000
          Width = 211.653680000000000000
          Height = 22.677180000000000000
          DataSet = frxDBD_CCK_Auftrag
          DataSetName = 'FDQueryCCK_Auftrag'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -16
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
          Memo.UTF8W = (
            'Version: [FDQueryCCK_Auftrag."VERSION"]')
          ParentFont = False
        end
        object Memo24: TfrxMemoView
          AllowVectorExport = True
          Left = 35.000000000000000000
          Top = 7.559059999999990000
          Width = 128.000000000000000000
          Height = 16.000000000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftTop]
          Memo.UTF8W = (
            'Bewertende Beh'#246'rde:')
          ParentFont = False
        end
        object Memo22: TfrxMemoView
          AllowVectorExport = True
          Left = 36.000000000000000000
          Top = 20.000000000000000000
          Width = 124.000000000000000000
          Height = 40.000000000000000000
          OnBeforePrint = 'Memo22OnBeforePrint'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          ParentFont = False
          WordBreak = True
        end
        object Memo25: TfrxMemoView
          AllowVectorExport = True
          Left = 540.000000000000000000
          Top = 114.000000000000000000
          Width = 30.015748030000000000
          Height = 66.929190000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            'historisch')
          ParentFont = False
          Rotation = 90
          VAlign = vaCenter
        end
      end
      object PageFooter1: TfrxPageFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 37.795300000000000000
        Top = 672.000000000000000000
        Width = 1046.929810000000000000
        object Memo14: TfrxMemoView
          AllowVectorExport = True
          Left = 3.779530000000000000
          Width = 188.976500000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -8
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          Memo.UTF8W = (
            'EsCulenta ELKE System'
            'www.esculenta.at'
            '[Systemdaten."ELKEversion"]')
          ParentFont = False
        end
        object Memo15: TfrxMemoView
          AllowVectorExport = True
          Left = 411.968770000000000000
          Width = 219.212740000000000000
          Height = 34.015770000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -8
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          HAlign = haCenter
          Memo.UTF8W = (
            'Seite [Page#]/[TotalPages#]'
            '[frxBundesland."BEZEICHNUNG"]'
            'AMA-Konditionalit'#228't  Bewertungsblatt')
          ParentFont = False
          Formats = <
            item
            end
            item
            end
            item
            end>
        end
        object Memo16: TfrxMemoView
          AllowVectorExport = True
          Left = 854.173780000000000000
          Top = 3.779530000000022000
          Width = 188.976500000000000000
          Height = 34.015770000000010000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -8
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          HAlign = haRight
          Memo.UTF8W = (
            '[Date] [Time]'
            '[Systemdaten."AngemeldeterUser"]'
            'Reportversion 1.0.6')
          ParentFont = False
          Formats = <
            item
            end
            item
            end
            item
            end>
        end
      end
      object Footer1: TfrxFooter
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 192.756030000000000000
        Top = 420.000000000000000000
        Width = 1046.929810000000000000
        object Rich1: TfrxRichView
          AllowVectorExport = True
          Left = 3.779530000000000000
          Top = 3.779530000000020000
          Width = 279.685220000000000000
          Height = 173.858380000000000000
          Frame.Typ = []
          GapX = 2.000000000000000000
          GapY = 1.000000000000000000
          RichEdit = {
            7B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F
            7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66
            305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C
            67656E657261746F722052696368656432302031302E302E31393034317D5C76
            6965776B696E64345C756331200D0A5C706172645C625C66305C66733136204C
            69737465206D6974204D6F64756C2D2F416E666F72646572756E67736E756D6D
            65723A5C62305C7061720D0A5C7061720D0A5C756C204D6F64756C204C4D5320
            2D204C6562656E7320756E64204675747465726D697474656C73696368657268
            6569745C756C6E6F6E655C7061720D0A4C4D532E335C74616220456967656E6B
            6F6E74726F6C6C65202D204172742E2031375C7061720D0A4C4D532E345C7461
            6220525C276663636B766572666F6C676261726B6569742028446F6B752E2920
            2D204172742E2031385C7061720D0A4C4D532E355C74616220525C276663636B
            686F6C756E6720756E6420496E666F726D6174696F6E202D204172742E203139
            20756E642032305C7061720D0A5C7061720D0A5C756C204D6F64756C20484F52
            202D20486F726D6F6E616E77656E64756E6773766572626F7420756E64205469
            657261727A6E65696D697474656C616E77656E64756E675C7061720D0A5C756C
            6E6F6E6520484F522E315C74616220486F726D6F6E616E77656E64756E675C70
            61720D0A54414D2E325C746162205469657261727A6E65696D697474656C616E
            77656E64756E6720692E20532E20642E204C4D535C7061720D0A7D0D0A00}
        end
        object Rich2: TfrxRichView
          AllowVectorExport = True
          Left = 275.905690000000000000
          Top = 3.779530000000020000
          Width = 249.448980000000000000
          Height = 188.976500000000000000
          Frame.Typ = []
          GapX = 2.000000000000000000
          GapY = 1.000000000000000000
          RichEdit = {
            7B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F
            7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66
            305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C
            67656E657261746F722052696368656432302031302E302E31393034317D5C76
            6965776B696E64345C756331200D0A5C706172645C756C5C66305C6673313620
            4D6F64756C20464D202D204675747465726D697474656C20696E6B6C2E205469
            65726D65686C766572665C27666374746572756E675C756C6E6F6E655C706172
            0D0A464D2E315C746162204675747465726D697474656C5C7061720D0A464D2E
            325C74616220546965726D65686C766572665C27666374746572756E675C7061
            720D0A5C7061720D0A5C756C204D6F64756C2054534B45202D204B5C2765346C
            62657273636875747A72696368746C696E69655C756C6E6F6E655C7061720D0A
            54534B414520315C746162204B6F6E74726F6C6C656E5C7061720D0A54534B41
            4520325C746162204265776567756E677366726569686569745C7061720D0A54
            534B414520335C7461622042657361747A6469636874655C7061720D0A54534B
            414520345C746162204765625C27653475646520756E6420556E746572627269
            6E67756E675C7061720D0A54534B414520355C746162204D696E64657362656C
            6575636874756E675C7061720D0A54534B414520365C746162204175746F6D61
            74697363686520756E64206D656368616E697363686520416E6C6167656E5C70
            61720D0A54534B414520375C74616220465C276663747465726E2C2054725C27
            65346E6B656E20756E64206265696765665C2766636774652053746F6666655C
            7061720D0A54534B414520385C74616220485C2765346D6F676C6F62696E7765
            72745C7061720D0A54534B414520395C74616220466173657268616C74696765
            73205261756675747465725C7061720D0A7D0D0A00}
        end
        object Rich3: TfrxRichView
          AllowVectorExport = True
          Left = 529.134200000000000000
          Top = 3.779530000000020000
          Width = 249.448980000000000000
          Height = 188.976500000000000000
          Frame.Typ = []
          GapX = 2.000000000000000000
          GapY = 1.000000000000000000
          RichEdit = {
            7B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F
            7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66
            305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C
            67656E657261746F722052696368656432302031302E302E31393034317D5C76
            6965776B696E64345C756331200D0A5C706172645C756C5C66305C6673313620
            4D6F64756C2054535357202D205363687765696E6573636875747A7269636874
            6C696E69655C756C6E6F6E655C7061720D0A5453535720315C74616220506572
            736F6E616C5C7061720D0A5453535720325C746162204B6F6E74726F6C6C656E
            5C7061720D0A5453535720335C746162204265776567756E6773667265696865
            69745C7061720D0A5453535720345C7461622042657361747A6469636874655C
            7061720D0A5453535720355C746162204765625C27653475646520756E642055
            6E7465726272696E67756E675C7061720D0A5453535720365C746162204D696E
            64657362656C6575636874756E675C7061720D0A5453535720375C7461622042
            5C27663664656E5C7061720D0A5453535720385C7461622045696E7374726575
            5C7061720D0A5453535720395C74616220465C276663747465726E2C2054725C
            2765346E6B656E20756E64206265696765665C2766636774652053746F666665
            5C7061720D0A545353572031305C74616220466173657268616C746967657320
            5261756675747465725C7061720D0A545353572031315C746162205665727374
            5C2766636D6D656C756E67656E2F45696E6772696666655C7061720D0A545353
            572031325C746162205A756368746D6574686F64656E5C7061720D0A7D0D0A00}
        end
        object Rich4: TfrxRichView
          AllowVectorExport = True
          Left = 774.803650000000000000
          Top = 3.779530000000020000
          Width = 249.448980000000000000
          Height = 188.976500000000000000
          Frame.Typ = []
          GapX = 2.000000000000000000
          GapY = 1.000000000000000000
          RichEdit = {
            7B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F
            7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66
            305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C
            67656E657261746F722052696368656432302031302E302E31393034317D5C76
            6965776B696E64345C756331200D0A5C706172645C756C5C66305C6673313620
            4D6F64756C2054534E54202D20524C207A756D2053636875747A206C772E204E
            75747A74696572655C756C6E6F6E655C7061720D0A54534E5420315C74616220
            506572736F6E616C5C7061720D0A54534E5420325C746162204B6F6E74726F6C
            6C656E5C7061720D0A54534E5420335C746162204175667A656963686E756E67
            656E5C7061720D0A54534E5420345C746162204265776567756E677366726569
            686569745C7061720D0A54534E5420355C746162204765625C27653475646520
            756E6420556E7465726272696E67756E675C7061720D0A54534E5420365C7461
            62204175746F6D61746973636865206F646572206D656361686E697363686520
            416E6C6167656E5C7061720D0A54534E5420375C74616220465C276663747465
            726E2C2054725C2765346E6B656E20756E64206265696765665C276663677465
            2053746F6666655C7061720D0A54534E5420382020202056657273745C276663
            6D6D656C756E67656E202F2045696E6772696666655C7061720D0A54534E5420
            395C746162205A756368746D6574686F64656E5C7061720D0A7D0D0A00}
        end
      end
      object DetailData1: TfrxDetailData
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = [ftLeft, ftRight, ftTop, ftBottom]
        Height = 18.897650000000000000
        Top = 380.000000000000000000
        Width = 1046.929810000000000000
        DataSet = frxDB_Bewertungsdaten
        DataSetName = 'FDQueryBewertungsdaten'
        RowCount = 0
        Stretched = True
        object FDQueryBewertungsdatenAnforderung: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Width = 75.590600000000000000
          Height = 18.897650000000000000
          DataField = 'Anforderung'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight]
          Memo.UTF8W = (
            '[FDQueryBewertungsdaten."Anforderung"]')
          ParentFont = False
        end
        object FDQueryBewertungsdatenAusmass: TfrxMemoView
          IndexTag = 1
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 245.669450000000000000
          Width = 98.267780000000000000
          Height = 18.897650000000000000
          DataField = 'Ausmass'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight]
          HAlign = haCenter
          Memo.UTF8W = (
            '[FDQueryBewertungsdaten."Ausmass"]')
          ParentFont = False
        end
        object FDQueryBewertungsdatenSchwere: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 343.937230000000000000
          Width = 98.267716540000000000
          Height = 18.897650000000000000
          DataField = 'Schwere'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight]
          HAlign = haCenter
          Memo.UTF8W = (
            '[FDQueryBewertungsdaten."Schwere"]')
          ParentFont = False
        end
        object FDQueryBewertungsdatenDauer: TfrxMemoView
          IndexTag = 1
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 442.205010000000000000
          Width = 98.267716540000000000
          Height = 18.897650000000000000
          DataField = 'Dauer'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft, ftRight]
          HAlign = haCenter
          Memo.UTF8W = (
            '[FDQueryBewertungsdaten."Dauer"]')
          ParentFont = False
        end
        object FDQueryBewertungsdatenBemerkung: TfrxMemoView
          IndexTag = 1
          AllowVectorExport = True
          Left = 569.472790000000000000
          Width = 474.677490000000000000
          Height = 18.897650000000000000
          StretchMode = smActualHeight
          DataField = 'Bemerkung'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -11
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = [ftLeft]
          Memo.UTF8W = (
            '[FDQueryBewertungsdaten."Bemerkung"]')
          ParentFont = False
        end
        object CheckBox1: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 75.590600000000000000
          Width = 34.015770000000010000
          Height = 18.897650000000000000
          Editable = []
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'Kontrolliert'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = [ftLeft, ftRight]
        end
        object CheckBox2: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          ShiftMode = smDontShift
          Left = 109.606370000000000000
          Width = 34.015770000000000000
          Height = 18.897650000000000000
          Editable = []
          Restrictions = [rfDontSize]
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'Ok'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = [ftLeft, ftRight]
        end
        object CheckBox3: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 143.622140000000000000
          Width = 34.015748030000000000
          Height = 18.897650000000000000
          Editable = []
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'Auffaellig'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = [ftLeft, ftRight]
        end
        object CheckBox4: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 177.637910000000000000
          Width = 34.015748030000000000
          Height = 18.897650000000000000
          Editable = []
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'GerVerstossOk'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = [ftLeft, ftRight]
        end
        object CheckBox5: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 211.653680000000000000
          Width = 34.015748030000000000
          Height = 18.897650000000000000
          Editable = []
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'Vorsatz'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = [ftLeft, ftRight]
        end
        object CheckBox6: TfrxCheckBoxView
          Anchors = [fraLeft, fraTop, fraBottom]
          AllowVectorExport = True
          Left = 540.000000000000000000
          Width = 30.015748030000000000
          Height = 18.897650000000000000
          Editable = []
          CheckColor = clBlack
          CheckStyle = csCross
          DataField = 'KZ_HV'
          DataSet = frxDB_Bewertungsdaten
          DataSetName = 'FDQueryBewertungsdaten'
          Frame.Typ = []
        end
      end
    end
  end
  object Connection: TFDConnection
    Params.Strings = (
      'Database=ELKEDB'
      'User_Name=sa'
      'Server=SQL1ESC'
      'Password=ESA1234!'
      'DriverID=MSSQL')
    TxOptions.Isolation = xiSnapshot
    ConnectedStoredUsage = []
    LoginPrompt = False
    BeforeConnect = ConnectionBeforeConnect
    Left = 48
    Top = 24
  end
  object QU_sucheKontrollbericht: TFDQuery
    ActiveStoredUsage = []
    Connection = Connection
    SQL.Strings = (
      'select ID_KONTROLLBERICHT from BEWEGUNGSDATEN.CCK_AUFTRAG'
      
        'join BEWEGUNGSDATEN.CCK_BETRIEBSDATEN on BEWEGUNGSDATEN.CCK_BETR' +
        'IEBSDATEN.ID_CCK_AUFTRAG = BEWEGUNGSDATEN.CCK_AUFTRAG.ID'
      
        'where BEWEGUNGSDATEN.CCK_BETRIEBSDATEN.ID_CCK_AUFTRAG = :ID_AUFT' +
        'RAG and BETRIEBSTYP = '#39'HB'#39
      '')
    Left = 464
    Top = 136
    ParamData = <
      item
        Name = 'ID_AUFTRAG'
        DataType = ftInteger
        ParamType = ptInput
        Value = 6
      end>
  end
end
