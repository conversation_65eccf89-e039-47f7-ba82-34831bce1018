<?xml version="1.0" encoding="utf-8" standalone="no"?>
<TfrxReport Version="6.9.6" DotMatrixReport="False" IniFile="\Software\Fast Reports" PreviewOptions.Buttons="167935" PreviewOptions.Zoom="1" PrintOptions.Printer="Default" PrintOptions.PrintOnSheet="0" ReportOptions.CreateDate="44382,5614787847" ReportOptions.Description.Text="" ReportOptions.LastChange="44621,7288265278" ScriptLanguage="PascalScript" ScriptText.Text="&#13;&#10;procedure FDQueryCCK_AuftragVERWEIGERT_AMOnBeforePrint(Sender: TfrxComponent);&#13;&#10;begin&#13;&#10;  if &#60;FDQueryCCK_Auftrag.&#34;VERWEIGERT_AM&#34;&#62; = '0' then&#13;&#10;    FDQueryCCK_AuftragVERWEIGERT_AM.visible := false&#13;&#10;  else&#13;&#10;    FDQueryCCK_AuftragVERWEIGERT_AM.visible := true;&#13;&#10;&#13;&#10;end;&#13;&#10;&#13;&#10;begin&#13;&#10;&#13;&#10;end.">
  <Datasets>
    <item DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten"/>
    <item DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag"/>
    <item DataSet="frxBundesland" DataSetName="frxBundesland"/>
    <item DataSet="frxDBD_Systemdaten" DataSetName="Systemdaten"/>
  </Datasets>
  <TfrxDataPage Name="Data" HGuides.Text="" VGuides.Text="" Height="1000" Left="0" Top="0" Width="1000"/>
  <TfrxReportPage Name="Page1" HGuides.Text="" VGuides.Text="" Orientation="poLandscape" PaperWidth="297" PaperHeight="210" PaperSize="9" LeftMargin="10" RightMargin="10" TopMargin="10" BottomMargin="10" ColumnWidth="0" ColumnPositions.Text="" Frame.Typ="0" MirrorMode="0">
    <TfrxReportTitle Name="ReportTitle1" FillType="ftBrush" FillGap.Top="0" FillGap.Left="0" FillGap.Bottom="0" FillGap.Right="0" Frame.Typ="15" Height="102,04731" Left="0" Top="18,89765" Width="1046,92981">
      <TfrxMemoView Name="Memo2" AllowVectorExport="True" Left="7,55906" Top="26,45671" Width="419,52783" Height="52,91342" Font.Charset="1" Font.Color="0" Font.Height="-19" Font.Name="Arial" Font.Style="1" Frame.Typ="0" ParentFont="False" Text="Bewertungsblatt&#13;&#10;zur Vor-Ort-Kontrolle Cross Compliance"/>
      <TfrxPictureView Name="Picture4" AllowVectorExport="True" Left="657,63822" Top="0" Width="385,51206" Height="94,48825" Center="True" DataField="BLDLOGO" DataSet="frxBundesland" DataSetName="frxBundesland" Frame.Typ="0" HightQuality="False" Transparent="False" TransparentColor="16777215"/>
    </TfrxReportTitle>
    <TfrxMasterData Name="MasterData1" FillType="ftBrush" FillGap.Top="0" FillGap.Left="0" FillGap.Bottom="0" FillGap.Right="0" Frame.Typ="0" Height="181,41744" Left="0" Top="181,41744" Width="1046,92981" ColumnWidth="0" ColumnGap="0" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" RowCount="0">
      <TfrxMemoView Name="FDQueryCCK_AuftragBBKNr" IndexTag="1" AllowVectorExport="True" Left="835,27613" Top="7,55905999999999" Width="211,65368" Height="30,23624" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-24" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="BBK-NR: [FDQueryCCK_Auftrag.&#34;BBKNr&#34;]"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragLFBIS_HAUPTBETRIEB" IndexTag="1" AllowVectorExport="True" Left="461,10266" Top="7,55905999999999" Width="374,17347" Height="30,23624" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-24" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="Hauptbetriebs-Nr.: [FDQueryCCK_Auftrag.&#34;LFBIS_HAUPTBETRIEB&#34;]"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragBKB" IndexTag="1" AllowVectorExport="True" Left="461,10266" Top="37,7953" Width="374,17347" Height="22,67718" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-16" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="BKB: [FDQueryCCK_Auftrag.&#34;BKB&#34;]"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragABGESCHLOSSEN_AM" IndexTag="1" AllowVectorExport="True" Left="162,51979" Top="7,55905999999999" Width="298,58287" Height="18,89765" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-13" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="Bewertung am: [FDQueryCCK_Auftrag.&#34;ABGESCHLOSSEN_AM&#34;]"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragSTARTZEIT" IndexTag="1" AllowVectorExport="True" Left="34,01577" Top="60,47248" Width="427,08689" Height="18,89765" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" DisplayFormat.FormatStr="dd.mm.yyyy" DisplayFormat.Kind="fkDateTime" Font.Charset="1" Font.Color="0" Font.Height="-13" Font.Name="Arial" Font.Style="0" Frame.Typ="11" ParentFont="False" Text="Datum der Kontrolle: [FDQueryCCK_Auftrag.&#34;ENDEZEIT&#34;]"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragVERWEIGERT_AM" IndexTag="1" AllowVectorExport="True" Left="461,10266" Top="60,47248" Width="585,82715" Height="18,89765" OnBeforePrint="FDQueryCCK_AuftragVERWEIGERT_AMOnBeforePrint" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-13" Font.Name="Arial" Font.Style="1" Frame.Typ="11" ParentFont="False" Text="Verweigerung: [FDQueryCCK_Auftrag.&#34;VERWEIGERT_AM&#34;]"/>
      <TfrxMemoView Name="Memo4" AllowVectorExport="True" Left="75,5906" Top="94,48825" Width="34,01574803" Height="86,92919" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="kontroliert"/>
      <TfrxMemoView Name="Memo5" AllowVectorExport="True" Left="0" Top="94,48825" Width="75,5906" Height="86,92919" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="Anforderung"/>
      <TfrxMemoView Name="Memo6" AllowVectorExport="True" Left="109,60637" Top="94,48825" Width="34,01574803" Height="86,92919" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="ok"/>
      <TfrxMemoView Name="Memo7" AllowVectorExport="True" Left="143,62214" Top="94,48825" Width="34,01574803" Height="86,92919" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="auffällig"/>
      <TfrxMemoView Name="Memo8" AllowVectorExport="True" Left="177,63791" Top="94,48825" Width="34,01574803" Height="86,92919" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="Gerinfügiger&#13;&#10;Verstoß ok"/>
      <TfrxMemoView Name="Memo9" AllowVectorExport="True" Left="211,65368" Top="113,3859" Width="34,01574803" Height="68,03154" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="Vorsatz"/>
      <TfrxMemoView Name="Memo10" AllowVectorExport="True" Left="211,65368" Top="94,48825" Width="831,4966" Height="18,89765" Font.Charset="1" Font.Color="0" Font.Height="-16" Font.Name="Arial" Font.Style="1" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="Bewertung Verstoß gem. VO (EU) Nr. 809/2014, Art. 72 (1) c"/>
      <TfrxMemoView Name="Memo11" AllowVectorExport="True" Left="245,66945" Top="113,3859" Width="98,26778" Height="18,89765" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" VAlign="vaCenter" Text="Ausmaß"/>
      <TfrxMemoView Name="Memo12" AllowVectorExport="True" Left="245,66945" Top="132,28355" Width="98,26778" Height="49,13389" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="innerbetrieblich (1)&#13;&#10;betriebsübergr. (3)&#13;&#10;regional (5)"/>
      <TfrxMemoView Name="Memo17" AllowVectorExport="True" Left="343,93723" Top="113,3859" Width="98,26778" Height="18,89765" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" VAlign="vaCenter" Text="Schwere"/>
      <TfrxMemoView Name="Memo18" AllowVectorExport="True" Left="343,93723" Top="132,28355" Width="98,26778" Height="49,13389" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="leicht (1)&#13;&#10;mittel (3)&#13;&#10;schwer (5)"/>
      <TfrxMemoView Name="Memo19" AllowVectorExport="True" Left="442,20501" Top="113,3859" Width="98,26778" Height="18,89765" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" VAlign="vaCenter" Text="Dauer"/>
      <TfrxMemoView Name="Memo20" AllowVectorExport="True" Left="442,20501" Top="132,28355" Width="98,26778" Height="49,13389" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="kurzfristig (1)&#13;&#10;dauerhaft (5)"/>
      <TfrxMemoView Name="Memo21" AllowVectorExport="True" Left="540,47279" Top="113,3859" Width="502,67749" Height="68,03154" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" VAlign="vaCenter" Text="Bemerkungen"/>
      <TfrxMemoView Name="FDQueryCCK_AuftragTITEL_BEWERT" IndexTag="1" AllowVectorExport="True" Left="162,51979" Top="26,45671" Width="298,58287" Height="34,01577" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-13" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="Bewertet von: &#13;&#10;[FDQueryCCK_Auftrag.&#34;TITEL_BEWERT&#34;] [FDQueryCCK_Auftrag.&#34;VORNAME_BEWERT&#34;] [FDQueryCCK_Auftrag.&#34;NACHNAME_BEWERT&#34;]">
        <Formats>
          <item/>
          <item/>
        </Formats>
      </TfrxMemoView>
      <TfrxMemoView Name="Memo22" AllowVectorExport="True" Left="34,01577" Top="7,55905999999999" Width="128,50402" Height="52,91342" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="Bewertende Behörde:&#13;&#10;Amt der NOE Landesregierung&#13;&#10;Abteilung Veterinärangelegenheiten und&#13;&#10;Lebensmittelkontrolle"/>
      <TfrxMemoView Name="Memo23" AllowVectorExport="True" Left="0" Top="7,55905999999999" Width="34,01574803" Height="71,81107" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Rotation="90" VAlign="vaCenter" Text="Bearbeitung"/>
      <TfrxMemoView Name="md_version" AllowVectorExport="True" Left="835,27613" Top="37,7953" Width="211,65368" Height="22,67718" DataSet="frxDBD_CCK_Auftrag" DataSetName="FDQueryCCK_Auftrag" Font.Charset="1" Font.Color="0" Font.Height="-16" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="Version: [FDQueryCCK_Auftrag.&#34;VERSION&#34;]"/>
    </TfrxMasterData>
    <TfrxPageFooter Name="PageFooter1" FillType="ftBrush" FillGap.Top="0" FillGap.Left="0" FillGap.Bottom="0" FillGap.Right="0" Frame.Typ="0" Height="37,7953" Left="0" Top="680,3154" Width="1046,92981">
      <TfrxMemoView Name="Memo14" AllowVectorExport="True" Left="3,77953" Top="0" Width="188,9765" Height="34,01577" Font.Charset="1" Font.Color="0" Font.Height="-8" Font.Name="Arial" Font.Style="0" Frame.Typ="0" ParentFont="False" Text="EsCulenta ELKE System&#13;&#10;www.esculenta.at&#13;&#10;[Systemdaten.&#34;ELKEversion&#34;]"/>
      <TfrxMemoView Name="Memo15" AllowVectorExport="True" Left="411,96877" Top="0" Width="219,21274" Height="34,01577" Font.Charset="1" Font.Color="0" Font.Height="-8" Font.Name="Arial" Font.Style="0" Frame.Typ="0" HAlign="haCenter" ParentFont="False" Text="Seite [Page#]/[TotalPages#]&#13;&#10;[frxBundesland.&#34;BEZEICHNUNG&#34;]&#13;&#10;AMA-CCK Bewertungsblatt">
        <Formats>
          <item/>
          <item/>
          <item/>
        </Formats>
      </TfrxMemoView>
      <TfrxMemoView Name="Memo16" AllowVectorExport="True" Left="854,17378" Top="3,77953000000002" Width="188,9765" Height="34,01577" Font.Charset="1" Font.Color="0" Font.Height="-8" Font.Name="Arial" Font.Style="0" Frame.Typ="0" HAlign="haRight" ParentFont="False" Text="[Date] [Time]&#13;&#10;[Systemdaten.&#34;AngemeldeterUser&#34;]&#13;&#10;Reportversion 1.0.3">
        <Formats>
          <item/>
          <item/>
          <item/>
        </Formats>
      </TfrxMemoView>
    </TfrxPageFooter>
    <TfrxFooter Name="Footer1" FillType="ftBrush" FillGap.Top="0" FillGap.Left="0" FillGap.Bottom="0" FillGap.Right="0" Frame.Typ="0" Height="192,75603" Left="0" Top="427,08689" Width="1046,92981">
      <TfrxRichView Name="Rich1" AllowVectorExport="True" Left="3,77953" Top="3,77953000000002" Width="279,68522" Height="173,85838" Frame.Typ="0" GapX="2" GapY="1" PropData="0852696368456469740A5E0200007B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C67656E657261746F722052696368656432302031302E302E31393034317D5C766965776B696E64345C756331200D0A5C706172645C625C66305C66733136204C69737465206D6974204D6F64756C2D2F416E666F72646572756E67736E756D6D65723A5C62305C7061720D0A5C7061720D0A5C756C204D6F64756C204C4D53202D204C6562656E7320756E64204675747465726D697474656C736963686572686569745C756C6E6F6E655C7061720D0A4C4D532E335C74616220456967656E6B6F6E74726F6C6C65202D204172742E2031375C7061720D0A4C4D532E345C74616220525C276663636B766572666F6C676261726B6569742028446F6B752E29202D204172742E2031385C7061720D0A4C4D532E355C74616220525C276663636B686F6C756E6720756E6420496E666F726D6174696F6E202D204172742E20313920756E642032305C7061720D0A5C7061720D0A5C756C204D6F64756C20484F52202D20486F726D6F6E616E77656E64756E6773766572626F7420756E64205469657261727A6E65696D697474656C616E77656E64756E675C7061720D0A5C756C6E6F6E6520484F522E315C74616220486F726D6F6E616E77656E64756E675C7061720D0A54414D2E325C746162205469657261727A6E65696D697474656C616E77656E64756E6720692E20532E20642E204C4D535C7061720D0A7D0D0A00"/>
      <TfrxRichView Name="Rich2" AllowVectorExport="True" Left="275,90569" Top="3,77953000000002" Width="249,44898" Height="188,9765" Frame.Typ="0" GapX="2" GapY="1" PropData="0852696368456469740AD50200007B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C67656E657261746F722052696368656432302031302E302E31393034317D5C766965776B696E64345C756331200D0A5C706172645C756C5C66305C66733136204D6F64756C20464D202D204675747465726D697474656C20696E6B6C2E20546965726D65686C766572665C27666374746572756E675C756C6E6F6E655C7061720D0A464D2E315C746162204675747465726D697474656C5C7061720D0A464D2E325C74616220546965726D65686C766572665C27666374746572756E675C7061720D0A5C7061720D0A5C756C204D6F64756C2054534B45202D204B5C2765346C62657273636875747A72696368746C696E69655C756C6E6F6E655C7061720D0A54534B414520315C746162204B6F6E74726F6C6C656E5C7061720D0A54534B414520325C746162204265776567756E677366726569686569745C7061720D0A54534B414520335C7461622042657361747A6469636874655C7061720D0A54534B414520345C746162204765625C27653475646520756E6420556E7465726272696E67756E675C7061720D0A54534B414520355C746162204D696E64657362656C6575636874756E675C7061720D0A54534B414520365C746162204175746F6D6174697363686520756E64206D656368616E697363686520416E6C6167656E5C7061720D0A54534B414520375C74616220465C276663747465726E2C2054725C2765346E6B656E20756E64206265696765665C2766636774652053746F6666655C7061720D0A54534B414520385C74616220485C2765346D6F676C6F62696E776572745C7061720D0A54534B414520395C74616220466173657268616C7469676573205261756675747465725C7061720D0A7D0D0A00"/>
      <TfrxRichView Name="Rich3" AllowVectorExport="True" Left="529,1342" Top="3,77953000000002" Width="249,44898" Height="188,9765" Frame.Typ="0" GapX="2" GapY="1" PropData="0852696368456469740A800200007B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C67656E657261746F722052696368656432302031302E302E31393034317D5C766965776B696E64345C756331200D0A5C706172645C756C5C66305C66733136204D6F64756C2054535357202D205363687765696E6573636875747A72696368746C696E69655C756C6E6F6E655C7061720D0A5453535720315C74616220506572736F6E616C5C7061720D0A5453535720325C746162204B6F6E74726F6C6C656E5C7061720D0A5453535720335C746162204265776567756E677366726569686569745C7061720D0A5453535720345C7461622042657361747A6469636874655C7061720D0A5453535720355C746162204765625C27653475646520756E6420556E7465726272696E67756E675C7061720D0A5453535720365C746162204D696E64657362656C6575636874756E675C7061720D0A5453535720375C74616220425C27663664656E5C7061720D0A5453535720385C7461622045696E73747265755C7061720D0A5453535720395C74616220465C276663747465726E2C2054725C2765346E6B656E20756E64206265696765665C2766636774652053746F6666655C7061720D0A545353572031305C74616220466173657268616C7469676573205261756675747465725C7061720D0A545353572031315C7461622056657273745C2766636D6D656C756E67656E2F45696E6772696666655C7061720D0A545353572031325C746162205A756368746D6574686F64656E5C7061720D0A7D0D0A00"/>
      <TfrxRichView Name="Rich4" AllowVectorExport="True" Left="774,80365" Top="3,77953000000002" Width="249,44898" Height="188,9765" Frame.Typ="0" GapX="2" GapY="1" PropData="0852696368456469740A0F0200007B5C727466315C616E73695C616E7369637067313235325C64656666305C6E6F7569636F6D7061745C6465666C616E67313033317B5C666F6E7474626C7B5C66305C666E696C5C666368617273657430205461686F6D613B7D7D0D0A7B5C2A5C67656E657261746F722052696368656432302031302E302E31393034317D5C766965776B696E64345C756331200D0A5C706172645C756C5C66305C66733136204D6F64756C2054534E54202D20524C207A756D2053636875747A206C772E204E75747A74696572655C756C6E6F6E655C7061720D0A54534E5420315C74616220506572736F6E616C5C7061720D0A54534E5420325C746162204B6F6E74726F6C6C656E5C7061720D0A54534E5420335C746162204175667A656963686E756E67656E5C7061720D0A54534E5420345C746162204265776567756E677366726569686569745C7061720D0A54534E5420355C746162204765625C27653475646520756E6420556E7465726272696E67756E675C7061720D0A54534E5420365C746162204175746F6D61746973636865206F646572206D656361686E697363686520416E6C6167656E5C7061720D0A54534E5420375C74616220465C276663747465726E2C2054725C2765346E6B656E20756E64206265696765665C2766636774652053746F6666655C7061720D0A54534E5420395C746162205A756368746D6574686F64656E5C7061720D0A7D0D0A00"/>
    </TfrxFooter>
    <TfrxDetailData Name="DetailData1" FillType="ftBrush" FillGap.Top="0" FillGap.Left="0" FillGap.Bottom="0" FillGap.Right="0" Frame.Typ="0" Height="18,89765" Left="0" Top="385,51206" Width="1046,92981" ColumnWidth="0" ColumnGap="0" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" RowCount="0">
      <TfrxMemoView Name="FDQueryBewertungsdatenAnforderung" IndexTag="1" AllowVectorExport="True" Left="0" Top="0" Width="75,5906" Height="18,89765" DataField="Anforderung" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="[FDQueryBewertungsdaten.&#34;Anforderung&#34;]"/>
      <TfrxMemoView Name="FDQueryBewertungsdatenAusmass" IndexTag="1" AllowVectorExport="True" Left="245,66945" Top="0" Width="98,26778" Height="18,89765" DataField="Ausmass" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="[FDQueryBewertungsdaten.&#34;Ausmass&#34;]"/>
      <TfrxMemoView Name="FDQueryBewertungsdatenSchwere" IndexTag="1" AllowVectorExport="True" Left="343,93723" Top="0" Width="98,26771654" Height="18,89765" DataField="Schwere" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="[FDQueryBewertungsdaten.&#34;Schwere&#34;]"/>
      <TfrxMemoView Name="FDQueryBewertungsdatenDauer" IndexTag="1" AllowVectorExport="True" Left="442,20501" Top="0" Width="98,26771654" Height="18,89765" DataField="Dauer" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" HAlign="haCenter" ParentFont="False" Text="[FDQueryBewertungsdaten.&#34;Dauer&#34;]"/>
      <TfrxMemoView Name="FDQueryBewertungsdatenBemerkung" IndexTag="1" AllowVectorExport="True" Left="540,47279" Top="0" Width="502,67749" Height="18,89765" DataField="Bemerkung" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Font.Charset="1" Font.Color="0" Font.Height="-11" Font.Name="Arial" Font.Style="0" Frame.Typ="15" ParentFont="False" Text="[FDQueryBewertungsdaten.&#34;Bemerkung&#34;]"/>
      <TfrxCheckBoxView Name="CheckBox1" AllowVectorExport="True" Left="75,5906" Top="0" Width="34,01574803" Height="18,89765" CheckColor="0" CheckStyle="csCross" DataField="Kontrolliert" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Frame.Typ="15"/>
      <TfrxCheckBoxView Name="CheckBox2" AllowVectorExport="True" Left="109,60637" Top="0" Width="34,01577" Height="18,89765" CheckColor="0" CheckStyle="csCross" DataField="Ok" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Frame.Typ="15"/>
      <TfrxCheckBoxView Name="CheckBox3" AllowVectorExport="True" Left="143,62214" Top="0" Width="34,01574803" Height="18,89765" CheckColor="0" CheckStyle="csCross" DataField="Auffaellig" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Frame.Typ="15"/>
      <TfrxCheckBoxView Name="CheckBox4" AllowVectorExport="True" Left="177,63791" Top="0" Width="34,01574803" Height="18,89765" CheckColor="0" CheckStyle="csCross" DataField="GerVerstossOk" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Frame.Typ="15"/>
      <TfrxCheckBoxView Name="CheckBox5" AllowVectorExport="True" Left="211,65368" Top="0" Width="34,01574803" Height="18,89765" CheckColor="0" CheckStyle="csCross" DataField="Vorsatz" DataSet="frxDB_Bewertungsdaten" DataSetName="FDQueryBewertungsdaten" Frame.Typ="15"/>
    </TfrxDetailData>
  </TfrxReportPage>
</TfrxReport>
