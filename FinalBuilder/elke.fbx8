<?xml version="1.0" encoding="UTF-8"?>
<finalbuilder>
  <project>
    <projectid>{10A244FD-3B37-4DAF-BF21-A49FB802780C}</projectid>
    <target>
      <name>Default</name>
      <targetid>{01FA2E71-A7B7-41E8-A875-7C44114F9AE2}</targetid>
      <rootaction>
        <action.variable.set>
          <applytoexisting>false</applytoexisting>
          <doexpandexpression>true</doexpandexpression>
          <forcetype>ftDefault</forcetype>
          <id>{078A490E-F91B-425D-9342-01CC78B09D78}</id>
          <modifier>smExcludeTrailingBackSlash</modifier>
          <newvalue>%FBPROJECTDIR%\..</newvalue>
          <variablename>REPO</variablename>
        </action.variable.set>
        <action.path.manipulation>
          <actiontypeid>{097DBD39-9D4E-416B-A4B0-1FA0ED5FB081}</actiontypeid>
          <customactionname>
            <![CDATA[Path Manipulation]]>
          </customactionname>
          <enabletimeout>false</enabletimeout>
          <id>{6B76644B-E300-430F-AE99-DFF6198572FA}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.applytoinput>True</as.applytoinput>
          <as.usinginputvariable>True</as.usinginputvariable>
          <as.inputvariable>REPO</as.inputvariable>
          <as.includetrailingbackslash>False</as.includetrailingbackslash>
          <as.excludetrailingbackslash>False</as.excludetrailingbackslash>
          <as.extractfilename>False</as.extractfilename>
          <as.extractfilepath>False</as.extractfilepath>
          <as.extractfiledrive>False</as.extractfiledrive>
          <as.extractfileext>False</as.extractfileext>
          <as.removefileext>False</as.removefileext>
          <as.changefileext>False</as.changefileext>
          <as.appendsubdirectory>False</as.appendsubdirectory>
          <as.forcetrailingbackslash>False</as.forcetrailingbackslash>
          <as.expandrelativepath>False</as.expandrelativepath>
          <as.canonicalizepath>True</as.canonicalizepath>
          <as.extractrelativepath>False</as.extractrelativepath>
        </action.path.manipulation>
        <action.variables.logvalues>
          <failifnotdefined>true</failifnotdefined>
          <id>{8742A683-20DE-424E-A9E4-EE6C5C3799B1}</id>
          <logallvariables>false</logallvariables>
          <variablestoragestring>
            <![CDATA[REPO
]]>
          </variablestoragestring>
        </action.variables.logvalues>
        <action.variable.set>
          <applytoexisting>false</applytoexisting>
          <doexpandexpression>true</doexpandexpression>
          <forcetype>ftString</forcetype>
          <id>{1BC6B6FA-14A1-4BBF-AAD7-7F58F1329598}</id>
          <modifier>smNone</modifier>
          <newvalue>%REPO%\Signotaur\SignotaurTool.exe</newvalue>
          <variablename>SIGN_TOOL</variablename>
        </action.variable.set>
        <action.propertyset.define>
          <id>{C8B59ABB-F423-4601-9207-65F327C9BBF4}</id>
          <propertysetname>ELKEVersion</propertysetname>
          <propertyset.win32.numbers>
            <name>ELKEVersion</name>
          </propertyset.win32.numbers>
        </action.propertyset.define>
        <action.git.pull>
          <actiontypeid>{BFC687D6-AA9C-4A80-B756-A0BA4EBE9C0D}</actiontypeid>
          <customactionname>
            <![CDATA[Git Pull]]>
          </customactionname>
          <description>
            <![CDATA[Git Pull ]]>
          </description>
          <enabletimeout>false</enabletimeout>
          <id>{3970594B-480C-4A2C-A01B-DDD2EE891693}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.localrepos>%REPO%</as.localrepos>
          <as.isquiet>False</as.isquiet>
          <as.isverbose>True</as.isverbose>
          <as.isappend>False</as.isappend>
          <as.isforce>False</as.isforce>
          <as.iskeep>False</as.iskeep>
          <as.istags>False</as.istags>
          <as.isnotags>False</as.isnotags>
          <as.isdepth>False</as.isdepth>
          <as.depth>1</as.depth>
          <as.fetchall>False</as.fetchall>
          <as.recursesubmodules>Yes</as.recursesubmodules>
          <as.iscommit>False</as.iscommit>
          <as.isnocommit>False</as.isnocommit>
          <as.cleanup>Default</as.cleanup>
          <as.isff>False</as.isff>
          <as.isnoff>False</as.isnoff>
          <as.isffonly>False</as.isffonly>
          <as.islog>False</as.islog>
          <as.issignoff>False</as.issignoff>
          <as.isnosignoff>False</as.isnosignoff>
          <as.isverifysignatures>False</as.isverifysignatures>
          <as.isnoverifysignatures>False</as.isnoverifysignatures>
          <as.isallowunrelatedhistories>False</as.isallowunrelatedhistories>
          <as.isrebase>False</as.isrebase>
          <as.rebase>False</as.rebase>
          <as.isnorebase>False</as.isnorebase>
          <as.isautostash>False</as.isautostash>
          <as.isnoautostash>False</as.isnoautostash>
          <as.iscleanup>False</as.iscleanup>
          <as.log>0</as.log>
          <as.isnolog>False</as.isnolog>
          <as.isupdateshallow>False</as.isupdateshallow>
          <as.isunshallow>False</as.isunshallow>
          <as.issetupstream>False</as.issetupstream>
          <as.isshowforcedupdates>False</as.isshowforcedupdates>
          <as.isnoshowforcedupdates>False</as.isnoshowforcedupdates>
          <as.isstat>False</as.isstat>
          <as.isnostat>False</as.isnostat>
          <as.isnoverify>False</as.isnoverify>
          <as.issquash>False</as.issquash>
          <as.isnosquash>False</as.isnosquash>
          <as.username><EMAIL></as.username>
          <as.password>sZ7656Bd1zmKDeLHCaZiPEvLPIA=</as.password>
          <as.usecredentialhelper>True</as.usecredentialhelper>
        </action.git.pull>
        <action.propertyset.loadfromini>
          <failiffilemissing>false</failiffilemissing>
          <id>{78E087A9-33C9-4BD6-A772-333EEDC3F34B}</id>
          <inifilename>%REPO%\ELKEVersion.ini</inifilename>
          <propertysetname>ELKEVersion</propertysetname>
          <section>Version</section>
          <setvaluesto>
            <![CDATA[

910

1
64
0
]]>
          </setvaluesto>
          <valuestoset>
            <![CDATA[AutoUpdateFileVersionString=0
AutoUpdateProductVersionString=0
BuildVersion=-1
LinkProductVersion=0
MajorVersion=-1
MinorVersion=0
ReleaseVersion=0
]]>
          </valuestoset>
        </action.propertyset.loadfromini>
        <action.propertyset.incvalue>
          <id>{7D6627EB-5C08-41F0-8457-D5B6528D7205}</id>
          <incrementby>1</incrementby>
          <propertysetname>ELKEVersion</propertysetname>
          <theproperty>BuildVersion</theproperty>
        </action.propertyset.incvalue>
        <action.propertyset.logvalues>
          <id>{E655051E-43D7-483F-B4C4-07B1B8BD1B14}</id>
          <propertysetname>ELKEVersion</propertysetname>
        </action.propertyset.logvalues>
        <action.propertyset.tovariable>
          <chosenproperties>
            <![CDATA[MajorVersion
MinorVersion
ReleaseVersion
BuildVersion
]]>
          </chosenproperties>
          <fbvariable>Version_Full</fbvariable>
          <id>{AFFA9945-0D1A-4BE7-B5F7-C4A2E3C8FFAB}</id>
          <propertysetname>ELKEVersion</propertysetname>
          <separator>.</separator>
        </action.propertyset.tovariable>
        <action.group>
          <description>REST</description>
          <id>{4B7B8345-BF5C-4A2A-AA25-24458E67B699}</id>
          <action.fileset.define>
            <failifnofiles>false</failifnofiles>
            <filesetname>REST_EXE</filesetname>
            <filesetobject></filesetobject>
            <id>{7170D65E-3906-4FB7-B621-6663697BE7E0}</id>
            <includedatesizeinlog>true</includedatesizeinlog>
            <fileset>
              <basedirectory>%REPO%\_build\ELKEApiServer.exe</basedirectory>
              <excludepatternlist></excludepatternlist>
              <filterlist></filterlist>
              <name>REST_EXE</name>
              <outputquoting>qtDouble</outputquoting>
              <outputseparator>spNone</outputseparator>
              <patternlist></patternlist>
              <recursehiddensystem>false</recursehiddensystem>
              <sortascending>true</sortascending>
              <sortby>fsNone</sortby>
              <fileset.patternlist>
                <name>PATTERNLIST</name>
                <fileset.pattern>
                  <includedattributes>FilesOnly</includedattributes>
                  <item>*.*</item>
                  <recurse>false</recurse>
                </fileset.pattern>
              </fileset.patternlist>
              <fileset.patternlist>
                <name>EXCLUDEPATTERNLIST</name>
              </fileset.patternlist>
              <fileset.filterlist/>
            </fileset>
          </action.fileset.define>
          <action.directory.delete>
            <deletehidden>false</deletehidden>
            <deletereadonly>false</deletereadonly>
            <deletetree>true</deletetree>
            <enabledelay>false</enabledelay>
            <failifnodirectory>false</failifnodirectory>
            <fileordirectory>%REPO%\_Build\dcu</fileordirectory>
            <force>false</force>
            <id>{6CFAA75D-1FFE-43AD-A251-AC93DCFB2D10}</id>
            <logdeletedfiles>false</logdeletedfiles>
          </action.directory.delete>
          <action.delphi.build>
            <allowimplicitimport>true</allowimplicitimport>
            <alwaysuseconditionalsfromdof>false</alwaysuseconditionalsfromdof>
            <autoincbuild>false</autoincbuild>
            <autoupdatefileversion>true</autoupdatefileversion>
            <autoupdateproductversion>true</autoupdateproductversion>
            <buildall>true</buildall>
            <buildversion>939</buildversion>
            <codepage>1252</codepage>
            <compileprojectresources>false</compileprojectresources>
            <compileridl>true</compileridl>
            <configname>Release</configname>
            <debugversionnumbers>false</debugversionnumbers>
            <delphiversion>Delphi12</delphiversion>
            <description>
              <![CDATA[Build REST Win32]]>
            </description>
            <enabletimeout>false</enabletimeout>
            <eurekalogverboselogging>false</eurekalogverboselogging>
            <frameworktype>VCL</frameworktype>
            <hintsaserror>false</hintsaserror>
            <iconfile>icons8-api.ico</iconfile>
            <id>{9F466306-BABE-4F09-BE95-62D6B58E9A92}</id>
            <includecompiledate>true</includecompiledate>
            <includemanifest>false</includemanifest>
            <includeverinfo>true</includeverinfo>
            <isdebug>false</isdebug>
            <isdll>false</isdll>
            <isprerelease>false</isprerelease>
            <isprivate>false</isprivate>
            <isspecial>false</isspecial>
            <keepcfg>false</keepcfg>
            <linkproductversiontofileversion>true</linkproductversiontofileversion>
            <locale>3079</locale>
            <majorversion>1</majorversion>
            <minorversion>66</minorversion>
            <noconfig>false</noconfig>
            <platform>Win32</platform>
            <platformsdktype>ProjectSDK</platformsdktype>
            <projectfile>%REPO%\rest\Server\ELKEApiServer.dpr</projectfile>
            <regenerateresource>true</regenerateresource>
            <releaseversion>0</releaseversion>
            <resourcecompilertype>rcBorland</resourcecompilertype>
            <ridloutputsamefolder>true</ridloutputsamefolder>
            <startingdir>%REPO%\rest\Server\</startingdir>
            <timeoutlength>1</timeoutlength>
            <updatedoffile>false</updatedoffile>
            <updatepackagesource>false</updatepackagesource>
            <updateversioninfokeys>false</updateversioninfokeys>
            <useeurekalogcompiler>false</useeurekalogcompiler>
            <usefastdcccompiler>false</usefastdcccompiler>
            <useprojectsettings>[]</useprojectsettings>
            <usepropertyset>true</usepropertyset>
            <useversionfromdof>false</useversionfromdof>
            <verboseoutput>false</verboseoutput>
            <versioninfokeys>
              <![CDATA[CompanyName=EsCulenta GmbH
FileDescription=Implementiert einen  Http.sys basierenden REST-Server für die ELKE Kommunikation
FileVersion=********
InternalName=ELKERest
LegalCopyright=
LegalTrademarks=
OriginalFilename=$(MSBuildProjectName).exe
ProgramID=at.esculenta.$(MSBuildProjectName)
ProductName=ELKE API Server
ProductVersion=1.64.2
Comments=
]]>
            </versioninfokeys>
            <versioninfopropertysetname>ELKEVersion</versioninfopropertysetname>
            <warningsaserror>false</warningsaserror>
            <workaroundd5bug>false</workaroundd5bug>
            <delphi.compileroptions>
              <alwaysuseconditionalsfromdof>false</alwaysuseconditionalsfromdof>
              <alwaysusedelphilibrarypath>true</alwaysusedelphilibrarypath>
              <alwaysusedofsearchpath>true</alwaysusedofsearchpath>
              <assertions>true</assertions>
              <assignableconst>false</assignableconst>
              <booleval>false</booleval>
              <compilerwarnings>
                <![CDATA[-w-SYMBOL_PLATFORM=0
-w-UNIT_PLATFORM=0
-w-XML_WHITESPACE_NOT_ALLOWED=0
-w-XML_UNKNOWN_ENTITY=0
-w-XML_INVALID_NAME_START=0
-w-XML_INVALID_NAME=0
-w-XML_EXPECTED_CHARACTER=0
-w-XML_CREF_NO_RESOLVE=0
-w-XML_NO_PARM=0
-w-XML_NO_MATCHING_PARM=0
]]>
              </compilerwarnings>
              <conditionals>REST;RELEASE</conditionals>
              <consoleapp>false</consoleapp>
              <debuginfo>false</debuginfo>
              <definitionsonly>false</definitionsonly>
              <emitruntimetypeinformation>false</emitruntimetypeinformation>
              <exportallsymbols>false</exportallsymbols>
              <extendedsyntax>true</extendedsyntax>
              <externaltd32>false</externaltd32>
              <frameworktype>VCL</frameworktype>
              <generatedocumentation>true</generatedocumentation>
              <generatehpp>false</generatehpp>
              <hugestrings>true</hugestrings>
              <imagebase>4194304</imagebase>
              <includenamespaces>false</includenamespaces>
              <includeremotesymbols>false</includeremotesymbols>
              <includetd32>false</includetd32>
              <inlining>inOn</inlining>
              <iochecking>true</iochecking>
              <librarypath>
                <![CDATA[c:\program files (x86)\embarcadero\studio\23.0\lib\Win32\release;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;c:\program files (x86)\embarcadero\studio\23.0\Imports;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;c:\program files (x86)\embarcadero\studio\23.0\include;C:\Projekte\TMS Biz Core;c:\projekte\tms\TMS WEB Core RSXE15;c:\projekte\tms\TMS WEB Core RSXE15\Win32;c:\projekte\tms\TMS WEB Core RSXE15\Component Library Source;c:\projekte\TMS\TMS FNC Core;c:\projekte\TMS\TMS FNC Core\Delphi12\Win32\Release;C:\Projekte\TMS\TMS FNC UI Pack;C:\Projekte\TMS\TMS FNC UI Pack\Delphi12\Win32\Release;C:\Projekte\TMS\TMS FNC UI Pack\xlsAdapter;c:\projekte\TMS\TMS FNC Cloud Pack;c:\projekte\TMS\TMS FNC Cloud Pack\Delphi12\Win32\Release;C:\projekte\tms\TMS Aurelius\packages\d12\Win32\Release;C:\projekte\tms\TMS Aurelius\source\drivers;C:\projekte\tms\TMS Sparkle\packages\d12\Win32\Release;C:\projekte\tms\TMS Sparkle\source\extra;C:\projekte\tms\TMS Sparkle\source\app;C:\projekte\tms\TMS XData\packages\d12\Win32\Release;C:\projekte\tms\TMS BIZ Core Library\packages\d12\Win32\Release;C:\projekte\tms\TMS BIZ Core Library\source\extra;C:\Projekte\Atozed\IntraWeb15\LibD12W32;]]>
              </librarypath>
              <linkeroutput>0</linkeroutput>
              <localsymbols>false</localsymbols>
              <mapfile>3</mapfile>
              <maxstacksize>1048576</maxstacksize>
              <minstacksize>16384</minstacksize>
              <namespaceprefixes>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;</namespaceprefixes>
              <openstrings>true</openstrings>
              <optimisation>true</optimisation>
              <outputdir>%REPO%\_build</outputdir>
              <overflowchecking>false</overflowchecking>
              <packages>dbxsqlitedriver;restcomponents;fmxase;dbxdb2driver;dbxinterbasedriver;vcltmsfncchartpkgdxe12;cdata.gmail.d26;vclactnband;vclfiredac;emsclientfiredac;tethering;svnui;datasnapfiredac;firedacadsdriver;tmswizdxe12;dbxmssqldriver;datasnapconnectorsfreepascal;firedacmssqldriver;fmxtmsfncuipackpkgdxe12;vcltouch;vcldb;bindcompfmx;svn;tmswebcorepkglibdxe12;dbxoracledriver;inetdb;fmxteeui;emsedge;fmx;firedacibdriver;fmxdae;firedacdbxdriver;dbexpress;indycore;xdata;vclx;dsnap;emsclient;datasnapcommon;firedaccommon;vcltmsfncuipackpkgdxe12;vcltmsfnccloudpackpkgdxe12;restbackendcomponents;datasnapconnectors;vclrestcomponents;cdata.twilio.d26;soapserver;cdata.wordpress.d26;vclie;bindengine;dbxmysqldriver;cloudservice;firedacoracledriver;firedacmysqldriver;dbxfirebirddriver;firedaccommonodbc;firedaccommondriver;vcltmsfnccorepkgdxe12;datasnapclient;tmsfmxwebgmapspkgdxe12;inet;indyipcommon;bindcompdbx;vcl;indyipserver;dbxsybaseasedriver;sparkle;tmsbcl;indysystem;firedacdb2driver;dsnapcon;madexcept_;virtualtreesr;fmxtmsfnccorepkgdxe12;tmsxlsdxe12;firedacmsaccdriver;fmxfiredac;firedacinfxdriver;vclimg;madbasic_;teedb;firedac;emshosting;tmsdxe12;firedacsqlitedriver;firedacpgdriver;firedacasadriver;dbxodbcdriver;firedactdatadriver;fmxtee;soaprtl;dbxcommondriver;tee;datasnapserver;xmlrtl;soapmidas;datasnapnativeclient;fmxobj;vclwinx;firedacdsdriver;rtl;emsserverresource;dbxclientdriver;maddisasm_;dbxsybaseasadriver;tmsexdxe12;customiptransport;vcldsnap;bindcomp;appanalytics;dbxinformixdriver;fmxtmsfnccloudpackpkgdxe12;indyipclient;tmswebcorepkgdxe12;bindcompvcl;cdata.bing.d26;teeui;dbxcds;vclsmp;adortl;firedacodbcdriver;fixinsight_10_3;datasnapindy10servertransport;aurelius;dsnapxml;datasnapproviderclient;dbrtl;indyprotocols;inetdbxpress;firedacmongodbdriver;fmxtmsfncchartpkgdxe12;datasnapservermidas</packages>
              <rangechecking>false</rangechecking>
              <recordfieldalign>fa8</recordfieldalign>
              <referenceinfo>false</referenceinfo>
              <safedivide>false</safedivide>
              <searchpath>..\..\ReportBuilder;..\..\ESCore\classes;..\..\ESCore\model;..\..\_libs\dx-library;..\..\_libs\FastMM4;..\..\_libs\FastMM5;..\..\_libs\TMSAurelius\source;..\..\_libs\TMSAurelius\source\core;..\..\_libs\TMSAurelius\source\drivers;..\..\_libs\TMSBCL\source;..\..\_libs\TMSBCL\source\core;..\..\_libs\TMSBCL\source\core\common;..\..\_libs\TMSBCL\source\extra;..\..\_libs\TMSXData\source;..\..\_libs\TMSXData\source\core;..\..\_libs\TMSXData\source\core\common;..\..\_libs\TMSSparkle\source;..\..\_libs\TMSSparkle\source\core;..\..\_libs\TMSSparkle\source\extra;..\..\_libs\TMSSparkle\source\app;..\..\_libs\FastReport\FastCore\VCL\Sources;..\..\_libs\FastReport\FastGraphics\VCL\Sources;..\..\_libs\FastReport\FastLocalization\VCL\Sources;..\..\_libs\FastReport\FastQueryBuilder\VCL\Sources;..\..\_libs\FastReport\FastReport\VCL\Sources;..\..\_libs\FastReport\FastScript\VCL\Sources;..\..\_libs\\FastReport\FastGraphics\VCL\Sources</searchpath>
              <showhints>true</showhints>
              <showwarnings>true</showwarnings>
              <stackframes>false</stackframes>
              <stringchecks>true</stringchecks>
              <typedpointers>false</typedpointers>
              <unitaliases>WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE</unitaliases>
              <unitoutputdir>%REPO%\_build\DCU</unitoutputdir>
              <usedebugdcu>false</usedebugdcu>
              <usepackages>false</usepackages>
              <varstringchecks>true</varstringchecks>
            </delphi.compileroptions>
          </action.delphi.build>
          <action.madexcept.execute>
            <binpath>%REPO%\_build\ELKEApiServer.exe</binpath>
            <delmap>true</delmap>
            <id>{87419DB0-5FAB-453A-B757-57E517BEEA4E}</id>
            <mappath>%REPO%\_build\ELKEApiServer.map</mappath>
            <mespath>%REPO%\Rest\Server\ELKEApiServer.mes</mespath>
          </action.madexcept.execute>
          <action.doscmd.execute>
            <captureoutput>true</captureoutput>
            <command>
              <![CDATA[%SIGN_TOOL%  sign --sign-server https://***********:8088 --api-key AQAAAASdlU5Q29CDW_aLs2ajh7AeUwb8cKTe23cpRjYvNuOEk= --thumbprint BC0FCFFC27DB483EDB0D4BC7AB4A96F516207B7C --fd &quot;SHA256&quot; --tr http://timestamp.digicert.com --td &quot;SHA256&quot; -v -m 4 --allow-untrusted ELKEApiServer.exe]]>
            </command>
            <enablelivecapture>true</enablelivecapture>
            <enablereturncodecheck>true</enablereturncodecheck>
            <enabletimeout>false</enabletimeout>
            <expandimpersonationtoken>false</expandimpersonationtoken>
            <hidewindow>true</hidewindow>
            <id>{2D9EC4E6-0BFD-4E73-8194-070FAD5554A1}</id>
            <impersonateusenetcredonly>false</impersonateusenetcredonly>
            <impersonateuser>false</impersonateuser>
            <logoutput>true</logoutput>
            <logprocessparameters>true</logprocessparameters>
            <outputcodepage>1</outputcodepage>
            <params>
              <![CDATA[%SIGN_TOOL%  sign --sign-server https://***********:8088 --api-key AQAAAASdlU5Q29CDW_aLs2ajh7AeUwb8cKTe23cpRjYvNuOEk= --thumbprint BC0FCFFC27DB483EDB0D4BC7AB4A96F516207B7C --fd &quot;SHA256&quot; --tr http://timestamp.digicert.com --td &quot;SHA256&quot; -v -m 4 --allow-untrusted ELKEApiServer.exe]]>
            </params>
            <processoraffinity>0</processoraffinity>
            <processpriority>tpNormal</processpriority>
            <redirectstderr>true</redirectstderr>
            <returncodecomparator>rcEqualTo</returncodecomparator>
            <returncodetocheck>0</returncodetocheck>
            <startindir>%REPO%\_build</startindir>
            <terminateontimeout>false</terminateontimeout>
            <timeoutlength>0</timeoutlength>
            <useerrordialogmonitor>false</useerrordialogmonitor>
            <waitforcompletion>true</waitforcompletion>
          </action.doscmd.execute>
          <action.directory.rename>
            <actionlogtitle>
              <![CDATA[Rename [ %REPO%\build\ELKEApiServer.exe ] to [ %REPO%\build\ELKEApiServer_%Version_Full%.exe ]]]>
            </actionlogtitle>
            <id>{3EACCBC1-C748-487F-B1B9-71C940C0C8BE}</id>
            <newdirname>%REPO%\_build\ELKEApiServer_%Version_Full%.exe</newdirname>
            <overwriteexisting>true</overwriteexisting>
            <renamedir>false</renamedir>
            <sourcefile>%REPO%\_build\ELKEApiServer.exe</sourcefile>
          </action.directory.rename>
          <action.files.copy>
            <clearattributes>false</clearattributes>
            <failifexists>false</failifexists>
            <failifzerofiles>true</failifzerofiles>
            <filespec>%REPO%\_build\ELKEApiServer_%Version_Full%.exe</filespec>
            <force>true</force>
            <id>{006056D6-0A0B-4790-AF55-5591C6404A80}</id>
            <logfilesaffected>true</logfilesaffected>
            <overwrite>true</overwrite>
            <overwritereadonly>false</overwritereadonly>
            <recurse>false</recurse>
            <target>\\***********\c$\ELKE_Build</target>
            <targetisdir>true</targetisdir>
          </action.files.copy>
        </action.group>
        <action.group>
          <description>AMA</description>
          <enabled>false</enabled>
          <id>{0B3BA62C-4E49-4EC0-BEF9-0D928170281A}</id>
          <action.directory.delete>
            <deletehidden>false</deletehidden>
            <deletereadonly>false</deletereadonly>
            <deletetree>true</deletetree>
            <enabledelay>false</enabledelay>
            <failifnodirectory>false</failifnodirectory>
            <fileordirectory>%REPO%\_Build\dcu</fileordirectory>
            <force>false</force>
            <id>{34BEEFDB-DBD1-4851-812B-45CE7FA17FF9}</id>
            <logdeletedfiles>false</logdeletedfiles>
          </action.directory.delete>
          <action.delphi.build>
            <allowimplicitimport>true</allowimplicitimport>
            <alwaysuseconditionalsfromdof>false</alwaysuseconditionalsfromdof>
            <autoincbuild>false</autoincbuild>
            <autoupdatefileversion>true</autoupdatefileversion>
            <autoupdateproductversion>true</autoupdateproductversion>
            <buildall>true</buildall>
            <buildversion>920</buildversion>
            <codepage>1252</codepage>
            <compileprojectresources>false</compileprojectresources>
            <compileridl>true</compileridl>
            <configname>Release</configname>
            <debugversionnumbers>false</debugversionnumbers>
            <delphiversion>Delphi12</delphiversion>
            <description>
              <![CDATA[Build AMA Win32]]>
            </description>
            <enabletimeout>false</enabletimeout>
            <eurekalogverboselogging>false</eurekalogverboselogging>
            <frameworktype>VCL</frameworktype>
            <hintsaserror>false</hintsaserror>
            <iconfile>
              <![CDATA[%FBPROJECTDIR%\rest\AMAServer\icons8-api 2.ico]]>
            </iconfile>
            <id>{12C0E039-2E4D-4924-B844-3ABA0BFBB981}</id>
            <includecompiledate>true</includecompiledate>
            <includemanifest>false</includemanifest>
            <includeverinfo>true</includeverinfo>
            <isdebug>false</isdebug>
            <isdll>false</isdll>
            <isprerelease>false</isprerelease>
            <isprivate>false</isprivate>
            <isspecial>false</isspecial>
            <keepcfg>false</keepcfg>
            <linkproductversiontofileversion>true</linkproductversiontofileversion>
            <locale>3079</locale>
            <majorversion>1</majorversion>
            <minorversion>64</minorversion>
            <noconfig>false</noconfig>
            <platform>Win32</platform>
            <platformsdktype>ProjectSDK</platformsdktype>
            <projectfile>%FBPROJECTDIR%\rest\AMAServer\AMAApiServer.dpr</projectfile>
            <regenerateresource>true</regenerateresource>
            <releaseversion>0</releaseversion>
            <resourcecompilertype>rcBorland</resourcecompilertype>
            <ridloutputsamefolder>true</ridloutputsamefolder>
            <startingdir>%FBPROJECTDIR%\rest\AMAServer\</startingdir>
            <timeoutlength>1</timeoutlength>
            <updatedoffile>false</updatedoffile>
            <updatepackagesource>false</updatepackagesource>
            <updateversioninfokeys>false</updateversioninfokeys>
            <useeurekalogcompiler>false</useeurekalogcompiler>
            <usefastdcccompiler>false</usefastdcccompiler>
            <useprojectsettings>[]</useprojectsettings>
            <usepropertyset>true</usepropertyset>
            <useversionfromdof>false</useversionfromdof>
            <verboseoutput>false</verboseoutput>
            <versioninfokeys>
              <![CDATA[CompanyName=EsCulenta GmbH
FileDescription=ELKE AMA API - HTTP.SYS Server
FileVersion=1.64.0.901
InternalName=AMA
LegalCopyright=(c) 2024
OriginalFilename=AMAApiServer.exe
ProductName=ELKE AMA API
ProductVersion=0.0.0.0
]]>
            </versioninfokeys>
            <versioninfopropertysetname>ELKEVersion</versioninfopropertysetname>
            <warningsaserror>false</warningsaserror>
            <workaroundd5bug>false</workaroundd5bug>
            <delphi.compileroptions>
              <alwaysuseconditionalsfromdof>false</alwaysuseconditionalsfromdof>
              <alwaysusedelphilibrarypath>true</alwaysusedelphilibrarypath>
              <alwaysusedofsearchpath>true</alwaysusedofsearchpath>
              <assertions>true</assertions>
              <assignableconst>false</assignableconst>
              <booleval>false</booleval>
              <compilerwarnings>
                <![CDATA[-w-UNSAFE_TYPE=0
-w-UNSAFE_CODE=0
-w-UNSAFE_CAST=0
]]>
              </compilerwarnings>
              <conditionals>RELEASE;REST;</conditionals>
              <consoleapp>false</consoleapp>
              <debuginfo>true</debuginfo>
              <definitionsonly>true</definitionsonly>
              <emitruntimetypeinformation>false</emitruntimetypeinformation>
              <exportallsymbols>false</exportallsymbols>
              <extendedsyntax>true</extendedsyntax>
              <externaltd32>true</externaltd32>
              <frameworktype>VCL</frameworktype>
              <generatedocumentation>false</generatedocumentation>
              <generatehpp>false</generatehpp>
              <hugestrings>true</hugestrings>
              <imagebase>4194304</imagebase>
              <includenamespaces>false</includenamespaces>
              <includeremotesymbols>false</includeremotesymbols>
              <includetd32>false</includetd32>
              <inlining>inOn</inlining>
              <iochecking>true</iochecking>
              <librarypath>
                <![CDATA[c:\program files (x86)\embarcadero\studio\23.0\lib\Win32\release;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;c:\program files (x86)\embarcadero\studio\23.0\Imports;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;c:\program files (x86)\embarcadero\studio\23.0\include;C:\Projekte\TMS Biz Core;c:\projekte\tms\TMS WEB Core RSXE15;c:\projekte\tms\TMS WEB Core RSXE15\Win32;c:\projekte\tms\TMS WEB Core RSXE15\Component Library Source;c:\projekte\TMS\TMS FNC Core;c:\projekte\TMS\TMS FNC Core\Delphi12\Win32\Release;C:\Projekte\TMS\TMS FNC UI Pack;C:\Projekte\TMS\TMS FNC UI Pack\Delphi12\Win32\Release;C:\Projekte\TMS\TMS FNC UI Pack\xlsAdapter;c:\projekte\TMS\TMS FNC Cloud Pack;c:\projekte\TMS\TMS FNC Cloud Pack\Delphi12\Win32\Release;C:\projekte\tms\TMS Aurelius\packages\d12\Win32\Release;C:\projekte\tms\TMS Aurelius\source\drivers;C:\projekte\tms\TMS Sparkle\packages\d12\Win32\Release;C:\projekte\tms\TMS Sparkle\source\extra;C:\projekte\tms\TMS Sparkle\source\app;C:\projekte\tms\TMS XData\packages\d12\Win32\Release;C:\projekte\tms\TMS BIZ Core Library\packages\d12\Win32\Release;C:\projekte\tms\TMS BIZ Core Library\source\extra;C:\Projekte\Atozed\IntraWeb15\LibD12W32;]]>
              </librarypath>
              <linkeroutput>0</linkeroutput>
              <localsymbols>true</localsymbols>
              <mapfile>0</mapfile>
              <maxstacksize>1048576</maxstacksize>
              <minstacksize>16384</minstacksize>
              <namespaceprefixes>System;Xml;Data;Datasnap;Web;Soap;Winapi;System.Win;Data.Win;Web.Win;Soap.Win;Bde;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;</namespaceprefixes>
              <openstrings>true</openstrings>
              <optimisation>true</optimisation>
              <outputdir>%FBPROJECTDIR%\Build</outputdir>
              <overflowchecking>false</overflowchecking>
              <rangechecking>false</rangechecking>
              <recordfieldalign>fa8</recordfieldalign>
              <referenceinfo>true</referenceinfo>
              <safedivide>false</safedivide>
              <showhints>true</showhints>
              <showwarnings>true</showwarnings>
              <stackframes>false</stackframes>
              <stringchecks>true</stringchecks>
              <typedpointers>false</typedpointers>
              <unitaliases>WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE;</unitaliases>
              <unitoutputdir>%FBPROJECTDIR%\Build\dcu</unitoutputdir>
              <usedebugdcu>false</usedebugdcu>
              <usepackages>false</usepackages>
              <varstringchecks>true</varstringchecks>
            </delphi.compileroptions>
          </action.delphi.build>
          <action.directory.rename>
            <id>{9D876B8F-CB67-46EF-A716-1CF2CA90A081}</id>
            <newdirname>%REPO%\_build\AMAApiServer_%Version_Full%.exe</newdirname>
            <overwriteexisting>true</overwriteexisting>
            <renamedir>false</renamedir>
            <sourcefile>%REPO%\_build\AMAApiServer.exe</sourcefile>
          </action.directory.rename>
          <action.files.copy>
            <clearattributes>false</clearattributes>
            <failifexists>false</failifexists>
            <failifzerofiles>true</failifzerofiles>
            <filespec>%REPO%\_build\AMAApiServer_%Version_Full%.exe</filespec>
            <force>true</force>
            <id>{29183E8D-4751-4DF3-B43D-C7AE36B6F139}</id>
            <logfilesaffected>true</logfilesaffected>
            <overwrite>true</overwrite>
            <overwritereadonly>false</overwritereadonly>
            <recurse>false</recurse>
            <target>\\***********\c$\ELKE_Build</target>
            <targetisdir>true</targetisdir>
          </action.files.copy>
        </action.group>
        <action.propertyset.savetoini>
          <actionlogtitle>
            <![CDATA[PropertySet  [ ELKEVersion ] Save To [ %REPO%\_ELKEVersion.ini ]]]>
          </actionlogtitle>
          <id>{0FE37238-3E45-4F02-9C94-EE3AE22FD753}</id>
          <inifilename>%REPO%\ELKEVersion.ini</inifilename>
          <propertysetname>ELKEVersion</propertysetname>
          <section>Version</section>
        </action.propertyset.savetoini>
        <action.git.commit>
          <actiontypeid>{0A30A02B-5827-48F8-8C60-C612C8D3577E}</actiontypeid>
          <customactionname>
            <![CDATA[Git Commit]]>
          </customactionname>
          <enabletimeout>false</enabletimeout>
          <id>{95A29773-5B93-4D5D-A6CF-2206A12AA552}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.localrepos>%FBPROJECTDIR%</as.localrepos>
          <as.isquiet>False</as.isquiet>
          <as.isverbose>False</as.isverbose>
          <as.isnoverify>False</as.isnoverify>
          <as.isallowempty>False</as.isallowempty>
          <as.isamend>False</as.isamend>
          <as.isall>False</as.isall>
          <as.isonly>False</as.isonly>
          <as.filelist>
            <![CDATA[%REPO%\ELKEVersion.ini

]]>
          </as.filelist>
          <as.issignoff>False</as.issignoff>
          <as.message>Build %Version_Full%</as.message>
          <as.isresetauthor>False</as.isresetauthor>
          <as.isnopostrewrite>False</as.isnopostrewrite>
          <as.isinclude>False</as.isinclude>
          <as.isallowemptymessage>False</as.isallowemptymessage>
          <as.ispathspecfilenul>False</as.ispathspecfilenul>
          <as.isdate>False</as.isdate>
          <as.date>02.09.2024</as.date>
        </action.git.commit>
        <action.git.generic>
          <actiontypeid>{9FE3B354-A721-491E-86BF-19CF66F92F24}</actiontypeid>
          <customactionname>
            <![CDATA[Git Generic]]>
          </customactionname>
          <description>
            <![CDATA[Git tag version]]>
          </description>
          <enabletimeout>false</enabletimeout>
          <id>{CE6A69A5-D6D1-4203-9609-C662254EBF01}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.localrepos>%FBPROJECTDIR%</as.localrepos>
          <as.command>tag</as.command>
          <as.commandarguments>v%Version_Full%</as.commandarguments>
          <as.usecredentialhelper>False</as.usecredentialhelper>
        </action.git.generic>
        <action.git.push>
          <actiontypeid>{455976BE-54A2-4229-BD15-7A9C269FF49A}</actiontypeid>
          <customactionname>
            <![CDATA[Git Push]]>
          </customactionname>
          <enabletimeout>false</enabletimeout>
          <id>{BB92AEFE-3E9D-48E5-970F-C323E935B457}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.localrepos>%FBPROJECTDIR%</as.localrepos>
          <as.isall>False</as.isall>
          <as.ismirror>False</as.ismirror>
          <as.isdryrun>False</as.isdryrun>
          <as.isporcelain>False</as.isporcelain>
          <as.istags>False</as.istags>
          <as.isforce>False</as.isforce>
          <as.isverbose>False</as.isverbose>
          <as.issetupstream>False</as.issetupstream>
          <as.isdelete>False</as.isdelete>
          <as.isfollowtags>False</as.isfollowtags>
          <as.isquiet>False</as.isquiet>
          <as.isforcewithlease>False</as.isforcewithlease>
          <as.isatomic>False</as.isatomic>
          <as.isnoatomic>False</as.isnoatomic>
          <as.isverify>False</as.isverify>
          <as.isnoverify>False</as.isnoverify>
          <as.isthin>False</as.isthin>
          <as.isnothin>False</as.isnothin>
          <as.isprune>False</as.isprune>
          <as.usecredentialhelper>False</as.usecredentialhelper>
        </action.git.push>
        <action.git.push>
          <actiontypeid>{455976BE-54A2-4229-BD15-7A9C269FF49A}</actiontypeid>
          <customactionname>
            <![CDATA[Git Push]]>
          </customactionname>
          <description>
            <![CDATA[Git Push Tags]]>
          </description>
          <enabletimeout>false</enabletimeout>
          <id>{4B8BE7CA-3BC2-49BA-BD20-E58F6F502972}</id>
          <impersonateusenetcredonly>false</impersonateusenetcredonly>
          <impersonateuser>false</impersonateuser>
          <terminateontimeout>true</terminateontimeout>
          <timeoutlength>2</timeoutlength>
          <as.localrepos>%FBPROJECTDIR%</as.localrepos>
          <as.isall>False</as.isall>
          <as.ismirror>False</as.ismirror>
          <as.isdryrun>False</as.isdryrun>
          <as.isporcelain>False</as.isporcelain>
          <as.istags>True</as.istags>
          <as.isforce>False</as.isforce>
          <as.isverbose>False</as.isverbose>
          <as.issetupstream>False</as.issetupstream>
          <as.isdelete>False</as.isdelete>
          <as.isfollowtags>False</as.isfollowtags>
          <as.isquiet>False</as.isquiet>
          <as.isforcewithlease>False</as.isforcewithlease>
          <as.isatomic>False</as.isatomic>
          <as.isnoatomic>False</as.isnoatomic>
          <as.isverify>False</as.isverify>
          <as.isnoverify>False</as.isnoverify>
          <as.isthin>False</as.isthin>
          <as.isnothin>False</as.isnothin>
          <as.isprune>False</as.isprune>
          <as.usecredentialhelper>False</as.usecredentialhelper>
        </action.git.push>
      </rootaction>
    </target>
    <variable>
      <groupname>Dummy</groupname>
      <name>FRL</name>
      <variablescope>vtProject</variablescope>
      <variabletype>btString</variabletype>
    </variable>
    <variable>
      <defaultvalue>UNKNOWN</defaultvalue>
      <name>REPO</name>
      <variablescope>vtProject</variablescope>
    </variable>
    <variable>
      <comment>
        <![CDATA[Path too signtool exe]]>
      </comment>
      <name>SIGN_TOOL</name>
      <variablescope>vtProject</variablescope>
      <variabletype>btString</variabletype>
    </variable>
    <variable>
      <defaultvalue>1.64.0</defaultvalue>
      <groupname>Version</groupname>
      <name>Version_Full</name>
      <variablescope>vtProject</variablescope>
      <variabletype>btString</variabletype>
    </variable>
  </project>
</finalbuilder>
