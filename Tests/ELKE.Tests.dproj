﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Console</AppType>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>None</FrameworkType>
        <MainSource>ELKE.Tests.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{849AB4E7-3C3F-49C1-A8CC-F01330A5277E}</ProjectGuid>
        <ProjectVersion>18.8</ProjectVersion>
        <TargetedPlatforms>1</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>ELKE_Tests</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)\dcu</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UnitSearchPath>..\_libs\dx-library;..\_libs\madCollection\madExcept;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1031</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_ConsoleTarget>true</DCC_ConsoleTarget>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>DBXSqliteDriver;CData.Gmail.D26;fqb26;DBXDb2Driver;fqbDBX26;frLanguagePolish26;vclactnband;vclFireDAC;IWBootstrapD103;frLanguageChinese26;tethering;FireDACADSDriver;frLanguageRussian26;FireDACMSSQLDriver;frLanguageBulgarian26;vcltouch;vcldb;svn;frLanguageSwiss26;TMSFMXCloudPackPkgDEDXE12;frCoreLibrary26;frLanguageDutch26;IWCGJQMobile_150_260;frxTee26;FireDACDBXDriver;frLanguageUkrainian26;frLanguageTaiwan26;vclx;RESTBackendComponents;CData.Office365.D26;VCLRESTComponents;CData.Twilio.D26;TMSVCLUIPackPkgWizDXE12;frLanguageLithuanian26;fsTee26;MARSServerWizard;IWCGJQCore_150_260;frLanguageLatvian26;CData.WordPress.D26;vclie;TMSVCLUIPackPkgDXE12;bindengine;CloudService;remotedb;FireDACMySQLDriver;frLanguageJapanese26;frx26;frLanguageCatalon26;TMSFMXWebGMapsPkgDXE12;DataSnapClient;bindcompdbx;fsFD26;IndyIPServer;DBXSybaseASEDriver;sparkle;tmsbcl;IndySystem;frxDBX26;fsADO26;dsnapcon;VirtualTreesR;FireDACMSAccDriver;fmxFireDAC;FireDACInfxDriver;vclimg;Jcl;frLanguageHebrew26;emshosting;frLanguageArabic26;fqbADO26;DBXOdbcDriver;FireDACTDataDriver;FMXTee;TMSVCLUIPackPkgXlsDXE12;IWBootstrap4D103;soaprtl;DbxCommonDriver;frLanguageCroatian26;iw15tmsxlsdxe12upd;xmlrtl;soapmidas;DataSnapNativeClient;fmxobj;iw15tmshtml5dxe12upd;rtl;emsserverresource;DbxClientDriver;frLanguageSlovak26;DBXSybaseASADriver;frLanguageDanish26;iw15tmsgriddxe12upd;appanalytics;frLanguagePortuguese26;IndyIPClient;frxPDF26;bindcompvcl;CData.GoogleSheets.D26;TeeUI;VclSmp;Intraweb_15_D10_3;FireDACODBCDriver;FixInsight_10_3;frLanguageCzech26;JclVcl;DataSnapIndy10ServerTransport;aurelius;frLanguageSwedish26;frLanguageTurkish26;DataSnapProviderClient;frLanguageFrench26;FireDACMongoDBDriver;frLanguageBrazil26;DataSnapServerMidas;RESTComponents;frLanguageItalian26;DBXInterBaseDriver;frLanguageSerbian26;frLanguageBrazil126;emsclientfiredac;DataSnapFireDAC;svnui;CData.Shopify.D26;frxFD26;DBXMSSQLDriver;DatasnapConnectorsFreePascal;bindcompfmx;DBXOracleDriver;frLanguageSlovene26;inetdb;FmxTeeUI;emsedge;fmx;FireDACIBDriver;fmxdae;iw15tmshtml5dedxe12upd;CData.GoogleDrive.D26;fs26;CData.Instagram.D26;dbexpress;IndyCore;xdata;frxIntIO26;dsnap;emsclient;DataSnapCommon;FireDACCommon;FireDACX_D103;frLanguageSpanish26;frLanguageFarsi26;DataSnapConnectors;soapserver;IWCGBootstrap_150_260;JclDeveloperTools;FireDACOracleDriver;DBXMySQLDriver;DBXFirebirdDriver;FireDACCommonODBC;FireDACCommonDriver;frxIntIOIndy26;inet;IndyIPCommon;frLanguageHungarian26;vcl;TMSFMXCloudPackPkgDXE12;frxDB26;FireDACDb2Driver;madExcept_;frLanguageBrazil226;fsDB26;madBasic_;TeeDB;FireDAC;frxe26;FireDACSqliteDriver;FireDACPgDriver;FireDACASADriver;Tee;DataSnapServer;vclwinx;FireDACDSDriver;madDisAsm_;frxADO26;CustomIPTransport;vcldsnap;frLanguageRomanian26;iw15tmsdxe12upd;bindcomp;frLocalizationLibrary26;TMSVCLUIPackPkgExDXE12;DBXInformixDriver;frLanguageIndonesian26;CData.Bing.D26;TMSCryptoPkgDXE12;dbxcds;adortl;IWCGJQComps_150_260;frLanguageGreek26;dsnapxml;TMSCryptoPkgDEDXE12;dbrtl;IndyProtocols;inetdbxpress;frLanguageGerman26;JclContainers;frxHTML26;iw15tmsgriddedxe12upd;fmxase;$(DCC_UsePackage)</DCC_UsePackage>
        <UWP_DelphiLogo150>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_150.png</UWP_DelphiLogo150>
        <UWP_DelphiLogo44>$(BDS)\bin\Artwork\Windows\UWP\delphi_UwpDefault_44.png</UWP_DelphiLogo44>
        <VerInfo_Keys>CompanyName=;FileDescription=$(MSBuildProjectName);FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProgramID=com.embarcadero.$(MSBuildProjectName);ProductName=$(MSBuildProjectName);ProductVersion=*******;Comments=</VerInfo_Keys>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_Define>madExcept;$(DCC_Define)</DCC_Define>
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="..\_libs\dx-library\DX.Utils.Logger.madExcept.pas"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Application</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">ELKE.Tests.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Deployment Version="3">
                <DeployFile LocalName="Win32\Debug\ELKE.Tests.exe" Configuration="Debug" Class="ProjectOutput">
                    <Platform Name="Win32">
                        <RemoteName>ELKE_Tests.exe</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployClass Name="AdditionalDebugSymbols">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DebugSymbols">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyFramework">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyModule">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.dll;.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="DependencyPackage">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.bpl</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="File">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="ProjectOutput">
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectUWPManifest">
                    <Platform Name="Win32">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo150">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="UWP_DelphiLogo44">
                    <Platform Name="Win32">
                        <RemoteDir>Assets</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <ProjectRoot Platform="Win32" Name="$(PROJECTNAME)"/>
            </Deployment>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">False</Platform>
                <Platform value="Linux64">False</Platform>
                <Platform value="OSX32">False</Platform>
                <Platform value="OSX64">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
                <Platform value="iOSDevice32">False</Platform>
                <Platform value="iOSDevice64">False</Platform>
                <Platform value="iOSSimulator">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
