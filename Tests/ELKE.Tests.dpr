program ELKE.Tests;

{$APPTYPE CONSOLE}

{$R *.res}


uses
  madExcept,
  madLinkDisAsm,
  madListHardware,
  madListProcesses,
  madListModules,
  System.SysUtils,
  DX.Utils.Logger,
  DX.Utils.Logger.madExcept in '..\_libs\dx-library\DX.Utils.Logger.madExcept.pas';

begin
  TDXLogger.Instance.WaitForLogBuffer := true;
  try
    DXLog('App starting...');
    try
      raise EProgrammerNotFound.Create('boom');
    except
      writeln('handled exception');
    end;
  except
    on E: Exception do
      writeln(E.ClassName, ': ', E.Message);
  end;
  ReadLn;


  raise Exception.Create('Unhandled Exception');

end.
