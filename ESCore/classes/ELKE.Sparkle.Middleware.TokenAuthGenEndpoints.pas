unit ELKE.Sparkle.Middleware.TokenAuthGenEndpoints;

interface

uses
  System.Classes, System.SysUtils,
  Sparkle.HttpServer.Context, Sparkle.HttpServer.Module;

type

  TTokenAuthGenEndpointsMiddleware = class(THttpServerMiddleware)
  private
    FToken: string;
  protected
    procedure ProcessRequest(AContext: THttpServerContext; Next: THttpServerProc); override;
  public
    constructor Create(AToken: string); reintroduce;
  end;

implementation

uses
  ELKE.Server.Logger, XData.Sys.Exceptions;

{ TTokenAuthMiddleware }

constructor TTokenAuthGenEndpointsMiddleware.Create(AToken: string);
begin
  inherited Create;
  FToken := AToken;
end;

procedure TTokenAuthGenEndpointsMiddleware.ProcessRequest(AContext: THttpServerContext; Next: THttpServerProc);
begin
  var
  LPath := AContext.Request.Uri.Path.ToLower;
  if not(
    // Alle generischen Endpunkte ausser den Services und Swagger sperren
    // Todo: dynamisch aus den registrierten Services auslesen
    LPath.Contains('/admin') or LPath.Contains('/me') // ELKE REST Service
     or LPath.Contains('/$model') //Das Modell selbst darf gelesen werden
    or LPath.Contains('/cckontrollen') or LPath.Contains('/kiskontrollen') // AMA RESt Service
    or LPath.Contains('/swagger') // SwaggerUI und swagger.json (wenn aktiviert)
    ) then
  begin
    var
    LReceivedToken := AContext.Request.Headers.Get('GenericEnpointToken');
    if (LReceivedToken = FToken)
    {$ifdef debug}or (AContext.Request.Uri.Host.ToLower = '127.0.0.1'){$endif}
    then
    begin
      Next(AContext);
    end
    else
    begin
      AContext.Response.StatusCode := 404;
      AContext.Response.StatusReason := 'Endpoint not found';
      ELKELogError('Generic Endpoint access denied. Received Token: ' + LReceivedToken);
    end;
  end
  else
  begin
    Next(AContext);
  end;
end;

end.
