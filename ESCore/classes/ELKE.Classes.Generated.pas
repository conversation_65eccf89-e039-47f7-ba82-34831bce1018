unit ELKE.Classes.Generated;

interface

uses
  SysUtils, 
  Generics.Collections, 
  Aurelius.Mapping.Attributes, 
  Aurelius.Types.Blob, 
  Aurelius.Types.DynamicProperties, 
  Aurelius.Types.Nullable, 
  Aurelius.Types.Proxy, 
  XData.Model.Attributes;

type
  TAdresse = class;
  TAnsprechpartner = class;
  TAnwesender = class;
  TAPTyp = class;
  TBetrieb = class;
  TBetriebeKommunikationswege = class;
  TBetriebRevstamm = class;
  TBewerteteFrage = class;
  TBewertung = class;
  TBewertungsIcon = class;
  TBewertungstyp = class;
  TBkbnummer = class;
  TBkbTyp = class;
  TBkbtypenRechtsgrundlage = class;
  TBundesland = class;
  TBundeslandChecklistenKontrolltyp = class;
  TBundeslandModul = class;
  TCckAuftrag = class;
  TCckAuftragsart = class;
  TCckAuftragsbewertung = class;
  TCckAuswahl = class;
  TCckBetrieb = class;
  TCckModul = class;
  TCckModulAnforderung = class;
  TCckModulKontrolltyp = class;
  TCckStatus = class;
  TCckStatusMeldung = class;
  TCckTierdaten = class;
  TCckVokSanktion = class;
  TCheckliste = class;
  TChecklistenKontrolltyp = class;
  TDokument = class;
  TEmailArten = class;
  TEmailHistory = class;
  TEmailHistoryAttachment = class;
  TEmailTexte = class;
  TFormatierung = class;
  TFrage = class;
  TFrageBewertung = class;
  TFrageKontrollbereich = class;
  TFragengruppe = class;
  TFunktion = class;
  TFunktionRolle = class;
  TGemeinde = class;
  TGruppe = class;
  TKbProbe = class;
  TKommunikationsart = class;
  TKommunikationsweg = class;
  TKontrollbereich = class;
  TKontrollbericht = class;
  TKontrollberichtBild = class;
  TKontrollberichtOertlichkeit = class;
  TKontrolltyp = class;
  TKontrolltypReport = class;
  TLand = class;
  TMangel = class;
  TMangelKontrollbereich = class;
  TMangelOertlichkeit = class;
  TMangelStatus = class;
  TMangeltyp = class;
  TMassnahme = class;
  TMassnahmenkatalog = class;
  TModul = class;
  TModuleBkbtypen = class;
  TModulInstanz = class;
  TNachricht = class;
  TNachrichtenZustellung = class;
  TPerson = class;
  TPersonenKommunikationswege = class;
  TProgrammModul = class;
  TRechtsgrundlage = class;
  TRegistrierung = class;
  TReport = class;
  TReportTyp = class;
  TRevisionsplan = class;
  TRevisionsSchema = class;
  TRevisionsstamm = class;
  TRolle = class;
  TRollenBkbtypen = class;
  TSys = class;
  TTodo = class;
  TUnterschrift = class;
  TUser = class;
  TUsergruppe = class;
  TUserrolle = class;
  TVbetriebbezirksgruppe = class;
  TVbetriebsinfo = class;
  TVgroupuser = class;
  TVNachrichtForUser = class;
  TVUserGroupMembership = class;
  TvUserKontrollen = class;
  TZulassung = class;
  TZusatztext = class;
  
  [Entity]
  [Table('[ADRESSEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TAdresse = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('ORT', [TColumnProp.Required], 150)]
    FOrt: string;
    
    [Column('PLZ', [TColumnProp.Required], 7)]
    FPlz: string;
    
    [Column('STRASSE', [TColumnProp.Required], 150)]
    FStrasse: string;
    
    [Column('ADRESSZUSATZ', [], 150)]
    FAdresszusatz: Nullable<string>;
    
    [Column('xKoord_31287', [], 31, 8)]
    FXkoord31287: Nullable<Double>;
    
    [Column('yKoord_31287', [], 31, 8)]
    FYkoord31287: Nullable<Double>;
    
    [Column('xKoord_4326', [], 31, 8)]
    FXkoord4326: Nullable<Double>;
    
    [Column('yKoord_4326', [], 31, 8)]
    FYkoord4326: Nullable<Double>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GEMEINDE', [], 'ID')]
    FGemeinde: Proxy<TGemeinde>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetGemeinde: TGemeinde;
    procedure SetGemeinde(const Value: TGemeinde);
  public
    property Id: Integer read FId write FId;
    property Ort: string read FOrt write FOrt;
    property Plz: string read FPlz write FPlz;
    property Strasse: string read FStrasse write FStrasse;
    property Adresszusatz: Nullable<string> read FAdresszusatz write FAdresszusatz;
    property Xkoord31287: Nullable<Double> read FXkoord31287 write FXkoord31287;
    property Ykoord31287: Nullable<Double> read FYkoord31287 write FYkoord31287;
    property Xkoord4326: Nullable<Double> read FXkoord4326 write FXkoord4326;
    property Ykoord4326: Nullable<Double> read FYkoord4326 write FYkoord4326;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Gemeinde: TGemeinde read GetGemeinde write SetGemeinde;
  end;
  
  [Entity]
  [Table('[ANSPRECHPARTNER]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TAnsprechpartner = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('REGNR', [TColumnProp.Required], 7)]
    FRegnr: string;
    
    [Column('KOMMUNIKATIONS_BERECHTIGT', [TColumnProp.Required])]
    FKommunikationsBerechtigt: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_APTYP', [TColumnProp.Required], 'ID')]
    FAptyp: Proxy<TAPTyp>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSONEN', [], 'ID')]
    FPerson: Proxy<TPerson>;
    function GetAptyp: TAPTyp;
    procedure SetAptyp(const Value: TAPTyp);
    function GetPerson: TPerson;
    procedure SetPerson(const Value: TPerson);
  public
    property Id: Integer read FId write FId;
    property Regnr: string read FRegnr write FRegnr;
    property KommunikationsBerechtigt: Boolean read FKommunikationsBerechtigt write FKommunikationsBerechtigt;
    property Aptyp: TAPTyp read GetAptyp write SetAptyp;
    property Person: TPerson read GetPerson write SetPerson;
  end;
  
  [Entity]
  [Table('[ANWESENDE]', 'BEWEGUNGSDATEN')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TAnwesender = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('NAME', [TColumnProp.Required], 255)]
    FName: string;
    
    [Column('EMAIL', [], 255)]
    FEmail: Nullable<string>;
    
    [Column('KOMMUNIKATIONSBERECHTIGT', [TColumnProp.Required])]
    FKommunikationsberechtigt: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [TColumnProp.Required], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_APTYP', [TColumnProp.Required], 'ID')]
    FAPTyp: Proxy<TAPTyp>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSON', [], 'ID')]
    FPerson: Proxy<TPerson>;
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetAPTyp: TAPTyp;
    procedure SetAPTyp(const Value: TAPTyp);
    function GetPerson: TPerson;
    procedure SetPerson(const Value: TPerson);
  public
    property Guid: TGuid read FGuid write FGuid;
    property Name: string read FName write FName;
    property Email: Nullable<string> read FEmail write FEmail;
    property Kommunikationsberechtigt: Boolean read FKommunikationsberechtigt write FKommunikationsberechtigt;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property APTyp: TAPTyp read GetAPTyp write SetAPTyp;
    property Person: TPerson read GetPerson write SetPerson;
  end;
  
  [Entity]
  [Table('[APTYP]', 'STAMMDATEN')]
  [UniqueKey('BEZEICHNUNG')]
  [Id('FId', TIdGenerator.Guid)]
  [Model('ELKE')]
  [Model('AMA')]
  [IdUnsavedValue(-1)]
  TAPTyp = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: TGuid;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 255)]
    FBezeichnung: string;
    
    [Column('HAUPTANSPRECHPARTNER', [TColumnProp.Required])]
    FHauptansprechpartner: Boolean;
  public
    property Id: TGuid read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Hauptansprechpartner: Boolean read FHauptansprechpartner write FHauptansprechpartner;
  end;
  
  [Entity]
  [Table('[BETRIEBE]', 'STAMMDATEN')]
  [UniqueKey('REGNR')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TBetrieb = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('NAME', [TColumnProp.Required], 255)]
    FName: string;
    
    [Column('AUFSICHTSORGAN', [], 2)]
    FAufsichtsorgan: Nullable<string>;
    
    [Column('TELEFON', [], 50)]
    FTelefon: Nullable<string>;
    
    [Column('EMAIL', [], 50)]
    FEmail: Nullable<string>;
    
    [Column('VERGEBUEHRUNG', [], 2)]
    FVergebuehrung: Nullable<string>;
    
    [Column('VULGO', [], 30)]
    FVulgo: Nullable<string>;
    
    [Column('ANMERKUNG', [], 500)]
    FAnmerkung: Nullable<string>;
    
    [Column('BVBKZ', [], 3)]
    FBvbkz: Nullable<string>;
    
    [Column('BBKNR', [], 15)]
    FBbknr: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ADRESSE', [TColumnProp.Required], 'ID')]
    FAdresse: Proxy<TAdresse>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REGNR', [], 'REGNR')]
    FRegistrierung: Proxy<TRegistrierung>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REVISIONS_SCHEMA', [], 'REV_SCHEMA')]
    FRevisionsSchema: Proxy<TRevisionsSchema>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REVISIONS_GRUPPE_ID', [], 'ID')]
    FRevisionsGruppe: Proxy<TGruppe>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBetrieb')]
    FKontrollberichte: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBetrieb')]
    FKommunikationswege: Proxy<TList<TBetriebeKommunikationswege>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBetrieb')]
    FRevStaemme: Proxy<TList<TBetriebRevstamm>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBetrieb')]
    FBezirksgruppen: Proxy<TList<TVbetriebbezirksgruppe>>;
    function GetAdresse: TAdresse;
    procedure SetAdresse(const Value: TAdresse);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetRegistrierung: TRegistrierung;
    procedure SetRegistrierung(const Value: TRegistrierung);
    function GetRevisionsSchema: TRevisionsSchema;
    procedure SetRevisionsSchema(const Value: TRevisionsSchema);
    function GetRevisionsGruppe: TGruppe;
    procedure SetRevisionsGruppe(const Value: TGruppe);
    function GetKontrollberichte: TList<TKontrollbericht>;
    function GetKommunikationswege: TList<TBetriebeKommunikationswege>;
    function GetRevStaemme: TList<TBetriebRevstamm>;
    function GetBezirksgruppen: TList<TVbetriebbezirksgruppe>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Name: string read FName write FName;
    property Aufsichtsorgan: Nullable<string> read FAufsichtsorgan write FAufsichtsorgan;
    property Telefon: Nullable<string> read FTelefon write FTelefon;
    property Email: Nullable<string> read FEmail write FEmail;
    property Vergebuehrung: Nullable<string> read FVergebuehrung write FVergebuehrung;
    property Vulgo: Nullable<string> read FVulgo write FVulgo;
    property Anmerkung: Nullable<string> read FAnmerkung write FAnmerkung;
    property Bvbkz: Nullable<string> read FBvbkz write FBvbkz;
    property Bbknr: Nullable<string> read FBbknr write FBbknr;
    property Adresse: TAdresse read GetAdresse write SetAdresse;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Registrierung: TRegistrierung read GetRegistrierung write SetRegistrierung;
    property RevisionsSchema: TRevisionsSchema read GetRevisionsSchema write SetRevisionsSchema;
    property RevisionsGruppe: TGruppe read GetRevisionsGruppe write SetRevisionsGruppe;
    property Kontrollberichte: TList<TKontrollbericht> read GetKontrollberichte;
    property Kommunikationswege: TList<TBetriebeKommunikationswege> read GetKommunikationswege;
    property RevStaemme: TList<TBetriebRevstamm> read GetRevStaemme;
    property Bezirksgruppen: TList<TVbetriebbezirksgruppe> read GetBezirksgruppen;
  end;
  
  [Entity]
  [Table('[BETRIEBE_KOMMUNIKATIONSWEGE]', 'BEWEGUNGSDATEN')]
  [Id('FBetrieb', TIdGenerator.None)]
  [Id('FKommunikationsweg', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBetriebeKommunikationswege = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BETRIEB', [TColumnProp.Required], 'ID')]
    FBetrieb: Proxy<TBetrieb>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KOMMUNIKATIONSWEG', [TColumnProp.Required], 'ID')]
    FKommunikationsweg: Proxy<TKommunikationsweg>;
    function GetBetrieb: TBetrieb;
    procedure SetBetrieb(const Value: TBetrieb);
    function GetKommunikationsweg: TKommunikationsweg;
    procedure SetKommunikationsweg(const Value: TKommunikationsweg);
  public
    property Betrieb: TBetrieb read GetBetrieb write SetBetrieb;
    property Kommunikationsweg: TKommunikationsweg read GetKommunikationsweg write SetKommunikationsweg;
  end;
  
  [Entity]
  [Table('[BETRIEBE_REVSTAMM]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBetriebRevstamm = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: Integer;
    
    [Column('BEGDATE', [TColumnProp.Required])]
    FBegdate: TDateTime;
    
    [Column('ENDDATE', [TColumnProp.Required])]
    FEnddate: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BETRIEB', [TColumnProp.Required], 'ID')]
    FBetrieb: Proxy<TBetrieb>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_REVISIONSSTAMM', [TColumnProp.Required], 'ID')]
    FRevisionsstamm: Proxy<TRevisionsstamm>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REVISIONS_SCHEMA', [], 'REV_SCHEMA')]
    FRevisionsSchema: Proxy<TRevisionsSchema>;
    function GetBetrieb: TBetrieb;
    procedure SetBetrieb(const Value: TBetrieb);
    function GetRevisionsstamm: TRevisionsstamm;
    procedure SetRevisionsstamm(const Value: TRevisionsstamm);
    function GetRevisionsSchema: TRevisionsSchema;
    procedure SetRevisionsSchema(const Value: TRevisionsSchema);
  public
    property Id: Integer read FId write FId;
    property Begdate: TDateTime read FBegdate write FBegdate;
    property Enddate: TDateTime read FEnddate write FEnddate;
    property Betrieb: TBetrieb read GetBetrieb write SetBetrieb;
    property Revisionsstamm: TRevisionsstamm read GetRevisionsstamm write SetRevisionsstamm;
    property RevisionsSchema: TRevisionsSchema read GetRevisionsSchema write SetRevisionsSchema;
  end;
  
  [Entity]
  [Table('[BEWERTETE_FRAGEN]', 'BEWEGUNGSDATEN')]
  [UniqueKey('GUID')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TBewerteteFrage = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('ZUSATZTEXT', [], 500)]
    FZusatztext: Nullable<string>;
    
    [Column('WERT', [TColumnProp.Required])]
    [DBTypeWideMemo]
    FWert: Nullable<WideString>;
    
    [Column('GPS_LON', [], 12, 9)]
    FGpsLon: Nullable<Double>;
    
    [Column('GPS_LAT', [], 12, 9)]
    FGpsLat: Nullable<Double>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BEWERTUNG', [TColumnProp.Required], 'ID')]
    FBewertung: Proxy<TBewertung>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_FRAGE', [TColumnProp.Required], 'ID')]
    FFrage: Proxy<TFrage>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BERICHT', [TColumnProp.Required], 'ID')]
    FBericht: Proxy<TKontrollbericht>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGEL', [], 'ID')]
    FMangel: Proxy<TMangel>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBewerteteFrage')]
    FBilder: Proxy<TList<TKontrollberichtBild>>;
    function GetBewertung: TBewertung;
    procedure SetBewertung(const Value: TBewertung);
    function GetFrage: TFrage;
    procedure SetFrage(const Value: TFrage);
    function GetBericht: TKontrollbericht;
    procedure SetBericht(const Value: TKontrollbericht);
    function GetMangel: TMangel;
    procedure SetMangel(const Value: TMangel);
    function GetBilder: TList<TKontrollberichtBild>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Guid: TGuid read FGuid write FGuid;
    property Zusatztext: Nullable<string> read FZusatztext write FZusatztext;
    property Wert: Nullable<WideString> read FWert write FWert;
    property GpsLon: Nullable<Double> read FGpsLon write FGpsLon;
    property GpsLat: Nullable<Double> read FGpsLat write FGpsLat;
    property Bewertung: TBewertung read GetBewertung write SetBewertung;
    property Frage: TFrage read GetFrage write SetFrage;
    property Bericht: TKontrollbericht read GetBericht write SetBericht;
    property Mangel: TMangel read GetMangel write SetMangel;
    property Bilder: TList<TKontrollberichtBild> read GetBilder;
  end;
  
  [Entity]
  [Table('[BEWERTUNGEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TBewertung = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 200)]
    FBezeichnung: string;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ICON', [], 'ICON')]
    FIcon: Proxy<TBewertungsIcon>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('TYP', [], 'TYP')]
    FTyp: Proxy<TBewertungstyp>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBewertung')]
    FFragenBewertungen: Proxy<TList<TFrageBewertung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBewertung')]
    FBewerteteFragen: Proxy<TList<TBewerteteFrage>>;
    function GetIcon: TBewertungsIcon;
    procedure SetIcon(const Value: TBewertungsIcon);
    function GetTyp: TBewertungstyp;
    procedure SetTyp(const Value: TBewertungstyp);
    function GetFragenBewertungen: TList<TFrageBewertung>;
    function GetBewerteteFragen: TList<TBewerteteFrage>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Icon: TBewertungsIcon read GetIcon write SetIcon;
    property Typ: TBewertungstyp read GetTyp write SetTyp;
    property FragenBewertungen: TList<TFrageBewertung> read GetFragenBewertungen;
    property BewerteteFragen: TList<TBewerteteFrage> read GetBewerteteFragen;
  end;
  
  [Entity]
  [Table('[BEWERTUNGS_ICONS]', 'STAMMDATEN')]
  [Id('FIcon', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBewertungsIcon = class(TObject)
  private
    [Column('ICON', [TColumnProp.Required], 10)]
    FIcon: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 250)]
    FBeschreibung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FIcon')]
    FBewertungen: Proxy<TList<TBewertung>>;
    function GetBewertungen: TList<TBewertung>;
  public
    constructor Create;
    destructor Destroy; override;
    property Icon: string read FIcon write FIcon;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
    property Bewertungen: TList<TBewertung> read GetBewertungen;
  end;
  
  [Entity]
  [Table('[BEWERTUNGS_TYPEN]', 'STAMMDATEN')]
  [Id('FTyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBewertungstyp = class(TObject)
  private
    [Column('TYP', [TColumnProp.Required], 10)]
    FTyp: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 250)]
    FBeschreibung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FTyp')]
    FBewertungen: Proxy<TList<TBewertung>>;
    function GetBewertungen: TList<TBewertung>;
  public
    constructor Create;
    destructor Destroy; override;
    property Typ: string read FTyp write FTyp;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
    property Bewertungen: TList<TBewertung> read GetBewertungen;
  end;
  
  [Entity]
  [Table('[BKBNUMMERN]', 'BEWEGUNGSDATEN')]
  [Id('FNummer', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBkbnummer = class(TObject)
  private
    [Column('NUMMER', [TColumnProp.Required], 26)]
    FNummer: string;
    
    [Column('JAHR', [TColumnProp.Required], 4, 0)]
    FJahr: Double;
    
    [Column('LFD_NR', [TColumnProp.Required])]
    FLfdNr: Integer;
    
    [Column('LFD_NR_HEX', [], 5)]
    FLfdNrHex: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('SYSTEMKZ', [TColumnProp.Required], 'SYSTEMKZ')]
    FSystemkz: Proxy<TSys>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    FBkbtyp: Proxy<TBkbTyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BUNDESLAND', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    function GetSystemkz: TSys;
    procedure SetSystemkz(const Value: TSys);
    function GetBkbtyp: TBkbTyp;
    procedure SetBkbtyp(const Value: TBkbTyp);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
  public
    property Nummer: string read FNummer write FNummer;
    property Jahr: Double read FJahr write FJahr;
    property LfdNr: Integer read FLfdNr write FLfdNr;
    property LfdNrHex: Nullable<string> read FLfdNrHex write FLfdNrHex;
    property Systemkz: TSys read GetSystemkz write SetSystemkz;
    property Bkbtyp: TBkbTyp read GetBkbtyp write SetBkbtyp;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
  end;
  
  [Entity]
  [Table('[BKBTYPEN]', 'STAMMDATEN')]
  [Id('FTyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBkbTyp = class(TObject)
  private
    [Column('BKBTYP', [TColumnProp.Required], 10)]
    FTyp: string;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Column('AKTIV', [TColumnProp.Required])]
    FAktiv: Boolean;
    
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Column('LASTCHANGE', [])]
    FLastchange: Nullable<TDateTime>;
    
    [Column('TSTAMP_INSERT', [TColumnProp.Required])]
    FTstampInsert: TDateTime;
    
    [Column('SECCLASS_MIN', [])]
    FSecclassMin: Nullable<Integer>;
    
    [Column('PROBE', [TColumnProp.Required])]
    FProbe: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('MODUL', [TColumnProp.Required], 'MODUL')]
    FModul: Proxy<TModul>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FKontrolltypen: Proxy<TList<TKontrolltyp>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FBkbtypenRechtsgrundlagen: Proxy<TList<TBkbtypenRechtsgrundlage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FRollenBkbtypen: Proxy<TList<TRollenBkbtypen>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FBkbnummern: Proxy<TList<TBkbnummer>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FMangeltypen: Proxy<TList<TMangeltyp>>;
    function GetModul: TModul;
    procedure SetModul(const Value: TModul);
    function GetKontrolltypen: TList<TKontrolltyp>;
    function GetBkbtypenRechtsgrundlagen: TList<TBkbtypenRechtsgrundlage>;
    function GetRollenBkbtypen: TList<TRollenBkbtypen>;
    function GetBkbnummern: TList<TBkbnummer>;
    function GetMangeltypen: TList<TMangeltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Typ: string read FTyp write FTyp;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Aktiv: Boolean read FAktiv write FAktiv;
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property Lastchange: Nullable<TDateTime> read FLastchange write FLastchange;
    property TstampInsert: TDateTime read FTstampInsert write FTstampInsert;
    property SecclassMin: Nullable<Integer> read FSecclassMin write FSecclassMin;
    property Probe: Boolean read FProbe write FProbe;
    property Modul: TModul read GetModul write SetModul;
    property Kontrolltypen: TList<TKontrolltyp> read GetKontrolltypen;
    property BkbtypenRechtsgrundlagen: TList<TBkbtypenRechtsgrundlage> read GetBkbtypenRechtsgrundlagen;
    property RollenBkbtypen: TList<TRollenBkbtypen> read GetRollenBkbtypen;
    property Bkbnummern: TList<TBkbnummer> read GetBkbnummern;
    property Mangeltypen: TList<TMangeltyp> read GetMangeltypen;
  end;
  
  [Entity]
  [Table('[BKBTYPEN_RECHTSGRUNDLAGE]', 'STAMMDATEN')]
  [Id('FBkbtyp', TIdGenerator.None)]
  [Id('FRechtsgrundlage', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBkbtypenRechtsgrundlage = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    FBkbtyp: Proxy<TBkbTyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_RECHTSGRUNDLAGE', [TColumnProp.Required], 'ID')]
    FRechtsgrundlage: Proxy<TRechtsgrundlage>;
    function GetBkbtyp: TBkbTyp;
    procedure SetBkbtyp(const Value: TBkbTyp);
    function GetRechtsgrundlage: TRechtsgrundlage;
    procedure SetRechtsgrundlage(const Value: TRechtsgrundlage);
  public
    property Bkbtyp: TBkbTyp read GetBkbtyp write SetBkbtyp;
    property Rechtsgrundlage: TRechtsgrundlage read GetRechtsgrundlage write SetRechtsgrundlage;
  end;
  
  [Entity]
  [Table('[BUNDESLAENDER]', 'SYSTEMSTAMMDATEN')]
  [Id('FBldcode', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  [IdUnsavedValue(-1)]
  TBundesland = class(TObject)
  private
    [Column('BLDCODE', [TColumnProp.Required])]
    FBldcode: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 30)]
    FBezeichnung: string;
    
    [Column('KURZTEXT', [TColumnProp.Required], 2)]
    FKurztext: string;
    
    [Column('REGION', [TColumnProp.Required])]
    FRegion: Integer;
    
    [Column('BLDLOGO', [TColumnProp.Lazy])]
    FBldlogo: TBlob;
    
    [Column('OKZ', [TColumnProp.Required], 50)]
    FOkz: string;
    
    [Column('BKBKZ', [TColumnProp.Required], 1)]
    FBkbkz: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('LANDKZ', [TColumnProp.Required], 'LANDKZ')]
    FLand: Proxy<TLand>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FBetriebe: Proxy<TList<TBetrieb>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FBundeslaenderModule: Proxy<TList<TBundeslandModul>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FGruppen: Proxy<TList<TGruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FUsers: Proxy<TList<TUser>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FAdressen: Proxy<TList<TAdresse>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FGemeinden: Proxy<TList<TGemeinde>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FBkbNummern: Proxy<TList<TBkbnummer>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FEmailTexte: Proxy<TList<TEmailTexte>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBesitzerBundesland')]
    FBesitztChecklisten: Proxy<TList<TCheckliste>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBundesland')]
    FSichtbareChecklisten: Proxy<TList<TBundeslandChecklistenKontrolltyp>>;
    function GetLand: TLand;
    procedure SetLand(const Value: TLand);
    function GetBetriebe: TList<TBetrieb>;
    function GetBundeslaenderModule: TList<TBundeslandModul>;
    function GetGruppen: TList<TGruppe>;
    function GetUsers: TList<TUser>;
    function GetAdressen: TList<TAdresse>;
    function GetGemeinden: TList<TGemeinde>;
    function GetBkbNummern: TList<TBkbnummer>;
    function GetEmailTexte: TList<TEmailTexte>;
    function GetBesitztChecklisten: TList<TCheckliste>;
    function GetSichtbareChecklisten: TList<TBundeslandChecklistenKontrolltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Bldcode: Integer read FBldcode write FBldcode;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Kurztext: string read FKurztext write FKurztext;
    property Region: Integer read FRegion write FRegion;
    property Bldlogo: TBlob read FBldlogo write FBldlogo;
    property Okz: string read FOkz write FOkz;
    property Bkbkz: string read FBkbkz write FBkbkz;
    property Land: TLand read GetLand write SetLand;
    property Betriebe: TList<TBetrieb> read GetBetriebe;
    property BundeslaenderModule: TList<TBundeslandModul> read GetBundeslaenderModule;
    property Gruppen: TList<TGruppe> read GetGruppen;
    property Users: TList<TUser> read GetUsers;
    property Adressen: TList<TAdresse> read GetAdressen;
    property Gemeinden: TList<TGemeinde> read GetGemeinden;
    property BkbNummern: TList<TBkbnummer> read GetBkbNummern;
    property EmailTexte: TList<TEmailTexte> read GetEmailTexte;
    property BesitztChecklisten: TList<TCheckliste> read GetBesitztChecklisten;
    property SichtbareChecklisten: TList<TBundeslandChecklistenKontrolltyp> read GetSichtbareChecklisten;
  end;
  
  [Entity]
  [Table('[BUNDESLAENDER_CHECKLISTEN_KONTROLLTYPEN]', 'STAMMDATEN')]
  [Id('FBundesland', TIdGenerator.None)]
  [Id('FChecklisteKontrolltyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBundeslandChecklistenKontrolltyp = class(TObject)
  private
    [Column('GUELTIG_AB', [])]
    FGueltigAb: Nullable<TDateTime>;
    
    [Column('GUELTIG_BIS', [])]
    FGueltigBis: Nullable<TDateTime>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('KONTROLLTYP', [TColumnProp.Required], 'KONTROLLTYP')]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('ID_CHECKLISTEN', [TColumnProp.Required], 'ID_CHECKLISTEN')]
    FChecklisteKontrolltyp: Proxy<TChecklistenKontrolltyp>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetChecklisteKontrolltyp: TChecklistenKontrolltyp;
    procedure SetChecklisteKontrolltyp(const Value: TChecklistenKontrolltyp);
  public
    property GueltigAb: Nullable<TDateTime> read FGueltigAb write FGueltigAb;
    property GueltigBis: Nullable<TDateTime> read FGueltigBis write FGueltigBis;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property ChecklisteKontrolltyp: TChecklistenKontrolltyp read GetChecklisteKontrolltyp write SetChecklisteKontrolltyp;
  end;
  
  [Entity]
  [Table('[BUNDESLAENDER_MODULE]', 'SYSTEMSTAMMDATEN')]
  [Id('FBundesland', TIdGenerator.None)]
  [Id('FModul', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TBundeslandModul = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('MODUL', [TColumnProp.Required], 'MODUL')]
    FModul: Proxy<TModul>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetModul: TModul;
    procedure SetModul(const Value: TModul);
  public
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Modul: TModul read GetModul write SetModul;
  end;
  
  [Entity]
  [Table('[CCK_AUFTRAG]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckAuftrag = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('AUFTRAGSJAHR', [TColumnProp.Required])]
    FAuftragsjahr: Integer;
    
    [Column('LFBIS_HAUPTBETRIEB', [TColumnProp.Required], 15)]
    FLfbisHauptbetrieb: string;
    
    [Column('AUFTRAGSID', [TColumnProp.Required])]
    FAuftragsid: Integer;
    
    [Column('BKB', [], 26)]
    FBkb: Nullable<string>;
    
    [Column('BBKNr', [TColumnProp.Required], 10)]
    FBbknr: string;
    
    [Column('BLDCODE', [])]
    FBldcode: Nullable<Integer>;
    
    [Column('VORNAME', [], 50)]
    FVorname: Nullable<string>;
    
    [Column('NACHNAME', [], 50)]
    FNachname: Nullable<string>;
    
    [Column('PLZ_BEW', [], 10)]
    FPlzBew: Nullable<string>;
    
    [Column('ORT_BEW', [], 50)]
    FOrtBew: Nullable<string>;
    
    [Column('ADRESSE_BEW', [], 80)]
    FAdresseBew: Nullable<string>;
    
    [Column('GEMEINDEKZ_BEW', [])]
    FGemeindekzBew: Nullable<Integer>;
    
    [Column('TEL_FESTNETZ', [], 50)]
    FTelFestnetz: Nullable<string>;
    
    [Column('FLAG_1', [], 5)]
    FFlag1: Nullable<string>;
    
    [Column('FLAG_2', [], 5)]
    FFlag2: Nullable<string>;
    
    [Column('FLAG_3', [], 5)]
    FFlag3: Nullable<string>;
    
    [Column('INFO_1', [], 50)]
    FInfo1: Nullable<string>;
    
    [Column('INFO_2', [], 50)]
    FInfo2: Nullable<string>;
    
    [Column('INFO_3', [], 50)]
    FInfo3: Nullable<string>;
    
    [Column('ABGESCHLOSSEN_AM', [])]
    FAbgeschlossenAm: Nullable<TDateTime>;
    
    [Column('ID_BEARBEITER', [])]
    FIdBearbeiter: Nullable<Integer>;
    
    [Column('ID_BEWERTER', [])]
    FIdBewerter: Nullable<Integer>;
    
    [Column('LETZTE_RESTABFRAGE', [])]
    FLetzteRestabfrage: Nullable<TDateTime>;
    
    [Column('TSTAMP_INSERT', [])]
    FTstampInsert: Nullable<TDateTime>;
    
    [Column('TSTAMP_UPDATE', [])]
    FTstampUpdate: Nullable<TDateTime>;
    
    [Column('VERSION', [TColumnProp.Required])]
    FVersion: Integer;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('AUFTRAGSART', [TColumnProp.Required], 'BEZEICHNUNG')]
    FAuftragsart: Proxy<TCckAuftragsart>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_DOKUMENT', [], 'GUID')]
    FDokument: Proxy<TDokument>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FCckAuftrag')]
    FBetriebsdaten: Proxy<TList<TCckBetrieb>>;
    function GetAuftragsart: TCckAuftragsart;
    procedure SetAuftragsart(const Value: TCckAuftragsart);
    function GetDokument: TDokument;
    procedure SetDokument(const Value: TDokument);
    function GetBetriebsdaten: TList<TCckBetrieb>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Auftragsjahr: Integer read FAuftragsjahr write FAuftragsjahr;
    property LfbisHauptbetrieb: string read FLfbisHauptbetrieb write FLfbisHauptbetrieb;
    property Auftragsid: Integer read FAuftragsid write FAuftragsid;
    property Bkb: Nullable<string> read FBkb write FBkb;
    property Bbknr: string read FBbknr write FBbknr;
    property Bldcode: Nullable<Integer> read FBldcode write FBldcode;
    property Vorname: Nullable<string> read FVorname write FVorname;
    property Nachname: Nullable<string> read FNachname write FNachname;
    property PlzBew: Nullable<string> read FPlzBew write FPlzBew;
    property OrtBew: Nullable<string> read FOrtBew write FOrtBew;
    property AdresseBew: Nullable<string> read FAdresseBew write FAdresseBew;
    property GemeindekzBew: Nullable<Integer> read FGemeindekzBew write FGemeindekzBew;
    property TelFestnetz: Nullable<string> read FTelFestnetz write FTelFestnetz;
    property Flag1: Nullable<string> read FFlag1 write FFlag1;
    property Flag2: Nullable<string> read FFlag2 write FFlag2;
    property Flag3: Nullable<string> read FFlag3 write FFlag3;
    property Info1: Nullable<string> read FInfo1 write FInfo1;
    property Info2: Nullable<string> read FInfo2 write FInfo2;
    property Info3: Nullable<string> read FInfo3 write FInfo3;
    property AbgeschlossenAm: Nullable<TDateTime> read FAbgeschlossenAm write FAbgeschlossenAm;
    property IdBearbeiter: Nullable<Integer> read FIdBearbeiter write FIdBearbeiter;
    property IdBewerter: Nullable<Integer> read FIdBewerter write FIdBewerter;
    property LetzteRestabfrage: Nullable<TDateTime> read FLetzteRestabfrage write FLetzteRestabfrage;
    property TstampInsert: Nullable<TDateTime> read FTstampInsert write FTstampInsert;
    property TstampUpdate: Nullable<TDateTime> read FTstampUpdate write FTstampUpdate;
    property Version: Integer read FVersion write FVersion;
    property Auftragsart: TCckAuftragsart read GetAuftragsart write SetAuftragsart;
    property Dokument: TDokument read GetDokument write SetDokument;
    property Betriebsdaten: TList<TCckBetrieb> read GetBetriebsdaten;
  end;
  
  [Entity]
  [Table('[CCK_AUFTRAGSARTEN]', 'STAMMDATEN')]
  [Id('FBezeichnung', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckAuftragsart = class(TObject)
  private
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
    
    [Column('GESPERRT', [TColumnProp.Required])]
    FGesperrt: Integer;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FAuftragsart')]
    FCckAuftraege: Proxy<TList<TCckAuftrag>>;
    function GetCckAuftraege: TList<TCckAuftrag>;
  public
    constructor Create;
    destructor Destroy; override;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Gesperrt: Integer read FGesperrt write FGesperrt;
    property CckAuftraege: TList<TCckAuftrag> read GetCckAuftraege;
  end;
  
  [Entity]
  [Table('[CCK_AUFTRAGSBEWERTUNG]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckAuftragsbewertung = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: Integer;
    
    [Column('BewertetAm', [TColumnProp.Required])]
    FBewertetam: TDateTime;
    
    [Column('Kontrolliert', [TColumnProp.Required])]
    FKontrolliert: Boolean;
    
    [Column('Ok', [TColumnProp.Required])]
    FOk: Boolean;
    
    [Column('Auffaellig', [TColumnProp.Required])]
    FAuffaellig: Boolean;
    
    [Column('GerVerstossOk', [TColumnProp.Required])]
    FGerverstossok: Boolean;
    
    [Column('Vorsatz', [TColumnProp.Required])]
    FVorsatz: Boolean;
    
    [Column('Ausmass', [TColumnProp.Required])]
    FAusmass: Integer;
    
    [Column('Schwere', [TColumnProp.Required])]
    FSchwere: Integer;
    
    [Column('Dauer', [TColumnProp.Required])]
    FDauer: Integer;
    
    [Column('Bemerkung', [TColumnProp.Required], 50)]
    FBemerkung: string;
    
    [Column('KZ_HV', [TColumnProp.Required])]
    FKzHv: Boolean;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_AUSWAHLDATEN', [], 'ID')]
    FAuswahldaten: Proxy<TCckAuswahl>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('Anforderung', [TColumnProp.Required], 'Anforderung')]
    FAnforderung: Proxy<TCckModulAnforderung>;
    function GetAuswahldaten: TCckAuswahl;
    procedure SetAuswahldaten(const Value: TCckAuswahl);
    function GetAnforderung: TCckModulAnforderung;
    procedure SetAnforderung(const Value: TCckModulAnforderung);
  public
    property Id: Integer read FId write FId;
    property Bewertetam: TDateTime read FBewertetam write FBewertetam;
    property Kontrolliert: Boolean read FKontrolliert write FKontrolliert;
    property Ok: Boolean read FOk write FOk;
    property Auffaellig: Boolean read FAuffaellig write FAuffaellig;
    property Gerverstossok: Boolean read FGerverstossok write FGerverstossok;
    property Vorsatz: Boolean read FVorsatz write FVorsatz;
    property Ausmass: Integer read FAusmass write FAusmass;
    property Schwere: Integer read FSchwere write FSchwere;
    property Dauer: Integer read FDauer write FDauer;
    property Bemerkung: string read FBemerkung write FBemerkung;
    property KzHv: Boolean read FKzHv write FKzHv;
    property Auswahldaten: TCckAuswahl read GetAuswahldaten write SetAuswahldaten;
    property Anforderung: TCckModulAnforderung read GetAnforderung write SetAnforderung;
  end;
  
  [Entity]
  [Table('[CCK_AUSWAHLDATEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckAuswahl = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('AUSWAHL_ID', [TColumnProp.Required])]
    FAuswahlId: Integer;
    
    [Column('AUSWAHLDATUM', [])]
    FAuswahldatum: Nullable<TDateTime>;
    
    [Column('AUSWAHLGRUND', [], 50)]
    FAuswahlgrund: Nullable<string>;
    
    [Column('STATUS', [], 1)]
    FStatus: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_CCK_BETRIEBSDATEN', [TColumnProp.Required], 'ID')]
    FCckBetriebsdaten: Proxy<TCckBetrieb>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('MODUL', [], 'Modul')]
    FModul: Proxy<TCckModul>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FAuswahldaten')]
    FCckAuftragsbewertungen: Proxy<TList<TCckAuftragsbewertung>>;
    function GetCckBetriebsdaten: TCckBetrieb;
    procedure SetCckBetriebsdaten(const Value: TCckBetrieb);
    function GetModul: TCckModul;
    procedure SetModul(const Value: TCckModul);
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetCckAuftragsbewertungen: TList<TCckAuftragsbewertung>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property AuswahlId: Integer read FAuswahlId write FAuswahlId;
    property Auswahldatum: Nullable<TDateTime> read FAuswahldatum write FAuswahldatum;
    property Auswahlgrund: Nullable<string> read FAuswahlgrund write FAuswahlgrund;
    property Status: Nullable<string> read FStatus write FStatus;
    property CckBetriebsdaten: TCckBetrieb read GetCckBetriebsdaten write SetCckBetriebsdaten;
    property Modul: TCckModul read GetModul write SetModul;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property CckAuftragsbewertungen: TList<TCckAuftragsbewertung> read GetCckAuftragsbewertungen;
  end;
  
  [Entity]
  [Table('[CCK_BETRIEBSDATEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckBetrieb = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BETRIEBSTYP', [TColumnProp.Required], 2)]
    FBetriebstyp: string;
    
    [Column('BETRIEBSART', [TColumnProp.Required], 50)]
    FBetriebsart: string;
    
    [Column('LFBIS', [TColumnProp.Required], 15)]
    FLfbis: string;
    
    [Column('PLZ_BETR', [TColumnProp.Required], 10)]
    FPlzBetr: string;
    
    [Column('ORT_BETR', [TColumnProp.Required], 50)]
    FOrtBetr: string;
    
    [Column('ADRESSE_BETR', [TColumnProp.Required], 80)]
    FAdresseBetr: string;
    
    [Column('GEMEINDEKZ_BETR', [TColumnProp.Required])]
    FGemeindekzBetr: Integer;
    
    [Column('LN_FLAECHE', [TColumnProp.Required])]
    FLnFlaeche: Double;
    
    [Column('TGD', [TColumnProp.Required], 1)]
    FTgd: string;
    
    [Column('TIERHALTER', [TColumnProp.Required], 1)]
    FTierhalter: string;
    
    [Column('ID_BETRIEB', [])]
    FIdBetrieb: Nullable<Integer>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_CCK_AUFTRAG', [TColumnProp.Required], 'ID')]
    FCckAuftrag: Proxy<TCckAuftrag>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FCckBetriebsdaten')]
    FCckAuswahldaten: Proxy<TList<TCckAuswahl>>;
    function GetCckAuftrag: TCckAuftrag;
    procedure SetCckAuftrag(const Value: TCckAuftrag);
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetCckAuswahldaten: TList<TCckAuswahl>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Betriebstyp: string read FBetriebstyp write FBetriebstyp;
    property Betriebsart: string read FBetriebsart write FBetriebsart;
    property Lfbis: string read FLfbis write FLfbis;
    property PlzBetr: string read FPlzBetr write FPlzBetr;
    property OrtBetr: string read FOrtBetr write FOrtBetr;
    property AdresseBetr: string read FAdresseBetr write FAdresseBetr;
    property GemeindekzBetr: Integer read FGemeindekzBetr write FGemeindekzBetr;
    property LnFlaeche: Double read FLnFlaeche write FLnFlaeche;
    property Tgd: string read FTgd write FTgd;
    property Tierhalter: string read FTierhalter write FTierhalter;
    property IdBetrieb: Nullable<Integer> read FIdBetrieb write FIdBetrieb;
    property CckAuftrag: TCckAuftrag read GetCckAuftrag write SetCckAuftrag;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property CckAuswahldaten: TList<TCckAuswahl> read GetCckAuswahldaten;
  end;
  
  [Entity]
  [Table('[CCK_MODUL]', 'STAMMDATEN')]
  [Id('FModul', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckModul = class(TObject)
  private
    [Column('Modul', [TColumnProp.Required], 10)]
    FModul: string;
    
    [Column('Bezeichnung', [TColumnProp.Required], 150)]
    FBezeichnung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FModul')]
    FCckAuswahldaten: Proxy<TList<TCckAuswahl>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FModul')]
    FCckModulAnforderungen: Proxy<TList<TCckModulAnforderung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FCckModul')]
    FCckModulKontrolltypen: Proxy<TList<TCckModulKontrolltyp>>;
    function GetCckAuswahldaten: TList<TCckAuswahl>;
    function GetCckModulAnforderungen: TList<TCckModulAnforderung>;
    function GetCckModulKontrolltypen: TList<TCckModulKontrolltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Modul: string read FModul write FModul;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property CckAuswahldaten: TList<TCckAuswahl> read GetCckAuswahldaten;
    property CckModulAnforderungen: TList<TCckModulAnforderung> read GetCckModulAnforderungen;
    property CckModulKontrolltypen: TList<TCckModulKontrolltyp> read GetCckModulKontrolltypen;
  end;
  
  [Entity]
  [Table('[CCK_MODUL_ANFORDERUNG]', 'STAMMDATEN')]
  [Id('FAnforderung', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckModulAnforderung = class(TObject)
  private
    [Column('Anforderung', [TColumnProp.Required], 150)]
    FAnforderung: string;
    
    [Column('Bezeichnung', [TColumnProp.Required], 100)]
    FBezeichnung: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('Modul', [TColumnProp.Required], 'Modul')]
    FModul: Proxy<TCckModul>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FAnforderung')]
    FCckAuftragsbewertungen: Proxy<TList<TCckAuftragsbewertung>>;
    function GetModul: TCckModul;
    procedure SetModul(const Value: TCckModul);
    function GetCckAuftragsbewertungen: TList<TCckAuftragsbewertung>;
  public
    constructor Create;
    destructor Destroy; override;
    property Anforderung: string read FAnforderung write FAnforderung;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Modul: TCckModul read GetModul write SetModul;
    property CckAuftragsbewertungen: TList<TCckAuftragsbewertung> read GetCckAuftragsbewertungen;
  end;
  
  [Entity]
  [Table('[CCK_MODUL_KONTROLLTYPEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckModulKontrolltyp = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('CCK_MODUL', [TColumnProp.Required], 'Modul')]
    FCckModul: Proxy<TCckModul>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('KONTROLLTYP', [TColumnProp.Required], 'KONTROLLTYP')]
    FBkbtyp: Proxy<TKontrolltyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    function GetCckModul: TCckModul;
    procedure SetCckModul(const Value: TCckModul);
    function GetBkbtyp: TKontrolltyp;
    procedure SetBkbtyp(const Value: TKontrolltyp);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
  public
    property Id: Integer read FId write FId;
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property CckModul: TCckModul read GetCckModul write SetCckModul;
    property Bkbtyp: TKontrolltyp read GetBkbtyp write SetBkbtyp;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
  end;
  
  [Entity]
  [Table('[CCK_STATUS_STATI]', 'STAMMDATEN')]
  [Id('FStatus', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckStatus = class(TObject)
  private
    [Column('STATUS', [TColumnProp.Required], 5)]
    FStatus: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 200)]
    FBeschreibung: string;
  public
    property Status: string read FStatus write FStatus;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
  end;
  
  [Entity]
  [Table('[CCK_STATUS_MELDUNGEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckStatusMeldung = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('MELDEDATUM', [TColumnProp.Required])]
    FMeldedatum: TDateTime;
    
    [Column('KOMMENTAR', [], 1000)]
    FKommentar: Nullable<string>;
    
    [Column('KONTAKT', [], 200)]
    FKontakt: Nullable<string>;
    
    [Column('BEWERTUNGS_VERSION', [], 100)]
    FBewertungsVersion: Nullable<string>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKB', [], 'NUMMER')]
    FBkb: Proxy<TBkbnummer>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('STATUS', [TColumnProp.Required], 'STATUS')]
    FStatus: Proxy<TCckStatus>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKB_AUFTRAG', [], 'NUMMER')]
    FBkbAuftrag: Proxy<TBkbnummer>;
    function GetBkb: TBkbnummer;
    procedure SetBkb(const Value: TBkbnummer);
    function GetStatus: TCckStatus;
    procedure SetStatus(const Value: TCckStatus);
    function GetBkbAuftrag: TBkbnummer;
    procedure SetBkbAuftrag(const Value: TBkbnummer);
  public
    property Id: Integer read FId write FId;
    property Meldedatum: TDateTime read FMeldedatum write FMeldedatum;
    property Kommentar: Nullable<string> read FKommentar write FKommentar;
    property Kontakt: Nullable<string> read FKontakt write FKontakt;
    property BewertungsVersion: Nullable<string> read FBewertungsVersion write FBewertungsVersion;
    property Bkb: TBkbnummer read GetBkb write SetBkb;
    property Status: TCckStatus read GetStatus write SetStatus;
    property BkbAuftrag: TBkbnummer read GetBkbAuftrag write SetBkbAuftrag;
  end;
  
  [Entity]
  [Table('[CCK_TIERDATEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckTierdaten = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('ID_CCK_BETRIEBSDATEN', [])]
    FIdCckBetriebsdaten: Nullable<Integer>;
    
    [Column('TIERDATEN_ID', [])]
    FTierdatenId: Nullable<Integer>;
    
    [Column('TIERKATEGORIE', [], 10)]
    FTierkategorie: Nullable<string>;
    
    [Column('ANZAHL', [])]
    FAnzahl: Nullable<Integer>;
    
    [Column('VIS_TIERART', [], 2)]
    FVisTierart: Nullable<string>;
    
    [Column('VIS_PRBKAT', [], 3)]
    FVisPrbkat: Nullable<string>;
  public
    property Id: Integer read FId write FId;
    property IdCckBetriebsdaten: Nullable<Integer> read FIdCckBetriebsdaten write FIdCckBetriebsdaten;
    property TierdatenId: Nullable<Integer> read FTierdatenId write FTierdatenId;
    property Tierkategorie: Nullable<string> read FTierkategorie write FTierkategorie;
    property Anzahl: Nullable<Integer> read FAnzahl write FAnzahl;
    property VisTierart: Nullable<string> read FVisTierart write FVisTierart;
    property VisPrbkat: Nullable<string> read FVisPrbkat write FVisPrbkat;
  end;
  
  [Entity]
  [Table('[CCK_VOK_SANKTIONEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCckVokSanktion = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('ID_CCK_BETRIEBSDATEN', [TColumnProp.Required])]
    FIdCckBetriebsdaten: Integer;
    
    [Column('VOK_SANKTIONEN_ID', [])]
    FVokSanktionenId: Nullable<Integer>;
    
    [Column('VOK_SANK', [], 50)]
    FVokSank: Nullable<string>;
    
    [Column('JAHR', [])]
    FJahr: Nullable<Integer>;
  public
    property Id: Integer read FId write FId;
    property IdCckBetriebsdaten: Integer read FIdCckBetriebsdaten write FIdCckBetriebsdaten;
    property VokSanktionenId: Nullable<Integer> read FVokSanktionenId write FVokSanktionenId;
    property VokSank: Nullable<string> read FVokSank write FVokSank;
    property Jahr: Nullable<Integer> read FJahr write FJahr;
  end;
  
  [Entity]
  [Table('[CHECKLISTEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TCheckliste = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 100)]
    FBezeichnung: string;
    
    [Column('GUELTIG_AB', [])]
    FGueltigAb: Nullable<TDateTime>;
    
    [Column('GUELTIG_BIS', [])]
    FGueltigBis: Nullable<TDateTime>;
    
    [Column('VERSION', [TColumnProp.Required])]
    FVersion: Integer;
    
    [Column('VERSIONSTEXT', [TColumnProp.Required], 255)]
    FVersionstext: string;
    
    [Column('CCRelevant', [TColumnProp.Required])]
    FCcrelevant: Boolean;
    
    [Column('VIS_BKBTID', [], 10)]
    FVisBkbtid: Nullable<string>;
    
    [Column('PRIVAT', [TColumnProp.Required])]
    FPrivat: Boolean;
    
    [Column('LASTCHANGE', [TColumnProp.Required])]
    FLastChange: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BESITZER_BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBesitzerBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('CHANGE_DBUSER', [TColumnProp.Required], 'ID')]
    FLastChangeUser: Proxy<TUser>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FCheckliste')]
    FFragen: Proxy<TList<TFrage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FCheckliste')]
    FChecklistenKontrolltypen: Proxy<TList<TChecklistenKontrolltyp>>;
    function GetBesitzerBundesland: TBundesland;
    procedure SetBesitzerBundesland(const Value: TBundesland);
    function GetLastChangeUser: TUser;
    procedure SetLastChangeUser(const Value: TUser);
    function GetFragen: TList<TFrage>;
    function GetChecklistenKontrolltypen: TList<TChecklistenKontrolltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property GueltigAb: Nullable<TDateTime> read FGueltigAb write FGueltigAb;
    property GueltigBis: Nullable<TDateTime> read FGueltigBis write FGueltigBis;
    property Version: Integer read FVersion write FVersion;
    property Versionstext: string read FVersionstext write FVersionstext;
    property Ccrelevant: Boolean read FCcrelevant write FCcrelevant;
    property VisBkbtid: Nullable<string> read FVisBkbtid write FVisBkbtid;
    property Privat: Boolean read FPrivat write FPrivat;
    property LastChange: TDateTime read FLastChange write FLastChange;
    property BesitzerBundesland: TBundesland read GetBesitzerBundesland write SetBesitzerBundesland;
    property LastChangeUser: TUser read GetLastChangeUser write SetLastChangeUser;
    property Fragen: TList<TFrage> read GetFragen;
    property ChecklistenKontrolltypen: TList<TChecklistenKontrolltyp> read GetChecklistenKontrolltypen;
  end;
  
  [Entity]
  [Table('[CHECKLISTEN_KONTROLLTYPEN]', 'STAMMDATEN')]
  [Id('FKontrolltyp', TIdGenerator.None)]
  [Id('FCheckliste', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TChecklistenKontrolltyp = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_CHECKLISTEN', [TColumnProp.Required], 'ID')]
    FCheckliste: Proxy<TCheckliste>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('KONTROLLTYP', [TColumnProp.Required], 'KONTROLLTYP')]
    FKontrolltyp: Proxy<TKontrolltyp>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FChecklisteKontrolltyp')]
    FBundeslaenderChecklistenKontrolltypen: Proxy<TList<TBundeslandChecklistenKontrolltyp>>;
    function GetCheckliste: TCheckliste;
    procedure SetCheckliste(const Value: TCheckliste);
    function GetKontrolltyp: TKontrolltyp;
    procedure SetKontrolltyp(const Value: TKontrolltyp);
    function GetBundeslaenderChecklistenKontrolltypen: TList<TBundeslandChecklistenKontrolltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Checkliste: TCheckliste read GetCheckliste write SetCheckliste;
    property Kontrolltyp: TKontrolltyp read GetKontrolltyp write SetKontrolltyp;
    property BundeslaenderChecklistenKontrolltypen: TList<TBundeslandChecklistenKontrolltyp> read GetBundeslaenderChecklistenKontrolltypen;
  end;
  
  [Entity]
  [Table('[DOKUMENTE]', 'BEWEGUNGSDATEN')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TDokument = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('BLDCODE', [TColumnProp.Required])]
    FBldcode: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 250)]
    FBezeichnung: string;
    
    [Column('TYP', [TColumnProp.Required], 4)]
    FTyp: string;
    
    [Column('DATEINAME', [TColumnProp.Required], 250)]
    FDateiname: string;
    
    [Column('ERSTELLT_AM', [TColumnProp.Required])]
    FErstelltAm: TDateTime;
    
    [Column('DOKUMENT', [TColumnProp.Required, TColumnProp.Lazy])]
    FDokument: TBlob;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FDokument')]
    FCckAuftraege: Proxy<TList<TCckAuftrag>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FDokument')]
    FKontrollberichte: Proxy<TList<TKontrollbericht>>;
    function GetCckAuftraege: TList<TCckAuftrag>;
    function GetKontrollberichte: TList<TKontrollbericht>;
  public
    constructor Create;
    destructor Destroy; override;
    property Guid: TGuid read FGuid write FGuid;
    property Bldcode: Integer read FBldcode write FBldcode;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Typ: string read FTyp write FTyp;
    property Dateiname: string read FDateiname write FDateiname;
    property ErstelltAm: TDateTime read FErstelltAm write FErstelltAm;
    property Dokument: TBlob read FDokument write FDokument;
    property CckAuftraege: TList<TCckAuftrag> read GetCckAuftraege;
    property Kontrollberichte: TList<TKontrollbericht> read GetKontrollberichte;
  end;
  
  [Entity]
  [Table('[EMAIL_ARTEN]', 'STAMMDATEN')]
  [Id('FArt', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TEmailArten = class(TObject)
  private
    [Column('ART', [TColumnProp.Required], 20)]
    FArt: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 255)]
    FBeschreibung: string;
  public
    property Art: string read FArt write FArt;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
  end;
  
  [Entity]
  [Table('[EMAIL_HISTORY]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TEmailHistory = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('DATUM_GESENDET', [TColumnProp.Required])]
    FDatumGesendet: TDateTime;
    
    [Column('SUBJECT', [TColumnProp.Required], 1000)]
    FSubject: string;
    
    [Column('BODY', [TColumnProp.Required])]
    [DBTypeWideMemo]
    FBody: WideString;
    
    [Column('EMAIL_FROM', [TColumnProp.Required], 1000)]
    FEmailFrom: string;
    
    [Column('EMAIL_TO', [TColumnProp.Required], 1000)]
    FEmailTo: string;
    
    [Column('EMAIL_CC', [], 1000)]
    FEmailCc: Nullable<string>;
    
    [Column('EMAIL_BC', [], 1000)]
    FEmailBc: Nullable<string>;
    
    [Column('HOST', [], 100)]
    FHost: Nullable<string>;
    
    [Column('ERROR', [], 1000)]
    FError: Nullable<string>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FEmail')]
    FAttachments: Proxy<TList<TEmailHistoryAttachment>>;
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetAttachments: TList<TEmailHistoryAttachment>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property DatumGesendet: TDateTime read FDatumGesendet write FDatumGesendet;
    property Subject: string read FSubject write FSubject;
    property Body: WideString read FBody write FBody;
    property EmailFrom: string read FEmailFrom write FEmailFrom;
    property EmailTo: string read FEmailTo write FEmailTo;
    property EmailCc: Nullable<string> read FEmailCc write FEmailCc;
    property EmailBc: Nullable<string> read FEmailBc write FEmailBc;
    property Host: Nullable<string> read FHost write FHost;
    property Error: Nullable<string> read FError write FError;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property Attachments: TList<TEmailHistoryAttachment> read GetAttachments;
  end;
  
  [Entity]
  [Table('[EMAIL_HISTORY_ATTACHMENTS]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TEmailHistoryAttachment = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('FILENAME', [TColumnProp.Required], 255)]
    FFilename: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_EMAIL', [TColumnProp.Required], 'ID')]
    FEmail: Proxy<TEmailHistory>;
    function GetEmail: TEmailHistory;
    procedure SetEmail(const Value: TEmailHistory);
  public
    property Id: Integer read FId write FId;
    property Filename: string read FFilename write FFilename;
    property Email: TEmailHistory read GetEmail write SetEmail;
  end;
  
  [Entity]
  [Table('[EMAIL_TEXTE]', 'STAMMDATEN')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TEmailTexte = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('Text', [TColumnProp.Required], 4000)]
    FText: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ART', [TColumnProp.Required], 'ART')]
    FArt: Proxy<TEmailArten>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    function GetArt: TEmailArten;
    procedure SetArt(const Value: TEmailArten);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
  public
    property Guid: TGuid read FGuid write FGuid;
    property Text: string read FText write FText;
    property Art: TEmailArten read GetArt write SetArt;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
  end;
  
  [Entity]
  [Table('[FORMATIERUNGEN]', 'STAMMDATEN')]
  [Id('FCode', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TFormatierung = class(TObject)
  private
    [Column('CODE', [TColumnProp.Required], 10)]
    FCode: string;
    
    [Column('BESCHREIBUNG', [], 200)]
    FBeschreibung: Nullable<string>;
  public
    property Code: string read FCode write FCode;
    property Beschreibung: Nullable<string> read FBeschreibung write FBeschreibung;
  end;
  
  [Entity]
  [Table('[FRAGEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TFrage = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('ID_EXTERN', [], 100)]
    FIdExtern: Nullable<string>;
    
    [Column('EXTERN_QUELLE', [], 200)]
    FExternQuelle: Nullable<string>;
    
    [Column('LFNR', [])]
    FLfnr: Nullable<Integer>;
    
    [Column('FRAGENNR', [], 20)]
    FFragennr: Nullable<string>;
    
    [Column('TEXT', [TColumnProp.Required], 500)]
    FText: string;
    
    [Column('INFO', [])]
    [DBTypeWideMemo]
    FInfo: string;
    
    [Column('CCRelevant', [])]
    FCCRelevant: Nullable<Boolean>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_CHECKLISTE', [], 'ID')]
    FCheckliste: Proxy<TCheckliste>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('FORMATIERUNG', [], 'CODE')]
    FFormatierung: Proxy<TFormatierung>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_UEBERGEORDNETE_FRAGE', [], 'ID')]
    FUebergeordneteFrage: Proxy<TFrage>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TFragengruppe>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FFrage')]
    FFragenBewertungen: Proxy<TList<TFrageBewertung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FFrage')]
    FBewerteteFragen: Proxy<TList<TBewerteteFrage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUebergeordneteFrage')]
    FUntergeordneteFragen: Proxy<TList<TFrage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FFrage')]
    FFragenKontrollbereiche: Proxy<TList<TFrageKontrollbereich>>;
    function GetCheckliste: TCheckliste;
    procedure SetCheckliste(const Value: TCheckliste);
    function GetFormatierung: TFormatierung;
    procedure SetFormatierung(const Value: TFormatierung);
    function GetUebergeordneteFrage: TFrage;
    procedure SetUebergeordneteFrage(const Value: TFrage);
    function GetGruppe: TFragengruppe;
    procedure SetGruppe(const Value: TFragengruppe);
    function GetFragenBewertungen: TList<TFrageBewertung>;
    function GetBewerteteFragen: TList<TBewerteteFrage>;
    function GetUntergeordneteFragen: TList<TFrage>;
    function GetFragenKontrollbereiche: TList<TFrageKontrollbereich>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property IdExtern: Nullable<string> read FIdExtern write FIdExtern;
    property ExternQuelle: Nullable<string> read FExternQuelle write FExternQuelle;
    property Lfnr: Nullable<Integer> read FLfnr write FLfnr;
    property Fragennr: Nullable<string> read FFragennr write FFragennr;
    property Text: string read FText write FText;
    property Info: string read FInfo write FInfo;
    property CCRelevant: Nullable<Boolean> read FCCRelevant write FCCRelevant;
    property Checkliste: TCheckliste read GetCheckliste write SetCheckliste;
    property Formatierung: TFormatierung read GetFormatierung write SetFormatierung;
    property UebergeordneteFrage: TFrage read GetUebergeordneteFrage write SetUebergeordneteFrage;
    property Gruppe: TFragengruppe read GetGruppe write SetGruppe;
    property FragenBewertungen: TList<TFrageBewertung> read GetFragenBewertungen;
    property BewerteteFragen: TList<TBewerteteFrage> read GetBewerteteFragen;
    property UntergeordneteFragen: TList<TFrage> read GetUntergeordneteFragen;
    property FragenKontrollbereiche: TList<TFrageKontrollbereich> read GetFragenKontrollbereiche;
  end;
  
  [Entity]
  [Table('[FRAGEN_BEWERTUNGEN]', 'STAMMDATEN')]
  [UniqueKey('ID_FRAGE, SORTIERUNG')]
  [Id('FFrage', TIdGenerator.None)]
  [Id('FBewertung', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TFrageBewertung = class(TObject)
  private
    [Column('AUSBLENDEN', [TColumnProp.Required])]
    FAusblenden: Boolean;
    
    [Column('POSITIV', [TColumnProp.Required])]
    FPositiv: Boolean;
    
    [Column('SORTIERUNG', [])]
    FSortierung: Nullable<Integer>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BEWERTUNG', [TColumnProp.Required], 'ID')]
    FBewertung: Proxy<TBewertung>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_FRAGE', [TColumnProp.Required], 'ID')]
    FFrage: Proxy<TFrage>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_STANDARD_MASSNAHME', [], 'ID')]
    FStandardMassnahme: Proxy<TMassnahme>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_STANDARD_MANGELTYP', [], 'ID')]
    FStandardMangeltyp: Proxy<TMangeltyp>;
    function GetBewertung: TBewertung;
    procedure SetBewertung(const Value: TBewertung);
    function GetFrage: TFrage;
    procedure SetFrage(const Value: TFrage);
    function GetStandardMassnahme: TMassnahme;
    procedure SetStandardMassnahme(const Value: TMassnahme);
    function GetStandardMangeltyp: TMangeltyp;
    procedure SetStandardMangeltyp(const Value: TMangeltyp);
  public
    property Ausblenden: Boolean read FAusblenden write FAusblenden;
    property Positiv: Boolean read FPositiv write FPositiv;
    property Sortierung: Nullable<Integer> read FSortierung write FSortierung;
    property Bewertung: TBewertung read GetBewertung write SetBewertung;
    property Frage: TFrage read GetFrage write SetFrage;
    property StandardMassnahme: TMassnahme read GetStandardMassnahme write SetStandardMassnahme;
    property StandardMangeltyp: TMangeltyp read GetStandardMangeltyp write SetStandardMangeltyp;
  end;
  
  [Entity]
  [Table('[FRAGEN_KONTROLLBEREICHE]', 'STAMMDATEN')]
  [Id('FFrage', TIdGenerator.None)]
  [Id('FKontrollbereich', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TFrageKontrollbereich = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_FRAGE', [TColumnProp.Required], 'ID')]
    FFrage: Proxy<TFrage>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBEREICH', [TColumnProp.Required], 'ID')]
    FKontrollbereich: Proxy<TKontrollbereich>;
    function GetFrage: TFrage;
    procedure SetFrage(const Value: TFrage);
    function GetKontrollbereich: TKontrollbereich;
    procedure SetKontrollbereich(const Value: TKontrollbereich);
  public
    property Frage: TFrage read GetFrage write SetFrage;
    property Kontrollbereich: TKontrollbereich read GetKontrollbereich write SetKontrollbereich;
  end;
  
  [Entity]
  [Table('[FRAGENGRUPPEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TFragengruppe = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 100)]
    FBezeichnung: string;
    
    [Column('FARBCODE', [])]
    FFarbcode: Nullable<Integer>;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Column('AKTIV', [TColumnProp.Required])]
    FAktiv: Boolean;
    
    [Column('ZUZSATZTEXT', [])]
    [DBTypeMemo]
    FZuzsatztext: string;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_UEBERGEORDNETE_GRUPPE', [], 'ID')]
    FUebergeordneteGruppe: Proxy<TFragengruppe>;
    function GetUebergeordneteGruppe: TFragengruppe;
    procedure SetUebergeordneteGruppe(const Value: TFragengruppe);
  public
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Farbcode: Nullable<Integer> read FFarbcode write FFarbcode;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Aktiv: Boolean read FAktiv write FAktiv;
    property Zuzsatztext: string read FZuzsatztext write FZuzsatztext;
    property UebergeordneteGruppe: TFragengruppe read GetUebergeordneteGruppe write SetUebergeordneteGruppe;
  end;
  
  [Entity]
  [Table('[FUNKTIONEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TFunktion = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
    
    [Column('HINTTEXT', [], 200)]
    FHinttext: Nullable<string>;
    
    [Column('FARBCODE', [])]
    FFarbcode: Nullable<Integer>;
    
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Column('PROGCALLID', [TColumnProp.Required])]
    FProgcallid: Integer;
    
    [Column('OBJEKT', [], 50)]
    FObjekt: Nullable<string>;
    
    [Column('PRGTEIL', [], 50)]
    FPrgteil: Nullable<string>;
    
    [Column('POSITION', [])]
    FPosition: Nullable<Integer>;
    
    [Column('BESCHRIFTUNG', [], 100)]
    FBeschriftung: Nullable<string>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MUTTER', [], 'ID')]
    FMutter: Proxy<TFunktion>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('PROGMODUL', [TColumnProp.Required], 'KURZBEZEICHNNUNG')]
    FProgrammmodul: Proxy<TProgrammModul>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMutter')]
    FUnterFunktionen: Proxy<TList<TFunktion>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FFunktion')]
    FFunktionsrollen: Proxy<TList<TFunktionRolle>>;
    function GetMutter: TFunktion;
    procedure SetMutter(const Value: TFunktion);
    function GetProgrammmodul: TProgrammModul;
    procedure SetProgrammmodul(const Value: TProgrammModul);
    function GetUnterFunktionen: TList<TFunktion>;
    function GetFunktionsrollen: TList<TFunktionRolle>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Hinttext: Nullable<string> read FHinttext write FHinttext;
    property Farbcode: Nullable<Integer> read FFarbcode write FFarbcode;
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Progcallid: Integer read FProgcallid write FProgcallid;
    property Objekt: Nullable<string> read FObjekt write FObjekt;
    property Prgteil: Nullable<string> read FPrgteil write FPrgteil;
    property Position: Nullable<Integer> read FPosition write FPosition;
    property Beschriftung: Nullable<string> read FBeschriftung write FBeschriftung;
    property Mutter: TFunktion read GetMutter write SetMutter;
    property Programmmodul: TProgrammModul read GetProgrammmodul write SetProgrammmodul;
    property UnterFunktionen: TList<TFunktion> read GetUnterFunktionen;
    property Funktionsrollen: TList<TFunktionRolle> read GetFunktionsrollen;
  end;
  
  [Entity]
  [Table('[FUNKTIONSROLLEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FRolle', TIdGenerator.None)]
  [Id('FFunktion', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TFunktionRolle = class(TObject)
  private
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_FUNKTION', [TColumnProp.Required], 'ID')]
    FFunktion: Proxy<TFunktion>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ROLLE', [TColumnProp.Required], 'ID')]
    FRolle: Proxy<TRolle>;
    function GetFunktion: TFunktion;
    procedure SetFunktion(const Value: TFunktion);
    function GetRolle: TRolle;
    procedure SetRolle(const Value: TRolle);
  public
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Funktion: TFunktion read GetFunktion write SetFunktion;
    property Rolle: TRolle read GetRolle write SetRolle;
  end;
  
  [Entity]
  [Table('[GEMEINDEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TGemeinde = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GEMEINDEKENNZIFFER', [TColumnProp.Required])]
    FGemeindekennziffer: Integer;
    
    [Column('GEMEINDENAME', [TColumnProp.Required], 80)]
    FGemeindename: string;
    
    [Column('GEMEINDECODE', [TColumnProp.Required])]
    FGemeindecode: Integer;
    
    [Column('AMTSPLZ', [TColumnProp.Required], 10)]
    FAmtsplz: string;
    
    [Column('POL_BEZ_KENNZIF', [TColumnProp.Required])]
    FPolBezKennzif: Integer;
    
    [Column('POL_BEZIRK', [TColumnProp.Required], 80)]
    FPolBezirk: string;
    
    [Column('POL_BEZ_CODE', [TColumnProp.Required])]
    FPolBezCode: Integer;
    
    [Column('LAND_ISO2', [TColumnProp.Required], 2)]
    FLandIso2: string;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BUNDESLANDCODE', [], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
  public
    property Id: Integer read FId write FId;
    property Gemeindekennziffer: Integer read FGemeindekennziffer write FGemeindekennziffer;
    property Gemeindename: string read FGemeindename write FGemeindename;
    property Gemeindecode: Integer read FGemeindecode write FGemeindecode;
    property Amtsplz: string read FAmtsplz write FAmtsplz;
    property PolBezKennzif: Integer read FPolBezKennzif write FPolBezKennzif;
    property PolBezirk: string read FPolBezirk write FPolBezirk;
    property PolBezCode: Integer read FPolBezCode write FPolBezCode;
    property LandIso2: string read FLandIso2 write FLandIso2;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
  end;
  
  [Entity]
  [Table('[GRUPPEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TGruppe = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
    
    [Column('OKZ', [], 100)]
    FOkz: Nullable<string>;
    
    [Column('PERSOENLICH', [TColumnProp.Required])]
    FPersoenlich: Boolean;
    
    [Column('EMAIL', [], 250)]
    FEmail: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('MUTTERGRUPPE', [], 'ID')]
    FMuttergruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER_HAUPTVER', [TColumnProp.Required], 'ID')]
    FHauptverantwortlicher: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER_STELLVER', [], 'ID')]
    FStellvertreter: Proxy<TUser>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMuttergruppe')]
    FUntergruppen: Proxy<TList<TGruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FGruppe')]
    FNachrichten: Proxy<TList<TNachricht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FGruppe')]
    FUsergruppen: Proxy<TList<TUsergruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FGruppe')]
    FTodoList: Proxy<TList<TTodo>>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetMuttergruppe: TGruppe;
    procedure SetMuttergruppe(const Value: TGruppe);
    function GetHauptverantwortlicher: TUser;
    procedure SetHauptverantwortlicher(const Value: TUser);
    function GetStellvertreter: TUser;
    procedure SetStellvertreter(const Value: TUser);
    function GetUntergruppen: TList<TGruppe>;
    function GetNachrichten: TList<TNachricht>;
    function GetUsergruppen: TList<TUsergruppe>;
    function GetTodoList: TList<TTodo>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Okz: Nullable<string> read FOkz write FOkz;
    property Persoenlich: Boolean read FPersoenlich write FPersoenlich;
    property Email: Nullable<string> read FEmail write FEmail;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Muttergruppe: TGruppe read GetMuttergruppe write SetMuttergruppe;
    property Hauptverantwortlicher: TUser read GetHauptverantwortlicher write SetHauptverantwortlicher;
    property Stellvertreter: TUser read GetStellvertreter write SetStellvertreter;
    property Untergruppen: TList<TGruppe> read GetUntergruppen;
    property Nachrichten: TList<TNachricht> read GetNachrichten;
    property Usergruppen: TList<TUsergruppe> read GetUsergruppen;
    property TodoList: TList<TTodo> read GetTodoList;
  end;
  
  [Entity]
  [Table('[KB_PROBEN]', 'BEWEGUNGSDATEN')]
  [UniqueKey('GUID')]
  [UniqueKey('Probenkennung')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TKbProbe = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('PROBENBKB', [], 26)]
    FProbenbkb: Nullable<string>;
    
    [Column('PROBENART', [], 50)]
    FProbenart: Nullable<string>;
    
    [Column('BEMERKUNG', [], 250)]
    FBemerkung: Nullable<string>;
    
    [Column('DATUM', [])]
    FDatum: Nullable<TDateTime>;
    
    [Column('VORG_MENGE', [], 50)]
    FVorgMenge: Nullable<string>;
    
    [Column('BESCHAFFENHEIT', [], 50)]
    FBeschaffenheit: Nullable<string>;
    
    [Column('FUTTERTYP', [], 50)]
    FFuttertyp: Nullable<string>;
    
    [Column('VERWENDUNGSZWECK', [], 80)]
    FVerwendungszweck: Nullable<string>;
    
    [Column('TIER_ART_LISA', [], 50)]
    FTierArtLisa: Nullable<string>;
    
    [Column('TIER_KATEGORIE', [], 50)]
    FTierKategorie: Nullable<string>;
    
    [Column('BEIMISCHRATE', [], 18, 4)]
    FBeimischrate: Nullable<Double>;
    
    [Column('VERPACKUNG', [], 50)]
    FVerpackung: Nullable<string>;
    
    [Column('VERSCHLUSS', [], 50)]
    FVerschluss: Nullable<string>;
    
    [Column('VERSIEGELT', [], 5)]
    FVersiegelt: Nullable<string>;
    
    [Column('HERK_ZUKAUF', [], 250)]
    FHerkZukauf: Nullable<string>;
    
    [Column('UNTERSUCHUNGSAUFTRAG', [], 250)]
    FUntersuchungsauftrag: Nullable<string>;
    
    [Column('STATUS', [TColumnProp.Required], 1)]
    FStatus: string;
    
    [Column('VERDACHT', [], 250)]
    FVerdacht: Nullable<string>;
    
    [Column('GEGENPROBE_BELASSEN', [TColumnProp.Required])]
    FGegenprobeBelassen: Boolean;
    
    [Column('EXPORTNAME', [], 50)]
    FExportname: Nullable<string>;
    
    [Column('EXPORTTIME', [])]
    FExporttime: Nullable<TDateTime>;
    
    [Column('AGESAUFTRAGSNUMMER', [], 50)]
    FAgesauftragsnummer: Nullable<string>;
    
    [Column('AGESPROBENNUMMER', [], 50)]
    FAgesprobennummer: Nullable<string>;
    
    [Column('AGES_AUFTRAGSSTATUS', [], 50)]
    FAgesAuftragsstatus: Nullable<string>;
    
    [Column('AGES_PROBENSTATUS', [], 50)]
    FAgesProbenstatus: Nullable<string>;
    
    [Column('PROBENBEZEICHNUNG', [TColumnProp.Required], 255)]
    FProbenbezeichnung: string;
    
    [Column('Futterart', [], 50)]
    FFutterart: Nullable<string>;
    
    [Column('Probenkennung', [], 24)]
    FProbenkennung: Nullable<string>;
    
    [Column('GPS_LAT', [], 12, 9)]
    FGpsLat: Nullable<Double>;
    
    [Column('GPS_LON', [], 12, 9)]
    FGpsLon: Nullable<Double>;
    
    [Column('KENNZEICHNUNG_GEGENPROBE', [], 24)]
    FKennzeichnungGegenprobe: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [TColumnProp.Required], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_EINSENDER', [], 'ID')]
    FEinsender: Proxy<TPerson>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [], 'BKBTYP')]
    FBkbtyp: Proxy<TBkbTyp>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_DOKUMENT', [], 'GUID')]
    FDokument: Proxy<TDokument>;
    
    [Association([], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_DOKUMENT_AGES_ERGEBNIS', [], 'GUID')]
    FAgesErgebnisbericht: TDokument;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FProbe')]
    FBilder: Proxy<TList<TKontrollberichtBild>>;
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetEinsender: TPerson;
    procedure SetEinsender(const Value: TPerson);
    function GetBkbtyp: TBkbTyp;
    procedure SetBkbtyp(const Value: TBkbTyp);
    function GetDokument: TDokument;
    procedure SetDokument(const Value: TDokument);
    function GetBilder: TList<TKontrollberichtBild>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Guid: TGuid read FGuid write FGuid;
    property Probenbkb: Nullable<string> read FProbenbkb write FProbenbkb;
    property Probenart: Nullable<string> read FProbenart write FProbenart;
    property Bemerkung: Nullable<string> read FBemerkung write FBemerkung;
    property Datum: Nullable<TDateTime> read FDatum write FDatum;
    property VorgMenge: Nullable<string> read FVorgMenge write FVorgMenge;
    property Beschaffenheit: Nullable<string> read FBeschaffenheit write FBeschaffenheit;
    property Futtertyp: Nullable<string> read FFuttertyp write FFuttertyp;
    property Verwendungszweck: Nullable<string> read FVerwendungszweck write FVerwendungszweck;
    property TierArtLisa: Nullable<string> read FTierArtLisa write FTierArtLisa;
    property TierKategorie: Nullable<string> read FTierKategorie write FTierKategorie;
    property Beimischrate: Nullable<Double> read FBeimischrate write FBeimischrate;
    property Verpackung: Nullable<string> read FVerpackung write FVerpackung;
    property Verschluss: Nullable<string> read FVerschluss write FVerschluss;
    property Versiegelt: Nullable<string> read FVersiegelt write FVersiegelt;
    property HerkZukauf: Nullable<string> read FHerkZukauf write FHerkZukauf;
    property Untersuchungsauftrag: Nullable<string> read FUntersuchungsauftrag write FUntersuchungsauftrag;
    property Status: string read FStatus write FStatus;
    property Verdacht: Nullable<string> read FVerdacht write FVerdacht;
    property GegenprobeBelassen: Boolean read FGegenprobeBelassen write FGegenprobeBelassen;
    property Exportname: Nullable<string> read FExportname write FExportname;
    property Exporttime: Nullable<TDateTime> read FExporttime write FExporttime;
    property Agesauftragsnummer: Nullable<string> read FAgesauftragsnummer write FAgesauftragsnummer;
    property Agesprobennummer: Nullable<string> read FAgesprobennummer write FAgesprobennummer;
    property AgesAuftragsstatus: Nullable<string> read FAgesAuftragsstatus write FAgesAuftragsstatus;
    property AgesProbenstatus: Nullable<string> read FAgesProbenstatus write FAgesProbenstatus;
    property Probenbezeichnung: string read FProbenbezeichnung write FProbenbezeichnung;
    property Futterart: Nullable<string> read FFutterart write FFutterart;
    property Probenkennung: Nullable<string> read FProbenkennung write FProbenkennung;
    property GpsLat: Nullable<Double> read FGpsLat write FGpsLat;
    property GpsLon: Nullable<Double> read FGpsLon write FGpsLon;
    property KennzeichnungGegenprobe: Nullable<string> read FKennzeichnungGegenprobe write FKennzeichnungGegenprobe;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property Einsender: TPerson read GetEinsender write SetEinsender;
    property Bkbtyp: TBkbTyp read GetBkbtyp write SetBkbtyp;
    property Dokument: TDokument read GetDokument write SetDokument;
    property AgesErgebnisbericht: TDokument read FAgesErgebnisbericht write FAgesErgebnisbericht;
    property Bilder: TList<TKontrollberichtBild> read GetBilder;
  end;
  
  [Entity]
  [Table('[KOMMUNIKATIONSARTEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FArt', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TKommunikationsart = class(TObject)
  private
    [Column('ART', [TColumnProp.Required], 10)]
    FArt: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FArt')]
    FKommunikationswege: Proxy<TList<TKommunikationsweg>>;
    function GetKommunikationswege: TList<TKommunikationsweg>;
  public
    constructor Create;
    destructor Destroy; override;
    property Art: string read FArt write FArt;
    property Kommunikationswege: TList<TKommunikationsweg> read GetKommunikationswege;
  end;
  
  [Entity]
  [Table('[KOMMUNIKATIONSWEGE]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TKommunikationsweg = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 100)]
    FBezeichnung: string;
    
    [Column('VALUE', [TColumnProp.Required], 100)]
    FValue: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ART', [TColumnProp.Required], 'ART')]
    FArt: Proxy<TKommunikationsart>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKommunikationsweg')]
    FBetriebeKommunikationswege: Proxy<TList<TBetriebeKommunikationswege>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKommunikationsweg')]
    FPersonenKommunikationswege: Proxy<TList<TPersonenKommunikationswege>>;
    function GetArt: TKommunikationsart;
    procedure SetArt(const Value: TKommunikationsart);
    function GetBetriebeKommunikationswege: TList<TBetriebeKommunikationswege>;
    function GetPersonenKommunikationswege: TList<TPersonenKommunikationswege>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Value: string read FValue write FValue;
    property Art: TKommunikationsart read GetArt write SetArt;
    property BetriebeKommunikationswege: TList<TBetriebeKommunikationswege> read GetBetriebeKommunikationswege;
    property PersonenKommunikationswege: TList<TPersonenKommunikationswege> read GetPersonenKommunikationswege;
  end;
  
  [Entity]
  [Table('[KONTROLLBEREICHE]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrollbereich = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 200)]
    FBezeichnung: string;
    
    [Column('VIS_KKATTID', [], 20)]
    FVisKkattid: Nullable<string>;
    
    [Column('PFLICHTBEREICH', [TColumnProp.Required])]
    FPflichtbereich: Boolean;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbereich')]
    FFragenKontrollbereiche: Proxy<TList<TFrageKontrollbereich>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbereich')]
    FMaengelKontrollbereiche: Proxy<TList<TMangelKontrollbereich>>;
    function GetFragenKontrollbereiche: TList<TFrageKontrollbereich>;
    function GetMaengelKontrollbereiche: TList<TMangelKontrollbereich>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property VisKkattid: Nullable<string> read FVisKkattid write FVisKkattid;
    property Pflichtbereich: Boolean read FPflichtbereich write FPflichtbereich;
    property FragenKontrollbereiche: TList<TFrageKontrollbereich> read GetFragenKontrollbereiche;
    property MaengelKontrollbereiche: TList<TMangelKontrollbereich> read GetMaengelKontrollbereiche;
  end;
  
  [Entity]
  [Table('[KONTROLLBERICHT]', 'BEWEGUNGSDATEN')]
  [UniqueKey('GUID')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrollbericht = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('BKB', [TColumnProp.Required], 26)]
    FBkb: string;
    
    [Column('DATUM', [])]
    FDatum: Nullable<TDate>;
    
    [Column('REF_BKB', [], 26)]
    FRefBkb: Nullable<string>;
    
    [Column('PROBENZIEHUNG', [TColumnProp.Required])]
    FProbenziehung: Boolean;
    
    [Column('REGNR_ORT', [], 7)]
    FRegnrOrt: Nullable<string>;
    
    [Column('KURZBEMERKUNG', [])]
    [DBTypeWideMemo]
    FKurzbemerkung: Nullable<WideString>;
    
    [Column('STARTZEIT', [])]
    FStartzeit: Nullable<TDateTime>;
    
    [Column('ENDEZEIT', [])]
    FEndezeit: Nullable<TDateTime>;
    
    [Column('BESTAETIGT_UM', [])]
    FBestaetigtUm: Nullable<TDateTime>;
    
    [Column('STATUS', [], 1)]
    FStatus: Nullable<string>;
    
    [Column('ANGEMELDET_UM', [])]
    FAngemeldetUm: Nullable<TDateTime>;
    
    [Column('TSTAMP_INSERT', [])]
    FTstampInsert: Nullable<TDateTime>;
    
    [Column('LASTCHANGE', [])]
    FLastchange: Nullable<TDateTime>;
    
    [Column('BETRIEBSTYP', [TColumnProp.Required], 2)]
    FBetriebstyp: string;
    
    [Column('Verweigerunggrund', [], 150)]
    FVerweigerungsgrund: Nullable<string>;
    
    [Column('VERWEIGERUNGSGRUND_UNTERSCHRIFT', [], 255)]
    FVerweigerungsgrundUnterschrift: Nullable<string>;
    
    [Column('FEHLERHAFT_GESETZT_AM', [])]
    FFehlerhaftGesetztAm: Nullable<TDateTime>;
    
    [Column('STORNIERT_AM', [])]
    FStorniertAm: Nullable<TDateTime>;
    
    [Column('STORNOGRUND', [], 255)]
    FStornogrund: Nullable<string>;
    
    [Column('VERWEIGERT_AM', [])]
    FVerweigertAm: Nullable<TDateTime>;
    
    [Column('KONTROLL_INFORMATIONEN', [], 4000)]
    FKontrollInformationen: Nullable<string>;
    
    [Column('INTERNE_NOTIZ', [])]
    [DBTypeWideMemo]
    FInterneNotiz: Nullable<WideString>;
    
    [Column('BETRIEB_NAME', [], 255)]
    FBetriebName: Nullable<string>;
    
    [Column('BETRIEB_REGNR', [], 7)]
    FBetriebRegnr: Nullable<string>;
    
    [Column('BETRIEB_BLD', [])]
    FBetriebBld: Nullable<Integer>;
    
    [Column('BETRIEB_ADRESSE_STRASSE', [], 150)]
    FBetriebAdresseStrasse: Nullable<string>;
    
    [Column('BETRIEB_ADRESSE_ZUSATZ', [], 150)]
    FBetriebAdresseZusatz: Nullable<string>;
    
    [Column('BETRIEB_ADRESSE_PLZ', [], 7)]
    FBetriebAdressePlz: Nullable<string>;
    
    [Column('BETRIEB_ADRESSE_ORT', [], 150)]
    FBetriebAdresseOrt: Nullable<string>;
    
    [Column('KONTROLLTYP_BEZEICHNUNG', [], 80)]
    FKontrolltypBezeichnung: Nullable<string>;
    
    [Column('BKBTYP_BEZEICHNUNG', [], 50)]
    FBkbtypBezeichnung: Nullable<string>;
    
    [Column('ERFASSER_NACHNAME', [], 60)]
    FErfasserNachname: Nullable<string>;
    
    [Column('ERFASSER_VORNAME', [], 60)]
    FErfasserVorname: Nullable<string>;
    
    [Column('ERFASSER_ANREDE', [], 20)]
    FErfasserAnrede: Nullable<string>;
    
    [Column('ERFASSER_TITEL', [], 20)]
    FErfasserTitel: Nullable<string>;
    
    [Column('KONTROLLORGAN_NACHNAME', [], 60)]
    FKontrollorganNachname: Nullable<string>;
    
    [Column('KONTROLLORGAN_VORNAME', [], 60)]
    FKontrollorganVorname: Nullable<string>;
    
    [Column('KONTROLLORGAN_ANREDE', [], 20)]
    FKontrollorganAnrede: Nullable<string>;
    
    [Column('KONTROLLORGAN_TITEL', [], 20)]
    FKontrollorganTitel: Nullable<string>;
    
    [Column('MAENGEL_GESAMT', [])]
    FMaengelGesamt: Nullable<Integer>;
    
    [Column('MAENGEL_OFFEN', [])]
    FMaengelOffen: Nullable<Integer>;
    
    [Column('TODO_COUNT', [])]
    FTodoCount: Nullable<Integer>;
    
    [Column('EFFECTIVE_DATE', [])]
    FEffectiveDate: Nullable<TDateTime>;
    
    [Column('PRIORITAET', [], 1)]
    FPrioritaet: Nullable<string>;
    
    [Column('SEUCHEN_ID', [], 20)]
    FSeuchenId: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BETRIEB', [TColumnProp.Required], 'ID')]
    FBetrieb: Proxy<TBetrieb>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSON_ERFASSER', [], 'ID')]
    FErfasser: Proxy<TPerson>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSON_KONTROLLORGAN', [], 'ID')]
    FKontrollorgan: Proxy<TPerson>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('KONTROLLTYP', [], 'KONTROLLTYP')]
    FKontrolltyp: Proxy<TKontrolltyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_RECHTSGRUNDLAGE', [TColumnProp.Required], 'ID')]
    FRechtsgrundlage: Proxy<TRechtsgrundlage>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB', [], 'GUID')]
    FUnterschriftAnwesenderBetrieb: Proxy<TUnterschrift>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_UNTERSCHRIFT_KONTROLLORGAN', [], 'GUID')]
    FUnterschriftKontrollorgan: Proxy<TUnterschrift>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_DOKUMENT', [], 'GUID')]
    FDokument: Proxy<TDokument>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_DOKUMENT_CC', [], 'GUID')]
    FDokumentCC: Proxy<TDokument>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE_QUELLE', [], 'ID')]
    FGruppeQuelle: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_REVISIONSPLAN', [], 'ID')]
    FRevisionsplan: Proxy<TRevisionsplan>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FTodos: Proxy<TList<TTodo>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBericht')]
    FBewerteteFragen: Proxy<TList<TBewerteteFrage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FProben: Proxy<TList<TKbProbe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FAnwesende: Proxy<TList<TAnwesender>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FCckAuswahldaten: Proxy<TList<TCckAuswahl>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FOertlichkeiten: Proxy<TList<TKontrollberichtOertlichkeit>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollbericht')]
    FCckBetriebsdaten: Proxy<TList<TCckBetrieb>>;
    function GetBetrieb: TBetrieb;
    procedure SetBetrieb(const Value: TBetrieb);
    function GetErfasser: TPerson;
    procedure SetErfasser(const Value: TPerson);
    function GetKontrollorgan: TPerson;
    procedure SetKontrollorgan(const Value: TPerson);
    function GetKontrolltyp: TKontrolltyp;
    procedure SetKontrolltyp(const Value: TKontrolltyp);
    function GetRechtsgrundlage: TRechtsgrundlage;
    procedure SetRechtsgrundlage(const Value: TRechtsgrundlage);
    function GetUnterschriftAnwesenderBetrieb: TUnterschrift;
    procedure SetUnterschriftAnwesenderBetrieb(const Value: TUnterschrift);
    function GetUnterschriftKontrollorgan: TUnterschrift;
    procedure SetUnterschriftKontrollorgan(const Value: TUnterschrift);
    function GetDokument: TDokument;
    procedure SetDokument(const Value: TDokument);
    function GetDokumentCC: TDokument;
    procedure SetDokumentCC(const Value: TDokument);
    function GetGruppeQuelle: TGruppe;
    procedure SetGruppeQuelle(const Value: TGruppe);
    function GetRevisionsplan: TRevisionsplan;
    procedure SetRevisionsplan(const Value: TRevisionsplan);
    function GetTodos: TList<TTodo>;
    function GetBewerteteFragen: TList<TBewerteteFrage>;
    function GetProben: TList<TKbProbe>;
    function GetAnwesende: TList<TAnwesender>;
    function GetCckAuswahldaten: TList<TCckAuswahl>;
    function GetOertlichkeiten: TList<TKontrollberichtOertlichkeit>;
    function GetCckBetriebsdaten: TList<TCckBetrieb>;
  protected
    [Transient, XdataProperty]
    FSort: Integer;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Guid: TGuid read FGuid write FGuid;
    property Bkb: string read FBkb write FBkb;
    property Datum: Nullable<TDate> read FDatum write FDatum;
    property RefBkb: Nullable<string> read FRefBkb write FRefBkb;
    property Probenziehung: Boolean read FProbenziehung write FProbenziehung;
    property RegnrOrt: Nullable<string> read FRegnrOrt write FRegnrOrt;
    property Kurzbemerkung: Nullable<WideString> read FKurzbemerkung write FKurzbemerkung;
    property Startzeit: Nullable<TDateTime> read FStartzeit write FStartzeit;
    property Endezeit: Nullable<TDateTime> read FEndezeit write FEndezeit;
    property BestaetigtUm: Nullable<TDateTime> read FBestaetigtUm write FBestaetigtUm;
    property Status: Nullable<string> read FStatus write FStatus;
    property AngemeldetUm: Nullable<TDateTime> read FAngemeldetUm write FAngemeldetUm;
    property TstampInsert: Nullable<TDateTime> read FTstampInsert write FTstampInsert;
    property Lastchange: Nullable<TDateTime> read FLastchange write FLastchange;
    property Betriebstyp: string read FBetriebstyp write FBetriebstyp;
    property Verweigerungsgrund: Nullable<string> read FVerweigerungsgrund write FVerweigerungsgrund;
    property VerweigerungsgrundUnterschrift: Nullable<string> read FVerweigerungsgrundUnterschrift write FVerweigerungsgrundUnterschrift;
    property FehlerhaftGesetztAm: Nullable<TDateTime> read FFehlerhaftGesetztAm write FFehlerhaftGesetztAm;
    property StorniertAm: Nullable<TDateTime> read FStorniertAm write FStorniertAm;
    property Stornogrund: Nullable<string> read FStornogrund write FStornogrund;
    property VerweigertAm: Nullable<TDateTime> read FVerweigertAm write FVerweigertAm;
    property KontrollInformationen: Nullable<string> read FKontrollInformationen write FKontrollInformationen;
    property InterneNotiz: Nullable<WideString> read FInterneNotiz write FInterneNotiz;
    property BetriebName: Nullable<string> read FBetriebName write FBetriebName;
    property BetriebRegnr: Nullable<string> read FBetriebRegnr write FBetriebRegnr;
    property BetriebBld: Nullable<Integer> read FBetriebBld write FBetriebBld;
    property BetriebAdresseStrasse: Nullable<string> read FBetriebAdresseStrasse write FBetriebAdresseStrasse;
    property BetriebAdresseZusatz: Nullable<string> read FBetriebAdresseZusatz write FBetriebAdresseZusatz;
    property BetriebAdressePlz: Nullable<string> read FBetriebAdressePlz write FBetriebAdressePlz;
    property BetriebAdresseOrt: Nullable<string> read FBetriebAdresseOrt write FBetriebAdresseOrt;
    property KontrolltypBezeichnung: Nullable<string> read FKontrolltypBezeichnung write FKontrolltypBezeichnung;
    property BkbtypBezeichnung: Nullable<string> read FBkbtypBezeichnung write FBkbtypBezeichnung;
    property ErfasserNachname: Nullable<string> read FErfasserNachname write FErfasserNachname;
    property ErfasserVorname: Nullable<string> read FErfasserVorname write FErfasserVorname;
    property ErfasserAnrede: Nullable<string> read FErfasserAnrede write FErfasserAnrede;
    property ErfasserTitel: Nullable<string> read FErfasserTitel write FErfasserTitel;
    property KontrollorganNachname: Nullable<string> read FKontrollorganNachname write FKontrollorganNachname;
    property KontrollorganVorname: Nullable<string> read FKontrollorganVorname write FKontrollorganVorname;
    property KontrollorganAnrede: Nullable<string> read FKontrollorganAnrede write FKontrollorganAnrede;
    property KontrollorganTitel: Nullable<string> read FKontrollorganTitel write FKontrollorganTitel;
    property MaengelGesamt: Nullable<Integer> read FMaengelGesamt write FMaengelGesamt;
    property MaengelOffen: Nullable<Integer> read FMaengelOffen write FMaengelOffen;
    property TodoCount: Nullable<Integer> read FTodoCount write FTodoCount;
    property EffectiveDate: Nullable<TDateTime> read FEffectiveDate write FEffectiveDate;
    property Prioritaet: Nullable<string> read FPrioritaet write FPrioritaet;
    property SeuchenId: Nullable<string> read FSeuchenId write FSeuchenId;
    property Betrieb: TBetrieb read GetBetrieb write SetBetrieb;
    property Erfasser: TPerson read GetErfasser write SetErfasser;
    property Kontrollorgan: TPerson read GetKontrollorgan write SetKontrollorgan;
    property Kontrolltyp: TKontrolltyp read GetKontrolltyp write SetKontrolltyp;
    property Rechtsgrundlage: TRechtsgrundlage read GetRechtsgrundlage write SetRechtsgrundlage;
    property UnterschriftAnwesenderBetrieb: TUnterschrift read GetUnterschriftAnwesenderBetrieb write SetUnterschriftAnwesenderBetrieb;
    property UnterschriftKontrollorgan: TUnterschrift read GetUnterschriftKontrollorgan write SetUnterschriftKontrollorgan;
    property Dokument: TDokument read GetDokument write SetDokument;
    property DokumentCC: TDokument read GetDokumentCC write SetDokumentCC;
    property GruppeQuelle: TGruppe read GetGruppeQuelle write SetGruppeQuelle;
    property Revisionsplan: TRevisionsplan read GetRevisionsplan write SetRevisionsplan;
    property Todos: TList<TTodo> read GetTodos;
    property BewerteteFragen: TList<TBewerteteFrage> read GetBewerteteFragen;
    property Proben: TList<TKbProbe> read GetProben;
    property Anwesende: TList<TAnwesender> read GetAnwesende;
    property CckAuswahldaten: TList<TCckAuswahl> read GetCckAuswahldaten;
    property Oertlichkeiten: TList<TKontrollberichtOertlichkeit> read GetOertlichkeiten;
    property CckBetriebsdaten: TList<TCckBetrieb> read GetCckBetriebsdaten;
  end;
  
  [Entity]
  [Table('[KONTROLLBERICHT_BILDER]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.Guid)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrollberichtBild = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: TGuid;
    
    [Column('BILD', [TColumnProp.Required, TColumnProp.Lazy])]
    FBild: TBlob;
    
    [Column('FORMAT', [TColumnProp.Required], 3)]
    FFormat: string;
    
    [Column('BEMERKUNG', [TColumnProp.Required])]
    [DBTypeMemo]
    FBemerkung: string;
    
    [Column('AUFNAHMEDATUM', [TColumnProp.Required])]
    FAufnahmedatum: TDateTime;
    
    [Column('GPS_LAT', [], 12, 9)]
    FGpsLat: Nullable<Double>;
    
    [Column('GPS_LON', [], 12, 9)]
    FGpsLon: Nullable<Double>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BEWERTETEFRAGE', [], 'ID')]
    FBewerteteFrage: Proxy<TBewerteteFrage>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGEL', [], 'ID')]
    FMangel: Proxy<TMangel>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_AUFGENOMMEN_VON', [TColumnProp.Required], 'ID')]
    FAufgenommenVon: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PROBE', [], 'ID')]
    FProbe: Proxy<TKbProbe>;
    function GetBewerteteFrage: TBewerteteFrage;
    procedure SetBewerteteFrage(const Value: TBewerteteFrage);
    function GetMangel: TMangel;
    procedure SetMangel(const Value: TMangel);
    function GetAufgenommenVon: TUser;
    procedure SetAufgenommenVon(const Value: TUser);
    function GetProbe: TKbProbe;
    procedure SetProbe(const Value: TKbProbe);
  public
    property Id: TGuid read FId write FId;
    property Bild: TBlob read FBild write FBild;
    property Format: string read FFormat write FFormat;
    property Bemerkung: string read FBemerkung write FBemerkung;
    property Aufnahmedatum: TDateTime read FAufnahmedatum write FAufnahmedatum;
    property GpsLat: Nullable<Double> read FGpsLat write FGpsLat;
    property GpsLon: Nullable<Double> read FGpsLon write FGpsLon;
    property BewerteteFrage: TBewerteteFrage read GetBewerteteFrage write SetBewerteteFrage;
    property Mangel: TMangel read GetMangel write SetMangel;
    property AufgenommenVon: TUser read GetAufgenommenVon write SetAufgenommenVon;
    property Probe: TKbProbe read GetProbe write SetProbe;
  end;
  
  [Entity]
  [Table('[KONTROLLBERICHT_OERTLICHKEITEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.Guid)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrollberichtOertlichkeit = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: TGuid;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 255)]
    FBezeichnung: string;
    
    [Column('BAUTYP', [], 255)]
    FBautyp: Nullable<string>;
    
    [Column('HALTUNGSFORM', [], 255)]
    FHaltungsform: Nullable<string>;
    
    [Column('Krankheiten', [TColumnProp.Required])]
    FKrankheiten: Boolean;
    
    [Column('Krankheitsbeschreibung', [], 500)]
    FKrankheitsbeschreibung: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [TColumnProp.Required], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FOertlichkeit')]
    FOertlichkeitMaengel: Proxy<TList<TMangelOertlichkeit>>;
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
    function GetOertlichkeitMaengel: TList<TMangelOertlichkeit>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: TGuid read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Bautyp: Nullable<string> read FBautyp write FBautyp;
    property Haltungsform: Nullable<string> read FHaltungsform write FHaltungsform;
    property Krankheiten: Boolean read FKrankheiten write FKrankheiten;
    property Krankheitsbeschreibung: Nullable<string> read FKrankheitsbeschreibung write FKrankheitsbeschreibung;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
    property OertlichkeitMaengel: TList<TMangelOertlichkeit> read GetOertlichkeitMaengel;
  end;
  
  [Entity]
  [Table('[KONTROLLTYPEN]', 'STAMMDATEN')]
  [Id('FBkbtyp', TIdGenerator.None)]
  [Id('FKontrolltyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrolltyp = class(TObject)
  private
    [Column('KONTROLLTYP', [TColumnProp.Required], 3)]
    FKontrolltyp: string;
    
    [Column('BEZEICHNUNG', [], 80)]
    FBezeichnung: Nullable<string>;
    
    [Column('REFBKB', [])]
    FRefbkb: Nullable<Integer>;
    
    [Column('FREIE_ADRESSE', [TColumnProp.Required])]
    FFreieAdresse: Boolean;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Column('PROBEN', [TColumnProp.Required])]
    FProben: Boolean;
    
    [Column('OERTLICHKEITEN', [TColumnProp.Required])]
    FOertlichkeiten: Boolean;
    
    [Column('AUTO_KONTROLLIERT', [TColumnProp.Required])]
    FAutoKontrolliert: Boolean;
    
    [Association([TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    FBkbtyp: TBkbTyp;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrolltyp')]
    FKontrollberichte: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FBkbtyp')]
    FCckModulKontrolltypen: Proxy<TList<TCckModulKontrolltyp>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrolltyp')]
    FKontrolltypReports: Proxy<TList<TKontrolltypReport>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrolltyp')]
    FChecklistenKontrolltypen: Proxy<TList<TChecklistenKontrolltyp>>;
    function GetKontrollberichte: TList<TKontrollbericht>;
    function GetCckModulKontrolltypen: TList<TCckModulKontrolltyp>;
    function GetKontrolltypReports: TList<TKontrolltypReport>;
    function GetChecklistenKontrolltypen: TList<TChecklistenKontrolltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Kontrolltyp: string read FKontrolltyp write FKontrolltyp;
    property Bezeichnung: Nullable<string> read FBezeichnung write FBezeichnung;
    property Refbkb: Nullable<Integer> read FRefbkb write FRefbkb;
    property FreieAdresse: Boolean read FFreieAdresse write FFreieAdresse;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Proben: Boolean read FProben write FProben;
    property Oertlichkeiten: Boolean read FOertlichkeiten write FOertlichkeiten;
    property AutoKontrolliert: Boolean read FAutoKontrolliert write FAutoKontrolliert;
    property Bkbtyp: TBkbTyp read FBkbtyp write FBkbtyp;
    property Kontrollberichte: TList<TKontrollbericht> read GetKontrollberichte;
    property CckModulKontrolltypen: TList<TCckModulKontrolltyp> read GetCckModulKontrolltypen;
    property KontrolltypReports: TList<TKontrolltypReport> read GetKontrolltypReports;
    property ChecklistenKontrolltypen: TList<TChecklistenKontrolltyp> read GetChecklistenKontrolltypen;
  end;
  
  [Entity]
  [Table('[KONTROLLTYPEN_REPORTS]', 'STAMMDATEN')]
  [UniqueKey('BKBTYP, KONTROLLTYP, GUID_REPORT, BLDCODE')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TKontrolltypReport = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('VORSCHAU', [TColumnProp.Required])]
    FVorschau: Boolean;
    
    [Column('MAILBETRIEB', [TColumnProp.Required])]
    FMailbetrieb: Boolean;
    
    [Column('MAILKONTROLLEUR', [TColumnProp.Required])]
    FMailkontrolleur: Boolean;
    
    [Column('MAILGRUPPE', [TColumnProp.Required])]
    FMailgruppe: Boolean;
    
    [Column('MAILMUTTERGRUPPE', [TColumnProp.Required])]
    FMailmuttergruppe: Boolean;
    
    [Column('MAILMASTERGRUPPE', [TColumnProp.Required])]
    FMailmastergruppe: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('KONTROLLTYP', [TColumnProp.Required], 'KONTROLLTYP')]
    FKontrolltyp: Proxy<TKontrolltyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GUID_REPORT', [TColumnProp.Required], 'GUID')]
    FReport: Proxy<TReport>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REPORT_TYP', [TColumnProp.Required], 'Typ')]
    FReportTyp: Proxy<TReportTyp>;
    function GetKontrolltyp: TKontrolltyp;
    procedure SetKontrolltyp(const Value: TKontrolltyp);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetReport: TReport;
    procedure SetReport(const Value: TReport);
    function GetReportTyp: TReportTyp;
    procedure SetReportTyp(const Value: TReportTyp);
  public
    property Guid: TGuid read FGuid write FGuid;
    property Vorschau: Boolean read FVorschau write FVorschau;
    property Mailbetrieb: Boolean read FMailbetrieb write FMailbetrieb;
    property Mailkontrolleur: Boolean read FMailkontrolleur write FMailkontrolleur;
    property Mailgruppe: Boolean read FMailgruppe write FMailgruppe;
    property Mailmuttergruppe: Boolean read FMailmuttergruppe write FMailmuttergruppe;
    property Mailmastergruppe: Boolean read FMailmastergruppe write FMailmastergruppe;
    property Kontrolltyp: TKontrolltyp read GetKontrolltyp write SetKontrolltyp;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Report: TReport read GetReport write SetReport;
    property ReportTyp: TReportTyp read GetReportTyp write SetReportTyp;
  end;
  
  [Entity]
  [Table('[LAENDER]', 'SYSTEMSTAMMDATEN')]
  [Id('FLandkz', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TLand = class(TObject)
  private
    [Column('LANDKZ', [TColumnProp.Required], 2)]
    FLandkz: string;
    
    [Column('LANDNR', [TColumnProp.Required])]
    FLandnr: Integer;
    
    [Column('LAND_ISO3', [TColumnProp.Required], 3)]
    FLandIso3: string;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 100)]
    FBezeichnung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FLand')]
    FBundeslaender: Proxy<TList<TBundesland>>;
    function GetBundeslaender: TList<TBundesland>;
  public
    constructor Create;
    destructor Destroy; override;
    property Landkz: string read FLandkz write FLandkz;
    property Landnr: Integer read FLandnr write FLandnr;
    property LandIso3: string read FLandIso3 write FLandIso3;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Bundeslaender: TList<TBundesland> read GetBundeslaender;
  end;
  
  [Entity]
  [Table('[MAENGEL]', 'BEWEGUNGSDATEN')]
  [UniqueKey('GUID')]
  [UniqueKey('ID_MANGEL_WEITERFUEHRUNG')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TMangel = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('FRIST', [])]
    FFrist: Nullable<TDateTime>;
    
    [Column('TEXT', [TColumnProp.Required])]
    [DBTypeMemo]
    FText: String;
    
    [Column('BESEITIGT_AM', [])]
    FBeseitigtAm: Nullable<TDateTime>;
    
    [Column('STORNIERT_AM', [])]
    FStorniertAm: Nullable<TDateTime>;
    
    [Column('BEHEBUNGSAUFTRAG', [], 4000)]
    FBehebungsauftrag: Nullable<string>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('STATUS', [], 'STATUS')]
    FStatus: Proxy<TMangelStatus>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGELTYP', [TColumnProp.Required], 'ID')]
    FMangeltyp: Proxy<TMangeltyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MASSNAHME', [TColumnProp.Required], 'ID')]
    FMassnahme: Proxy<TMassnahme>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGEL_WEITERFUEHRUNG', [], 'ID')]
    FMangelWeiterfuehrung: Proxy<TMangel>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangel')]
    FBewerteteFragen: Proxy<TList<TBewerteteFrage>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangel')]
    FBilder: Proxy<TList<TKontrollberichtBild>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangel')]
    FMaengelKontrollbereiche: Proxy<TList<TMangelKontrollbereich>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangelWeiterfuehrung')]
    FWeitergefuehrtVonMangel: Proxy<TList<TMangel>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangel')]
    FMangelOertlichkeiten: Proxy<TList<TMangelOertlichkeit>>;
    function GetStatus: TMangelStatus;
    procedure SetStatus(const Value: TMangelStatus);
    function GetMangeltyp: TMangeltyp;
    procedure SetMangeltyp(const Value: TMangeltyp);
    function GetMassnahme: TMassnahme;
    procedure SetMassnahme(const Value: TMassnahme);
    function GetMangelWeiterfuehrung: TMangel;
    procedure SetMangelWeiterfuehrung(const Value: TMangel);
    function GetBewerteteFragen: TList<TBewerteteFrage>;
    function GetBilder: TList<TKontrollberichtBild>;
    function GetMaengelKontrollbereiche: TList<TMangelKontrollbereich>;
    function GetWeitergefuehrtVonMangel: TList<TMangel>;
    function GetMangelOertlichkeiten: TList<TMangelOertlichkeit>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Guid: TGuid read FGuid write FGuid;
    property Frist: Nullable<TDateTime> read FFrist write FFrist;
    property Text: String read FText write FText;
    property BeseitigtAm: Nullable<TDateTime> read FBeseitigtAm write FBeseitigtAm;
    property StorniertAm: Nullable<TDateTime> read FStorniertAm write FStorniertAm;
    property Behebungsauftrag: Nullable<string> read FBehebungsauftrag write FBehebungsauftrag;
    property Status: TMangelStatus read GetStatus write SetStatus;
    property Mangeltyp: TMangeltyp read GetMangeltyp write SetMangeltyp;
    property Massnahme: TMassnahme read GetMassnahme write SetMassnahme;
    property MangelWeiterfuehrung: TMangel read GetMangelWeiterfuehrung write SetMangelWeiterfuehrung;
    property BewerteteFragen: TList<TBewerteteFrage> read GetBewerteteFragen;
    property Bilder: TList<TKontrollberichtBild> read GetBilder;
    property MaengelKontrollbereiche: TList<TMangelKontrollbereich> read GetMaengelKontrollbereiche;
    property WeitergefuehrtVonMangel: TList<TMangel> read GetWeitergefuehrtVonMangel;
    property MangelOertlichkeiten: TList<TMangelOertlichkeit> read GetMangelOertlichkeiten;
  end;
  
  [Entity]
  [Table('[MAENGEL_KONTROLLBEREICHE]', 'STAMMDATEN')]
  [Id('FMangel', TIdGenerator.None)]
  [Id('FKontrollbereich', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TMangelKontrollbereich = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGEL', [TColumnProp.Required], 'ID')]
    FMangel: Proxy<TMangel>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBEREICH', [TColumnProp.Required], 'ID')]
    FKontrollbereich: Proxy<TKontrollbereich>;
    function GetMangel: TMangel;
    procedure SetMangel(const Value: TMangel);
    function GetKontrollbereich: TKontrollbereich;
    procedure SetKontrollbereich(const Value: TKontrollbereich);
  public
    property Mangel: TMangel read GetMangel write SetMangel;
    property Kontrollbereich: TKontrollbereich read GetKontrollbereich write SetKontrollbereich;
  end;
  
  [Entity]
  [Table('[MAENGEL_OERTLICHKEITEN]', 'BEWEGUNGSDATEN')]
  [Id('FMangel', TIdGenerator.None)]
  [Id('FOertlichkeit', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TMangelOertlichkeit = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MANGEL', [TColumnProp.Required], 'ID')]
    FMangel: Proxy<TMangel>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_OERTLICHKEIT', [TColumnProp.Required], 'ID')]
    FOertlichkeit: Proxy<TKontrollberichtOertlichkeit>;
    function GetMangel: TMangel;
    procedure SetMangel(const Value: TMangel);
    function GetOertlichkeit: TKontrollberichtOertlichkeit;
    procedure SetOertlichkeit(const Value: TKontrollberichtOertlichkeit);
  public
    property Mangel: TMangel read GetMangel write SetMangel;
    property Oertlichkeit: TKontrollberichtOertlichkeit read GetOertlichkeit write SetOertlichkeit;
  end;
  
  [Entity]
  [Table('[MANGEL_STATUS]', 'STAMMDATEN')]
  [Id('FStatus', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TMangelStatus = class(TObject)
  private
    [Column('STATUS', [TColumnProp.Required], 1)]
    FStatus: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 200)]
    FBeschreibung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FStatus')]
    FMaengel: Proxy<TList<TMangel>>;
    function GetMaengel: TList<TMangel>;
  public
    constructor Create;
    destructor Destroy; override;
    property Status: string read FStatus write FStatus;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
    property Maengel: TList<TMangel> read GetMaengel;
  end;
  
  [Entity]
  [Table('[MANGELTYPEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TMangeltyp = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [], 200)]
    FBezeichnung: Nullable<string>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    FBkbtyp: Proxy<TBkbTyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MASSNAHMENKATALOG', [TColumnProp.Required], 'ID')]
    FMassnahmenkatalog: Proxy<TMassnahmenkatalog>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMangeltyp')]
    FMaengel: Proxy<TList<TMangel>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FStandardMangeltyp')]
    FFragenBewertungen: Proxy<TList<TFrageBewertung>>;
    function GetBkbtyp: TBkbTyp;
    procedure SetBkbtyp(const Value: TBkbTyp);
    function GetMassnahmenkatalog: TMassnahmenkatalog;
    procedure SetMassnahmenkatalog(const Value: TMassnahmenkatalog);
    function GetMaengel: TList<TMangel>;
    function GetFragenBewertungen: TList<TFrageBewertung>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: Nullable<string> read FBezeichnung write FBezeichnung;
    property Bkbtyp: TBkbTyp read GetBkbtyp write SetBkbtyp;
    property Massnahmenkatalog: TMassnahmenkatalog read GetMassnahmenkatalog write SetMassnahmenkatalog;
    property Maengel: TList<TMangel> read GetMaengel;
    property FragenBewertungen: TList<TFrageBewertung> read GetFragenBewertungen;
  end;
  
  [Entity]
  [Table('[MASSNAHMEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TMassnahme = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('LANGTEXT', [TColumnProp.Required])]
    [DBTypeMemo]
    FLangtext: String;
    
    [Column('STANDARDFRIST', [])]
    FStandardfrist: Nullable<Integer>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MASSNAHMENKATALOG', [TColumnProp.Required], 'ID')]
    FMassnahmenkatalog: Proxy<TMassnahmenkatalog>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FStandardMassnahme')]
    FFragenBewertungen: Proxy<TList<TFrageBewertung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMassnahme')]
    FMaengel: Proxy<TList<TMangel>>;
    function GetMassnahmenkatalog: TMassnahmenkatalog;
    procedure SetMassnahmenkatalog(const Value: TMassnahmenkatalog);
    function GetFragenBewertungen: TList<TFrageBewertung>;
    function GetMaengel: TList<TMangel>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Langtext: String read FLangtext write FLangtext;
    property Standardfrist: Nullable<Integer> read FStandardfrist write FStandardfrist;
    property Massnahmenkatalog: TMassnahmenkatalog read GetMassnahmenkatalog write SetMassnahmenkatalog;
    property FragenBewertungen: TList<TFrageBewertung> read GetFragenBewertungen;
    property Maengel: TList<TMangel> read GetMaengel;
  end;
  
  [Entity]
  [Table('[MASSNAHMENKATALOG]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TMassnahmenkatalog = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 200)]
    FBezeichnung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMassnahmenkatalog')]
    FMassnahmen: Proxy<TList<TMassnahme>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FMassnahmenkatalog')]
    FMangeltypen: Proxy<TList<TMangeltyp>>;
    function GetMassnahmen: TList<TMassnahme>;
    function GetMangeltypen: TList<TMangeltyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Massnahmen: TList<TMassnahme> read GetMassnahmen;
    property Mangeltypen: TList<TMangeltyp> read GetMangeltypen;
  end;
  
  [Entity]
  [Table('[MODULE]', 'SYSTEMSTAMMDATEN')]
  [Id('FModul', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TModul = class(TObject)
  private
    [Column('MODUL', [TColumnProp.Required], 10)]
    FModul: string;
    
    [Column('BEZEICHNUNG', [], 50)]
    FBezeichnung: Nullable<string>;
    
    [Column('TSTAMP_INSERT', [])]
    FTstampInsert: Nullable<TDateTime>;
    
    [Column('INS_DBUSER', [], 30)]
    FInsDbuser: Nullable<string>;
    
    [Column('LASTCHANGE', [])]
    FLastchange: Nullable<TDateTime>;
    
    [Column('CHANGE_USER', [], 30)]
    FChangeUser: Nullable<string>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FModul')]
    FBundeslaenderModule: Proxy<TList<TBundeslandModul>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FModul')]
    FBkbtypen: Proxy<TList<TBkbTyp>>;
    function GetBundeslaenderModule: TList<TBundeslandModul>;
    function GetBkbtypen: TList<TBkbTyp>;
  public
    constructor Create;
    destructor Destroy; override;
    property Modul: string read FModul write FModul;
    property Bezeichnung: Nullable<string> read FBezeichnung write FBezeichnung;
    property TstampInsert: Nullable<TDateTime> read FTstampInsert write FTstampInsert;
    property InsDbuser: Nullable<string> read FInsDbuser write FInsDbuser;
    property Lastchange: Nullable<TDateTime> read FLastchange write FLastchange;
    property ChangeUser: Nullable<string> read FChangeUser write FChangeUser;
    property BundeslaenderModule: TList<TBundeslandModul> read GetBundeslaenderModule;
    property Bkbtypen: TList<TBkbTyp> read GetBkbtypen;
  end;
  
  [Entity]
  [Table('[MODULE_BKBTYPEN]', 'STAMMDATEN')]
  [Id('FIdModul', TIdGenerator.None)]
  [Id('FBkbtyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TModuleBkbtypen = class(TObject)
  private
    [Column('ID_MODUL', [TColumnProp.Required])]
    FIdModul: Integer;
    
    [Column('BKBTYP', [TColumnProp.Required], 10)]
    FBkbtyp: string;
  public
    property IdModul: Integer read FIdModul write FIdModul;
    property Bkbtyp: string read FBkbtyp write FBkbtyp;
  end;
  
  [Entity]
  [Table('[MODUL_INSTANZEN]', 'STAMMDATEN')]
  [UniqueKey('INSTANZ_URL')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TModulInstanz = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('INSTANZ_NAME', [TColumnProp.Required], 100)]
    FInstanzName: string;
    
    [Column('INSTANZ_URL', [TColumnProp.Required], 200)]
    FInstanzUrl: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('MODUL', [TColumnProp.Required], 'KURZBEZEICHNNUNG')]
    FModul: Proxy<TProgrammModul>;
    function GetModul: TProgrammModul;
    procedure SetModul(const Value: TProgrammModul);
  public
    property Id: Integer read FId write FId;
    property InstanzName: string read FInstanzName write FInstanzName;
    property InstanzUrl: string read FInstanzUrl write FInstanzUrl;
    property Modul: TProgrammModul read GetModul write SetModul;
  end;
  
  [Entity]
  [Table('[NACHRICHTEN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TNachricht = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('STATUS', [TColumnProp.Required], 1)]
    FStatus: string;
    
    [Column('TYP', [TColumnProp.Required], 1)]
    FTyp: string;
    
    [Column('ABSENDER_KZ', [TColumnProp.Required], 1)]
    FAbsenderKz: string;
    
    [Column('PRIORI', [TColumnProp.Required])]
    FPriori: Integer;
    
    [Column('TEXT', [], 1000)]
    FText: Nullable<string>;
    
    [Column('LINK', [], 200)]
    FLink: Nullable<string>;
    
    [Column('GESENDET_AM', [TColumnProp.Required])]
    FGesendetAm: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ABSENDER', [], 'ID')]
    FAbsender: Proxy<TUser>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge, TCascadeType.Remove], 'FNachricht')]
    FZustellungen: Proxy<TList<TNachrichtenZustellung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FNachricht')]
    FVNachrichtenUser: Proxy<TList<TVNachrichtForUser>>;
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
    function GetAbsender: TUser;
    procedure SetAbsender(const Value: TUser);
    function GetZustellungen: TList<TNachrichtenZustellung>;
    function GetVNachrichtenUser: TList<TVNachrichtForUser>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Status: string read FStatus write FStatus;
    property Typ: string read FTyp write FTyp;
    property AbsenderKz: string read FAbsenderKz write FAbsenderKz;
    property Priori: Integer read FPriori write FPriori;
    property Text: Nullable<string> read FText write FText;
    property Link: Nullable<string> read FLink write FLink;
    property GesendetAm: TDateTime read FGesendetAm write FGesendetAm;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
    property Absender: TUser read GetAbsender write SetAbsender;
    property Zustellungen: TList<TNachrichtenZustellung> read GetZustellungen;
    property VNachrichtenUser: TList<TVNachrichtForUser> read GetVNachrichtenUser;
  end;
  
  [Entity]
  [Table('[NACHRICHTEN_ZUSTELLUNG]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TNachrichtenZustellung = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('GESEHEN_TSTAMP', [])]
    FGesehenTstamp: Nullable<TDateTime>;
    
    [Column('GELESEN_TSTAMP', [])]
    FGelesenTstamp: Nullable<TDateTime>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_NACHRICHT', [TColumnProp.Required], 'ID')]
    FNachricht: Proxy<TNachricht>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    function GetNachricht: TNachricht;
    procedure SetNachricht(const Value: TNachricht);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
  public
    property Id: Integer read FId write FId;
    property GesehenTstamp: Nullable<TDateTime> read FGesehenTstamp write FGesehenTstamp;
    property GelesenTstamp: Nullable<TDateTime> read FGelesenTstamp write FGelesenTstamp;
    property Nachricht: TNachricht read GetNachricht write SetNachricht;
    property User: TUser read GetUser write SetUser;
  end;
  
  [Entity]
  [Table('[PERSONEN]', 'STAMMDATEN')]
  [UniqueKey('')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TPerson = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('TITEL', [], 20)]
    FTitel: Nullable<string>;
    
    [Column('VORNAME', [TColumnProp.Required], 60)]
    FVorname: string;
    
    [Column('NACHNAME', [TColumnProp.Required], 60)]
    FNachname: string;
    
    [Column('ADRESSE', [], 100)]
    FAdresseALT: Nullable<string>;
    
    [Column('ORT', [], 50)]
    FOrtALT: Nullable<string>;
    
    [Column('PLZ', [], 7)]
    FPlzALT: Nullable<string>;
    
    [Column('LANDKZ', [], 2)]
    FLandkz: Nullable<string>;
    
    [Column('TELEFON', [], 50)]
    FTelefon: Nullable<string>;
    
    [Column('EMAIL', [], 50)]
    FEmail: Nullable<string>;
    
    [Column('TSTAMP_INSERT', [])]
    FTstampInsert: Nullable<TDateTime>;
    
    [Column('INS_DBUSER', [], 30)]
    FInsDbuser: Nullable<string>;
    
    [Column('LASTCHANGE', [])]
    FLastchange: Nullable<TDateTime>;
    
    [Column('CHANGE_DBUSER', [], 30)]
    FChangeDbuser: Nullable<string>;
    
    [Column('ANREDE', [], 20)]
    FAnrede: Nullable<string>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ADRESSE', [], 'ID')]
    FAdresse: Proxy<TAdresse>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FErfasser')]
    FKontrollberichteErfasser: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FKontrollorgan')]
    FKontrollberichteKontrollorgan: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FPerson')]
    FKommunikationswege: Proxy<TList<TPersonenKommunikationswege>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FPerson')]
    FUsers: Proxy<TList<TUser>>;
    function GetAdresse: TAdresse;
    procedure SetAdresse(const Value: TAdresse);
    function GetKontrollberichteErfasser: TList<TKontrollbericht>;
    function GetKontrollberichteKontrollorgan: TList<TKontrollbericht>;
    function GetKommunikationswege: TList<TPersonenKommunikationswege>;
    function GetUsers: TList<TUser>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Titel: Nullable<string> read FTitel write FTitel;
    property Vorname: string read FVorname write FVorname;
    property Nachname: string read FNachname write FNachname;
    property AdresseALT: Nullable<string> read FAdresseALT write FAdresseALT;
    property OrtALT: Nullable<string> read FOrtALT write FOrtALT;
    property PlzALT: Nullable<string> read FPlzALT write FPlzALT;
    property Landkz: Nullable<string> read FLandkz write FLandkz;
    property Telefon: Nullable<string> read FTelefon write FTelefon;
    property Email: Nullable<string> read FEmail write FEmail;
    property TstampInsert: Nullable<TDateTime> read FTstampInsert write FTstampInsert;
    property InsDbuser: Nullable<string> read FInsDbuser write FInsDbuser;
    property Lastchange: Nullable<TDateTime> read FLastchange write FLastchange;
    property ChangeDbuser: Nullable<string> read FChangeDbuser write FChangeDbuser;
    property Anrede: Nullable<string> read FAnrede write FAnrede;
    property Adresse: TAdresse read GetAdresse write SetAdresse;
    property KontrollberichteErfasser: TList<TKontrollbericht> read GetKontrollberichteErfasser;
    property KontrollberichteKontrollorgan: TList<TKontrollbericht> read GetKontrollberichteKontrollorgan;
    property Kommunikationswege: TList<TPersonenKommunikationswege> read GetKommunikationswege;
    property Users: TList<TUser> read GetUsers;
  end;
  
  [Entity]
  [Table('[PERSONEN_KOMMUNIKATIONSWEGE]', 'BEWEGUNGSDATEN')]
  [Id('FPerson', TIdGenerator.None)]
  [Id('FKommunikationsweg', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TPersonenKommunikationswege = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KOMMUNIKATIONSWEG', [TColumnProp.Required], 'ID')]
    FKommunikationsweg: Proxy<TKommunikationsweg>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSON', [TColumnProp.Required], 'ID')]
    FPerson: Proxy<TPerson>;
    function GetKommunikationsweg: TKommunikationsweg;
    procedure SetKommunikationsweg(const Value: TKommunikationsweg);
    function GetPerson: TPerson;
    procedure SetPerson(const Value: TPerson);
  public
    property Kommunikationsweg: TKommunikationsweg read GetKommunikationsweg write SetKommunikationsweg;
    property Person: TPerson read GetPerson write SetPerson;
  end;
  
  [Entity]
  [Table('[PROGRAMM_MODULE]', 'SYSTEMSTAMMDATEN')]
  [Id('FKurzbezeichnnung', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TProgrammModul = class(TObject)
  private
    [Column('KURZBEZEICHNNUNG', [TColumnProp.Required], 3)]
    FKurzbezeichnnung: string;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 500)]
    FBeschreibung: string;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FProgrammmodul')]
    FFunktionen: Proxy<TList<TFunktion>>;
    function GetFunktionen: TList<TFunktion>;
  public
    constructor Create;
    destructor Destroy; override;
    property Kurzbezeichnnung: string read FKurzbezeichnnung write FKurzbezeichnnung;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
    property Funktionen: TList<TFunktion> read GetFunktionen;
  end;
  
  [Entity]
  [Table('[RECHTSGRUNDLAGEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TRechtsgrundlage = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 300)]
    FBezeichnung: string;
    
    [Column('KURZBEZEICHNUNG', [TColumnProp.Required], 50)]
    FKurzbezeichnung: string;
    
    [Column('UNTERSCHRIFT_ERFORDERLICH', [TColumnProp.Required])]
    FUnterschriftErforderlich: Boolean;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRechtsgrundlage')]
    FKontrollberichte: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRechtsgrundlage')]
    FBkbtypen: Proxy<TList<TBkbtypenRechtsgrundlage>>;
    function GetKontrollberichte: TList<TKontrollbericht>;
    function GetBkbtypen: TList<TBkbtypenRechtsgrundlage>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Kurzbezeichnung: string read FKurzbezeichnung write FKurzbezeichnung;
    property UnterschriftErforderlich: Boolean read FUnterschriftErforderlich write FUnterschriftErforderlich;
    property Kontrollberichte: TList<TKontrollbericht> read GetKontrollberichte;
    property Bkbtypen: TList<TBkbtypenRechtsgrundlage> read GetBkbtypen;
  end;
  
  [Entity]
  [Table('[REGISTRIERUNGEN]', 'STAMMDATEN')]
  [Id('FRegnr', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TRegistrierung = class(TObject)
  private
    [Column('REGNR', [TColumnProp.Required], 7)]
    FRegnr: string;
    
    [Column('FREIER_BETRIEB', [TColumnProp.Required])]
    FFreierBetrieb: Boolean;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRegistrierung')]
    FZulassungen: Proxy<TList<TZulassung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRegistrierung')]
    FBetriebe: Proxy<TList<TBetrieb>>;
    function GetZulassungen: TList<TZulassung>;
    function GetBetriebe: TList<TBetrieb>;
  public
    constructor Create;
    destructor Destroy; override;
    property Regnr: string read FRegnr write FRegnr;
    property FreierBetrieb: Boolean read FFreierBetrieb write FFreierBetrieb;
    property Zulassungen: TList<TZulassung> read GetZulassungen;
    property Betriebe: TList<TBetrieb> read GetBetriebe;
  end;
  
  [Entity]
  [Table('[REPORTS]', 'SYSTEMSTAMMDATEN')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TReport = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 30)]
    FBezeichnung: string;
    
    [Column('BESCHREIBUNG', [], 500)]
    FBeschreibung: Nullable<string>;
    
    [Column('REPORTTYP', [TColumnProp.Required], 3)]
    FReporttyp: string;
    
    [Column('REPORTVORLAGE', [TColumnProp.Lazy])]
    [DBTypeWideMemo]
    FReportvorlage: TBlob;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FReport')]
    FReportKontrolltypen: Proxy<TList<TKontrolltypReport>>;
    function GetReportKontrolltypen: TList<TKontrolltypReport>;
  public
    constructor Create;
    destructor Destroy; override;
    property Guid: TGuid read FGuid write FGuid;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Beschreibung: Nullable<string> read FBeschreibung write FBeschreibung;
    property Reporttyp: string read FReporttyp write FReporttyp;
    property Reportvorlage: TBlob read FReportvorlage write FReportvorlage;
    property ReportKontrolltypen: TList<TKontrolltypReport> read GetReportKontrolltypen;
  end;
  
  [Entity]
  [Table('[REPORT_TYPEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FTyp', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TReportTyp = class(TObject)
  private
    [Column('Typ', [TColumnProp.Required], 2)]
    FTyp: string;
    
    [Column('Bezeichnung', [TColumnProp.Required], 30)]
    FBezeichnung: string;
  public
    property Typ: string read FTyp write FTyp;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
  end;
  
  [Entity]
  [Table('[REVISIONSPLAN]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TRevisionsplan = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('JAHR', [])]
    FJahr: Nullable<Integer>;
    
    [Column('RISIKO_KATEGORIE', [])]
    FRisikoKategorie: Nullable<Integer>;
    
    [Column('J_MINDEST_KONTROLL_FREQUENZ', [])]
    FJMindestKontrollFrequenz: Nullable<Double>;
    
    [Column('ANZ_BETRIEBE_IM_LAND', [])]
    FAnzBetriebeImLand: Nullable<Integer>;
    
    [Column('ANZ_GESAMT_KONTROLLEN', [])]
    FAnzGesamtKontrollen: Nullable<Integer>;
    
    [Column('GESPERRT', [TColumnProp.Required])]
    FGesperrt: Boolean;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BldCode', [TColumnProp.Required], 'BLDCODE')]
    FBldcode: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_REVISIONSSTAMM', [TColumnProp.Required], 'ID')]
    FRevisionsstamm: Proxy<TRevisionsstamm>;
    function GetBldcode: TBundesland;
    procedure SetBldcode(const Value: TBundesland);
    function GetRevisionsstamm: TRevisionsstamm;
    procedure SetRevisionsstamm(const Value: TRevisionsstamm);
  public
    property Id: Integer read FId write FId;
    property Jahr: Nullable<Integer> read FJahr write FJahr;
    property RisikoKategorie: Nullable<Integer> read FRisikoKategorie write FRisikoKategorie;
    property JMindestKontrollFrequenz: Nullable<Double> read FJMindestKontrollFrequenz write FJMindestKontrollFrequenz;
    property AnzBetriebeImLand: Nullable<Integer> read FAnzBetriebeImLand write FAnzBetriebeImLand;
    property AnzGesamtKontrollen: Nullable<Integer> read FAnzGesamtKontrollen write FAnzGesamtKontrollen;
    property Gesperrt: Boolean read FGesperrt write FGesperrt;
    property Bldcode: TBundesland read GetBldcode write SetBldcode;
    property Revisionsstamm: TRevisionsstamm read GetRevisionsstamm write SetRevisionsstamm;
  end;
  
  [Entity]
  [Table('[REVISIONS_SCHEMATA]', 'STAMMDATEN')]
  [Id('FRevSchema', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TRevisionsSchema = class(TObject)
  private
    [Column('REV_SCHEMA', [TColumnProp.Required], 1)]
    FRevSchema: string;
    
    [Column('GEPLANT', [TColumnProp.Required])]
    FGeplant: Boolean;
    
    [Column('BESCHREIBUNG', [TColumnProp.Required], 1000)]
    FBeschreibung: string;
  public
    property RevSchema: string read FRevSchema write FRevSchema;
    property Geplant: Boolean read FGeplant write FGeplant;
    property Beschreibung: string read FBeschreibung write FBeschreibung;
  end;
  
  [Entity]
  [Table('[REVISIONSSTAMM]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TRevisionsstamm = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BLDCODE', [])]
    FBldcode: Nullable<Integer>;
    
    [Column('SEKTION', [], 5)]
    FSektion: Nullable<string>;
    
    [Column('BETRIEBSGRUPPE_LM', [], 20)]
    FBetriebsgruppeLm: Nullable<string>;
    
    [Column('BETRIEBSGRUPPE_DETAIL', [], 10)]
    FBetriebsgruppeDetail: Nullable<string>;
    
    [Column('BETRIEBSART', [], 250)]
    FBetriebsart: Nullable<string>;
    
    [Column('BetriebsGruppenKZ', [TColumnProp.Required], 30)]
    FBetriebsgruppenkz: string;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    [JoinColumn('KONTROLLTYP', [TColumnProp.Required], 'KONTROLLTYP')]
    FKontrollTyp: Proxy<TKontrolltyp>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('Rechtsgrundlage', [], 'ID')]
    FRechtsgrundlage: Proxy<TRechtsgrundlage>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REVISIONS_SCHEMA', [], 'REV_SCHEMA')]
    FRevisionsSchema: Proxy<TRevisionsSchema>;
    function GetKontrollTyp: TKontrolltyp;
    procedure SetKontrollTyp(const Value: TKontrolltyp);
    function GetRechtsgrundlage: TRechtsgrundlage;
    procedure SetRechtsgrundlage(const Value: TRechtsgrundlage);
    function GetRevisionsSchema: TRevisionsSchema;
    procedure SetRevisionsSchema(const Value: TRevisionsSchema);
  public
    property Id: Integer read FId write FId;
    property Bldcode: Nullable<Integer> read FBldcode write FBldcode;
    property Sektion: Nullable<string> read FSektion write FSektion;
    property BetriebsgruppeLm: Nullable<string> read FBetriebsgruppeLm write FBetriebsgruppeLm;
    property BetriebsgruppeDetail: Nullable<string> read FBetriebsgruppeDetail write FBetriebsgruppeDetail;
    property Betriebsart: Nullable<string> read FBetriebsart write FBetriebsart;
    property Betriebsgruppenkz: string read FBetriebsgruppenkz write FBetriebsgruppenkz;
    property KontrollTyp: TKontrolltyp read GetKontrollTyp write SetKontrollTyp;
    property Rechtsgrundlage: TRechtsgrundlage read GetRechtsgrundlage write SetRechtsgrundlage;
    property RevisionsSchema: TRevisionsSchema read GetRevisionsSchema write SetRevisionsSchema;
  end;
  
  [Entity]
  [Table('[ROLLEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TRolle = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('BEZEICHNUNG', [TColumnProp.Required], 50)]
    FBezeichnung: string;
    
    [Column('DEFAULTROLLE', [TColumnProp.Required])]
    FDefaultrolle: Boolean;
    
    [Column('SICHTBAR', [TColumnProp.Required])]
    FSichtbar: Boolean;
    
    [Column('GUELTIG_AB', [TColumnProp.Required])]
    FGueltigAb: TDateTime;
    
    [Column('PARAMETER', [], 500)]
    FParameter: Nullable<string>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRolle')]
    FFunktionenRollen: Proxy<TList<TFunktionRolle>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRolle')]
    FUserRollen: Proxy<TList<TUserrolle>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FRolle')]
    FRollenBkbtypen: Proxy<TList<TRollenBkbtypen>>;
    function GetFunktionenRollen: TList<TFunktionRolle>;
    function GetUserRollen: TList<TUserrolle>;
    function GetRollenBkbtypen: TList<TRollenBkbtypen>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Bezeichnung: string read FBezeichnung write FBezeichnung;
    property Defaultrolle: Boolean read FDefaultrolle write FDefaultrolle;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property GueltigAb: TDateTime read FGueltigAb write FGueltigAb;
    property Parameter: Nullable<string> read FParameter write FParameter;
    property FunktionenRollen: TList<TFunktionRolle> read GetFunktionenRollen;
    property UserRollen: TList<TUserrolle> read GetUserRollen;
    property RollenBkbtypen: TList<TRollenBkbtypen> read GetRollenBkbtypen;
  end;
  
  [Entity]
  [Table('[ROLLEN_BKBTYPEN]', 'STAMMDATEN')]
  [Id('FBkbtyp', TIdGenerator.None)]
  [Id('FRolle', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TRollenBkbtypen = class(TObject)
  private
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BKBTYP', [TColumnProp.Required], 'BKBTYP')]
    FBkbtyp: Proxy<TBkbTyp>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ROLLEN', [TColumnProp.Required], 'ID')]
    FRolle: Proxy<TRolle>;
    function GetBkbtyp: TBkbTyp;
    procedure SetBkbtyp(const Value: TBkbTyp);
    function GetRolle: TRolle;
    procedure SetRolle(const Value: TRolle);
  public
    property Bkbtyp: TBkbTyp read GetBkbtyp write SetBkbtyp;
    property Rolle: TRolle read GetRolle write SetRolle;
  end;
  
  [Entity]
  [Table('[SYS]', 'SYSTEMSTAMMDATEN')]
  [Id('FSystemkz', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TSys = class(TObject)
  private
    [Column('SYSTEMKZ', [TColumnProp.Required], 3)]
    FSystemkz: string;
    
    [Column('SYSTEMNAME', [], 50)]
    FSystemname: Nullable<string>;
    
    [Column('VERSION_DB', [], 10)]
    FVersionDb: Nullable<string>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FSystemkz')]
    FBkbnummern: Proxy<TList<TBkbnummer>>;
    function GetBkbnummern: TList<TBkbnummer>;
  public
    constructor Create;
    destructor Destroy; override;
    property Systemkz: string read FSystemkz write FSystemkz;
    property Systemname: Nullable<string> read FSystemname write FSystemname;
    property VersionDb: Nullable<string> read FVersionDb write FVersionDb;
    property Bkbnummern: TList<TBkbnummer> read GetBkbnummern;
  end;
  
  [Entity]
  [Table('[TODO]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TTodo = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('TITEL', [TColumnProp.Required], 50)]
    FTitel: string;
    
    [Column('FAELLIG', [])]
    FFaellig: Nullable<TDateTime>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [], 'ID')]
    FGruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER', [], 'ID')]
    FUser: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_KONTROLLBERICHT', [], 'ID')]
    FKontrollbericht: Proxy<TKontrollbericht>;
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
    function GetKontrollbericht: TKontrollbericht;
    procedure SetKontrollbericht(const Value: TKontrollbericht);
  public
    property Id: Integer read FId write FId;
    property Titel: string read FTitel write FTitel;
    property Faellig: Nullable<TDateTime> read FFaellig write FFaellig;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
    property User: TUser read GetUser write SetUser;
    property Kontrollbericht: TKontrollbericht read GetKontrollbericht write SetKontrollbericht;
  end;
  
  [Entity]
  [Table('[UNTERSCHRIFTEN]', 'BEWEGUNGSDATEN')]
  [Id('FGuid', TIdGenerator.SmartGuid)]
  [Model('ELKE')]
  [Model('AMA')]
  TUnterschrift = class(TObject)
  private
    [Column('GUID', [TColumnProp.Unique])]
    FGuid: TGuid;
    
    [Column('BILD', [TColumnProp.Required, TColumnProp.Lazy])]
    FBild: TBlob;
    
    [Column('DATUM', [TColumnProp.Required])]
    FDatum: TDateTime;
    
    [Column('NAME', [TColumnProp.Required], 255)]
    FName: string;
    
    [Column('ID_PERSON', [])]
    FIdPerson: Nullable<Integer>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUnterschriftAnwesenderBetrieb')]
    FKontrollberichteAnwesender: Proxy<TList<TKontrollbericht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUnterschriftKontrollorgan')]
    FKontrollberichteKontrollorgan: Proxy<TList<TKontrollbericht>>;
    function GetKontrollberichteAnwesender: TList<TKontrollbericht>;
    function GetKontrollberichteKontrollorgan: TList<TKontrollbericht>;
  public
    constructor Create;
    destructor Destroy; override;
    property Guid: TGuid read FGuid write FGuid;
    property Bild: TBlob read FBild write FBild;
    property Datum: TDateTime read FDatum write FDatum;
    property Name: string read FName write FName;
    property IdPerson: Nullable<Integer> read FIdPerson write FIdPerson;
    property KontrollberichteAnwesender: TList<TKontrollbericht> read GetKontrollberichteAnwesender;
    property KontrollberichteKontrollorgan: TList<TKontrollbericht> read GetKontrollberichteKontrollorgan;
  end;
  
  [Entity]
  [Table('[USER]', 'SYSTEMSTAMMDATEN')]
  [UniqueKey('ID')]
  [UniqueKey('USERGUID')]
  [Id('FId', TIdGenerator.IdentityOrSequence)]
  [Model('ELKE')]
  [Model('AMA')]
  TUser = class(TObject)
  private
    [Column('ID', [TColumnProp.Required, TColumnProp.NoInsert, TColumnProp.NoUpdate])]
    FId: Integer;
    
    [Column('USERNAME', [], 40)]
    FUsername: Nullable<string>;
    
    [Column('USERGUID', [], 128)]
    FUserguid: Nullable<string>;
    
    [Column('USERTYPE', [])]
    FUsertype: Nullable<Integer>;
    
    [Column('GESPERRT', [])]
    FGesperrt: Nullable<Integer>;
    
    [Column('USERPWC', [])]
    FUserpwc: Nullable<Integer>;
    
    [Column('AKTIV', [])]
    FAktiv: Nullable<Integer>;
    
    [Column('TSTAMP_INSERT', [])]
    FTstampInsert: Nullable<TDateTime>;
    
    [Column('INS_DBUSER', [], 30)]
    FInsDbuser: Nullable<string>;
    
    [Column('LASTCHANGE', [])]
    FLastchange: Nullable<TDateTime>;
    
    [Column('CHANGE_DBUSER', [], 30)]
    FChangeDbuser: Nullable<string>;
    
    [Column('EMAIL', [], 100)]
    FEmail: Nullable<string>;
    
    [Column('LAST_LOGIN', [])]
    FLastLogin: Nullable<TDateTime>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_PERSON', [], 'ID')]
    FPerson: Proxy<TPerson>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FHauptverantwortlicher')]
    FStellvertreterFuerGruppen: Proxy<TList<TGruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FAbsender')]
    FNachrichten: Proxy<TList<TNachricht>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUser')]
    FNachrichtenZustellungen: Proxy<TList<TNachrichtenZustellung>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUser')]
    FUsergruppen: Proxy<TList<TUsergruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUser')]
    FUserrollen: Proxy<TList<TUserrolle>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FUser')]
    FTodos: Proxy<TList<TTodo>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FStellvertreter')]
    FHauptverantwortlicherFuerGruppen: Proxy<TList<TGruppe>>;
    
    [ManyValuedAssociation([TAssociationProp.Lazy, TAssociationProp.Required], [TCascadeType.SaveUpdate, TCascadeType.Merge], 'FAufgenommenVon')]
    FKontrollberichtBilder: Proxy<TList<TKontrollberichtBild>>;
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
    function GetPerson: TPerson;
    procedure SetPerson(const Value: TPerson);
    function GetStellvertreterFuerGruppen: TList<TGruppe>;
    function GetNachrichten: TList<TNachricht>;
    function GetNachrichtenZustellungen: TList<TNachrichtenZustellung>;
    function GetUsergruppen: TList<TUsergruppe>;
    function GetUserrollen: TList<TUserrolle>;
    function GetTodos: TList<TTodo>;
    function GetHauptverantwortlicherFuerGruppen: TList<TGruppe>;
    function GetKontrollberichtBilder: TList<TKontrollberichtBild>;
  public
    constructor Create;
    destructor Destroy; override;
    property Id: Integer read FId write FId;
    property Username: Nullable<string> read FUsername write FUsername;
    property Userguid: Nullable<string> read FUserguid write FUserguid;
    property Usertype: Nullable<Integer> read FUsertype write FUsertype;
    property Gesperrt: Nullable<Integer> read FGesperrt write FGesperrt;
    property Userpwc: Nullable<Integer> read FUserpwc write FUserpwc;
    property Aktiv: Nullable<Integer> read FAktiv write FAktiv;
    property TstampInsert: Nullable<TDateTime> read FTstampInsert write FTstampInsert;
    property InsDbuser: Nullable<string> read FInsDbuser write FInsDbuser;
    property Lastchange: Nullable<TDateTime> read FLastchange write FLastchange;
    property ChangeDbuser: Nullable<string> read FChangeDbuser write FChangeDbuser;
    property Email: Nullable<string> read FEmail write FEmail;
    property LastLogin: Nullable<TDateTime> read FLastLogin write FLastLogin;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
    property Person: TPerson read GetPerson write SetPerson;
    property StellvertreterFuerGruppen: TList<TGruppe> read GetStellvertreterFuerGruppen;
    property Nachrichten: TList<TNachricht> read GetNachrichten;
    property NachrichtenZustellungen: TList<TNachrichtenZustellung> read GetNachrichtenZustellungen;
    property Usergruppen: TList<TUsergruppe> read GetUsergruppen;
    property Userrollen: TList<TUserrolle> read GetUserrollen;
    property Todos: TList<TTodo> read GetTodos;
    property HauptverantwortlicherFuerGruppen: TList<TGruppe> read GetHauptverantwortlicherFuerGruppen;
    property KontrollberichtBilder: TList<TKontrollberichtBild> read GetKontrollberichtBilder;
  end;
  
  [Entity]
  [Table('[USERGRUPPEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FUser', TIdGenerator.None)]
  [Id('FGruppe', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TUsergruppe = class(TObject)
  private
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
  public
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
    property User: TUser read GetUser write SetUser;
  end;
  
  [Entity]
  [Table('[USERROLLEN]', 'SYSTEMSTAMMDATEN')]
  [Id('FUser', TIdGenerator.None)]
  [Id('FRolle', TIdGenerator.None)]
  [Id('FModulInstanz', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TUserrolle = class(TObject)
  private
    [Column('BEGDAT', [TColumnProp.Required])]
    FBegdat: TDateTime;
    
    [Column('ENDDAT', [TColumnProp.Required])]
    FEnddat: TDateTime;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_ROLLE', [TColumnProp.Required], 'ID')]
    FRolle: Proxy<TRolle>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_MODUL_INSTANZ', [TColumnProp.Required], 'ID')]
    FModulInstanz: Proxy<TModulInstanz>;
    function GetRolle: TRolle;
    procedure SetRolle(const Value: TRolle);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
    function GetModulInstanz: TModulInstanz;
    procedure SetModulInstanz(const Value: TModulInstanz);
  public
    property Begdat: TDateTime read FBegdat write FBegdat;
    property Enddat: TDateTime read FEnddat write FEnddat;
    property Rolle: TRolle read GetRolle write SetRolle;
    property User: TUser read GetUser write SetUser;
    property ModulInstanz: TModulInstanz read GetModulInstanz write SetModulInstanz;
  end;
  
  [Entity]
  [Table('[vBetriebBezirksGruppe]', 'STAMMDATEN')]
  [Id('FBetrieb', TIdGenerator.None)]
  [Id('FGruppe', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TVbetriebbezirksgruppe = class(TObject)
  private
    [Column('GEMEINDEKENNZIFFER', [TColumnProp.Required])]
    FGemeindekennziffer: Integer;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_BETRIEB', [TColumnProp.Required], 'ID')]
    FBetrieb: Proxy<TBetrieb>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TGruppe>;
    function GetBetrieb: TBetrieb;
    procedure SetBetrieb(const Value: TBetrieb);
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
  public
    property Gemeindekennziffer: Integer read FGemeindekennziffer write FGemeindekennziffer;
    property Betrieb: TBetrieb read GetBetrieb write SetBetrieb;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
  end;
  
  [Entity]
  [Table('[vBetriebsInfo]', 'STAMMDATEN')]
  [Id('FBetriebsId', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TVbetriebsinfo = class(TObject)
  private
    [Column('Betriebs_ID', [TColumnProp.Required])]
    FBetriebsId: Integer;
    
    [Column('REGNR', [TColumnProp.Required], 7)]
    FRegnr: string;
    
    [Column('Betriebsdaten_ID', [])]
    FBetriebsdatenId: Nullable<Integer>;
    
    [Column('NAME', [], 255)]
    FName: Nullable<string>;
    
    [Column('ID_CCK_AUFTRAG', [])]
    FIdCckAuftrag: Nullable<Integer>;
    
    [Column('BETRIEBSTYP', [], 2)]
    FBetriebstyp: Nullable<string>;
    
    [Column('BETRIEBSART', [], 50)]
    FBetriebsart: Nullable<string>;
    
    [Column('REGNR_Hauptbetrieb', [], 15)]
    FRegnrHauptbetrieb: Nullable<string>;
    
    [Column('ID_Hauptbetrieb', [])]
    FIdHauptbetrieb: Nullable<Integer>;
    
    [Column('ORT', [], 50)]
    FOrt: Nullable<string>;
    
    [Column('Taetigkeiten', [], 8000)]
    FTaetigkeiten: Nullable<string>;
    
    [Column('Zulassungsnummern', [], 8000)]
    FZulassungsnummern: Nullable<string>;
  public
    property BetriebsId: Integer read FBetriebsId write FBetriebsId;
    property Regnr: string read FRegnr write FRegnr;
    property BetriebsdatenId: Nullable<Integer> read FBetriebsdatenId write FBetriebsdatenId;
    property Name: Nullable<string> read FName write FName;
    property IdCckAuftrag: Nullable<Integer> read FIdCckAuftrag write FIdCckAuftrag;
    property Betriebstyp: Nullable<string> read FBetriebstyp write FBetriebstyp;
    property Betriebsart: Nullable<string> read FBetriebsart write FBetriebsart;
    property RegnrHauptbetrieb: Nullable<string> read FRegnrHauptbetrieb write FRegnrHauptbetrieb;
    property IdHauptbetrieb: Nullable<Integer> read FIdHauptbetrieb write FIdHauptbetrieb;
    property Ort: Nullable<string> read FOrt write FOrt;
    property Taetigkeiten: Nullable<string> read FTaetigkeiten write FTaetigkeiten;
    property Zulassungsnummern: Nullable<string> read FZulassungsnummern write FZulassungsnummern;
  end;
  
  [Entity]
  [Table('[vGroupUsers]', 'SYSTEMSTAMMDATEN')]
  [Id('FUser', TIdGenerator.None)]
  [Id('FGroup', TIdGenerator.None)]
  [Id('FGroupUser', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TVgroupuser = class(TObject)
  private
    [Column('MembershipLevel', [TColumnProp.Required])]
    FMembershiplevel: Integer;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('UserID', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GroupUserID', [TColumnProp.Required], 'ID')]
    FGroupUser: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GroupID', [TColumnProp.Required], 'ID')]
    FGroup: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_Person', [TColumnProp.Required], 'ID')]
    FGroupPerson: Proxy<TPerson>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('BLDCODE', [TColumnProp.Required], 'BLDCODE')]
    FBundesland: Proxy<TBundesland>;
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
    function GetGroupUser: TUser;
    procedure SetGroupUser(const Value: TUser);
    function GetGroup: TGruppe;
    procedure SetGroup(const Value: TGruppe);
    function GetGroupPerson: TPerson;
    procedure SetGroupPerson(const Value: TPerson);
    function GetBundesland: TBundesland;
    procedure SetBundesland(const Value: TBundesland);
  public
    property Membershiplevel: Integer read FMembershiplevel write FMembershiplevel;
    property User: TUser read GetUser write SetUser;
    property GroupUser: TUser read GetGroupUser write SetGroupUser;
    property Group: TGruppe read GetGroup write SetGroup;
    property GroupPerson: TPerson read GetGroupPerson write SetGroupPerson;
    property Bundesland: TBundesland read GetBundesland write SetBundesland;
  end;
  
  [Entity]
  [Table('[vNACHRICHTEN_USER]', 'BEWEGUNGSDATEN')]
  [Id('FNachricht', TIdGenerator.None)]
  [Id('FGruppe', TIdGenerator.None)]
  [Id('FUser', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TVNachrichtForUser = class(TObject)
  private
    [Column('GESEHEN_TSTAMP', [])]
    FGesehenTstamp: Nullable<TDateTime>;
    
    [Column('GELESEN_TSTAMP', [])]
    FGelesenTstamp: Nullable<TDateTime>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_NACHRICHT', [TColumnProp.Required], 'ID')]
    FNachricht: Proxy<TNachricht>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_GRUPPE', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_USER', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    
    [Association([TAssociationProp.Lazy], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID_NACHRICHTEN_ZUSTELLUNG', [], 'ID')]
    FNachrichtenZustellung: Proxy<TNachrichtenZustellung>;
    function GetNachricht: TNachricht;
    procedure SetNachricht(const Value: TNachricht);
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
    function GetNachrichtenZustellung: TNachrichtenZustellung;
    procedure SetNachrichtenZustellung(const Value: TNachrichtenZustellung);
  public
    property GesehenTstamp: Nullable<TDateTime> read FGesehenTstamp write FGesehenTstamp;
    property GelesenTstamp: Nullable<TDateTime> read FGelesenTstamp write FGelesenTstamp;
    property Nachricht: TNachricht read GetNachricht write SetNachricht;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
    property User: TUser read GetUser write SetUser;
    property NachrichtenZustellung: TNachrichtenZustellung read GetNachrichtenZustellung write SetNachrichtenZustellung;
  end;
  
  [Entity]
  [Table('[vUserGroupMembership]', 'SYSTEMSTAMMDATEN')]
  [Id('FUser', TIdGenerator.None)]
  [Id('FGruppe', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TVUserGroupMembership = class(TObject)
  private
    [Column('MembershipLevel', [TColumnProp.Required])]
    FMembershipLevel: Integer;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('GroupID', [TColumnProp.Required], 'ID')]
    FGruppe: Proxy<TGruppe>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('UserID', [TColumnProp.Required], 'ID')]
    FUser: Proxy<TUser>;
    function GetGruppe: TGruppe;
    procedure SetGruppe(const Value: TGruppe);
    function GetUser: TUser;
    procedure SetUser(const Value: TUser);
  public
    property MembershipLevel: Integer read FMembershipLevel write FMembershipLevel;
    property Gruppe: TGruppe read GetGruppe write SetGruppe;
    property User: TUser read GetUser write SetUser;
  end;
  
  [Entity]
  [Table('[vUserKontrollen]', 'BEWEGUNGSDATEN')]
  [Id('FIdUser', TIdGenerator.None)]
  [Id('FKontrolle', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TvUserKontrollen = class(TObject)
  private
    [Column('ID_USER', [TColumnProp.Required])]
    FIdUser: Integer;
    
    [Column('STATUS', [TColumnProp.Required], 1)]
    FStatus: string;
    
    [Column('DATUM', [])]
    FDatum: Nullable<TDateTime>;
    
    [Column('ENDEZEIT', [])]
    FEndezeit: Nullable<TDateTime>;
    
    [Column('FAELLIG', [])]
    FFaellig: Nullable<TDate>;
    
    [Column('ID_ERFASSER', [])]
    FIdErfasser: Nullable<Integer>;
    
    [Column('ID_KONTROLLORGAN', [])]
    FIdKontrollorgan: Nullable<Integer>;
    
    [Column('ID_GRUPPE_QUELLE', [])]
    FIdGruppeQuelle: Nullable<Integer>;
    
    [Column('EFFECTIVE_DATE', [])]
    FEffectiveDate: Nullable<TDate>;
    
    [Column('GruppeLevel', [TColumnProp.Required])]
    FGruppeLevel: Integer;
    
    [Column('STATUS_PRIO', [TColumnProp.Required])]
    FStatusPrio: Integer;
    
    [Column('SORT', [TColumnProp.Required])]
    FSort: Integer;
    
    [Association([TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('ID', [TColumnProp.Required], 'ID')]
    FKontrolle: TKontrollbericht;
  public
    property IdUser: Integer read FIdUser write FIdUser;
    property Status: string read FStatus write FStatus;
    property Datum: Nullable<TDateTime> read FDatum write FDatum;
    property Endezeit: Nullable<TDateTime> read FEndezeit write FEndezeit;
    property Faellig: Nullable<TDate> read FFaellig write FFaellig;
    property IdErfasser: Nullable<Integer> read FIdErfasser write FIdErfasser;
    property IdKontrollorgan: Nullable<Integer> read FIdKontrollorgan write FIdKontrollorgan;
    property IdGruppeQuelle: Nullable<Integer> read FIdGruppeQuelle write FIdGruppeQuelle;
    property EffectiveDate: Nullable<TDate> read FEffectiveDate write FEffectiveDate;
    property GruppeLevel: Integer read FGruppeLevel write FGruppeLevel;
    property StatusPrio: Integer read FStatusPrio write FStatusPrio;
    property Sort: Integer read FSort write FSort;
    property Kontrolle: TKontrollbericht read FKontrolle write FKontrolle;
  end;
  
  [Entity]
  [Table('[ZULASSUNGEN]', 'STAMMDATEN')]
  [Id('FId', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TZulassung = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: Integer;
    
    [Column('ZULNR', [TColumnProp.Required], 9)]
    FZulnr: string;
    
    [Column('BEGINNDATUM', [])]
    FBeginndatum: Nullable<TDateTime>;
    
    [Column('ENDDATUM', [])]
    FEnddatum: Nullable<TDateTime>;
    
    [Column('AKTIV', [])]
    FAktiv: Nullable<Integer>;
    
    [Column('SICHTBAR', [])]
    FSichtbar: Nullable<Integer>;
    
    [Association([TAssociationProp.Lazy, TAssociationProp.Required], CascadeTypeAll - [TCascadeType.Remove])]
    [JoinColumn('REGNR', [TColumnProp.Required], 'REGNR')]
    FRegistrierung: Proxy<TRegistrierung>;
    function GetRegistrierung: TRegistrierung;
    procedure SetRegistrierung(const Value: TRegistrierung);
  public
    property Id: Integer read FId write FId;
    property Zulnr: string read FZulnr write FZulnr;
    property Beginndatum: Nullable<TDateTime> read FBeginndatum write FBeginndatum;
    property Enddatum: Nullable<TDateTime> read FEnddatum write FEnddatum;
    property Aktiv: Nullable<Integer> read FAktiv write FAktiv;
    property Sichtbar: Nullable<Integer> read FSichtbar write FSichtbar;
    property Registrierung: TRegistrierung read GetRegistrierung write SetRegistrierung;
  end;
  
  [Entity]
  [Table('[ZUSATZTEXTE]', 'BEWEGUNGSDATEN')]
  [Id('FId', TIdGenerator.None)]
  [Model('ELKE')]
  [Model('AMA')]
  TZusatztext = class(TObject)
  private
    [Column('ID', [TColumnProp.Required])]
    FId: Integer;
    
    [Column('TABELLE', [TColumnProp.Required], 50)]
    FTabelle: string;
    
    [Column('BEZEICHNUNG', [], 150)]
    FBezeichnung: Nullable<string>;
    
    [Column('LANGTEXT', [], 800)]
    FLangtext: Nullable<string>;
  public
    property Id: Integer read FId write FId;
    property Tabelle: string read FTabelle write FTabelle;
    property Bezeichnung: Nullable<string> read FBezeichnung write FBezeichnung;
    property Langtext: Nullable<string> read FLangtext write FLangtext;
  end;
  
implementation

{ TAdresse }

function TAdresse.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TAdresse.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TAdresse.GetGemeinde: TGemeinde;
begin
  result := FGemeinde.Value;
end;

procedure TAdresse.SetGemeinde(const Value: TGemeinde);
begin
  FGemeinde.Value := Value;
end;

{ TAnsprechpartner }

function TAnsprechpartner.GetAptyp: TAPTyp;
begin
  result := FAptyp.Value;
end;

procedure TAnsprechpartner.SetAptyp(const Value: TAPTyp);
begin
  FAptyp.Value := Value;
end;

function TAnsprechpartner.GetPerson: TPerson;
begin
  result := FPerson.Value;
end;

procedure TAnsprechpartner.SetPerson(const Value: TPerson);
begin
  FPerson.Value := Value;
end;

{ TAnwesender }

function TAnwesender.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TAnwesender.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

function TAnwesender.GetAPTyp: TAPTyp;
begin
  result := FAPTyp.Value;
end;

procedure TAnwesender.SetAPTyp(const Value: TAPTyp);
begin
  FAPTyp.Value := Value;
end;

function TAnwesender.GetPerson: TPerson;
begin
  result := FPerson.Value;
end;

procedure TAnwesender.SetPerson(const Value: TPerson);
begin
  FPerson.Value := Value;
end;

{ TBetrieb }

function TBetrieb.GetAdresse: TAdresse;
begin
  result := FAdresse.Value;
end;

procedure TBetrieb.SetAdresse(const Value: TAdresse);
begin
  FAdresse.Value := Value;
end;

function TBetrieb.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TBetrieb.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TBetrieb.GetRegistrierung: TRegistrierung;
begin
  result := FRegistrierung.Value;
end;

procedure TBetrieb.SetRegistrierung(const Value: TRegistrierung);
begin
  FRegistrierung.Value := Value;
end;

function TBetrieb.GetRevisionsSchema: TRevisionsSchema;
begin
  result := FRevisionsSchema.Value;
end;

procedure TBetrieb.SetRevisionsSchema(const Value: TRevisionsSchema);
begin
  FRevisionsSchema.Value := Value;
end;

function TBetrieb.GetRevisionsGruppe: TGruppe;
begin
  result := FRevisionsGruppe.Value;
end;

procedure TBetrieb.SetRevisionsGruppe(const Value: TGruppe);
begin
  FRevisionsGruppe.Value := Value;
end;

constructor TBetrieb.Create;
begin
  inherited;
  FKontrollberichte.SetInitialValue(TList<TKontrollbericht>.Create);
  FKommunikationswege.SetInitialValue(TList<TBetriebeKommunikationswege>.Create);
  FRevStaemme.SetInitialValue(TList<TBetriebRevstamm>.Create);
  FBezirksgruppen.SetInitialValue(TList<TVbetriebbezirksgruppe>.Create);
end;

destructor TBetrieb.Destroy;
begin
  FBezirksgruppen.DestroyValue;
  FRevStaemme.DestroyValue;
  FKommunikationswege.DestroyValue;
  FKontrollberichte.DestroyValue;
  inherited;
end;

function TBetrieb.GetKontrollberichte: TList<TKontrollbericht>;
begin
  result := FKontrollberichte.Value;
end;

function TBetrieb.GetKommunikationswege: TList<TBetriebeKommunikationswege>;
begin
  result := FKommunikationswege.Value;
end;

function TBetrieb.GetRevStaemme: TList<TBetriebRevstamm>;
begin
  result := FRevStaemme.Value;
end;

function TBetrieb.GetBezirksgruppen: TList<TVbetriebbezirksgruppe>;
begin
  result := FBezirksgruppen.Value;
end;

{ TBetriebeKommunikationswege }

function TBetriebeKommunikationswege.GetBetrieb: TBetrieb;
begin
  result := FBetrieb.Value;
end;

procedure TBetriebeKommunikationswege.SetBetrieb(const Value: TBetrieb);
begin
  FBetrieb.Value := Value;
end;

function TBetriebeKommunikationswege.GetKommunikationsweg: TKommunikationsweg;
begin
  result := FKommunikationsweg.Value;
end;

procedure TBetriebeKommunikationswege.SetKommunikationsweg(const Value: TKommunikationsweg);
begin
  FKommunikationsweg.Value := Value;
end;

{ TBetriebRevstamm }

function TBetriebRevstamm.GetBetrieb: TBetrieb;
begin
  result := FBetrieb.Value;
end;

procedure TBetriebRevstamm.SetBetrieb(const Value: TBetrieb);
begin
  FBetrieb.Value := Value;
end;

function TBetriebRevstamm.GetRevisionsstamm: TRevisionsstamm;
begin
  result := FRevisionsstamm.Value;
end;

procedure TBetriebRevstamm.SetRevisionsstamm(const Value: TRevisionsstamm);
begin
  FRevisionsstamm.Value := Value;
end;

function TBetriebRevstamm.GetRevisionsSchema: TRevisionsSchema;
begin
  result := FRevisionsSchema.Value;
end;

procedure TBetriebRevstamm.SetRevisionsSchema(const Value: TRevisionsSchema);
begin
  FRevisionsSchema.Value := Value;
end;

{ TBewerteteFrage }

function TBewerteteFrage.GetBewertung: TBewertung;
begin
  result := FBewertung.Value;
end;

procedure TBewerteteFrage.SetBewertung(const Value: TBewertung);
begin
  FBewertung.Value := Value;
end;

function TBewerteteFrage.GetFrage: TFrage;
begin
  result := FFrage.Value;
end;

procedure TBewerteteFrage.SetFrage(const Value: TFrage);
begin
  FFrage.Value := Value;
end;

function TBewerteteFrage.GetBericht: TKontrollbericht;
begin
  result := FBericht.Value;
end;

procedure TBewerteteFrage.SetBericht(const Value: TKontrollbericht);
begin
  FBericht.Value := Value;
end;

function TBewerteteFrage.GetMangel: TMangel;
begin
  result := FMangel.Value;
end;

procedure TBewerteteFrage.SetMangel(const Value: TMangel);
begin
  FMangel.Value := Value;
end;

constructor TBewerteteFrage.Create;
begin
  inherited;
  FBilder.SetInitialValue(TList<TKontrollberichtBild>.Create);
end;

destructor TBewerteteFrage.Destroy;
begin
  FBilder.DestroyValue;
  inherited;
end;

function TBewerteteFrage.GetBilder: TList<TKontrollberichtBild>;
begin
  result := FBilder.Value;
end;

{ TBewertung }

function TBewertung.GetIcon: TBewertungsIcon;
begin
  result := FIcon.Value;
end;

procedure TBewertung.SetIcon(const Value: TBewertungsIcon);
begin
  FIcon.Value := Value;
end;

function TBewertung.GetTyp: TBewertungstyp;
begin
  result := FTyp.Value;
end;

procedure TBewertung.SetTyp(const Value: TBewertungstyp);
begin
  FTyp.Value := Value;
end;

constructor TBewertung.Create;
begin
  inherited;
  FFragenBewertungen.SetInitialValue(TList<TFrageBewertung>.Create);
  FBewerteteFragen.SetInitialValue(TList<TBewerteteFrage>.Create);
end;

destructor TBewertung.Destroy;
begin
  FBewerteteFragen.DestroyValue;
  FFragenBewertungen.DestroyValue;
  inherited;
end;

function TBewertung.GetFragenBewertungen: TList<TFrageBewertung>;
begin
  result := FFragenBewertungen.Value;
end;

function TBewertung.GetBewerteteFragen: TList<TBewerteteFrage>;
begin
  result := FBewerteteFragen.Value;
end;

{ TBewertungsIcon }

constructor TBewertungsIcon.Create;
begin
  inherited;
  FBewertungen.SetInitialValue(TList<TBewertung>.Create);
end;

destructor TBewertungsIcon.Destroy;
begin
  FBewertungen.DestroyValue;
  inherited;
end;

function TBewertungsIcon.GetBewertungen: TList<TBewertung>;
begin
  result := FBewertungen.Value;
end;

{ TBewertungstyp }

constructor TBewertungstyp.Create;
begin
  inherited;
  FBewertungen.SetInitialValue(TList<TBewertung>.Create);
end;

destructor TBewertungstyp.Destroy;
begin
  FBewertungen.DestroyValue;
  inherited;
end;

function TBewertungstyp.GetBewertungen: TList<TBewertung>;
begin
  result := FBewertungen.Value;
end;

{ TBkbnummer }

function TBkbnummer.GetSystemkz: TSys;
begin
  result := FSystemkz.Value;
end;

procedure TBkbnummer.SetSystemkz(const Value: TSys);
begin
  FSystemkz.Value := Value;
end;

function TBkbnummer.GetBkbtyp: TBkbTyp;
begin
  result := FBkbtyp.Value;
end;

procedure TBkbnummer.SetBkbtyp(const Value: TBkbTyp);
begin
  FBkbtyp.Value := Value;
end;

function TBkbnummer.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TBkbnummer.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

{ TBkbTyp }

function TBkbTyp.GetModul: TModul;
begin
  result := FModul.Value;
end;

procedure TBkbTyp.SetModul(const Value: TModul);
begin
  FModul.Value := Value;
end;

constructor TBkbTyp.Create;
begin
  inherited;
  FKontrolltypen.SetInitialValue(TList<TKontrolltyp>.Create);
  FBkbtypenRechtsgrundlagen.SetInitialValue(TList<TBkbtypenRechtsgrundlage>.Create);
  FRollenBkbtypen.SetInitialValue(TList<TRollenBkbtypen>.Create);
  FBkbnummern.SetInitialValue(TList<TBkbnummer>.Create);
  FMangeltypen.SetInitialValue(TList<TMangeltyp>.Create);
end;

destructor TBkbTyp.Destroy;
begin
  FMangeltypen.DestroyValue;
  FBkbnummern.DestroyValue;
  FRollenBkbtypen.DestroyValue;
  FBkbtypenRechtsgrundlagen.DestroyValue;
  FKontrolltypen.DestroyValue;
  inherited;
end;

function TBkbTyp.GetKontrolltypen: TList<TKontrolltyp>;
begin
  result := FKontrolltypen.Value;
end;

function TBkbTyp.GetBkbtypenRechtsgrundlagen: TList<TBkbtypenRechtsgrundlage>;
begin
  result := FBkbtypenRechtsgrundlagen.Value;
end;

function TBkbTyp.GetRollenBkbtypen: TList<TRollenBkbtypen>;
begin
  result := FRollenBkbtypen.Value;
end;

function TBkbTyp.GetBkbnummern: TList<TBkbnummer>;
begin
  result := FBkbnummern.Value;
end;

function TBkbTyp.GetMangeltypen: TList<TMangeltyp>;
begin
  result := FMangeltypen.Value;
end;

{ TBkbtypenRechtsgrundlage }

function TBkbtypenRechtsgrundlage.GetBkbtyp: TBkbTyp;
begin
  result := FBkbtyp.Value;
end;

procedure TBkbtypenRechtsgrundlage.SetBkbtyp(const Value: TBkbTyp);
begin
  FBkbtyp.Value := Value;
end;

function TBkbtypenRechtsgrundlage.GetRechtsgrundlage: TRechtsgrundlage;
begin
  result := FRechtsgrundlage.Value;
end;

procedure TBkbtypenRechtsgrundlage.SetRechtsgrundlage(const Value: TRechtsgrundlage);
begin
  FRechtsgrundlage.Value := Value;
end;

{ TBundesland }

function TBundesland.GetLand: TLand;
begin
  result := FLand.Value;
end;

procedure TBundesland.SetLand(const Value: TLand);
begin
  FLand.Value := Value;
end;

constructor TBundesland.Create;
begin
  inherited;
  FBetriebe.SetInitialValue(TList<TBetrieb>.Create);
  FBundeslaenderModule.SetInitialValue(TList<TBundeslandModul>.Create);
  FGruppen.SetInitialValue(TList<TGruppe>.Create);
  FUsers.SetInitialValue(TList<TUser>.Create);
  FAdressen.SetInitialValue(TList<TAdresse>.Create);
  FGemeinden.SetInitialValue(TList<TGemeinde>.Create);
  FBkbNummern.SetInitialValue(TList<TBkbnummer>.Create);
  FEmailTexte.SetInitialValue(TList<TEmailTexte>.Create);
  FBesitztChecklisten.SetInitialValue(TList<TCheckliste>.Create);
  FSichtbareChecklisten.SetInitialValue(TList<TBundeslandChecklistenKontrolltyp>.Create);
end;

destructor TBundesland.Destroy;
begin
  FSichtbareChecklisten.DestroyValue;
  FBesitztChecklisten.DestroyValue;
  FEmailTexte.DestroyValue;
  FBkbNummern.DestroyValue;
  FGemeinden.DestroyValue;
  FAdressen.DestroyValue;
  FUsers.DestroyValue;
  FGruppen.DestroyValue;
  FBundeslaenderModule.DestroyValue;
  FBetriebe.DestroyValue;
  inherited;
end;

function TBundesland.GetBetriebe: TList<TBetrieb>;
begin
  result := FBetriebe.Value;
end;

function TBundesland.GetBundeslaenderModule: TList<TBundeslandModul>;
begin
  result := FBundeslaenderModule.Value;
end;

function TBundesland.GetGruppen: TList<TGruppe>;
begin
  result := FGruppen.Value;
end;

function TBundesland.GetUsers: TList<TUser>;
begin
  result := FUsers.Value;
end;

function TBundesland.GetAdressen: TList<TAdresse>;
begin
  result := FAdressen.Value;
end;

function TBundesland.GetGemeinden: TList<TGemeinde>;
begin
  result := FGemeinden.Value;
end;

function TBundesland.GetBkbNummern: TList<TBkbnummer>;
begin
  result := FBkbNummern.Value;
end;

function TBundesland.GetEmailTexte: TList<TEmailTexte>;
begin
  result := FEmailTexte.Value;
end;

function TBundesland.GetBesitztChecklisten: TList<TCheckliste>;
begin
  result := FBesitztChecklisten.Value;
end;

function TBundesland.GetSichtbareChecklisten: TList<TBundeslandChecklistenKontrolltyp>;
begin
  result := FSichtbareChecklisten.Value;
end;

{ TBundeslandChecklistenKontrolltyp }

function TBundeslandChecklistenKontrolltyp.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TBundeslandChecklistenKontrolltyp.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TBundeslandChecklistenKontrolltyp.GetChecklisteKontrolltyp: TChecklistenKontrolltyp;
begin
  result := FChecklisteKontrolltyp.Value;
end;

procedure TBundeslandChecklistenKontrolltyp.SetChecklisteKontrolltyp(const Value: TChecklistenKontrolltyp);
begin
  FChecklisteKontrolltyp.Value := Value;
end;

{ TBundeslandModul }

function TBundeslandModul.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TBundeslandModul.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TBundeslandModul.GetModul: TModul;
begin
  result := FModul.Value;
end;

procedure TBundeslandModul.SetModul(const Value: TModul);
begin
  FModul.Value := Value;
end;

{ TCckAuftrag }

function TCckAuftrag.GetAuftragsart: TCckAuftragsart;
begin
  result := FAuftragsart.Value;
end;

procedure TCckAuftrag.SetAuftragsart(const Value: TCckAuftragsart);
begin
  FAuftragsart.Value := Value;
end;

function TCckAuftrag.GetDokument: TDokument;
begin
  result := FDokument.Value;
end;

procedure TCckAuftrag.SetDokument(const Value: TDokument);
begin
  FDokument.Value := Value;
end;

constructor TCckAuftrag.Create;
begin
  inherited;
  FBetriebsdaten.SetInitialValue(TList<TCckBetrieb>.Create);
end;

destructor TCckAuftrag.Destroy;
begin
  FBetriebsdaten.DestroyValue;
  inherited;
end;

function TCckAuftrag.GetBetriebsdaten: TList<TCckBetrieb>;
begin
  result := FBetriebsdaten.Value;
end;

{ TCckAuftragsart }

constructor TCckAuftragsart.Create;
begin
  inherited;
  FCckAuftraege.SetInitialValue(TList<TCckAuftrag>.Create);
end;

destructor TCckAuftragsart.Destroy;
begin
  FCckAuftraege.DestroyValue;
  inherited;
end;

function TCckAuftragsart.GetCckAuftraege: TList<TCckAuftrag>;
begin
  result := FCckAuftraege.Value;
end;

{ TCckAuftragsbewertung }

function TCckAuftragsbewertung.GetAuswahldaten: TCckAuswahl;
begin
  result := FAuswahldaten.Value;
end;

procedure TCckAuftragsbewertung.SetAuswahldaten(const Value: TCckAuswahl);
begin
  FAuswahldaten.Value := Value;
end;

function TCckAuftragsbewertung.GetAnforderung: TCckModulAnforderung;
begin
  result := FAnforderung.Value;
end;

procedure TCckAuftragsbewertung.SetAnforderung(const Value: TCckModulAnforderung);
begin
  FAnforderung.Value := Value;
end;

{ TCckAuswahl }

function TCckAuswahl.GetCckBetriebsdaten: TCckBetrieb;
begin
  result := FCckBetriebsdaten.Value;
end;

procedure TCckAuswahl.SetCckBetriebsdaten(const Value: TCckBetrieb);
begin
  FCckBetriebsdaten.Value := Value;
end;

function TCckAuswahl.GetModul: TCckModul;
begin
  result := FModul.Value;
end;

procedure TCckAuswahl.SetModul(const Value: TCckModul);
begin
  FModul.Value := Value;
end;

function TCckAuswahl.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TCckAuswahl.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

constructor TCckAuswahl.Create;
begin
  inherited;
  FCckAuftragsbewertungen.SetInitialValue(TList<TCckAuftragsbewertung>.Create);
end;

destructor TCckAuswahl.Destroy;
begin
  FCckAuftragsbewertungen.DestroyValue;
  inherited;
end;

function TCckAuswahl.GetCckAuftragsbewertungen: TList<TCckAuftragsbewertung>;
begin
  result := FCckAuftragsbewertungen.Value;
end;

{ TCckBetrieb }

function TCckBetrieb.GetCckAuftrag: TCckAuftrag;
begin
  result := FCckAuftrag.Value;
end;

procedure TCckBetrieb.SetCckAuftrag(const Value: TCckAuftrag);
begin
  FCckAuftrag.Value := Value;
end;

function TCckBetrieb.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TCckBetrieb.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

constructor TCckBetrieb.Create;
begin
  inherited;
  FCckAuswahldaten.SetInitialValue(TList<TCckAuswahl>.Create);
end;

destructor TCckBetrieb.Destroy;
begin
  FCckAuswahldaten.DestroyValue;
  inherited;
end;

function TCckBetrieb.GetCckAuswahldaten: TList<TCckAuswahl>;
begin
  result := FCckAuswahldaten.Value;
end;

{ TCckModul }

constructor TCckModul.Create;
begin
  inherited;
  FCckAuswahldaten.SetInitialValue(TList<TCckAuswahl>.Create);
  FCckModulAnforderungen.SetInitialValue(TList<TCckModulAnforderung>.Create);
  FCckModulKontrolltypen.SetInitialValue(TList<TCckModulKontrolltyp>.Create);
end;

destructor TCckModul.Destroy;
begin
  FCckModulKontrolltypen.DestroyValue;
  FCckModulAnforderungen.DestroyValue;
  FCckAuswahldaten.DestroyValue;
  inherited;
end;

function TCckModul.GetCckAuswahldaten: TList<TCckAuswahl>;
begin
  result := FCckAuswahldaten.Value;
end;

function TCckModul.GetCckModulAnforderungen: TList<TCckModulAnforderung>;
begin
  result := FCckModulAnforderungen.Value;
end;

function TCckModul.GetCckModulKontrolltypen: TList<TCckModulKontrolltyp>;
begin
  result := FCckModulKontrolltypen.Value;
end;

{ TCckModulAnforderung }

function TCckModulAnforderung.GetModul: TCckModul;
begin
  result := FModul.Value;
end;

procedure TCckModulAnforderung.SetModul(const Value: TCckModul);
begin
  FModul.Value := Value;
end;

constructor TCckModulAnforderung.Create;
begin
  inherited;
  FCckAuftragsbewertungen.SetInitialValue(TList<TCckAuftragsbewertung>.Create);
end;

destructor TCckModulAnforderung.Destroy;
begin
  FCckAuftragsbewertungen.DestroyValue;
  inherited;
end;

function TCckModulAnforderung.GetCckAuftragsbewertungen: TList<TCckAuftragsbewertung>;
begin
  result := FCckAuftragsbewertungen.Value;
end;

{ TCckModulKontrolltyp }

function TCckModulKontrolltyp.GetCckModul: TCckModul;
begin
  result := FCckModul.Value;
end;

procedure TCckModulKontrolltyp.SetCckModul(const Value: TCckModul);
begin
  FCckModul.Value := Value;
end;

function TCckModulKontrolltyp.GetBkbtyp: TKontrolltyp;
begin
  result := FBkbtyp.Value;
end;

procedure TCckModulKontrolltyp.SetBkbtyp(const Value: TKontrolltyp);
begin
  FBkbtyp.Value := Value;
end;

function TCckModulKontrolltyp.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TCckModulKontrolltyp.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

{ TCckStatusMeldung }

function TCckStatusMeldung.GetBkb: TBkbnummer;
begin
  result := FBkb.Value;
end;

procedure TCckStatusMeldung.SetBkb(const Value: TBkbnummer);
begin
  FBkb.Value := Value;
end;

function TCckStatusMeldung.GetStatus: TCckStatus;
begin
  result := FStatus.Value;
end;

procedure TCckStatusMeldung.SetStatus(const Value: TCckStatus);
begin
  FStatus.Value := Value;
end;

function TCckStatusMeldung.GetBkbAuftrag: TBkbnummer;
begin
  result := FBkbAuftrag.Value;
end;

procedure TCckStatusMeldung.SetBkbAuftrag(const Value: TBkbnummer);
begin
  FBkbAuftrag.Value := Value;
end;

{ TCheckliste }

function TCheckliste.GetBesitzerBundesland: TBundesland;
begin
  result := FBesitzerBundesland.Value;
end;

procedure TCheckliste.SetBesitzerBundesland(const Value: TBundesland);
begin
  FBesitzerBundesland.Value := Value;
end;

function TCheckliste.GetLastChangeUser: TUser;
begin
  result := FLastChangeUser.Value;
end;

procedure TCheckliste.SetLastChangeUser(const Value: TUser);
begin
  FLastChangeUser.Value := Value;
end;

constructor TCheckliste.Create;
begin
  inherited;
  FFragen.SetInitialValue(TList<TFrage>.Create);
  FChecklistenKontrolltypen.SetInitialValue(TList<TChecklistenKontrolltyp>.Create);
end;

destructor TCheckliste.Destroy;
begin
  FChecklistenKontrolltypen.DestroyValue;
  FFragen.DestroyValue;
  inherited;
end;

function TCheckliste.GetFragen: TList<TFrage>;
begin
  result := FFragen.Value;
end;

function TCheckliste.GetChecklistenKontrolltypen: TList<TChecklistenKontrolltyp>;
begin
  result := FChecklistenKontrolltypen.Value;
end;

{ TChecklistenKontrolltyp }

function TChecklistenKontrolltyp.GetCheckliste: TCheckliste;
begin
  result := FCheckliste.Value;
end;

procedure TChecklistenKontrolltyp.SetCheckliste(const Value: TCheckliste);
begin
  FCheckliste.Value := Value;
end;

function TChecklistenKontrolltyp.GetKontrolltyp: TKontrolltyp;
begin
  result := FKontrolltyp.Value;
end;

procedure TChecklistenKontrolltyp.SetKontrolltyp(const Value: TKontrolltyp);
begin
  FKontrolltyp.Value := Value;
end;

constructor TChecklistenKontrolltyp.Create;
begin
  inherited;
  FBundeslaenderChecklistenKontrolltypen.SetInitialValue(TList<TBundeslandChecklistenKontrolltyp>.Create);
end;

destructor TChecklistenKontrolltyp.Destroy;
begin
  FBundeslaenderChecklistenKontrolltypen.DestroyValue;
  inherited;
end;

function TChecklistenKontrolltyp.GetBundeslaenderChecklistenKontrolltypen: TList<TBundeslandChecklistenKontrolltyp>;
begin
  result := FBundeslaenderChecklistenKontrolltypen.Value;
end;

{ TDokument }

constructor TDokument.Create;
begin
  inherited;
  FCckAuftraege.SetInitialValue(TList<TCckAuftrag>.Create);
  FKontrollberichte.SetInitialValue(TList<TKontrollbericht>.Create);
end;

destructor TDokument.Destroy;
begin
  FKontrollberichte.DestroyValue;
  FCckAuftraege.DestroyValue;
  inherited;
end;

function TDokument.GetCckAuftraege: TList<TCckAuftrag>;
begin
  result := FCckAuftraege.Value;
end;

function TDokument.GetKontrollberichte: TList<TKontrollbericht>;
begin
  result := FKontrollberichte.Value;
end;

{ TEmailHistory }

function TEmailHistory.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TEmailHistory.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

constructor TEmailHistory.Create;
begin
  inherited;
  FAttachments.SetInitialValue(TList<TEmailHistoryAttachment>.Create);
end;

destructor TEmailHistory.Destroy;
begin
  FAttachments.DestroyValue;
  inherited;
end;

function TEmailHistory.GetAttachments: TList<TEmailHistoryAttachment>;
begin
  result := FAttachments.Value;
end;

{ TEmailHistoryAttachment }

function TEmailHistoryAttachment.GetEmail: TEmailHistory;
begin
  result := FEmail.Value;
end;

procedure TEmailHistoryAttachment.SetEmail(const Value: TEmailHistory);
begin
  FEmail.Value := Value;
end;

{ TEmailTexte }

function TEmailTexte.GetArt: TEmailArten;
begin
  result := FArt.Value;
end;

procedure TEmailTexte.SetArt(const Value: TEmailArten);
begin
  FArt.Value := Value;
end;

function TEmailTexte.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TEmailTexte.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

{ TFrage }

function TFrage.GetCheckliste: TCheckliste;
begin
  result := FCheckliste.Value;
end;

procedure TFrage.SetCheckliste(const Value: TCheckliste);
begin
  FCheckliste.Value := Value;
end;

function TFrage.GetFormatierung: TFormatierung;
begin
  result := FFormatierung.Value;
end;

procedure TFrage.SetFormatierung(const Value: TFormatierung);
begin
  FFormatierung.Value := Value;
end;

function TFrage.GetUebergeordneteFrage: TFrage;
begin
  result := FUebergeordneteFrage.Value;
end;

procedure TFrage.SetUebergeordneteFrage(const Value: TFrage);
begin
  FUebergeordneteFrage.Value := Value;
end;

function TFrage.GetGruppe: TFragengruppe;
begin
  result := FGruppe.Value;
end;

procedure TFrage.SetGruppe(const Value: TFragengruppe);
begin
  FGruppe.Value := Value;
end;

constructor TFrage.Create;
begin
  inherited;
  FFragenBewertungen.SetInitialValue(TList<TFrageBewertung>.Create);
  FBewerteteFragen.SetInitialValue(TList<TBewerteteFrage>.Create);
  FUntergeordneteFragen.SetInitialValue(TList<TFrage>.Create);
  FFragenKontrollbereiche.SetInitialValue(TList<TFrageKontrollbereich>.Create);
end;

destructor TFrage.Destroy;
begin
  FFragenKontrollbereiche.DestroyValue;
  FUntergeordneteFragen.DestroyValue;
  FBewerteteFragen.DestroyValue;
  FFragenBewertungen.DestroyValue;
  inherited;
end;

function TFrage.GetFragenBewertungen: TList<TFrageBewertung>;
begin
  result := FFragenBewertungen.Value;
end;

function TFrage.GetBewerteteFragen: TList<TBewerteteFrage>;
begin
  result := FBewerteteFragen.Value;
end;

function TFrage.GetUntergeordneteFragen: TList<TFrage>;
begin
  result := FUntergeordneteFragen.Value;
end;

function TFrage.GetFragenKontrollbereiche: TList<TFrageKontrollbereich>;
begin
  result := FFragenKontrollbereiche.Value;
end;

{ TFrageBewertung }

function TFrageBewertung.GetBewertung: TBewertung;
begin
  result := FBewertung.Value;
end;

procedure TFrageBewertung.SetBewertung(const Value: TBewertung);
begin
  FBewertung.Value := Value;
end;

function TFrageBewertung.GetFrage: TFrage;
begin
  result := FFrage.Value;
end;

procedure TFrageBewertung.SetFrage(const Value: TFrage);
begin
  FFrage.Value := Value;
end;

function TFrageBewertung.GetStandardMassnahme: TMassnahme;
begin
  result := FStandardMassnahme.Value;
end;

procedure TFrageBewertung.SetStandardMassnahme(const Value: TMassnahme);
begin
  FStandardMassnahme.Value := Value;
end;

function TFrageBewertung.GetStandardMangeltyp: TMangeltyp;
begin
  result := FStandardMangeltyp.Value;
end;

procedure TFrageBewertung.SetStandardMangeltyp(const Value: TMangeltyp);
begin
  FStandardMangeltyp.Value := Value;
end;

{ TFrageKontrollbereich }

function TFrageKontrollbereich.GetFrage: TFrage;
begin
  result := FFrage.Value;
end;

procedure TFrageKontrollbereich.SetFrage(const Value: TFrage);
begin
  FFrage.Value := Value;
end;

function TFrageKontrollbereich.GetKontrollbereich: TKontrollbereich;
begin
  result := FKontrollbereich.Value;
end;

procedure TFrageKontrollbereich.SetKontrollbereich(const Value: TKontrollbereich);
begin
  FKontrollbereich.Value := Value;
end;

{ TFragengruppe }

function TFragengruppe.GetUebergeordneteGruppe: TFragengruppe;
begin
  result := FUebergeordneteGruppe.Value;
end;

procedure TFragengruppe.SetUebergeordneteGruppe(const Value: TFragengruppe);
begin
  FUebergeordneteGruppe.Value := Value;
end;

{ TFunktion }

function TFunktion.GetMutter: TFunktion;
begin
  result := FMutter.Value;
end;

procedure TFunktion.SetMutter(const Value: TFunktion);
begin
  FMutter.Value := Value;
end;

function TFunktion.GetProgrammmodul: TProgrammModul;
begin
  result := FProgrammmodul.Value;
end;

procedure TFunktion.SetProgrammmodul(const Value: TProgrammModul);
begin
  FProgrammmodul.Value := Value;
end;

constructor TFunktion.Create;
begin
  inherited;
  FUnterFunktionen.SetInitialValue(TList<TFunktion>.Create);
  FFunktionsrollen.SetInitialValue(TList<TFunktionRolle>.Create);
end;

destructor TFunktion.Destroy;
begin
  FFunktionsrollen.DestroyValue;
  FUnterFunktionen.DestroyValue;
  inherited;
end;

function TFunktion.GetUnterFunktionen: TList<TFunktion>;
begin
  result := FUnterFunktionen.Value;
end;

function TFunktion.GetFunktionsrollen: TList<TFunktionRolle>;
begin
  result := FFunktionsrollen.Value;
end;

{ TFunktionRolle }

function TFunktionRolle.GetFunktion: TFunktion;
begin
  result := FFunktion.Value;
end;

procedure TFunktionRolle.SetFunktion(const Value: TFunktion);
begin
  FFunktion.Value := Value;
end;

function TFunktionRolle.GetRolle: TRolle;
begin
  result := FRolle.Value;
end;

procedure TFunktionRolle.SetRolle(const Value: TRolle);
begin
  FRolle.Value := Value;
end;

{ TGemeinde }

function TGemeinde.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TGemeinde.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

{ TGruppe }

function TGruppe.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TGruppe.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TGruppe.GetMuttergruppe: TGruppe;
begin
  result := FMuttergruppe.Value;
end;

procedure TGruppe.SetMuttergruppe(const Value: TGruppe);
begin
  FMuttergruppe.Value := Value;
end;

function TGruppe.GetHauptverantwortlicher: TUser;
begin
  result := FHauptverantwortlicher.Value;
end;

procedure TGruppe.SetHauptverantwortlicher(const Value: TUser);
begin
  FHauptverantwortlicher.Value := Value;
end;

function TGruppe.GetStellvertreter: TUser;
begin
  result := FStellvertreter.Value;
end;

procedure TGruppe.SetStellvertreter(const Value: TUser);
begin
  FStellvertreter.Value := Value;
end;

constructor TGruppe.Create;
begin
  inherited;
  FUntergruppen.SetInitialValue(TList<TGruppe>.Create);
  FNachrichten.SetInitialValue(TList<TNachricht>.Create);
  FUsergruppen.SetInitialValue(TList<TUsergruppe>.Create);
  FTodoList.SetInitialValue(TList<TTodo>.Create);
end;

destructor TGruppe.Destroy;
begin
  FTodoList.DestroyValue;
  FUsergruppen.DestroyValue;
  FNachrichten.DestroyValue;
  FUntergruppen.DestroyValue;
  inherited;
end;

function TGruppe.GetUntergruppen: TList<TGruppe>;
begin
  result := FUntergruppen.Value;
end;

function TGruppe.GetNachrichten: TList<TNachricht>;
begin
  result := FNachrichten.Value;
end;

function TGruppe.GetUsergruppen: TList<TUsergruppe>;
begin
  result := FUsergruppen.Value;
end;

function TGruppe.GetTodoList: TList<TTodo>;
begin
  result := FTodoList.Value;
end;

{ TKbProbe }

function TKbProbe.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TKbProbe.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

function TKbProbe.GetEinsender: TPerson;
begin
  result := FEinsender.Value;
end;

procedure TKbProbe.SetEinsender(const Value: TPerson);
begin
  FEinsender.Value := Value;
end;

function TKbProbe.GetBkbtyp: TBkbTyp;
begin
  result := FBkbtyp.Value;
end;

procedure TKbProbe.SetBkbtyp(const Value: TBkbTyp);
begin
  FBkbtyp.Value := Value;
end;

function TKbProbe.GetDokument: TDokument;
begin
  result := FDokument.Value;
end;

procedure TKbProbe.SetDokument(const Value: TDokument);
begin
  FDokument.Value := Value;
end;

constructor TKbProbe.Create;
begin
  inherited;
  FBilder.SetInitialValue(TList<TKontrollberichtBild>.Create);
end;

destructor TKbProbe.Destroy;
begin
  FBilder.DestroyValue;
  inherited;
end;

function TKbProbe.GetBilder: TList<TKontrollberichtBild>;
begin
  result := FBilder.Value;
end;

{ TKommunikationsart }

constructor TKommunikationsart.Create;
begin
  inherited;
  FKommunikationswege.SetInitialValue(TList<TKommunikationsweg>.Create);
end;

destructor TKommunikationsart.Destroy;
begin
  FKommunikationswege.DestroyValue;
  inherited;
end;

function TKommunikationsart.GetKommunikationswege: TList<TKommunikationsweg>;
begin
  result := FKommunikationswege.Value;
end;

{ TKommunikationsweg }

function TKommunikationsweg.GetArt: TKommunikationsart;
begin
  result := FArt.Value;
end;

procedure TKommunikationsweg.SetArt(const Value: TKommunikationsart);
begin
  FArt.Value := Value;
end;

constructor TKommunikationsweg.Create;
begin
  inherited;
  FBetriebeKommunikationswege.SetInitialValue(TList<TBetriebeKommunikationswege>.Create);
  FPersonenKommunikationswege.SetInitialValue(TList<TPersonenKommunikationswege>.Create);
end;

destructor TKommunikationsweg.Destroy;
begin
  FPersonenKommunikationswege.DestroyValue;
  FBetriebeKommunikationswege.DestroyValue;
  inherited;
end;

function TKommunikationsweg.GetBetriebeKommunikationswege: TList<TBetriebeKommunikationswege>;
begin
  result := FBetriebeKommunikationswege.Value;
end;

function TKommunikationsweg.GetPersonenKommunikationswege: TList<TPersonenKommunikationswege>;
begin
  result := FPersonenKommunikationswege.Value;
end;

{ TKontrollbereich }

constructor TKontrollbereich.Create;
begin
  inherited;
  FFragenKontrollbereiche.SetInitialValue(TList<TFrageKontrollbereich>.Create);
  FMaengelKontrollbereiche.SetInitialValue(TList<TMangelKontrollbereich>.Create);
end;

destructor TKontrollbereich.Destroy;
begin
  FMaengelKontrollbereiche.DestroyValue;
  FFragenKontrollbereiche.DestroyValue;
  inherited;
end;

function TKontrollbereich.GetFragenKontrollbereiche: TList<TFrageKontrollbereich>;
begin
  result := FFragenKontrollbereiche.Value;
end;

function TKontrollbereich.GetMaengelKontrollbereiche: TList<TMangelKontrollbereich>;
begin
  result := FMaengelKontrollbereiche.Value;
end;

{ TKontrollbericht }

function TKontrollbericht.GetBetrieb: TBetrieb;
begin
  result := FBetrieb.Value;
end;

procedure TKontrollbericht.SetBetrieb(const Value: TBetrieb);
begin
  FBetrieb.Value := Value;
end;

function TKontrollbericht.GetErfasser: TPerson;
begin
  result := FErfasser.Value;
end;

procedure TKontrollbericht.SetErfasser(const Value: TPerson);
begin
  FErfasser.Value := Value;
end;

function TKontrollbericht.GetKontrollorgan: TPerson;
begin
  result := FKontrollorgan.Value;
end;

procedure TKontrollbericht.SetKontrollorgan(const Value: TPerson);
begin
  FKontrollorgan.Value := Value;
end;

function TKontrollbericht.GetKontrolltyp: TKontrolltyp;
begin
  result := FKontrolltyp.Value;
end;

procedure TKontrollbericht.SetKontrolltyp(const Value: TKontrolltyp);
begin
  FKontrolltyp.Value := Value;
end;

function TKontrollbericht.GetRechtsgrundlage: TRechtsgrundlage;
begin
  result := FRechtsgrundlage.Value;
end;

procedure TKontrollbericht.SetRechtsgrundlage(const Value: TRechtsgrundlage);
begin
  FRechtsgrundlage.Value := Value;
end;

function TKontrollbericht.GetUnterschriftAnwesenderBetrieb: TUnterschrift;
begin
  result := FUnterschriftAnwesenderBetrieb.Value;
end;

procedure TKontrollbericht.SetUnterschriftAnwesenderBetrieb(const Value: TUnterschrift);
begin
  FUnterschriftAnwesenderBetrieb.Value := Value;
end;

function TKontrollbericht.GetUnterschriftKontrollorgan: TUnterschrift;
begin
  result := FUnterschriftKontrollorgan.Value;
end;

procedure TKontrollbericht.SetUnterschriftKontrollorgan(const Value: TUnterschrift);
begin
  FUnterschriftKontrollorgan.Value := Value;
end;

function TKontrollbericht.GetDokument: TDokument;
begin
  result := FDokument.Value;
end;

procedure TKontrollbericht.SetDokument(const Value: TDokument);
begin
  FDokument.Value := Value;
end;

function TKontrollbericht.GetDokumentCC: TDokument;
begin
  result := FDokumentCC.Value;
end;

procedure TKontrollbericht.SetDokumentCC(const Value: TDokument);
begin
  FDokumentCC.Value := Value;
end;

function TKontrollbericht.GetGruppeQuelle: TGruppe;
begin
  result := FGruppeQuelle.Value;
end;

procedure TKontrollbericht.SetGruppeQuelle(const Value: TGruppe);
begin
  FGruppeQuelle.Value := Value;
end;

function TKontrollbericht.GetRevisionsplan: TRevisionsplan;
begin
  result := FRevisionsplan.Value;
end;

procedure TKontrollbericht.SetRevisionsplan(const Value: TRevisionsplan);
begin
  FRevisionsplan.Value := Value;
end;

constructor TKontrollbericht.Create;
begin
  inherited;
  FTodos.SetInitialValue(TList<TTodo>.Create);
  FBewerteteFragen.SetInitialValue(TList<TBewerteteFrage>.Create);
  FProben.SetInitialValue(TList<TKbProbe>.Create);
  FAnwesende.SetInitialValue(TList<TAnwesender>.Create);
  FCckAuswahldaten.SetInitialValue(TList<TCckAuswahl>.Create);
  FOertlichkeiten.SetInitialValue(TList<TKontrollberichtOertlichkeit>.Create);
  FCckBetriebsdaten.SetInitialValue(TList<TCckBetrieb>.Create);
end;

destructor TKontrollbericht.Destroy;
begin
  FCckBetriebsdaten.DestroyValue;
  FOertlichkeiten.DestroyValue;
  FCckAuswahldaten.DestroyValue;
  FAnwesende.DestroyValue;
  FProben.DestroyValue;
  FBewerteteFragen.DestroyValue;
  FTodos.DestroyValue;
  inherited;
end;

function TKontrollbericht.GetTodos: TList<TTodo>;
begin
  result := FTodos.Value;
end;

function TKontrollbericht.GetBewerteteFragen: TList<TBewerteteFrage>;
begin
  result := FBewerteteFragen.Value;
end;

function TKontrollbericht.GetProben: TList<TKbProbe>;
begin
  result := FProben.Value;
end;

function TKontrollbericht.GetAnwesende: TList<TAnwesender>;
begin
  result := FAnwesende.Value;
end;

function TKontrollbericht.GetCckAuswahldaten: TList<TCckAuswahl>;
begin
  result := FCckAuswahldaten.Value;
end;

function TKontrollbericht.GetOertlichkeiten: TList<TKontrollberichtOertlichkeit>;
begin
  result := FOertlichkeiten.Value;
end;

function TKontrollbericht.GetCckBetriebsdaten: TList<TCckBetrieb>;
begin
  result := FCckBetriebsdaten.Value;
end;

{ TKontrollberichtBild }

function TKontrollberichtBild.GetBewerteteFrage: TBewerteteFrage;
begin
  result := FBewerteteFrage.Value;
end;

procedure TKontrollberichtBild.SetBewerteteFrage(const Value: TBewerteteFrage);
begin
  FBewerteteFrage.Value := Value;
end;

function TKontrollberichtBild.GetMangel: TMangel;
begin
  result := FMangel.Value;
end;

procedure TKontrollberichtBild.SetMangel(const Value: TMangel);
begin
  FMangel.Value := Value;
end;

function TKontrollberichtBild.GetAufgenommenVon: TUser;
begin
  result := FAufgenommenVon.Value;
end;

procedure TKontrollberichtBild.SetAufgenommenVon(const Value: TUser);
begin
  FAufgenommenVon.Value := Value;
end;

function TKontrollberichtBild.GetProbe: TKbProbe;
begin
  result := FProbe.Value;
end;

procedure TKontrollberichtBild.SetProbe(const Value: TKbProbe);
begin
  FProbe.Value := Value;
end;

{ TKontrollberichtOertlichkeit }

function TKontrollberichtOertlichkeit.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TKontrollberichtOertlichkeit.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

constructor TKontrollberichtOertlichkeit.Create;
begin
  inherited;
  FOertlichkeitMaengel.SetInitialValue(TList<TMangelOertlichkeit>.Create);
end;

destructor TKontrollberichtOertlichkeit.Destroy;
begin
  FOertlichkeitMaengel.DestroyValue;
  inherited;
end;

function TKontrollberichtOertlichkeit.GetOertlichkeitMaengel: TList<TMangelOertlichkeit>;
begin
  result := FOertlichkeitMaengel.Value;
end;

{ TKontrolltyp }

constructor TKontrolltyp.Create;
begin
  inherited;
  FKontrollberichte.SetInitialValue(TList<TKontrollbericht>.Create);
  FCckModulKontrolltypen.SetInitialValue(TList<TCckModulKontrolltyp>.Create);
  FKontrolltypReports.SetInitialValue(TList<TKontrolltypReport>.Create);
  FChecklistenKontrolltypen.SetInitialValue(TList<TChecklistenKontrolltyp>.Create);
end;

destructor TKontrolltyp.Destroy;
begin
  FChecklistenKontrolltypen.DestroyValue;
  FKontrolltypReports.DestroyValue;
  FCckModulKontrolltypen.DestroyValue;
  FKontrollberichte.DestroyValue;
  inherited;
end;

function TKontrolltyp.GetKontrollberichte: TList<TKontrollbericht>;
begin
  result := FKontrollberichte.Value;
end;

function TKontrolltyp.GetCckModulKontrolltypen: TList<TCckModulKontrolltyp>;
begin
  result := FCckModulKontrolltypen.Value;
end;

function TKontrolltyp.GetKontrolltypReports: TList<TKontrolltypReport>;
begin
  result := FKontrolltypReports.Value;
end;

function TKontrolltyp.GetChecklistenKontrolltypen: TList<TChecklistenKontrolltyp>;
begin
  result := FChecklistenKontrolltypen.Value;
end;

{ TKontrolltypReport }

function TKontrolltypReport.GetKontrolltyp: TKontrolltyp;
begin
  result := FKontrolltyp.Value;
end;

procedure TKontrolltypReport.SetKontrolltyp(const Value: TKontrolltyp);
begin
  FKontrolltyp.Value := Value;
end;

function TKontrolltypReport.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TKontrolltypReport.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TKontrolltypReport.GetReport: TReport;
begin
  result := FReport.Value;
end;

procedure TKontrolltypReport.SetReport(const Value: TReport);
begin
  FReport.Value := Value;
end;

function TKontrolltypReport.GetReportTyp: TReportTyp;
begin
  result := FReportTyp.Value;
end;

procedure TKontrolltypReport.SetReportTyp(const Value: TReportTyp);
begin
  FReportTyp.Value := Value;
end;

{ TLand }

constructor TLand.Create;
begin
  inherited;
  FBundeslaender.SetInitialValue(TList<TBundesland>.Create);
end;

destructor TLand.Destroy;
begin
  FBundeslaender.DestroyValue;
  inherited;
end;

function TLand.GetBundeslaender: TList<TBundesland>;
begin
  result := FBundeslaender.Value;
end;

{ TMangel }

function TMangel.GetStatus: TMangelStatus;
begin
  result := FStatus.Value;
end;

procedure TMangel.SetStatus(const Value: TMangelStatus);
begin
  FStatus.Value := Value;
end;

function TMangel.GetMangeltyp: TMangeltyp;
begin
  result := FMangeltyp.Value;
end;

procedure TMangel.SetMangeltyp(const Value: TMangeltyp);
begin
  FMangeltyp.Value := Value;
end;

function TMangel.GetMassnahme: TMassnahme;
begin
  result := FMassnahme.Value;
end;

procedure TMangel.SetMassnahme(const Value: TMassnahme);
begin
  FMassnahme.Value := Value;
end;

function TMangel.GetMangelWeiterfuehrung: TMangel;
begin
  result := FMangelWeiterfuehrung.Value;
end;

procedure TMangel.SetMangelWeiterfuehrung(const Value: TMangel);
begin
  FMangelWeiterfuehrung.Value := Value;
end;

constructor TMangel.Create;
begin
  inherited;
  FBewerteteFragen.SetInitialValue(TList<TBewerteteFrage>.Create);
  FBilder.SetInitialValue(TList<TKontrollberichtBild>.Create);
  FMaengelKontrollbereiche.SetInitialValue(TList<TMangelKontrollbereich>.Create);
  FWeitergefuehrtVonMangel.SetInitialValue(TList<TMangel>.Create);
  FMangelOertlichkeiten.SetInitialValue(TList<TMangelOertlichkeit>.Create);
end;

destructor TMangel.Destroy;
begin
  FMangelOertlichkeiten.DestroyValue;
  FWeitergefuehrtVonMangel.DestroyValue;
  FMaengelKontrollbereiche.DestroyValue;
  FBilder.DestroyValue;
  FBewerteteFragen.DestroyValue;
  inherited;
end;

function TMangel.GetBewerteteFragen: TList<TBewerteteFrage>;
begin
  result := FBewerteteFragen.Value;
end;

function TMangel.GetBilder: TList<TKontrollberichtBild>;
begin
  result := FBilder.Value;
end;

function TMangel.GetMaengelKontrollbereiche: TList<TMangelKontrollbereich>;
begin
  result := FMaengelKontrollbereiche.Value;
end;

function TMangel.GetWeitergefuehrtVonMangel: TList<TMangel>;
begin
  result := FWeitergefuehrtVonMangel.Value;
end;

function TMangel.GetMangelOertlichkeiten: TList<TMangelOertlichkeit>;
begin
  result := FMangelOertlichkeiten.Value;
end;

{ TMangelKontrollbereich }

function TMangelKontrollbereich.GetMangel: TMangel;
begin
  result := FMangel.Value;
end;

procedure TMangelKontrollbereich.SetMangel(const Value: TMangel);
begin
  FMangel.Value := Value;
end;

function TMangelKontrollbereich.GetKontrollbereich: TKontrollbereich;
begin
  result := FKontrollbereich.Value;
end;

procedure TMangelKontrollbereich.SetKontrollbereich(const Value: TKontrollbereich);
begin
  FKontrollbereich.Value := Value;
end;

{ TMangelOertlichkeit }

function TMangelOertlichkeit.GetMangel: TMangel;
begin
  result := FMangel.Value;
end;

procedure TMangelOertlichkeit.SetMangel(const Value: TMangel);
begin
  FMangel.Value := Value;
end;

function TMangelOertlichkeit.GetOertlichkeit: TKontrollberichtOertlichkeit;
begin
  result := FOertlichkeit.Value;
end;

procedure TMangelOertlichkeit.SetOertlichkeit(const Value: TKontrollberichtOertlichkeit);
begin
  FOertlichkeit.Value := Value;
end;

{ TMangelStatus }

constructor TMangelStatus.Create;
begin
  inherited;
  FMaengel.SetInitialValue(TList<TMangel>.Create);
end;

destructor TMangelStatus.Destroy;
begin
  FMaengel.DestroyValue;
  inherited;
end;

function TMangelStatus.GetMaengel: TList<TMangel>;
begin
  result := FMaengel.Value;
end;

{ TMangeltyp }

function TMangeltyp.GetBkbtyp: TBkbTyp;
begin
  result := FBkbtyp.Value;
end;

procedure TMangeltyp.SetBkbtyp(const Value: TBkbTyp);
begin
  FBkbtyp.Value := Value;
end;

function TMangeltyp.GetMassnahmenkatalog: TMassnahmenkatalog;
begin
  result := FMassnahmenkatalog.Value;
end;

procedure TMangeltyp.SetMassnahmenkatalog(const Value: TMassnahmenkatalog);
begin
  FMassnahmenkatalog.Value := Value;
end;

constructor TMangeltyp.Create;
begin
  inherited;
  FMaengel.SetInitialValue(TList<TMangel>.Create);
  FFragenBewertungen.SetInitialValue(TList<TFrageBewertung>.Create);
end;

destructor TMangeltyp.Destroy;
begin
  FFragenBewertungen.DestroyValue;
  FMaengel.DestroyValue;
  inherited;
end;

function TMangeltyp.GetMaengel: TList<TMangel>;
begin
  result := FMaengel.Value;
end;

function TMangeltyp.GetFragenBewertungen: TList<TFrageBewertung>;
begin
  result := FFragenBewertungen.Value;
end;

{ TMassnahme }

function TMassnahme.GetMassnahmenkatalog: TMassnahmenkatalog;
begin
  result := FMassnahmenkatalog.Value;
end;

procedure TMassnahme.SetMassnahmenkatalog(const Value: TMassnahmenkatalog);
begin
  FMassnahmenkatalog.Value := Value;
end;

constructor TMassnahme.Create;
begin
  inherited;
  FFragenBewertungen.SetInitialValue(TList<TFrageBewertung>.Create);
  FMaengel.SetInitialValue(TList<TMangel>.Create);
end;

destructor TMassnahme.Destroy;
begin
  FMaengel.DestroyValue;
  FFragenBewertungen.DestroyValue;
  inherited;
end;

function TMassnahme.GetFragenBewertungen: TList<TFrageBewertung>;
begin
  result := FFragenBewertungen.Value;
end;

function TMassnahme.GetMaengel: TList<TMangel>;
begin
  result := FMaengel.Value;
end;

{ TMassnahmenkatalog }

constructor TMassnahmenkatalog.Create;
begin
  inherited;
  FMassnahmen.SetInitialValue(TList<TMassnahme>.Create);
  FMangeltypen.SetInitialValue(TList<TMangeltyp>.Create);
end;

destructor TMassnahmenkatalog.Destroy;
begin
  FMangeltypen.DestroyValue;
  FMassnahmen.DestroyValue;
  inherited;
end;

function TMassnahmenkatalog.GetMassnahmen: TList<TMassnahme>;
begin
  result := FMassnahmen.Value;
end;

function TMassnahmenkatalog.GetMangeltypen: TList<TMangeltyp>;
begin
  result := FMangeltypen.Value;
end;

{ TModul }

constructor TModul.Create;
begin
  inherited;
  FBundeslaenderModule.SetInitialValue(TList<TBundeslandModul>.Create);
  FBkbtypen.SetInitialValue(TList<TBkbTyp>.Create);
end;

destructor TModul.Destroy;
begin
  FBkbtypen.DestroyValue;
  FBundeslaenderModule.DestroyValue;
  inherited;
end;

function TModul.GetBundeslaenderModule: TList<TBundeslandModul>;
begin
  result := FBundeslaenderModule.Value;
end;

function TModul.GetBkbtypen: TList<TBkbTyp>;
begin
  result := FBkbtypen.Value;
end;

{ TModulInstanz }

function TModulInstanz.GetModul: TProgrammModul;
begin
  result := FModul.Value;
end;

procedure TModulInstanz.SetModul(const Value: TProgrammModul);
begin
  FModul.Value := Value;
end;

{ TNachricht }

function TNachricht.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TNachricht.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

function TNachricht.GetAbsender: TUser;
begin
  result := FAbsender.Value;
end;

procedure TNachricht.SetAbsender(const Value: TUser);
begin
  FAbsender.Value := Value;
end;

constructor TNachricht.Create;
begin
  inherited;
  FZustellungen.SetInitialValue(TList<TNachrichtenZustellung>.Create);
  FVNachrichtenUser.SetInitialValue(TList<TVNachrichtForUser>.Create);
end;

destructor TNachricht.Destroy;
begin
  FVNachrichtenUser.DestroyValue;
  FZustellungen.DestroyValue;
  inherited;
end;

function TNachricht.GetZustellungen: TList<TNachrichtenZustellung>;
begin
  result := FZustellungen.Value;
end;

function TNachricht.GetVNachrichtenUser: TList<TVNachrichtForUser>;
begin
  result := FVNachrichtenUser.Value;
end;

{ TNachrichtenZustellung }

function TNachrichtenZustellung.GetNachricht: TNachricht;
begin
  result := FNachricht.Value;
end;

procedure TNachrichtenZustellung.SetNachricht(const Value: TNachricht);
begin
  FNachricht.Value := Value;
end;

function TNachrichtenZustellung.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TNachrichtenZustellung.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

{ TPerson }

function TPerson.GetAdresse: TAdresse;
begin
  result := FAdresse.Value;
end;

procedure TPerson.SetAdresse(const Value: TAdresse);
begin
  FAdresse.Value := Value;
end;

constructor TPerson.Create;
begin
  inherited;
  FKontrollberichteErfasser.SetInitialValue(TList<TKontrollbericht>.Create);
  FKontrollberichteKontrollorgan.SetInitialValue(TList<TKontrollbericht>.Create);
  FKommunikationswege.SetInitialValue(TList<TPersonenKommunikationswege>.Create);
  FUsers.SetInitialValue(TList<TUser>.Create);
end;

destructor TPerson.Destroy;
begin
  FUsers.DestroyValue;
  FKommunikationswege.DestroyValue;
  FKontrollberichteKontrollorgan.DestroyValue;
  FKontrollberichteErfasser.DestroyValue;
  inherited;
end;

function TPerson.GetKontrollberichteErfasser: TList<TKontrollbericht>;
begin
  result := FKontrollberichteErfasser.Value;
end;

function TPerson.GetKontrollberichteKontrollorgan: TList<TKontrollbericht>;
begin
  result := FKontrollberichteKontrollorgan.Value;
end;

function TPerson.GetKommunikationswege: TList<TPersonenKommunikationswege>;
begin
  result := FKommunikationswege.Value;
end;

function TPerson.GetUsers: TList<TUser>;
begin
  result := FUsers.Value;
end;

{ TPersonenKommunikationswege }

function TPersonenKommunikationswege.GetKommunikationsweg: TKommunikationsweg;
begin
  result := FKommunikationsweg.Value;
end;

procedure TPersonenKommunikationswege.SetKommunikationsweg(const Value: TKommunikationsweg);
begin
  FKommunikationsweg.Value := Value;
end;

function TPersonenKommunikationswege.GetPerson: TPerson;
begin
  result := FPerson.Value;
end;

procedure TPersonenKommunikationswege.SetPerson(const Value: TPerson);
begin
  FPerson.Value := Value;
end;

{ TProgrammModul }

constructor TProgrammModul.Create;
begin
  inherited;
  FFunktionen.SetInitialValue(TList<TFunktion>.Create);
end;

destructor TProgrammModul.Destroy;
begin
  FFunktionen.DestroyValue;
  inherited;
end;

function TProgrammModul.GetFunktionen: TList<TFunktion>;
begin
  result := FFunktionen.Value;
end;

{ TRechtsgrundlage }

constructor TRechtsgrundlage.Create;
begin
  inherited;
  FKontrollberichte.SetInitialValue(TList<TKontrollbericht>.Create);
  FBkbtypen.SetInitialValue(TList<TBkbtypenRechtsgrundlage>.Create);
end;

destructor TRechtsgrundlage.Destroy;
begin
  FBkbtypen.DestroyValue;
  FKontrollberichte.DestroyValue;
  inherited;
end;

function TRechtsgrundlage.GetKontrollberichte: TList<TKontrollbericht>;
begin
  result := FKontrollberichte.Value;
end;

function TRechtsgrundlage.GetBkbtypen: TList<TBkbtypenRechtsgrundlage>;
begin
  result := FBkbtypen.Value;
end;

{ TRegistrierung }

constructor TRegistrierung.Create;
begin
  inherited;
  FZulassungen.SetInitialValue(TList<TZulassung>.Create);
  FBetriebe.SetInitialValue(TList<TBetrieb>.Create);
end;

destructor TRegistrierung.Destroy;
begin
  FBetriebe.DestroyValue;
  FZulassungen.DestroyValue;
  inherited;
end;

function TRegistrierung.GetZulassungen: TList<TZulassung>;
begin
  result := FZulassungen.Value;
end;

function TRegistrierung.GetBetriebe: TList<TBetrieb>;
begin
  result := FBetriebe.Value;
end;

{ TReport }

constructor TReport.Create;
begin
  inherited;
  FReportKontrolltypen.SetInitialValue(TList<TKontrolltypReport>.Create);
end;

destructor TReport.Destroy;
begin
  FReportKontrolltypen.DestroyValue;
  inherited;
end;

function TReport.GetReportKontrolltypen: TList<TKontrolltypReport>;
begin
  result := FReportKontrolltypen.Value;
end;

{ TRevisionsplan }

function TRevisionsplan.GetBldcode: TBundesland;
begin
  result := FBldcode.Value;
end;

procedure TRevisionsplan.SetBldcode(const Value: TBundesland);
begin
  FBldcode.Value := Value;
end;

function TRevisionsplan.GetRevisionsstamm: TRevisionsstamm;
begin
  result := FRevisionsstamm.Value;
end;

procedure TRevisionsplan.SetRevisionsstamm(const Value: TRevisionsstamm);
begin
  FRevisionsstamm.Value := Value;
end;

{ TRevisionsstamm }

function TRevisionsstamm.GetKontrollTyp: TKontrolltyp;
begin
  result := FKontrollTyp.Value;
end;

procedure TRevisionsstamm.SetKontrollTyp(const Value: TKontrolltyp);
begin
  FKontrollTyp.Value := Value;
end;

function TRevisionsstamm.GetRechtsgrundlage: TRechtsgrundlage;
begin
  result := FRechtsgrundlage.Value;
end;

procedure TRevisionsstamm.SetRechtsgrundlage(const Value: TRechtsgrundlage);
begin
  FRechtsgrundlage.Value := Value;
end;

function TRevisionsstamm.GetRevisionsSchema: TRevisionsSchema;
begin
  result := FRevisionsSchema.Value;
end;

procedure TRevisionsstamm.SetRevisionsSchema(const Value: TRevisionsSchema);
begin
  FRevisionsSchema.Value := Value;
end;

{ TRolle }

constructor TRolle.Create;
begin
  inherited;
  FFunktionenRollen.SetInitialValue(TList<TFunktionRolle>.Create);
  FUserRollen.SetInitialValue(TList<TUserrolle>.Create);
  FRollenBkbtypen.SetInitialValue(TList<TRollenBkbtypen>.Create);
end;

destructor TRolle.Destroy;
begin
  FRollenBkbtypen.DestroyValue;
  FUserRollen.DestroyValue;
  FFunktionenRollen.DestroyValue;
  inherited;
end;

function TRolle.GetFunktionenRollen: TList<TFunktionRolle>;
begin
  result := FFunktionenRollen.Value;
end;

function TRolle.GetUserRollen: TList<TUserrolle>;
begin
  result := FUserRollen.Value;
end;

function TRolle.GetRollenBkbtypen: TList<TRollenBkbtypen>;
begin
  result := FRollenBkbtypen.Value;
end;

{ TRollenBkbtypen }

function TRollenBkbtypen.GetBkbtyp: TBkbTyp;
begin
  result := FBkbtyp.Value;
end;

procedure TRollenBkbtypen.SetBkbtyp(const Value: TBkbTyp);
begin
  FBkbtyp.Value := Value;
end;

function TRollenBkbtypen.GetRolle: TRolle;
begin
  result := FRolle.Value;
end;

procedure TRollenBkbtypen.SetRolle(const Value: TRolle);
begin
  FRolle.Value := Value;
end;

{ TSys }

constructor TSys.Create;
begin
  inherited;
  FBkbnummern.SetInitialValue(TList<TBkbnummer>.Create);
end;

destructor TSys.Destroy;
begin
  FBkbnummern.DestroyValue;
  inherited;
end;

function TSys.GetBkbnummern: TList<TBkbnummer>;
begin
  result := FBkbnummern.Value;
end;

{ TTodo }

function TTodo.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TTodo.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

function TTodo.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TTodo.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

function TTodo.GetKontrollbericht: TKontrollbericht;
begin
  result := FKontrollbericht.Value;
end;

procedure TTodo.SetKontrollbericht(const Value: TKontrollbericht);
begin
  FKontrollbericht.Value := Value;
end;

{ TUnterschrift }

constructor TUnterschrift.Create;
begin
  inherited;
  FKontrollberichteAnwesender.SetInitialValue(TList<TKontrollbericht>.Create);
  FKontrollberichteKontrollorgan.SetInitialValue(TList<TKontrollbericht>.Create);
end;

destructor TUnterschrift.Destroy;
begin
  FKontrollberichteKontrollorgan.DestroyValue;
  FKontrollberichteAnwesender.DestroyValue;
  inherited;
end;

function TUnterschrift.GetKontrollberichteAnwesender: TList<TKontrollbericht>;
begin
  result := FKontrollberichteAnwesender.Value;
end;

function TUnterschrift.GetKontrollberichteKontrollorgan: TList<TKontrollbericht>;
begin
  result := FKontrollberichteKontrollorgan.Value;
end;

{ TUser }

function TUser.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TUser.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

function TUser.GetPerson: TPerson;
begin
  result := FPerson.Value;
end;

procedure TUser.SetPerson(const Value: TPerson);
begin
  FPerson.Value := Value;
end;

constructor TUser.Create;
begin
  inherited;
  FStellvertreterFuerGruppen.SetInitialValue(TList<TGruppe>.Create);
  FNachrichten.SetInitialValue(TList<TNachricht>.Create);
  FNachrichtenZustellungen.SetInitialValue(TList<TNachrichtenZustellung>.Create);
  FUsergruppen.SetInitialValue(TList<TUsergruppe>.Create);
  FUserrollen.SetInitialValue(TList<TUserrolle>.Create);
  FTodos.SetInitialValue(TList<TTodo>.Create);
  FHauptverantwortlicherFuerGruppen.SetInitialValue(TList<TGruppe>.Create);
  FKontrollberichtBilder.SetInitialValue(TList<TKontrollberichtBild>.Create);
end;

destructor TUser.Destroy;
begin
  FKontrollberichtBilder.DestroyValue;
  FHauptverantwortlicherFuerGruppen.DestroyValue;
  FTodos.DestroyValue;
  FUserrollen.DestroyValue;
  FUsergruppen.DestroyValue;
  FNachrichtenZustellungen.DestroyValue;
  FNachrichten.DestroyValue;
  FStellvertreterFuerGruppen.DestroyValue;
  inherited;
end;

function TUser.GetStellvertreterFuerGruppen: TList<TGruppe>;
begin
  result := FStellvertreterFuerGruppen.Value;
end;

function TUser.GetNachrichten: TList<TNachricht>;
begin
  result := FNachrichten.Value;
end;

function TUser.GetNachrichtenZustellungen: TList<TNachrichtenZustellung>;
begin
  result := FNachrichtenZustellungen.Value;
end;

function TUser.GetUsergruppen: TList<TUsergruppe>;
begin
  result := FUsergruppen.Value;
end;

function TUser.GetUserrollen: TList<TUserrolle>;
begin
  result := FUserrollen.Value;
end;

function TUser.GetTodos: TList<TTodo>;
begin
  result := FTodos.Value;
end;

function TUser.GetHauptverantwortlicherFuerGruppen: TList<TGruppe>;
begin
  result := FHauptverantwortlicherFuerGruppen.Value;
end;

function TUser.GetKontrollberichtBilder: TList<TKontrollberichtBild>;
begin
  result := FKontrollberichtBilder.Value;
end;

{ TUsergruppe }

function TUsergruppe.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TUsergruppe.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

function TUsergruppe.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TUsergruppe.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

{ TUserrolle }

function TUserrolle.GetRolle: TRolle;
begin
  result := FRolle.Value;
end;

procedure TUserrolle.SetRolle(const Value: TRolle);
begin
  FRolle.Value := Value;
end;

function TUserrolle.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TUserrolle.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

function TUserrolle.GetModulInstanz: TModulInstanz;
begin
  result := FModulInstanz.Value;
end;

procedure TUserrolle.SetModulInstanz(const Value: TModulInstanz);
begin
  FModulInstanz.Value := Value;
end;

{ TVbetriebbezirksgruppe }

function TVbetriebbezirksgruppe.GetBetrieb: TBetrieb;
begin
  result := FBetrieb.Value;
end;

procedure TVbetriebbezirksgruppe.SetBetrieb(const Value: TBetrieb);
begin
  FBetrieb.Value := Value;
end;

function TVbetriebbezirksgruppe.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TVbetriebbezirksgruppe.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

{ TVgroupuser }

function TVgroupuser.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TVgroupuser.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

function TVgroupuser.GetGroupUser: TUser;
begin
  result := FGroupUser.Value;
end;

procedure TVgroupuser.SetGroupUser(const Value: TUser);
begin
  FGroupUser.Value := Value;
end;

function TVgroupuser.GetGroup: TGruppe;
begin
  result := FGroup.Value;
end;

procedure TVgroupuser.SetGroup(const Value: TGruppe);
begin
  FGroup.Value := Value;
end;

function TVgroupuser.GetGroupPerson: TPerson;
begin
  result := FGroupPerson.Value;
end;

procedure TVgroupuser.SetGroupPerson(const Value: TPerson);
begin
  FGroupPerson.Value := Value;
end;

function TVgroupuser.GetBundesland: TBundesland;
begin
  result := FBundesland.Value;
end;

procedure TVgroupuser.SetBundesland(const Value: TBundesland);
begin
  FBundesland.Value := Value;
end;

{ TVNachrichtForUser }

function TVNachrichtForUser.GetNachricht: TNachricht;
begin
  result := FNachricht.Value;
end;

procedure TVNachrichtForUser.SetNachricht(const Value: TNachricht);
begin
  FNachricht.Value := Value;
end;

function TVNachrichtForUser.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TVNachrichtForUser.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

function TVNachrichtForUser.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TVNachrichtForUser.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

function TVNachrichtForUser.GetNachrichtenZustellung: TNachrichtenZustellung;
begin
  result := FNachrichtenZustellung.Value;
end;

procedure TVNachrichtForUser.SetNachrichtenZustellung(const Value: TNachrichtenZustellung);
begin
  FNachrichtenZustellung.Value := Value;
end;

{ TVUserGroupMembership }

function TVUserGroupMembership.GetGruppe: TGruppe;
begin
  result := FGruppe.Value;
end;

procedure TVUserGroupMembership.SetGruppe(const Value: TGruppe);
begin
  FGruppe.Value := Value;
end;

function TVUserGroupMembership.GetUser: TUser;
begin
  result := FUser.Value;
end;

procedure TVUserGroupMembership.SetUser(const Value: TUser);
begin
  FUser.Value := Value;
end;

{ TZulassung }

function TZulassung.GetRegistrierung: TRegistrierung;
begin
  result := FRegistrierung.Value;
end;

procedure TZulassung.SetRegistrierung(const Value: TRegistrierung);
begin
  FRegistrierung.Value := Value;
end;

initialization
  RegisterEntity(TAdresse);
  RegisterEntity(TAnsprechpartner);
  RegisterEntity(TAPTyp);
  RegisterEntity(TBetrieb);
  RegisterEntity(TBewertung);
  RegisterEntity(TBkbTyp);
  RegisterEntity(TBundesland);
  RegisterEntity(TBundeslandModul);
  RegisterEntity(TCheckliste);
  RegisterEntity(TFragengruppe);
  RegisterEntity(TFrage);
  RegisterEntity(TFunktion);
  RegisterEntity(TFunktionRolle);
  RegisterEntity(TGemeinde);
  RegisterEntity(TGruppe);
  RegisterEntity(TKontrollbericht);
  RegisterEntity(TKontrolltyp);
  RegisterEntity(TLand);
  RegisterEntity(TMangel);
  RegisterEntity(TModul);
  RegisterEntity(TNachricht);
  RegisterEntity(TNachrichtenZustellung);
  RegisterEntity(TPerson);
  RegisterEntity(TProgrammModul);
  RegisterEntity(TRechtsgrundlage);
  RegisterEntity(TRevisionsplan);
  RegisterEntity(TRevisionsstamm);
  RegisterEntity(TRolle);
  RegisterEntity(TSys);
  RegisterEntity(TTodo);
  RegisterEntity(TUser);
  RegisterEntity(TUsergruppe);
  RegisterEntity(TUserrolle);
  RegisterEntity(TZulassung);
  RegisterEntity(TZusatztext);
  RegisterEntity(TPersonenKommunikationswege);
  RegisterEntity(TBkbtypenRechtsgrundlage);
  RegisterEntity(TBetriebeKommunikationswege);
  RegisterEntity(TKommunikationsart);
  RegisterEntity(TKommunikationsweg);
  RegisterEntity(TRollenBkbtypen);
  RegisterEntity(TModulInstanz);
  RegisterEntity(TFormatierung);
  RegisterEntity(TBkbnummer);
  RegisterEntity(TBewerteteFrage);
  RegisterEntity(TFrageBewertung);
  RegisterEntity(TModuleBkbtypen);
  RegisterEntity(TMassnahmenkatalog);
  RegisterEntity(TMassnahme);
  RegisterEntity(TMangeltyp);
  RegisterEntity(TMangelStatus);
  RegisterEntity(TKontrollberichtBild);
  RegisterEntity(TKontrollbereich);
  RegisterEntity(TFrageKontrollbereich);
  RegisterEntity(TKbProbe);
  RegisterEntity(TMangelKontrollbereich);
  RegisterEntity(TBewertungsIcon);
  RegisterEntity(TBewertungstyp);
  RegisterEntity(TUnterschrift);
  RegisterEntity(TAnwesender);
  RegisterEntity(TRegistrierung);
  RegisterEntity(TCckAuftrag);
  RegisterEntity(TCckAuftragsart);
  RegisterEntity(TCckAuftragsbewertung);
  RegisterEntity(TCckAuswahl);
  RegisterEntity(TCckBetrieb);
  RegisterEntity(TCckModul);
  RegisterEntity(TCckModulAnforderung);
  RegisterEntity(TCckModulKontrolltyp);
  RegisterEntity(TCckTierdaten);
  RegisterEntity(TCckVokSanktion);
  RegisterEntity(TDokument);
  RegisterEntity(TKontrollberichtOertlichkeit);
  RegisterEntity(TMangelOertlichkeit);
  RegisterEntity(TReport);
  RegisterEntity(TKontrolltypReport);
  RegisterEntity(TReportTyp);
  RegisterEntity(TEmailArten);
  RegisterEntity(TEmailTexte);
  RegisterEntity(TChecklistenKontrolltyp);
  RegisterEntity(TBundeslandChecklistenKontrolltyp);
  RegisterEntity(TVbetriebsinfo);
  RegisterEntity(TVNachrichtForUser);
  RegisterEntity(TvUserKontrollen);
  RegisterEntity(TVUserGroupMembership);
  RegisterEntity(TBetriebRevstamm);
  RegisterEntity(TRevisionsSchema);
  RegisterEntity(TVbetriebbezirksgruppe);
  RegisterEntity(TVgroupuser);
  RegisterEntity(TCckStatusMeldung);
  RegisterEntity(TCckStatus);
  RegisterEntity(TEmailHistory);
  RegisterEntity(TEmailHistoryAttachment);

end.
