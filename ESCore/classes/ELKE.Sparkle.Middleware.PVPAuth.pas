﻿unit ELKE.Sparkle.Middleware.PVPAuth;

interface

uses
  System.Classes, System.SysUtils,
  Sparkle.HttpServer.Context, Sparkle.HttpServer.Module, Sparkle.Security,
  ELKE.Classes.PVP.Token;

type
  IUserIdentity = Sparkle.Security.IUserIdentity;
  TUserIdentity = Sparkle.Security.TUserIdentity;

  TAuthenticateProc = reference to procedure(const AToken: TPVPToken; var User: IUserIdentity);

  TPVPAuthMiddleware = class(THttpServerMiddleware)
  private
    FOnAuthenticate: TAuthenticateProc;
  protected
    function RetrieveIdentity(const AToken: TPVPToken): IUserIdentity; virtual;
    procedure DoAuthenticate(const AToken: TPVPToken; var User: IUserIdentity); virtual;
    procedure ProcessRequest(AContext: THttpServerContext; Next: THttpServerProc); override;
  public
    constructor Create; overload;
    constructor Create(AAuthenticateProc: TAuthenticateProc); overload;
    property OnAuthenticate: TAuthenticateProc read FOnAuthenticate write FOnAuthenticate;
  end;

implementation

uses
  ELKE.Server.Logger;

{ TTokenAuthMiddleware }

constructor TPVPAuthMiddleware.Create;
begin
  Create(nil);
end;

constructor TPVPAuthMiddleware.Create(AAuthenticateProc: TAuthenticateProc);
begin
  OnAuthenticate := AAuthenticateProc;
end;

procedure TPVPAuthMiddleware.DoAuthenticate(const AToken: TPVPToken; var User: IUserIdentity);
begin
  if Assigned(FOnAuthenticate) then
    FOnAuthenticate(AToken, User);
end;

procedure TPVPAuthMiddleware.ProcessRequest(AContext: THttpServerContext; Next: THttpServerProc);
begin
  if AContext.Request.User = nil then
  begin

    // Todo: Genau überlegen, wie wir hier das Token validieren wollen
    // Version, GID ....
    // Funktionen/Rollen beachten
    try
      var LToken := TPVPToken.Create(AContext.Request.Headers);
      try
        ELKELog(LToken);

        if LToken.PVP_VERSION.ValueOrDefault <> '2.1' then
          raise Exception.Create('PVP-EgovToken-Version ("' + LToken.PVP_VERSION.ValueOrDefault +
            '") nicht unterstützt');

        if LToken.GID.ValueOrDefault.Trim = '' then
          raise Exception.Create('Keine GID (X-PVP-GID) angegeben!');

        if LToken.PRINCIPAL_NAME.ValueOrDefault.Trim = '' then
          raise Exception.Create('Nachname fehlt im PVP Token (X-PVP-PRINCIPAL-NAME)!');

        if LToken.GIVEN_NAME.ValueOrDefault.Trim = '' then
          raise Exception.Create('Vorname fehlt im PVP Token (X-PVP-GIVEN-NAME)!');

        AContext.Request.User := RetrieveIdentity(LToken);
      finally
        FreeAndNil(LToken);
      end;
      Next(AContext);
    except
      on e: Exception do
      begin
        AContext.Response.StatusCode := 403;
        AContext.Response.StatusReason := 'Zugriff verweigert. ' + e.Message;
        ELKELogError(AContext.Response.StatusReason);
      end;
    end;
  end;
end;

function TPVPAuthMiddleware.RetrieveIdentity(const AToken: TPVPToken): IUserIdentity;
begin
  Result := nil;

  Result := TUserIdentity.Create;
  Result.Claims.AddOrSet('GID', AToken.GID.ValueOrDefault);
  Result.Claims.AddOrSet('Mail', AToken.MAIL.ValueOrDefault);
  Result.Claims.AddOrSet('TXID', AToken.TXID.ValueOrDefault);

  // Aktuell nicht wirklich genutzt.
  DoAuthenticate(AToken, Result);
end;

end.

