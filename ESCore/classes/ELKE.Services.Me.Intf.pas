﻿unit ELKE.Services.Me.Intf;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  XData.Service.Common,
  Aurelius.Mapping.Attributes,
  ELKE.Classes, ELKE.Classes.Generated, ELKE.Classes.Request, ELKE.Classes.PVP.Token, ELKE.Classes.PVP.Roles;

const
  // Unsere HTTP Status Codes sind im Bereich 471-479

  /// <summary>
  /// Das übermittelte Objekt wurde bereits gespeichert. Z.b. bei
  /// KontrolleBeenden oder BildSenden
  /// </summary>
  HTTP_STATUS_OBJECT_ALREADY_SAVED = 471;

  FREIER_BETRIEB = '9999999';

  // Kontrollen
  STATUS_ALLE = 'S,E,U,G,P,V,L,O';
  STATUS_OFFEN = 'PUL';
  STATUS_BEENDET = 'GVSOE';

  GROUP_LEVEL_ALLE = 100; // 100 ist wilkürlich, aber mehr als 100 Ebenen in den Gruppen wird es kaum geben

type

  /// <summary>
  /// Im ME Interface/Endpunkt werden Funktionen angeboten, die sich direkt
  /// auf den aktuellen User beziehen.
  /// </summary>
  [Model('ELKE')]
  [ServiceContract]
  [URIPathSegment('me')]
  IMe = interface(IInvokable)
    ['{81BBB0DF-7781-4945-8B64-80CADD864E7C}']

    /// <summary>
    /// Gibt das PVP-Token für den aktuellen User zurück.
    /// </summary>
    /// <remarks>
    /// Die PVP Nomenklatur verwendet zumindest für das PVP-Token eine
    /// spezielle "SnakeUpperCase" Convention: <br /><br />PVP-VERSION <br /><br />
    /// Da dies in Pascal nicht valide ist, verwenden wir hier eine
    /// Underscore-Variante: <br /><br />PVP_VERSION <br />
    /// </remarks>
    [HttpGet]
    [URIPathSegment('pvpToken')]
    function GetPVPToken: TPVPToken;

    [HttpGet]
    [URIPathSegment('')]
    function Me: TUser;

    /// <summary>
    /// Gibt die geplanten Kontrollen des Users zurück - also keine ungeplantene Kontrollen.
    /// Es werden nur die dem User zugewiesene Kontrollen, also ohne Gruppen angzeigt.
    /// GroupLevel: 0
    // Status: G
    /// </summary>
    /// <remarks>
    /// Alle "Kontrollen" Requests, mit Paging Parametern liefern
    /// Informationen über das Paging in folgenden Response Headern (mit
    /// Beispielwerten) <br /><br />x-records: 10 - Anzahl der Records in der
    /// aktuellen Page <br />x-records-page: 1 - Aktuelle Page (1-basierend) <br />
    /// x-records-page-size: 10 - Größe der Page <br />x-records-total: 723 -
    /// Anzahl aller Records (über alle Pages) <br />x-records-page-max: 73 -
    /// Anzahl der Pages
    /// </remarks>
    [HttpGet]
    [URIPathSegment('kontrollen')]
    function Kontrollen(
      [XDefault(1)]Page: integer = 1;
      [XDefault(10)]PageSize: integer = 10
      ): TList<TKontrollbericht>;

    /// <summary>
    /// <para>
    /// Alle Kontrollen des Users und seiner Gruppen, eingeschränkt auf
    /// die für den User gültigen/sichtbaren BKBTypen <br /><br />
    /// Variabler Filter: <br />- Datum <br />- Kontrollstatus
    /// </para>
    /// <para>
    /// WICHTIG! <br />Ungeplante Kontrollen werden anhand des Datums im
    /// Todo selektiert, da diese kein Kontrollbericht.Datum haben. <br />
    /// </para>
    /// </summary>
    /// <param name="DatumVon">
    /// ab Kontrollbericht.EndeZeit  (oder .Datum wenn noch nicht beendet)
    /// </param>
    /// <param name="DatumBis">
    /// bis Kontrollbericht.EndeZeit (oder .Datum wenn noch nicht beendet)
    /// </param>
    /// <param name="Status">
    /// <para>
    /// G = nur Kontrollberichte mit Status "G"
    /// </para>
    /// <para>
    /// GE = Kontrollberichte mit Status "G" und "E" usw.
    /// </para>
    /// <para>
    /// Leer = Kein Filter auf den Status
    /// </para>
    /// </param>
    [HttpGet]
    [URIPathSegment('gruppenKontrollen')]
    function GruppenKontrollen(
      [XDefault(GROUP_LEVEL_ALLE)]GroupLevel: integer = GROUP_LEVEL_ALLE;
      [XDefault(-1)]DatumVon: TDateTime = -1;
      [XDefault(-1)]DatumBis: TDateTime = -1;
      [XDefault('')]Status: string = '';
      [XDefault(1)]Page: integer = 1;
      [XDefault(10)]PageSize: integer = 10): TList<TKontrollbericht>;

    /// <summary>
    /// Gibt die, für den User sichtbaren, ungeplanten Kontrollen zurück.
    /// Inkl. direkte Untergruppen. GroupLevel 1
    /// </summary>
    [HttpGet]
    [URIPathSegment('ungeplanteKontrollen')]
    function UngeplanteKontrollen(
      [XDefault(1)]Page: integer = 1;
      [XDefault(10)]PageSize: integer = 10): TList<TKontrollbericht>;

    /// <summary>
    /// Der Kontrollverlauf des Users, d.h. beendete Kontrollen (die eine ENDEZEIT gesetzt haben)
    /// Inkl. direkte Gruppen.
    /// GroupLevel: 1
    /// </summary>
    [HttpGet]
    [URIPathSegment('kontrollverlauf')]
    [FromQuery]
    function kontrollverlauf(
      [XDefault(1)]Page: integer = 1;
      [XDefault(10)]PageSize: integer = 10;
      [XDefault(-1)]DateAb: TDate = -1;
      [XDefault(-1)]DateBis: TDate = -1;
      [XDefault(false)]IncludeDetails: Boolean = false;
      [XDefault(false)]AutoExpand: Boolean = false;
      [XDefault(true)]IncludeTodos: Boolean = true
      ): TList<TKontrollbericht>;

    /// <summary>
    /// Alle beendeten Kontrollen des Users, inkl. der Mängel
    /// Umfang wie "/kontrollverlauf"
    /// </summary>
    [HttpGet]
    [URIPathSegment('kontrollverlaufDetails')]
    function kontrollverlaufDetails(
      [XDefault(1)]Page: integer = 1;
      [XDefault(10)]PageSize: integer = 10): TList<TKontrolle>;

    /// <summary>
    /// Eine Liste aller Kontrollen
    /// Verlauf/Geplante/Ungeplante
    ///  Die Liste ist "flach", also ohne eingebette Objekte
    /// </summary>
    [HttpGet]
    [URIPathSegment('kontrollListe')]
    function kontrollListe(
      Typ: TKontrollListenTyp
      ): TList<TKontrollbericht>;

    /// <summary>
    /// Hier wird ein neuer Kontrollbericht mit den im Body übergebenen Informationen angelegt.
    /// </summary>
    /// <param name="Bericht">
    /// Alle relevanten Eigenschaften eines Kontrollberichts müssen hier angegeben sein
    /// </param>
    /// <param name="Nacherfassung">
    /// Wenn Nacherfassung auf TRUE gestellt wird, dann ist es möglich, einen Kontrollbericht mit einem Planungsdatum in der Vergangenheit zu erstellen. Dies wird genutzt für Berichte die bereits
    /// auf Papier vorliegen und ins System eingefügt werden sollen.
    /// </param>
    /// <returns>
    /// ID des im System abgespeicherten Berichts
    /// </returns>
    /// <remarks>
    /// In Bericht.Betrieb wird intern nur die Id des Betriebs übernommen. Ein Betrieb mit dieser Id MUSS bereits existieren. Implizite Datenänderungen im Betrieb (oder Neuanlagen) sind hier nicht
    /// zulässig, bzw. werden ignoriert.
    /// </remarks>
    [HttpPost]
    [URIPathSegment('neuerKontrollbericht')]
    function NeuerKontrollBericht(Bericht: TKontrollbericht; [XDefault(false)]Nacherfassung: Boolean = false): integer;

    /// <summary>
    /// Hier wird ein neuer *ungeplanter* Kontrollbericht mit den im Body
    /// übergebenen Informationen angelegt.
    /// </summary>
    /// <param name="Bericht">
    /// Alle relevanten Eigenschaften eines Kontrollberichts müssen hier
    /// angegeben sein
    /// </param>
    /// <param name="Todo">
    /// Eine ungeplante Kontrolle braucht ein Todo in dem User, Gruppe und
    /// Fälligkeit definiert werden
    /// </param>
    /// <returns>
    /// ID des im System abgespeicherten Berichts
    /// </returns>
    /// <remarks>
    /// Unterschiede zu /me/neuerKontrollbericht: <br />Wenn im ToDo eine
    /// Gruppe gesetzt ist, bleibt der Erfasser im Kontrollbericht leer. Das
    /// Kontrollbericht.Datum ist nicht gesetzt. Im Todo ist der Titel und
    /// das Faellig-Datum zu setzen. Zusätzlich ist noch die Gruppe oder ein
    /// User zu setzen.
    /// </remarks>
    [HttpPost]
    [URIPathSegment('neuerUngeplanterKontrollbericht')]
    function NeuerUngeplanterKontrollBericht(
      Bericht: TKontrollbericht;
      Todo: TTodo
      ): integer;

    [HttpPost]
    [URIPathSegment('neuerUngeplanterKontrollbericht2')]
    function NeuerUngeplanterKontrollBericht2(Kontrolle: TUngeplanteKontrolle): integer;

    [HttpGet]
    [URIPathSegment('betriebe')]
    function Betriebe(
      Filter: string;
      Top: integer): TList<TBetrieb>;

    /// <summary>
    /// Zeigt die für den angemeldeten User vorhandenen Nachrichten an.
    /// </summary>
    /// <remarks>
    /// Bei AbsenderKz="U" ist in "Absender" der User eingetragen, der die
    /// Nachricht erstellt hat.
    /// </remarks>
    [HttpGet]
    [URIPathSegment('nachrichten')]
    function NachrichtenForUserKurz: TObjectList<TNachrichtKurz>;

    /// <summary>
    /// Listet alle Gruppen in denen der aktuelle User Mitglied ist.
    /// </summary>
    /// <param name="Direkt">
    /// Wenn Direkt =true, dann werden nur die direkten Gruppen (wo der User direkt Mitglied ist) angezeigt. <br />
    /// </param>
    [HttpGet]
    [URIPathSegment('gruppen')]
    function Gruppen([XDefault(true)]Direkt: Boolean = true): TList<TGruppe>;

    [HttpGet]
    [URIPathSegment('roles')]
    [Description('Description from Attribute in interface')]
    function Rollen: TPVPRoles;

    [HttpGet]
    [URIPathSegment('nachrichtGelesen')]
    procedure NachrichtGelesen(Id: integer);

    [HttpGet]
    [URIPathSegment('funktionen')]
    function Funktionen: TList<TFunktion>;

    [HttpGet]
    [URIPathSegment('funktionenKurz')]
    function FunktionenKurz: TObjectList<TFunktionKurz>;

    /// <summary>
    /// Zeigt die Berichtstypen für den User. Die verfügbaren Berichtstypen
    /// ergeben sich durch die Module, die für das Bundesland des Benutzers
    /// frei geschaltet sind. Die Berichtstypen werden weiterhin durch die
    /// Rolle(n) des Users eingeschränkt.
    /// </summary>
    /// <remarks>
    /// Wichtig: Die Eigenschaft "Sichtbar" gibt an, ob es sich um einen für
    /// den User sichtbaren BKBTyp handelt. Nicht-sichtbare BKBTypen werden
    /// nur intern verwendet. Dazu gehören insbesondere die BKBTypen für
    /// Proben. Diese sind "nicht sichtbar" und haben zusätzlich das Flag
    /// Probe=true gesetzt.
    /// </remarks>
    [HttpGet]
    [URIPathSegment('berichtstypen')]
    function Berichtstypen: TList<TBkbTyp>;

    [HttpGet]
    [URIPathSegment('kontrolltypen')]
    function Kontrolltypen(const BkbTyp: string): TList<TKontrolltyp>;

    [HttpGet]
    [URIPathSegment('rechtsgrundlagen')]
    function Rechtsgrundlagen(const BkbTyp: string): TList<TRechtsgrundlage>;

    /// <summary>
    /// Hier kann eine "freie Adresse" eingetragen werden. Diese wird
    /// automatisch mit dem fiktiven Betrieb 9999999 in Beziehung gebracht.
    /// Dies ist für eine sog. "freie Niederschrift" sinnvoll.
    /// </summary>
    [HttpPost]
    [URIPathSegment('freieAdresseHinzufuegen')]
    function FreieAdresseHinzufuegen(Adresse: TFreieAdresse): TAdresse;

    [HttpGet]
    [URIPathSegment('neueBKBNummer')]
    function NeueBKBNummer(BkbTyp: string): string;

    /// <summary>
    /// Gibt alle Mangeltypen für den angegebenen BKBTyp zurück.
    /// </summary>
    /// <param name="BkbTyp">
    /// Der jeweilige BKBTyp
    /// </param>
    [HttpGet]
    [URIPathSegment('mangeltypen')]
    function Mangeltypen(BkbTyp: string): TList<TMangeltyp>;

    /// <summary>
    /// Gibt alle Checklisten für den angegebenen Kontrollbericht zurück
    /// </summary>
    /// <param name="KontrollberichtID">
    /// Die ID des jeweiligen Kontrollberichts
    /// </param>
    /// <remarks>
    /// Wichtig: In dem Fragen-Array jeder Checkliste sind nur die
    /// "Wurzel-Fragen" enthalten. Also nur die Fragen, die keine
    /// übergeordnete Frage haben. Die restlichen Fragen müssen über die
    /// untergeordneten Fragen "ausgefaltet" werden.
    /// </remarks>
    [HttpGet]
    [URIPathSegment('checklisten')]
    function Checklisten(KontrollberichtID: integer): TList<TCheckliste>;

    /// <summary>
    /// Hiermit wird eine laufende Kontrolle beendet
    /// </summary>
    /// <returns>
    /// Id der laufenden Kontrolle.
    /// </returns>
    [HttpPost]
    [URIPathSegment('kontrolleBeenden')]
    function KontrolleBeenden(KontrollErgebnis: TKontrollErgebnis): integer;

    /// <summary>
    /// Trägt die übergebene Probe für den Kontrollbericht in die Datenbank
    /// ein.
    /// </summary>
    /// <param name="KontrollberichtID">
    /// ID der zugehörigen Kontrolle
    /// </param>
    /// <param name="Probe">
    /// Die neue Probe
    /// </param>
    /// <param name="PdfErstellen">
    /// Gibt an, ob direkt der Probenbegleitschein erzeugt werden soll.
    /// </param>
    [HttpPost]
    [URIPathSegment('probeEintragen')]
    function ProbeEintragen(KontrollberichtID: integer; Probe: TKBProbe; PdfErstellen: Boolean = false): TGuid;

    /// <summary>
    /// Erstellt zu einer beendeten Kontrolle die PDF-Berichte, inkl.
    /// Probenbegleitscheine (falls Proben in der Kontrolle vorhanden sind)
    /// und versendet diese per E-Mail.
    /// </summary>
    /// <param name="KontrollberichtID">
    /// Die ID des Kontrollberichts.
    /// </param>
    [HttpGet]
    [URIPathSegment('kontrollberichteErstellen')]
    function KontrollberichteErstellen(KontrollberichtID: integer): TErstellteKontrollberichte;

    /// <summary>
    /// Erstellt für alle beendeten Kontrollen ('G', 'V', 'O', 'S', 'E') , die kein PDF Dokument haben, die PDF-Dateien neu.
    // Es wird auf die regulären und CC Kontrollberichte geschaut. Es werden KEINE Emails verschickt.
    /// </summary>
    [HttpGet]
    [URIPathSegment('kontrollberichteNeuErstellen')]
    function KontrollberichteNeuErstellen: TNeuErstellteKontrollberichte;

    /// <summary>
    /// Schließt einen AMA-CCK Auftrag ab und erstellt dabei das
    /// Bewertungsblatt (PDF).
    /// </summary>
    /// <param name="Id">
    /// Die ID des AMA-CCK Auftrags
    /// </param>
    /// <returns>
    /// Es wird die GUID der PDF-Datei des generierten Bewertungsblatts
    /// zurück gegeben.
    /// </returns>
    [HttpGet]
    [URIPathSegment('AMAAuftragAbschliessen')]
    function AMAAuftragAbschliessen(Id: integer): TGuid;

    /// <summary>
    /// Erstellt das  Bewertungsblatt (PDF) neu.
    /// </summary>
    /// <param name="Id">
    /// Die ID des AMA-CCK Auftrags
    /// </param>
    /// <returns>
    /// Es wird die GUID der PDF-Datei des generierten Bewertungsblatts
    /// zurück gegeben.
    /// </returns>
    [HttpGet]
    [URIPathSegment('AMABewertungsblattErzeugen')]
    function AMABewertungsBlattErzeugen(AId: integer): TGuid;

    /// <summary>
    /// Lädt das angegebene Bild hoch und ordnet es einem Mangel, einer Probe oder einer Bewerteten Frage zu.
    /// </summary>
    /// <param name="Bild">
    /// Base64 enkodierte Bilddaten
    /// </param>
    /// <param name="BildFormat">
    /// Jpeg oder Png
    /// </param>
    /// <param name="Art">
    /// Bewertung, Mangel oder Probe
    /// </param>
    /// <param name="Bemerkung">
    /// Informationen zum Bild
    /// </param>
    /// <param name="GUID">
    /// Die GUID des Elements das durch die Art angegeben wurde. Beispiel {"GUID": "A9FC5F30-9655-44D8-9A2E-CB751DB1AF1C"}
    /// </param>
    /// <param name="GPSLon">
    /// GPS Location - Longitude
    /// </param>
    /// <param name="GPSLat">
    /// GPS Location - Latitude
    /// </param>
    /// <returns>
    /// Die ID des neuen Bildes
    /// </returns>
    [HttpPost]
    [URIPathSegment('bildSenden')]
    function BildSenden(
      Bild: TBytes;
      BildFormat: TBildFormat;
      Art: TBildArt;
      Bemerkung: string;
      GUID: TGuid;
      [XDefault(-1)]GPSLon: Double = -1;
      [XDefault(-1)]GPSLat: Double = -1): TGuid;

    /// <summary>
    /// Dieser Endpunkt liefert ein Dokument anhand der übergebenen
    /// Dokumenten-ID.
    /// </summary>
    [HttpGet]
    [URIPathSegment('dokument')]
    function Dokument(Id: TGuid): TDokument;

    /// <summary>
    /// Dieser Endpunkt liefert ein Dokument als "rohe Datei" (also als Download) anhand der übergebenen
    /// Dokumenten-ID.
    /// </summary>
    [HttpGet]
    [URIPathSegment('dokumentDatei')]
    function DokumentDatei(Id: TGuid): TStream;

    /// <summary>
    /// Dieser Endpunkt liefert alle Personen, die Mitglied in den Gruppen
    /// des Users sind. Inklusive Untergruppen.
    /// </summary>
    [HttpGet]
    [URIPathSegment('gruppenPersonen')]
    function GruppenPersonen: TList<TKontakt>;

    /// <summary>
    /// Erstellt ein Duplikat (deep copy) der angegebenen Checkliste.
    /// </summary>
    /// <param name="CheclistenId">
    /// Die zu duplizierende Checkliste
    /// </param>
    [HttpGet]
    [URIPathSegment('kopiereCheckliste')]
    function KopiereCheckliste(ChecklistenId: integer): TCheckliste;

    /// <summary>
    /// Löscht die angegebene Checkliste. Das Löschen ist möglich, solange es
    /// keine bewerteten Fragen zu der Checkliste gibt.
    /// </summary>
    /// <param name="CheclistenId">
    /// Die zu löschende Checkliste
    /// </param>
    [HttpGet]
    [URIPathSegment('loescheCheckliste')]
    procedure LoescheCheckliste(ChecklistenId: integer);

    /// <summary>
    /// Ergebnisdatenliste des Betriebes passend zum Kontrollbericht Typ
    /// (ACHTUNG ALLE und nicht nur die vom Benutzer durchgeführten !!!!)
    /// </summary>
    /// <param name="BetriebId">
    /// Technische ID des Betriebs
    /// </param>
    /// <param name="BkbTyp">
    /// BKB Typ (HYK, NIEDER, etc.)
    /// </param>
    [HttpGet]
    [URIPathSegment('kontrollenFuerBetrieb')]
    function KontrollenFuerBetrieb(BetriebId: integer; BkbTyp: string): TObjectList<TKontrollberichtKurz>;

    /// <summary>
    /// Alle noch offenen Kontrollen für den angegeben Betrieb - bis zum
    /// Jahresende. <br />Es werden nur Kontrollen gelistet, die der aktuelle
    /// User ach sehen darf.
    /// </summary>
    /// <param name="BetriebId">
    /// Technische ID des Betriebs
    /// </param>
    [HttpGet]
    [URIPathSegment('OffeneKontrollenFuerBetrieb')]
    function OffeneKontrollenFuerBetrieb(BetriebId: integer): TObjectList<TKontrollberichtKurz>;

    /// <summary>
    /// Storniert die angegebene Kontrolle. Als Stornodatum wird automatisch
    /// das aktuelle Datum eingesetzt.
    /// </summary>
    /// <param name="KontrollberichtGUID">
    /// Die GUID der existierenden Kontrolle
    /// </param>
    /// <param name="Stornogrund">
    /// Der Stornierungsgrund
    /// </param>
    [HttpGet]
    [URIPathSegment('kontrolleStornieren')]
    procedure KontrolleStornieren(KontrollberichtGUID: TGuid; Stornogrund: string);

    /// <summary>
    /// Verweigert die angegebene Kontrolle. Es können nur Kontrollen mit
    /// Status "P" verweigert werden.
    /// </summary>
    /// <param name="KontrollVerweigerung">
    /// Die Parameter der Verweigerung.
    /// </param>
    [HttpPost]
    [URIPathSegment('kontrolleVerweigern')]
    procedure KontrolleVerweigern(KontrollVerweigerung: TKontrollVerweigerung);

    /// <summary>
    /// Liefert die Dokumenten-GUID für den PDF-Bericht eine Kontrolle
    /// </summary>
    /// <param name="Bkb">
    /// Die Bkb Nummer der Kontrolle
    /// </param>
    [HttpGet]
    [URIPathSegment('kontrollberichtDokumentFuerBkb')]
    function kontrollberichtDokumentFuerBkb(Bkb: string): TGuid;

    /// <summary>
    /// Liefert die Liste der bekannten Funktionen für Anwesende (Geschäftsführer, etc).
    /// Dies ist soll in TKontrollbericht.Anwesende.FunktionAP eingetragen werden.
    /// "AP" steht für "Anwesende Person"
    /// </summary>
    [HttpGet]
    [URIPathSegment('apTypen')]
    function ApTypen: TList<TAPTyp>;

    /// <summary>
    /// Über diesen Endpunkt wird eine Text Info abgerufen. Der Inhalt wird am Server per SQL-View (vBetriebsInfo) zusammen gestellt und ist universell gestaltet.
    /// </summary>
    /// <param name="BetriebId">
    /// Die ID des Betriebs
    /// </param>
    [HttpGet]
    [URIPathSegment('betriebsInfo')]
    function BetriebsInfo(BetriebId: integer): TBetriebsInfo;

    /// <summary>
    /// Liefert die Liste der Bundesländer für das jeweilige Land
    /// </summary>
    /// <param name="Land">
    /// Länderkürzel. at = Österreich
    /// </param>
    [HttpGet]
    [URIPathSegment('bundeslaender')]
    function bundeslaender(Land: string): TList<TBundesland>;

    /// <summary>
    /// Liefert den jeweiligen Kontrollbericht
    /// </summary>
    /// <param name="Id">
    /// ID des Kontrollberichts
    /// </param>
    /// <remarks>
    /// Es wird geprüft, ob der Kontrollbericht für den User sichtbar ist!
    /// </remarks>
    [HttpGet]
    [URIPathSegment('kontrollbericht')]
    function kontrollbericht(Id: integer): TKontrollbericht; overload;

    /// <summary>
    /// Hier kann der angegebene Kontrollbericht aktualisiert werden. Folgende Änderungen werden übernommen: <br /><br />- Kontrollorgan <br />- Erfasser <br />- Datum <br />- Referenzbericht /
    /// RefBkb <br />- Kontrollgrund / Rechtsgrundlage <br />- AngemeldetUm <br /><br />
    /// </summary>
    /// <param name="Id">
    /// Der komplette Kontrollbericht, der mindestens die ID und die o.a. Felder enthält
    /// </param>
    /// <remarks>
    /// Es wird geprüft, ob der Kontrollbericht für den User sichtbar ist!
    /// </remarks>
    [HttpPost]
    [URIPathSegment('kontrollbericht')]
    procedure kontrollbericht(kontrollbericht: TKontrollbericht); overload;

    /// <summary>
    /// Hier kann der angegebene Kontrollbericht aktualisiert werden. Folgende Änderungen werden übernommen: <br /><br />- Kontrollorgan <br />- Erfasser <br />- Datum <br />- Referenzbericht /
    /// RefBkb <br />- Kontrollgrund / Rechtsgrundlage <br />- AngemeldetUm <br /><br />
    /// </summary>
    /// <param name="Id">
    /// Der komplette Kontrollbericht, der mindestens die ID und die o.a. Felder enthält
    /// </param>
    /// <remarks>
    /// IOdentische Funktion wie <br />procedure kontrollbericht(kontrollbericht: TKontrollbericht); <br />Workaround für Overload Issue in XDataClient <br />Es wird geprüft, ob der Kontrollbericht
    /// für den User sichtbar ist!
    /// </remarks>

    [HttpPost]
    [URIPathSegment('kontrollberichtSpeichern')]
    procedure kontrollberichtSpeichern(kontrollbericht: TKontrollbericht);

    /// <summary>
    ///   Weist die (ungeplante) Kontrolle dem jeweiligen Erfasser zu.
    /// </summary>
    /// <param name="Id">
    ///   ID des Kontrollberichts
    /// </param>
    /// <param name="PersonID">
    ///   ID der Person
    /// </param>
    /// <param name="GruppeID">
    ///   ID der Gruppe, die als QuellGruppe gesetzt werden soll
    /// </param>
    [HttpPost]
    [URIPathSegment('kontrolleZuweisen')]
    function KontrolleZuweisen(
      Id: integer;
      PersonID: integer;
      GruppeID: integer;
      Planungsdatum: TDate;
      AngemeldetUm: TDateTime
      ): TKontrollbericht;

    /// <summary>
    /// Alle User aus den Gruppen des aktuellen Users.
    /// </summary>
    /// <param name="NurDirekteGruppen">
    /// Wenn FALSE dann werden die User aller Untergruppen des aktuellen Users zurück gegeben. Wenn TTRUE dann nur die User aus den direkten Gruppen
    /// </param>
    [HttpGet]
    [URIPathSegment('users')]
    function Users(
      [XDefault(false)]NurDirekteGruppen: Boolean = false): TList<TUser>;

    /// <summary>
    /// Revisionsplan abschließen. Für das angebene Jahr und den jeweiligen Kontrolltyp die Kontrollen aus dem Revisionsplan erstellen.
    /// </summary>
    /// <param name="NurDirekteGruppen">
    /// Wenn FALSE dann werden die User aller Untergruppen des aktuellen Users zurück gegeben. Wenn TTRUE dann nur die User aus den direkten Gruppen
    /// </param>
    [HttpGet]
    [URIPathSegment('revisionsplanAbschliessen')]
    function RevisionsplanAbschliessen(
      Jahr: integer;
      KontrollTyp: string
      ): TList<TRevisionsplan>;

    /// <summary>
    /// Listet die Ansprechpartner/Anwesenden für einen Betrieb aus den vorherigen Kontrollen
    /// </summary>
    [HttpGet]
    [URIPathSegment('ansprechpartner/betrieb/ID/{ID}')]
    function Ansprechpartner(Id: integer): TObjectList<TAnsprechpartnerKontakt>; overload;

    [HttpGet]
    [URIPathSegment('ansprechpartner/betrieb/RegNr/{RegNr}')]
    function Ansprechpartner(const RegNr: string): TObjectList<TAnsprechpartnerKontakt>; overload;
  end;

implementation

initialization

  RegisterServiceType(TypeInfo(IMe));

end.

