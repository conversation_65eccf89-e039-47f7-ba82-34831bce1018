﻿unit ELKE.Classes;

interface

uses
  System.SysUtils, System.Generics.Defaults, Aurelius.Mapping.Attributes,
  Aurelius.Types.Blob,
  Aurelius.Types.DynamicProperties,
  Aurelius.Types.Nullable,
  Aurelius.Types.Proxy,
  Aurelius.Engine.ObjectManager,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  XData.Service.Common, XData.Model.Attributes, XData.Server.Module,
  ELKE.Classes.PVP.Roles, ELKE.Classes.Generated, ELKE.Classes.Generated.Dictionary,
  ELKE.Server.Logger, System.Generics.Collections;

type
  // Hier finden sich einige ergänzende Klassen zum ELKE Modell

  TKontakt = class;
  TAnsprechpartnerKontakt = class;

{$SCOPEDENUMS ON}

  TKBParams = class(TObject)
  private
    FBkbTyp: string;
    FKontrollTyp: string;
  public
    property BkbTyp: string read FBkbTyp write FBkbTyp;
    property KontrollTyp: string read FKontrollTyp write FKontrollTyp;
  end;

  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TFreieAdresse = class(TObject)
  private
    FAdresszusatz: string;
    FLandKZ: string;
    FOrt: string;
    FPLZ: string;
    FStrasse: string;
  public
    property Strasse: string read FStrasse write FStrasse;
    property Adresszusatz: string read FAdresszusatz write FAdresszusatz;
    property Ort: string read FOrt write FOrt;
    property PLZ: string read FPLZ write FPLZ;
    property LandKZ: string read FLandKZ write FLandKZ;
  end;

  TFunktionKurz = class(TObject)
  private
    FBeschriftung: Nullable<string>;
    FId: Integer;
    FPosition: Nullable<Integer>;
    FProgcallid: Nullable<Integer>;
    FSichtbar: Boolean;
  public
    property Id: Integer read FId write FId;
    property Progcallid: Nullable<Integer>read FProgcallid write FProgcallid;
    property Sichtbar: Boolean read FSichtbar write FSichtbar;
    property Position: Nullable<Integer>read FPosition write FPosition;
    property Beschriftung: Nullable<string>read FBeschriftung write FBeschriftung;
  end;

  TNachrichtKurz = class(TObject)
  private
    // FAbsender: TUser;
    /// <summary>
    /// TNachrichtKurz ist eine kompakte Variante von TNachricht, die keine
    /// Verlinkungen enthält, sowie zusätzlich die Zustellung für den
    /// aktuellen User enthält;
    /// </summary>
    FAbsenderKz: string;
    FAbsender: TPerson;
    FGelesen: Nullable<TDateTime>;
    FGesehen: Nullable<TDateTime>;
    // FGruppe: TGruppe;
    FId: Integer;
    FLink: Nullable<string>;
    FPriori: Integer;
    FStatus: string;
    FText: Nullable<string>;
    FTyp: string;

    [Transient]
    [JsonIgnore]
    FOriginalNachricht: TNachricht;

    [Transient]
    [JsonIgnore]
    FForUser: TUser;
    FGesendet: TDateTime;

  public
    class function ComparerDatumDesc: IComparer<TNachrichtKurz>;
  public
    constructor Create(AUser: TUser; ANachrichtForUser: TvNachrichtForUser);
    property Absender: TPerson read FAbsender write FAbsender;
    property AbsenderKz: string read FAbsenderKz write FAbsenderKz;
    property Gesendet: TDateTime read FGesendet write FGesendet;
    property Gelesen: Nullable<TDateTime>read FGelesen;
    property Gesehen: Nullable<TDateTime>read FGesehen;
    property Id: Integer read FId write FId;
    property Link: Nullable<string>read FLink write FLink;
    property OriginalNachricht: TNachricht read FOriginalNachricht;
    property ForUser: TUser read FForUser;
    property Priori: Integer read FPriori write FPriori;
    property Status: string read FStatus write FStatus;
    property Text: Nullable<string>read FText write FText;
    property Typ: string read FTyp write FTyp;
  end;

{$SCOPEDENUMS ON}

  /// <summary>
  /// <para>
  /// Status der Kontrollen wie in der Datenbank definiert.
  /// </para>
  /// <para>
  /// Siehe: <br />BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS <br />und <br />
  /// https://esculenta.atlassian.net/wiki/spaces/ELKE/pages/********/Statusfelder+Kontrollen
  /// </para>
  /// </summary>
  TKontrollStatus = (
    /// <summary>
    /// Storniert
    /// </summary>
    S,
    /// <summary>
    /// Fehlerhaft
    /// </summary>
    E,
    /// <summary>
    /// Ungeplant
    /// </summary>
    U,
    /// <summary>
    /// Geschlossen
    /// </summary>
    G,
    /// <summary>
    /// Geplant
    /// </summary>
    P,
    /// <summary>
    /// Verweigert
    /// </summary>
    V,
    /// <summary>
    /// Läuft
    /// </summary>
    L,
    /// <summary>
    /// Offen
    /// </summary>
    O);

{$SCOPEDENUMS ON}
  TGroupLevel = (OhneGruppen, MitDirektenGruppen, MitAllenUntergruppen);

  TKontrollListenTyp = (Verlauf, Geplant, Ungeplant);
{$SCOPEDENUMS OFF}

  TKontrollStatusConverter = class(TObject)
  public
    class function StatusToString(AStatus: TKontrollStatus): string;
  end;

  TUnterschrift = class(TObject)
  private
    FBild: TBytes;
    FDatum: TDateTime;
    FIDPerson: Nullable<Integer>;
    FName: string;
  public
    /// <summary>
    /// Das Unterschriftsbild (JPEG/PNG)
    /// </summary>
    property Bild: TBytes read FBild write FBild;
    /// <summary>
    /// Datum/Uhrzeit wann unterschrieben wurde.
    /// </summary>
    property Datum: TDateTime read FDatum write FDatum;
    /// <summary>
    /// Name des Unterschreibenden
    /// </summary>
    property Name: string read FName write FName;
    /// <summary>
    /// Falls bekannt, die Personen ID.
    /// </summary>
    property IDPerson: Nullable<Integer>read FIDPerson write FIDPerson;
  end;

  TUnterschriften = class(TObject)
  private
    FAnwesender: TUnterschrift;
    FKontrollorgan: TUnterschrift;
    FVerweigerungsgrund: Nullable<string>;
  public
    /// <summary>
    /// Die Unterschrift des Anwesenden des Betriebs. Diese Unterschrift kann
    /// verweigert werden (dann null).
    /// </summary>
    property Anwesender: TUnterschrift read FAnwesender write FAnwesender;
    /// <summary>
    /// Wenn der Anwesende die Unterschrift verweigert, dann ist hier der
    /// Grund einzutragen.
    /// </summary>
    property Verweigerungsgrund: Nullable<string>read FVerweigerungsgrund write FVerweigerungsgrund;
    /// <summary>
    /// Die Unterschrift des Kontrollorgans.
    /// </summary>
    property Kontrollorgan: TUnterschrift read FKontrollorgan write FKontrollorgan;
  end;

  /// <summary>
  /// Die Klasse Kontrollergebnis repräsentiert bzw. kapselt die bewerteten
  /// Fragen zu einem Kontrollbericht.
  /// </summary>
  TKontrollErgebnis = class(TObject)
  private
    FAngemeldetUm: Nullable<TDateTime>;
    FAnwesende: TList<TAnsprechpartnerKontakt>;
    FBewerteteFragen: TList<TBewerteteFrage>;
    FEndezeit: TDateTime;
    FInterneNotiz: Nullable<string>;
    FKontrollberichtID: Integer;
    FKurzbemerkung: Nullable<string>;
    FMaengel: TList<TMangel>;
    FOertlichkeiten: TList<TKontrollberichtOertlichkeit>;
    FStartzeit: TDateTime;
    FUnterschriften: TUnterschriften;
  public
    constructor Create;
    destructor Destroy; override;
    property KontrollberichtID: Integer read FKontrollberichtID write FKontrollberichtID;
    property BewerteteFragen: TList<TBewerteteFrage>read FBewerteteFragen write FBewerteteFragen;
    property Maengel: TList<TMangel>read FMaengel write FMaengel;
    property Oertlichkeiten: TList<TKontrollberichtOertlichkeit>read FOertlichkeiten write FOertlichkeiten;
    function MangelFuerFrage(AFrageId: Integer): TMangel;

    /// <summary>
    /// Datum und Uhrzeit falls die Kontrolle AngemeldetUm wurde.
    /// </summary>
    property AngemeldetUm: Nullable<TDateTime>read FAngemeldetUm write FAngemeldetUm;

    /// <summary>
    /// Datum und Uhrzeit wan die Kontrolle gestartet wurde.
    /// </summary>
    property Startzeit: TDateTime read FStartzeit write FStartzeit;

    /// <summary>
    /// Datum und Uhrzeit wan die Kontrolle beendet wurde.
    /// </summary>
    property Endezeit: TDateTime read FEndezeit write FEndezeit;

    property Anwesende: TList<TAnsprechpartnerKontakt>read FAnwesende;

    /// <summary>
    /// Die Unterschriften des Berichts inkl. Unterschriftsbild in PNG oder
    /// JPEG Format.
    /// </summary>
    property Unterschriften: TUnterschriften read FUnterschriften write FUnterschriften;

    /// <summary>
    /// Bemerkung zur Kontrolle.
    /// </summary>
    property Kurzbemerkung: Nullable<string>read FKurzbemerkung write FKurzbemerkung;

    /// <summary>
    /// Optionale, interne Notiz zur Kontrolle.
    /// </summary>
    property InterneNotiz: Nullable<string>read FInterneNotiz write FInterneNotiz;
  end;

  /// <summary>
  /// Detailinformationen zu einem Betrieb/Betriebs-ID.
  /// </summary>
  TBetriebsInfo = class(TObject)
  private
    FBetriebID: Integer;
    FBetriebsart: Nullable<string>;
    FBetriebstyp: string;
    FFreierBetrieb: Boolean;
    FTaetigkeiten: Nullable<string>;
    FHauptbetrieb: TBetriebsInfo;
    FRegNr: string;
    FTeilbetriebe: TObjectlist<TBetriebsInfo>;
    FName: string;
    procedure SetName(const Value: string);
    procedure SetTeilbetriebe(const Value: TObjectlist<TBetriebsInfo>);
  public
    constructor Create(ABetrieb: TBetrieb); overload;
    constructor Create(ABetriebsInfo: TVbetriebsinfo); overload;
    destructor Destroy; override;
    property BetriebID: Integer read FBetriebID write FBetriebID;
    property RegNr: string read FRegNr write FRegNr;
    /// <summary>
    /// Zugelassene Tätigkeiten
    /// </summary>
    property Taetigkeiten: Nullable<string>read FTaetigkeiten write FTaetigkeiten;
    /// <summary>
    /// Der Name des Betriebs (Schlachterei Franz Hackebeil, Imst)
    /// </summary>
    property Name: string read FName write SetName;
    /// <summary>
    /// HB : Hauptbetrieb <br />TB : Teilbetrieb
    /// </summary>
    property Betriebstyp: string read FBetriebstyp write FBetriebstyp;
    /// <summary>
    /// Heimgut, ALM etc
    /// </summary>
    property Betriebsart: Nullable<string>read FBetriebsart write FBetriebsart;
    /// <summary>
    /// Der Hauptbetrieb zum aktuellen Betrieb. Wenn der Betrieb selbst bereits Hauptbetrieb ist, dann ist die
    /// Eigenschaft leer (nil).
    /// </summary>
    property Hauptbetrieb: TBetriebsInfo read FHauptbetrieb write FHauptbetrieb;
    /// <summary>
    /// Die Liste der Teilbetriebe.
    /// </summary>
    property Teilbetriebe: TObjectlist<TBetriebsInfo>read FTeilbetriebe write SetTeilbetriebe;

    /// <summary>
    /// Ist dies ein freier Betrieb
    /// </summary>
    property FreierBetrieb: Boolean read FFreierBetrieb write FFreierBetrieb;

  end;

  TPdfDokument = class(TObject)
  private
    FDateiname: string;
    FDokumentGUID: Nullable<TGuid>;
  public
    constructor Create(ADokument: TDokument);
    destructor Destroy; override;
    property DokumentGUID: Nullable<TGuid>read FDokumentGUID write FDokumentGUID;
    property Dateiname: string read FDateiname write FDateiname;
  end;

  /// <summary>
  /// TErstellteKontrollberichte fasst die erstellen PDF-Dokumente zusammen,
  /// die zu einem Kontrollbericht gehören.
  /// </summary>
  TErstellteKontrollberichte = class(TObject)
  private
    FPDFKontrollBericht: TPdfDokument;
    FPDFProbenbegleitscheine: TArray<TPdfDokument>;
  public
    constructor Create(ABericht: TKontrollbericht);
    destructor Destroy; override;
    property PDFKontrollBericht: TPdfDokument read FPDFKontrollBericht write FPDFKontrollBericht;
    property PDFProbenbegleitscheine: TArray<TPdfDokument>read FPDFProbenbegleitscheine write FPDFProbenbegleitscheine;
  end;

  TKontrollPDF = class(TObject)
  private
    FDocumentGUID: TGuid;
    FKontrollID: Integer;
  public
    constructor Create(AKontrollID: Integer; ADokumentGUID: TGuid);
    property KontrollID: Integer read FKontrollID write FKontrollID;
    property DocumentGUID: TGuid read FDocumentGUID write FDocumentGUID;
  end;

  /// <summary>
  /// Fasst die PDF-Dokumente zusammen, neu erstellt wurden.
  /// </summary>
  TNeuErstellteKontrollberichte = class(TObject)
  private
    FKontrollBerichte: TObjectlist<TKontrollPDF>;
    FKontrollBerichteCC: TObjectlist<TKontrollPDF>;
  public
    constructor Create;
    destructor Destroy; override;
    property KontrollBerichte: TObjectlist<TKontrollPDF>read FKontrollBerichte;
    property KontrollBerichteCC: TObjectlist<TKontrollPDF>read FKontrollBerichteCC;
  end;

  [Entity]
  [Automapping]
  [Id('FDokumentId', TIdGenerator.SmartGuid)]
  TAGESErgebnisBericht = class(TObject)
  private
    FDokumentID: TGuid;
    FProbeID: TGuid;
  public
    property ProbeID: TGuid read FProbeID write FProbeID;
    property DokumentID: TGuid read FDokumentID write FDokumentID;
  end;

  /// <summary>
  /// TKontrolle ist eine Hülle für Kontrollberichte, direkt kombiniert mit
  /// den dazu gehörigen Mängeln. Wenn die Kontrolle noch nicht
  /// beendet/durchgeführt wurde, dann ist die Mängelliste leer. Wenn die
  /// Mängelliste bei einer beendeten Kontrolle leer ist, dann gab es keine
  /// Beanstandungen.
  /// </summary>
  [Entity]
  [Automapping]
  [Id('FId', TIdGenerator.SmartGuid)]
  TKontrolle = class(TObject)
  private
    FAGESErgebnisBerichte: TList<TAGESErgebnisBericht>;
    FId: TGuid;
    FBericht: TKontrollbericht;
    FDokumentID: Nullable<TGuid>;
    FMaengel: TList<TMangel>;
    FStatus: string;
  public
    constructor Create(ABericht: TKontrollbericht);
    destructor Destroy; override;
    property Id: TGuid read FId write FId;
    property Bericht: TKontrollbericht read FBericht;
    property Maengel: TList<TMangel>read FMaengel;
    property DokumentID: Nullable<TGuid>read FDokumentID;
    property Status: string read FStatus write FStatus;
    property AGESErgebnisBerichte: TList<TAGESErgebnisBericht>read FAGESErgebnisBerichte;
  end;

  /// <summary>
  /// TUngeplanteKontrolle ist eine Hülle für die Paramter einer neuen ungeplanten Kontrolle.
  /// </summary>
  TUngeplanteKontrolle = class(TObject)
  private
    FBetriebID: Integer;
    FBkbTyp: string;
    FFaellig: TDate;
    FGruppeID: Integer;
    FKontrollTyp: string;
    FTitel: string;
  public
    property KontrollTyp: string read FKontrollTyp write FKontrollTyp;
    property BkbTyp: string read FBkbTyp write FBkbTyp;
    property BetriebID: Integer read FBetriebID write FBetriebID;
    property Faellig: TDate read FFaellig write FFaellig;
    property Titel: string read FTitel write FTitel;
    property GruppeID: Integer read FGruppeID write FGruppeID;
  end;

  /// <summary>
  /// Zusammenfassende Informationen zu einem Kontrollbericht.
  /// </summary>
  TKontrollberichtKurz = class(TObject)
  private
    FBKB: string;
    FFaelligkeitsDatum: Nullable<TDate>;
    FGUIDKontrollberichtPDF: Nullable<TGuid>;
    FId: Integer;
    FKontrollDatum: Nullable<TDate>;
    FKontrollInformationen: Nullable<string>;
    FKontrollTyp: TKontrolltyp;
    FStatus: Nullable<string>;
  public
    property Id: Integer read FId write FId;
    property BKB: string read FBKB write FBKB;
    property KontrollTyp: TKontrolltyp read FKontrollTyp write FKontrollTyp;
    /// <summary>
    /// Das Planungsdatum
    /// </summary>
    property KontrollDatum: Nullable<TDate>read FKontrollDatum write FKontrollDatum;
    /// <summary>
    /// Das Fälligkeitsdatum - wenn vorhanden
    /// </summary>
    property FaelligkeitsDatum: Nullable<TDate>read FFaelligkeitsDatum write FFaelligkeitsDatum;
    property Status: Nullable<string>read FStatus write FStatus;
    /// <summary>
    /// Bei bereits beendeten Kontrollen der Verweis auf die PDF Datei des
    /// Berichts.
    /// </summary>
    property GUIDKontrollberichtPDF: Nullable<TGuid>read FGUIDKontrollberichtPDF write FGUIDKontrollberichtPDF;
    /// <summary>
    /// Um dem Veterinär zusätzliche Information zu einer bestimmten Kontrolle zukommen zu lassen.
    /// </summary>
    property KontrollInformationen: Nullable<string>read FKontrollInformationen write FKontrollInformationen;
  end;

  // Ab hier finden sich Helper Klassen, die generierten Klassen des Modells um bestimmte Funktionalitäten ergänzen

  TNachrichtForUserHelper = class helper for TvNachrichtForUser
  strict private
    function GetZustellung: TNachrichtenZustellung;
  public
    procedure MarkierenAlsGesehen(AUser: TUser; ATimestamp: TDateTime);
    procedure MarkierenAlsGelesen(AUser: TUser);
    property Zustellung: TNachrichtenZustellung read GetZustellung;
  end;

  TKontrollberichtHelper = class helper for TKontrollbericht
  protected
    function GetBeendet: Boolean;
    function GetSort: Integer;
    procedure SetSort(Value: Integer);
  public
    class function ComparerDefault: IComparer<TKontrollbericht>;
  public
    function GetBundesland: TBundesland;
    function istSichtbarFuerUser(User: TUser): Boolean;
    function GetEMailText(AArt: string; ADefault: string): string;

    function ErfasserOderKontrollorganIn(AGruppe: TGruppe): Boolean;

    procedure QuellGruppeValidieren;
    procedure QuellGruppeStandardSetzen;

    property beendet: Boolean read GetBeendet;
    property Bundesland: TBundesland read GetBundesland;
    property Sort: Integer read GetSort write SetSort;
  end;

  TUserHelper = class helper for TUser
  public
    // die erlaubten BKBTypen entsprechend der Rollen des Users
    function ErlaubterBKBTyp(ABkbTyp: TBkbTyp): Boolean;
  end;

  TPersonHelper = class helper for TPerson
  public
    function Fullname: string;

    /// <summary>
    /// Gibt an, ob die Person (bzw. der User) Mitglied der angegebenen Gruppe ist.
    /// </summary>
    /// <param name="AGroup">
    /// Die zu prüfendende Gruppe
    /// </param>
    /// <param name="ADirectMemberOnly">
    /// Nur auf direkte Mitgliedschaft prüfen, keine indirekte Mitgliedschaft über eine übergeordnete Gruppe.
    /// </param>
    function IsMemberOf(AGroup: TGruppe; ADirectMemberOnly: Boolean = false): Boolean;

    /// <summary>
    /// Gibt an, ob die Person (bzw. der User) Mitglied mindestens einer (nicht-persönlichen) Gruppe ist
    /// </summary>
    function IsMemberOfAnyGroup: Boolean;
  end;

  TChecklisteHelper = class helper for TCheckliste
  public
    class function Comparer: IComparer<TCheckliste>;
    function SichtbarFuerBundesland(ABundesland: TBundesland; AKontrolltyp: TKontrolltyp): Boolean;
    function SichtbarUndAktiv(ABundesland: TBundesland; AKontrolltyp: TKontrolltyp): Boolean;
    procedure SortFragenbewertungen;
    procedure SortBewertungen(AFragen: TList<TFrage>);
  end;

  TFrageBewertungHelper = class helper for TFrageBewertung
  private
    type
      TComparerSortierung = class(TInterfacedObject, IComparer<TFrageBewertung>)
      public
        function Compare(const Left, Right: TFrageBewertung): Integer;
      end;
  public
    class function Comparer: IComparer<TFrageBewertung>;
  end;

  TComparerCheckliste = class(TInterfacedObject, IComparer<TCheckliste>)
  public
    /// <summary>
    /// Sortierung für Checklisten <br />Einfach nach Namen <br />
    /// </summary>
    function Compare(const Left, Right: TCheckliste): Integer;
  end;

  TComparerNachrichtKurzDatumDesc = class(TInterfacedObject, IComparer<TNachrichtKurz>)
  public
    function Compare(const Left, Right: TNachrichtKurz): Integer;
  end;

  /// <summary>
  /// Die Klasse Kontrollverweigerung repräsentiert bzw. kapselt die erforderlichen Parameter beim Verweigern einer
  /// Kontrolle
  /// </summary>
  TKontrollVerweigerung = class(TObject)
  private
    FAnwesende: TList<TAnsprechpartnerKontakt>;
    FInterneNotiz: Nullable<string>;
    FKontrollberichtGUID: TGuid;
    FKurzbemerkung: Nullable<string>;
    FStartzeit: TDateTime;
    FUnterschriften: TUnterschriften;
    FVerweigerungsgrund: string;
  public
    constructor Create;
    destructor Destroy; override;
    property KontrollberichtGUID: TGuid read FKontrollberichtGUID write FKontrollberichtGUID;

    /// <summary>
    /// Die anwesenden Betriebsangehörigen
    /// </summary>
    property Anwesende: TList<TAnsprechpartnerKontakt>read FAnwesende;

    /// <summary>
    /// Bemerkung zur Verweigerung.
    /// </summary>
    property Kurzbemerkung: Nullable<string>read FKurzbemerkung write FKurzbemerkung;

    /// <summary>
    /// Interne Notiz zur Verweigerung.
    /// </summary>
    property InterneNotiz: Nullable<string>read FInterneNotiz write FInterneNotiz;

    /// <summary>
    /// Die Startzeit - also wann der Kontrolleur den Betrieb betreten hat, bzw. die Kontrolle beginnen wollte.
    /// </summary>
    property Startzeit: TDateTime read FStartzeit write FStartzeit;

    /// <summary>
    /// Die Unterschriften des Berichts inkl. Unterschriftsbild in PNG oder
    /// JPEG Format.
    /// </summary>
    property Unterschriften: TUnterschriften read FUnterschriften write FUnterschriften;

    /// <summary>
    /// Der Verweigerungsgrund.
    /// </summary>
    property Verweigerungsgrund: string read FVerweigerungsgrund write FVerweigerungsgrund;

  end;

  TBetriebHelper = class helper for TBetrieb
  protected
    function GetGruppeGemeinde: TGruppe;
    function GetGruppeBezirk: TGruppe;
    function GetGruppeBetrieb: TGruppe;
  public
    property GruppeGemeinde: TGruppe read GetGruppeGemeinde;
    property GruppeBezirk: TGruppe read GetGruppeBezirk;
    property GruppeBetrieb: TGruppe read GetGruppeBetrieb;
  end;

  TBetriebsInfoHelper = class helper for TVbetriebsinfo
  public
    function GetFreierBetrieb: Boolean;
    property FreierBetrieb: Boolean read GetFreierBetrieb;
  end;

  /// <summary>
  /// Vereinfachte Version einer TPerson
  /// </summary>
  TKontakt = class(TObject)
  private
    FAnrede: Nullable<string>;
    FEMail: Nullable<string>;
    FGruppe: TGruppe;
    FNachname: string;
    FPersonID: Nullable<Integer>;
    FTelefon: Nullable<string>;
    FTitel: Nullable<string>;
    FVorname: string;
  protected
    function GetFullname: string;
  public
    constructor Create(APerson: TPerson; AGruppe: TGruppe = nil);
    property Anrede: Nullable<string>read FAnrede write FAnrede;
    property Titel: Nullable<string>read FTitel write FTitel;
    property Vorname: string read FVorname write FVorname;
    property Nachname: string read FNachname write FNachname;
    /// <summary>
    /// Read Only!
    /// </summary>
    [JsonProperty('Fullname')]
    property Fullname: string read GetFullname;

    [JsonProperty('Email')]
    property EMail: Nullable<string>read FEMail write FEMail;
    property Telefon: Nullable<string>read FTelefon write FTelefon;
    /// <summary>
    /// Die ID des Kontakts in der Tabelle Stammdaten.Personen <br />Kann leer sein, wenn von Moped geliefert und die Person noch unbekannt ist.
    /// </summary>
    property PersonID: Nullable<Integer>read FPersonID write FPersonID;

    property Gruppe: TGruppe read FGruppe write FGruppe;
  end;

  TAnsprechpartnerKontakt = class(TKontakt)
  private
    FGuid: TGuid;
    FName: string;
    FKommunikationsberechtigt: Boolean;
    FKontrollbericht: TKontrollbericht;
    FAPTyp: TAPTyp;
  public
    constructor Create(APerson: TPerson);
    property Guid: TGuid read FGuid write FGuid;
    property Name: string read FName write FName;
    property Kommunikationsberechtigt: Boolean read FKommunikationsberechtigt write FKommunikationsberechtigt;
    property Kontrollbericht: TKontrollbericht read FKontrollbericht write FKontrollbericht;
    property APTyp: TAPTyp read FAPTyp write FAPTyp;
  end;

  /// <summary>
  /// TBildArt wird beim Hochlanden von Bildern (/me/BildSenden) verwendet,
  /// um identifizieren zu können, ob das jeweilige Bild einer Probe, einem
  /// Mangel oder einer Bewertung (BewerteteFrage) zugeordnet werde soll.
  /// </summary>
  TBildArt = (Bewertung, Mangel, Probe);

  TBildFormat = (JPEG, PNG);

function ToDate(const ADateTime: Nullable<TDateTime>): Nullable<TDate>;

function Context: TObjectManager;

implementation

uses
  DX.Classes.Collections, System.DateUtils, System.TypInfo, XData.Sys.Exceptions, ELKE.Services.Me.Intf;

function ToDate(const ADateTime: Nullable<TDateTime>): Nullable<TDate>;
begin
  if ADateTime.IsNull then
  begin
    result := SNull;
  end
  else
  begin
    result.Value := TDate(ADateTime.Value);
  end;
end;

function Context: TObjectManager;
begin
  result := TXDataOperationContext.Current.GetManager;
end;

{ TVNachrichtForUser }

procedure TNachrichtForUserHelper.MarkierenAlsGelesen(AUser: TUser);
begin
  var
  LZustellung := GetZustellung;
  if LZustellung.GelesenTstamp.IsNull or (LZustellung.GelesenTstamp = 0) then
  begin
    LZustellung.GelesenTstamp := now;
  end;
end;

procedure TNachrichtForUserHelper.MarkierenAlsGesehen(
  AUser: TUser;
  ATimestamp: TDateTime);
begin
  var
  LZustellung := GetZustellung;
  if LZustellung.GesehenTstamp.IsNull or (LZustellung.GesehenTstamp = 0) then
  begin
    LZustellung.GesehenTstamp := ATimestamp;
  end;
end;

function TNachrichtForUserHelper.GetZustellung: TNachrichtenZustellung;
begin
  if self.NachrichtenZustellung = nil then
  begin
    self.NachrichtenZustellung := TNachrichtenZustellung.Create;
    self.NachrichtenZustellung.Nachricht := self.Nachricht;
    Context.SaveOrUpdate(self);
  end;

  result := self.NachrichtenZustellung;
end;

{ TNachrichtKurz }

constructor TNachrichtKurz.Create(AUser: TUser; ANachrichtForUser: TvNachrichtForUser);
begin
  FOriginalNachricht := ANachrichtForUser.Nachricht;
  FForUser := AUser;

  // Alle relevanten Werte kopieren
  FAbsenderKz := FOriginalNachricht.AbsenderKz;
  if (FOriginalNachricht.Absender <> nil) then
  begin
    FAbsender := FOriginalNachricht.Absender.Person;
  end;
  FId := FOriginalNachricht.Id;
  FLink := FOriginalNachricht.Link;
  FPriori := FOriginalNachricht.Priori;
  FStatus := FOriginalNachricht.Status;
  FText := FOriginalNachricht.Text;
  FTyp := FOriginalNachricht.Typ;
  FGesendet := FOriginalNachricht.GesendetAm;
  FGelesen := ANachrichtForUser.GelesenTstamp;
  FGesehen := ANachrichtForUser.GesehenTstamp;
end;

class function TNachrichtKurz.ComparerDatumDesc: IComparer<TNachrichtKurz>;
begin
  result := TComparerNachrichtKurzDatumDesc.Create;
end;

{ TKontrollberichtHelper }
class function TKontrollberichtHelper.ComparerDefault:
  IComparer<TKontrollbericht>;
begin
  result := TDelegatedComparer<TKontrollbericht>.Create(
    function(const Left, Right: TKontrollbericht): Integer
    begin
      // IComparer:  Wenn Links größer als Rechts, dann result = 1
      if Left.Sort = Right.Sort then
        result := 0
      else if Left.Sort > Right.Sort then
        result := 1
      else
        result := -1;
    end
    );
end;

function TKontrollberichtHelper.GetBeendet: Boolean;
begin
  result := Endezeit.ValueOrDefault > 0;
end;

function TKontrollberichtHelper.GetBundesland: TBundesland;
begin
  if Assigned(self.Kontrollorgan) then
  begin
    result := Kontrollorgan.Users.First.Bundesland;
  end
  else if Assigned(self.Erfasser) then
  begin
    result := Erfasser.Users.First.Bundesland;
  end
  else
  begin
    result := self.Betrieb.Bundesland;
  end;
  if result = nil then
    raise EXDataHttpException.Create(495, 'Dem Bericht konnte kein Bundesland zugeordnet werden!');

end;

function TKontrollberichtHelper.GetEMailText(AArt: string; ADefault: string): string;
begin
  result := ADefault;
  // Todo: Default in die DB schreiben
  for var LText in Betrieb.Bundesland.EmailTexte do
  begin
    if LText.Art.Art = AArt then
    begin
      result := LText.Text;
      break;
    end;
  end;
end;

function TKontrollberichtHelper.istSichtbarFuerUser(User: TUser): Boolean;
begin
  // Nur sichtbar, wenn der BKBTyp in den Rollen des Users enthalten ist
  result := User.ErlaubterBKBTyp(self.KontrollTyp.BkbTyp);
end;

function TKontrollberichtHelper.ErfasserOderKontrollorganIn(AGruppe: TGruppe):
  Boolean;
begin
  if Assigned(Kontrollorgan) then
  begin
    result := Kontrollorgan.IsMemberOf(AGruppe);
  end
  else if Assigned(Erfasser) then
  begin
    result := Erfasser.IsMemberOf(AGruppe);
  end
    // Kein Erfasser/Kontrollorgan gesetzt, dann wird nicht geprüft!
  else
    result := true;
end;

function TKontrollberichtHelper.GetSort: Integer;
begin
  result := FSort;
end;

procedure TKontrollberichtHelper.QuellGruppeStandardSetzen;
begin
  // Standard ist die Bezirksgruppe die zum Betrieb gehört.
  // Wir gehen hier explizit über Find<>, damit dies auch mit Betrieb-Instanzen funktioniert, die noch nicht
  // persistiert sind
  var
  LGruppe: TGruppe := nil;
  // Wir nehmen die Bezirksgruppe die zum Betrieb gehört
  if Assigned(Betrieb) then
  begin
    var
    LBezirksGruppen := Context.Find<TVbetriebbezirksgruppe>
      .Where(M.Vbetriebbezirksgruppe.Betrieb.Id = Betrieb.Id).List;
    if LBezirksGruppen.Count > 0 then
    begin
      LGruppe := LBezirksGruppen.First.Gruppe;
    end;
  end;
  if LGruppe = nil then
    // Wenn es keine Betrieb/Bezirksgruppe gibt (freie Adresse z.B.), dann MUSS eine Gruppe mitgegeben werden
    raise EXDataHttpException.Create(491, 'Es konnte keine Quellgruppe zugeordnet werden!');
  GruppeQuelle := LGruppe;
end;

procedure TKontrollberichtHelper.QuellGruppeValidieren;
begin
  if GruppeQuelle = nil then
  begin
    QuellGruppeStandardSetzen;
  end
  else if not ErfasserOderKontrollorganIn(GruppeQuelle) then
  begin
    QuellGruppeStandardSetzen;
  end
  else if GruppeQuelle.Persoenlich then
  begin
    QuellGruppeStandardSetzen;
  end;

  // Bundesland muss zur Quellgruppe passen
  // if Assigned(self.Bundesland) then
  begin
    if self.Bundesland.Bldcode <> GruppeQuelle.Bundesland.Bldcode then
      raise EXDataHttpException.Create(490, 'Bundeslandzuordnung des Kontrollberichts ist nicht konsistent!');
  end;
end;

procedure TKontrollberichtHelper.SetSort(Value: Integer);
begin
  FSort := Value;
end;

{ TUserHelper }

function TUserHelper.ErlaubterBKBTyp(ABkbTyp: TBkbTyp): Boolean;
var
  LErlaubteTypen: TList<TBkbTyp>;
  LIsAdmin: Boolean;
begin
  LIsAdmin := false;
  LErlaubteTypen := TList<TBkbTyp>.Create;
  try
    for var LUserRolle in self.Userrollen do
    begin
      if LUserRolle.Rolle.Bezeichnung.ToUpper = 'ELKE-APPADMIN' then
      begin
        LIsAdmin := true;
        break;
      end
      else
      begin
        for var LRolleBkbTyp in LUserRolle.Rolle.RollenBkbtypen do
        begin
          LErlaubteTypen.Add(LRolleBkbTyp.BkbTyp);
        end;
      end;
    end;
    result := LIsAdmin or (LErlaubteTypen.IndexOf(ABkbTyp) >= 0);
  finally
    FreeAndNil(LErlaubteTypen);
  end;
end;

constructor TKontrollErgebnis.Create;
begin
  inherited;
  FBewerteteFragen := TList<TBewerteteFrage>.Create;
  FMaengel := TList<TMangel>.Create;
  FAnwesende := TList<TAnsprechpartnerKontakt>.Create;
end;

destructor TKontrollErgebnis.Destroy;
begin
  FreeAndNil(FAnwesende);
  FreeAndNil(FBewerteteFragen);
  FreeAndNil(FMaengel);
  inherited;
end;

function TKontrollErgebnis.MangelFuerFrage(AFrageId: Integer): TMangel;
begin
  // Ein Mangel kann mehreren Fragen zugeordnet sein. Daher durchsuchen wir alle Mängel, ob dort die Frage mit AFrageID
  // referenziert wird
  result := nil;
  for var LMangel in Maengel do
  begin
    for var LFrage in LMangel.BewerteteFragen do
    begin
      if LFrage.Id = AFrageId then
      begin
        result := LMangel;
        break
      end;
    end;
    if Assigned(result) then
      break;
  end;
end;

constructor TKontrolle.Create(ABericht: TKontrollbericht);
begin
  //Mängel
  //Dokument
  //BewerteteFragen
  //BewerteteFragen.Mangel
  //Proben
  //Probe.AgesErgebnisbericht

  inherited Create;
  Id := ABericht.Guid;
  FBericht := ABericht;
  FStatus := ABericht.Status;
  FMaengel := TList<TMangel>.Create;
  if ABericht.Dokument <> nil then
  begin
    FDokumentID := ABericht.Dokument.Guid;
  end;
  for var LBewerteteFrage in FBericht.BewerteteFragen do
  begin
    if LBewerteteFrage.Mangel <> nil then
    begin
      FMaengel.Add(LBewerteteFrage.Mangel);
    end;
  end;
  TListHelper<TMangel>.RemoveDuplicates(FMaengel);
  FAGESErgebnisBerichte := TObjectlist<TAGESErgebnisBericht>.Create;
  for var LProbe in ABericht.Proben do
  begin
    if LProbe.AgesErgebnisbericht <> nil then
    begin
      var
      LErgebnisbericht := TAGESErgebnisBericht.Create;
      LErgebnisbericht.ProbeID := LProbe.Guid;
      LErgebnisbericht.DokumentID := LProbe.AgesErgebnisbericht.Guid;
      AGESErgebnisBerichte.Add(LErgebnisbericht);
    end;
  end;
end;

destructor TKontrolle.Destroy;
begin
  FreeAndNil(FAGESErgebnisBerichte);
  FreeAndNil(FMaengel);
  inherited;
end;

{ TPersonHelper }

function TPersonHelper.Fullname: string;
begin
  result := Format('%s %s', [self.Vorname, self.Nachname]).Trim;
end;

function TPersonHelper.IsMemberOf(AGroup: TGruppe; ADirectMemberOnly: Boolean = false): Boolean;
begin
  if not Assigned(AGroup) then
    raise EXDataHttpException.Create(404, 'Keine Gruppe angegeben');

  var LLevel := 100;
  if ADirectMemberOnly then
    LLevel := 1;
  var LUserGroups := Context.Find<TVUserGroupMembership>
  .Where(M.VUserGroupMembership.User.Id = self.Users.First.Id)
    .Where(M.VUserGroupMembership.Gruppe.Id = AGroup.Id)
    .Where(M.VUserGroupMembership.MembershipLevel <= LLevel).List;
  try
    result := LUserGroups.Count > 0;
  finally
    FreeAndNil(LUserGroups);
  end;
end;

function TPersonHelper.IsMemberOfAnyGroup: Boolean;
begin
  result := false;
  var
  LUserGroups := Context.Find<TVUserGroupMembership>
    .Where(M.VUserGroupMembership.User.Id = self.Users.First.Id)
    .Where(M.VUserGroupMembership.MembershipLevel = 1).List; // Nur direkte Gruppen machen Sinn/müssen geprüft werden
  try
    for var LGruppe in LUserGroups do
    begin
      // Die erste nicht-persönliche reicht
      if not LGruppe.Gruppe.Persoenlich then
      begin
        result := true;
        break;
      end;
    end;
  finally
    FreeAndNil(LUserGroups);
  end;
end;

{ TChecklisteHelper }

class function TChecklisteHelper.Comparer: IComparer<TCheckliste>;
begin
  result := TComparerCheckliste.Create;
end;

{ TChecklisteHelper }

function TChecklisteHelper.SichtbarFuerBundesland(ABundesland: TBundesland; AKontrolltyp: TKontrolltyp): Boolean;
begin
  result := false;
  // Die Checkliste ist für das Bundesland sichtbar, wenn in den sichtbaren Checklisten
  // (Bundesländer-Checklisten-Kontrolltypen) enthalten und dort für das Bundesland und den Kontrolltyp eingetragen
  for var LSichtbareCheckliste in ABundesland.SichtbareChecklisten do
  begin
    if LSichtbareCheckliste.ChecklisteKontrolltyp.Checkliste.Id = self.Id then
    begin
      // Zunächst muss der Kontrolltyp auch für das Bundesland freigegeben sein
      result := (LSichtbareCheckliste.ChecklisteKontrolltyp.KontrollTyp.KontrollTyp = AKontrolltyp.KontrollTyp)
        and (LSichtbareCheckliste.ChecklisteKontrolltyp.KontrollTyp.BkbTyp.Typ = AKontrolltyp.BkbTyp.Typ);
      if LSichtbareCheckliste.GueltigAb.HasValue then
      begin
        result := result and (Trunc(LSichtbareCheckliste.GueltigAb.Value) <= today)
      end;

      if LSichtbareCheckliste.GueltigBis.HasValue then
      begin
        result := result and (Trunc(LSichtbareCheckliste.GueltigBis.Value) >= today)
      end;
      if result then
        break;
    end;
  end;
end;

function TChecklisteHelper.SichtbarUndAktiv(ABundesland: TBundesland; AKontrolltyp: TKontrolltyp): Boolean;
begin
  // Wenn Besitzer, dann wird die Gültigkeit der Checkliste direkt geprüft.
  if self.BesitzerBundesland.Bldcode = ABundesland.Bldcode then
  begin
    result := true;
    if GueltigAb.HasValue then
    begin
      result := result and (Trunc(GueltigAb.Value) <= today)
    end;

    if GueltigBis.HasValue then
    begin
      result := result and (Trunc(GueltigBis.Value) >= today)
    end;
  end
    // Wenn nicht Besitzer, dann wird geprüft, ob für das Bundesland sichtbar und dort gültig.
  else
    result := SichtbarFuerBundesland(ABundesland, AKontrolltyp);
end;

procedure TChecklisteHelper.SortBewertungen(AFragen: TList<TFrage>);
begin
  if AFragen <> nil then
  begin
    for var LFrage in AFragen do
    begin
      // Rekursiv die untergeordneten Fragen sortieren!
      SortBewertungen(LFrage.UntergeordneteFragen);
      LFrage.FragenBewertungen.Sort(TFrageBewertung.Comparer);
    end;
  end;
end;

procedure TChecklisteHelper.SortFragenbewertungen;
begin
  SortBewertungen(Fragen);
end;

{ TPdfDokument }

constructor TPdfDokument.Create(ADokument: TDokument);
begin
  inherited Create;
  FDokumentGUID := ADokument.Guid;
  FDateiname := ADokument.Dateiname;
end;

destructor TPdfDokument.Destroy;
begin
  //
  inherited;
end;

{ TErstellteKontrollberichte }

constructor TErstellteKontrollberichte.Create(ABericht: TKontrollbericht);
begin
  inherited Create;
  FPDFKontrollBericht := TPdfDokument.Create(ABericht.Dokument);
  if ABericht.Proben <> nil then
  begin
    SetLength(FPDFProbenbegleitscheine, ABericht.Proben.Count);
    var
    i := 0;
    for var LProbe in ABericht.Proben do
    begin
      if LProbe.Dokument <> nil then
      begin
        FPDFProbenbegleitscheine[i] := TPdfDokument.Create(LProbe.Dokument);
      end;
      inc(i);
    end;
  end;
end;

destructor TErstellteKontrollberichte.Destroy;
begin
  // In Klassen die per XData als result einer Service Funktion raus gehen, nichts frei geben!
  // Das wird automatisch von XData über managed Objects gehandelt!
  (*
    for var LBegleitschein in FPDFProbenbegleitscheine do
    begin
    LBegleitschein.Free;
    end;
    FreeAndNil(FPDFKontrollBericht);
  *)
  inherited;
end;

{ TFrageBewertungHelper }

class function TFrageBewertungHelper.Comparer: IComparer<TFrageBewertung>;
begin
  result := TComparerSortierung.Create;
end;

{ TFrageBewertungHelper.TComparerSortierung }

function TFrageBewertungHelper.TComparerSortierung.Compare(const Left, Right: TFrageBewertung): Integer;
begin
  if Left.Sortierung < Right.Sortierung then
    result := -1
  else if Left.Sortierung = Right.Sortierung then
    result := 0
  else
    result := 1;
end;

class function TKontrollStatusConverter.StatusToString(AStatus: TKontrollStatus): string;
begin
  result := GetEnumName(TypeInfo(TKontrollStatus), Integer(AStatus)).ToUpper;
end;

{ TKontrollVerweigerung }

constructor TKontrollVerweigerung.Create;
begin
  inherited;
  FAnwesende := TList<TAnsprechpartnerKontakt>.Create;
end;

destructor TKontrollVerweigerung.Destroy;
begin
  FreeAndNil(FAnwesende);
  inherited;
end;

{ TKontrollPDF }

constructor TKontrollPDF.Create(AKontrollID: Integer; ADokumentGUID: TGuid);
begin
  FKontrollID := AKontrollID;
  FDocumentGUID := ADokumentGUID;
end;

{ TNeuErstellteKontrollberichte }

constructor TNeuErstellteKontrollberichte.Create;
begin
  FKontrollBerichte := TObjectlist<TKontrollPDF>.Create;
  FKontrollBerichteCC := TObjectlist<TKontrollPDF>.Create;
end;

destructor TNeuErstellteKontrollberichte.Destroy;
begin
  FreeAndNil(FKontrollBerichte);
  FreeAndNil(FKontrollBerichteCC);
  inherited;
end;

{ TComparerCheckliste }

function TComparerCheckliste.Compare(const Left, Right: TCheckliste): Integer;
begin
  if Left.Bezeichnung = Right.Bezeichnung then
    result := 0
  else if Left.Bezeichnung < Right.Bezeichnung then
    result := -1
  else
    result := 1;
end;

function TComparerNachrichtKurzDatumDesc.Compare(const Left, Right: TNachrichtKurz): Integer;
begin
  if Left.Gesendet = Right.Gesendet then
    result := 0
  else if Left.Gesendet > Right.Gesendet then
    result := -1
  else
    result := 1;
end;

function TBetriebHelper.GetGruppeBezirk: TGruppe;
begin
  result := nil;
  if Assigned(self.Adresse) and Assigned(self.Adresse.Gemeinde) then
  begin
    // Bezirk/BH: OKZ = erste drei Stellen des Gemeindecodes
    // Es sollte genau eine Gruppe existieren. Zur sicherheit machen wir eine Liste und
    // nehmen den ersten Eintrag

    if Length(self.Adresse.Gemeinde.Gemeindecode.ToString) <> 5 then
      raise Exception.Create('Die OKZ (Gemeindecode) muss 5-stellig sein, um die Bezirksgruppe zu berechnen');
    var
    LCode := self.Adresse.Gemeinde.Gemeindecode div 100;
    var
    LGruppen := Context.Find<TGruppe>
      .Where(M.Gruppe.Okz = LCode).List;
    try
      if LGruppen.Count > 0 then
      begin
        result := LGruppen.First;
      end;
    finally
      FreeAndNil(LGruppen);
    end;
  end;
end;

{ TBetriebHelper }

function TBetriebHelper.GetGruppeGemeinde: TGruppe;
begin
  result := nil;
  if Assigned(self.Adresse) and Assigned(self.Adresse.Gemeinde) then
  begin
    // OKZ (5-stellig) = Gemeindecode
    if Length(self.Adresse.Gemeinde.Gemeindecode.ToString) <> 5 then
      raise Exception.Create('Die OKZ (Gemeindecode) muss 5-stellig sein, um eine Gemeindegruppe zu berchnen');

    // Es sollte genau eine Gruppe existieren. Zur sicherheit machen wir eine Liste und
    // nehmen den ersten Eintrag
    var
    LGruppen := Context.Find<TGruppe>
      .Where(M.Gruppe.Okz = self.Adresse.Gemeinde.Gemeindecode).List;
    try
      if LGruppen.Count > 0 then
      begin
        result := LGruppen.First;
      end;
    finally
      FreeAndNil(LGruppen);
    end;
  end;
end;

function TBetriebHelper.GetGruppeBetrieb: TGruppe;
begin
  result := nil;
  if Assigned(self.Registrierung) then
  begin
    // OKZ = RegNr des Betriebs
    // Es sollte genau eine Gruppe existieren. Zur sicherheit machen wir eine Liste und
    // nehmen den ersten Eintrag
    var
    LGruppen := Context.Find<TGruppe>
      .Where(M.Gruppe.Okz = self.Registrierung.RegNr).List;
    try
      if LGruppen.Count = 0 then
        raise Exception.CreateFmt
          ('Für die RegNr (%s) des Betriebs konnte keine Gruppe mit entsprechender OKZ gefunden werden',
          [self.Registrierung.RegNr]);

      result := LGruppen.First;
    finally
      FreeAndNil(LGruppen);
    end;
  end;

end;

constructor TKontakt.Create(APerson: TPerson; AGruppe: TGruppe = nil);
begin
  inherited Create;
  Assert(Assigned(APerson), 'TKontakt.Create APerson is nil');
  FPersonID := APerson.Id;
  FVorname := APerson.Vorname;
  FNachname := APerson.Nachname;
  FAnrede := APerson.Anrede;
  FTitel := APerson.Titel;
  FEMail := APerson.EMail;
  FTelefon := APerson.Telefon;
  FGruppe := AGruppe;
end;

function TKontakt.GetFullname: string;
begin
  result := Format('%s %s %s %s', [FAnrede.ValueOrDefault, FTitel.ValueOrDefault, FVorname, FNachname])
    .Trim.Replace('  ', ' ');
end;

constructor TAnsprechpartnerKontakt.Create(APerson: TPerson);
begin
  inherited Create(APerson);
  FName := Fullname;
end;

{ TBetriebsInfo }

constructor TBetriebsInfo.Create(ABetrieb: TBetrieb);
begin
  inherited Create;
  FHauptbetrieb := nil;
  FTeilbetriebe := TObjectlist<TBetriebsInfo>.Create;

  FBetriebID := ABetrieb.Id;

  var
  LBetriebsInfo := Context.Find<TVbetriebsinfo>
    .Where(M.Vbetriebsinfo.BetriebsId = FBetriebID).UniqueResult;

  if not Assigned(LBetriebsInfo) then
    raise EXDataHttpException.Create(404, 'Betriebsinfo nicht gefunden!');

  FName := LBetriebsInfo.Name;
  FBetriebstyp := LBetriebsInfo.Betriebstyp;
  FBetriebsart := LBetriebsInfo.Betriebsart;
  FRegNr := LBetriebsInfo.RegNr;
  FFreierBetrieb := LBetriebsInfo.FreierBetrieb;

  if not (LBetriebsInfo.FreierBetrieb) then
  begin
    if LBetriebsInfo.IdHauptbetrieb.HasValue then
    begin
      var
      LHauptbetrieb := Context.Find<TBetrieb>
        .Where(M.Betrieb.Id = LBetriebsInfo.IdHauptbetrieb).UniqueResult;
      // Nur wen ein anderer Betrieb der Hauptbetrieb ist.
      // In der DB sind Betriebe, die sich selbst als Hauptbetrieb haben...
      if LBetriebsInfo.BetriebsId <> LHauptbetrieb.Id then
      begin
        FHauptbetrieb := TBetriebsInfo.Create(LHauptbetrieb);
      end;
    end;

    var
    LTeilbetriebe := Context.Find<TVbetriebsinfo>
      .Where(M.Vbetriebsinfo.IdHauptbetrieb = FBetriebID).List;

    for var LTeilbetrieb in LTeilbetriebe do
    begin
      if LBetriebsInfo.BetriebsId <> LTeilbetrieb.BetriebsId then
      begin
        var
        LTeilbetriebsinfo := TBetriebsInfo.Create(LTeilbetrieb);
        FTeilbetriebe.Add(LTeilbetriebsinfo);
      end;
    end;
  end;

  FTaetigkeiten := LBetriebsInfo.Taetigkeiten;
end;

constructor TBetriebsInfo.Create(ABetriebsInfo: TVbetriebsinfo);
begin
  var
  LBetrieb := Context.Find<TBetrieb>(ABetriebsInfo.BetriebsId);
  Create(LBetrieb);
end;

destructor TBetriebsInfo.Destroy;
begin
  FreeAndNil(FTeilbetriebe);
  inherited;
end;

procedure TBetriebsInfo.SetName(const Value: string);
begin
  FName := Value;
end;

procedure TBetriebsInfo.SetTeilbetriebe(const Value: TObjectlist<TBetriebsInfo>);
begin
  FTeilbetriebe := Value;
end;

function TBetriebsInfoHelper.GetFreierBetrieb: Boolean;
begin
  result := RegNr.Trim = FREIER_BETRIEB;
  // Todo: theoretisch müsste man noch in den Registrierungen schauen, ob dort FreierBetrieb = true ist.
  // Aktuell ist aber die REGNR = 9999999 eindeutig
end;

end.

