unit ELKE.Classes.Admin;

interface

uses
  System.Classes, System.SysUtils,
    Bcl.Json.Attributes, Bcl.Json.NamingStrategies;

type
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TVersionInfo = class(TObject)
  private
    FBuildTimestamp: TDateTime;
    FBuildVersion: string;
    FProductVersion: string;
    FVersion: string;
  public
    Constructor Create;
    property Version: string read FVersion write FVersion;
    property BuildVersion: string read FBuildVersion write FBuildVersion;
    property ProductVersion: string read FProductVersion write FProductVersion;
    property BuildTimestamp: TDateTime read FBuildTimestamp write FBuildTimestamp;
  end;

implementation

uses
  DX.Utils.Windows;

{ TVersionInfo }

constructor TVersionInfo.Create;
begin
  inherited;
  //"Version" ist die "einfache" Versionsnummer
  FVersion := GetExeVersionData.ProductVersion;
  FBuildVersion := GetExeVersion;
  FProductVersion := GetExeVersionData.ProductVersion;
  FBuildTimestamp := GetExeBuildTimestamp;
end;

end.
