unit ELKE.Services.Me.Impl;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Defaults, System.Generics.Collections,
  Bcl.Collections,
  Aurelius.Criteria.Linq, Aurelius.Mapping.Attributes, Aurelius.Engine.ObjectManager,
  Aurelius.Mapping.Explorer, Aurelius.Events.Manager, Aurelius.Criteria.Projections,
  XData.Server.Module, XData.Service.Common, XData.Sys.Exceptions,
  ELKE.Classes, ELKE.Classes.Generated, ELKE.Classes.Request, ELKE.Services.Me.Intf,
  ELKE.Classes.PVP.Token, Sparkle.HttpServer.Request, ELKE.Classes.PVP.Roles, ELKE.Server.Modules.Main;

type

  [ServiceImplementation]
  TMe = class(TInterfacedObject, IMe)
  private
    FPVPToken: TPVPToken;
    FUser: TUser;
    FModulInstanz: TModulInstanz;
    FGruppen: TList<TGruppe>;

    FDMMain: TDMMain;

    function GetContext: TObjectManager;
    function GetManagedObjects: Bcl.Collections.THashSet<TObject>;
  protected

    // Gleicht die Rollen aus dem Header mit der Datenbank ab und trägt ggfs. neue Rollen ein
    procedure UpdateRollen;
    function NachrichtenForUser: TList<TNachricht>;

    /// <summary>
    /// Legt den im PVPToken spezifizierten User in der Datenbank an.
    /// </summary>
    procedure AddUserFromToken;

    /// <summary>
    /// Updated/prüft den im PVPToken spezifizierten User in der Datenbank.
    /// </summary>
    procedure UpdateUserFromToken(
      LUser: TUser;
      AIsNewUser: Boolean);

    /// <summary>
    /// Interne Funktion, die das PVPToken generiert/holt.
    /// </summary>
    function PVPToken: TPVPToken;

    /// <summary>
    /// Interne Funktion, die den aktuellen User holt.
    /// </summary>
    function GetUser: TUser;

    function KontrollenForUserNeu(AUser: TUser; AStatus: string; ALevel: TGroupLevel; DatumVon: TDateTime = -1;
      DatumBis:
      TDateTime = -1; Page: integer = 1; PageSize: integer = MaxInt; AFetchDetails: Boolean = false;
      AAutoExpand: Boolean = true; AIncludeTodos: Boolean = true): TList<TKontrollbericht>; overload;

    function NeuerKontrollBerichtInternal(Bericht: TKontrollbericht;
      AGeplant: Boolean;
      ANacherfassung: Boolean = false): integer;

    /// <summary>
    /// Gibt die aktuelle Programmodul-Instanz zurück. Abgeleitet über den Request-Pfad
    /// </summary>
    function ModulInstanz: TModulInstanz;

    function GetKontrollbericht(AId: integer): TKontrollbericht;

    function CloneBewerteteFrage(ABewerteteFrage: TBewerteteFrage): TBewerteteFrage;
    function CloneMangel(AMangel: TMangel): TMangel;

    // Prüft ob es die angegebene Scheckliste bereist gibt
    function ChecklisteExistiert(AVersion: integer; AVersionstext: string; ABezeichnung: string): Boolean;
    function GetAnsprechpartner(const ARegNr: string): TObjectList<TAnsprechpartnerKontakt>;
    procedure RevPlanKontrolleErstellen(ARevPlan: TRevisionsplan; ABetriebsRevStamm: TBetriebRevstamm);

    //Lade alle Rollen/BKBTypen
    procedure LadeRollenBkbTypen;

    //liefert alle Expand-Entities aus dem Request
    //$expand=todos,Betrieb/Adresse/Gemeinde
    function GetExpandsFromRequest: TArray<string>;
    //$expand=... leeren
    procedure ClearExpands;
    //Eintrag zu Expand hinzufügen
    procedure AddExpand(ANewExpand: string);

    //TRUE wenn einer der Stati in AStatusSet enthalten ist
    function IsInStatus(const AStatus, AStatusSet: string): Boolean;

    function StatusToArray(AStatus: string): TArray<Variant>;

  public
    constructor Create;
    destructor Destroy; override;

    property Context: TObjectManager read GetContext;
    property ManagedObjects: Bcl.Collections.THashSet<TObject>read GetManagedObjects;

    property User: TUser read GetUser write FUser;

    [HttpGet]
    [URIPathSegment('pvpToken')]
    function GetPVPToken: TPVPToken;

    [HttpGet]
    [URIPathSegment('')]
    function Me: TUser;

    [HttpGet]
    [URIPathSegment('kontrollen')]
    function Kontrollen(
      Page: integer = 1;
      PageSize: integer = 10
      ): TList<TKontrollbericht>;

    [HttpGet]
    [URIPathSegment('gruppenKontrollen')]
    function GruppenKontrollen(
      GroupLevel: integer = GROUP_LEVEL_ALLE;
      DatumVon: TDateTime = -1;
      DatumBis: TDateTime = -1;
      Status: string = '';
      Page: integer = 1;
      PageSize: integer = 10): TList<TKontrollbericht>;

    [HttpGet]
    [URIPathSegment('ungeplanteKontrollen')]
    function UngeplanteKontrollen(
      Page: integer = 1;
      PageSize: integer = 10): TList<TKontrollbericht>;

    [HttpGet]
    [URIPathSegment('kontrollverlauf')]
    function kontrollverlauf(Page: integer = 1; PageSize: integer = 10; DateAb: TDate = -1; DateBis: TDate = -1;
      IncludeDetails: Boolean = false; AutoExpand: Boolean = true; AIncludeTodos: Boolean = true):
      TList<TKontrollbericht>;

    [HttpGet]
    [URIPathSegment('kontrollverlaufDetails')]
    function kontrollverlaufDetails(
      Page: integer = 1;
      PageSize: integer = 10): TList<TKontrolle>;

    [HttpGet]
    [URIPathSegment('kontrollListe')]
    function kontrollListe(
      Typ: TKontrollListenTyp
      ): TList<TKontrollbericht>;

    function BLDCode: integer;

    [HttpPost]
    [URIPathSegment('neuerKontrollbericht')]
    function NeuerKontrollBericht(Bericht: TKontrollbericht;
      [XDefault(false)]Nacherfassung: Boolean = false): integer;

    [HttpPost]
    [URIPathSegment('neuerUngeplanterKontrollbericht')]
    function NeuerUngeplanterKontrollBericht(
      Bericht: TKontrollbericht;
      Todo: TTodo): integer;

    [HttpPost]
    [URIPathSegment('neuerUngeplanterKontrollbericht2')]
    function NeuerUngeplanterKontrollBericht2(Kontrolle: TUngeplanteKontrolle): integer;

    [HttpGet]
    [URIPathSegment('betriebe')]
    function Betriebe(
      Filter: string = '';
      Top: integer = 100): TList<TBetrieb>;

    [HttpGet]
    [URIPathSegment('nachrichten')]
    function NachrichtenForUserKurz: TObjectList<TNachrichtKurz>;

    [HttpGet]
    [URIPathSegment('gruppen')]
    function Gruppen(Direkt: Boolean = true): TList<TGruppe>;

    [HttpGet]
    [URIPathSegment('nachrichtGelesen')]
    procedure NachrichtGelesen(Id: integer);

    [HttpGet]
    [URIPathSegment('roles')]
    function Rollen: TPVPRoles;

    [HttpGet]
    [URIPathSegment('funktionen')]
    function Funktionen: TList<TFunktion>;

    [HttpGet]
    [URIPathSegment('funktionenKurz')]
    function FunktionenKurz: TObjectList<TFunktionKurz>;

    [HttpGet]
    [URIPathSegment('berichtstypen')]
    function Berichtstypen: TList<TBkbTyp>;

    [HttpGet]
    [URIPathSegment('kontrolltypen')]
    function Kontrolltypen(const BkbTyp: string): TList<TKontrolltyp>;

    [HttpGet]
    [URIPathSegment('rechtsgrundlagen')]
    function Rechtsgrundlagen(const BkbTyp: string): TList<TRechtsgrundlage>;

    [HttpPost]
    [URIPathSegment('freieAdresseHinzufuegen')]
    function FreieAdresseHinzufuegen(Adresse: TFreieAdresse): TAdresse;

    [HttpGet]
    [URIPathSegment('neueBKBNummer')]
    function NeueBKBNummer(BkbTyp: string): string;

    [HttpGet]
    [URIPathSegment('mangeltypen')]
    function Mangeltypen(BkbTyp: string): TList<TMangeltyp>;

    [HttpGet]
    [URIPathSegment('checklisten')]
    function Checklisten(KontrollberichtID: integer): TList<TCheckliste>;

    [HttpPost]
    [URIPathSegment('kontrolleBeenden')]
    function KontrolleBeenden(KontrollErgebnis: TKontrollErgebnis): integer;

    [HttpGet]
    [URIPathSegment('kontrollberichteErstellen')]
    function KontrollberichteErstellen(KontrollberichtID: integer): TErstellteKontrollberichte;

    [HttpGet]
    [URIPathSegment('kontrollberichteNeuErstellen')]
    function KontrollberichteNeuErstellen: TNeuErstellteKontrollberichte;

    [HttpGet]
    [URIPathSegment('AMAAuftragAbschliessen')]
    function AMAAuftragAbschliessen(AId: integer): TGuid;

    [HttpGet]
    [URIPathSegment('AMABewertungsblattErzeugen')]
    function AMABewertungsBlattErzeugen(AId: integer): TGuid;

    [HttpPost]
    [URIPathSegment('probeEintragen')]
    function ProbeEintragen(KontrollberichtID: integer; Probe: TKBProbe; PdfErstellen: Boolean = false): TGuid;

    [HttpPost]
    [URIPathSegment('bildSenden')]
    function BildSenden(
      Bild: TBytes;
      BildFormat: TBildFormat;
      Art: TBildArt;
      Bemerkung: string;
      GUID: TGuid;
      GPSLon: Double = -1;
      GPSLat: Double = -1): TGuid;

    [HttpGet]
    [URIPathSegment('dokument')]
    function Dokument(Id: TGuid): TDokument;

    [HttpGet]
    [URIPathSegment('dokumentDatei')]
    function DokumentDatei(Id: TGuid): TStream;

    [HttpGet]
    [URIPathSegment('gruppenPersonen')]
    function GruppenPersonen: TList<TKontakt>;

    [HttpGet]
    [URIPathSegment('kopiereCheckliste')]
    function KopiereCheckliste(ChecklistenId: integer): TCheckliste;

    [HttpGet]
    [URIPathSegment('loescheCheckliste')]
    procedure LoescheCheckliste(ChecklistenId: integer);

    [HttpGet]
    [URIPathSegment('kontrollenFuerBetrieb')]
    function KontrollenFuerBetrieb(BetriebId: integer; BkbTyp: string): TObjectList<TKontrollberichtKurz>;

    [HttpGet]
    [URIPathSegment('OffeneKontrollenFuerBetrieb')]
    function OffeneKontrollenFuerBetrieb(BetriebId: integer): TObjectList<TKontrollberichtKurz>;

    [HttpGet]
    [URIPathSegment('kontrolleStronieren')]
    procedure KontrolleStornieren(KontrollberichtGUID: TGuid; Stornogrund: string);

    [HttpPost]
    [URIPathSegment('kontrolleVerweigern')]
    procedure KontrolleVerweigern(KontrollVerweigerung: TKontrollVerweigerung);

    [HttpGet]
    [URIPathSegment('kontrollberichtDokumentFuerBkb')]
    function KontrollberichtDokumentFuerBkb(Bkb: string): TGuid;

    [HttpGet]
    [URIPathSegment('apTypen')]
    function ApTypen: TList<TAPTyp>;

    [HttpGet]
    [URIPathSegment('betriebsInfo')]
    function BetriebsInfo(BetriebId: integer): TBetriebsInfo;

    [HttpGet]
    [URIPathSegment('bundeslaender')]
    function bundeslaender(Land: string): TList<TBundesland>;

    [HttpGet]
    [URIPathSegment('kontrollbericht')]
    function kontrollbericht(Id: integer): TKontrollbericht; overload;

    [HttpPost]
    [URIPathSegment('kontrollbericht')]
    procedure kontrollbericht(kontrollbericht: TKontrollbericht); overload;

    [HttpPost]
    [URIPathSegment('kontrollberichtSpeichern')]
    procedure kontrollberichtSpeichern(kontrollbericht: TKontrollbericht);

    [HttpPost]
    [URIPathSegment('kontrolleZuweisen')]
    function KontrolleZuweisen(
      Id: integer;
      PersonID: integer;
      GruppeID: integer;
      Planungsdatum: TDate;
      AngemeldetUm: TDateTime
      ): TKontrollbericht;

    [HttpGet]
    [URIPathSegment('users')]
    function Users(
      [XDefault(false)]NurDirekteGruppen: Boolean = false): TList<TUser>;

    [HttpGet]
    [URIPathSegment('revisionsplanAbschliessen')]
    function RevisionsplanAbschliessen(
      Jahr: integer;
      KontrollTyp: string
      ): TList<TRevisionsplan>;

    [HttpGet]
    [URIPathSegment('ansprechpartner/betrieb/ID/{ID}')]
    function Ansprechpartner(Id: integer): TObjectList<TAnsprechpartnerKontakt>; overload;

    [HttpGet]
    [URIPathSegment('ansprechpartner/betrieb/RegNr/{RegNr}')]
    function Ansprechpartner(const RegNr: string): TObjectList<TAnsprechpartnerKontakt>; overload;

  end;

  // ONLY for Debug!
var
  GDebugUserId: integer;

implementation

uses
  System.DateUtils,
  Sparkle.Http.Headers,
  ELKE.Server.Modules.ServerContainer, ELKE.Server.Logger, Aurelius.Types.Nullable, ELKE.Classes.Logging,
  XData.Web.Response, System.Variants, Aurelius.Criteria.Base, Aurelius.Criteria.Expression,
  System.Math, ELKE.Sparkle.Middleware.ReverseProxy, DX.Classes.Collections, System.IOUtils,
  ELKE.Classes.Generated.Dictionary, Modules.Reports.ELKE, DX.Utils.RTTI, Modules.Reports.AMA, System.RTTI,
  Aurelius.Drivers.Interfaces, Data.DB;

procedure TMe.UpdateUserFromToken(
  LUser: TUser;
  AIsNewUser: Boolean);
var
  LBundesland: TBundesland;
  LKommunikation: TPersonenKommunikationswege;
  LGruppe: TGruppe;
  LUserGruppe: TUsergruppe;
  LHasPersoenlicheGruppe: Boolean;
begin
  // Todo: Welche Eigenschaften sollen bei einem Update (also keine Neuanlage) überschrieben werden?

  LUser.Username := (PVPToken.GIVEN_NAME.ValueOrDefault.Trim + ' ' + PVPToken.PRINCIPAL_NAME.ValueOrDefault.Trim).Trim;
  if AIsNewUser then
  begin
    LUser.Userguid := PVPToken.GID;
    LUser.Usertype := 1;
    LUser.Gesperrt := 0;
    LUser.Userpwc := 0;
    LUser.Aktiv := 1;
  end;
  LUser.ChangeDbuser := 'PVP->REST';
  if not PVPToken.MAIL.HasValue then
    raise EPVPTokenException.Create('Im PVP-Token fehlt die E-Mail Adresse des Users!');
  LUser.Email := PVPToken.MAIL;

  LBundesland := Context.Find<TBundesland>
    .where((M.Bundesland.Land.Landkz = PVPToken.OKZ.Landkz)
    and (M.Bundesland.OKZ = PVPToken.OKZ.OKZ))
    .UniqueResult;

  if LBundesland = nil then
    raise EPVPTokenException.Create('Unbekannte OKZ, Bundesland kann nicht identifiziert werden!');
  LUser.Bundesland := LBundesland;

  // Person generieren und Stammdaten eintragen
  if LUser.Person = nil then
  begin
    LUser.Person := TPerson.Create;
    // LUser.Person.Titel :=

    // Aktuell keine Adresse im Token
    // LUser.Person.Adresse := TAdresse.Create;

    if PVPToken.MAIL.HasValue then
    begin
      LKommunikation := TPersonenKommunikationswege.Create;
      LKommunikation.Person := LUser.Person;
      LKommunikation.Kommunikationsweg := TKommunikationsweg.Create;
      LKommunikation.Kommunikationsweg.Bezeichnung := 'E-Mail';
      LKommunikation.Kommunikationsweg.Art := TKommunikationsart.Create;
      LKommunikation.Kommunikationsweg.Art.Art := 'EMAIL';
      LKommunikation.Kommunikationsweg.Value := PVPToken.MAIL;
      LUser.Person.Kommunikationswege.Add(LKommunikation);
    end;
    if PVPToken.TEL.HasValue then
    begin
      LKommunikation := TPersonenKommunikationswege.Create;
      LKommunikation.Person := LUser.Person;
      LKommunikation.Kommunikationsweg := TKommunikationsweg.Create;
      LKommunikation.Kommunikationsweg.Bezeichnung := 'Telefon';
      LKommunikation.Kommunikationsweg.Art := TKommunikationsart.Create;
      LKommunikation.Kommunikationsweg.Art.Art := 'TEL';
      LKommunikation.Kommunikationsweg.Value := PVPToken.TEL;
      LUser.Person.Kommunikationswege.Add(LKommunikation);
    end;
  end;

  // Vorname/Nachname/Email wird immer abgeglichen
  LUser.Person.Vorname := PVPToken.GIVEN_NAME;
  LUser.Person.Nachname := PVPToken.PRINCIPAL_NAME;
  // Todo: Einfaches Feld "EMail" abschaffen und durch Kommunikationsweg ersetzen
  LUser.Person.Email := PVPToken.MAIL;

  // Jeder User hat eine "Persönliche Gruppe", die der Nachrichtenverteilung dient
  LHasPersoenlicheGruppe := false;
  for LUserGruppe in LUser.Usergruppen do
  begin
    if LUserGruppe.Gruppe.Persoenlich then
    begin
      LHasPersoenlicheGruppe := true;
      break;
    end;
  end;
  if not LHasPersoenlicheGruppe then
  begin
    LGruppe := TGruppe.Create;
    LGruppe.Persoenlich := true;
    LGruppe.Bezeichnung := LUser.Username + ' (' + LUser.Userguid.ValueOrDefault + ')';
    LGruppe.Bundesland := LUser.Bundesland;
    LGruppe.Hauptverantwortlicher := LUser;
    // Todo: müssen diese Werte bei einer persönlichen Gruppe angelegt werden?
    // LGruppe.Okz

    LUserGruppe := TUsergruppe.Create;
    LUser.Usergruppen.Add(LUserGruppe);
    LUserGruppe.User := LUser;
    LUserGruppe.Gruppe := LGruppe;
    Context.Save(LUserGruppe);
  end;

  if Context.HasChanges(LUser) then
  begin
    LUser.Lastchange := now;
  end;
  LUser.LastLogin := now;

  Context.Flush;
end;

function TMe.Users(NurDirekteGruppen: Boolean): TList<TUser>;
var
  LLevel: integer;
begin
  if NurDirekteGruppen then
  begin
    LLevel := 1;
  end
  else
  begin
    LLevel := GROUP_LEVEL_ALLE;
  end;
  var
  LGroupUsers := Context.Find<TVgroupuser>
    .where(M.Vgroupuser.User.Id = User.Id)
    .where(M.Vgroupuser.MembershipLevel <= LLevel)
    .OrderBy(M.Vgroupuser.GroupPerson.Nachname)
    .OrderBy(M.Vgroupuser.GroupPerson.Vorname)
    .List;
  ManagedObjects.Add(LGroupUsers);

  result := TList<TUser>.Create;
  for var LUser in LGroupUsers do
  begin
    result.Add(LUser.GroupUser);
    // Person expandieren
    LUser.GroupUser.Person;
  end;
  TListHelper<TUser>.RemoveDuplicates(result);
end;

procedure TMe.AddExpand(ANewExpand: string);
begin
  var LRequest := TXDataOperationContext.Current.Request;
  var LRawUri := LRequest.RawUri;

  //foo/bar oder foo.bar kann angegeben werden
  ANewExpand := ANewExpand.Replace('.', '/');

  // Query extrahieren
  var LQueryPos := LRawUri.IndexOf('?');
  if LQueryPos < 0 then
  begin
    // Es gibt noch keinen Query-Teil: einfach $expand anhängen
    LRequest.RawUri := LRawUri + '?$expand=' + ANewExpand;
    Exit;
  end;

  var LPath := LRawUri.Substring(0, LQueryPos);
  var LQuery := LRawUri.Substring(LQueryPos + 1);

  var LParamList := TStringList.Create;
  try
    LParamList.Delimiter := '&';
    LParamList.DelimitedText := LQuery;

    // $expand suchen
    var LExpandIndex := -1;
    for var LIndex := 0 to LParamList.Count - 1 do
      if SameText(LParamList.Names[LIndex], '$expand') then
      begin
        LExpandIndex := LIndex;
        break;
      end;

    var LExpandValues := TList<string>.Create;
    try
      if LExpandIndex >= 0 then
      begin
        var LCurrentExpand := LParamList.ValueFromIndex[LExpandIndex].Trim;
        if LCurrentExpand <> '' then
          LExpandValues.AddRange(LCurrentExpand.Split([',']));
        if not LExpandValues.Contains(ANewExpand) then
          LExpandValues.Add(ANewExpand);
        LParamList.ValueFromIndex[LExpandIndex] := string.Join(',', LExpandValues.ToArray);
      end
      else
      begin
        // $expand fehlt -> neu anlegen
        LParamList.Add('$expand=' + ANewExpand);
      end;

      var LNewQuery := LParamList.DelimitedText;
      var LNewRawUri := LPath + '?' + LNewQuery;
      LRequest.RawUri := LNewRawUri;
    finally
      LExpandValues.Free;
    end;
  finally
    LParamList.Free;
  end;
end;

procedure TMe.AddUserFromToken;
var
  LMessage: string;
begin

  FUser := TUser.Create;
  try
    FUser.TstampInsert := now;
    FUser.InsDbuser := 'PVP->REST';
    UpdateUserFromToken(FUser, true);
  except
    on e: Exception do
    begin
      FreeAndNil(FUser);
      ELKELogError(e.Message);
      // Nur eigene Exceptions zusätzlich anzeigen
      if e is EPVPTokenException then
      begin
        LMessage := ' ' + e.Message;
      end;
      raise EXDataHttpException.Create(403, 'User konnte nicht angemeldet werden!' + LMessage.Trim);
    end;
  end;
  Context.SaveOrUpdate(FUser);
  Context.Flush;
end;

function TMe.AMAAuftragAbschliessen(AId: integer): TGuid;
begin
  var
  LTransaction := Context.Connection.BeginTransaction;
  try
    var
    LAuftrag := Context.Find<TCckAuftrag>(AId);
    if LAuftrag = nil then
      raise EXDataHttpException.Create(422, 'Auftrag nicht gefunden');

    // Zuerst die Version hochzählen, da diese bei der Ablage der PDF Datei benötigt wird
    LAuftrag.Version := LAuftrag.Version + 1;

    // Die Details eintragen
    LAuftrag.AbgeschlossenAm := now;
    LAuftrag.IdBearbeiter := User.Id;
    LAuftrag.IdBewerter := User.Id;
    Context.Flush;

    // Zum Schluss das Bewertunsgblatt erzeugen
    TModuleAMAReports.PdfBewertungsblattVerarbeiten(LAuftrag, User);

    // Wir returnieren die GUID des PDFs
    result := LAuftrag.Dokument.GUID;
    LTransaction.Commit;
  except
    LTransaction.Rollback;
    raise;
  end;
end;

function TMe.AMABewertungsBlattErzeugen(AId: integer): TGuid;
begin

  var
  LTransaction := Context.Connection.BeginTransaction;
  try
    var
    LAuftrag := Context.Find<TCckAuftrag>(AId);
    if LAuftrag = nil then
      raise EXDataHttpException.Create(422, 'Auftrag nicht gefunden!');

    if LAuftrag.AbgeschlossenAm.ValueOrDefault = 0 then
      raise EXDataHttpException.Create(422, 'Auftrag wurde noch nicht abgeschlossen!');

    // Bewertunsgblatt neu erzeugen. Falls es bereits ein Dokument/PDF gab, wird dieses überschrieben (keine Änderung der GUID)
    TModuleAMAReports.PdfBewertungsblattVerarbeiten(LAuftrag, User);

    // Wir returnieren die GUID des PDFs
    Context.Flush;
    Context.Refresh(LAuftrag);
    result := LAuftrag.Dokument.GUID;
    LTransaction.Commit;
  except
    LTransaction.Rollback;
    raise;
  end;
end;

function TMe.Ansprechpartner(const RegNr: string): TObjectList<TAnsprechpartnerKontakt>;
begin
  var
  LBetrieb := Context.Find<TBetrieb>
    .where(M.Betrieb.Registrierung.RegNr = RegNr).UniqueResult;
  if LBetrieb = nil then
    raise EXDataHttpException.Create(404, 'Betrieb nicht gefunden');

  result := GetAnsprechpartner(RegNr);
end;

function TMe.Ansprechpartner(Id: integer): TObjectList<TAnsprechpartnerKontakt>;
begin
  var
  LBetrieb := Context.Find<TBetrieb>(Id);
  if LBetrieb = nil then
    raise EXDataHttpException.Create(404, 'Betrieb nicht gefunden');

  result := GetAnsprechpartner(LBetrieb.Registrierung.RegNr);
end;

function TMe.ApTypen: TList<TAPTyp>;
begin
  result := Context.Find<TAPTyp>.List;
end;

function TMe.Berichtstypen: TList<TBkbTyp>;
var
  LBundeslandModul: TBundeslandModul;
begin
  // Die verfügbaren Berichtstypen ergeben sich durch die Module, die für das Bundesland des Benutzers frei geschaltet sind.
  result := TList<TBkbTyp>.Create;
  for LBundeslandModul in self.User.Bundesland.BundeslaenderModule do
  begin
    for var LBkbTyp in LBundeslandModul.Modul.Bkbtypen do
    begin
      if (* LBkbTyp.Sichtbar and *)User.ErlaubterBKBTyp(LBkbTyp) then
      begin
        result.Add(LBkbTyp);
      end;
    end;
  end;
end;

function TMe.Betriebe(
  Filter: string = '';
  Top: integer = 100): TList<TBetrieb>;
var
  LTop: integer;
  LFilter: string;
  LExpression: TCriteria<TBetrieb>;
begin

  LTop := Min(Top, 100); // Mehr als 100 lassen wir nicht zu

  LFilter := Filter.Trim;

  LExpression := Context.Find<TBetrieb>
    .FetchEager('Adresse')
    .FetchEager('Registrierung.Zulassungen')
    .where(M.Betrieb.Bundesland.BLDCode = User.Bundesland.BLDCode)
    .where(M.Betrieb.Registrierung.FreierBetrieb = false);

  if LFilter > '' then
  begin
    LExpression
      .Add(M.Betrieb.Name.Contains(LFilter)
      or (M.Betrieb.Adresse.PLZ = LFilter)
      or (M.Betrieb.Adresse.Ort.StartsWith(LFilter))
      or (M.Betrieb.Adresse.Strasse.StartsWith(LFilter))
      or (M.Betrieb.Registrierung.RegNr = (LFilter))
      or (
      (M.Betrieb.Registrierung.Zulassungen.Zulnr = (LFilter))
      and (M.Betrieb.Registrierung.Zulassungen.Beginndatum <= Today)
      and (M.Betrieb.Registrierung.Zulassungen.Enddatum >= Today)
      )
      );
  end;

  // DISTINCT!
  result := LExpression.RemovingDuplicatedEntities.Take(LTop).List;
end;

function TMe.BetriebsInfo(BetriebId: integer): TBetriebsInfo;
begin
  result := TBetriebsInfo.Create;
  result.Name := 'FUNKTION NICHT VERFÜGBAR';

  // TODO: AV Prüfen
  (*
    var
    LBetrieb := Context.Find<TBetrieb>(BetriebId);
    if not Assigned(LBetrieb) then
    raise EXDataHttpException.Create(404, 'Betrieb nicht gefunden!');

    result := TBetriebsInfo.Create(LBetrieb);
  *)
end;

function TMe.BildSenden(
  Bild: TBytes;
  BildFormat: TBildFormat;
  Art: TBildArt;
  Bemerkung: string;
  GUID: TGuid;
  GPSLon: Double = -1;
  GPSLat: Double = -1): TGuid;
var
  LBild: TKontrollberichtBild;
  LKontrolle: TKontrollbericht;
begin
  LBild := Context.Find<TKontrollberichtBild>(GUID);
  if Assigned(LBild) then
  begin
    raise EXDataHttpException.CreateFmt(HTTP_STATUS_OBJECT_ALREADY_SAVED, 'Das Bild wurde bereits gespeichert! (%s)',
      [GUID.ToString]);
  end;

  LBild := TKontrollberichtBild.Create;
  try
    case Art of
      TBildArt.Bewertung:
        begin
          LBild.BewerteteFrage := Context.Find<TBewerteteFrage>
            .where(M.BewerteteFrage.GUID = GUID.ToString).UniqueResult;

          // Wenn die zugehörige bewertete Frage bisher keine Koordinate hat, dann übernehmen wir die Koordinate des Bildes
          if LBild.BewerteteFrage.GPSLon.IsNull and (GPSLon > -1) then
          begin
            LBild.BewerteteFrage.GPSLon := GPSLon;
            LBild.BewerteteFrage.GPSLat := GPSLat;
          end;

          LKontrolle := LBild.BewerteteFrage.Bericht;
          // Proben, Bilder etc. dürfen erst NACH Beenden der Kontrolle eingetragen werden
          if not LKontrolle.Beendet then
            raise EXDataHttpForbidden.Create
              ('Kontrolle ist noch nicht beendet, es können keine Proben eingetragen werden!');
        end;
      TBildArt.Mangel:
        begin
          LBild.Mangel := Context.Find<TMangel>.where(M.Mangel.GUID = GUID.ToString).UniqueResult;
        end;
      TBildArt.Probe:
        begin
          LBild.Probe := Context.Find<TKBProbe>.where(M.KbProbe.GUID = GUID.ToString).UniqueResult;
        end;
    else
      raise EXDataHttpException.Create(422, 'Nicht unterstütze Bildart!');
    end;

  except
    FreeAndNil(LBild);
    raise;
  end;
  LBild.Bild.AsBytes := Bild;
  case BildFormat of
    TBildFormat.JPEG:
      LBild.Format := 'JPG';
    TBildFormat.PNG:
      LBild.Format := 'PNG';
  else
    raise EXDataHttpException.Create(422, 'Das Bildformat wird nicht unterstützt!');
  end;
  LBild.Aufnahmedatum := now;
  LBild.AufgenommenVon := User;
  LBild.Bemerkung := Bemerkung;
  LBild.GPSLat := GPSLat;
  LBild.GPSLon := GPSLon;
  Context.Save(LBild);
  result := LBild.Id;

  // Bilder ohne Dokument hier loggen, damit wir die GUID im Log sehen
  if (LBild.BewerteteFrage = nil) and (LBild.Mangel = nil) and (LBild.Probe = nil) then
  begin
    // Bilder ohne Zuordnung werden trotzdem gespeichert aber separat geloggt.
    ELKELogError('Zugerordnete Probe/Bewertung/Mangel nicht gefunden! Bild: %s / %s: %s',
      [LBild.Id.ToString, TRttiEnumerationType.GetName(Art), GUID.ToString]);
  end;
end;

function TMe.BLDCode: integer;
begin
  result := User.Bundesland.BLDCode;
end;

function TMe.bundeslaender(Land: string): TList<TBundesland>;
begin
  var
  LLand := Land.Trim.ToUpper;
  result := Context.Find<TBundesland>
    .where(M.Bundesland.Land.Landkz = LLand)
    .where(M.Bundesland.BLDCode > 0)
    .List;
end;

function TMe.ChecklisteExistiert(AVersion: integer; AVersionstext, ABezeichnung: string): Boolean;
begin
  var
  LCheckliste := Context.Find<TCheckliste>
    .where((M.Checkliste.Version = AVersion)
    and (M.Checkliste.Versionstext = AVersionstext)
    and (M.Checkliste.Bezeichnung = ABezeichnung)).List;
  result := LCheckliste.Count > 0;
end;

function TMe.Checklisten(
  KontrollberichtID:
  integer): TList<TCheckliste>;
begin
  result := TList<TCheckliste>.Create;
  var LBericht := GetKontrollbericht(KontrollberichtID);

  // Wie schauen NUR auf die zugewiesene Checklisten aus der Zuordnung Bundesländer-Checklisten-Kontrolltypen

  var LZugewieseneChecklisten := Context.Find<TBundeslandChecklistenKontrolltyp>
  .FetchEager('ChecklisteKontrolltyp')
    .FetchEager('ChecklisteKontrolltyp.Checkliste')
    .FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen')
    //   .FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.UntergeordneteFragen')
  .FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.UebergeordneteFrage')
    //.FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.FragenBewertungen.Bewertung.Typ')
    //.FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.FragenBewertungen.Bewertung.Icon')
    //.FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.FragenBewertungen.Standardmassnahme')
    //    .FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.FragenBewertungen.Standardmangeltyp.Massnahmenkatalog.Massnahmen')
  //.FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.FragenKontrollbereiche.Kontrollbereich')
  .FetchEager('ChecklisteKontrolltyp.Checkliste.Fragen.Formatierung')

  // Gültigkeit der Bundeslandzuordnung prüfen
  .where((M.BundeslandChecklistenKontrolltyp.GueltigAb <= Today) and
    (M.BundeslandChecklistenKontrolltyp.GueltigBis >= Today))
    // Gültigkeite der Checkliste selbst prüfen
  .where((M.BundeslandChecklistenKontrolltyp.ChecklisteKontrolltyp.Checkliste.GueltigAb <= Today) and
    (M.BundeslandChecklistenKontrolltyp.ChecklisteKontrolltyp.Checkliste.GueltigBis >= Today))

  .where(M.BundeslandChecklistenKontrolltyp.Bundesland.BLDCode = User.Bundesland.BLDCode)
    .where(M.BundeslandChecklistenKontrolltyp.ChecklisteKontrolltyp.KontrollTyp.BkbTyp.Typ =
    LBericht.KontrollTyp.BkbTyp.Typ)
    .where(M.BundeslandChecklistenKontrolltyp.ChecklisteKontrolltyp.KontrollTyp.KontrollTyp =
    LBericht.KontrollTyp.KontrollTyp).List;

  ManagedObjects.Add(LZugewieseneChecklisten);

  for var LCheckliste in LZugewieseneChecklisten do
  begin

    // Achtung: hier werden die Fragen gelöscht und NUr die ROOT Fragen wieder eingehängt.
    var LRootFragen := TList<TFrage>.Create;
    for var LFrage in LCheckliste.ChecklisteKontrolltyp.Checkliste.Fragen do
    begin
      if LFrage.UebergeordneteFrage = nil then
      begin
        LRootFragen.Add(LFrage);
      end;
    end;
    ManagedObjects.Add(LRootFragen);

    LCheckliste.ChecklisteKontrolltyp.Checkliste.Fragen.Clear;
    LCheckliste.ChecklisteKontrolltyp.Checkliste.Fragen.AddRange(LRootFragen);
    LCheckliste.ChecklisteKontrolltyp.Checkliste.SortFragenbewertungen;
    result.Add(LCheckliste.ChecklisteKontrolltyp.Checkliste);
  end;

  TListHelper<TCheckliste>.RemoveDuplicates(result);
  result.Sort(TCheckliste.Comparer);
end;

constructor TMe.Create;
begin
  inherited;
  FPVPToken := nil;
  FUser := nil;
  FDMMain := TDMMain.Create(Context.Connection);
  //Todo: Der SQL Log ist hier rudimentär implementiert und damit auf "ME" beschränkt
  if TDMMain.SQLLogActive then
  begin
    if Context <> nil then
    begin
      Context.Explorer.Events.OnSqlExecuting.Subscribe(
        procedure(Args: TSQLExecutingArgs)
        begin
          //Im Event auch noch mal abfragen, damit "mittendrin" abgeschaltet werden kann
          if TDMMain.SQLLogActive then
          begin
            var LParams := '';
            for var LParam in Args.Params do
            begin
              LParams := LParams + ' ' + LParam.ToString;
            end;
            ELKELog('Exec SQL'#13#10'%s'#13#10'%s', [Args.SQL, LParams]);
          end;
        end);
    end;
  end;
end;

destructor TMe.Destroy;
begin
  FreeAndNil(FDMMain);
  inherited;
end;

procedure TMe.ClearExpands;
begin
  var LOriginalUri := TXDataOperationContext.Current.Request.RawUri;
  // Zerlege RawUri in Path und Query
  var LPos := LOriginalUri.IndexOf('?');
  // Gibt es überhaupt eine Query
  if LPos > 0 then
  begin
    var LPath := LOriginalUri.Substring(0, LPos);
    var LQuery := LOriginalUri.Substring(LPos + 1);

    var LParamList := TStringList.Create;
    try
      LParamList.Delimiter := '&';
      LParamList.DelimitedText := LQuery;

      for var LIndex := LParamList.Count - 1 downto 0 do
        if SameText(LParamList.Names[LIndex], '$expand') then
          LParamList.Delete(LIndex);

      var LNewQuery := LParamList.DelimitedText;
      var LNewUri := '';
      if LNewQuery <> '' then
      begin
        LNewUri := LPath + '?' + LNewQuery
      end
      else
      begin
        LNewUri := LPath;
      end;
      //leave an empty "$expand"
      TXDataOperationContext.Current.Request.RawUri := LNewUri + '&$expand=';
    finally
      LParamList.Free;
    end;
  end;
end;

/// <summary>
/// Returns a TDokument instance managed by the Aurelius Context.
/// Memory management is handled by the Context.
/// </summary>
function TMe.Dokument(Id: TGuid): TDokument;
begin
  result := Context.Find<TDokument>
    .where(M.Dokument.GUID = Id.ToString)
    .UniqueResult;
  if result = nil then
    raise EXDataHttpException.Create(404, 'Das angefoderte Dokument wurde nicht gefunden!');
end;

/// <summary>
/// Returns a stream containing the document data.
/// The returned stream is automatically managed by Aurelius when assigned to result.
/// </summary>
function TMe.DokumentDatei(Id: TGuid): TStream;
begin
  // LDokument.Dokument ist ein Blob des PDF (oder was auch immer). Als Stream kommt das im Client als "pure" Datei an
  var LDokument := Dokument(Id);
  result := TMemoryStream.Create;
  LDokument.Dokument.SaveToStream(result);
end;

{ TMe }

/// <summary>
/// Creates a clone of the given TMangel instance.
/// </summary>
function TMe.CloneMangel(AMangel: TMangel): TMangel;
var
  LMangel: TMangel;
begin
  LMangel := TMangel.Create;
  LMangel.CopyFrom(AMangel);
  if AMangel.Mangeltyp = nil then
    raise EXDataHttpException.Create(422, 'Mangel hat keinen Mangel-Typ zugeordnet! ' + AMangel.GUID.ToString);
  LMangel.Mangeltyp := Context.Find<TMangeltyp>(AMangel.Mangeltyp.Id);
  if AMangel.Massnahme = nil then
    raise EXDataHttpException.Create(422, 'Mangel hat keine Massnahme zugeordnet! ' + AMangel.GUID.ToString);
  LMangel.Massnahme := Context.Find<TMassnahme>(AMangel.Massnahme.Id);
  result := LMangel;
end;

/// <summary>
/// Returns a list of contact persons for a given registration number.
/// The returned TObjectList owns and manages the TAnsprechpartnerKontakt instances.
/// Caller takes ownership of the returned list.
/// </summary>
function TMe.GetAnsprechpartner(const ARegNr: string): TObjectList<TAnsprechpartnerKontakt>;
begin
  result := TObjectList<TAnsprechpartnerKontakt>.Create;

  // Freie Betriebe / Freie Adressen haben keine Ansprechpartner
  if ARegNr.Trim = FREIER_BETRIEB then
  begin
    ELKELog('Freie Betriebe haben keine Ansprechpartner!');
  end
  else
  begin
    var
    LAnsprechpartner := Context.Find<TAnsprechpartner>
      .where(M.Ansprechpartner.RegNr = ARegNr).List;

    for var LAP in LAnsprechpartner do
    begin
      var
      LKontakt := TAnsprechpartnerKontakt.Create(LAP.Person);
      LKontakt.APTyp := LAP.APTyp;
      LKontakt.Kommunikationsberechtigt := LAP.Kommunikationsberechtigt;
      result.Add(LKontakt);
    end;
    ELKELog('Betrieb: %s - Ansprechpartner: %d', [ARegNr, result.Count]);
  end;
end;

function TMe.GetContext: TObjectManager;
begin
  result := TXDataOperationContext.Current.GetManager;
end;

function TMe.GetExpandsFromRequest: TArray<string>;
begin
  result := [];
  var LRequestURI := TXDataOperationContext.Current.Request.RawUri;
  if LRequestURI.tolower.Contains('$expand') then
  begin
    var LURI := TXDataOperationContext.Current.Request.Uri;
    var LQuery := LURI.Query;
    if LQuery.StartsWith('?') then
    begin
      LQuery := Copy(LQuery, 2, Length(LQuery));
    end;
    var LParamList := TStringList.Create;
    try
      LParamList.Delimiter := '&';
      LParamList.DelimitedText := LQuery;
      for var LIndex := 0 to LParamList.Count - 1 do
        if LParamList.Names[LIndex] = '$expand' then
        begin
          var LExpandValue := LParamList.ValueFromIndex[LIndex];
          result := LExpandValue.Split([',']);
          break;
        end;
    finally
      LParamList.Free;
    end;
  end;
end;

/// <summary>
/// Creates a clone of the given TBewerteteFrage instance.
/// </summary>
function TMe.CloneBewerteteFrage(ABewerteteFrage: TBewerteteFrage): TBewerteteFrage;
var
  LBewerteteFrage: TBewerteteFrage;
begin
  LBewerteteFrage := TBewerteteFrage.Create;
  LBewerteteFrage.Wert := ABewerteteFrage.Wert;
  LBewerteteFrage.Zusatztext := ABewerteteFrage.Zusatztext;
  LBewerteteFrage.GUID := ABewerteteFrage.GUID;

  if ABewerteteFrage.Frage = nil then
    raise EXDataHttpException.Create(422, 'Bewertung keiner Frage zugeordnet!');
  LBewerteteFrage.Frage := Context.Find<TFrage>(ABewerteteFrage.Frage.Id);

  if ABewerteteFrage.Bewertung = nil then
    raise EXDataHttpException.Create(422, 'Bewertung zu Frage ' + LBewerteteFrage.Frage.Id.ToString + ' fehlt!');
  LBewerteteFrage.Bewertung := Context.Find<TBewertung>(ABewerteteFrage.Bewertung.Id);

  Assert(LBewerteteFrage <> nil);
  result := LBewerteteFrage;
end;

function TMe.FreieAdresseHinzufuegen(Adresse: TFreieAdresse): TAdresse;
var
  LAdressen: TList<TAdresse>;
  LAehnlicheAdresse: TAdresse;
  LAdresse: TAdresse;
begin

  LAdresse := nil;
  LAdressen := nil;
  try
    LAdressen := Context.Find<TAdresse>
      .where(M.Adresse.Ort = Adresse.Ort)
      .where(M.Adresse.PLZ = Adresse.PLZ)
      .where(M.Adresse.Strasse = Adresse.Strasse).List;

    // ähnliche Adressen prüfen
    for LAehnlicheAdresse in LAdressen do
    begin
      if (LAehnlicheAdresse.Strasse.tolower.Trim = Adresse.Strasse.tolower.Trim)
        and (LAehnlicheAdresse.Adresszusatz.ValueOrDefault.tolower.Trim = Adresse.Adresszusatz.tolower.
        Trim) then
      begin
        LAdresse := LAehnlicheAdresse;
      end;
    end;
    if LAdresse = nil then
    begin
      // Neue Adresse anlegen
      LAdresse := TAdresse.Create;
      try
        LAdresse.Strasse := Adresse.Strasse;
        LAdresse.Adresszusatz := Adresse.Adresszusatz;
        LAdresse.Bundesland := User.Bundesland;
        if LAdresse.Bundesland.Land.Landkz <> Adresse.Landkz then
          raise EXDataHttpException.Create(400, 'Unzulässiges Landeskennzeichen!');
        LAdresse.PLZ := Adresse.PLZ;
        LAdresse.Ort := Adresse.Ort;
        // Todo: Validate  implementieren
      finally
        Context.Save(LAdresse);
      end;
    end;
    Context.Flush;
    result := LAdresse;
  finally
    FreeAndNil(LAdressen)
  end;
end;

function TMe.Funktionen: TList<TFunktion>;
var
  LUserRolle: TUserRolle;
  LFunktionRolle: TFunktionRolle;
begin
  result := TList<TFunktion>.Create;
  // Alle Funktionen, die in der Rolle des Users enthalten sind.
  if self.User.Userrollen.Count = 0 then
    ELKELog('Keine User-Rollen gefunden!', TLogLevel.Warning);
  for LUserRolle in self.User.Userrollen do
  begin
    // Nur Funktionen die zu Rollen für die aktuelle Instanz gehören
    if LUserRolle.ModulInstanz.Id = ModulInstanz.Id then
    begin
      for LFunktionRolle in LUserRolle.Rolle.FunktionenRollen do
      begin
        // Nur Funktionen, die zum aktuellen Modul gehören
        if LFunktionRolle.Funktion.Programmmodul.Kurzbezeichnnung = ModulInstanz.Modul.Kurzbezeichnnung then
        begin
          // Nur sichtbare Funktionen ausliefern
          if LFunktionRolle.Funktion.Sichtbar
            and (LFunktionRolle.Funktion.Begdat <= date)
            and ((LFunktionRolle.Funktion.Enddat >= date))
            and (LFunktionRolle.Sichtbar)
            and (LFunktionRolle.Rolle.Sichtbar) then
          begin
            result.Add(LFunktionRolle.Funktion);
          end;
        end;
      end;
    end;
  end;
  if result.Count = 0 then
    ELKELog('Keine Funktionen gefunden!', TLogLevel.Warning);
  TListHelper<TFunktion>.RemoveDuplicates(result);
end;

function TMe.FunktionenKurz: TObjectList<TFunktionKurz>;
var
  LFunktionen: TList<TFunktion>;
  LFunktion: TFunktion;
  LFunktionKurz: TFunktionKurz;
begin
  result := TObjectList<TFunktionKurz>.Create;
  LFunktionen := Funktionen;

  for LFunktion in LFunktionen do
  begin
    LFunktionKurz := TFunktionKurz.Create;
    LFunktionKurz.Id := LFunktion.Id;
    LFunktionKurz.Sichtbar := LFunktion.Sichtbar;
    LFunktionKurz.Progcallid := LFunktion.Progcallid;
    LFunktionKurz.Position := LFunktion.Position;
    LFunktionKurz.Beschriftung := LFunktion.Beschriftung;
    result.Add(LFunktionKurz);
  end;
end;

function TMe.ModulInstanz: TModulInstanz;
var
  LModulInstanzen: TList<TModulInstanz>;
  LRequestPath: string;
begin
  if FModulInstanz = nil then
  begin
    // Das Modul bestimmt sich über die ORIG-URI, die wir im PVP Header bekommen
    // Die ORIG-URI stammt effektiv von der aufrufenden App, also aktuell
    // Moped oder Intraweb. Es kann auch die REST App URL drin stecken,
    // wenn REST direkt (z.B. über den Browser aufgerufen wurde.

    LRequestPath := GCurrentRequest.PVPOrigUri.Path;

    LModulInstanzen := Context.Find<TModulInstanz>.List;
    ManagedObjects.Add(LModulInstanzen);
    for var LInstanz in LModulInstanzen do
    begin
      if LRequestPath.StartsWith(LInstanz.InstanzUrl) then
      begin
        FModulInstanz := LInstanz;
        break;
      end;
    end;
  end;
  if FModulInstanz = nil then
    raise EXDataHttpForbidden.CreateFmt(403, 'Abfrage nicht zulässig. Instanz nicht eingerichtet!'#13#10'%s',
      [LRequestPath]);
  result := FModulInstanz;
end;

function TMe.ProbeEintragen(KontrollberichtID: integer;
  Probe: TKBProbe;
  PdfErstellen: Boolean = false): TGuid;
begin
  if Probe = nil then
    raise EXDataHttpException.Create(422, 'Keine Probe angegeben!');
  if Probe.Probenart.ValueOrDefault = '' then
    raise EXDataHttpException.Create(422, 'Probenart fehlt!');
  if Probe.BkbTyp = nil then
    raise EXDataHttpException.Create(422, 'BkbTyp fehlt!');

  if Probe.Probenbezeichnung = '' then
    raise EXDataHttpException.Create(422, 'Probenbezeichnung fehlt!');

  if Probe.GUID <> TGuid.Empty then
  begin
    var
    LProbe := Context.Find<TKBProbe>
      .where(M.KbProbe.GUID = Probe.GUID.ToString).UniqueResult;
    if LProbe <> nil then
      raise EXDataHttpException.CreateFmt(HTTP_STATUS_OBJECT_ALREADY_SAVED,
        'Das Probe wurde bereits eingetragen! (%s)',
        [Probe.GUID.ToString]);
  end;

  var
  LKontrollbericht := GetKontrollbericht(KontrollberichtID);

  // Proben, Bilder etc. dürfen erst NACH Beenden der Kontrolle eingetragen werden
  if not LKontrollbericht.Beendet then
    raise EXDataHttpForbidden.Create('Kontrolle ist noch nicht beendet, es können keine Proben eingetragen werden!');

  LKontrollbericht.Probenziehung := true;

  // https://esculenta.atlassian.net/browse/EIW-408
  Probe.Datum := LKontrollbericht.Endezeit;

  Probe.BkbTyp := Context.Find<TBkbTyp>(Probe.BkbTyp.Typ);
  if not Probe.BkbTyp.Probe then
    raise EXDataHttpException.Create('Der BkbTyp ' + Probe.BkbTyp.Typ + ' kann nicht für Proben verwendet werden!');
  Probe.Probenbkb := NeueBKBNummer(Probe.BkbTyp.Typ); // Probenart muss ein gültiger BKBTyp sein
  Probe.Einsender := User.Person;
  Probe.kontrollbericht := LKontrollbericht;

  Context.Save(Probe);

  if PdfErstellen then
  begin
    TModuleReports.PdfProbenbegleitscheinErzeugenUndSpeichern(Probe, User);
  end;
  Context.Flush;
  Context.Refresh(Probe);
  result := Probe.GUID;
end;

function TMe.PVPToken: TPVPToken;
begin
  // Todo: über das neue Auth System implementieren
  if FPVPToken = nil then
  begin
    FPVPToken := TPVPToken.Create(TXDataOperationContext.Current.Request.Headers);
    ManagedObjects.Add(FPVPToken);
  end;
  result := FPVPToken;
end;

function TMe.Gruppen(Direkt: Boolean = true): TList<TGruppe>;
var
  LUserGruppen: TList<TVUserGroupMembership>;
  LUserGruppe: TVUserGroupMembership;
begin
  if not Assigned(FGruppen) then
  begin
    FGruppen := TList<TGruppe>.Create;
    ManagedObjects.Add(FGruppen);

    LUserGruppen := Context.Find<TVUserGroupMembership>
      .where(M.Vusergroupmembership.User.Id = User.Id).List;

    try
      for LUserGruppe in LUserGruppen do
      begin
        // Die Gruppen je nach "Direkt" hinzufügen
        // Wenn Direkt = true, dann nur direkte Gruppen, d.h. Level = 1 hinzufügen
        // Todo: Später den MembershipLevel als transientes Feld in TGruppe mitgeben
        if (not Direkt) or (LUserGruppe.MembershipLevel = 1) then
        begin
          FGruppen.Add(LUserGruppe.Gruppe);
        end;
      end;
    finally
      FreeAndNil(LUserGruppen);
    end;

    // Alle Gruppen hinzufügen, in denen der User Hauptverantwortlicher ist
    FGruppen.AddRange(self.User.HauptverantwortlicherFuerGruppen.ToArray);

    // Alle Gruppen hinzufügen, in denen der User Stellvertreter ist
    FGruppen.AddRange(self.User.StellvertreterFuerGruppen.ToArray);

    // Duplikate entfernen
    TListHelper<TGruppe>.RemoveDuplicates(FGruppen);
  end;
  result := FGruppen;
end;

function TMe.GruppenKontrollen(
  GroupLevel: integer = GROUP_LEVEL_ALLE;
  DatumVon: TDateTime = -1;
  DatumBis: TDateTime = -1;
  Status: string = '';
  Page: integer = 1;
  PageSize: integer = 10
  ): TList<TKontrollbericht>;
begin
  result := KontrollenForUserNeu(User, Status, TGroupLevel.MitAllenUntergruppen, DatumVon, DatumBis, Page, PageSize);

  Assert(result.Count <= PageSize, 'PageSize <> result.Count');
end;

function TMe.GruppenPersonen: TList<TKontakt>;
begin
  result := TList<TKontakt>.Create;
  // Alle User, die zu den Gruppen des aktuellen Users gehören.
  // Inklusive Untergruppen
  var
  LGroupUsers := Context.Find<TVgroupuser>
    .CreateAlias('GroupPerson', 'p', TFetchMode.Eager) // Alias für Person, um EagerLoading zu forcieren
  .CreateAlias('Group', 'g', TFetchMode.Eager) // Alias für die Gruppe von UserGruppen -> EagerLoading
  .where(M.Vgroupuser.MembershipLevel <= GROUP_LEVEL_ALLE)
    .where(M.Vgroupuser.User.Id = User.Id)
    .List;
  for var LGroupUser in LGroupUsers do
  begin
    result.Add(TKontakt.Create(LGroupUser.GroupPerson, LGroupUser.Group));
  end;
  // Ein  Duplikat wäre eine Person mit gleicher Gruppe, die mehrfach vorkommt.
  // Ein "Alois Mayer" kann trozdem mehrfach vorkommen - mit jeweils unterschiedlicher Gruppe
  TListHelper<TKontakt>.RemoveDuplicates(result);
  TListHelper<TKontakt>.SortByProperty(result, 'Nachname');
end;

function TMe.IsInStatus(const AStatus, AStatusSet: string): Boolean;
begin
  for var LChar in AStatus do
    if AStatusSet.IndexOf(LChar) >= 0 then
      Exit(true);
  result := false;

end;

procedure TMe.kontrollbericht(kontrollbericht: TKontrollbericht);
begin
  var
  LKontrollbericht := Context.Find<TKontrollbericht>.where(M.kontrollbericht.Id = kontrollbericht.Id).UniqueResult;
  if LKontrollbericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrollbericht nicht gefunden!');

  if not LKontrollbericht.istSichtbarFuerUser(User) then
    raise EXDataHttpException.Create(403, 'Keine Berechtigung für Kontrollbericht ' + kontrollbericht.Id.ToString);

  if Assigned(kontrollbericht.Kontrollorgan) then
  begin
    var
    LKontrollorgan := Context.Find<TPerson>(kontrollbericht.Kontrollorgan.Id);
    LKontrollbericht.Kontrollorgan := LKontrollorgan;
  end;

  if Assigned(kontrollbericht.Erfasser) then
  begin
    var
    LErfasser := Context.Find<TPerson>(kontrollbericht.Erfasser.Id);
    LKontrollbericht.Erfasser := LErfasser;
  end
  else
  begin
    // Es sollte immer einen Erfasser geben
    LKontrollbericht.Erfasser := User.Person;
  end;

  LKontrollbericht.Datum := kontrollbericht.Datum;
  LKontrollbericht.RefBkb := kontrollbericht.RefBkb;

  if Assigned(kontrollbericht.Rechtsgrundlage) then
  begin
    var
    LRechtsgrundlage := Context.Find<TRechtsgrundlage>(kontrollbericht.Rechtsgrundlage.Id);
    LKontrollbericht.Rechtsgrundlage := LRechtsgrundlage;
  end;

  LKontrollbericht.Kurzbemerkung := kontrollbericht.Kurzbemerkung;
  LKontrollbericht.KontrollInformationen := kontrollbericht.KontrollInformationen;
  LKontrollbericht.InterneNotiz := kontrollbericht.InterneNotiz;

  LKontrollbericht.AngemeldetUm := kontrollbericht.AngemeldetUm;

  LKontrollbericht.QuellGruppeValidieren;
  Context.SaveOrUpdate(LKontrollbericht);
  Context.Flush;
end;

function TMe.kontrollbericht(Id: integer): TKontrollbericht;
begin
  var Context := Context.Create;

  AddExpand('Kontrolltyp.Bkbtyp');
  AddExpand('Betrieb.Registrierung');
  AddExpand('Betrieb.Adresse.Gemeinde');
  AddExpand('Erfasser');
  AddExpand('Rechtsgrundlage');
  AddExpand('Kontrollorgan');
  AddExpand('GruppeQuelle');
  AddExpand('Todos');

  var LKontrollbericht := Context
  .Find<TKontrollbericht>
    .where(M.kontrollbericht.Id = Id)
    .FetchEager('Kontrolltyp.Bkbtyp')
    .FetchEager('Betrieb.Registrierung')
    .FetchEager('Betrieb.Adresse.Gemeinde')
    .FetchEager('Erfasser')
    .FetchEager('Rechtsgrundlage')
    .FetchEager('Kontrollorgan')
    .FetchEager('GruppeQuelle')
    .FetchEager('Todos')
    .UniqueResult;

  if LKontrollbericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrollbericht nicht gefunden!');

  if not LKontrollbericht.istSichtbarFuerUser(User) then
    raise EXDataHttpForbidden.Create('Keine Berechtigung für Kontrollbericht ' + Id.ToString);

  //Todo: Werden die Todos hier benötigt?
  //LKontrollbericht.Todos;

  result := LKontrollbericht;
end;

function TMe.KontrollberichtDokumentFuerBkb(Bkb: string): TGuid;
begin
  var
  Context := Context.Create;
  var
  LKontrollbericht := Context.Find<TKontrollbericht>.where(M.kontrollbericht.Bkb = Bkb).UniqueResult;
  if LKontrollbericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrollbericht nicht gefunden!');
  if not LKontrollbericht.istSichtbarFuerUser(User) then
    raise EXDataHttpException.Create(403, 'Kein Zugriff auf Kontrollbericht!');
  if LKontrollbericht.Dokument = nil then
    raise EXDataHttpException.Create(404, 'Für die Kontrolle liegt kein Kontrollbericht vor!');

  result := LKontrollbericht.Dokument.GUID;
end;

function TMe.KontrolleBeenden(KontrollErgebnis: TKontrollErgebnis): integer;
var
  LDatum: TDateTime;
begin

  if KontrollErgebnis = nil then
    raise EXDataHttpException.Create(422, 'Kontrollergebnis fehlt!');

  var
  LBerichtId := KontrollErgebnis.KontrollberichtID;

  ELKELog('Kontrolle ' + LBerichtId.ToString + ' wird beendet...');

  // Kontrolle laden
  var
  LBericht := Context.Find<TKontrollbericht>(KontrollErgebnis.KontrollberichtID);

  if LBericht = nil then
    raise EXDataHttpException.CreateFmt(404, 'Kontrolle %d wurde nicht gefunden!',
      [KontrollErgebnis.KontrollberichtID]);

  if LBericht.Rechtsgrundlage.UnterschriftErforderlich then
  begin
    if (KontrollErgebnis.Anwesende = nil) or (KontrollErgebnis.Anwesende.Count = 0) then
      raise EXDataHttpException.Create(422, 'Anwesende sind nicht eingetragen!');

    if KontrollErgebnis.Unterschriften = nil then
      raise EXDataHttpException.Create(422, 'Unterschriften fehlen!');

    if (KontrollErgebnis.Unterschriften.Anwesender <> nil) and (KontrollErgebnis.Unterschriften.Anwesender.Name = '')
      then
      raise EXDataHttpException.Create(422,
        'Name des Anwesenden muss zusätzlich in der Unterschrift angegeben werden!');
    // Todo: Verweigerungsgrund bei fehlender Unterschrift Anwesender prüfen

  end;

  if KontrollErgebnis.Unterschriften.Kontrollorgan = nil then
    raise EXDataHttpException.Create(422, 'Unterschrift des Kontrollorgans fehlt!');

  if KontrollErgebnis.Unterschriften.Kontrollorgan.Name = '' then
    raise EXDataHttpException.Create(422, 'Name des Kontrollorgans muss in der Unterschrift angegeben werden!');

  // Prüfung ob Kontrolle bereits beendet
  // Eigene Fehlercodes beginnen ab 471

  if LBericht.Beendet then
    raise EXDataHttpException.CreateFmt(HTTP_STATUS_OBJECT_ALREADY_SAVED, 'Kontrolle %d wurde bereits beendet!',
      [LBericht.Id]);

  // Prüfung ob User Zugriff auf Kontrolle hat
  // Todo: Das hier ist potentiell langsam!
  var
  LFound := false;
  for var LUserKontrolle in KontrollenForUserNeu(self.User, STATUS_OFFEN, TGroupLevel.MitDirektenGruppen, -1, -1, 1,
    MaxInt) do
  begin
    if KontrollErgebnis.KontrollberichtID = LUserKontrolle.Id then
    begin
      LFound := true;
      break
    end;
  end;
  if not LFound then
    raise EXDataHttpException.CreateFmt(472, 'Achtung! Kontrolle %d ist nicht dem User zugeordnet!',
      [KontrollErgebnis.KontrollberichtID]);

  var
  LTransaction := Context.Connection.BeginTransaction;
  try

    // Örtlichkeiten übernehmen
    if (KontrollErgebnis.Oertlichkeiten <> nil) and (KontrollErgebnis.Oertlichkeiten.Count > 0) then
    begin
      for var LOertlichkeit in KontrollErgebnis.Oertlichkeiten do
      begin
        if LOertlichkeit.Id = TGuid.Empty then
          raise EXDataHttpException.CreateFmt(422, 'Örtlichkeiten müßen eine eindeutige IDs haben! (%s)',
            [LOertlichkeit.Bezeichnung]);
        // var LOertlichkeit := TKontrollberichtOertlichkeit.Create;
        // LOertlichkeit.CopyFrom(LOertlichkeit);
        LOertlichkeit.kontrollbericht := LBericht;
        // Achtung: Replicate, da die GUID von aussen herein kommt.
        Context.Replicate(LOertlichkeit);
      end;
    end;

    // Prüfen, dass es überhaupt Bewertungen gibt
    if ((KontrollErgebnis.BewerteteFragen = nil) or (KontrollErgebnis.BewerteteFragen.Count = 0))
      and ((KontrollErgebnis.Maengel = nil) or (KontrollErgebnis.Maengel.Count = 0)) then
      raise EXDataHttpException.Create(422, 'Es wurden keine Bewertungen übergeben!');

    // Bewertungen mit Maengeln übertragen
    if KontrollErgebnis.Maengel <> nil then
    begin
      for var LMangelR in KontrollErgebnis.Maengel do
      begin
        var
        LMangel := CloneMangel(LMangelR); // Ein Mangel kann mehreren Bewertungen zugeordnet sein

        // Oertlichkeiten aus den Mängeln übertragen
        for var LOertlichkeitR in LMangelR.MangelOertlichkeiten do
        begin
          var
          LOertlichkeit := TMangelOertlichkeit.Create;
          LOertlichkeit.Mangel := LMangel;
          LOertlichkeit.Oertlichkeit := Context.Find<TKontrollberichtOertlichkeit>(LOertlichkeitR.Oertlichkeit.Id);
          if LOertlichkeit.Oertlichkeit = nil then
            raise EXDataHttpException.CreateFmt(422, 'Unbekannte Örtlichkeit (%s) im Mangel!',
              [LOertlichkeitR.Oertlichkeit.Bezeichnung]);
          Context.Save(LOertlichkeit);
        end;

        // Wenn es Örtlichkeiten gibt, dann ist der Mangel bereits im Context
        Context.SaveOrUpdate(LMangel);

        if (LMangelR.BewerteteFragen = nil) or (LMangelR.BewerteteFragen.Count = 0) then
          raise EXDataHttpException.Create('Mangel ' + LMangelR.Id.ToString + ' hat keine bewerteten Fragen!');
        for var LBewerteteFrageR in LMangelR.BewerteteFragen do
        begin
          var
          LBewerteteFrage := CloneBewerteteFrage(LBewerteteFrageR);
          LBewerteteFrage.Mangel := LMangel;
          LBewerteteFrage.Bericht := LBericht;
          Context.Save(LBewerteteFrage);

          // Bewertung aus der Gesamtliste entfernen - da sonst doppelt gespeichert wird
          for var i := 0 to KontrollErgebnis.BewerteteFragen.Count - 1 do
          begin
            if KontrollErgebnis.BewerteteFragen[i].GUID = LBewerteteFrage.GUID then
            begin
              KontrollErgebnis.BewerteteFragen.Delete(i);
              break;
            end;
          end;

        end;
      end;
    end;

    // Bewertungen ohne Maengel übertragen
    for var LBewerteteFrageR in KontrollErgebnis.BewerteteFragen do
    begin
      var
      LBewerteteFrage := CloneBewerteteFrage(LBewerteteFrageR);
      LBewerteteFrage.Bericht := LBericht;
      Context.Save(LBewerteteFrage);
    end;

    // Datum
    // Angemeldet
    if KontrollErgebnis.AngemeldetUm.HasValue then
    begin
      LBericht.AngemeldetUm := KontrollErgebnis.AngemeldetUm;
    end;

    // Startzeit
    if KontrollErgebnis.Startzeit = 0 then
      raise EXDataHttpException.Create(422, 'Startzeit fehlt');
    begin
      LBericht.Startzeit := KontrollErgebnis.Startzeit;
    end;

    // Endezeit
    if KontrollErgebnis.Endezeit > 0 then
    begin
      LBericht.Endezeit := KontrollErgebnis.Endezeit;
    end
    else
    begin
      LBericht.Endezeit := now;
    end;

    // Namen
    LBericht.Kontrollorgan := User.Person;
    if LBericht.Erfasser = nil then
    begin
      LBericht.Erfasser := LBericht.Kontrollorgan;
    end;

    // Anwesende sind vom Typ TAnsprechpartnerKontakt. Es wird für jeden Kontrollbericht
    // eine Liste der Anwesenden als TList<TAnwesender> in die Tabelle Bewegungsdaten.Anwesende gespeichert
    // Für jeden anwesenden Kontakt erzeugen wir
    // - einen TAnwesender
    // - einen TAnsprechpartner

    // Default-Funktion ist "Anwesender", wird gesetzt, wenn nichts übergeben wird
    var
    LDefaultAPTyp := Context.Find<TAPTyp>.where(M.APTyp.Bezeichnung = 'Anwesender').UniqueResult;

    for var LAnwesenderErgebnis in KontrollErgebnis.Anwesende do
    begin
      // Wenn die ID_PERSON leer ist, dann muss zunächst die Person gesucht, bzw. erzeugt werden
      var
      LPerson: TPerson := nil;
      if LAnwesenderErgebnis.PersonID.IsNull then
      begin
        // Gibt es die Person bereits? Wenn nicht eine neue Person anlegen
        // Wir suchen zur Sicherheit per Nachname plus EMail.
        // Die EMail MUSS existieren x@x.x
        if LAnwesenderErgebnis.Email.ValueOrDefault.Length > 4 then
        begin
          var
          LPersonen := Context.Find<TPerson>
            .where(M.Person.Nachname.ILike(LAnwesenderErgebnis.Nachname.Trim))
            .where(M.Person.Email.ILike(LAnwesenderErgebnis.Email.ValueOrDefault.Trim)).List;
          // List -> wir können bereits Duplikate/Leichen haben!
          if LPersonen.Count > 0 then
          begin
            LPerson := LPersonen.First;
          end;
        end;
        // Nichts gefunden, neue Person anlegen
        if LPerson = nil then
        begin
          LPerson := TPerson.Create;
        end;
      end
      else
      begin
        // Gibt es die angebene Person/PersonID?
        LPerson := Context.Find<TPerson>(LAnwesenderErgebnis.PersonID);
        if LPerson = nil then
          raise EXDataHttpException.CreateFmt(404, 'Anwesende Person %d nicht gefunden!',
            [LAnwesenderErgebnis.PersonID.ValueOrDefault]);
      end;

      // Personendaten updaten
      LPerson.Titel := LAnwesenderErgebnis.Titel;
      LPerson.Anrede := LAnwesenderErgebnis.Anrede;

      // Fallback für alte Version, die NUR NAME übermittelt
      if (LAnwesenderErgebnis.Name > '') and (LAnwesenderErgebnis.Nachname = '') then
      begin
        // Todo: Nachname Vorname automatisch trennen?
        LAnwesenderErgebnis.Nachname := LAnwesenderErgebnis.Name;
      end;

      LPerson.Vorname := LAnwesenderErgebnis.Vorname;
      LPerson.Nachname := LAnwesenderErgebnis.Nachname;
      LPerson.Telefon := LAnwesenderErgebnis.Telefon;
      LPerson.Email := LAnwesenderErgebnis.Email;
      Context.SaveOrUpdate(LPerson);
      LAnwesenderErgebnis.PersonID := LPerson.Id;
      Assert(LAnwesenderErgebnis.PersonID.HasValue);

      // Anwesenden erzeugen (für die Kontrolle)
      var
      LAnwesender := TAnwesender.Create;
      LAnwesender.kontrollbericht := LBericht;
      if LAnwesenderErgebnis.APTyp = nil then
      begin
        LAnwesenderErgebnis.APTyp := LDefaultAPTyp;
        LAnwesender.APTyp := LDefaultAPTyp;
      end
      else
      begin
        LAnwesender.APTyp := Context.Find<TAPTyp>(LAnwesenderErgebnis.APTyp.Id);
        if LAnwesender.APTyp = nil then
          raise EXDataHttpException.CreateFmt(404, 'Kontakt.APTyp %s nicht gefunden!',
            [LAnwesenderErgebnis.APTyp.Id.ToString]);
      end;
      LAnwesender.Kommunikationsberechtigt := LAnwesenderErgebnis.Kommunikationsberechtigt;
      // Person zuordnen
      LAnwesender.Person := LPerson;
      // zusätzlich (redundant) Name/Mail direkt vermerken
      LAnwesender.Name := LPerson.Fullname;
      LAnwesender.Email := LAnwesenderErgebnis.Email;

      // Es muss mindestens ein Name vorhanden sein
      if LPerson.Fullname.Trim = '' then
        raise EXDataHttpException.Create(497, 'Anwesende ohne Name sind nicht erlaubt!');

      Context.Save(LAnwesender);

      // Ansprechpartner prüfen/erzeugen (für den Betrieb)
      var
      LAnsprechpartner := Context.Find<TAnsprechpartner>
        .where(M.Ansprechpartner.RegNr = LBericht.Betrieb.Registrierung.RegNr)
        .where(M.Ansprechpartner.Person.Id = LAnwesenderErgebnis.PersonID).UniqueResult;
      if LAnsprechpartner = nil then
      begin
        LAnsprechpartner := TAnsprechpartner.Create;
        LAnsprechpartner.RegNr := LBericht.Betrieb.Registrierung.RegNr;
        LAnsprechpartner.Person := LPerson;
      end;

      LAnsprechpartner.Kommunikationsberechtigt := LAnwesenderErgebnis.Kommunikationsberechtigt;
      LAnsprechpartner.APTyp := LAnwesender.APTyp;
      Context.SaveOrUpdate(LAnsprechpartner);
    end;

    if LBericht.Rechtsgrundlage.UnterschriftErforderlich then
    begin
      if (KontrollErgebnis.Unterschriften.Anwesender = nil) and
        (KontrollErgebnis.Unterschriften.Verweigerungsgrund.ValueOrDefault = '') then
        raise EXDataHttpException.Create(422,
          'Es muss entweder eine Unterschrift eines Anwesenden vorliegen ODER ein Verweigerungsgrund angegeben sein!');

      if (KontrollErgebnis.Unterschriften.Anwesender <> nil) and
        (KontrollErgebnis.Unterschriften.Verweigerungsgrund.ValueOrDefault > '') then
        raise EXDataHttpException.Create(422,
          'Wenn eine Unterschrift eines Anwesenden vorliegt, darf kein Verweigerungsgrund angegeben sein!');

      // Unterschriften
      if KontrollErgebnis.Unterschriften.Anwesender <> nil then
      begin
        LBericht.UnterschriftAnwesenderBetrieb := ELKE.Classes.Generated.TUnterschrift.Create;
        LBericht.UnterschriftAnwesenderBetrieb.Bild.AsBytes := KontrollErgebnis.Unterschriften.Anwesender.Bild;
        LBericht.UnterschriftAnwesenderBetrieb.Name := KontrollErgebnis.Unterschriften.Anwesender.Name;
        LBericht.UnterschriftAnwesenderBetrieb.IdPerson := KontrollErgebnis.Unterschriften.Anwesender.IdPerson;
        LDatum := KontrollErgebnis.Unterschriften.Anwesender.Datum;
        if (LDatum = 0) or (LDatum > (now + 5 * OneMinute)) then
        begin
          // Hier loggen wir nur!
          ELKELog('Ungültiges Unterschriftsdatum (Anwesender)! ' + FormatDateTime('yyyy.mm.dd hh:mm:ss', LDatum));
        end;
        LBericht.UnterschriftAnwesenderBetrieb.Datum := KontrollErgebnis.Unterschriften.Anwesender.Datum
      end
      else
      begin
        // Wenn die Unterschrift des Anwesenden = nil ist, dann wurde Unterschrift verweigert
        LBericht.VerweigerungsgrundUnterschrift := KontrollErgebnis.Unterschriften.Verweigerungsgrund;
      end;
      // Das Kontrollorgan muss immer unterschreiben. auf <> nil wird am Anfang geprüft
      LBericht.UnterschriftKontrollorgan := ELKE.Classes.Generated.TUnterschrift.Create;
      LBericht.UnterschriftKontrollorgan.Bild.AsBytes := KontrollErgebnis.Unterschriften.Kontrollorgan.Bild;
      LBericht.UnterschriftKontrollorgan.Name := KontrollErgebnis.Unterschriften.Kontrollorgan.Name;
      LBericht.UnterschriftKontrollorgan.IdPerson := KontrollErgebnis.Unterschriften.Kontrollorgan.IdPerson;
      LDatum := KontrollErgebnis.Unterschriften.Kontrollorgan.Datum;
      if (LDatum = 0) or (LDatum > (now + 5 * OneMinute)) then
        // Beim Kontrollorgan MUSS die Unterschrift passen, daher eine Fehlermeldung
        raise EXDataHttpException.Create(422, 'Ungültiges Unterschriftsdatum (Kontrollorgan)!');
      LBericht.UnterschriftKontrollorgan.Datum := KontrollErgebnis.Unterschriften.Kontrollorgan.Datum;
    end;
    // Bemerkung
    var
    LBemerkung := KontrollErgebnis.Kurzbemerkung.ValueOrDefault.Trim;
    if LBemerkung > '' then
    begin
      LBericht.Kurzbemerkung := LBemerkung;
    end
    else
    begin
      LBericht.Kurzbemerkung := sNull;
    end;

    // Interne Notiz
    var
    LInterneNotiz := KontrollErgebnis.InterneNotiz.ValueOrDefault.Trim;
    if LInterneNotiz > '' then
    begin
      LBericht.InterneNotiz := LInterneNotiz;
    end
    else
    begin
      LBericht.InterneNotiz := sNull;
    end;

    // Übermittelte JSON Daten der Kontrolle als "Beweis" in der DB ablegen
    var
    LÜbermittelteDaten := TDokument.Create;
    LÜbermittelteDaten.BLDCode := User.Bundesland.BLDCode;
    LÜbermittelteDaten.Bezeichnung := 'JSON ' + LBericht.Id.ToString;
    LÜbermittelteDaten.Typ := 'JSON';
    LÜbermittelteDaten.Dateiname := LBericht.Id.ToString + '.json';
    if TXDataOperationContext.Current <> nil then
    begin
      LÜbermittelteDaten.Dokument.AsBytes := TXDataOperationContext.Current.Request.Content;
    end
    else
    begin
      LÜbermittelteDaten.Dokument.AsString := 'REPLAY FROM LOG';
    end;
    Context.Save(LÜbermittelteDaten);

    Context.Flush;
    LTransaction.Commit;
    result := LBericht.Id;
  except
    on e: Exception do
    begin
      LTransaction.Rollback;
      ELKELogError('Fehler beim Beenden von Kontrolle ' + LBerichtId.ToString + ' ' + e.Message);
      raise;
    end;
  end;
end;

function TMe.KontrollberichteErstellen(KontrollberichtID: integer): TErstellteKontrollberichte;
begin
  // PDF-Berichte erzeugen und mailen
  var
  LBericht := Context.Find<TKontrollbericht>(KontrollberichtID);
  if LBericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle nicht gefunden!');
  ELKELog('Kontroll-PDF wird erstellt: ' + LBericht.Id.ToString);

  if not LBericht.Beendet then
    raise EXDataHttpForbidden.Create
      ('Kontrolle ist noch nicht beendet, Berichte können nicht erstellt werden!');

  // PDF-Berichte erstellen und per Mail versenden
  // (Die Berichte werden im Context einer klassischen TFDConnection/Query erstellt.)
  try
    TModuleReports.PdfBerichteVerarbeiten(LBericht, User);

    // Zusammenfassung der Dokumente erstellen
    result := TErstellteKontrollberichte.Create(LBericht);
    // Hier noch ein Flush, weil im LBericht die PDF Referenzen eingehängt werden
    Context.Flush;
  except
    on e: Exception do
    begin
      raise EXDataHttpException.Create(422, 'Kontrollberichte konnten nicht erstellt werden: '#13#10 + e.Message);
    end;
  end;
end;

function TMe.KontrollberichteNeuErstellen: TNeuErstellteKontrollberichte;
begin
  result := TNeuErstellteKontrollberichte.Create;

  // Alle PDFs neu erstellen für die Kontrollen wo Dokument oder DokumentCC null ist
  // ('G', 'V', 'O', 'S', 'E')

  var
  LManager := Context;

  var
  LSQLSelect :=
    '  select K.ID'#13#10
    + '  from BEWEGUNGSDATEN.KONTROLLBERICHT K'#13#10
    + '           inner join BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS S'#13#10
    + '                      on K.ID = S.ID and S.STATUS in (''G'', ''V'', ''O'', ''S'', ''E'')'#13#10
    + '';

  // Erst die "normalen" Kontrollberichte (KB) prüfen
  var
  LSQL := LSQLSelect
    + '  where'#13#10
    + '  (K.GUID_DOKUMENT is null and (K.BKBTYP + K.KONTROLLTYP in (select BKBTYP + KONTROLLTYP'#13#10
    + '                                                                from STAMMDATEN.KONTROLLTYPEN_REPORTS'#13#10
    + '                                                                where REPORT_TYP = ''KB'')))'#13#10
    + '';

  var
  LExpression := LManager.Find<TKontrollbericht>
    .where(Linq.SQL('{ID} in (' + LSQL + ' )'));
  var
  LKontrollen := LExpression.List;

  ELKELog('Überprüfe %d neu zu erstellende PDF Berichte ...', [LKontrollen.Count]);

  for var LKontrolle in LKontrollen do
  begin
    if LKontrolle.Dokument = nil then
    begin
      try
        TModuleReports.PdfKontrollberichtErzeugenUndSpeichern(LKontrolle, User);
        LManager.Flush;
        LManager.Refresh(LKontrolle);
        result.Kontrollberichte.Add(TKontrollPDF.Create(LKontrolle.Id, LKontrolle.Dokument.GUID));
      except
        on e: Exception do
        begin
          ELKELog('Kontrollbericht [%d] PDF konnte nicht neu erstellt werden: %s', [LKontrolle.Id, e.Message],
            TLogLevel.Error);
        end;
      end;
    end;
  end;

  // Nun die CC-relevanten Berichte (CC) prüfen
  LSQL := LSQLSelect
    + '  where'#13#10
    + '  (K.GUID_DOKUMENT_CC is null and (K.BKBTYP + K.KONTROLLTYP in (select BKBTYP + KONTROLLTYP'#13#10
    + '                                                                from STAMMDATEN.KONTROLLTYPEN_REPORTS'#13#10
    + '                                                                where REPORT_TYP = ''CC'')))'#13#10
    + '';

  LExpression := LManager.Find<TKontrollbericht>
    .where(Linq.SQL('{ID} in (' + LSQL + ' )'));
  LKontrollen := LExpression.List;

  ELKELog('Überprüfe %d neu zu erstellende CC-PDF Berichte ...', [LKontrollen.Count]);
  for var LKontrolle in LKontrollen do
  begin
    if LKontrolle.DokumentCC = nil then
    begin
      try
        TModuleReports.PdfKontrollberichtCCErzeugenUndSpeichern(LKontrolle, User);
        LManager.Flush;
        LManager.Refresh(LKontrolle);
        result.KontrollBerichteCC.Add(TKontrollPDF.Create(LKontrolle.Id, LKontrolle.DokumentCC.GUID));
      except
        on e: Exception do
        begin
          ELKELog('Kontrollbericht [%d] PDF konnte nicht neu erstellt werden: %s', [e.Message, LKontrolle.Id],
            TLogLevel.Error);
        end;
      end;
    end;
  end;

end;

procedure TMe.kontrollberichtSpeichern(kontrollbericht: TKontrollbericht);
begin
  // Todo: Der eigentliche Code sollte hier hin
  self.kontrollbericht(kontrollbericht);
end;

function TMe.Kontrollen(
  Page: integer = 1;
  PageSize: integer = 10
  ): TList<TKontrollbericht>;
begin
  // Geplante Kontrollen des Users:
  // + Nur User
  // - Keine Gruppen
  // + Status = P
  result := KontrollenForUserNeu(User, 'P', TGroupLevel.OhneGruppen, -1, -1, Page, PageSize);
end;

function TMe.KontrollenForUserNeu(AUser: TUser; AStatus: string; ALevel: TGroupLevel; DatumVon: TDateTime = -1;
  DatumBis: TDateTime = -1; Page: integer = 1; PageSize: integer = MaxInt; AFetchDetails: Boolean = false; AAutoExpand:
  Boolean = true; AIncludeTodos: Boolean = true):
  TList<TKontrollbericht>;
var
  LStatus: TArray<Variant>;
  LGroupLevel: integer;
begin
  ELKELog('Kontrollen...');

  var LOldExpands := GetExpandsFromRequest;
  ELKELog('Incomming $expands: ' + string.Join(',', LOldExpands));
  // ClearExpands;

  if DatumBis = -1 then
  begin
    DatumBis := Today + 365 * 3;
  end;
  if DatumVon = -1 then
  begin
    DatumVon := Today - 365 * 3;
  end;

  AStatus := AStatus.Trim;
  // Status = leer, dann alle Stati anzeigen
  if AStatus = '' then
  begin
    AStatus := STATUS_ALLE;
  end;

  LStatus := StatusToArray(AStatus);

  case ALevel of
    TGroupLevel.OhneGruppen:
      LGroupLevel := 0;
    TGroupLevel.MitDirektenGruppen:
      LGroupLevel := 1;
    TGroupLevel.MitAllenUntergruppen:
      LGroupLevel := GROUP_LEVEL_ALLE;
  else
    begin
      raise Exception.Create('Unknown Group Level');
    end;
  end;
  var
  LUserId := AUser.Id;
{$IFDEF DEBUG}
  if GDebugUserId > 0 then
  begin
    LUserId := GDebugUserId;
  end;
{$ENDIF}
  ELKELog('Kontrollen für User %d werden abgerufen...', [LUserId], TLogLevel.Trace);

  var LQuery := Context.Find<TvUserKontrollen>
  // Basis-Daten
  .FetchEager('Kontrolle.Kontrolltyp.Bkbtyp.Kontrolltypen')
    .FetchEager('Kontrolle.Betrieb.Registrierung')
    .FetchEager('Kontrolle.Betrieb.Adresse.Gemeinde')
    .FetchEager('Kontrolle.Rechtsgrundlage')
    .FetchEager('Kontrolle.Erfasser')
    .FetchEager('Kontrolle.Kontrollorgan')
    .FetchEager('Kontrolle.GruppeQuelle');

  if AAutoExpand then
  begin
    AddExpand('Kontrolltyp.Bkbtyp.Kontrolltypen');
    AddExpand('Betrieb.Registrierung');
    AddExpand('Betrieb.Adresse.Gemeinde');
    AddExpand('Rechtsgrundlage');
    AddExpand('Erfasser');
    AddExpand('Kontrollorgan');
    AddExpand('GruppeQuelle');
  end;

  var LUserKontrollen := LQuery
  .where(M.vUserKontrollen.IdUser = LUserId)
    .where(M.vUserKontrollen.Status._In(LStatus))
    .where(M.vUserKontrollen.GruppeLevel <= LGroupLevel)
    .where((M.vUserKontrollen.EffectiveDate >= DatumVon) or (M.vUserKontrollen.EffectiveDate.IsNull))
    .where((M.vUserKontrollen.EffectiveDate <= DatumBis) or (M.vUserKontrollen.EffectiveDate.IsNull))
    .List;
  ManagedObjects.Add(LUserKontrollen);

  TListHelper<TvUserKontrollen>.RemoveDuplicates(LUserKontrollen);

  //listen, die zu den Kontrollen gehören ,gezielt mit je einer Abfrage abholen.
  //Wenn man das nicht so macht, enstehen ganz viele Einzelabfragen im weiteren Verlauf

  //Zunächst alle KontrollIDs ermitteln
  var LKontrollIds: TArray<Variant>;

  SetLength(LKontrollIds, LUserKontrollen.Count);
  for var i := 0 to LUserKontrollen.Count - 1 do
  begin
    LKontrollIds[i] := LUserKontrollen[i].Kontrolle.Id;
  end;

  if (LUserKontrollen.Count > 0) and (AIncludeTodos or AFetchDetails) then
  begin
    //AddExpand('Todos'); HIER KEIN EXPAND! Der aufrufende Endpunkt kann die Kontrolle auf einer anderen Ebene einbinden!
    var LTodos := Context
    .Find<TTodo>
      .Add(Linq['Kontrollbericht.Id']._In(LKontrollIds))
      .List;
    ManagedObjects.Add(LTodos);

    //Explizite, erneute Abfrage  vehindern
    for var LTodo in LTodos do
    begin
      for var LKontrolle in LUserKontrollen do
      begin
        if LTodo.kontrollbericht.Id = LKontrolle.Kontrolle.Id then
        begin
          LKontrolle.Kontrolle.Todos[0] := LTodo;
          break;
        end;
      end;
    end;
  end;

  //Wenn $expand angegeben ist, dann holen wir hier alle details. Das dröseln wir später noch gezielt auf.
  if (LUserKontrollen.Count > 0) and (AFetchDetails or (Length(LOldExpands) > 0)) then
  begin

    //Bewertungen gibt es nur bei fertigen Kontrollen
    //D.h. NICHT STATUS_OFFEN

    if not IsInStatus(AStatus, STATUS_OFFEN) then
    begin

      //Bewertete Fragen
//      if AAutoExpand then
//      begin
//        AddExpand('BewerteteFragen.Mangel.Massnahme');
//        AddExpand('BewerteteFragen.Mangel.MangelTyp');
//        AddExpand('BewerteteFragen.Frage');
//      end;
      var LBewerteteFragen := Context
      .Find<TBewerteteFrage>
        .Add(Linq['Bericht.Id']._In(LKontrollIds))
        .FetchEager('Mangel.MangelTyp')
        .FetchEager('Mangel.Massnahme')
        .FetchEager('Frage')
        .List;
      ManagedObjects.Add(LBewerteteFragen);

      //Proben
//      if AAutoExpand then
//      begin
//        AddExpand('Proben.Dokument');
//        AddExpand('Proben.AgesErgebnisbericht');
//      end;
      var LProben := Context
      .Find<TKBProbe>
        .Add(Linq['Kontrollbericht.Id']._In(LKontrollIds))
        .FetchEager('Dokument')
        .FetchEager('AgesErgebnisbericht')
        .List;
      ManagedObjects.Add(LProben);

      //Explizite, erneute Abfrage  vehindern
      for var LKontrolle in LUserKontrollen do
      begin
        //Bewertete Fragen

        for var LFrage in LBewerteteFragen do
        begin
          if LFrage.Bericht.Id = LKontrolle.Kontrolle.Id then
          begin
            LKontrolle.Kontrolle.BewerteteFragen.Add(LFrage);
          end;
        end;

        //Proben
        for var LProbe in LProben do
        begin
          if LProbe.kontrollbericht.Id = LKontrolle.Kontrolle.Id then
          begin
            LKontrolle.Kontrolle.Proben.Add(LProbe);
          end;
        end;
      end;
    end;

  end;

  ELKELog('Ingesamt %d Kontrollen werden aufbereitet...', [LUserKontrollen.Count], TLogLevel.Trace);
  result := TList<TKontrollbericht>.Create;
  for var LUserKontrolle in LUserKontrollen do
  begin
    // Wir sortieren hier alle ungeplanten Kontrollen aus, die kein Todo haben.
    // Wir hatten am 23.10.2023 einen Fehler in den Daten, der eine entsprechende Inkonsistenz erzeugt hat.
    if (LUserKontrolle.Status <> 'U') or (LUserKontrolle.Status = 'U') and
      (LUserKontrolle.Kontrolle.TodoCount > 0) then
    begin
      // Kontrolle in den Result-Set übernehmen

     // Wichtig: Der Sort-Wert kann nur in VUserKontrollen berechnet werden und muss hier für die aktuelle Darstellung
     // in die Kontrolle übernommen werden. Kontrolle.Sort ist transient, wird also NICHT in der DB persistiert
      LUserKontrolle.Kontrolle.Sort := LUserKontrolle.Sort;

      result.Add(LUserKontrolle.Kontrolle);
    end;

  end;

  if result.Count < LUserKontrollen.Count then
  begin
    ELKELog('Ungeplante Kontrollen ohne Todo aussortiert: ' + (LUserKontrollen.Count - result.Count).ToString);
  end;
  var LCountIncludingDuplicates := result.Count;
  TListHelper<TKontrollbericht>.RemoveDuplicates(result);
  if result.Count < LCountIncludingDuplicates then
  begin
    ELKELog('Duplikate aussortiert: %d', [LCountIncludingDuplicates - result.Count]);
  end;

  result.Sort(TKontrollbericht.ComparerDefault);

  // Hier werden Informationen über das Paging als Header übergeben.
  if TXDataOperationContext.Current <> nil then
  begin
    TXDataOperationContext.Current.Response.Headers.SetValue('X-Records-Total', result.Count.ToString);
    TXDataOperationContext.Current.Response.Headers.SetValue('X-Records-Page', Page.ToString);
    TXDataOperationContext.Current.Response.Headers.SetValue('X-Records-Page-Size', PageSize.ToString);
  end;
  var
  LTotalCount := result.Count;
  var
  LTotalPages := TListHelper<TKontrollbericht>.Paginate(result, Page, PageSize);
  if TXDataOperationContext.Current <> nil then
  begin
    TXDataOperationContext.Current.Response.Headers.SetValue('X-Records', result.Count.ToString);
    TXDataOperationContext.Current.Response.Headers.SetValue('X-Records-Page-Max', LTotalPages.ToString);
  end;

  var LNewExpands := GetExpandsFromRequest;
  ELKELog('Generated $expands: ' + string.Join(',', LNewExpands));

  Assert(result.Count <= PageSize, 'PageSize <> result.Count');
  ELKELog('Kontrollen: User [%d] / Status [%s] / Group Level [%d] / Count [%d] / Page [%d] / PageSize [%d] / Total Pages [%d] / Total Count [%d]',
    [User.Id, AStatus, LGroupLevel, result.Count, Page, PageSize, LTotalPages, LTotalCount]);
end;

function TMe.KontrollenFuerBetrieb(BetriebId: integer; BkbTyp: string): TObjectList<TKontrollberichtKurz>;
begin
  //Betrieb prüfen
  var LBetrieb := Context
  .Find<TBetrieb>
    .where(M.Betrieb.Id = BetriebId)
    .UniqueResult;
  if LBetrieb = nil then
    raise EXDataHttpException.Create(404, 'Betrieb nicht gefunden!');

  result := TObjectList<TKontrollberichtKurz>.Create;

  var LKontrollBerichte := Context
  .Find<TKontrollbericht>
    .where(M.kontrollbericht.Betrieb.Id = LBetrieb.Id)
    .where(M.kontrollbericht.KontrollTyp.BkbTyp.Typ = BkbTyp)
    .where(M.kontrollbericht.Endezeit.IsNotNull) // Nur bereits durchgeführte Kontrollen
  .FetchEager('Betrieb')
    .FetchEager('Kontrolltyp')
    .FetchEager('Dokument')
    .List;

  ManagedObjects.Add(LKontrollBerichte);

  for var LBericht in LKontrollBerichte do
  begin
    var
    LBerichtKurz := TKontrollberichtKurz.Create;
    LBerichtKurz.Id := LBericht.Id;
    LBerichtKurz.Bkb := LBericht.Bkb;
    LBerichtKurz.KontrollTyp := LBericht.KontrollTyp;
    LBerichtKurz.KontrollDatum := LBericht.Datum;
    LBerichtKurz.Status := LBericht.Status;
    LBerichtKurz.KontrollInformationen := LBericht.KontrollInformationen;
    if LBericht.Dokument <> nil then
    begin
      LBerichtKurz.GUIDKontrollberichtPDF := LBericht.Dokument.GUID;
    end
    else
    begin
      LBerichtKurz.GUIDKontrollberichtPDF := sNull;
    end;

    result.Add(LBerichtKurz);
  end;

  if result.Count = 0 then
    raise EXDataHttpException.Create(404,
      'Für den Betrieb liegen bisher keine durchgeführten Kontrollen vom Typ "' + BkbTyp + '" vor!');
end;

function TMe.OffeneKontrollenFuerBetrieb(BetriebId: integer): TObjectList<TKontrollberichtKurz>;
var
  LTodos: TObjectList<TTodo>;

  function FindTodo(const ABerichtId: integer): TTodo;
  begin
    for var Todo in LTodos do
      if Assigned(Todo.kontrollbericht) and (Todo.kontrollbericht.Id = ABerichtId) then
        Exit(Todo);
    result := nil; // Falls nichts gefunden wird
  end;

begin
  ELKELog('Offene Kontrollen: Betrieb [%d]', [BetriebId]);

  TXDataOperationContext.Current.Request.Headers.SetValue('xdata-expand-level', '3');

  //Betrieb prüfen
  var LBetrieb := Context
  .Find<TBetrieb>
    .where(M.Betrieb.Id = BetriebId)
    .UniqueResult;
  if LBetrieb = nil then
    raise EXDataHttpException.Create(404, 'Betrieb nicht gefunden!');

  //Rollen und BkbTypen vorladen - die werden sonst später einzeln nachgeladen
  LadeRollenBkbTypen;

  //Kontrollen des Betriebs gezielt abholen
  var LKontrollen := Context
  .Find<TKontrollbericht>
    .where(M.kontrollbericht.Betrieb.Id = BetriebId)
    .Add(Linq['Status']._In(StatusToArray(STATUS_OFFEN))) //nur offene
  //.FetchEager('Todos') --> das dauert lange
  .FetchEager('KontrollTyp')
    .FetchEager('KontrollTyp.Bkbtyp')
    .List;

  //Das geht schnell, da nur eine SQL Abfrage entsteht und trotzdem alle Todos abgeholt werden
  //Die Todos solten 1:1 mit Kontrollbericht sein, daher verrenken wir uns hier etwas
  var LKontrollIds: TArray<Variant>;
  SetLength(LKontrollIds, LKontrollen.Count);
  for var i := 0 to LKontrollen.Count - 1 do
  begin
    LKontrollIds[i] := LKontrollen[i].Id;
  end;

  if Length(LKontrollIds) > 0 then
  begin
    LTodos := Context
      .Find<TTodo>
      .Add(Linq['Kontrollbericht.Id']._In(LKontrollIds))
      .List;
  end;

  //Kurze Übersicht aufbauen
  result := TObjectList<TKontrollberichtKurz>.Create;

  AddExpand('KontrollTyp.BkbTyp');
  for var LBericht in LKontrollen do
  begin
    // Gffs. Fälligkeit ermitteln
    var LTodo: TTodo;
    var LFaelligBis: Nullable<TDate>;
    LFaelligBis := sNull;

    LTodo := FindTodo(LBericht.Id);
    if Assigned(LTodo) then
    begin
      LFaelligBis := Trunc(LTodo.Faellig.ValueOrDefault);
    end;

    // Planungsdatum oder fällig bis Ende diesen Jahres
    if (LBericht.Datum.ValueOrDefault < EndOfTheYear(Today) + 1) and (LBericht.Datum.ValueOrDefault > 0)
      or
      (LFaelligBis.HasValue) and (LFaelligBis < EndOfTheYear(Today) + 1) then
    begin
      // Nur offene Kontrollen  - die der User sehen darf
      if (LBericht.Endezeit.ValueOrDefault = 0) and (LBericht.istSichtbarFuerUser(User)) then
      begin
        var LBerichtKurz := TKontrollberichtKurz.Create;
        LBerichtKurz.Id := LBericht.Id;
        LBerichtKurz.Bkb := LBericht.Bkb;
        LBerichtKurz.KontrollTyp := LBericht.KontrollTyp;
        LBerichtKurz.KontrollDatum := LBericht.Datum;
        LBerichtKurz.FaelligkeitsDatum := LFaelligBis;
        LBerichtKurz.Status := LBericht.Status;
        LBerichtKurz.KontrollInformationen := LBericht.KontrollInformationen;
        result.Add(LBerichtKurz);
      end;
    end;
  end;
  ELKELog('Offene Kontrollen: Betrieb [%d] / Count [%d] ', [BetriebId, result.Count]);
end;

procedure TMe.KontrolleStornieren(KontrollberichtGUID: TGuid;
  Stornogrund:
  string);
begin
  var
  LKontrollbericht := Context.Find<TKontrollbericht>
    .where(M.kontrollbericht.GUID = KontrollberichtGUID.ToString)
    .UniqueResult;

  if LKontrollbericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle nicht gefunden!');

  if not LKontrollbericht.istSichtbarFuerUser(User) then
    raise EXDataHttpUnauthorized.Create('Kein Zugriff auf die angegebene Kontrolle!');

  if CharInSet(LKontrollbericht.Status.ValueOrDefault[1], ['G', 'V', 'S']) then
    raise EXDataHttpForbidden.Create('Die angegebene Kontrolle kann nicht storniert werden!');

  LKontrollbericht.StorniertAm := now;
  LKontrollbericht.Endezeit := LKontrollbericht.StorniertAm;
  LKontrollbericht.Stornogrund := Stornogrund;
  LKontrollbericht.Kontrollorgan := User.Person;

  if LKontrollbericht.Kontrollorgan = nil then
    raise EXDataHttpException.Create(422, 'Kein Kontrollorgan angegeben!');

  // Wichtig: Flush zuerst, da KontrollberichteErstellen auf klassische DB Operationen zurück greift.
  Context.Flush;

  KontrollberichteErstellen(LKontrollbericht.Id);
end;

procedure TMe.KontrolleVerweigern(KontrollVerweigerung: TKontrollVerweigerung);
begin
  var
  LKontrollbericht := Context.Find<TKontrollbericht>
    .where(M.kontrollbericht.GUID = KontrollVerweigerung.KontrollberichtGUID.ToString)
    .UniqueResult;

  if LKontrollbericht = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle nicht gefunden!');

  if not LKontrollbericht.istSichtbarFuerUser(User) then
    raise EXDataHttpUnauthorized.Create('Kein Zugriff auf die angegebene Kontrolle!');

  if LKontrollbericht.Status.ValueOrDefault <> 'P' then
    raise EXDataHttpForbidden.Create('Die angegebene Kontrolle kann nicht verweigert werden!');

  LKontrollbericht.VerweigertAm := now;
  LKontrollbericht.Verweigerungsgrund := KontrollVerweigerung.Verweigerungsgrund;
  LKontrollbericht.Startzeit := KontrollVerweigerung.Startzeit;
  LKontrollbericht.Endezeit := LKontrollbericht.VerweigertAm;
  LKontrollbericht.Kontrollorgan := User.Person;

  // Bemerkung
  var
  LBemerkung := KontrollVerweigerung.Kurzbemerkung.ValueOrDefault.Trim;
  if LBemerkung > '' then
  begin
    LKontrollbericht.Kurzbemerkung := LBemerkung;
  end
  else
  begin
    LKontrollbericht.Kurzbemerkung := sNull;
  end;

  // Notiz
  var
  LInterneNotiz := KontrollVerweigerung.InterneNotiz.ValueOrDefault.Trim;
  if LInterneNotiz > '' then
  begin
    LKontrollbericht.InterneNotiz := LInterneNotiz;
  end
  else
  begin
    LKontrollbericht.InterneNotiz := sNull;
  end;

  // Anwesende sind vom Typ TAnsprechpartnerKontakt. Es wird für jeden Kontrollbericht
  // eine Liste der Anwesenden als TList<TAnwesender> in die Tabelle Bewegungsdaten.Anwesende gespeichert
  // Für jeden anwesenden Kontakt erzeugen wir
  // - einen TAnwesender
  // - einen TAnsprechpartner

  // Default-Funktion ist "Anwesender", wird gesetzt, wenn nichts übergeben wird
  var
  LDefaultAPTyp := Context.Find<TAPTyp>.where(M.APTyp.Bezeichnung = 'Anwesender').UniqueResult;

  for var LAnwesenderErgebnis in KontrollVerweigerung.Anwesende do
  begin
    // Wenn die ID_PERSON leer ist, dann muss zunächst die Person gesucht, bzw. erzeugt werden
    var
    LPerson: TPerson := nil;
    if LAnwesenderErgebnis.PersonID.IsNull then
    begin
      // Gibt es die Person bereits? Wenn nicht eine neue Person anlegen
      // Wir suchen zur Sicherheit per Nachname plus EMail.
      // Die EMail MUSS existieren x@x.x
      if LAnwesenderErgebnis.Email.ValueOrDefault.Length > 4 then
      begin
        var
        LPersonen := Context.Find<TPerson>
          .where(M.Person.Nachname.ILike(LAnwesenderErgebnis.Nachname.Trim))
          .where(M.Person.Email.ILike(LAnwesenderErgebnis.Email.ValueOrDefault.Trim)).List;
        // List -> wir können bereits Duplikate/Leichen haben!
        if LPersonen.Count > 0 then
        begin
          LPerson := LPersonen.First;
        end;
      end;
      // Nichts gefunden, neue Person anlegen
      if LPerson = nil then
      begin
        LPerson := TPerson.Create;
      end;
    end
    else
    begin
      // Gibt es die angebene Person/PersonID?
      LPerson := Context.Find<TPerson>(LAnwesenderErgebnis.PersonID);
      if LPerson = nil then
        raise EXDataHttpException.CreateFmt(404, 'Anwesende Person %d nicht gefunden!',
          [LAnwesenderErgebnis.PersonID.ValueOrDefault]);
    end;

    // Personendaten updaten
    LPerson.Titel := LAnwesenderErgebnis.Titel;
    LPerson.Anrede := LAnwesenderErgebnis.Anrede;

    // Fallback für alte Version, die NUR NAME übermittelt
    if (LAnwesenderErgebnis.Name > '') and (LAnwesenderErgebnis.Nachname = '') then
    begin
      // Todo: Nachname Vorname automatisch trennen?
      LAnwesenderErgebnis.Nachname := LAnwesenderErgebnis.Name;
    end;

    LPerson.Vorname := LAnwesenderErgebnis.Vorname;
    LPerson.Nachname := LAnwesenderErgebnis.Nachname;
    LPerson.Telefon := LAnwesenderErgebnis.Telefon;
    LPerson.Email := LAnwesenderErgebnis.Email;
    Context.SaveOrUpdate(LPerson);
    LAnwesenderErgebnis.PersonID := LPerson.Id;
    Assert(LAnwesenderErgebnis.PersonID.HasValue);

    // Anwesenden erzeugen (für die Kontrolle)
    var
    LAnwesender := TAnwesender.Create;
    LAnwesender.kontrollbericht := LKontrollbericht;
    if LAnwesenderErgebnis.APTyp = nil then
    begin
      LAnwesenderErgebnis.APTyp := LDefaultAPTyp;
      LAnwesender.APTyp := LDefaultAPTyp;
    end
    else
    begin
      LAnwesender.APTyp := Context.Find<TAPTyp>(LAnwesenderErgebnis.APTyp.Id);
      if LAnwesender.APTyp = nil then
        raise EXDataHttpException.CreateFmt(404, 'Kontakt.APTyp %s nicht gefunden!',
          [LAnwesenderErgebnis.APTyp.Id.ToString]);
    end;
    LAnwesender.Kommunikationsberechtigt := LAnwesenderErgebnis.Kommunikationsberechtigt;
    // Person zuordnen
    LAnwesender.Person := LPerson;
    // zusätzlich (redundant) Name/Mail direkt vermerken
    LAnwesender.Name := LPerson.Fullname;
    LAnwesender.Email := LAnwesenderErgebnis.Email;

    // Es muss mindestens ein Name vorhanden sein
    if LPerson.Fullname.Trim = '' then
      raise EXDataHttpException.Create(497, 'Anwesende ohne Name sind nicht erlaubt!');

    Context.Save(LAnwesender);

    // Ansprechpartner prüfen/erzeugen (für den Betrieb)
    var
    LAnsprechpartner := Context.Find<TAnsprechpartner>
      .where(M.Ansprechpartner.RegNr = LKontrollbericht.Betrieb.Registrierung.RegNr)
      .where(M.Ansprechpartner.Person.Id = LAnwesenderErgebnis.PersonID).UniqueResult;
    if LAnsprechpartner = nil then
    begin
      LAnsprechpartner := TAnsprechpartner.Create;
      LAnsprechpartner.RegNr := LKontrollbericht.Betrieb.Registrierung.RegNr;
      LAnsprechpartner.Person := LPerson;
    end;

    LAnsprechpartner.Kommunikationsberechtigt := LAnwesenderErgebnis.Kommunikationsberechtigt;
    LAnsprechpartner.APTyp := LAnwesender.APTyp;
    Context.SaveOrUpdate(LAnsprechpartner);
  end;

  // Unterschriften
  if KontrollVerweigerung.Unterschriften.Anwesender <> nil then
  begin
    LKontrollbericht.UnterschriftAnwesenderBetrieb := ELKE.Classes.Generated.TUnterschrift.Create;
    LKontrollbericht.UnterschriftAnwesenderBetrieb.Bild.AsBytes :=
      KontrollVerweigerung.Unterschriften.Anwesender.Bild;
    LKontrollbericht.UnterschriftAnwesenderBetrieb.Name := KontrollVerweigerung.Unterschriften.Anwesender.Name;
    LKontrollbericht.UnterschriftAnwesenderBetrieb.IdPerson :=
      KontrollVerweigerung.Unterschriften.Anwesender.IdPerson;
    var
    LDatum := KontrollVerweigerung.Unterschriften.Anwesender.Datum;
    if (LDatum = 0) or (LDatum > (now + 5 * OneMinute)) then
      raise EXDataHttpException.Create(422, 'Ungültiges Unterschriftsdatum (Anwesender)!');
    LKontrollbericht.UnterschriftAnwesenderBetrieb.Datum := KontrollVerweigerung.Unterschriften.Anwesender.Datum
  end
  else
  begin
    // Wenn die Unterschrift des Anwesenden = nil ist, dann wurde Unterschrift verweigert
    LKontrollbericht.VerweigerungsgrundUnterschrift := KontrollVerweigerung.Unterschriften.Verweigerungsgrund;
  end;
  // Das Kontrollorgan muss immer unterschreiben. auf <> nil wird am Anfang geprüft
  LKontrollbericht.UnterschriftKontrollorgan := ELKE.Classes.Generated.TUnterschrift.Create;
  LKontrollbericht.UnterschriftKontrollorgan.Bild.AsBytes :=
    KontrollVerweigerung.Unterschriften.Kontrollorgan.Bild;
  LKontrollbericht.UnterschriftKontrollorgan.Name := KontrollVerweigerung.Unterschriften.Kontrollorgan.Name;
  LKontrollbericht.UnterschriftKontrollorgan.IdPerson :=
    KontrollVerweigerung.Unterschriften.Kontrollorgan.IdPerson;
  var
  LDatum := KontrollVerweigerung.Unterschriften.Kontrollorgan.Datum;
  if (LDatum = 0) or (LDatum > (now + 5 * OneMinute)) then
    raise EXDataHttpException.Create(422, 'Ungültiges Unterschriftsdatum (Kontrollorgan)!');
  LKontrollbericht.UnterschriftKontrollorgan.Datum := KontrollVerweigerung.Unterschriften.Kontrollorgan.Datum;

  Context.Flush;
  ELKELog('Kontrolle %d wurde verweigert', [LKontrollbericht.Id]);
end;

function TMe.Kontrolltypen(
  const
  BkbTyp:
  string): TList<TKontrolltyp>;
begin

  result := Context.Find<TKontrolltyp>.where(M.KontrollTyp.BkbTyp.Typ = BkbTyp).List;
end;

function TMe.kontrollverlauf(Page: integer = 1; PageSize: integer = 10; DateAb: TDate = -1; DateBis: TDate = -1;
  IncludeDetails: Boolean = false; AutoExpand: Boolean = true; AIncludeTodos: Boolean = true): TList<TKontrollbericht>;
begin
  // Alle beendeten Kontrollen, die direkt dem User zugeordnet sind,
  // oder die in einer seiner Gruppen für den User sichtbar sind

  result := KontrollenForUserNeu(User, STATUS_BEENDET, TGroupLevel.MitDirektenGruppen, DateAb, DateBis, Page,
    PageSize, IncludeDetails, AutoExpand);
end;

function TMe.kontrollverlaufDetails(
  Page: integer = 1;
  PageSize: integer = 10): TList<TKontrolle>;
begin
  result := TList<TKontrolle>.Create;
  // Wir nehmen alle Berichte im Kontrollverlauf des Users und bauen das erweiterte TKontrolle Objekt auf.
  var LKontrollBerichte := kontrollverlauf(Page, PageSize, -1, -1, true, false, true);
  AddExpand('Bericht.Todos');
  for var LBericht in LKontrollBerichte do
  begin
    var
    LKontrolle := TKontrolle.Create(LBericht);
    ManagedObjects.Add(LKontrolle);
    result.Add(LKontrolle);
  end;
end;

function TMe.kontrollListe(Typ: TKontrollListenTyp): TList<TKontrollbericht>;
begin
  // TODO: Delete! Unused. Kept for backward comnpatibility
  ELKELog('Nicht implementierter Endpunkt wurde aufgerufen: "/kontrollListe"', TLogLevel.Warning);
  result := TList<TKontrollbericht>.Create;
end;

function TMe.KopiereCheckliste(ChecklistenId: integer): TCheckliste;
begin
  var
  LTransaction := Context.Connection.BeginTransaction;
  try
    var
    LChecklisteSource := Context.Find<TCheckliste>(ChecklistenId);
    if LChecklisteSource = nil then
      raise EXDataHttpException.Create(404, 'Checkliste nicht gefunden!');

    // Die Checkliste ansich kopieren
    var
    LChecklisteCopy := TCheckliste.Create;
    LChecklisteCopy.CopyFrom(LChecklisteSource);
    LChecklisteCopy.Id := 0;
    LChecklisteCopy.LastChangeUser := User;
    LChecklisteCopy.Version := LChecklisteSource.Version + 1;
    LChecklisteCopy.Versionstext := Copy('Kopie von ' + LChecklisteSource.Versionstext, 1, 255); // Max length
    LChecklisteCopy.Bezeichnung := Copy(LChecklisteCopy.Bezeichnung + ' [KOPIE]', 1, 100);

    // Prüfen, ob es version/Versionstext/Bezeichnung schon gibt. Wenn ja wird die Versionsnummer hochgezählt
    while ChecklisteExistiert(LChecklisteCopy.Version, LChecklisteCopy.Versionstext, LChecklisteCopy.Bezeichnung) do
    begin
      LChecklisteCopy.Version := LChecklisteCopy.Version + 1;
    end;
    LChecklisteCopy.GueltigAb := Trunc(LChecklisteSource.GueltigBis.ValueOrDefault + 1);
    LChecklisteCopy.Ccrelevant := LChecklisteSource.Ccrelevant;
    LChecklisteCopy.VisBkbtid := LChecklisteSource.VisBkbtid;
    // Besitzer aus User-BLD übernehmen
    LChecklisteCopy.BesitzerBundesland := User.Bundesland;
    // Alle Checklisten sind erstmal privat
    LChecklisteCopy.Privat := true;

    // Gültigkeit anschliessend an Original
    LChecklisteCopy.GueltigBis := IncYear(Today, 100); // 100 Jahre gültig

    // Die Kontrolltypen-Zuornung kopieren wir NICHT (mehr)!
    // Siehe https://esculenta.atlassian.net/browse/EIW-378

    // Die Zuordnung der Bundesländer der Quell-Checkliste ignorieren wir, da ja eine neue Checkliste entsteht,
    // die ja noch niemand anderes kennt.

    // NICHT explizit speichern! Wird bereits über LCKCopy referenziert und gespeichert.
    // Context.Save(LChecklisteCopy);

    // Die Fragen
    var
    LFragenMap := TDictionary<integer, TFrage>.Create;
    try
      for var LFrage in LChecklisteSource.Fragen do
      begin
        var
        LFrageCopy := TFrage.Create;
        LFrageCopy.CopyFrom(LFrage);
        LFrageCopy.Id := 0;
        LFrageCopy.BewerteteFragen.Clear;
        LFrageCopy.Ccrelevant := LFrage.Ccrelevant;

        // Die Bewertungen
        LFrageCopy.FragenBewertungen.Clear;
        for var LBewertung in LFrage.FragenBewertungen do
        begin
          var
          LBewertungCopy := TFrageBewertung.Create;
          LBewertungCopy.CopyFrom(LBewertung);
          LBewertungCopy.Frage := LFrageCopy;
          LBewertungCopy.Bewertung := LBewertung.Bewertung;
          Context.Save(LBewertungCopy);
        end;

        // Die Kontrollbereiche
        LFrageCopy.FragenKontrollbereiche.Clear;
        for var LKontrollbereich in LFrage.FragenKontrollbereiche do
        begin
          var
          LFrageKontrollbereich := TFrageKontrollbereich.Create;
          LFrageKontrollbereich.CopyFrom(LKontrollbereich);
          LFrageKontrollbereich.Frage := LFrageCopy;
          LFrageKontrollbereich.Kontrollbereich := LKontrollbereich.Kontrollbereich;
          Context.Save(LFrageKontrollbereich);
        end;

        LFrageCopy.UntergeordneteFragen.Clear;
        LFrageCopy.UebergeordneteFrage := nil;
        LChecklisteCopy.Fragen.Add(LFrageCopy);

        // Um die Hierarchie der Übergeordneten Fragen kopieren zu können, merken wir uns zunächst wo welche kopierte Frage herkommt
        LFragenMap.Add(LFrage.Id, LFrageCopy);
      end;

      // Nun sind alle Fragen kopiert und wir können die Hierarchie replizieren
      for var LSource in LChecklisteSource.Fragen do
      begin
        var
        LCopy := LFragenMap.Items[LSource.Id];
        if LSource.UebergeordneteFrage <> nil then
        begin
          LCopy.UebergeordneteFrage := LFragenMap.Items[LSource.UebergeordneteFrage.Id];
        end;
      end;

    finally
      FreeAndNil(LFragenMap);
    end;
    Context.Save(LChecklisteCopy);
    Context.Flush;
    result := LChecklisteCopy;
    LTransaction.Commit;
  except
    LTransaction.Rollback;
    raise;
  end;
end;

procedure TMe.LadeRollenBkbTypen;
begin
  ELKELog('LadeRollen', TLogLevel.Trace);
  var LRollen := Context.Find<TRolle>
  .List;
  var LBkbTypen := Context.Find<TBkbTyp>
  .List;
  var LRollenTypen := Context.Find<TRollenBkbtypen>
  .List;
  ELKELog('Rollen geladen', TLogLevel.Trace);
end;

procedure TMe.LoescheCheckliste(ChecklistenId: integer);
var
  LFragentoDelete: TList<TFrage>;

  // Trägt die zu löschenden Fragen in der richtigen Löschreinefolge in die LFragenToDelete ein
  procedure FindeFragen(AFragen: TList<TFrage>);
  var
    LFrage: TFrage;
  begin
    if Assigned(AFragen) then
    begin
      for LFrage in AFragen do
      begin
        FindeFragen(LFrage.UntergeordneteFragen);
        LFragentoDelete.Insert(0, LFrage);
      end;
    end;
  end;

begin

  var
  LTransaction := Context.Connection.BeginTransaction;
  try
    var
    LCheckliste := Context.Find<TCheckliste>(ChecklistenId);
    if LCheckliste = nil then
      raise EXDataHttpException.Create(404, 'Checkliste nicht gefunden!');

    // Prüfen, ob die Checkliste dem eigenen Bundesland gehört
    if LCheckliste.BesitzerBundesland.BLDCode <> User.Bundesland.BLDCode then
      raise EXDataHttpForbidden.CreateFmt
        (403, 'Die Checkliste gehört %s und kann daher nicht gelöscht werden!',
        [LCheckliste.BesitzerBundesland.Bezeichnung]);

    // Prüfen ob es bereits Bewertungen gibt
    for var LFrage in LCheckliste.Fragen do
    begin
      if LFrage.BewerteteFragen.Count > 0 then
      begin
        raise EXDataHttpForbidden.Create
          ('Die Checkliste hat bereits bewertete Frage und kann daher nicht gelöscht werden!');
      end;
    end;

    // Prüfen, ob diese Checkliste bereits von einem anderen Bundesland verwendet wird
    for var LSichtbareCheckliste in LCheckliste.ChecklistenKontrolltypen do
    begin
      for var LBundeslandKontrolltyp in LSichtbareCheckliste.BundeslaenderChecklistenKontrolltypen do
      begin
        if LBundeslandKontrolltyp.Bundesland.BLDCode <> User.Bundesland.BLDCode then
          raise EXDataHttpForbidden.CreateFmt
            (403, 'Die Checkliste wird von %s verwendet und kann daher nicht gelöscht werden!',
            [LCheckliste.BesitzerBundesland.Bezeichnung]);
      end;
    end;

    // Nun alle Fragen löschen
    LFragentoDelete := TList<TFrage>.Create;
    FindeFragen(LCheckliste.Fragen);
    for var LFrage in LFragentoDelete do
    begin
      if Context.IsAttached(LFrage) then
      begin
        Context.Remove(LFrage);
      end;
    end;

    // Zum Schluss die Checkliste an sich löschen
    Context.Remove(LCheckliste);

    Context.Flush;
    LTransaction.Commit;
  except
    LTransaction.Rollback;
    raise;
  end;
end;

function TMe.Mangeltypen(BkbTyp: string): TList<TMangeltyp>;
begin
  result := TList<TMangeltyp>.Create;
  var LBKB := Context
  .Find<TBkbTyp>
    .where(M.BkbTyp.Typ = BkbTyp)
    .FetchEager('MangelTypen.Massnahmenkatalog.Massnahmen')
    .UniqueResult;

  if LBKB = nil then
    raise EXDataHttpException.Create(422, 'Unbekannter BKBTyp!');
  result.AddRange(LBKB.Mangeltypen);
end;

function TMe.Me: TUser;
begin
  var
  LTransaction := Context.Connection.BeginTransaction;
  try
    result := User;
    // Die User/Rollen werden nur hier im /me Endpunkt aktualisiert.
    UpdateUserFromToken(result, false);
    UpdateRollen;
    // Person wird expandiert!
    result.Person;
    LTransaction.Commit;
  except
    on e: Exception do
    begin
      LTransaction.Rollback;
      if e is EXDataHttpException then
        raise
      else
        raise EXDataHttpUnauthorized.Create('Es wurde ein fehlerhaftes PVP-Token übermittelt! ' + e.Message);
    end;
  end;
end;

function TMe.NachrichtenForUser: TList<TNachricht>;
begin
  result := TList<TNachricht>.Create;

  var
  LNachrichten := Context.Find<TVnachrichtForUser>
    .where(M.VNachrichtForUser.User.Id = User.Id).List;

  for var LNachricht in LNachrichten do
  begin
    result.Add(LNachricht.Nachricht);
  end;
end;

function TMe.NachrichtenForUserKurz: TObjectList<TNachrichtKurz>;
begin
  result := TObjectList<TNachrichtKurz>.Create;

  var
  LNachrichtenForUser := Context.Find<TVnachrichtForUser>
    .where(M.VNachrichtForUser.User.Id = User.Id).List;

  try
    // Alle NachrichtenForUser, die erstmalig ausgeliefert werden, werden automatisch auf "gelesen" gesetzt
    FDMMain.NachrichtenMarkGesehen(User);
    // Hier werden die NachrichtForUser Objekte in TNachrichtKurz Objekte gewrappt.
    for var LNachrichtForUser in LNachrichtenForUser do
    begin
      var
      LNachrichtKurz := TNachrichtKurz.Create(self.User, LNachrichtForUser);
      result.Add(LNachrichtKurz);
    end;

    // Wir sortieren absteigen nach Datum, also die neuesten Nachrichten zuerst
    result.Sort(TNachrichtKurz.ComparerDatumDesc);
    Assert(LNachrichtenForUser.Count = result.Count);
  finally
    FreeAndNil(LNachrichtenForUser);
  end;

  Context.Flush;
  ELKELog('Nachrichten for user: ' + result.Count.ToString);
end;

procedure TMe.NachrichtGelesen(Id: integer);

  procedure RaiseNoAccess;
  begin
    raise EXDataHttpForbidden.Create('Kein Zugriff auf Nachricht mit der ID = ' + Id.ToString);
  end;

begin
  var
  LNachricht := Context.Find<TNachricht>(Id);
  if LNachricht = nil then
    RaiseNoAccess;
  var
  LFound := false;
  for var LNachrichtForUser in LNachricht.VNachrichtenUser do
  begin
    if LNachrichtForUser.User.Id = User.Id then
    begin
      LNachrichtForUser.Zustellung.GelesenTstamp := now;
      Context.SaveOrUpdate(LNachrichtForUser.Zustellung);
      Context.Flush;
      LFound := true;
      break;
    end;
  end;
  if not LFound then
    RaiseNoAccess;
end;

function TMe.NeuerKontrollBerichtInternal(Bericht: TKontrollbericht;
  AGeplant:
  Boolean;
  ANacherfassung:
  Boolean = false): integer;
var
  LFehlendeFelder: TStringList;
  LAdresse: TFreieAdresse;
  LBetrieb: TBetrieb;
begin

  if Bericht = nil then
    raise EXDataHttpException.Create(422, 'Fehlender Kontrollbericht');

  // Darf der User überhaupt Kontrollen erstellen. Dazu muss er in mindestens einer Gruppe Mitglied sein
  // https://esculenta.atlassian.net/browse/ERT-221
  if not User.Person.IsMemberOfAnyGroup then
    raise EXDataHttpException.Create(423, 'User ist nicht Mitglied einer Gruppe');

  // Pflichtfelder prüfen!
  LFehlendeFelder := TStringList.Create;
  try
    LFehlendeFelder.Delimiter := ',';

    if Bericht.KontrollTyp = nil then
    begin
      LFehlendeFelder.Add('Kontrolltyp');
    end
    else
    begin
      if Bericht.KontrollTyp.BkbTyp = nil then
        LFehlendeFelder.Add('Kontrolltyp.BKBTyp');
      if Bericht.KontrollTyp.KontrollTyp = '' then
        LFehlendeFelder.Add('Kontrolltyp.Kontrolltyp');
      var
      LKontrollTyp := Bericht.KontrollTyp;

      Bericht.KontrollTyp := Context.Find<TKontrolltyp>
        .where((M.KontrollTyp.KontrollTyp = LKontrollTyp.KontrollTyp)
        and (M.KontrollTyp.BkbTyp.Typ = LKontrollTyp.BkbTyp.Typ)).UniqueResult;

      if Bericht.KontrollTyp = nil then
        raise EXDataHttpException.Create(422, 'Unbekannter Kontrolltyp!');
    end;

    // Unbekannte Betriebe sind ausser bei Freien Niederschriften nicht erlaubt
    if (Bericht.Betrieb = nil) or ((Bericht.Betrieb.Id = 0) and not Bericht.Betrieb.Registrierung.FreierBetrieb) then
      LFehlendeFelder.Add('Betrieb');

    if Bericht.Rechtsgrundlage = nil then
      LFehlendeFelder.Add('Rechtsgrundlage');

    if Bericht.Probenziehung then
      LFehlendeFelder.Add('Probenziehung muss FALSE sein');

    if LFehlendeFelder.Count > 0 then
      raise EXDataHttpException.Create(422, 'Pflichtfelder fehlen: ' + LFehlendeFelder.DelimitedText);
  finally
    FreeAndNil(LFehlendeFelder);
  end;

  // Rechtsgrundlage
  Bericht.Rechtsgrundlage := Context.Find<TRechtsgrundlage>(Bericht.Rechtsgrundlage.Id);
  if Bericht.Rechtsgrundlage = nil then
    raise EXDataHttpException.Create(422, 'Ungültige Rechtsgrundlage');

  // Rechtsgrundlage zum Bkbtyp passend?
  var
  LRechtsGrundlageKorrekt := false;
  for var LBkbtype in Bericht.Rechtsgrundlage.Bkbtypen do
  begin
    if LBkbtype.BkbTyp = Bericht.KontrollTyp.BkbTyp then
    begin
      LRechtsGrundlageKorrekt := true;
      break
    end;
  end;
  if not LRechtsGrundlageKorrekt then
    raise EXDataHttpException.Create(422, 'Rechtsgrundlage passt nicht zum Bkbtyp!');

  // Ab hier diverse Standardfelder befüllen

  // Betrieb gefüllt oder nur ID angegeben (ID wird oben geprüft)?
  if Bericht.Betrieb.Name = '' then
  begin
    Bericht.Betrieb := Context.Find<TBetrieb>(Bericht.Betrieb.Id);
    if Bericht.Betrieb = nil then
      raise EXDataHttpException.Create(404, 'Betieb wurde nicht gefunden!');
  end;

  if AGeplant then
  begin
    // Geplante Kontrolle

    // Erfasser und Kontrollorgan auf Standard = User setzen, wenn nicht bereits eingestellt.
    if (Bericht.Erfasser = nil) then
    begin
      Bericht.Erfasser := User.Person;
    end;

    if (Bericht.Kontrollorgan = nil) then
    begin
      Bericht.Kontrollorgan := Bericht.Erfasser;
    end;

    if Bericht.Todos.Count > 0 then
      raise EXDataHttpException.Create(422, 'Geplante Kontrollen dürfen keine Todos haben!');
    // Berichte die keine Nacherfassung sind, dürfen nicht in der Vergangenheit liegen!
    if Bericht.Datum.IsNull or (not ANacherfassung and (Bericht.Datum < Today)) then
      raise EXDataHttpException.Create(422, 'Datum nicht ausgefüllt oder in der Vergangenheit!');
  end
  else
  begin
    // Ungeplante Kontrolle

    // Wenn keine Quellgruppe zugewiesen ist, dann muss zwingend ein Erfasser gesetzt werden
    // Ungeplante Kontrollen aus dem CSV Import kommen ohne Erfasser aber mit Quellgruppe!
    if (Bericht.Erfasser = nil) and (Bericht.GruppeQuelle = nil) then
    begin
      Bericht.Erfasser := User.Person;
    end;

    if Bericht.Datum.HasValue then
      raise EXDataHttpException.Create(422, 'Ungeplante Kontrollen dürfen kein Datum haben!');

    if Bericht.Todos.Count <> 1 then
      raise EXDataHttpException.Create(422, 'Ungeplante Kontrollen müssen genau ein Todo haben!');

    if (Bericht.Todos.First.Gruppe = nil) and (Bericht.Todos.First.User = nil) then
      raise EXDataHttpException.Create(422,
        'Ungeplante Kontrollen müssen ein Todo mit Gruppe oder User haben!');

    if (Bericht.Todos.First.Titel = '') or (Bericht.Todos.First.Faellig.IsNull) then
      raise EXDataHttpException.Create(422,
        'Das Todo einer ungeplanten Kontrolle muss einen Titel und ein Faellig-Datum haben!');

    if Bericht.Todos.First.Gruppe <> nil then
    begin
      Bericht.Todos.First.Gruppe := Context.Find<TGruppe>(Bericht.Todos.First.Gruppe.Id);
      if Bericht.Todos.First.Gruppe = nil then
        raise EXDataHttpException.Create(404, 'Gruppe des Todo nicht gefunden!');

      // Wenn das Todo eine Gruppe hat, dann ist das gleichzeitig die Quellgruppe
      if Bericht.GruppeQuelle = nil then
      begin
        Bericht.GruppeQuelle := Bericht.Todos.First.Gruppe;
      end;
    end;

    if Bericht.Todos.First.User <> nil then
    begin
      Bericht.Todos.First.User := Context.Find<TUser>(Bericht.Todos.First.User.Id);
      if Bericht.Todos.First.User = nil then
        raise EXDataHttpException.Create(404, 'User des Todo nicht gefunden!');
    end;
  end;

  // Ist die Quellgruppe bereits gefüllt? (bei ungeplanten kommt das aus IntraWeb)
  if Assigned(Bericht.GruppeQuelle) then
  begin
    Bericht.GruppeQuelle := Context.Find<TGruppe>(Bericht.GruppeQuelle.Id);
    Bericht.QuellGruppeValidieren;
  end
  else
  begin
    // Nicht befüllt, also die Standardgruppe einsetzen
    Bericht.QuellGruppeStandardSetzen;
  end;

  // Es muss eine Quellgruppe exisitieren
  if not Assigned(Bericht.GruppeQuelle) then
    raise EXDataHttpException.Create(422, 'Es konnte keine Quellgruppe zugewiesen werden!');

  // Fehlermeldung, wenn Uhrzeit ohne Datum vorliegt
  if Bericht.AngemeldetUm.HasValue and (Bericht.AngemeldetUm < 1) then
    raise EXDataHttpException.Create(422, 'AngemeldetUm enthält kein vollständiges Datum!');

  // Teilbetriebe werden über AMA CCK importiert und hat nur dort eine Relevanz.
  // Per default ist es immer ein Hauptbetrieb
  if Bericht.Betriebstyp.Trim = '' then
  begin
    Bericht.Betriebstyp := 'HB';
  end;

  // Wenn es eine ungeplante Kontrolle ist, dann wird die Person im todo auch als Kontrollorgan eingetragen
  if (Bericht.Kontrollorgan = nil) and (Bericht.Todos.Count > 0) and (Bericht.Todos.First.User <> nil) then
  begin
    // TODO: Eigentlich sollte HIER das Kontrollorgan NICHT gesetzt werden!! Nur wenn die Kontrolle startet ...
    Bericht.Kontrollorgan := Bericht.Todos.First.User.Person;
  end;

  Bericht.Startzeit := sNull;
  Bericht.Endezeit := sNull;

  // Prüfung auf freie Niederschrift / RegNr 9999999
  if (Bericht.Betrieb.Registrierung <> nil) and Bericht.Betrieb.Registrierung.FreierBetrieb then
  begin
    // freie Adresse anlegen bzw aus DB holen
    LAdresse := TFreieAdresse.Create;
    LAdresse.Strasse := Bericht.Betrieb.Adresse.Strasse;
    LAdresse.Ort := Bericht.Betrieb.Adresse.Ort;
    LAdresse.PLZ := Bericht.Betrieb.Adresse.PLZ;
    LAdresse.Landkz := User.Bundesland.Land.Landkz;

    // Freien Betrieb anlegen
    LBetrieb := TBetrieb.Create;
    LBetrieb.Registrierung := Context.Find<TRegistrierung>(FREIER_BETRIEB);
    LBetrieb.Bundesland := User.Bundesland;
    LBetrieb.Adresse := FreieAdresseHinzufuegen(LAdresse);
    LBetrieb.Name := Bericht.Betrieb.Name;
    Bericht.Betrieb := LBetrieb;
    Context.Save(LBetrieb);
  end
  else
  begin
    // Der Betrieb muss bekannt sein
    LBetrieb := Context.Find<TBetrieb>(Bericht.Betrieb.Id);
    if LBetrieb = nil then
      raise EXDataHttpException.Create(422, 'Der angegebene Betrieb wurde nicht gefunden!');
    // Wir schauen nur auf die ID - implizite Änderungen im Betrieb akzeptieren wir hier nicht.  Daher wird der
    // mitgelieferte Betrieb verworfen
    Bericht.Betrieb := LBetrieb;
  end;

  // Die BKBNummer zum Schluss generieren, um unnötige Löcher im Nummernkreislauf zu vermeiden
  // Vor der BKBNummer den Kontrollbericht in den Kontext legen, da sonst in NeueBkbNummer insbesondere der BKBTyp quasi
  // doppelt aus der DB geholt wird.

  if Bericht.Bkb = '' then
  begin
    Bericht.Bkb := NeueBKBNummer(Bericht.KontrollTyp.BkbTyp.Typ);
  end
  else
  begin
    // Wenn der Bericht bereits eine BKB Nummer enthält, dann MUSS diese vorher registriert worden sein!
    if Context.Find<TBKBNummer>(Bericht.Bkb) = nil then
      raise EXDataHttpException.Create(422, 'Die angegebene BKB Nummer ist unbekannt!');
  end;
  Context.SaveOrUpdate(Bericht);
  Context.Refresh(Bericht); // Um die GUID zu laden
  try
    Context.Flush;
    Assert(Assigned(Bericht), 'Bericht ist NIL');
    ELKELog('Bericht %d / %s / %s wurde erzeugt', [Bericht.Id, Bericht.Bkb, Bericht.GUID.ToString]);
    result := Bericht.Id;
  except
    ELKELog('Erstellen des Berichts fehlgeschlagen.');
    result := -1
  end;
end;

function TMe.NeueBKBNummer(BkbTyp: string): string;
begin
  var LNeueNummer := TBKBNummer.Create;
  try
    LNeueNummer.BkbTyp := Context.Find<TBkbTyp>(BkbTyp.ToUpper);
    if LNeueNummer.BkbTyp = nil then
      raise EXDataHttpException.Create(422, 'Unbekannter BKB Typ!');
    LNeueNummer.Jahr := YearOf(now);
    var LSysKZ := Context.Find<TSys>.List;
    try
      LNeueNummer.Systemkz := LSysKZ.First;
    finally
      FreeAndNil(LSysKZ);
    end;
    LNeueNummer.Bundesland := User.Bundesland;

    var Statement := Context.Connection.CreateStatement;
    Statement.SetSQLCommand('SELECT NEXT VALUE FOR [BEWEGUNGSDATEN].[SEQ_BBK_LFDNR]  AS LFDNR');
    var ResultSet := Statement.ExecuteQuery;

    while ResultSet.Next do
      LNeueNummer.LfdNr := ResultSet.GetFieldValue('LFDNR');

    // Um Platz zu sparen kodieren wir die laufende Nummer hexadezimal
    LNeueNummer.LfdNrHex := LNeueNummer.LfdNr.ToHexString(5);
    if LNeueNummer.LfdNrHex.ValueOrDefault.Length > 5 then
      raise EXDataHttpException.Create(500, 'Überlauf bei den BKBNummern! Administration informieren!');

    LNeueNummer.Nummer := Format('%.3s.%.10s.%.4d.%.1s%s', [LNeueNummer.Systemkz.Systemkz, LNeueNummer.BkbTyp.Typ,
        Trunc(LNeueNummer.Jahr),
        LNeueNummer.Bundesland.BKBKZ, LNeueNummer.LfdNrHex.ValueOrDefault]);

    Context.Save(LNeueNummer);
  except
    FreeAndNil(LNeueNummer);
    raise;
  end;
  result := LNeueNummer.Nummer;
end;

function TMe.NeuerKontrollBericht(Bericht: TKontrollbericht;
  [XDefault(false)]Nacherfassung: Boolean = false): integer;
begin
  result := NeuerKontrollBerichtInternal(Bericht, true, Nacherfassung);
end;

function TMe.NeuerUngeplanterKontrollBericht2(Kontrolle: TUngeplanteKontrolle): integer;
begin
  var
  LBericht := TKontrollbericht.Create;

  var
  LKontrollTyp := Context.Find<TKontrolltyp>
    .where(M.KontrollTyp.KontrollTyp = Kontrolle.KontrollTyp)
    .where(M.KontrollTyp.BkbTyp.Typ = Kontrolle.BkbTyp).List;

  if LKontrollTyp.Count <> 1 then
    raise EXDataHttpException.Create(422, 'Ungültiger Kontrolltyp');

  LBericht.KontrollTyp := LKontrollTyp.First;

  if LBericht.KontrollTyp.BkbTyp.BkbtypenRechtsgrundlagen.Count = 0 then
    raise EXDataHttpException.Create(422, 'BkbTyp hat keine Rechtsgrundlage');

  LBericht.Rechtsgrundlage := LBericht.KontrollTyp.BkbTyp.BkbtypenRechtsgrundlagen.First.Rechtsgrundlage;
  LBericht.Betrieb := Context.Find<TBetrieb>(Kontrolle.BetriebId);
  if LBericht.Betrieb = nil then
    raise EXDataHttpException.Create(422, 'Betrieb nicht gefunden');

  LBericht.Todos.Add(TTodo.Create);
  LBericht.Todos.First.Titel := Kontrolle.Titel;
  LBericht.Todos.First.Faellig := Kontrolle.Faellig;
  LBericht.Todos.First.Gruppe := Context.Find<TGruppe>(Kontrolle.GruppeID);
  if LBericht.Todos.First.Gruppe = nil then
    raise EXDataHttpException.Create(422, 'Gruppe fehlt oder wurde nicht gefunden');

  result := NeuerKontrollBerichtInternal(LBericht, false);
end;

function TMe.NeuerUngeplanterKontrollBericht(
  Bericht: TKontrollbericht;
  Todo: TTodo): integer;
begin
  Bericht.Todos.Clear;
  if Todo <> nil then
  begin
    Bericht.Todos.Add(Todo);
  end;

  result := NeuerKontrollBerichtInternal(Bericht, false);
end;

function TMe.GetKontrollbericht(AId: integer): TKontrollbericht;
begin
  result := Context.Find<TKontrollbericht>(AId);
  if result = nil then
    raise EXDataHttpException.Create(422, 'Unbekannter Kontrollbericht');
end;

function TMe.GetManagedObjects: Bcl.Collections.THashSet<TObject>;
begin
  result := TXDataOperationContext.Current.Handler.ManagedObjects;
end;

function TMe.GetPVPToken: TPVPToken;
begin
  result := PVPToken;
end;

function TMe.Rechtsgrundlagen(
  const
  BkbTyp:
  string): TList<TRechtsgrundlage>;
var
  LBkbTyp: TBkbTyp;
  LBkbRechtsgrundlage: TBkbtypenRechtsgrundlage;
begin
  result := TList<TRechtsgrundlage>.Create;

  if BkbTyp.Trim > '' then
  begin
    LBkbTyp := Context.Find<TBkbTyp>(BkbTyp.ToUpper.Trim);
    for LBkbRechtsgrundlage in LBkbTyp.BkbtypenRechtsgrundlagen do
    begin
      result.Add(LBkbRechtsgrundlage.Rechtsgrundlage);
    end;
  end;
end;

procedure TMe.RevPlanKontrolleErstellen(ARevPlan: TRevisionsplan; ABetriebsRevStamm: TBetriebRevstamm);
begin
  ELKELog('RevPlanAbschl: Erzeuge Kontrolle(n) für ' + ABetriebsRevStamm.Betrieb.Name +
    ' [' + ABetriebsRevStamm.Betrieb.Id.ToString + ']');
  ELKELog('RevPlanAbschl: Betriebsart ' + ABetriebsRevStamm.Revisionsstamm.Betriebsart);

  var
  LJahr := ARevPlan.Jahr;
  var
  LFrequenz := ARevPlan.JMindestKontrollFrequenz.ValueOrDefault;

  var
  LRevSchemaA := Context.Find<TRevisionsSchema>
    .where(M.RevisionsSchema.RevSchema = 'A').UniqueResult;

  ELKELog('RevPlanAbschl: Frequenz: ' + LFrequenz.ToString);
  // Wir legen je nach Frequenz eine Liste der Fälligkeiten pro Jahr an
  var
  LKontrollDates := TList<TDate>.Create;
  try
    if LFrequenz = 0 then
    begin
      ELKELog('RevPlanAbschl: Frequenz ist "0". Es wird keine Kontrolle angelegt. RevPlanID: ' +
        ARevPlan.Id.ToString);
    end
    else if LFrequenz >= 1 then
    begin
      // Wenn > 1, dann ganzzahlig!
      Assert(not (LFrequenz > (Trunc(LFrequenz) + 0.01)),
        'Frequenz muss ganzzahlig sein. RevPlanID: ' + ARevPlan.Id.ToString);

      // Abstand in Monaten der Kontrollen voneinander
      var
      LAbstand := 1 / (Trunc(LFrequenz) / 12);

      // Wir rechnen hier bewußt in Monaten
      // f=24 -> 1 / (24/12) = 1 / 2 = halber Monat Abstand
      // f=1 -> 1 / (1/12) = 12 = 12 Monate Abstand
      // f=2 -> 1 / (2/12) = 6 = 6 Monate Abstand

      // die letzte Kontrolle ist immer zum Jahresende fällig
      var
      LMonat := 12.0;
      var
      LTag := 31;
      var
      LKontrollDatum := EncodeDate(LJahr, Trunc(LMonat), LTag);
      LKontrollDates.Add(LKontrollDatum);

      // Nun die weiteren Tage berechnen.
      // Wenn 1, dann keine weitere Kontrolle
      // Wenn 2, dann eine Kontrolle am 30.06. usw.
      while (LMonat - LAbstand) > 0 do
      begin
        LMonat := LMonat - LAbstand;
        var
        LActualMonat := Ceil(LMonat); // Month könnte 11.5 sein -> dann ist der Monat = 12 - Ceil(11.5) = 12
        LTag := DaysInAMonth(LJahr, LActualMonat);

        LKontrollDatum := EncodeDate(LJahr, LActualMonat, LTag);
        LKontrollDates.Add(LKontrollDatum);
      end;
      Assert(LKontrollDates.Count = Trunc(LFrequenz),
        'Fehler bei der Berechnung der Kontrollfrequenz. RevPlanID: ' + ARevPlan.Id.ToString);
    end
    else // 0 < f < 1
    begin
      // Wenn die Frequenz kleiner Eins ist, dann müssen die Kontrollen der Vorjahre geprüft werden
      // Also: 0,5 -> Eine Kontrolle alle zwei Jahre
      // wir arbeiten hier mit Ganzahlen weiter, sonst gibt es blöde Rundungsfehler
      var
      LFrequenzInt := Trunc(LFrequenz * 100);
      var
      LJahresFrequenz: integer;
      case LFrequenzInt of
        50:
          LJahresFrequenz := 2; // alle 2 Jahre
        30, 33:
          LJahresFrequenz := 3; // alle 3 Jahre
        25:
          LJahresFrequenz := 4; // alle 4 Jahre
        20:
          LJahresFrequenz := 5; // alle 5 Jahre
        10:
          LJahresFrequenz := 10; // alle 10 Jahre
      else
        raise EXDataHttpException.CreateFmt(441, 'Ungültige Mehrjahres-Frequenz %s Revisionsplan ID : %d',
          [LFrequenz.ToString, ARevPlan.Id]);
      end;
      // Wenn es im aktuellen Jahr eine Kontrolle geben soll, dann ist die Fälligkeit zum 31.12.
      var
      LMonat := 12.0;
      var
      LTag := 31;
      var
      LKontrollDatum := EncodeDate(LJahr, Trunc(LMonat), LTag);

      // 1.1. des Vorjahres gemäß Frequenz
      // f=3 -> 2024 -> 1.1.2022
      var
      LKontrollzeitraum := EncodeDate(LJahr.ValueOrDefault - LJahresFrequenz + 1, 1, 1);

      var
      LVorjahresKontrollen := Context.Find<TKontrollbericht>
        .where(M.kontrollbericht.Betrieb.Id = ABetriebsRevStamm.Betrieb.Id)
        .where(M.kontrollbericht.Endezeit >= LKontrollzeitraum)
        .where(M.kontrollbericht.KontrollTyp.KontrollTyp = ABetriebsRevStamm.Revisionsstamm.KontrollTyp.KontrollTyp)
        .where(M.kontrollbericht.KontrollTyp.BkbTyp.Typ = ABetriebsRevStamm.Revisionsstamm.KontrollTyp.BkbTyp.Typ)
        .List;
      if LVorjahresKontrollen.Count > 0 then
      begin
        ELKELog('RevPlanAbschl: Vorjahreskontrolle wurde gemäß Frequenz bereits durchgeführt. Keine weitere Kontrolle.');
      end
      else
      begin
        ELKELog('RevPlanAbschl: Keine Vorjahreskontrolle vorhanden. Neue Kontrolle wird angelegt.');
        LKontrollDates.Add(LKontrollDatum);
      end;
    end;
    ELKELog('RevPlanAbschl: Für den Betrieb werden %d Kontrollen erzeugt...', [LKontrollDates.Count]);

    // Hier nun die eigentlichen Kontrollen gemäß den Frequenzen in LKontrollDates anlegen.
    // Wichtig, wir sortieren das Datum aufsteigend, damit Kontrolle 1/3 , 2/3 usw korrekt beschriftet werden kann
    LKontrollDates.Sort(TDelegatedComparer<TDate>.Create(
      function(const Left, Right: TDate): integer
      begin
        result := Trunc(Left - Right);
      end));

    var
    i := 1;
    for var LKontrollDatum in LKontrollDates do
    begin

      var
      LKontrolle := TKontrollbericht.Create;
      ManagedObjects.Add(LKontrolle);

      LKontrolle.Revisionsplan := ARevPlan;
      LKontrolle.Betrieb := ABetriebsRevStamm.Betrieb;
      LKontrolle.Rechtsgrundlage := ABetriebsRevStamm.Revisionsstamm.Rechtsgrundlage;
      // Fallback, falls im RevStamm keine Rechtsgrundlage eingetragen ist
      if LKontrolle.Rechtsgrundlage = nil then
      begin
        LKontrolle.Rechtsgrundlage := Rechtsgrundlagen(ABetriebsRevStamm.Revisionsstamm.KontrollTyp.BkbTyp.Typ)
          .First;
      end;
      if LKontrolle.Rechtsgrundlage = nil then
        raise EXDataHttpException.Create(441, 'Es konnte keine Rechtsgrundlage zugeordnet werden');

      var
      LBetrieb := ABetriebsRevStamm.Betrieb;

      // Gruppe / Revisionsschema
      // https://esculenta.atlassian.net/browse/ERT-224
      // Todo: Gruppe nur einmal pro Betrieb berechnen

      // Das Revisionsschema wird zuerst im Revisionsstamm gesucht. Wenn dort keines angegebn ist, dann wird das Schema aus
      // dem Betrieb genommen
      // Erweiterung von 17.11.2023 https://esculenta.atlassian.net/browse/ERT-224
      var
      LRevisionsSchema: TRevisionsSchema;

      if Assigned(ABetriebsRevStamm.RevisionsSchema) then
      begin
        LRevisionsSchema := ABetriebsRevStamm.RevisionsSchema;
        // Zuordnung auf Betriebsebene-Revisionsstamm Prio 1
      end
      else if Assigned(ABetriebsRevStamm.Revisionsstamm.RevisionsSchema) then
        // Zuordnung auf Revisionsstammebene Prio 2
      begin
        LRevisionsSchema := ABetriebsRevStamm.Revisionsstamm.RevisionsSchema;
      end
      else
        // Schema A ist "Default"
      begin
        LRevisionsSchema := LRevSchemaA;
        ELKELog('Fallback - Kein Schema gefunden: RevSchema A');
      end;

      // LBetrieb.RevisionsSchema kommt erst zum Tragen, wenn hier oben Schema "D" selektiert wurde.
      // Dann wird im Betrieb geschaut, und das Schema ggfs. nochmals überschrieben (siehe Code unten)
      // Die Gruppe wird mit "D" direkt aus LBetrieb.RevisionsGruppe gelesen.

      // Geplant oder ungeplant gemnäß Revionsschema
      var
      LGeplant := LRevisionsSchema.Geplant;
      var
      LTodo: TTodo;
      if LGeplant then
      begin
        LKontrolle.Datum := LKontrollDatum;
        LTodo := nil;
      end
      else
      begin
        LKontrolle.Datum := sNull;
        // Nur ungeplante Kontrollen brauchen ein Todo
        LTodo := TTodo.Create;
        LKontrolle.Todos.Add(LTodo);
      end;

      // Gruppenwahl gemäß Revisionschema
      if LRevisionsSchema.RevSchema = 'D' then
      begin
        // Spezialfall bei LMI Betrieben. Siehe z.B. Ellegast GmbH
        // Das Revisionsschema kann hier im Betrieb nochmal überschrieben werden
        if Assigned(LBetrieb.RevisionsSchema) and (LBetrieb.RevisionsSchema.RevSchema <> 'D') then
        begin
          LRevisionsSchema := LBetrieb.RevisionsSchema;
        end
        else
        begin
          LKontrolle.GruppeQuelle := LBetrieb.RevisionsGruppe;

          // Fallback, wenn keine Gruppe gefunden wurde: SCHEMA = A
          if (LKontrolle.GruppeQuelle = nil) then
          begin
            LRevisionsSchema := LRevSchemaA;
            ELKELog('Fallback D - keine Gruppe gefunden: RevSchema A');
          end;
        end;
      end;

      if LRevisionsSchema.RevSchema = 'G' then
      begin
        LKontrolle.GruppeQuelle := LBetrieb.GruppeGemeinde;
      end
      else if LRevisionsSchema.RevSchema = 'A' then
      begin
        LKontrolle.GruppeQuelle := LBetrieb.GruppeBezirk;
      end
      else if LRevisionsSchema.RevSchema = 'B' then
      begin
        LKontrolle.GruppeQuelle := LBetrieb.GruppeBetrieb;
      end
        // Achtung: "D" wird oben unabhängig geprüft.
      else if LRevisionsSchema.RevSchema <> 'D' then
        raise EXDataHttpException.CreateFmt(
          404, 'Unbekanntes Revisionsschema %s', [LRevisionsSchema.RevSchema]
          );

      if (LKontrolle.GruppeQuelle = nil) then
        raise EXDataHttpException.CreateFmt(
          404, 'Dem Betrieb [%d] konnte keine Gruppe zugeordnet werden', [ABetriebsRevStamm.Betrieb.Id]
          );

      ELKELog('Auswahl : Revsionsschema %s / Geplant %d / Gruppe %d',
        [LRevisionsSchema.RevSchema, IfThen(LGeplant, 1, 0), LKontrolle.GruppeQuelle.Id]);

      LKontrolle.Erfasser := LKontrolle.GruppeQuelle.Hauptverantwortlicher.Person;
      LKontrolle.KontrollTyp := ABetriebsRevStamm.Revisionsstamm.KontrollTyp;
      LKontrolle.Betriebstyp := 'HB'; // Todo: ist das immer ein HB?
      LKontrolle.KontrollInformationen :=
        Format('Kontrolle %d von %d / Revisionsplan %d/%d / Revisionsstamm %d / ',
        [i, LKontrollDates.Count, LJahr.ValueOrDefault, ARevPlan.Id, ABetriebsRevStamm.Revisionsstamm.Id]) + #13#10
        + 'Betrieb: ' + LBetrieb.Registrierung.RegNr
        + ' - ' + ABetriebsRevStamm.Revisionsstamm.Betriebsart;
      ELKELog('RevPlanAbschl: Erzeuge: %s', [LKontrolle.KontrollInformationen.ValueOrDefault]);

      // Todo: mit Kontrollinfos updaten:
      if not LGeplant then
      begin
        LTodo.Titel := LKontrolle.KontrollInformationen;
        LTodo.Faellig := LKontrollDatum;
        LTodo.Gruppe := LKontrolle.GruppeQuelle;
        // ungeplante Kontrollen haben nur eine Gruppe
        LKontrolle.Erfasser := nil;
      end;

      // theoretisch können Kontrollen mit Datum in der Vergangeheit enstehen, wenn
      // der Revisionsplan für das aktuelle quasi zu spät abgearbeitet wird.
      // Daher hier immer Nacherfassung = true
      NeuerKontrollBerichtInternal(LKontrolle, LGeplant, true);
      inc(i);
      // Statisitik
      ARevPlan.AnzGesamtKontrollen := ARevPlan.AnzGesamtKontrollen.ValueOrDefault + 1;
    end;
  finally
    FreeAndNil(LKontrollDates);
  end;
end;

function TMe.RevisionsplanAbschliessen(Jahr: integer; KontrollTyp: string): TList<TRevisionsplan>;
begin
  // Der "Kontrolltyp" (z.B. HYG) ist eigentlich der BKB Typ
  var
  LBkbTyp := KontrollTyp.ToUpper;
  // Prüfen, ob es den BKBTyp überhaupt gibt. Dazu suchen wir aus den Kontrolltypen, alle mit dem angegebenen BkbTyp raus
  var
  LKontrollTypen := Context.Find<TKontrolltyp>.where(M.KontrollTyp.BkbTyp.Typ = LBkbTyp).List;
  if LKontrollTypen.Count = 0 then
    raise EXDataHttpException.Create(404, 'Kontrolltyp [' + LBkbTyp + '] nicht gefunden.');

  ELKELog('RevPlanAbschl: BLD: %d Jahr: %d Kontrolltyp: %s', [User.Bundesland.BLDCode, Jahr, LBkbTyp]);

  // Nun alle Revisionsplaneinträge mit dem entsprechenden Kontrolltypen für das Jahr raussuchen
  result := Context.Find<TRevisionsplan>
    .where(M.Revisionsplan.Jahr = Jahr)
    .where(M.Revisionsplan.BLDCode.BLDCode = User.Bundesland.BLDCode)
    .where(M.Revisionsplan.Revisionsstamm.KontrollTyp.BkbTyp.Typ = LBkbTyp).List;

  ELKELog('RevPlanAbschl: ' + result.Count.ToString + ' Revisionsplan-Kontrolltypen werden verarbeitet');

  // Alle Revisionspläne für das Jahr/BKB-Typ in ein Dictionary, um das später schnell referenzieren zu können
  // Key ist die ID des Revisionsstamm, der über die gleiche ID auch in LBetriebsRevDaten referenziert wird
  // Damit kann dann über Revisionsstamm.ID der für dieses Jahr zugehörige Revisionsplan zu geordnet werden
  var
  LRevisionsStammdaten := TDictionary<integer, TRevisionsplan>.Create;
  ManagedObjects.Add(LRevisionsStammdaten);
  for var LKontrollPlan in result do
  begin
    LRevisionsStammdaten.Add(LKontrollPlan.Revisionsstamm.Id, LKontrollPlan);
  end;
  ELKELog('RevPlanAbschl: Revisionsplan Dictionary erzeugt: ' + LRevisionsStammdaten.Count.ToString);

  // Betriebe mit Revisionsdaten (die zum Bundesland und BKBTyp passen), holen
  var
  LBetriebsRevStammDaten := Context.Find<TBetriebRevstamm>
    .where(M.BetriebRevstamm.Revisionsstamm.BLDCode = User.Bundesland.BLDCode)
    .where(M.BetriebRevstamm.Begdate <= Today)
    .where(M.BetriebRevstamm.Enddate >= Today)
    .where(M.BetriebRevstamm.Revisionsstamm.KontrollTyp.BkbTyp.Typ = LBkbTyp)
    .List;
  ManagedObjects.Add(LBetriebsRevStammDaten);
  var
  LBetriebeRevCount := LBetriebsRevStammDaten.Count;
  ELKELog('RevPlanAbschl: Anzahl der zu verarbeitenden Betriebe/Revisionsstämme: ' +
    LBetriebeRevCount.ToString);

  var
  LRevplanBetriebe := TObjectDictionary < TRevisionsplan, TList<TBetrieb> > .Create([doOwnsValues]);
  ManagedObjects.Add(LRevplanBetriebe);

  // Alle Betriebe loggen
  var
  LBetriebe := TStringList.Create;
  try
    LBetriebe.Add('Alle Betriebe aus BetriebsRevstamm für die Revisionskontrollen erstellt werden');
    for var LBetriebsRevStamm in LBetriebsRevStammDaten do
    begin
      LBetriebe.Add(LBetriebsRevStamm.Betrieb.Id.ToString + '/' + LBetriebsRevStamm.Id.ToString + ' ' +
        LBetriebsRevStamm.Betrieb.Name);
    end;
    ELKELog(LBetriebe.Text);
  finally
    FreeAndNil(LBetriebe);
  end;

  var
  LBetriebeRevCountProcessing := 0;
  for var LBetriebsRevStamm in LBetriebsRevStammDaten do
  begin
    var
    LTransaction := Context.Connection.BeginTransaction;
    try
      inc(LBetriebeRevCountProcessing);
      ELKELog('Verarbeite Betrieb-RevStamm ID %d (%d von %d) ', [LBetriebsRevStamm.Id, LBetriebeRevCountProcessing,
          LBetriebeRevCount]);
      var
      LRevPlan: TRevisionsplan := nil;

      if LRevisionsStammdaten.TryGetValue(LBetriebsRevStamm.Revisionsstamm.Id, LRevPlan) then
      begin
        Assert(LRevPlan.Jahr = Jahr, 'Falsches Jahr im Revisionsplan! RevPlanID: ' + LRevPlan.Id.ToString);
        if LRevPlan.Gesperrt then
        begin
          ELKELog('RevPlanAbschl: Revisionsplan [%d] ist gesperrt. Es werden keine Kontrollen erstellt.',
            [LRevPlan.Id]);
        end
        else
        begin
          // hier wird für den jeweiligen Betrieb eine oder mehrere Kontrollen erstellt (je nach Frequenz)
          RevPlanKontrolleErstellen(LRevPlan, LBetriebsRevStamm);

          // Für Statistikzwecke pflegen wir eine Liste der Revisionspläne und der Betriebe, die diesen REvisionsplan haben
          // Eintrag anlegen, wenn es den Revisionplan noch nicht gibt
          if not (LRevplanBetriebe.ContainsKey(LRevPlan)) then
          begin
            // Für jeden Revisionsplan legen wir eine List der Betriebe an
            LRevplanBetriebe.AddOrSetValue(LRevPlan, TList<TBetrieb>.Create);
          end;
          if not LRevplanBetriebe.Items[LRevPlan].Contains(LBetriebsRevStamm.Betrieb) then
          begin
            // Wenn der aktuelle Betrieb noch nicht in der Liste ist, dann fügen wir ihn ein
            LRevplanBetriebe.Items[LRevPlan].Add(LBetriebsRevStamm.Betrieb);
          end;
        end;
      end
      else
      begin
        raise EXDataHttpException.CreateFmt(404,
          'RevPlanAbschl: Revisionsplan zu RevisionsStamm ID %d nicht gefunden!',
          [LBetriebsRevStamm.Revisionsstamm.Id]);
      end;
      Context.Flush;
      LTransaction.Commit;
    except
      LTransaction.Rollback;
      raise;
    end;
  end;

  // Statisitik
  // Anzahl der Betriebe für jeden RevPlan ermitteln
  for var LRevPlan in result do
  begin
    // Wenn bereits gesperrt, dann machen wir nix weiter.
    if LRevPlan.Gesperrt then
      continue;
    if LRevplanBetriebe.ContainsKey(LRevPlan) then
    begin
      LRevPlan.AnzBetriebeImLand := LRevplanBetriebe.Items[LRevPlan].Count;
    end;
    // Als letztes den Revplan sperren
    LRevPlan.Gesperrt := true;
  end;

  Context.Flush;
end;

function TMe.Rollen: TPVPRoles;
begin
  result := PVPToken.Rollen;
end;

function TMe.StatusToArray(AStatus: string): TArray<Variant>;
begin
  // AStatus := 'U','G' ... oder 'UG...'
    // In Variant Array umkopieren. Aurelius kann nur variante Arrays
  AStatus := AStatus.Replace('''', '').Replace(',', '').Replace(' ', '');

  SetLength(result, Length(AStatus));
  for var i := 1 to Length(AStatus) do
  begin
    result[i - 1] := AStatus[i];
  end;
end;

function TMe.UngeplanteKontrollen(Page: integer = 1; PageSize: integer = 10): TList<TKontrollbericht>;
begin
  // Die ungeplanten Kontrollen für den User

  // + Kontrolle hat Status "U"
  // + Nur direkte Gruppen (GroupLevel 0, 1)
  // + für die nächsten 365 Tage

  result := KontrollenForUserNeu(User, 'U', TGroupLevel.MitDirektenGruppen, 0, Today + 365, Page, PageSize);
end;

procedure TMe.UpdateRollen;
var
  LRollenDB: TList<TRolle>;
  LPVPRoles: TPVPRoles;
  LRole: TPVPRole;
  LRolleDb: TRolle;
  LTempRolleDB: TRolle;
  LFound: Boolean;
  LUserRolle: TUserRolle;
begin

  // Zunächst alle Rollen aus der DB holen
  // Todo: Sichtbarkeit beachten
  LRollenDB := Context.Find<TRolle>.List;
  ManagedObjects.Add(LRollenDB);
  // Nun die Rollen des aktuellen Users aus dem PVP Token
  LPVPRoles := Rollen;

  for LRole in LPVPRoles do
  begin
    // Prüfen, dass jede Rolle in den Stammdaten existiert
    LRolleDb := nil;
    LFound := false;
    for LTempRolleDB in LRollenDB do
    begin
      if LTempRolleDB.Bezeichnung.tolower = LRole.Name.tolower then
      begin
        LRolleDb := LTempRolleDB;
        LFound := true;
        break;
      end;
    end;
    if not LFound then
    begin
      // PVP Rolle ist nicht in der Datenbank bekannt. Neu anlegen.
      LRolleDb := TRolle.Create;
      LRolleDb.Bezeichnung := LRole.Name;
      LRolleDb.Sichtbar := true;
      Context.Save(LRolleDb);
    end;

    // Nun prüfen, dass der User die Rolle in der DB hat
    LFound := false;
    for LUserRolle in self.User.Userrollen do
    begin
      if (LUserRolle.ModulInstanz = ModulInstanz) and (LRolleDb = LUserRolle.Rolle) then
      begin
        LFound := true;
        break;
      end;
    end;
    if not LFound then
    begin
      LUserRolle := TUserRolle.Create;
      LUserRolle.User := self.User;
      LUserRolle.Rolle := LRolleDb;
      LUserRolle.ModulInstanz := ModulInstanz;
      self.User.Userrollen.Add(LUserRolle);
      Context.Save(LUserRolle);
    end;

    // Todo: Parameter auswerten
  end;

  // Abschliessend prüfen, ob der User eine Rolle in der DB hat, die im PVP token nicht (nerh)mehr) vorhanden ist
  Context.DeferDestruction := true;
  for LUserRolle in self.User.Userrollen do
  begin
    LFound := false;
    for LRole in LPVPRoles do
    begin
      if LRole.Name.tolower = LUserRolle.Rolle.Bezeichnung.tolower then
      begin
        LFound := true;
        break;
      end;
    end;

    if not LFound then
    begin
      // Rolle für den User aus der DB entfernen
      Context.Remove(LUserRolle);
    end;
  end;
  Context.Flush;
end;

function TMe.GetUser: TUser;
var
  LUserGUID: string;
begin
  if FUser = nil then
  begin
    LUserGUID := PVPToken.GID;
    FUser := Context.Find<TUser>.where(M.User.Userguid = LUserGUID).UniqueResult;
  end;
  if FUser = nil then
  begin
    // Unbekannte User werden automatisch aus dem Stammportal/PVPToken angelegt
    AddUserFromToken;
  end;
  result := FUser;
end;

function TMe.KontrolleZuweisen(
  Id: integer;
  PersonID: integer;
  GruppeID: integer;
  Planungsdatum: TDate;
  AngemeldetUm: TDateTime
  ): TKontrollbericht;
var
  LKontrolle: TKontrollbericht;
  LPerson: TPerson;
  LGruppe: TGruppe;
begin
  LKontrolle := Context.Find<TKontrollbericht>(Id);
  if LKontrolle = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle zuweisen - Kontrolle ID nicht gefunden: ' + Id.ToString);

  // Wir merken uns, ob die Kontrolle ursprünglich ungeplant ist
  var
  LUngeplant := LKontrolle.Status = 'U';

  LPerson := Context.Find<TPerson>(PersonID);
  if LPerson = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle zuweisen - PersonID nicht gefunden: ' + PersonID.ToString);

  LKontrolle.Erfasser := LPerson;

  LGruppe := Context.Find<TGruppe>(GruppeID);
  if LGruppe = nil then
    raise EXDataHttpException.Create(404, 'Kontrolle zuweisen - GruppeID nicht gefunden: ' + GruppeID.ToString);

  LKontrolle.GruppeQuelle := LGruppe;

  ELKELog('Kontrolle zuweisen: ' + Id.ToString + ' ' + LKontrolle.Status
    + ' : P' + PersonID.ToString + '/G' + GruppeID.ToString);

  // Wenn kein Datum angegeben ist (-1), dann belassen wir es wie es ist.
  // Wenn das Datum mit "0" angegeben ist, dann setzten wir das Datum auf null/nil
  if Planungsdatum = 0 then
  begin
    LKontrolle.Datum := sNull;
  end
  else if Planungsdatum > 0 then
  begin
    LKontrolle.Datum := Planungsdatum;
  end;

  if AngemeldetUm = 0 then
  begin
    LKontrolle.AngemeldetUm := sNull;
  end
  else if AngemeldetUm > 0 then
  begin
    LKontrolle.AngemeldetUm := AngemeldetUm;
  end;

  // Beim Zuweisen und Stornieren von Ungeplanten Kontrollen löschen wir die Todos
  if LUngeplant then
  begin
    var
    LTodos := LKontrolle.Todos;
    for var LTodo in LTodos do
    begin
      Context.Remove(LTodo);
    end;
    LKontrolle.Todos.Clear;
  end;

  // Abschließend Quellgruppe prüfen/setzen
  LKontrolle.QuellGruppeValidieren;

  Context.SaveOrUpdate(LKontrolle);
  Context.Flush;
  result := LKontrolle;
end;

initialization

  RegisterServiceType(TMe);

end.

