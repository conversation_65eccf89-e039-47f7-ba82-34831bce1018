unit ELKE.Classes.Generated.Dictionary;

interface

uses
  Aurelius.Dictionary.Classes, 
  Aurelius.Linq;

type
  TAPTypDictionary = class;
  TAdresseDictionary = class;
  TAnsprechpartnerDictionary = class;
  TAnwesenderDictionary = class;
  TBetriebDictionary = class;
  TBetriebRevstammDictionary = class;
  TBetriebeKommunikationswegeDictionary = class;
  TBewerteteFrageDictionary = class;
  TBewertungDictionary = class;
  TBewertungsIconDictionary = class;
  TBewertungstypDictionary = class;
  TBkbTypDictionary = class;
  TBkbnummerDictionary = class;
  TBkbtypenRechtsgrundlageDictionary = class;
  TBundeslandDictionary = class;
  TBundeslandChecklistenKontrolltypDictionary = class;
  TBundeslandModulDictionary = class;
  TCckAuftragDictionary = class;
  TCckAuftragsartDictionary = class;
  TCckAuftragsbewertungDictionary = class;
  TCckAuswahlDictionary = class;
  TCckBetriebDictionary = class;
  TCckModulDictionary = class;
  TCckModulAnforderungDictionary = class;
  TCckModulKontrolltypDictionary = class;
  TCckStatusDictionary = class;
  TCckStatusMeldungDictionary = class;
  TCckTierdatenDictionary = class;
  TCckVokSanktionDictionary = class;
  TChecklisteDictionary = class;
  TChecklistenKontrolltypDictionary = class;
  TDokumentDictionary = class;
  TEmailArtenDictionary = class;
  TEmailHistoryDictionary = class;
  TEmailHistoryAttachmentDictionary = class;
  TEmailTexteDictionary = class;
  TFormatierungDictionary = class;
  TFrageDictionary = class;
  TFrageBewertungDictionary = class;
  TFrageKontrollbereichDictionary = class;
  TFragengruppeDictionary = class;
  TFunktionDictionary = class;
  TFunktionRolleDictionary = class;
  TGemeindeDictionary = class;
  TGruppeDictionary = class;
  TKbProbeDictionary = class;
  TKommunikationsartDictionary = class;
  TKommunikationswegDictionary = class;
  TKontrollbereichDictionary = class;
  TKontrollberichtDictionary = class;
  TKontrollberichtBildDictionary = class;
  TKontrollberichtOertlichkeitDictionary = class;
  TKontrolltypDictionary = class;
  TKontrolltypReportDictionary = class;
  TLandDictionary = class;
  TMangelDictionary = class;
  TMangelKontrollbereichDictionary = class;
  TMangelOertlichkeitDictionary = class;
  TMangelStatusDictionary = class;
  TMangeltypDictionary = class;
  TMassnahmeDictionary = class;
  TMassnahmenkatalogDictionary = class;
  TModulDictionary = class;
  TModulInstanzDictionary = class;
  TModuleBkbtypenDictionary = class;
  TNachrichtDictionary = class;
  TNachrichtenZustellungDictionary = class;
  TPersonDictionary = class;
  TPersonenKommunikationswegeDictionary = class;
  TProgrammModulDictionary = class;
  TRechtsgrundlageDictionary = class;
  TRegistrierungDictionary = class;
  TReportDictionary = class;
  TReportTypDictionary = class;
  TRevisionsSchemaDictionary = class;
  TRevisionsplanDictionary = class;
  TRevisionsstammDictionary = class;
  TRolleDictionary = class;
  TRollenBkbtypenDictionary = class;
  TSysDictionary = class;
  TTodoDictionary = class;
  TUnterschriftDictionary = class;
  TUserDictionary = class;
  TUsergruppeDictionary = class;
  TUserrolleDictionary = class;
  TVNachrichtForUserDictionary = class;
  TVUserGroupMembershipDictionary = class;
  TVbetriebbezirksgruppeDictionary = class;
  TVbetriebsinfoDictionary = class;
  TVgroupuserDictionary = class;
  TZulassungDictionary = class;
  TZusatztextDictionary = class;
  TvUserKontrollenDictionary = class;
  
  IAPTypDictionary = interface;
  
  IAdresseDictionary = interface;
  
  IAnsprechpartnerDictionary = interface;
  
  IAnwesenderDictionary = interface;
  
  IBetriebDictionary = interface;
  
  IBetriebRevstammDictionary = interface;
  
  IBetriebeKommunikationswegeDictionary = interface;
  
  IBewerteteFrageDictionary = interface;
  
  IBewertungDictionary = interface;
  
  IBewertungsIconDictionary = interface;
  
  IBewertungstypDictionary = interface;
  
  IBkbTypDictionary = interface;
  
  IBkbnummerDictionary = interface;
  
  IBkbtypenRechtsgrundlageDictionary = interface;
  
  IBundeslandDictionary = interface;
  
  IBundeslandChecklistenKontrolltypDictionary = interface;
  
  IBundeslandModulDictionary = interface;
  
  ICckAuftragDictionary = interface;
  
  ICckAuftragsartDictionary = interface;
  
  ICckAuftragsbewertungDictionary = interface;
  
  ICckAuswahlDictionary = interface;
  
  ICckBetriebDictionary = interface;
  
  ICckModulDictionary = interface;
  
  ICckModulAnforderungDictionary = interface;
  
  ICckModulKontrolltypDictionary = interface;
  
  ICckStatusDictionary = interface;
  
  ICckStatusMeldungDictionary = interface;
  
  ICckTierdatenDictionary = interface;
  
  ICckVokSanktionDictionary = interface;
  
  IChecklisteDictionary = interface;
  
  IChecklistenKontrolltypDictionary = interface;
  
  IDokumentDictionary = interface;
  
  IEmailArtenDictionary = interface;
  
  IEmailHistoryDictionary = interface;
  
  IEmailHistoryAttachmentDictionary = interface;
  
  IEmailTexteDictionary = interface;
  
  IFormatierungDictionary = interface;
  
  IFrageDictionary = interface;
  
  IFrageBewertungDictionary = interface;
  
  IFrageKontrollbereichDictionary = interface;
  
  IFragengruppeDictionary = interface;
  
  IFunktionDictionary = interface;
  
  IFunktionRolleDictionary = interface;
  
  IGemeindeDictionary = interface;
  
  IGruppeDictionary = interface;
  
  IKbProbeDictionary = interface;
  
  IKommunikationsartDictionary = interface;
  
  IKommunikationswegDictionary = interface;
  
  IKontrollbereichDictionary = interface;
  
  IKontrollberichtDictionary = interface;
  
  IKontrollberichtBildDictionary = interface;
  
  IKontrollberichtOertlichkeitDictionary = interface;
  
  IKontrolltypDictionary = interface;
  
  IKontrolltypReportDictionary = interface;
  
  ILandDictionary = interface;
  
  IMangelDictionary = interface;
  
  IMangelKontrollbereichDictionary = interface;
  
  IMangelOertlichkeitDictionary = interface;
  
  IMangelStatusDictionary = interface;
  
  IMangeltypDictionary = interface;
  
  IMassnahmeDictionary = interface;
  
  IMassnahmenkatalogDictionary = interface;
  
  IModulDictionary = interface;
  
  IModulInstanzDictionary = interface;
  
  IModuleBkbtypenDictionary = interface;
  
  INachrichtDictionary = interface;
  
  INachrichtenZustellungDictionary = interface;
  
  IPersonDictionary = interface;
  
  IPersonenKommunikationswegeDictionary = interface;
  
  IProgrammModulDictionary = interface;
  
  IRechtsgrundlageDictionary = interface;
  
  IRegistrierungDictionary = interface;
  
  IReportDictionary = interface;
  
  IReportTypDictionary = interface;
  
  IRevisionsSchemaDictionary = interface;
  
  IRevisionsplanDictionary = interface;
  
  IRevisionsstammDictionary = interface;
  
  IRolleDictionary = interface;
  
  IRollenBkbtypenDictionary = interface;
  
  ISysDictionary = interface;
  
  ITodoDictionary = interface;
  
  IUnterschriftDictionary = interface;
  
  IUserDictionary = interface;
  
  IUsergruppeDictionary = interface;
  
  IUserrolleDictionary = interface;
  
  IVNachrichtForUserDictionary = interface;
  
  IVUserGroupMembershipDictionary = interface;
  
  IVbetriebbezirksgruppeDictionary = interface;
  
  IVbetriebsinfoDictionary = interface;
  
  IVgroupuserDictionary = interface;
  
  IZulassungDictionary = interface;
  
  IZusatztextDictionary = interface;
  
  IvUserKontrollenDictionary = interface;
  
  IAPTypDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Hauptansprechpartner: TLinqProjection;
  end;
  
  IAdresseDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Ort: TLinqProjection;
    function Plz: TLinqProjection;
    function Strasse: TLinqProjection;
    function Adresszusatz: TLinqProjection;
    function Xkoord31287: TLinqProjection;
    function Ykoord31287: TLinqProjection;
    function Xkoord4326: TLinqProjection;
    function Ykoord4326: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Gemeinde: IGemeindeDictionary;
  end;
  
  IAnsprechpartnerDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Regnr: TLinqProjection;
    function KommunikationsBerechtigt: TLinqProjection;
    function Aptyp: IAPTypDictionary;
    function Person: IPersonDictionary;
  end;
  
  IAnwesenderDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Name: TLinqProjection;
    function Email: TLinqProjection;
    function Kommunikationsberechtigt: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function APTyp: IAPTypDictionary;
    function Person: IPersonDictionary;
  end;
  
  IBetriebDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Name: TLinqProjection;
    function Aufsichtsorgan: TLinqProjection;
    function Telefon: TLinqProjection;
    function Email: TLinqProjection;
    function Vergebuehrung: TLinqProjection;
    function Vulgo: TLinqProjection;
    function Anmerkung: TLinqProjection;
    function Bvbkz: TLinqProjection;
    function Bbknr: TLinqProjection;
    function Adresse: IAdresseDictionary;
    function Bundesland: IBundeslandDictionary;
    function Registrierung: IRegistrierungDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
    function RevisionsGruppe: IGruppeDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
    function Kommunikationswege: IBetriebeKommunikationswegeDictionary;
    function RevStaemme: IBetriebRevstammDictionary;
    function Bezirksgruppen: IVbetriebbezirksgruppeDictionary;
  end;
  
  IBetriebRevstammDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Begdate: TLinqProjection;
    function Enddate: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
  end;
  
  IBetriebeKommunikationswegeDictionary = interface(IAureliusEntityDictionary)
    function Betrieb: IBetriebDictionary;
    function Kommunikationsweg: IKommunikationswegDictionary;
  end;
  
  IBewerteteFrageDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Zusatztext: TLinqProjection;
    function Wert: TLinqProjection;
    function GpsLon: TLinqProjection;
    function GpsLat: TLinqProjection;
    function Bewertung: IBewertungDictionary;
    function Frage: IFrageDictionary;
    function Bericht: IKontrollberichtDictionary;
    function Mangel: IMangelDictionary;
    function Bilder: IKontrollberichtBildDictionary;
  end;
  
  IBewertungDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Icon: IBewertungsIconDictionary;
    function Typ: IBewertungstypDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
  end;
  
  IBewertungsIconDictionary = interface(IAureliusEntityDictionary)
    function Icon: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Bewertungen: IBewertungDictionary;
  end;
  
  IBewertungstypDictionary = interface(IAureliusEntityDictionary)
    function Typ: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Bewertungen: IBewertungDictionary;
  end;
  
  IBkbTypDictionary = interface(IAureliusEntityDictionary)
    function Typ: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Lastchange: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function SecclassMin: TLinqProjection;
    function Probe: TLinqProjection;
    function Modul: IModulDictionary;
    function Kontrolltypen: IKontrolltypDictionary;
    function BkbtypenRechtsgrundlagen: IBkbtypenRechtsgrundlageDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
    function Bkbnummern: IBkbnummerDictionary;
    function Mangeltypen: IMangeltypDictionary;
  end;
  
  IBkbnummerDictionary = interface(IAureliusEntityDictionary)
    function Nummer: TLinqProjection;
    function Jahr: TLinqProjection;
    function LfdNr: TLinqProjection;
    function LfdNrHex: TLinqProjection;
    function Systemkz: ISysDictionary;
    function Bkbtyp: IBkbTypDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  IBkbtypenRechtsgrundlageDictionary = interface(IAureliusEntityDictionary)
    function Bkbtyp: IBkbTypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
  end;
  
  IBundeslandDictionary = interface(IAureliusEntityDictionary)
    function Bldcode: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Kurztext: TLinqProjection;
    function Region: TLinqProjection;
    function Bldlogo: TLinqProjection;
    function Okz: TLinqProjection;
    function Bkbkz: TLinqProjection;
    function Land: ILandDictionary;
    function Betriebe: IBetriebDictionary;
    function BundeslaenderModule: IBundeslandModulDictionary;
    function Gruppen: IGruppeDictionary;
    function Users: IUserDictionary;
    function Adressen: IAdresseDictionary;
    function Gemeinden: IGemeindeDictionary;
    function BkbNummern: IBkbnummerDictionary;
    function EmailTexte: IEmailTexteDictionary;
    function BesitztChecklisten: IChecklisteDictionary;
    function SichtbareChecklisten: IBundeslandChecklistenKontrolltypDictionary;
  end;
  
  IBundeslandChecklistenKontrolltypDictionary = interface(IAureliusEntityDictionary)
    function GueltigAb: TLinqProjection;
    function GueltigBis: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function ChecklisteKontrolltyp: IChecklistenKontrolltypDictionary;
  end;
  
  IBundeslandModulDictionary = interface(IAureliusEntityDictionary)
    function Bundesland: IBundeslandDictionary;
    function Modul: IModulDictionary;
  end;
  
  ICckAuftragDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Auftragsjahr: TLinqProjection;
    function LfbisHauptbetrieb: TLinqProjection;
    function Auftragsid: TLinqProjection;
    function Bkb: TLinqProjection;
    function Bbknr: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Vorname: TLinqProjection;
    function Nachname: TLinqProjection;
    function PlzBew: TLinqProjection;
    function OrtBew: TLinqProjection;
    function AdresseBew: TLinqProjection;
    function GemeindekzBew: TLinqProjection;
    function TelFestnetz: TLinqProjection;
    function Flag1: TLinqProjection;
    function Flag2: TLinqProjection;
    function Flag3: TLinqProjection;
    function Info1: TLinqProjection;
    function Info2: TLinqProjection;
    function Info3: TLinqProjection;
    function AbgeschlossenAm: TLinqProjection;
    function IdBearbeiter: TLinqProjection;
    function IdBewerter: TLinqProjection;
    function LetzteRestabfrage: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function TstampUpdate: TLinqProjection;
    function Version: TLinqProjection;
    function Auftragsart: ICckAuftragsartDictionary;
    function Dokument: IDokumentDictionary;
    function Betriebsdaten: ICckBetriebDictionary;
  end;
  
  ICckAuftragsartDictionary = interface(IAureliusEntityDictionary)
    function Bezeichnung: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function CckAuftraege: ICckAuftragDictionary;
  end;
  
  ICckAuftragsbewertungDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bewertetam: TLinqProjection;
    function Kontrolliert: TLinqProjection;
    function Ok: TLinqProjection;
    function Auffaellig: TLinqProjection;
    function Gerverstossok: TLinqProjection;
    function Vorsatz: TLinqProjection;
    function Ausmass: TLinqProjection;
    function Schwere: TLinqProjection;
    function Dauer: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function KzHv: TLinqProjection;
    function Auswahldaten: ICckAuswahlDictionary;
    function Anforderung: ICckModulAnforderungDictionary;
  end;
  
  ICckAuswahlDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function AuswahlId: TLinqProjection;
    function Auswahldatum: TLinqProjection;
    function Auswahlgrund: TLinqProjection;
    function Status: TLinqProjection;
    function CckBetriebsdaten: ICckBetriebDictionary;
    function Modul: ICckModulDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
  end;
  
  ICckBetriebDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function Lfbis: TLinqProjection;
    function PlzBetr: TLinqProjection;
    function OrtBetr: TLinqProjection;
    function AdresseBetr: TLinqProjection;
    function GemeindekzBetr: TLinqProjection;
    function LnFlaeche: TLinqProjection;
    function Tgd: TLinqProjection;
    function Tierhalter: TLinqProjection;
    function IdBetrieb: TLinqProjection;
    function CckAuftrag: ICckAuftragDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function CckAuswahldaten: ICckAuswahlDictionary;
  end;
  
  ICckModulDictionary = interface(IAureliusEntityDictionary)
    function Modul: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function CckAuswahldaten: ICckAuswahlDictionary;
    function CckModulAnforderungen: ICckModulAnforderungDictionary;
    function CckModulKontrolltypen: ICckModulKontrolltypDictionary;
  end;
  
  ICckModulAnforderungDictionary = interface(IAureliusEntityDictionary)
    function Anforderung: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Modul: ICckModulDictionary;
    function CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
  end;
  
  ICckModulKontrolltypDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function CckModul: ICckModulDictionary;
    function Bkbtyp: IKontrolltypDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  ICckStatusDictionary = interface(IAureliusEntityDictionary)
    function Status: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  ICckStatusMeldungDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Meldedatum: TLinqProjection;
    function Kommentar: TLinqProjection;
    function Kontakt: TLinqProjection;
    function BewertungsVersion: TLinqProjection;
    function Bkb: IBkbnummerDictionary;
    function Status: ICckStatusDictionary;
    function BkbAuftrag: IBkbnummerDictionary;
  end;
  
  ICckTierdatenDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function IdCckBetriebsdaten: TLinqProjection;
    function TierdatenId: TLinqProjection;
    function Tierkategorie: TLinqProjection;
    function Anzahl: TLinqProjection;
    function VisTierart: TLinqProjection;
    function VisPrbkat: TLinqProjection;
  end;
  
  ICckVokSanktionDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function IdCckBetriebsdaten: TLinqProjection;
    function VokSanktionenId: TLinqProjection;
    function VokSank: TLinqProjection;
    function Jahr: TLinqProjection;
  end;
  
  IChecklisteDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function GueltigAb: TLinqProjection;
    function GueltigBis: TLinqProjection;
    function Version: TLinqProjection;
    function Versionstext: TLinqProjection;
    function Ccrelevant: TLinqProjection;
    function VisBkbtid: TLinqProjection;
    function Privat: TLinqProjection;
    function LastChange: TLinqProjection;
    function BesitzerBundesland: IBundeslandDictionary;
    function LastChangeUser: IUserDictionary;
    function Fragen: IFrageDictionary;
    function ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
  end;
  
  IChecklistenKontrolltypDictionary = interface(IAureliusEntityDictionary)
    function Checkliste: IChecklisteDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function BundeslaenderChecklistenKontrolltypen: IBundeslandChecklistenKontrolltypDictionary;
  end;
  
  IDokumentDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Typ: TLinqProjection;
    function Dateiname: TLinqProjection;
    function ErstelltAm: TLinqProjection;
    function Dokument: TLinqProjection;
    function CckAuftraege: ICckAuftragDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
  end;
  
  IEmailArtenDictionary = interface(IAureliusEntityDictionary)
    function Art: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  IEmailHistoryDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function DatumGesendet: TLinqProjection;
    function Subject: TLinqProjection;
    function Body: TLinqProjection;
    function EmailFrom: TLinqProjection;
    function EmailTo: TLinqProjection;
    function EmailCc: TLinqProjection;
    function EmailBc: TLinqProjection;
    function Host: TLinqProjection;
    function Error: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function Attachments: IEmailHistoryAttachmentDictionary;
  end;
  
  IEmailHistoryAttachmentDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Filename: TLinqProjection;
    function Email: IEmailHistoryDictionary;
  end;
  
  IEmailTexteDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Text: TLinqProjection;
    function Art: IEmailArtenDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  IFormatierungDictionary = interface(IAureliusEntityDictionary)
    function Code: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  IFrageDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function IdExtern: TLinqProjection;
    function ExternQuelle: TLinqProjection;
    function Lfnr: TLinqProjection;
    function Fragennr: TLinqProjection;
    function Text: TLinqProjection;
    function Info: TLinqProjection;
    function CCRelevant: TLinqProjection;
    function Checkliste: IChecklisteDictionary;
    function Formatierung: IFormatierungDictionary;
    function UebergeordneteFrage: IFrageDictionary;
    function Gruppe: IFragengruppeDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function UntergeordneteFragen: IFrageDictionary;
    function FragenKontrollbereiche: IFrageKontrollbereichDictionary;
  end;
  
  IFrageBewertungDictionary = interface(IAureliusEntityDictionary)
    function Ausblenden: TLinqProjection;
    function Positiv: TLinqProjection;
    function Sortierung: TLinqProjection;
    function Bewertung: IBewertungDictionary;
    function Frage: IFrageDictionary;
    function StandardMassnahme: IMassnahmeDictionary;
    function StandardMangeltyp: IMangeltypDictionary;
  end;
  
  IFrageKontrollbereichDictionary = interface(IAureliusEntityDictionary)
    function Frage: IFrageDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
  end;
  
  IFragengruppeDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Farbcode: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Zuzsatztext: TLinqProjection;
    function UebergeordneteGruppe: IFragengruppeDictionary;
  end;
  
  IFunktionDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Hinttext: TLinqProjection;
    function Farbcode: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Progcallid: TLinqProjection;
    function Objekt: TLinqProjection;
    function Prgteil: TLinqProjection;
    function Position: TLinqProjection;
    function Beschriftung: TLinqProjection;
    function Mutter: IFunktionDictionary;
    function Programmmodul: IProgrammModulDictionary;
    function UnterFunktionen: IFunktionDictionary;
    function Funktionsrollen: IFunktionRolleDictionary;
  end;
  
  IFunktionRolleDictionary = interface(IAureliusEntityDictionary)
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Funktion: IFunktionDictionary;
    function Rolle: IRolleDictionary;
  end;
  
  IGemeindeDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Gemeindekennziffer: TLinqProjection;
    function Gemeindename: TLinqProjection;
    function Gemeindecode: TLinqProjection;
    function Amtsplz: TLinqProjection;
    function PolBezKennzif: TLinqProjection;
    function PolBezirk: TLinqProjection;
    function PolBezCode: TLinqProjection;
    function LandIso2: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
  end;
  
  IGruppeDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Okz: TLinqProjection;
    function Persoenlich: TLinqProjection;
    function Email: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Muttergruppe: IGruppeDictionary;
    function Hauptverantwortlicher: IUserDictionary;
    function Stellvertreter: IUserDictionary;
    function Untergruppen: IGruppeDictionary;
    function Nachrichten: INachrichtDictionary;
    function Usergruppen: IUsergruppeDictionary;
    function TodoList: ITodoDictionary;
  end;
  
  IKbProbeDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Probenbkb: TLinqProjection;
    function Probenart: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function Datum: TLinqProjection;
    function VorgMenge: TLinqProjection;
    function Beschaffenheit: TLinqProjection;
    function Futtertyp: TLinqProjection;
    function Verwendungszweck: TLinqProjection;
    function TierArtLisa: TLinqProjection;
    function TierKategorie: TLinqProjection;
    function Beimischrate: TLinqProjection;
    function Verpackung: TLinqProjection;
    function Verschluss: TLinqProjection;
    function Versiegelt: TLinqProjection;
    function HerkZukauf: TLinqProjection;
    function Untersuchungsauftrag: TLinqProjection;
    function Status: TLinqProjection;
    function Verdacht: TLinqProjection;
    function GegenprobeBelassen: TLinqProjection;
    function Exportname: TLinqProjection;
    function Exporttime: TLinqProjection;
    function Agesauftragsnummer: TLinqProjection;
    function Agesprobennummer: TLinqProjection;
    function AgesAuftragsstatus: TLinqProjection;
    function AgesProbenstatus: TLinqProjection;
    function Probenbezeichnung: TLinqProjection;
    function Futterart: TLinqProjection;
    function Probenkennung: TLinqProjection;
    function GpsLat: TLinqProjection;
    function GpsLon: TLinqProjection;
    function KennzeichnungGegenprobe: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function Einsender: IPersonDictionary;
    function Bkbtyp: IBkbTypDictionary;
    function Dokument: IDokumentDictionary;
    function AgesErgebnisbericht: IDokumentDictionary;
    function Bilder: IKontrollberichtBildDictionary;
  end;
  
  IKommunikationsartDictionary = interface(IAureliusEntityDictionary)
    function Art: TLinqProjection;
    function Kommunikationswege: IKommunikationswegDictionary;
  end;
  
  IKommunikationswegDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Value: TLinqProjection;
    function Art: IKommunikationsartDictionary;
    function BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
    function PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
  end;
  
  IKontrollbereichDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function VisKkattid: TLinqProjection;
    function Pflichtbereich: TLinqProjection;
    function FragenKontrollbereiche: IFrageKontrollbereichDictionary;
    function MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
  end;
  
  IKontrollberichtDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Bkb: TLinqProjection;
    function Datum: TLinqProjection;
    function RefBkb: TLinqProjection;
    function Probenziehung: TLinqProjection;
    function RegnrOrt: TLinqProjection;
    function Kurzbemerkung: TLinqProjection;
    function Startzeit: TLinqProjection;
    function Endezeit: TLinqProjection;
    function BestaetigtUm: TLinqProjection;
    function Status: TLinqProjection;
    function AngemeldetUm: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function Lastchange: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Verweigerungsgrund: TLinqProjection;
    function VerweigerungsgrundUnterschrift: TLinqProjection;
    function FehlerhaftGesetztAm: TLinqProjection;
    function StorniertAm: TLinqProjection;
    function Stornogrund: TLinqProjection;
    function VerweigertAm: TLinqProjection;
    function KontrollInformationen: TLinqProjection;
    function InterneNotiz: TLinqProjection;
    function BetriebName: TLinqProjection;
    function BetriebRegnr: TLinqProjection;
    function BetriebBld: TLinqProjection;
    function BetriebAdresseStrasse: TLinqProjection;
    function BetriebAdresseZusatz: TLinqProjection;
    function BetriebAdressePlz: TLinqProjection;
    function BetriebAdresseOrt: TLinqProjection;
    function KontrolltypBezeichnung: TLinqProjection;
    function BkbtypBezeichnung: TLinqProjection;
    function ErfasserNachname: TLinqProjection;
    function ErfasserVorname: TLinqProjection;
    function ErfasserAnrede: TLinqProjection;
    function ErfasserTitel: TLinqProjection;
    function KontrollorganNachname: TLinqProjection;
    function KontrollorganVorname: TLinqProjection;
    function KontrollorganAnrede: TLinqProjection;
    function KontrollorganTitel: TLinqProjection;
    function MaengelGesamt: TLinqProjection;
    function MaengelOffen: TLinqProjection;
    function TodoCount: TLinqProjection;
    function EffectiveDate: TLinqProjection;
    function Prioritaet: TLinqProjection;
    function SeuchenId: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Erfasser: IPersonDictionary;
    function Kontrollorgan: IPersonDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function UnterschriftAnwesenderBetrieb: IUnterschriftDictionary;
    function UnterschriftKontrollorgan: IUnterschriftDictionary;
    function Dokument: IDokumentDictionary;
    function DokumentCC: IDokumentDictionary;
    function GruppeQuelle: IGruppeDictionary;
    function Revisionsplan: IRevisionsplanDictionary;
    function Todos: ITodoDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function Proben: IKbProbeDictionary;
    function Anwesende: IAnwesenderDictionary;
    function CckAuswahldaten: ICckAuswahlDictionary;
    function Oertlichkeiten: IKontrollberichtOertlichkeitDictionary;
    function CckBetriebsdaten: ICckBetriebDictionary;
  end;
  
  IKontrollberichtBildDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bild: TLinqProjection;
    function Format: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function Aufnahmedatum: TLinqProjection;
    function GpsLat: TLinqProjection;
    function GpsLon: TLinqProjection;
    function BewerteteFrage: IBewerteteFrageDictionary;
    function Mangel: IMangelDictionary;
    function AufgenommenVon: IUserDictionary;
    function Probe: IKbProbeDictionary;
  end;
  
  IKontrollberichtOertlichkeitDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bautyp: TLinqProjection;
    function Haltungsform: TLinqProjection;
    function Krankheiten: TLinqProjection;
    function Krankheitsbeschreibung: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function OertlichkeitMaengel: IMangelOertlichkeitDictionary;
  end;
  
  IKontrolltypDictionary = interface(IAureliusEntityDictionary)
    function Kontrolltyp: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Refbkb: TLinqProjection;
    function FreieAdresse: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Proben: TLinqProjection;
    function Oertlichkeiten: TLinqProjection;
    function AutoKontrolliert: TLinqProjection;
    function Bkbtyp: IBkbTypDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
    function CckModulKontrolltypen: ICckModulKontrolltypDictionary;
    function KontrolltypReports: IKontrolltypReportDictionary;
    function ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
  end;
  
  IKontrolltypReportDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Vorschau: TLinqProjection;
    function Mailbetrieb: TLinqProjection;
    function Mailkontrolleur: TLinqProjection;
    function Mailgruppe: TLinqProjection;
    function Mailmuttergruppe: TLinqProjection;
    function Mailmastergruppe: TLinqProjection;
    function Kontrolltyp: IKontrolltypDictionary;
    function Bundesland: IBundeslandDictionary;
    function Report: IReportDictionary;
    function ReportTyp: IReportTypDictionary;
  end;
  
  ILandDictionary = interface(IAureliusEntityDictionary)
    function Landkz: TLinqProjection;
    function Landnr: TLinqProjection;
    function LandIso3: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bundeslaender: IBundeslandDictionary;
  end;
  
  IMangelDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Frist: TLinqProjection;
    function Text: TLinqProjection;
    function BeseitigtAm: TLinqProjection;
    function StorniertAm: TLinqProjection;
    function Behebungsauftrag: TLinqProjection;
    function Status: IMangelStatusDictionary;
    function Mangeltyp: IMangeltypDictionary;
    function Massnahme: IMassnahmeDictionary;
    function MangelWeiterfuehrung: IMangelDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function Bilder: IKontrollberichtBildDictionary;
    function MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
    function WeitergefuehrtVonMangel: IMangelDictionary;
    function MangelOertlichkeiten: IMangelOertlichkeitDictionary;
  end;
  
  IMangelKontrollbereichDictionary = interface(IAureliusEntityDictionary)
    function Mangel: IMangelDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
  end;
  
  IMangelOertlichkeitDictionary = interface(IAureliusEntityDictionary)
    function Mangel: IMangelDictionary;
    function Oertlichkeit: IKontrollberichtOertlichkeitDictionary;
  end;
  
  IMangelStatusDictionary = interface(IAureliusEntityDictionary)
    function Status: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Maengel: IMangelDictionary;
  end;
  
  IMangeltypDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bkbtyp: IBkbTypDictionary;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function Maengel: IMangelDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
  end;
  
  IMassnahmeDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Langtext: TLinqProjection;
    function Standardfrist: TLinqProjection;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function Maengel: IMangelDictionary;
  end;
  
  IMassnahmenkatalogDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Massnahmen: IMassnahmeDictionary;
    function Mangeltypen: IMangeltypDictionary;
  end;
  
  IModulDictionary = interface(IAureliusEntityDictionary)
    function Modul: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeUser: TLinqProjection;
    function BundeslaenderModule: IBundeslandModulDictionary;
    function Bkbtypen: IBkbTypDictionary;
  end;
  
  IModulInstanzDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function InstanzName: TLinqProjection;
    function InstanzUrl: TLinqProjection;
    function Modul: IProgrammModulDictionary;
  end;
  
  IModuleBkbtypenDictionary = interface(IAureliusEntityDictionary)
    function IdModul: TLinqProjection;
    function Bkbtyp: TLinqProjection;
  end;
  
  INachrichtDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Status: TLinqProjection;
    function Typ: TLinqProjection;
    function AbsenderKz: TLinqProjection;
    function Priori: TLinqProjection;
    function Text: TLinqProjection;
    function Link: TLinqProjection;
    function GesendetAm: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function Absender: IUserDictionary;
    function Zustellungen: INachrichtenZustellungDictionary;
    function VNachrichtenUser: IVNachrichtForUserDictionary;
  end;
  
  INachrichtenZustellungDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function GesehenTstamp: TLinqProjection;
    function GelesenTstamp: TLinqProjection;
    function Nachricht: INachrichtDictionary;
    function User: IUserDictionary;
  end;
  
  IPersonDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Titel: TLinqProjection;
    function Vorname: TLinqProjection;
    function Nachname: TLinqProjection;
    function AdresseALT: TLinqProjection;
    function OrtALT: TLinqProjection;
    function PlzALT: TLinqProjection;
    function Landkz: TLinqProjection;
    function Telefon: TLinqProjection;
    function Email: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeDbuser: TLinqProjection;
    function Anrede: TLinqProjection;
    function Adresse: IAdresseDictionary;
    function KontrollberichteErfasser: IKontrollberichtDictionary;
    function KontrollberichteKontrollorgan: IKontrollberichtDictionary;
    function Kommunikationswege: IPersonenKommunikationswegeDictionary;
    function Users: IUserDictionary;
  end;
  
  IPersonenKommunikationswegeDictionary = interface(IAureliusEntityDictionary)
    function Kommunikationsweg: IKommunikationswegDictionary;
    function Person: IPersonDictionary;
  end;
  
  IProgrammModulDictionary = interface(IAureliusEntityDictionary)
    function Kurzbezeichnnung: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Funktionen: IFunktionDictionary;
  end;
  
  IRechtsgrundlageDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Kurzbezeichnung: TLinqProjection;
    function UnterschriftErforderlich: TLinqProjection;
    function Kontrollberichte: IKontrollberichtDictionary;
    function Bkbtypen: IBkbtypenRechtsgrundlageDictionary;
  end;
  
  IRegistrierungDictionary = interface(IAureliusEntityDictionary)
    function Regnr: TLinqProjection;
    function FreierBetrieb: TLinqProjection;
    function Zulassungen: IZulassungDictionary;
    function Betriebe: IBetriebDictionary;
  end;
  
  IReportDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Reporttyp: TLinqProjection;
    function Reportvorlage: TLinqProjection;
    function ReportKontrolltypen: IKontrolltypReportDictionary;
  end;
  
  IReportTypDictionary = interface(IAureliusEntityDictionary)
    function Typ: TLinqProjection;
    function Bezeichnung: TLinqProjection;
  end;
  
  IRevisionsSchemaDictionary = interface(IAureliusEntityDictionary)
    function RevSchema: TLinqProjection;
    function Geplant: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  IRevisionsplanDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Jahr: TLinqProjection;
    function RisikoKategorie: TLinqProjection;
    function JMindestKontrollFrequenz: TLinqProjection;
    function AnzBetriebeImLand: TLinqProjection;
    function AnzGesamtKontrollen: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function Bldcode: IBundeslandDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
  end;
  
  IRevisionsstammDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Sektion: TLinqProjection;
    function BetriebsgruppeLm: TLinqProjection;
    function BetriebsgruppeDetail: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function Betriebsgruppenkz: TLinqProjection;
    function KontrollTyp: IKontrolltypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
  end;
  
  IRolleDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Defaultrolle: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function GueltigAb: TLinqProjection;
    function Parameter: TLinqProjection;
    function FunktionenRollen: IFunktionRolleDictionary;
    function UserRollen: IUserrolleDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
  end;
  
  IRollenBkbtypenDictionary = interface(IAureliusEntityDictionary)
    function Bkbtyp: IBkbTypDictionary;
    function Rolle: IRolleDictionary;
  end;
  
  ISysDictionary = interface(IAureliusEntityDictionary)
    function Systemkz: TLinqProjection;
    function Systemname: TLinqProjection;
    function VersionDb: TLinqProjection;
    function Bkbnummern: IBkbnummerDictionary;
  end;
  
  ITodoDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Titel: TLinqProjection;
    function Faellig: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
  end;
  
  IUnterschriftDictionary = interface(IAureliusEntityDictionary)
    function Guid: TLinqProjection;
    function Bild: TLinqProjection;
    function Datum: TLinqProjection;
    function Name: TLinqProjection;
    function IdPerson: TLinqProjection;
    function KontrollberichteAnwesender: IKontrollberichtDictionary;
    function KontrollberichteKontrollorgan: IKontrollberichtDictionary;
  end;
  
  IUserDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Username: TLinqProjection;
    function Userguid: TLinqProjection;
    function Usertype: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function Userpwc: TLinqProjection;
    function Aktiv: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeDbuser: TLinqProjection;
    function Email: TLinqProjection;
    function LastLogin: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Person: IPersonDictionary;
    function StellvertreterFuerGruppen: IGruppeDictionary;
    function Nachrichten: INachrichtDictionary;
    function NachrichtenZustellungen: INachrichtenZustellungDictionary;
    function Usergruppen: IUsergruppeDictionary;
    function Userrollen: IUserrolleDictionary;
    function Todos: ITodoDictionary;
    function HauptverantwortlicherFuerGruppen: IGruppeDictionary;
    function KontrollberichtBilder: IKontrollberichtBildDictionary;
  end;
  
  IUsergruppeDictionary = interface(IAureliusEntityDictionary)
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
  end;
  
  IUserrolleDictionary = interface(IAureliusEntityDictionary)
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Rolle: IRolleDictionary;
    function User: IUserDictionary;
    function ModulInstanz: IModulInstanzDictionary;
  end;
  
  IVNachrichtForUserDictionary = interface(IAureliusEntityDictionary)
    function GesehenTstamp: TLinqProjection;
    function GelesenTstamp: TLinqProjection;
    function Nachricht: INachrichtDictionary;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
    function NachrichtenZustellung: INachrichtenZustellungDictionary;
  end;
  
  IVUserGroupMembershipDictionary = interface(IAureliusEntityDictionary)
    function MembershipLevel: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
  end;
  
  IVbetriebbezirksgruppeDictionary = interface(IAureliusEntityDictionary)
    function Gemeindekennziffer: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Gruppe: IGruppeDictionary;
  end;
  
  IVbetriebsinfoDictionary = interface(IAureliusEntityDictionary)
    function BetriebsId: TLinqProjection;
    function Regnr: TLinqProjection;
    function BetriebsdatenId: TLinqProjection;
    function Name: TLinqProjection;
    function IdCckAuftrag: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function RegnrHauptbetrieb: TLinqProjection;
    function IdHauptbetrieb: TLinqProjection;
    function Ort: TLinqProjection;
    function Taetigkeiten: TLinqProjection;
    function Zulassungsnummern: TLinqProjection;
  end;
  
  IVgroupuserDictionary = interface(IAureliusEntityDictionary)
    function Membershiplevel: TLinqProjection;
    function User: IUserDictionary;
    function GroupUser: IUserDictionary;
    function Group: IGruppeDictionary;
    function GroupPerson: IPersonDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  IZulassungDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Zulnr: TLinqProjection;
    function Beginndatum: TLinqProjection;
    function Enddatum: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Registrierung: IRegistrierungDictionary;
  end;
  
  IZusatztextDictionary = interface(IAureliusEntityDictionary)
    function Id: TLinqProjection;
    function Tabelle: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Langtext: TLinqProjection;
  end;
  
  IvUserKontrollenDictionary = interface(IAureliusEntityDictionary)
    function IdUser: TLinqProjection;
    function Status: TLinqProjection;
    function Datum: TLinqProjection;
    function Endezeit: TLinqProjection;
    function Faellig: TLinqProjection;
    function IdErfasser: TLinqProjection;
    function IdKontrollorgan: TLinqProjection;
    function IdGruppeQuelle: TLinqProjection;
    function EffectiveDate: TLinqProjection;
    function GruppeLevel: TLinqProjection;
    function StatusPrio: TLinqProjection;
    function Sort: TLinqProjection;
    function Kontrolle: IKontrollberichtDictionary;
  end;
  
  TAPTypDictionary = class(TAureliusEntityDictionary, IAPTypDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Hauptansprechpartner: TLinqProjection;
  end;
  
  TAdresseDictionary = class(TAureliusEntityDictionary, IAdresseDictionary)
  public
    function Id: TLinqProjection;
    function Ort: TLinqProjection;
    function Plz: TLinqProjection;
    function Strasse: TLinqProjection;
    function Adresszusatz: TLinqProjection;
    function Xkoord31287: TLinqProjection;
    function Ykoord31287: TLinqProjection;
    function Xkoord4326: TLinqProjection;
    function Ykoord4326: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Gemeinde: IGemeindeDictionary;
  end;
  
  TAnsprechpartnerDictionary = class(TAureliusEntityDictionary, IAnsprechpartnerDictionary)
  public
    function Id: TLinqProjection;
    function Regnr: TLinqProjection;
    function KommunikationsBerechtigt: TLinqProjection;
    function Aptyp: IAPTypDictionary;
    function Person: IPersonDictionary;
  end;
  
  TAnwesenderDictionary = class(TAureliusEntityDictionary, IAnwesenderDictionary)
  public
    function Guid: TLinqProjection;
    function Name: TLinqProjection;
    function Email: TLinqProjection;
    function Kommunikationsberechtigt: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function APTyp: IAPTypDictionary;
    function Person: IPersonDictionary;
  end;
  
  TBetriebDictionary = class(TAureliusEntityDictionary, IBetriebDictionary)
  public
    function Id: TLinqProjection;
    function Name: TLinqProjection;
    function Aufsichtsorgan: TLinqProjection;
    function Telefon: TLinqProjection;
    function Email: TLinqProjection;
    function Vergebuehrung: TLinqProjection;
    function Vulgo: TLinqProjection;
    function Anmerkung: TLinqProjection;
    function Bvbkz: TLinqProjection;
    function Bbknr: TLinqProjection;
    function Adresse: IAdresseDictionary;
    function Bundesland: IBundeslandDictionary;
    function Registrierung: IRegistrierungDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
    function RevisionsGruppe: IGruppeDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
    function Kommunikationswege: IBetriebeKommunikationswegeDictionary;
    function RevStaemme: IBetriebRevstammDictionary;
    function Bezirksgruppen: IVbetriebbezirksgruppeDictionary;
  end;
  
  TBetriebRevstammDictionary = class(TAureliusEntityDictionary, IBetriebRevstammDictionary)
  public
    function Id: TLinqProjection;
    function Begdate: TLinqProjection;
    function Enddate: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
  end;
  
  TBetriebeKommunikationswegeDictionary = class(TAureliusEntityDictionary, IBetriebeKommunikationswegeDictionary)
  public
    function Betrieb: IBetriebDictionary;
    function Kommunikationsweg: IKommunikationswegDictionary;
  end;
  
  TBewerteteFrageDictionary = class(TAureliusEntityDictionary, IBewerteteFrageDictionary)
  public
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Zusatztext: TLinqProjection;
    function Wert: TLinqProjection;
    function GpsLon: TLinqProjection;
    function GpsLat: TLinqProjection;
    function Bewertung: IBewertungDictionary;
    function Frage: IFrageDictionary;
    function Bericht: IKontrollberichtDictionary;
    function Mangel: IMangelDictionary;
    function Bilder: IKontrollberichtBildDictionary;
  end;
  
  TBewertungDictionary = class(TAureliusEntityDictionary, IBewertungDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Icon: IBewertungsIconDictionary;
    function Typ: IBewertungstypDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
  end;
  
  TBewertungsIconDictionary = class(TAureliusEntityDictionary, IBewertungsIconDictionary)
  public
    function Icon: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Bewertungen: IBewertungDictionary;
  end;
  
  TBewertungstypDictionary = class(TAureliusEntityDictionary, IBewertungstypDictionary)
  public
    function Typ: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Bewertungen: IBewertungDictionary;
  end;
  
  TBkbTypDictionary = class(TAureliusEntityDictionary, IBkbTypDictionary)
  public
    function Typ: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Lastchange: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function SecclassMin: TLinqProjection;
    function Probe: TLinqProjection;
    function Modul: IModulDictionary;
    function Kontrolltypen: IKontrolltypDictionary;
    function BkbtypenRechtsgrundlagen: IBkbtypenRechtsgrundlageDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
    function Bkbnummern: IBkbnummerDictionary;
    function Mangeltypen: IMangeltypDictionary;
  end;
  
  TBkbnummerDictionary = class(TAureliusEntityDictionary, IBkbnummerDictionary)
  public
    function Nummer: TLinqProjection;
    function Jahr: TLinqProjection;
    function LfdNr: TLinqProjection;
    function LfdNrHex: TLinqProjection;
    function Systemkz: ISysDictionary;
    function Bkbtyp: IBkbTypDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  TBkbtypenRechtsgrundlageDictionary = class(TAureliusEntityDictionary, IBkbtypenRechtsgrundlageDictionary)
  public
    function Bkbtyp: IBkbTypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
  end;
  
  TBundeslandDictionary = class(TAureliusEntityDictionary, IBundeslandDictionary)
  public
    function Bldcode: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Kurztext: TLinqProjection;
    function Region: TLinqProjection;
    function Bldlogo: TLinqProjection;
    function Okz: TLinqProjection;
    function Bkbkz: TLinqProjection;
    function Land: ILandDictionary;
    function Betriebe: IBetriebDictionary;
    function BundeslaenderModule: IBundeslandModulDictionary;
    function Gruppen: IGruppeDictionary;
    function Users: IUserDictionary;
    function Adressen: IAdresseDictionary;
    function Gemeinden: IGemeindeDictionary;
    function BkbNummern: IBkbnummerDictionary;
    function EmailTexte: IEmailTexteDictionary;
    function BesitztChecklisten: IChecklisteDictionary;
    function SichtbareChecklisten: IBundeslandChecklistenKontrolltypDictionary;
  end;
  
  TBundeslandChecklistenKontrolltypDictionary = class(TAureliusEntityDictionary, IBundeslandChecklistenKontrolltypDictionary)
  public
    function GueltigAb: TLinqProjection;
    function GueltigBis: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function ChecklisteKontrolltyp: IChecklistenKontrolltypDictionary;
  end;
  
  TBundeslandModulDictionary = class(TAureliusEntityDictionary, IBundeslandModulDictionary)
  public
    function Bundesland: IBundeslandDictionary;
    function Modul: IModulDictionary;
  end;
  
  TCckAuftragDictionary = class(TAureliusEntityDictionary, ICckAuftragDictionary)
  public
    function Id: TLinqProjection;
    function Auftragsjahr: TLinqProjection;
    function LfbisHauptbetrieb: TLinqProjection;
    function Auftragsid: TLinqProjection;
    function Bkb: TLinqProjection;
    function Bbknr: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Vorname: TLinqProjection;
    function Nachname: TLinqProjection;
    function PlzBew: TLinqProjection;
    function OrtBew: TLinqProjection;
    function AdresseBew: TLinqProjection;
    function GemeindekzBew: TLinqProjection;
    function TelFestnetz: TLinqProjection;
    function Flag1: TLinqProjection;
    function Flag2: TLinqProjection;
    function Flag3: TLinqProjection;
    function Info1: TLinqProjection;
    function Info2: TLinqProjection;
    function Info3: TLinqProjection;
    function AbgeschlossenAm: TLinqProjection;
    function IdBearbeiter: TLinqProjection;
    function IdBewerter: TLinqProjection;
    function LetzteRestabfrage: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function TstampUpdate: TLinqProjection;
    function Version: TLinqProjection;
    function Auftragsart: ICckAuftragsartDictionary;
    function Dokument: IDokumentDictionary;
    function Betriebsdaten: ICckBetriebDictionary;
  end;
  
  TCckAuftragsartDictionary = class(TAureliusEntityDictionary, ICckAuftragsartDictionary)
  public
    function Bezeichnung: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function CckAuftraege: ICckAuftragDictionary;
  end;
  
  TCckAuftragsbewertungDictionary = class(TAureliusEntityDictionary, ICckAuftragsbewertungDictionary)
  public
    function Id: TLinqProjection;
    function Bewertetam: TLinqProjection;
    function Kontrolliert: TLinqProjection;
    function Ok: TLinqProjection;
    function Auffaellig: TLinqProjection;
    function Gerverstossok: TLinqProjection;
    function Vorsatz: TLinqProjection;
    function Ausmass: TLinqProjection;
    function Schwere: TLinqProjection;
    function Dauer: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function KzHv: TLinqProjection;
    function Auswahldaten: ICckAuswahlDictionary;
    function Anforderung: ICckModulAnforderungDictionary;
  end;
  
  TCckAuswahlDictionary = class(TAureliusEntityDictionary, ICckAuswahlDictionary)
  public
    function Id: TLinqProjection;
    function AuswahlId: TLinqProjection;
    function Auswahldatum: TLinqProjection;
    function Auswahlgrund: TLinqProjection;
    function Status: TLinqProjection;
    function CckBetriebsdaten: ICckBetriebDictionary;
    function Modul: ICckModulDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
  end;
  
  TCckBetriebDictionary = class(TAureliusEntityDictionary, ICckBetriebDictionary)
  public
    function Id: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function Lfbis: TLinqProjection;
    function PlzBetr: TLinqProjection;
    function OrtBetr: TLinqProjection;
    function AdresseBetr: TLinqProjection;
    function GemeindekzBetr: TLinqProjection;
    function LnFlaeche: TLinqProjection;
    function Tgd: TLinqProjection;
    function Tierhalter: TLinqProjection;
    function IdBetrieb: TLinqProjection;
    function CckAuftrag: ICckAuftragDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function CckAuswahldaten: ICckAuswahlDictionary;
  end;
  
  TCckModulDictionary = class(TAureliusEntityDictionary, ICckModulDictionary)
  public
    function Modul: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function CckAuswahldaten: ICckAuswahlDictionary;
    function CckModulAnforderungen: ICckModulAnforderungDictionary;
    function CckModulKontrolltypen: ICckModulKontrolltypDictionary;
  end;
  
  TCckModulAnforderungDictionary = class(TAureliusEntityDictionary, ICckModulAnforderungDictionary)
  public
    function Anforderung: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Modul: ICckModulDictionary;
    function CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
  end;
  
  TCckModulKontrolltypDictionary = class(TAureliusEntityDictionary, ICckModulKontrolltypDictionary)
  public
    function Id: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function CckModul: ICckModulDictionary;
    function Bkbtyp: IKontrolltypDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  TCckStatusDictionary = class(TAureliusEntityDictionary, ICckStatusDictionary)
  public
    function Status: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  TCckStatusMeldungDictionary = class(TAureliusEntityDictionary, ICckStatusMeldungDictionary)
  public
    function Id: TLinqProjection;
    function Meldedatum: TLinqProjection;
    function Kommentar: TLinqProjection;
    function Kontakt: TLinqProjection;
    function BewertungsVersion: TLinqProjection;
    function Bkb: IBkbnummerDictionary;
    function Status: ICckStatusDictionary;
    function BkbAuftrag: IBkbnummerDictionary;
  end;
  
  TCckTierdatenDictionary = class(TAureliusEntityDictionary, ICckTierdatenDictionary)
  public
    function Id: TLinqProjection;
    function IdCckBetriebsdaten: TLinqProjection;
    function TierdatenId: TLinqProjection;
    function Tierkategorie: TLinqProjection;
    function Anzahl: TLinqProjection;
    function VisTierart: TLinqProjection;
    function VisPrbkat: TLinqProjection;
  end;
  
  TCckVokSanktionDictionary = class(TAureliusEntityDictionary, ICckVokSanktionDictionary)
  public
    function Id: TLinqProjection;
    function IdCckBetriebsdaten: TLinqProjection;
    function VokSanktionenId: TLinqProjection;
    function VokSank: TLinqProjection;
    function Jahr: TLinqProjection;
  end;
  
  TChecklisteDictionary = class(TAureliusEntityDictionary, IChecklisteDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function GueltigAb: TLinqProjection;
    function GueltigBis: TLinqProjection;
    function Version: TLinqProjection;
    function Versionstext: TLinqProjection;
    function Ccrelevant: TLinqProjection;
    function VisBkbtid: TLinqProjection;
    function Privat: TLinqProjection;
    function LastChange: TLinqProjection;
    function BesitzerBundesland: IBundeslandDictionary;
    function LastChangeUser: IUserDictionary;
    function Fragen: IFrageDictionary;
    function ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
  end;
  
  TChecklistenKontrolltypDictionary = class(TAureliusEntityDictionary, IChecklistenKontrolltypDictionary)
  public
    function Checkliste: IChecklisteDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function BundeslaenderChecklistenKontrolltypen: IBundeslandChecklistenKontrolltypDictionary;
  end;
  
  TDokumentDictionary = class(TAureliusEntityDictionary, IDokumentDictionary)
  public
    function Guid: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Typ: TLinqProjection;
    function Dateiname: TLinqProjection;
    function ErstelltAm: TLinqProjection;
    function Dokument: TLinqProjection;
    function CckAuftraege: ICckAuftragDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
  end;
  
  TEmailArtenDictionary = class(TAureliusEntityDictionary, IEmailArtenDictionary)
  public
    function Art: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  TEmailHistoryDictionary = class(TAureliusEntityDictionary, IEmailHistoryDictionary)
  public
    function Id: TLinqProjection;
    function DatumGesendet: TLinqProjection;
    function Subject: TLinqProjection;
    function Body: TLinqProjection;
    function EmailFrom: TLinqProjection;
    function EmailTo: TLinqProjection;
    function EmailCc: TLinqProjection;
    function EmailBc: TLinqProjection;
    function Host: TLinqProjection;
    function Error: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function Attachments: IEmailHistoryAttachmentDictionary;
  end;
  
  TEmailHistoryAttachmentDictionary = class(TAureliusEntityDictionary, IEmailHistoryAttachmentDictionary)
  public
    function Id: TLinqProjection;
    function Filename: TLinqProjection;
    function Email: IEmailHistoryDictionary;
  end;
  
  TEmailTexteDictionary = class(TAureliusEntityDictionary, IEmailTexteDictionary)
  public
    function Guid: TLinqProjection;
    function Text: TLinqProjection;
    function Art: IEmailArtenDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  TFormatierungDictionary = class(TAureliusEntityDictionary, IFormatierungDictionary)
  public
    function Code: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  TFrageDictionary = class(TAureliusEntityDictionary, IFrageDictionary)
  public
    function Id: TLinqProjection;
    function IdExtern: TLinqProjection;
    function ExternQuelle: TLinqProjection;
    function Lfnr: TLinqProjection;
    function Fragennr: TLinqProjection;
    function Text: TLinqProjection;
    function Info: TLinqProjection;
    function CCRelevant: TLinqProjection;
    function Checkliste: IChecklisteDictionary;
    function Formatierung: IFormatierungDictionary;
    function UebergeordneteFrage: IFrageDictionary;
    function Gruppe: IFragengruppeDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function UntergeordneteFragen: IFrageDictionary;
    function FragenKontrollbereiche: IFrageKontrollbereichDictionary;
  end;
  
  TFrageBewertungDictionary = class(TAureliusEntityDictionary, IFrageBewertungDictionary)
  public
    function Ausblenden: TLinqProjection;
    function Positiv: TLinqProjection;
    function Sortierung: TLinqProjection;
    function Bewertung: IBewertungDictionary;
    function Frage: IFrageDictionary;
    function StandardMassnahme: IMassnahmeDictionary;
    function StandardMangeltyp: IMangeltypDictionary;
  end;
  
  TFrageKontrollbereichDictionary = class(TAureliusEntityDictionary, IFrageKontrollbereichDictionary)
  public
    function Frage: IFrageDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
  end;
  
  TFragengruppeDictionary = class(TAureliusEntityDictionary, IFragengruppeDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Farbcode: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Zuzsatztext: TLinqProjection;
    function UebergeordneteGruppe: IFragengruppeDictionary;
  end;
  
  TFunktionDictionary = class(TAureliusEntityDictionary, IFunktionDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Hinttext: TLinqProjection;
    function Farbcode: TLinqProjection;
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Progcallid: TLinqProjection;
    function Objekt: TLinqProjection;
    function Prgteil: TLinqProjection;
    function Position: TLinqProjection;
    function Beschriftung: TLinqProjection;
    function Mutter: IFunktionDictionary;
    function Programmmodul: IProgrammModulDictionary;
    function UnterFunktionen: IFunktionDictionary;
    function Funktionsrollen: IFunktionRolleDictionary;
  end;
  
  TFunktionRolleDictionary = class(TAureliusEntityDictionary, IFunktionRolleDictionary)
  public
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Funktion: IFunktionDictionary;
    function Rolle: IRolleDictionary;
  end;
  
  TGemeindeDictionary = class(TAureliusEntityDictionary, IGemeindeDictionary)
  public
    function Id: TLinqProjection;
    function Gemeindekennziffer: TLinqProjection;
    function Gemeindename: TLinqProjection;
    function Gemeindecode: TLinqProjection;
    function Amtsplz: TLinqProjection;
    function PolBezKennzif: TLinqProjection;
    function PolBezirk: TLinqProjection;
    function PolBezCode: TLinqProjection;
    function LandIso2: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
  end;
  
  TGruppeDictionary = class(TAureliusEntityDictionary, IGruppeDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Okz: TLinqProjection;
    function Persoenlich: TLinqProjection;
    function Email: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Muttergruppe: IGruppeDictionary;
    function Hauptverantwortlicher: IUserDictionary;
    function Stellvertreter: IUserDictionary;
    function Untergruppen: IGruppeDictionary;
    function Nachrichten: INachrichtDictionary;
    function Usergruppen: IUsergruppeDictionary;
    function TodoList: ITodoDictionary;
  end;
  
  TKbProbeDictionary = class(TAureliusEntityDictionary, IKbProbeDictionary)
  public
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Probenbkb: TLinqProjection;
    function Probenart: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function Datum: TLinqProjection;
    function VorgMenge: TLinqProjection;
    function Beschaffenheit: TLinqProjection;
    function Futtertyp: TLinqProjection;
    function Verwendungszweck: TLinqProjection;
    function TierArtLisa: TLinqProjection;
    function TierKategorie: TLinqProjection;
    function Beimischrate: TLinqProjection;
    function Verpackung: TLinqProjection;
    function Verschluss: TLinqProjection;
    function Versiegelt: TLinqProjection;
    function HerkZukauf: TLinqProjection;
    function Untersuchungsauftrag: TLinqProjection;
    function Status: TLinqProjection;
    function Verdacht: TLinqProjection;
    function GegenprobeBelassen: TLinqProjection;
    function Exportname: TLinqProjection;
    function Exporttime: TLinqProjection;
    function Agesauftragsnummer: TLinqProjection;
    function Agesprobennummer: TLinqProjection;
    function AgesAuftragsstatus: TLinqProjection;
    function AgesProbenstatus: TLinqProjection;
    function Probenbezeichnung: TLinqProjection;
    function Futterart: TLinqProjection;
    function Probenkennung: TLinqProjection;
    function GpsLat: TLinqProjection;
    function GpsLon: TLinqProjection;
    function KennzeichnungGegenprobe: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function Einsender: IPersonDictionary;
    function Bkbtyp: IBkbTypDictionary;
    function Dokument: IDokumentDictionary;
    function AgesErgebnisbericht: IDokumentDictionary;
    function Bilder: IKontrollberichtBildDictionary;
  end;
  
  TKommunikationsartDictionary = class(TAureliusEntityDictionary, IKommunikationsartDictionary)
  public
    function Art: TLinqProjection;
    function Kommunikationswege: IKommunikationswegDictionary;
  end;
  
  TKommunikationswegDictionary = class(TAureliusEntityDictionary, IKommunikationswegDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Value: TLinqProjection;
    function Art: IKommunikationsartDictionary;
    function BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
    function PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
  end;
  
  TKontrollbereichDictionary = class(TAureliusEntityDictionary, IKontrollbereichDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function VisKkattid: TLinqProjection;
    function Pflichtbereich: TLinqProjection;
    function FragenKontrollbereiche: IFrageKontrollbereichDictionary;
    function MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
  end;
  
  TKontrollberichtDictionary = class(TAureliusEntityDictionary, IKontrollberichtDictionary)
  public
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Bkb: TLinqProjection;
    function Datum: TLinqProjection;
    function RefBkb: TLinqProjection;
    function Probenziehung: TLinqProjection;
    function RegnrOrt: TLinqProjection;
    function Kurzbemerkung: TLinqProjection;
    function Startzeit: TLinqProjection;
    function Endezeit: TLinqProjection;
    function BestaetigtUm: TLinqProjection;
    function Status: TLinqProjection;
    function AngemeldetUm: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function Lastchange: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Verweigerungsgrund: TLinqProjection;
    function VerweigerungsgrundUnterschrift: TLinqProjection;
    function FehlerhaftGesetztAm: TLinqProjection;
    function StorniertAm: TLinqProjection;
    function Stornogrund: TLinqProjection;
    function VerweigertAm: TLinqProjection;
    function KontrollInformationen: TLinqProjection;
    function InterneNotiz: TLinqProjection;
    function BetriebName: TLinqProjection;
    function BetriebRegnr: TLinqProjection;
    function BetriebBld: TLinqProjection;
    function BetriebAdresseStrasse: TLinqProjection;
    function BetriebAdresseZusatz: TLinqProjection;
    function BetriebAdressePlz: TLinqProjection;
    function BetriebAdresseOrt: TLinqProjection;
    function KontrolltypBezeichnung: TLinqProjection;
    function BkbtypBezeichnung: TLinqProjection;
    function ErfasserNachname: TLinqProjection;
    function ErfasserVorname: TLinqProjection;
    function ErfasserAnrede: TLinqProjection;
    function ErfasserTitel: TLinqProjection;
    function KontrollorganNachname: TLinqProjection;
    function KontrollorganVorname: TLinqProjection;
    function KontrollorganAnrede: TLinqProjection;
    function KontrollorganTitel: TLinqProjection;
    function MaengelGesamt: TLinqProjection;
    function MaengelOffen: TLinqProjection;
    function TodoCount: TLinqProjection;
    function EffectiveDate: TLinqProjection;
    function Prioritaet: TLinqProjection;
    function SeuchenId: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Erfasser: IPersonDictionary;
    function Kontrollorgan: IPersonDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function UnterschriftAnwesenderBetrieb: IUnterschriftDictionary;
    function UnterschriftKontrollorgan: IUnterschriftDictionary;
    function Dokument: IDokumentDictionary;
    function DokumentCC: IDokumentDictionary;
    function GruppeQuelle: IGruppeDictionary;
    function Revisionsplan: IRevisionsplanDictionary;
    function Todos: ITodoDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function Proben: IKbProbeDictionary;
    function Anwesende: IAnwesenderDictionary;
    function CckAuswahldaten: ICckAuswahlDictionary;
    function Oertlichkeiten: IKontrollberichtOertlichkeitDictionary;
    function CckBetriebsdaten: ICckBetriebDictionary;
  end;
  
  TKontrollberichtBildDictionary = class(TAureliusEntityDictionary, IKontrollberichtBildDictionary)
  public
    function Id: TLinqProjection;
    function Bild: TLinqProjection;
    function Format: TLinqProjection;
    function Bemerkung: TLinqProjection;
    function Aufnahmedatum: TLinqProjection;
    function GpsLat: TLinqProjection;
    function GpsLon: TLinqProjection;
    function BewerteteFrage: IBewerteteFrageDictionary;
    function Mangel: IMangelDictionary;
    function AufgenommenVon: IUserDictionary;
    function Probe: IKbProbeDictionary;
  end;
  
  TKontrollberichtOertlichkeitDictionary = class(TAureliusEntityDictionary, IKontrollberichtOertlichkeitDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bautyp: TLinqProjection;
    function Haltungsform: TLinqProjection;
    function Krankheiten: TLinqProjection;
    function Krankheitsbeschreibung: TLinqProjection;
    function Kontrollbericht: IKontrollberichtDictionary;
    function OertlichkeitMaengel: IMangelOertlichkeitDictionary;
  end;
  
  TKontrolltypDictionary = class(TAureliusEntityDictionary, IKontrolltypDictionary)
  public
    function Kontrolltyp: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Refbkb: TLinqProjection;
    function FreieAdresse: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Proben: TLinqProjection;
    function Oertlichkeiten: TLinqProjection;
    function AutoKontrolliert: TLinqProjection;
    function Bkbtyp: IBkbTypDictionary;
    function Kontrollberichte: IKontrollberichtDictionary;
    function CckModulKontrolltypen: ICckModulKontrolltypDictionary;
    function KontrolltypReports: IKontrolltypReportDictionary;
    function ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
  end;
  
  TKontrolltypReportDictionary = class(TAureliusEntityDictionary, IKontrolltypReportDictionary)
  public
    function Guid: TLinqProjection;
    function Vorschau: TLinqProjection;
    function Mailbetrieb: TLinqProjection;
    function Mailkontrolleur: TLinqProjection;
    function Mailgruppe: TLinqProjection;
    function Mailmuttergruppe: TLinqProjection;
    function Mailmastergruppe: TLinqProjection;
    function Kontrolltyp: IKontrolltypDictionary;
    function Bundesland: IBundeslandDictionary;
    function Report: IReportDictionary;
    function ReportTyp: IReportTypDictionary;
  end;
  
  TLandDictionary = class(TAureliusEntityDictionary, ILandDictionary)
  public
    function Landkz: TLinqProjection;
    function Landnr: TLinqProjection;
    function LandIso3: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bundeslaender: IBundeslandDictionary;
  end;
  
  TMangelDictionary = class(TAureliusEntityDictionary, IMangelDictionary)
  public
    function Id: TLinqProjection;
    function Guid: TLinqProjection;
    function Frist: TLinqProjection;
    function Text: TLinqProjection;
    function BeseitigtAm: TLinqProjection;
    function StorniertAm: TLinqProjection;
    function Behebungsauftrag: TLinqProjection;
    function Status: IMangelStatusDictionary;
    function Mangeltyp: IMangeltypDictionary;
    function Massnahme: IMassnahmeDictionary;
    function MangelWeiterfuehrung: IMangelDictionary;
    function BewerteteFragen: IBewerteteFrageDictionary;
    function Bilder: IKontrollberichtBildDictionary;
    function MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
    function WeitergefuehrtVonMangel: IMangelDictionary;
    function MangelOertlichkeiten: IMangelOertlichkeitDictionary;
  end;
  
  TMangelKontrollbereichDictionary = class(TAureliusEntityDictionary, IMangelKontrollbereichDictionary)
  public
    function Mangel: IMangelDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
  end;
  
  TMangelOertlichkeitDictionary = class(TAureliusEntityDictionary, IMangelOertlichkeitDictionary)
  public
    function Mangel: IMangelDictionary;
    function Oertlichkeit: IKontrollberichtOertlichkeitDictionary;
  end;
  
  TMangelStatusDictionary = class(TAureliusEntityDictionary, IMangelStatusDictionary)
  public
    function Status: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Maengel: IMangelDictionary;
  end;
  
  TMangeltypDictionary = class(TAureliusEntityDictionary, IMangeltypDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Bkbtyp: IBkbTypDictionary;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function Maengel: IMangelDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
  end;
  
  TMassnahmeDictionary = class(TAureliusEntityDictionary, IMassnahmeDictionary)
  public
    function Id: TLinqProjection;
    function Langtext: TLinqProjection;
    function Standardfrist: TLinqProjection;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function FragenBewertungen: IFrageBewertungDictionary;
    function Maengel: IMangelDictionary;
  end;
  
  TMassnahmenkatalogDictionary = class(TAureliusEntityDictionary, IMassnahmenkatalogDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Massnahmen: IMassnahmeDictionary;
    function Mangeltypen: IMangeltypDictionary;
  end;
  
  TModulDictionary = class(TAureliusEntityDictionary, IModulDictionary)
  public
    function Modul: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeUser: TLinqProjection;
    function BundeslaenderModule: IBundeslandModulDictionary;
    function Bkbtypen: IBkbTypDictionary;
  end;
  
  TModulInstanzDictionary = class(TAureliusEntityDictionary, IModulInstanzDictionary)
  public
    function Id: TLinqProjection;
    function InstanzName: TLinqProjection;
    function InstanzUrl: TLinqProjection;
    function Modul: IProgrammModulDictionary;
  end;
  
  TModuleBkbtypenDictionary = class(TAureliusEntityDictionary, IModuleBkbtypenDictionary)
  public
    function IdModul: TLinqProjection;
    function Bkbtyp: TLinqProjection;
  end;
  
  TNachrichtDictionary = class(TAureliusEntityDictionary, INachrichtDictionary)
  public
    function Id: TLinqProjection;
    function Status: TLinqProjection;
    function Typ: TLinqProjection;
    function AbsenderKz: TLinqProjection;
    function Priori: TLinqProjection;
    function Text: TLinqProjection;
    function Link: TLinqProjection;
    function GesendetAm: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function Absender: IUserDictionary;
    function Zustellungen: INachrichtenZustellungDictionary;
    function VNachrichtenUser: IVNachrichtForUserDictionary;
  end;
  
  TNachrichtenZustellungDictionary = class(TAureliusEntityDictionary, INachrichtenZustellungDictionary)
  public
    function Id: TLinqProjection;
    function GesehenTstamp: TLinqProjection;
    function GelesenTstamp: TLinqProjection;
    function Nachricht: INachrichtDictionary;
    function User: IUserDictionary;
  end;
  
  TPersonDictionary = class(TAureliusEntityDictionary, IPersonDictionary)
  public
    function Id: TLinqProjection;
    function Titel: TLinqProjection;
    function Vorname: TLinqProjection;
    function Nachname: TLinqProjection;
    function AdresseALT: TLinqProjection;
    function OrtALT: TLinqProjection;
    function PlzALT: TLinqProjection;
    function Landkz: TLinqProjection;
    function Telefon: TLinqProjection;
    function Email: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeDbuser: TLinqProjection;
    function Anrede: TLinqProjection;
    function Adresse: IAdresseDictionary;
    function KontrollberichteErfasser: IKontrollberichtDictionary;
    function KontrollberichteKontrollorgan: IKontrollberichtDictionary;
    function Kommunikationswege: IPersonenKommunikationswegeDictionary;
    function Users: IUserDictionary;
  end;
  
  TPersonenKommunikationswegeDictionary = class(TAureliusEntityDictionary, IPersonenKommunikationswegeDictionary)
  public
    function Kommunikationsweg: IKommunikationswegDictionary;
    function Person: IPersonDictionary;
  end;
  
  TProgrammModulDictionary = class(TAureliusEntityDictionary, IProgrammModulDictionary)
  public
    function Kurzbezeichnnung: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Funktionen: IFunktionDictionary;
  end;
  
  TRechtsgrundlageDictionary = class(TAureliusEntityDictionary, IRechtsgrundlageDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Kurzbezeichnung: TLinqProjection;
    function UnterschriftErforderlich: TLinqProjection;
    function Kontrollberichte: IKontrollberichtDictionary;
    function Bkbtypen: IBkbtypenRechtsgrundlageDictionary;
  end;
  
  TRegistrierungDictionary = class(TAureliusEntityDictionary, IRegistrierungDictionary)
  public
    function Regnr: TLinqProjection;
    function FreierBetrieb: TLinqProjection;
    function Zulassungen: IZulassungDictionary;
    function Betriebe: IBetriebDictionary;
  end;
  
  TReportDictionary = class(TAureliusEntityDictionary, IReportDictionary)
  public
    function Guid: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Beschreibung: TLinqProjection;
    function Reporttyp: TLinqProjection;
    function Reportvorlage: TLinqProjection;
    function ReportKontrolltypen: IKontrolltypReportDictionary;
  end;
  
  TReportTypDictionary = class(TAureliusEntityDictionary, IReportTypDictionary)
  public
    function Typ: TLinqProjection;
    function Bezeichnung: TLinqProjection;
  end;
  
  TRevisionsSchemaDictionary = class(TAureliusEntityDictionary, IRevisionsSchemaDictionary)
  public
    function RevSchema: TLinqProjection;
    function Geplant: TLinqProjection;
    function Beschreibung: TLinqProjection;
  end;
  
  TRevisionsplanDictionary = class(TAureliusEntityDictionary, IRevisionsplanDictionary)
  public
    function Id: TLinqProjection;
    function Jahr: TLinqProjection;
    function RisikoKategorie: TLinqProjection;
    function JMindestKontrollFrequenz: TLinqProjection;
    function AnzBetriebeImLand: TLinqProjection;
    function AnzGesamtKontrollen: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function Bldcode: IBundeslandDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
  end;
  
  TRevisionsstammDictionary = class(TAureliusEntityDictionary, IRevisionsstammDictionary)
  public
    function Id: TLinqProjection;
    function Bldcode: TLinqProjection;
    function Sektion: TLinqProjection;
    function BetriebsgruppeLm: TLinqProjection;
    function BetriebsgruppeDetail: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function Betriebsgruppenkz: TLinqProjection;
    function KontrollTyp: IKontrolltypDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
  end;
  
  TRolleDictionary = class(TAureliusEntityDictionary, IRolleDictionary)
  public
    function Id: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Defaultrolle: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function GueltigAb: TLinqProjection;
    function Parameter: TLinqProjection;
    function FunktionenRollen: IFunktionRolleDictionary;
    function UserRollen: IUserrolleDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
  end;
  
  TRollenBkbtypenDictionary = class(TAureliusEntityDictionary, IRollenBkbtypenDictionary)
  public
    function Bkbtyp: IBkbTypDictionary;
    function Rolle: IRolleDictionary;
  end;
  
  TSysDictionary = class(TAureliusEntityDictionary, ISysDictionary)
  public
    function Systemkz: TLinqProjection;
    function Systemname: TLinqProjection;
    function VersionDb: TLinqProjection;
    function Bkbnummern: IBkbnummerDictionary;
  end;
  
  TTodoDictionary = class(TAureliusEntityDictionary, ITodoDictionary)
  public
    function Id: TLinqProjection;
    function Titel: TLinqProjection;
    function Faellig: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
  end;
  
  TUnterschriftDictionary = class(TAureliusEntityDictionary, IUnterschriftDictionary)
  public
    function Guid: TLinqProjection;
    function Bild: TLinqProjection;
    function Datum: TLinqProjection;
    function Name: TLinqProjection;
    function IdPerson: TLinqProjection;
    function KontrollberichteAnwesender: IKontrollberichtDictionary;
    function KontrollberichteKontrollorgan: IKontrollberichtDictionary;
  end;
  
  TUserDictionary = class(TAureliusEntityDictionary, IUserDictionary)
  public
    function Id: TLinqProjection;
    function Username: TLinqProjection;
    function Userguid: TLinqProjection;
    function Usertype: TLinqProjection;
    function Gesperrt: TLinqProjection;
    function Userpwc: TLinqProjection;
    function Aktiv: TLinqProjection;
    function TstampInsert: TLinqProjection;
    function InsDbuser: TLinqProjection;
    function Lastchange: TLinqProjection;
    function ChangeDbuser: TLinqProjection;
    function Email: TLinqProjection;
    function LastLogin: TLinqProjection;
    function Bundesland: IBundeslandDictionary;
    function Person: IPersonDictionary;
    function StellvertreterFuerGruppen: IGruppeDictionary;
    function Nachrichten: INachrichtDictionary;
    function NachrichtenZustellungen: INachrichtenZustellungDictionary;
    function Usergruppen: IUsergruppeDictionary;
    function Userrollen: IUserrolleDictionary;
    function Todos: ITodoDictionary;
    function HauptverantwortlicherFuerGruppen: IGruppeDictionary;
    function KontrollberichtBilder: IKontrollberichtBildDictionary;
  end;
  
  TUsergruppeDictionary = class(TAureliusEntityDictionary, IUsergruppeDictionary)
  public
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
  end;
  
  TUserrolleDictionary = class(TAureliusEntityDictionary, IUserrolleDictionary)
  public
    function Begdat: TLinqProjection;
    function Enddat: TLinqProjection;
    function Rolle: IRolleDictionary;
    function User: IUserDictionary;
    function ModulInstanz: IModulInstanzDictionary;
  end;
  
  TVNachrichtForUserDictionary = class(TAureliusEntityDictionary, IVNachrichtForUserDictionary)
  public
    function GesehenTstamp: TLinqProjection;
    function GelesenTstamp: TLinqProjection;
    function Nachricht: INachrichtDictionary;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
    function NachrichtenZustellung: INachrichtenZustellungDictionary;
  end;
  
  TVUserGroupMembershipDictionary = class(TAureliusEntityDictionary, IVUserGroupMembershipDictionary)
  public
    function MembershipLevel: TLinqProjection;
    function Gruppe: IGruppeDictionary;
    function User: IUserDictionary;
  end;
  
  TVbetriebbezirksgruppeDictionary = class(TAureliusEntityDictionary, IVbetriebbezirksgruppeDictionary)
  public
    function Gemeindekennziffer: TLinqProjection;
    function Betrieb: IBetriebDictionary;
    function Gruppe: IGruppeDictionary;
  end;
  
  TVbetriebsinfoDictionary = class(TAureliusEntityDictionary, IVbetriebsinfoDictionary)
  public
    function BetriebsId: TLinqProjection;
    function Regnr: TLinqProjection;
    function BetriebsdatenId: TLinqProjection;
    function Name: TLinqProjection;
    function IdCckAuftrag: TLinqProjection;
    function Betriebstyp: TLinqProjection;
    function Betriebsart: TLinqProjection;
    function RegnrHauptbetrieb: TLinqProjection;
    function IdHauptbetrieb: TLinqProjection;
    function Ort: TLinqProjection;
    function Taetigkeiten: TLinqProjection;
    function Zulassungsnummern: TLinqProjection;
  end;
  
  TVgroupuserDictionary = class(TAureliusEntityDictionary, IVgroupuserDictionary)
  public
    function Membershiplevel: TLinqProjection;
    function User: IUserDictionary;
    function GroupUser: IUserDictionary;
    function Group: IGruppeDictionary;
    function GroupPerson: IPersonDictionary;
    function Bundesland: IBundeslandDictionary;
  end;
  
  TZulassungDictionary = class(TAureliusEntityDictionary, IZulassungDictionary)
  public
    function Id: TLinqProjection;
    function Zulnr: TLinqProjection;
    function Beginndatum: TLinqProjection;
    function Enddatum: TLinqProjection;
    function Aktiv: TLinqProjection;
    function Sichtbar: TLinqProjection;
    function Registrierung: IRegistrierungDictionary;
  end;
  
  TZusatztextDictionary = class(TAureliusEntityDictionary, IZusatztextDictionary)
  public
    function Id: TLinqProjection;
    function Tabelle: TLinqProjection;
    function Bezeichnung: TLinqProjection;
    function Langtext: TLinqProjection;
  end;
  
  TvUserKontrollenDictionary = class(TAureliusEntityDictionary, IvUserKontrollenDictionary)
  public
    function IdUser: TLinqProjection;
    function Status: TLinqProjection;
    function Datum: TLinqProjection;
    function Endezeit: TLinqProjection;
    function Faellig: TLinqProjection;
    function IdErfasser: TLinqProjection;
    function IdKontrollorgan: TLinqProjection;
    function IdGruppeQuelle: TLinqProjection;
    function EffectiveDate: TLinqProjection;
    function GruppeLevel: TLinqProjection;
    function StatusPrio: TLinqProjection;
    function Sort: TLinqProjection;
    function Kontrolle: IKontrollberichtDictionary;
  end;
  
  IDictionary = interface(IAureliusDictionary)
    function APTyp: IAPTypDictionary;
    function Adresse: IAdresseDictionary;
    function Ansprechpartner: IAnsprechpartnerDictionary;
    function Anwesender: IAnwesenderDictionary;
    function Betrieb: IBetriebDictionary;
    function BetriebRevstamm: IBetriebRevstammDictionary;
    function BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
    function BewerteteFrage: IBewerteteFrageDictionary;
    function Bewertung: IBewertungDictionary;
    function BewertungsIcon: IBewertungsIconDictionary;
    function Bewertungstyp: IBewertungstypDictionary;
    function BkbTyp: IBkbTypDictionary;
    function Bkbnummer: IBkbnummerDictionary;
    function BkbtypenRechtsgrundlage: IBkbtypenRechtsgrundlageDictionary;
    function Bundesland: IBundeslandDictionary;
    function BundeslandChecklistenKontrolltyp: IBundeslandChecklistenKontrolltypDictionary;
    function BundeslandModul: IBundeslandModulDictionary;
    function CckAuftrag: ICckAuftragDictionary;
    function CckAuftragsart: ICckAuftragsartDictionary;
    function CckAuftragsbewertung: ICckAuftragsbewertungDictionary;
    function CckAuswahl: ICckAuswahlDictionary;
    function CckBetrieb: ICckBetriebDictionary;
    function CckModul: ICckModulDictionary;
    function CckModulAnforderung: ICckModulAnforderungDictionary;
    function CckModulKontrolltyp: ICckModulKontrolltypDictionary;
    function CckStatus: ICckStatusDictionary;
    function CckStatusMeldung: ICckStatusMeldungDictionary;
    function CckTierdaten: ICckTierdatenDictionary;
    function CckVokSanktion: ICckVokSanktionDictionary;
    function Checkliste: IChecklisteDictionary;
    function ChecklistenKontrolltyp: IChecklistenKontrolltypDictionary;
    function Dokument: IDokumentDictionary;
    function EmailArten: IEmailArtenDictionary;
    function EmailHistory: IEmailHistoryDictionary;
    function EmailHistoryAttachment: IEmailHistoryAttachmentDictionary;
    function EmailTexte: IEmailTexteDictionary;
    function Formatierung: IFormatierungDictionary;
    function Frage: IFrageDictionary;
    function FrageBewertung: IFrageBewertungDictionary;
    function FrageKontrollbereich: IFrageKontrollbereichDictionary;
    function Fragengruppe: IFragengruppeDictionary;
    function Funktion: IFunktionDictionary;
    function FunktionRolle: IFunktionRolleDictionary;
    function Gemeinde: IGemeindeDictionary;
    function Gruppe: IGruppeDictionary;
    function KbProbe: IKbProbeDictionary;
    function Kommunikationsart: IKommunikationsartDictionary;
    function Kommunikationsweg: IKommunikationswegDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function KontrollberichtBild: IKontrollberichtBildDictionary;
    function KontrollberichtOertlichkeit: IKontrollberichtOertlichkeitDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function KontrolltypReport: IKontrolltypReportDictionary;
    function Land: ILandDictionary;
    function Mangel: IMangelDictionary;
    function MangelKontrollbereich: IMangelKontrollbereichDictionary;
    function MangelOertlichkeit: IMangelOertlichkeitDictionary;
    function MangelStatus: IMangelStatusDictionary;
    function Mangeltyp: IMangeltypDictionary;
    function Massnahme: IMassnahmeDictionary;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function Modul: IModulDictionary;
    function ModulInstanz: IModulInstanzDictionary;
    function ModuleBkbtypen: IModuleBkbtypenDictionary;
    function Nachricht: INachrichtDictionary;
    function NachrichtenZustellung: INachrichtenZustellungDictionary;
    function Person: IPersonDictionary;
    function PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
    function ProgrammModul: IProgrammModulDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function Registrierung: IRegistrierungDictionary;
    function Report: IReportDictionary;
    function ReportTyp: IReportTypDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
    function Revisionsplan: IRevisionsplanDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
    function Rolle: IRolleDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
    function Sys: ISysDictionary;
    function Todo: ITodoDictionary;
    function Unterschrift: IUnterschriftDictionary;
    function User: IUserDictionary;
    function Usergruppe: IUsergruppeDictionary;
    function Userrolle: IUserrolleDictionary;
    function VNachrichtForUser: IVNachrichtForUserDictionary;
    function VUserGroupMembership: IVUserGroupMembershipDictionary;
    function Vbetriebbezirksgruppe: IVbetriebbezirksgruppeDictionary;
    function Vbetriebsinfo: IVbetriebsinfoDictionary;
    function Vgroupuser: IVgroupuserDictionary;
    function Zulassung: IZulassungDictionary;
    function Zusatztext: IZusatztextDictionary;
    function vUserKontrollen: IvUserKontrollenDictionary;
  end;
  
  TDictionary = class(TAureliusDictionary, IDictionary)
  public
    function APTyp: IAPTypDictionary;
    function Adresse: IAdresseDictionary;
    function Ansprechpartner: IAnsprechpartnerDictionary;
    function Anwesender: IAnwesenderDictionary;
    function Betrieb: IBetriebDictionary;
    function BetriebRevstamm: IBetriebRevstammDictionary;
    function BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
    function BewerteteFrage: IBewerteteFrageDictionary;
    function Bewertung: IBewertungDictionary;
    function BewertungsIcon: IBewertungsIconDictionary;
    function Bewertungstyp: IBewertungstypDictionary;
    function BkbTyp: IBkbTypDictionary;
    function Bkbnummer: IBkbnummerDictionary;
    function BkbtypenRechtsgrundlage: IBkbtypenRechtsgrundlageDictionary;
    function Bundesland: IBundeslandDictionary;
    function BundeslandChecklistenKontrolltyp: IBundeslandChecklistenKontrolltypDictionary;
    function BundeslandModul: IBundeslandModulDictionary;
    function CckAuftrag: ICckAuftragDictionary;
    function CckAuftragsart: ICckAuftragsartDictionary;
    function CckAuftragsbewertung: ICckAuftragsbewertungDictionary;
    function CckAuswahl: ICckAuswahlDictionary;
    function CckBetrieb: ICckBetriebDictionary;
    function CckModul: ICckModulDictionary;
    function CckModulAnforderung: ICckModulAnforderungDictionary;
    function CckModulKontrolltyp: ICckModulKontrolltypDictionary;
    function CckStatus: ICckStatusDictionary;
    function CckStatusMeldung: ICckStatusMeldungDictionary;
    function CckTierdaten: ICckTierdatenDictionary;
    function CckVokSanktion: ICckVokSanktionDictionary;
    function Checkliste: IChecklisteDictionary;
    function ChecklistenKontrolltyp: IChecklistenKontrolltypDictionary;
    function Dokument: IDokumentDictionary;
    function EmailArten: IEmailArtenDictionary;
    function EmailHistory: IEmailHistoryDictionary;
    function EmailHistoryAttachment: IEmailHistoryAttachmentDictionary;
    function EmailTexte: IEmailTexteDictionary;
    function Formatierung: IFormatierungDictionary;
    function Frage: IFrageDictionary;
    function FrageBewertung: IFrageBewertungDictionary;
    function FrageKontrollbereich: IFrageKontrollbereichDictionary;
    function Fragengruppe: IFragengruppeDictionary;
    function Funktion: IFunktionDictionary;
    function FunktionRolle: IFunktionRolleDictionary;
    function Gemeinde: IGemeindeDictionary;
    function Gruppe: IGruppeDictionary;
    function KbProbe: IKbProbeDictionary;
    function Kommunikationsart: IKommunikationsartDictionary;
    function Kommunikationsweg: IKommunikationswegDictionary;
    function Kontrollbereich: IKontrollbereichDictionary;
    function Kontrollbericht: IKontrollberichtDictionary;
    function KontrollberichtBild: IKontrollberichtBildDictionary;
    function KontrollberichtOertlichkeit: IKontrollberichtOertlichkeitDictionary;
    function Kontrolltyp: IKontrolltypDictionary;
    function KontrolltypReport: IKontrolltypReportDictionary;
    function Land: ILandDictionary;
    function Mangel: IMangelDictionary;
    function MangelKontrollbereich: IMangelKontrollbereichDictionary;
    function MangelOertlichkeit: IMangelOertlichkeitDictionary;
    function MangelStatus: IMangelStatusDictionary;
    function Mangeltyp: IMangeltypDictionary;
    function Massnahme: IMassnahmeDictionary;
    function Massnahmenkatalog: IMassnahmenkatalogDictionary;
    function Modul: IModulDictionary;
    function ModulInstanz: IModulInstanzDictionary;
    function ModuleBkbtypen: IModuleBkbtypenDictionary;
    function Nachricht: INachrichtDictionary;
    function NachrichtenZustellung: INachrichtenZustellungDictionary;
    function Person: IPersonDictionary;
    function PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
    function ProgrammModul: IProgrammModulDictionary;
    function Rechtsgrundlage: IRechtsgrundlageDictionary;
    function Registrierung: IRegistrierungDictionary;
    function Report: IReportDictionary;
    function ReportTyp: IReportTypDictionary;
    function RevisionsSchema: IRevisionsSchemaDictionary;
    function Revisionsplan: IRevisionsplanDictionary;
    function Revisionsstamm: IRevisionsstammDictionary;
    function Rolle: IRolleDictionary;
    function RollenBkbtypen: IRollenBkbtypenDictionary;
    function Sys: ISysDictionary;
    function Todo: ITodoDictionary;
    function Unterschrift: IUnterschriftDictionary;
    function User: IUserDictionary;
    function Usergruppe: IUsergruppeDictionary;
    function Userrolle: IUserrolleDictionary;
    function VNachrichtForUser: IVNachrichtForUserDictionary;
    function VUserGroupMembership: IVUserGroupMembershipDictionary;
    function Vbetriebbezirksgruppe: IVbetriebbezirksgruppeDictionary;
    function Vbetriebsinfo: IVbetriebsinfoDictionary;
    function Vgroupuser: IVgroupuserDictionary;
    function Zulassung: IZulassungDictionary;
    function Zusatztext: IZusatztextDictionary;
    function vUserKontrollen: IvUserKontrollenDictionary;
  end;
  
function M: IDictionary;

implementation

var
  __M: IDictionary;

function M: IDictionary;
begin
  if __M = nil then __M := TDictionary.Create;
  result := __M;
end;

{ TAPTypDictionary }

function TAPTypDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TAPTypDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TAPTypDictionary.Hauptansprechpartner: TLinqProjection;
begin
  Result := Prop('Hauptansprechpartner');
end;

{ TAdresseDictionary }

function TAdresseDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TAdresseDictionary.Ort: TLinqProjection;
begin
  Result := Prop('Ort');
end;

function TAdresseDictionary.Plz: TLinqProjection;
begin
  Result := Prop('Plz');
end;

function TAdresseDictionary.Strasse: TLinqProjection;
begin
  Result := Prop('Strasse');
end;

function TAdresseDictionary.Adresszusatz: TLinqProjection;
begin
  Result := Prop('Adresszusatz');
end;

function TAdresseDictionary.Xkoord31287: TLinqProjection;
begin
  Result := Prop('Xkoord31287');
end;

function TAdresseDictionary.Ykoord31287: TLinqProjection;
begin
  Result := Prop('Ykoord31287');
end;

function TAdresseDictionary.Xkoord4326: TLinqProjection;
begin
  Result := Prop('Xkoord4326');
end;

function TAdresseDictionary.Ykoord4326: TLinqProjection;
begin
  Result := Prop('Ykoord4326');
end;

function TAdresseDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TAdresseDictionary.Gemeinde: IGemeindeDictionary;
begin
  Result := TGemeindeDictionary.Create(PropName('Gemeinde'));
end;

{ TAnsprechpartnerDictionary }

function TAnsprechpartnerDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TAnsprechpartnerDictionary.Regnr: TLinqProjection;
begin
  Result := Prop('Regnr');
end;

function TAnsprechpartnerDictionary.KommunikationsBerechtigt: TLinqProjection;
begin
  Result := Prop('KommunikationsBerechtigt');
end;

function TAnsprechpartnerDictionary.Aptyp: IAPTypDictionary;
begin
  Result := TAPTypDictionary.Create(PropName('Aptyp'));
end;

function TAnsprechpartnerDictionary.Person: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Person'));
end;

{ TAnwesenderDictionary }

function TAnwesenderDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TAnwesenderDictionary.Name: TLinqProjection;
begin
  Result := Prop('Name');
end;

function TAnwesenderDictionary.Email: TLinqProjection;
begin
  Result := Prop('Email');
end;

function TAnwesenderDictionary.Kommunikationsberechtigt: TLinqProjection;
begin
  Result := Prop('Kommunikationsberechtigt');
end;

function TAnwesenderDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TAnwesenderDictionary.APTyp: IAPTypDictionary;
begin
  Result := TAPTypDictionary.Create(PropName('APTyp'));
end;

function TAnwesenderDictionary.Person: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Person'));
end;

{ TBetriebDictionary }

function TBetriebDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TBetriebDictionary.Name: TLinqProjection;
begin
  Result := Prop('Name');
end;

function TBetriebDictionary.Aufsichtsorgan: TLinqProjection;
begin
  Result := Prop('Aufsichtsorgan');
end;

function TBetriebDictionary.Telefon: TLinqProjection;
begin
  Result := Prop('Telefon');
end;

function TBetriebDictionary.Email: TLinqProjection;
begin
  Result := Prop('Email');
end;

function TBetriebDictionary.Vergebuehrung: TLinqProjection;
begin
  Result := Prop('Vergebuehrung');
end;

function TBetriebDictionary.Vulgo: TLinqProjection;
begin
  Result := Prop('Vulgo');
end;

function TBetriebDictionary.Anmerkung: TLinqProjection;
begin
  Result := Prop('Anmerkung');
end;

function TBetriebDictionary.Bvbkz: TLinqProjection;
begin
  Result := Prop('Bvbkz');
end;

function TBetriebDictionary.Bbknr: TLinqProjection;
begin
  Result := Prop('Bbknr');
end;

function TBetriebDictionary.Adresse: IAdresseDictionary;
begin
  Result := TAdresseDictionary.Create(PropName('Adresse'));
end;

function TBetriebDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TBetriebDictionary.Registrierung: IRegistrierungDictionary;
begin
  Result := TRegistrierungDictionary.Create(PropName('Registrierung'));
end;

function TBetriebDictionary.RevisionsSchema: IRevisionsSchemaDictionary;
begin
  Result := TRevisionsSchemaDictionary.Create(PropName('RevisionsSchema'));
end;

function TBetriebDictionary.RevisionsGruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('RevisionsGruppe'));
end;

function TBetriebDictionary.Kontrollberichte: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollberichte'));
end;

function TBetriebDictionary.Kommunikationswege: IBetriebeKommunikationswegeDictionary;
begin
  Result := TBetriebeKommunikationswegeDictionary.Create(PropName('Kommunikationswege'));
end;

function TBetriebDictionary.RevStaemme: IBetriebRevstammDictionary;
begin
  Result := TBetriebRevstammDictionary.Create(PropName('RevStaemme'));
end;

function TBetriebDictionary.Bezirksgruppen: IVbetriebbezirksgruppeDictionary;
begin
  Result := TVbetriebbezirksgruppeDictionary.Create(PropName('Bezirksgruppen'));
end;

{ TBetriebRevstammDictionary }

function TBetriebRevstammDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TBetriebRevstammDictionary.Begdate: TLinqProjection;
begin
  Result := Prop('Begdate');
end;

function TBetriebRevstammDictionary.Enddate: TLinqProjection;
begin
  Result := Prop('Enddate');
end;

function TBetriebRevstammDictionary.Betrieb: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betrieb'));
end;

function TBetriebRevstammDictionary.Revisionsstamm: IRevisionsstammDictionary;
begin
  Result := TRevisionsstammDictionary.Create(PropName('Revisionsstamm'));
end;

function TBetriebRevstammDictionary.RevisionsSchema: IRevisionsSchemaDictionary;
begin
  Result := TRevisionsSchemaDictionary.Create(PropName('RevisionsSchema'));
end;

{ TBetriebeKommunikationswegeDictionary }

function TBetriebeKommunikationswegeDictionary.Betrieb: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betrieb'));
end;

function TBetriebeKommunikationswegeDictionary.Kommunikationsweg: IKommunikationswegDictionary;
begin
  Result := TKommunikationswegDictionary.Create(PropName('Kommunikationsweg'));
end;

{ TBewerteteFrageDictionary }

function TBewerteteFrageDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TBewerteteFrageDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TBewerteteFrageDictionary.Zusatztext: TLinqProjection;
begin
  Result := Prop('Zusatztext');
end;

function TBewerteteFrageDictionary.Wert: TLinqProjection;
begin
  Result := Prop('Wert');
end;

function TBewerteteFrageDictionary.GpsLon: TLinqProjection;
begin
  Result := Prop('GpsLon');
end;

function TBewerteteFrageDictionary.GpsLat: TLinqProjection;
begin
  Result := Prop('GpsLat');
end;

function TBewerteteFrageDictionary.Bewertung: IBewertungDictionary;
begin
  Result := TBewertungDictionary.Create(PropName('Bewertung'));
end;

function TBewerteteFrageDictionary.Frage: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('Frage'));
end;

function TBewerteteFrageDictionary.Bericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Bericht'));
end;

function TBewerteteFrageDictionary.Mangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Mangel'));
end;

function TBewerteteFrageDictionary.Bilder: IKontrollberichtBildDictionary;
begin
  Result := TKontrollberichtBildDictionary.Create(PropName('Bilder'));
end;

{ TBewertungDictionary }

function TBewertungDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TBewertungDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TBewertungDictionary.Icon: IBewertungsIconDictionary;
begin
  Result := TBewertungsIconDictionary.Create(PropName('Icon'));
end;

function TBewertungDictionary.Typ: IBewertungstypDictionary;
begin
  Result := TBewertungstypDictionary.Create(PropName('Typ'));
end;

function TBewertungDictionary.FragenBewertungen: IFrageBewertungDictionary;
begin
  Result := TFrageBewertungDictionary.Create(PropName('FragenBewertungen'));
end;

function TBewertungDictionary.BewerteteFragen: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create(PropName('BewerteteFragen'));
end;

{ TBewertungsIconDictionary }

function TBewertungsIconDictionary.Icon: TLinqProjection;
begin
  Result := Prop('Icon');
end;

function TBewertungsIconDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

function TBewertungsIconDictionary.Bewertungen: IBewertungDictionary;
begin
  Result := TBewertungDictionary.Create(PropName('Bewertungen'));
end;

{ TBewertungstypDictionary }

function TBewertungstypDictionary.Typ: TLinqProjection;
begin
  Result := Prop('Typ');
end;

function TBewertungstypDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

function TBewertungstypDictionary.Bewertungen: IBewertungDictionary;
begin
  Result := TBewertungDictionary.Create(PropName('Bewertungen'));
end;

{ TBkbTypDictionary }

function TBkbTypDictionary.Typ: TLinqProjection;
begin
  Result := Prop('Typ');
end;

function TBkbTypDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TBkbTypDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TBkbTypDictionary.Aktiv: TLinqProjection;
begin
  Result := Prop('Aktiv');
end;

function TBkbTypDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TBkbTypDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TBkbTypDictionary.Lastchange: TLinqProjection;
begin
  Result := Prop('Lastchange');
end;

function TBkbTypDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TBkbTypDictionary.SecclassMin: TLinqProjection;
begin
  Result := Prop('SecclassMin');
end;

function TBkbTypDictionary.Probe: TLinqProjection;
begin
  Result := Prop('Probe');
end;

function TBkbTypDictionary.Modul: IModulDictionary;
begin
  Result := TModulDictionary.Create(PropName('Modul'));
end;

function TBkbTypDictionary.Kontrolltypen: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('Kontrolltypen'));
end;

function TBkbTypDictionary.BkbtypenRechtsgrundlagen: IBkbtypenRechtsgrundlageDictionary;
begin
  Result := TBkbtypenRechtsgrundlageDictionary.Create(PropName('BkbtypenRechtsgrundlagen'));
end;

function TBkbTypDictionary.RollenBkbtypen: IRollenBkbtypenDictionary;
begin
  Result := TRollenBkbtypenDictionary.Create(PropName('RollenBkbtypen'));
end;

function TBkbTypDictionary.Bkbnummern: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create(PropName('Bkbnummern'));
end;

function TBkbTypDictionary.Mangeltypen: IMangeltypDictionary;
begin
  Result := TMangeltypDictionary.Create(PropName('Mangeltypen'));
end;

{ TBkbnummerDictionary }

function TBkbnummerDictionary.Nummer: TLinqProjection;
begin
  Result := Prop('Nummer');
end;

function TBkbnummerDictionary.Jahr: TLinqProjection;
begin
  Result := Prop('Jahr');
end;

function TBkbnummerDictionary.LfdNr: TLinqProjection;
begin
  Result := Prop('LfdNr');
end;

function TBkbnummerDictionary.LfdNrHex: TLinqProjection;
begin
  Result := Prop('LfdNrHex');
end;

function TBkbnummerDictionary.Systemkz: ISysDictionary;
begin
  Result := TSysDictionary.Create(PropName('Systemkz'));
end;

function TBkbnummerDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TBkbnummerDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

{ TBkbtypenRechtsgrundlageDictionary }

function TBkbtypenRechtsgrundlageDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TBkbtypenRechtsgrundlageDictionary.Rechtsgrundlage: IRechtsgrundlageDictionary;
begin
  Result := TRechtsgrundlageDictionary.Create(PropName('Rechtsgrundlage'));
end;

{ TBundeslandDictionary }

function TBundeslandDictionary.Bldcode: TLinqProjection;
begin
  Result := Prop('Bldcode');
end;

function TBundeslandDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TBundeslandDictionary.Kurztext: TLinqProjection;
begin
  Result := Prop('Kurztext');
end;

function TBundeslandDictionary.Region: TLinqProjection;
begin
  Result := Prop('Region');
end;

function TBundeslandDictionary.Bldlogo: TLinqProjection;
begin
  Result := Prop('Bldlogo');
end;

function TBundeslandDictionary.Okz: TLinqProjection;
begin
  Result := Prop('Okz');
end;

function TBundeslandDictionary.Bkbkz: TLinqProjection;
begin
  Result := Prop('Bkbkz');
end;

function TBundeslandDictionary.Land: ILandDictionary;
begin
  Result := TLandDictionary.Create(PropName('Land'));
end;

function TBundeslandDictionary.Betriebe: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betriebe'));
end;

function TBundeslandDictionary.BundeslaenderModule: IBundeslandModulDictionary;
begin
  Result := TBundeslandModulDictionary.Create(PropName('BundeslaenderModule'));
end;

function TBundeslandDictionary.Gruppen: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppen'));
end;

function TBundeslandDictionary.Users: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('Users'));
end;

function TBundeslandDictionary.Adressen: IAdresseDictionary;
begin
  Result := TAdresseDictionary.Create(PropName('Adressen'));
end;

function TBundeslandDictionary.Gemeinden: IGemeindeDictionary;
begin
  Result := TGemeindeDictionary.Create(PropName('Gemeinden'));
end;

function TBundeslandDictionary.BkbNummern: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create(PropName('BkbNummern'));
end;

function TBundeslandDictionary.EmailTexte: IEmailTexteDictionary;
begin
  Result := TEmailTexteDictionary.Create(PropName('EmailTexte'));
end;

function TBundeslandDictionary.BesitztChecklisten: IChecklisteDictionary;
begin
  Result := TChecklisteDictionary.Create(PropName('BesitztChecklisten'));
end;

function TBundeslandDictionary.SichtbareChecklisten: IBundeslandChecklistenKontrolltypDictionary;
begin
  Result := TBundeslandChecklistenKontrolltypDictionary.Create(PropName('SichtbareChecklisten'));
end;

{ TBundeslandChecklistenKontrolltypDictionary }

function TBundeslandChecklistenKontrolltypDictionary.GueltigAb: TLinqProjection;
begin
  Result := Prop('GueltigAb');
end;

function TBundeslandChecklistenKontrolltypDictionary.GueltigBis: TLinqProjection;
begin
  Result := Prop('GueltigBis');
end;

function TBundeslandChecklistenKontrolltypDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TBundeslandChecklistenKontrolltypDictionary.ChecklisteKontrolltyp: IChecklistenKontrolltypDictionary;
begin
  Result := TChecklistenKontrolltypDictionary.Create(PropName('ChecklisteKontrolltyp'));
end;

{ TBundeslandModulDictionary }

function TBundeslandModulDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TBundeslandModulDictionary.Modul: IModulDictionary;
begin
  Result := TModulDictionary.Create(PropName('Modul'));
end;

{ TCckAuftragDictionary }

function TCckAuftragDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckAuftragDictionary.Auftragsjahr: TLinqProjection;
begin
  Result := Prop('Auftragsjahr');
end;

function TCckAuftragDictionary.LfbisHauptbetrieb: TLinqProjection;
begin
  Result := Prop('LfbisHauptbetrieb');
end;

function TCckAuftragDictionary.Auftragsid: TLinqProjection;
begin
  Result := Prop('Auftragsid');
end;

function TCckAuftragDictionary.Bkb: TLinqProjection;
begin
  Result := Prop('Bkb');
end;

function TCckAuftragDictionary.Bbknr: TLinqProjection;
begin
  Result := Prop('Bbknr');
end;

function TCckAuftragDictionary.Bldcode: TLinqProjection;
begin
  Result := Prop('Bldcode');
end;

function TCckAuftragDictionary.Vorname: TLinqProjection;
begin
  Result := Prop('Vorname');
end;

function TCckAuftragDictionary.Nachname: TLinqProjection;
begin
  Result := Prop('Nachname');
end;

function TCckAuftragDictionary.PlzBew: TLinqProjection;
begin
  Result := Prop('PlzBew');
end;

function TCckAuftragDictionary.OrtBew: TLinqProjection;
begin
  Result := Prop('OrtBew');
end;

function TCckAuftragDictionary.AdresseBew: TLinqProjection;
begin
  Result := Prop('AdresseBew');
end;

function TCckAuftragDictionary.GemeindekzBew: TLinqProjection;
begin
  Result := Prop('GemeindekzBew');
end;

function TCckAuftragDictionary.TelFestnetz: TLinqProjection;
begin
  Result := Prop('TelFestnetz');
end;

function TCckAuftragDictionary.Flag1: TLinqProjection;
begin
  Result := Prop('Flag1');
end;

function TCckAuftragDictionary.Flag2: TLinqProjection;
begin
  Result := Prop('Flag2');
end;

function TCckAuftragDictionary.Flag3: TLinqProjection;
begin
  Result := Prop('Flag3');
end;

function TCckAuftragDictionary.Info1: TLinqProjection;
begin
  Result := Prop('Info1');
end;

function TCckAuftragDictionary.Info2: TLinqProjection;
begin
  Result := Prop('Info2');
end;

function TCckAuftragDictionary.Info3: TLinqProjection;
begin
  Result := Prop('Info3');
end;

function TCckAuftragDictionary.AbgeschlossenAm: TLinqProjection;
begin
  Result := Prop('AbgeschlossenAm');
end;

function TCckAuftragDictionary.IdBearbeiter: TLinqProjection;
begin
  Result := Prop('IdBearbeiter');
end;

function TCckAuftragDictionary.IdBewerter: TLinqProjection;
begin
  Result := Prop('IdBewerter');
end;

function TCckAuftragDictionary.LetzteRestabfrage: TLinqProjection;
begin
  Result := Prop('LetzteRestabfrage');
end;

function TCckAuftragDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TCckAuftragDictionary.TstampUpdate: TLinqProjection;
begin
  Result := Prop('TstampUpdate');
end;

function TCckAuftragDictionary.Version: TLinqProjection;
begin
  Result := Prop('Version');
end;

function TCckAuftragDictionary.Auftragsart: ICckAuftragsartDictionary;
begin
  Result := TCckAuftragsartDictionary.Create(PropName('Auftragsart'));
end;

function TCckAuftragDictionary.Dokument: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create(PropName('Dokument'));
end;

function TCckAuftragDictionary.Betriebsdaten: ICckBetriebDictionary;
begin
  Result := TCckBetriebDictionary.Create(PropName('Betriebsdaten'));
end;

{ TCckAuftragsartDictionary }

function TCckAuftragsartDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TCckAuftragsartDictionary.Gesperrt: TLinqProjection;
begin
  Result := Prop('Gesperrt');
end;

function TCckAuftragsartDictionary.CckAuftraege: ICckAuftragDictionary;
begin
  Result := TCckAuftragDictionary.Create(PropName('CckAuftraege'));
end;

{ TCckAuftragsbewertungDictionary }

function TCckAuftragsbewertungDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckAuftragsbewertungDictionary.Bewertetam: TLinqProjection;
begin
  Result := Prop('Bewertetam');
end;

function TCckAuftragsbewertungDictionary.Kontrolliert: TLinqProjection;
begin
  Result := Prop('Kontrolliert');
end;

function TCckAuftragsbewertungDictionary.Ok: TLinqProjection;
begin
  Result := Prop('Ok');
end;

function TCckAuftragsbewertungDictionary.Auffaellig: TLinqProjection;
begin
  Result := Prop('Auffaellig');
end;

function TCckAuftragsbewertungDictionary.Gerverstossok: TLinqProjection;
begin
  Result := Prop('Gerverstossok');
end;

function TCckAuftragsbewertungDictionary.Vorsatz: TLinqProjection;
begin
  Result := Prop('Vorsatz');
end;

function TCckAuftragsbewertungDictionary.Ausmass: TLinqProjection;
begin
  Result := Prop('Ausmass');
end;

function TCckAuftragsbewertungDictionary.Schwere: TLinqProjection;
begin
  Result := Prop('Schwere');
end;

function TCckAuftragsbewertungDictionary.Dauer: TLinqProjection;
begin
  Result := Prop('Dauer');
end;

function TCckAuftragsbewertungDictionary.Bemerkung: TLinqProjection;
begin
  Result := Prop('Bemerkung');
end;

function TCckAuftragsbewertungDictionary.KzHv: TLinqProjection;
begin
  Result := Prop('KzHv');
end;

function TCckAuftragsbewertungDictionary.Auswahldaten: ICckAuswahlDictionary;
begin
  Result := TCckAuswahlDictionary.Create(PropName('Auswahldaten'));
end;

function TCckAuftragsbewertungDictionary.Anforderung: ICckModulAnforderungDictionary;
begin
  Result := TCckModulAnforderungDictionary.Create(PropName('Anforderung'));
end;

{ TCckAuswahlDictionary }

function TCckAuswahlDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckAuswahlDictionary.AuswahlId: TLinqProjection;
begin
  Result := Prop('AuswahlId');
end;

function TCckAuswahlDictionary.Auswahldatum: TLinqProjection;
begin
  Result := Prop('Auswahldatum');
end;

function TCckAuswahlDictionary.Auswahlgrund: TLinqProjection;
begin
  Result := Prop('Auswahlgrund');
end;

function TCckAuswahlDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TCckAuswahlDictionary.CckBetriebsdaten: ICckBetriebDictionary;
begin
  Result := TCckBetriebDictionary.Create(PropName('CckBetriebsdaten'));
end;

function TCckAuswahlDictionary.Modul: ICckModulDictionary;
begin
  Result := TCckModulDictionary.Create(PropName('Modul'));
end;

function TCckAuswahlDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TCckAuswahlDictionary.CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
begin
  Result := TCckAuftragsbewertungDictionary.Create(PropName('CckAuftragsbewertungen'));
end;

{ TCckBetriebDictionary }

function TCckBetriebDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckBetriebDictionary.Betriebstyp: TLinqProjection;
begin
  Result := Prop('Betriebstyp');
end;

function TCckBetriebDictionary.Betriebsart: TLinqProjection;
begin
  Result := Prop('Betriebsart');
end;

function TCckBetriebDictionary.Lfbis: TLinqProjection;
begin
  Result := Prop('Lfbis');
end;

function TCckBetriebDictionary.PlzBetr: TLinqProjection;
begin
  Result := Prop('PlzBetr');
end;

function TCckBetriebDictionary.OrtBetr: TLinqProjection;
begin
  Result := Prop('OrtBetr');
end;

function TCckBetriebDictionary.AdresseBetr: TLinqProjection;
begin
  Result := Prop('AdresseBetr');
end;

function TCckBetriebDictionary.GemeindekzBetr: TLinqProjection;
begin
  Result := Prop('GemeindekzBetr');
end;

function TCckBetriebDictionary.LnFlaeche: TLinqProjection;
begin
  Result := Prop('LnFlaeche');
end;

function TCckBetriebDictionary.Tgd: TLinqProjection;
begin
  Result := Prop('Tgd');
end;

function TCckBetriebDictionary.Tierhalter: TLinqProjection;
begin
  Result := Prop('Tierhalter');
end;

function TCckBetriebDictionary.IdBetrieb: TLinqProjection;
begin
  Result := Prop('IdBetrieb');
end;

function TCckBetriebDictionary.CckAuftrag: ICckAuftragDictionary;
begin
  Result := TCckAuftragDictionary.Create(PropName('CckAuftrag'));
end;

function TCckBetriebDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TCckBetriebDictionary.CckAuswahldaten: ICckAuswahlDictionary;
begin
  Result := TCckAuswahlDictionary.Create(PropName('CckAuswahldaten'));
end;

{ TCckModulDictionary }

function TCckModulDictionary.Modul: TLinqProjection;
begin
  Result := Prop('Modul');
end;

function TCckModulDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TCckModulDictionary.CckAuswahldaten: ICckAuswahlDictionary;
begin
  Result := TCckAuswahlDictionary.Create(PropName('CckAuswahldaten'));
end;

function TCckModulDictionary.CckModulAnforderungen: ICckModulAnforderungDictionary;
begin
  Result := TCckModulAnforderungDictionary.Create(PropName('CckModulAnforderungen'));
end;

function TCckModulDictionary.CckModulKontrolltypen: ICckModulKontrolltypDictionary;
begin
  Result := TCckModulKontrolltypDictionary.Create(PropName('CckModulKontrolltypen'));
end;

{ TCckModulAnforderungDictionary }

function TCckModulAnforderungDictionary.Anforderung: TLinqProjection;
begin
  Result := Prop('Anforderung');
end;

function TCckModulAnforderungDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TCckModulAnforderungDictionary.Modul: ICckModulDictionary;
begin
  Result := TCckModulDictionary.Create(PropName('Modul'));
end;

function TCckModulAnforderungDictionary.CckAuftragsbewertungen: ICckAuftragsbewertungDictionary;
begin
  Result := TCckAuftragsbewertungDictionary.Create(PropName('CckAuftragsbewertungen'));
end;

{ TCckModulKontrolltypDictionary }

function TCckModulKontrolltypDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckModulKontrolltypDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TCckModulKontrolltypDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TCckModulKontrolltypDictionary.CckModul: ICckModulDictionary;
begin
  Result := TCckModulDictionary.Create(PropName('CckModul'));
end;

function TCckModulKontrolltypDictionary.Bkbtyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('Bkbtyp'));
end;

function TCckModulKontrolltypDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

{ TCckStatusDictionary }

function TCckStatusDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TCckStatusDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

{ TCckStatusMeldungDictionary }

function TCckStatusMeldungDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckStatusMeldungDictionary.Meldedatum: TLinqProjection;
begin
  Result := Prop('Meldedatum');
end;

function TCckStatusMeldungDictionary.Kommentar: TLinqProjection;
begin
  Result := Prop('Kommentar');
end;

function TCckStatusMeldungDictionary.Kontakt: TLinqProjection;
begin
  Result := Prop('Kontakt');
end;

function TCckStatusMeldungDictionary.BewertungsVersion: TLinqProjection;
begin
  Result := Prop('BewertungsVersion');
end;

function TCckStatusMeldungDictionary.Bkb: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create(PropName('Bkb'));
end;

function TCckStatusMeldungDictionary.Status: ICckStatusDictionary;
begin
  Result := TCckStatusDictionary.Create(PropName('Status'));
end;

function TCckStatusMeldungDictionary.BkbAuftrag: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create(PropName('BkbAuftrag'));
end;

{ TCckTierdatenDictionary }

function TCckTierdatenDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckTierdatenDictionary.IdCckBetriebsdaten: TLinqProjection;
begin
  Result := Prop('IdCckBetriebsdaten');
end;

function TCckTierdatenDictionary.TierdatenId: TLinqProjection;
begin
  Result := Prop('TierdatenId');
end;

function TCckTierdatenDictionary.Tierkategorie: TLinqProjection;
begin
  Result := Prop('Tierkategorie');
end;

function TCckTierdatenDictionary.Anzahl: TLinqProjection;
begin
  Result := Prop('Anzahl');
end;

function TCckTierdatenDictionary.VisTierart: TLinqProjection;
begin
  Result := Prop('VisTierart');
end;

function TCckTierdatenDictionary.VisPrbkat: TLinqProjection;
begin
  Result := Prop('VisPrbkat');
end;

{ TCckVokSanktionDictionary }

function TCckVokSanktionDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TCckVokSanktionDictionary.IdCckBetriebsdaten: TLinqProjection;
begin
  Result := Prop('IdCckBetriebsdaten');
end;

function TCckVokSanktionDictionary.VokSanktionenId: TLinqProjection;
begin
  Result := Prop('VokSanktionenId');
end;

function TCckVokSanktionDictionary.VokSank: TLinqProjection;
begin
  Result := Prop('VokSank');
end;

function TCckVokSanktionDictionary.Jahr: TLinqProjection;
begin
  Result := Prop('Jahr');
end;

{ TChecklisteDictionary }

function TChecklisteDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TChecklisteDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TChecklisteDictionary.GueltigAb: TLinqProjection;
begin
  Result := Prop('GueltigAb');
end;

function TChecklisteDictionary.GueltigBis: TLinqProjection;
begin
  Result := Prop('GueltigBis');
end;

function TChecklisteDictionary.Version: TLinqProjection;
begin
  Result := Prop('Version');
end;

function TChecklisteDictionary.Versionstext: TLinqProjection;
begin
  Result := Prop('Versionstext');
end;

function TChecklisteDictionary.Ccrelevant: TLinqProjection;
begin
  Result := Prop('Ccrelevant');
end;

function TChecklisteDictionary.VisBkbtid: TLinqProjection;
begin
  Result := Prop('VisBkbtid');
end;

function TChecklisteDictionary.Privat: TLinqProjection;
begin
  Result := Prop('Privat');
end;

function TChecklisteDictionary.LastChange: TLinqProjection;
begin
  Result := Prop('LastChange');
end;

function TChecklisteDictionary.BesitzerBundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('BesitzerBundesland'));
end;

function TChecklisteDictionary.LastChangeUser: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('LastChangeUser'));
end;

function TChecklisteDictionary.Fragen: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('Fragen'));
end;

function TChecklisteDictionary.ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
begin
  Result := TChecklistenKontrolltypDictionary.Create(PropName('ChecklistenKontrolltypen'));
end;

{ TChecklistenKontrolltypDictionary }

function TChecklistenKontrolltypDictionary.Checkliste: IChecklisteDictionary;
begin
  Result := TChecklisteDictionary.Create(PropName('Checkliste'));
end;

function TChecklistenKontrolltypDictionary.Kontrolltyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('Kontrolltyp'));
end;

function TChecklistenKontrolltypDictionary.BundeslaenderChecklistenKontrolltypen: IBundeslandChecklistenKontrolltypDictionary;
begin
  Result := TBundeslandChecklistenKontrolltypDictionary.Create(PropName('BundeslaenderChecklistenKontrolltypen'));
end;

{ TDokumentDictionary }

function TDokumentDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TDokumentDictionary.Bldcode: TLinqProjection;
begin
  Result := Prop('Bldcode');
end;

function TDokumentDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TDokumentDictionary.Typ: TLinqProjection;
begin
  Result := Prop('Typ');
end;

function TDokumentDictionary.Dateiname: TLinqProjection;
begin
  Result := Prop('Dateiname');
end;

function TDokumentDictionary.ErstelltAm: TLinqProjection;
begin
  Result := Prop('ErstelltAm');
end;

function TDokumentDictionary.Dokument: TLinqProjection;
begin
  Result := Prop('Dokument');
end;

function TDokumentDictionary.CckAuftraege: ICckAuftragDictionary;
begin
  Result := TCckAuftragDictionary.Create(PropName('CckAuftraege'));
end;

function TDokumentDictionary.Kontrollberichte: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollberichte'));
end;

{ TEmailArtenDictionary }

function TEmailArtenDictionary.Art: TLinqProjection;
begin
  Result := Prop('Art');
end;

function TEmailArtenDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

{ TEmailHistoryDictionary }

function TEmailHistoryDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TEmailHistoryDictionary.DatumGesendet: TLinqProjection;
begin
  Result := Prop('DatumGesendet');
end;

function TEmailHistoryDictionary.Subject: TLinqProjection;
begin
  Result := Prop('Subject');
end;

function TEmailHistoryDictionary.Body: TLinqProjection;
begin
  Result := Prop('Body');
end;

function TEmailHistoryDictionary.EmailFrom: TLinqProjection;
begin
  Result := Prop('EmailFrom');
end;

function TEmailHistoryDictionary.EmailTo: TLinqProjection;
begin
  Result := Prop('EmailTo');
end;

function TEmailHistoryDictionary.EmailCc: TLinqProjection;
begin
  Result := Prop('EmailCc');
end;

function TEmailHistoryDictionary.EmailBc: TLinqProjection;
begin
  Result := Prop('EmailBc');
end;

function TEmailHistoryDictionary.Host: TLinqProjection;
begin
  Result := Prop('Host');
end;

function TEmailHistoryDictionary.Error: TLinqProjection;
begin
  Result := Prop('Error');
end;

function TEmailHistoryDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TEmailHistoryDictionary.Attachments: IEmailHistoryAttachmentDictionary;
begin
  Result := TEmailHistoryAttachmentDictionary.Create(PropName('Attachments'));
end;

{ TEmailHistoryAttachmentDictionary }

function TEmailHistoryAttachmentDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TEmailHistoryAttachmentDictionary.Filename: TLinqProjection;
begin
  Result := Prop('Filename');
end;

function TEmailHistoryAttachmentDictionary.Email: IEmailHistoryDictionary;
begin
  Result := TEmailHistoryDictionary.Create(PropName('Email'));
end;

{ TEmailTexteDictionary }

function TEmailTexteDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TEmailTexteDictionary.Text: TLinqProjection;
begin
  Result := Prop('Text');
end;

function TEmailTexteDictionary.Art: IEmailArtenDictionary;
begin
  Result := TEmailArtenDictionary.Create(PropName('Art'));
end;

function TEmailTexteDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

{ TFormatierungDictionary }

function TFormatierungDictionary.Code: TLinqProjection;
begin
  Result := Prop('Code');
end;

function TFormatierungDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

{ TFrageDictionary }

function TFrageDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TFrageDictionary.IdExtern: TLinqProjection;
begin
  Result := Prop('IdExtern');
end;

function TFrageDictionary.ExternQuelle: TLinqProjection;
begin
  Result := Prop('ExternQuelle');
end;

function TFrageDictionary.Lfnr: TLinqProjection;
begin
  Result := Prop('Lfnr');
end;

function TFrageDictionary.Fragennr: TLinqProjection;
begin
  Result := Prop('Fragennr');
end;

function TFrageDictionary.Text: TLinqProjection;
begin
  Result := Prop('Text');
end;

function TFrageDictionary.Info: TLinqProjection;
begin
  Result := Prop('Info');
end;

function TFrageDictionary.CCRelevant: TLinqProjection;
begin
  Result := Prop('CCRelevant');
end;

function TFrageDictionary.Checkliste: IChecklisteDictionary;
begin
  Result := TChecklisteDictionary.Create(PropName('Checkliste'));
end;

function TFrageDictionary.Formatierung: IFormatierungDictionary;
begin
  Result := TFormatierungDictionary.Create(PropName('Formatierung'));
end;

function TFrageDictionary.UebergeordneteFrage: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('UebergeordneteFrage'));
end;

function TFrageDictionary.Gruppe: IFragengruppeDictionary;
begin
  Result := TFragengruppeDictionary.Create(PropName('Gruppe'));
end;

function TFrageDictionary.FragenBewertungen: IFrageBewertungDictionary;
begin
  Result := TFrageBewertungDictionary.Create(PropName('FragenBewertungen'));
end;

function TFrageDictionary.BewerteteFragen: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create(PropName('BewerteteFragen'));
end;

function TFrageDictionary.UntergeordneteFragen: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('UntergeordneteFragen'));
end;

function TFrageDictionary.FragenKontrollbereiche: IFrageKontrollbereichDictionary;
begin
  Result := TFrageKontrollbereichDictionary.Create(PropName('FragenKontrollbereiche'));
end;

{ TFrageBewertungDictionary }

function TFrageBewertungDictionary.Ausblenden: TLinqProjection;
begin
  Result := Prop('Ausblenden');
end;

function TFrageBewertungDictionary.Positiv: TLinqProjection;
begin
  Result := Prop('Positiv');
end;

function TFrageBewertungDictionary.Sortierung: TLinqProjection;
begin
  Result := Prop('Sortierung');
end;

function TFrageBewertungDictionary.Bewertung: IBewertungDictionary;
begin
  Result := TBewertungDictionary.Create(PropName('Bewertung'));
end;

function TFrageBewertungDictionary.Frage: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('Frage'));
end;

function TFrageBewertungDictionary.StandardMassnahme: IMassnahmeDictionary;
begin
  Result := TMassnahmeDictionary.Create(PropName('StandardMassnahme'));
end;

function TFrageBewertungDictionary.StandardMangeltyp: IMangeltypDictionary;
begin
  Result := TMangeltypDictionary.Create(PropName('StandardMangeltyp'));
end;

{ TFrageKontrollbereichDictionary }

function TFrageKontrollbereichDictionary.Frage: IFrageDictionary;
begin
  Result := TFrageDictionary.Create(PropName('Frage'));
end;

function TFrageKontrollbereichDictionary.Kontrollbereich: IKontrollbereichDictionary;
begin
  Result := TKontrollbereichDictionary.Create(PropName('Kontrollbereich'));
end;

{ TFragengruppeDictionary }

function TFragengruppeDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TFragengruppeDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TFragengruppeDictionary.Farbcode: TLinqProjection;
begin
  Result := Prop('Farbcode');
end;

function TFragengruppeDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TFragengruppeDictionary.Aktiv: TLinqProjection;
begin
  Result := Prop('Aktiv');
end;

function TFragengruppeDictionary.Zuzsatztext: TLinqProjection;
begin
  Result := Prop('Zuzsatztext');
end;

function TFragengruppeDictionary.UebergeordneteGruppe: IFragengruppeDictionary;
begin
  Result := TFragengruppeDictionary.Create(PropName('UebergeordneteGruppe'));
end;

{ TFunktionDictionary }

function TFunktionDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TFunktionDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TFunktionDictionary.Hinttext: TLinqProjection;
begin
  Result := Prop('Hinttext');
end;

function TFunktionDictionary.Farbcode: TLinqProjection;
begin
  Result := Prop('Farbcode');
end;

function TFunktionDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TFunktionDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TFunktionDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TFunktionDictionary.Progcallid: TLinqProjection;
begin
  Result := Prop('Progcallid');
end;

function TFunktionDictionary.Objekt: TLinqProjection;
begin
  Result := Prop('Objekt');
end;

function TFunktionDictionary.Prgteil: TLinqProjection;
begin
  Result := Prop('Prgteil');
end;

function TFunktionDictionary.Position: TLinqProjection;
begin
  Result := Prop('Position');
end;

function TFunktionDictionary.Beschriftung: TLinqProjection;
begin
  Result := Prop('Beschriftung');
end;

function TFunktionDictionary.Mutter: IFunktionDictionary;
begin
  Result := TFunktionDictionary.Create(PropName('Mutter'));
end;

function TFunktionDictionary.Programmmodul: IProgrammModulDictionary;
begin
  Result := TProgrammModulDictionary.Create(PropName('Programmmodul'));
end;

function TFunktionDictionary.UnterFunktionen: IFunktionDictionary;
begin
  Result := TFunktionDictionary.Create(PropName('UnterFunktionen'));
end;

function TFunktionDictionary.Funktionsrollen: IFunktionRolleDictionary;
begin
  Result := TFunktionRolleDictionary.Create(PropName('Funktionsrollen'));
end;

{ TFunktionRolleDictionary }

function TFunktionRolleDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TFunktionRolleDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TFunktionRolleDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TFunktionRolleDictionary.Funktion: IFunktionDictionary;
begin
  Result := TFunktionDictionary.Create(PropName('Funktion'));
end;

function TFunktionRolleDictionary.Rolle: IRolleDictionary;
begin
  Result := TRolleDictionary.Create(PropName('Rolle'));
end;

{ TGemeindeDictionary }

function TGemeindeDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TGemeindeDictionary.Gemeindekennziffer: TLinqProjection;
begin
  Result := Prop('Gemeindekennziffer');
end;

function TGemeindeDictionary.Gemeindename: TLinqProjection;
begin
  Result := Prop('Gemeindename');
end;

function TGemeindeDictionary.Gemeindecode: TLinqProjection;
begin
  Result := Prop('Gemeindecode');
end;

function TGemeindeDictionary.Amtsplz: TLinqProjection;
begin
  Result := Prop('Amtsplz');
end;

function TGemeindeDictionary.PolBezKennzif: TLinqProjection;
begin
  Result := Prop('PolBezKennzif');
end;

function TGemeindeDictionary.PolBezirk: TLinqProjection;
begin
  Result := Prop('PolBezirk');
end;

function TGemeindeDictionary.PolBezCode: TLinqProjection;
begin
  Result := Prop('PolBezCode');
end;

function TGemeindeDictionary.LandIso2: TLinqProjection;
begin
  Result := Prop('LandIso2');
end;

function TGemeindeDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

{ TGruppeDictionary }

function TGruppeDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TGruppeDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TGruppeDictionary.Okz: TLinqProjection;
begin
  Result := Prop('Okz');
end;

function TGruppeDictionary.Persoenlich: TLinqProjection;
begin
  Result := Prop('Persoenlich');
end;

function TGruppeDictionary.Email: TLinqProjection;
begin
  Result := Prop('Email');
end;

function TGruppeDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TGruppeDictionary.Muttergruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Muttergruppe'));
end;

function TGruppeDictionary.Hauptverantwortlicher: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('Hauptverantwortlicher'));
end;

function TGruppeDictionary.Stellvertreter: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('Stellvertreter'));
end;

function TGruppeDictionary.Untergruppen: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Untergruppen'));
end;

function TGruppeDictionary.Nachrichten: INachrichtDictionary;
begin
  Result := TNachrichtDictionary.Create(PropName('Nachrichten'));
end;

function TGruppeDictionary.Usergruppen: IUsergruppeDictionary;
begin
  Result := TUsergruppeDictionary.Create(PropName('Usergruppen'));
end;

function TGruppeDictionary.TodoList: ITodoDictionary;
begin
  Result := TTodoDictionary.Create(PropName('TodoList'));
end;

{ TKbProbeDictionary }

function TKbProbeDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKbProbeDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TKbProbeDictionary.Probenbkb: TLinqProjection;
begin
  Result := Prop('Probenbkb');
end;

function TKbProbeDictionary.Probenart: TLinqProjection;
begin
  Result := Prop('Probenart');
end;

function TKbProbeDictionary.Bemerkung: TLinqProjection;
begin
  Result := Prop('Bemerkung');
end;

function TKbProbeDictionary.Datum: TLinqProjection;
begin
  Result := Prop('Datum');
end;

function TKbProbeDictionary.VorgMenge: TLinqProjection;
begin
  Result := Prop('VorgMenge');
end;

function TKbProbeDictionary.Beschaffenheit: TLinqProjection;
begin
  Result := Prop('Beschaffenheit');
end;

function TKbProbeDictionary.Futtertyp: TLinqProjection;
begin
  Result := Prop('Futtertyp');
end;

function TKbProbeDictionary.Verwendungszweck: TLinqProjection;
begin
  Result := Prop('Verwendungszweck');
end;

function TKbProbeDictionary.TierArtLisa: TLinqProjection;
begin
  Result := Prop('TierArtLisa');
end;

function TKbProbeDictionary.TierKategorie: TLinqProjection;
begin
  Result := Prop('TierKategorie');
end;

function TKbProbeDictionary.Beimischrate: TLinqProjection;
begin
  Result := Prop('Beimischrate');
end;

function TKbProbeDictionary.Verpackung: TLinqProjection;
begin
  Result := Prop('Verpackung');
end;

function TKbProbeDictionary.Verschluss: TLinqProjection;
begin
  Result := Prop('Verschluss');
end;

function TKbProbeDictionary.Versiegelt: TLinqProjection;
begin
  Result := Prop('Versiegelt');
end;

function TKbProbeDictionary.HerkZukauf: TLinqProjection;
begin
  Result := Prop('HerkZukauf');
end;

function TKbProbeDictionary.Untersuchungsauftrag: TLinqProjection;
begin
  Result := Prop('Untersuchungsauftrag');
end;

function TKbProbeDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TKbProbeDictionary.Verdacht: TLinqProjection;
begin
  Result := Prop('Verdacht');
end;

function TKbProbeDictionary.GegenprobeBelassen: TLinqProjection;
begin
  Result := Prop('GegenprobeBelassen');
end;

function TKbProbeDictionary.Exportname: TLinqProjection;
begin
  Result := Prop('Exportname');
end;

function TKbProbeDictionary.Exporttime: TLinqProjection;
begin
  Result := Prop('Exporttime');
end;

function TKbProbeDictionary.Agesauftragsnummer: TLinqProjection;
begin
  Result := Prop('Agesauftragsnummer');
end;

function TKbProbeDictionary.Agesprobennummer: TLinqProjection;
begin
  Result := Prop('Agesprobennummer');
end;

function TKbProbeDictionary.AgesAuftragsstatus: TLinqProjection;
begin
  Result := Prop('AgesAuftragsstatus');
end;

function TKbProbeDictionary.AgesProbenstatus: TLinqProjection;
begin
  Result := Prop('AgesProbenstatus');
end;

function TKbProbeDictionary.Probenbezeichnung: TLinqProjection;
begin
  Result := Prop('Probenbezeichnung');
end;

function TKbProbeDictionary.Futterart: TLinqProjection;
begin
  Result := Prop('Futterart');
end;

function TKbProbeDictionary.Probenkennung: TLinqProjection;
begin
  Result := Prop('Probenkennung');
end;

function TKbProbeDictionary.GpsLat: TLinqProjection;
begin
  Result := Prop('GpsLat');
end;

function TKbProbeDictionary.GpsLon: TLinqProjection;
begin
  Result := Prop('GpsLon');
end;

function TKbProbeDictionary.KennzeichnungGegenprobe: TLinqProjection;
begin
  Result := Prop('KennzeichnungGegenprobe');
end;

function TKbProbeDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TKbProbeDictionary.Einsender: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Einsender'));
end;

function TKbProbeDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TKbProbeDictionary.Dokument: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create(PropName('Dokument'));
end;

function TKbProbeDictionary.AgesErgebnisbericht: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create(PropName('AgesErgebnisbericht'));
end;

function TKbProbeDictionary.Bilder: IKontrollberichtBildDictionary;
begin
  Result := TKontrollberichtBildDictionary.Create(PropName('Bilder'));
end;

{ TKommunikationsartDictionary }

function TKommunikationsartDictionary.Art: TLinqProjection;
begin
  Result := Prop('Art');
end;

function TKommunikationsartDictionary.Kommunikationswege: IKommunikationswegDictionary;
begin
  Result := TKommunikationswegDictionary.Create(PropName('Kommunikationswege'));
end;

{ TKommunikationswegDictionary }

function TKommunikationswegDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKommunikationswegDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TKommunikationswegDictionary.Value: TLinqProjection;
begin
  Result := Prop('Value');
end;

function TKommunikationswegDictionary.Art: IKommunikationsartDictionary;
begin
  Result := TKommunikationsartDictionary.Create(PropName('Art'));
end;

function TKommunikationswegDictionary.BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
begin
  Result := TBetriebeKommunikationswegeDictionary.Create(PropName('BetriebeKommunikationswege'));
end;

function TKommunikationswegDictionary.PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
begin
  Result := TPersonenKommunikationswegeDictionary.Create(PropName('PersonenKommunikationswege'));
end;

{ TKontrollbereichDictionary }

function TKontrollbereichDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKontrollbereichDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TKontrollbereichDictionary.VisKkattid: TLinqProjection;
begin
  Result := Prop('VisKkattid');
end;

function TKontrollbereichDictionary.Pflichtbereich: TLinqProjection;
begin
  Result := Prop('Pflichtbereich');
end;

function TKontrollbereichDictionary.FragenKontrollbereiche: IFrageKontrollbereichDictionary;
begin
  Result := TFrageKontrollbereichDictionary.Create(PropName('FragenKontrollbereiche'));
end;

function TKontrollbereichDictionary.MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
begin
  Result := TMangelKontrollbereichDictionary.Create(PropName('MaengelKontrollbereiche'));
end;

{ TKontrollberichtDictionary }

function TKontrollberichtDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKontrollberichtDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TKontrollberichtDictionary.Bkb: TLinqProjection;
begin
  Result := Prop('Bkb');
end;

function TKontrollberichtDictionary.Datum: TLinqProjection;
begin
  Result := Prop('Datum');
end;

function TKontrollberichtDictionary.RefBkb: TLinqProjection;
begin
  Result := Prop('RefBkb');
end;

function TKontrollberichtDictionary.Probenziehung: TLinqProjection;
begin
  Result := Prop('Probenziehung');
end;

function TKontrollberichtDictionary.RegnrOrt: TLinqProjection;
begin
  Result := Prop('RegnrOrt');
end;

function TKontrollberichtDictionary.Kurzbemerkung: TLinqProjection;
begin
  Result := Prop('Kurzbemerkung');
end;

function TKontrollberichtDictionary.Startzeit: TLinqProjection;
begin
  Result := Prop('Startzeit');
end;

function TKontrollberichtDictionary.Endezeit: TLinqProjection;
begin
  Result := Prop('Endezeit');
end;

function TKontrollberichtDictionary.BestaetigtUm: TLinqProjection;
begin
  Result := Prop('BestaetigtUm');
end;

function TKontrollberichtDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TKontrollberichtDictionary.AngemeldetUm: TLinqProjection;
begin
  Result := Prop('AngemeldetUm');
end;

function TKontrollberichtDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TKontrollberichtDictionary.Lastchange: TLinqProjection;
begin
  Result := Prop('Lastchange');
end;

function TKontrollberichtDictionary.Betriebstyp: TLinqProjection;
begin
  Result := Prop('Betriebstyp');
end;

function TKontrollberichtDictionary.Verweigerungsgrund: TLinqProjection;
begin
  Result := Prop('Verweigerungsgrund');
end;

function TKontrollberichtDictionary.VerweigerungsgrundUnterschrift: TLinqProjection;
begin
  Result := Prop('VerweigerungsgrundUnterschrift');
end;

function TKontrollberichtDictionary.FehlerhaftGesetztAm: TLinqProjection;
begin
  Result := Prop('FehlerhaftGesetztAm');
end;

function TKontrollberichtDictionary.StorniertAm: TLinqProjection;
begin
  Result := Prop('StorniertAm');
end;

function TKontrollberichtDictionary.Stornogrund: TLinqProjection;
begin
  Result := Prop('Stornogrund');
end;

function TKontrollberichtDictionary.VerweigertAm: TLinqProjection;
begin
  Result := Prop('VerweigertAm');
end;

function TKontrollberichtDictionary.KontrollInformationen: TLinqProjection;
begin
  Result := Prop('KontrollInformationen');
end;

function TKontrollberichtDictionary.InterneNotiz: TLinqProjection;
begin
  Result := Prop('InterneNotiz');
end;

function TKontrollberichtDictionary.BetriebName: TLinqProjection;
begin
  Result := Prop('BetriebName');
end;

function TKontrollberichtDictionary.BetriebRegnr: TLinqProjection;
begin
  Result := Prop('BetriebRegnr');
end;

function TKontrollberichtDictionary.BetriebBld: TLinqProjection;
begin
  Result := Prop('BetriebBld');
end;

function TKontrollberichtDictionary.BetriebAdresseStrasse: TLinqProjection;
begin
  Result := Prop('BetriebAdresseStrasse');
end;

function TKontrollberichtDictionary.BetriebAdresseZusatz: TLinqProjection;
begin
  Result := Prop('BetriebAdresseZusatz');
end;

function TKontrollberichtDictionary.BetriebAdressePlz: TLinqProjection;
begin
  Result := Prop('BetriebAdressePlz');
end;

function TKontrollberichtDictionary.BetriebAdresseOrt: TLinqProjection;
begin
  Result := Prop('BetriebAdresseOrt');
end;

function TKontrollberichtDictionary.KontrolltypBezeichnung: TLinqProjection;
begin
  Result := Prop('KontrolltypBezeichnung');
end;

function TKontrollberichtDictionary.BkbtypBezeichnung: TLinqProjection;
begin
  Result := Prop('BkbtypBezeichnung');
end;

function TKontrollberichtDictionary.ErfasserNachname: TLinqProjection;
begin
  Result := Prop('ErfasserNachname');
end;

function TKontrollberichtDictionary.ErfasserVorname: TLinqProjection;
begin
  Result := Prop('ErfasserVorname');
end;

function TKontrollberichtDictionary.ErfasserAnrede: TLinqProjection;
begin
  Result := Prop('ErfasserAnrede');
end;

function TKontrollberichtDictionary.ErfasserTitel: TLinqProjection;
begin
  Result := Prop('ErfasserTitel');
end;

function TKontrollberichtDictionary.KontrollorganNachname: TLinqProjection;
begin
  Result := Prop('KontrollorganNachname');
end;

function TKontrollberichtDictionary.KontrollorganVorname: TLinqProjection;
begin
  Result := Prop('KontrollorganVorname');
end;

function TKontrollberichtDictionary.KontrollorganAnrede: TLinqProjection;
begin
  Result := Prop('KontrollorganAnrede');
end;

function TKontrollberichtDictionary.KontrollorganTitel: TLinqProjection;
begin
  Result := Prop('KontrollorganTitel');
end;

function TKontrollberichtDictionary.MaengelGesamt: TLinqProjection;
begin
  Result := Prop('MaengelGesamt');
end;

function TKontrollberichtDictionary.MaengelOffen: TLinqProjection;
begin
  Result := Prop('MaengelOffen');
end;

function TKontrollberichtDictionary.TodoCount: TLinqProjection;
begin
  Result := Prop('TodoCount');
end;

function TKontrollberichtDictionary.EffectiveDate: TLinqProjection;
begin
  Result := Prop('EffectiveDate');
end;

function TKontrollberichtDictionary.Prioritaet: TLinqProjection;
begin
  Result := Prop('Prioritaet');
end;

function TKontrollberichtDictionary.SeuchenId: TLinqProjection;
begin
  Result := Prop('SeuchenId');
end;

function TKontrollberichtDictionary.Betrieb: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betrieb'));
end;

function TKontrollberichtDictionary.Erfasser: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Erfasser'));
end;

function TKontrollberichtDictionary.Kontrollorgan: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Kontrollorgan'));
end;

function TKontrollberichtDictionary.Kontrolltyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('Kontrolltyp'));
end;

function TKontrollberichtDictionary.Rechtsgrundlage: IRechtsgrundlageDictionary;
begin
  Result := TRechtsgrundlageDictionary.Create(PropName('Rechtsgrundlage'));
end;

function TKontrollberichtDictionary.UnterschriftAnwesenderBetrieb: IUnterschriftDictionary;
begin
  Result := TUnterschriftDictionary.Create(PropName('UnterschriftAnwesenderBetrieb'));
end;

function TKontrollberichtDictionary.UnterschriftKontrollorgan: IUnterschriftDictionary;
begin
  Result := TUnterschriftDictionary.Create(PropName('UnterschriftKontrollorgan'));
end;

function TKontrollberichtDictionary.Dokument: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create(PropName('Dokument'));
end;

function TKontrollberichtDictionary.DokumentCC: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create(PropName('DokumentCC'));
end;

function TKontrollberichtDictionary.GruppeQuelle: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('GruppeQuelle'));
end;

function TKontrollberichtDictionary.Revisionsplan: IRevisionsplanDictionary;
begin
  Result := TRevisionsplanDictionary.Create(PropName('Revisionsplan'));
end;

function TKontrollberichtDictionary.Todos: ITodoDictionary;
begin
  Result := TTodoDictionary.Create(PropName('Todos'));
end;

function TKontrollberichtDictionary.BewerteteFragen: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create(PropName('BewerteteFragen'));
end;

function TKontrollberichtDictionary.Proben: IKbProbeDictionary;
begin
  Result := TKbProbeDictionary.Create(PropName('Proben'));
end;

function TKontrollberichtDictionary.Anwesende: IAnwesenderDictionary;
begin
  Result := TAnwesenderDictionary.Create(PropName('Anwesende'));
end;

function TKontrollberichtDictionary.CckAuswahldaten: ICckAuswahlDictionary;
begin
  Result := TCckAuswahlDictionary.Create(PropName('CckAuswahldaten'));
end;

function TKontrollberichtDictionary.Oertlichkeiten: IKontrollberichtOertlichkeitDictionary;
begin
  Result := TKontrollberichtOertlichkeitDictionary.Create(PropName('Oertlichkeiten'));
end;

function TKontrollberichtDictionary.CckBetriebsdaten: ICckBetriebDictionary;
begin
  Result := TCckBetriebDictionary.Create(PropName('CckBetriebsdaten'));
end;

{ TKontrollberichtBildDictionary }

function TKontrollberichtBildDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKontrollberichtBildDictionary.Bild: TLinqProjection;
begin
  Result := Prop('Bild');
end;

function TKontrollberichtBildDictionary.Format: TLinqProjection;
begin
  Result := Prop('Format');
end;

function TKontrollberichtBildDictionary.Bemerkung: TLinqProjection;
begin
  Result := Prop('Bemerkung');
end;

function TKontrollberichtBildDictionary.Aufnahmedatum: TLinqProjection;
begin
  Result := Prop('Aufnahmedatum');
end;

function TKontrollberichtBildDictionary.GpsLat: TLinqProjection;
begin
  Result := Prop('GpsLat');
end;

function TKontrollberichtBildDictionary.GpsLon: TLinqProjection;
begin
  Result := Prop('GpsLon');
end;

function TKontrollberichtBildDictionary.BewerteteFrage: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create(PropName('BewerteteFrage'));
end;

function TKontrollberichtBildDictionary.Mangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Mangel'));
end;

function TKontrollberichtBildDictionary.AufgenommenVon: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('AufgenommenVon'));
end;

function TKontrollberichtBildDictionary.Probe: IKbProbeDictionary;
begin
  Result := TKbProbeDictionary.Create(PropName('Probe'));
end;

{ TKontrollberichtOertlichkeitDictionary }

function TKontrollberichtOertlichkeitDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TKontrollberichtOertlichkeitDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TKontrollberichtOertlichkeitDictionary.Bautyp: TLinqProjection;
begin
  Result := Prop('Bautyp');
end;

function TKontrollberichtOertlichkeitDictionary.Haltungsform: TLinqProjection;
begin
  Result := Prop('Haltungsform');
end;

function TKontrollberichtOertlichkeitDictionary.Krankheiten: TLinqProjection;
begin
  Result := Prop('Krankheiten');
end;

function TKontrollberichtOertlichkeitDictionary.Krankheitsbeschreibung: TLinqProjection;
begin
  Result := Prop('Krankheitsbeschreibung');
end;

function TKontrollberichtOertlichkeitDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

function TKontrollberichtOertlichkeitDictionary.OertlichkeitMaengel: IMangelOertlichkeitDictionary;
begin
  Result := TMangelOertlichkeitDictionary.Create(PropName('OertlichkeitMaengel'));
end;

{ TKontrolltypDictionary }

function TKontrolltypDictionary.Kontrolltyp: TLinqProjection;
begin
  Result := Prop('Kontrolltyp');
end;

function TKontrolltypDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TKontrolltypDictionary.Refbkb: TLinqProjection;
begin
  Result := Prop('Refbkb');
end;

function TKontrolltypDictionary.FreieAdresse: TLinqProjection;
begin
  Result := Prop('FreieAdresse');
end;

function TKontrolltypDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TKontrolltypDictionary.Proben: TLinqProjection;
begin
  Result := Prop('Proben');
end;

function TKontrolltypDictionary.Oertlichkeiten: TLinqProjection;
begin
  Result := Prop('Oertlichkeiten');
end;

function TKontrolltypDictionary.AutoKontrolliert: TLinqProjection;
begin
  Result := Prop('AutoKontrolliert');
end;

function TKontrolltypDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TKontrolltypDictionary.Kontrollberichte: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollberichte'));
end;

function TKontrolltypDictionary.CckModulKontrolltypen: ICckModulKontrolltypDictionary;
begin
  Result := TCckModulKontrolltypDictionary.Create(PropName('CckModulKontrolltypen'));
end;

function TKontrolltypDictionary.KontrolltypReports: IKontrolltypReportDictionary;
begin
  Result := TKontrolltypReportDictionary.Create(PropName('KontrolltypReports'));
end;

function TKontrolltypDictionary.ChecklistenKontrolltypen: IChecklistenKontrolltypDictionary;
begin
  Result := TChecklistenKontrolltypDictionary.Create(PropName('ChecklistenKontrolltypen'));
end;

{ TKontrolltypReportDictionary }

function TKontrolltypReportDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TKontrolltypReportDictionary.Vorschau: TLinqProjection;
begin
  Result := Prop('Vorschau');
end;

function TKontrolltypReportDictionary.Mailbetrieb: TLinqProjection;
begin
  Result := Prop('Mailbetrieb');
end;

function TKontrolltypReportDictionary.Mailkontrolleur: TLinqProjection;
begin
  Result := Prop('Mailkontrolleur');
end;

function TKontrolltypReportDictionary.Mailgruppe: TLinqProjection;
begin
  Result := Prop('Mailgruppe');
end;

function TKontrolltypReportDictionary.Mailmuttergruppe: TLinqProjection;
begin
  Result := Prop('Mailmuttergruppe');
end;

function TKontrolltypReportDictionary.Mailmastergruppe: TLinqProjection;
begin
  Result := Prop('Mailmastergruppe');
end;

function TKontrolltypReportDictionary.Kontrolltyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('Kontrolltyp'));
end;

function TKontrolltypReportDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TKontrolltypReportDictionary.Report: IReportDictionary;
begin
  Result := TReportDictionary.Create(PropName('Report'));
end;

function TKontrolltypReportDictionary.ReportTyp: IReportTypDictionary;
begin
  Result := TReportTypDictionary.Create(PropName('ReportTyp'));
end;

{ TLandDictionary }

function TLandDictionary.Landkz: TLinqProjection;
begin
  Result := Prop('Landkz');
end;

function TLandDictionary.Landnr: TLinqProjection;
begin
  Result := Prop('Landnr');
end;

function TLandDictionary.LandIso3: TLinqProjection;
begin
  Result := Prop('LandIso3');
end;

function TLandDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TLandDictionary.Bundeslaender: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundeslaender'));
end;

{ TMangelDictionary }

function TMangelDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TMangelDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TMangelDictionary.Frist: TLinqProjection;
begin
  Result := Prop('Frist');
end;

function TMangelDictionary.Text: TLinqProjection;
begin
  Result := Prop('Text');
end;

function TMangelDictionary.BeseitigtAm: TLinqProjection;
begin
  Result := Prop('BeseitigtAm');
end;

function TMangelDictionary.StorniertAm: TLinqProjection;
begin
  Result := Prop('StorniertAm');
end;

function TMangelDictionary.Behebungsauftrag: TLinqProjection;
begin
  Result := Prop('Behebungsauftrag');
end;

function TMangelDictionary.Status: IMangelStatusDictionary;
begin
  Result := TMangelStatusDictionary.Create(PropName('Status'));
end;

function TMangelDictionary.Mangeltyp: IMangeltypDictionary;
begin
  Result := TMangeltypDictionary.Create(PropName('Mangeltyp'));
end;

function TMangelDictionary.Massnahme: IMassnahmeDictionary;
begin
  Result := TMassnahmeDictionary.Create(PropName('Massnahme'));
end;

function TMangelDictionary.MangelWeiterfuehrung: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('MangelWeiterfuehrung'));
end;

function TMangelDictionary.BewerteteFragen: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create(PropName('BewerteteFragen'));
end;

function TMangelDictionary.Bilder: IKontrollberichtBildDictionary;
begin
  Result := TKontrollberichtBildDictionary.Create(PropName('Bilder'));
end;

function TMangelDictionary.MaengelKontrollbereiche: IMangelKontrollbereichDictionary;
begin
  Result := TMangelKontrollbereichDictionary.Create(PropName('MaengelKontrollbereiche'));
end;

function TMangelDictionary.WeitergefuehrtVonMangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('WeitergefuehrtVonMangel'));
end;

function TMangelDictionary.MangelOertlichkeiten: IMangelOertlichkeitDictionary;
begin
  Result := TMangelOertlichkeitDictionary.Create(PropName('MangelOertlichkeiten'));
end;

{ TMangelKontrollbereichDictionary }

function TMangelKontrollbereichDictionary.Mangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Mangel'));
end;

function TMangelKontrollbereichDictionary.Kontrollbereich: IKontrollbereichDictionary;
begin
  Result := TKontrollbereichDictionary.Create(PropName('Kontrollbereich'));
end;

{ TMangelOertlichkeitDictionary }

function TMangelOertlichkeitDictionary.Mangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Mangel'));
end;

function TMangelOertlichkeitDictionary.Oertlichkeit: IKontrollberichtOertlichkeitDictionary;
begin
  Result := TKontrollberichtOertlichkeitDictionary.Create(PropName('Oertlichkeit'));
end;

{ TMangelStatusDictionary }

function TMangelStatusDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TMangelStatusDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

function TMangelStatusDictionary.Maengel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Maengel'));
end;

{ TMangeltypDictionary }

function TMangeltypDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TMangeltypDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TMangeltypDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TMangeltypDictionary.Massnahmenkatalog: IMassnahmenkatalogDictionary;
begin
  Result := TMassnahmenkatalogDictionary.Create(PropName('Massnahmenkatalog'));
end;

function TMangeltypDictionary.Maengel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Maengel'));
end;

function TMangeltypDictionary.FragenBewertungen: IFrageBewertungDictionary;
begin
  Result := TFrageBewertungDictionary.Create(PropName('FragenBewertungen'));
end;

{ TMassnahmeDictionary }

function TMassnahmeDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TMassnahmeDictionary.Langtext: TLinqProjection;
begin
  Result := Prop('Langtext');
end;

function TMassnahmeDictionary.Standardfrist: TLinqProjection;
begin
  Result := Prop('Standardfrist');
end;

function TMassnahmeDictionary.Massnahmenkatalog: IMassnahmenkatalogDictionary;
begin
  Result := TMassnahmenkatalogDictionary.Create(PropName('Massnahmenkatalog'));
end;

function TMassnahmeDictionary.FragenBewertungen: IFrageBewertungDictionary;
begin
  Result := TFrageBewertungDictionary.Create(PropName('FragenBewertungen'));
end;

function TMassnahmeDictionary.Maengel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create(PropName('Maengel'));
end;

{ TMassnahmenkatalogDictionary }

function TMassnahmenkatalogDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TMassnahmenkatalogDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TMassnahmenkatalogDictionary.Massnahmen: IMassnahmeDictionary;
begin
  Result := TMassnahmeDictionary.Create(PropName('Massnahmen'));
end;

function TMassnahmenkatalogDictionary.Mangeltypen: IMangeltypDictionary;
begin
  Result := TMangeltypDictionary.Create(PropName('Mangeltypen'));
end;

{ TModulDictionary }

function TModulDictionary.Modul: TLinqProjection;
begin
  Result := Prop('Modul');
end;

function TModulDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TModulDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TModulDictionary.InsDbuser: TLinqProjection;
begin
  Result := Prop('InsDbuser');
end;

function TModulDictionary.Lastchange: TLinqProjection;
begin
  Result := Prop('Lastchange');
end;

function TModulDictionary.ChangeUser: TLinqProjection;
begin
  Result := Prop('ChangeUser');
end;

function TModulDictionary.BundeslaenderModule: IBundeslandModulDictionary;
begin
  Result := TBundeslandModulDictionary.Create(PropName('BundeslaenderModule'));
end;

function TModulDictionary.Bkbtypen: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtypen'));
end;

{ TModulInstanzDictionary }

function TModulInstanzDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TModulInstanzDictionary.InstanzName: TLinqProjection;
begin
  Result := Prop('InstanzName');
end;

function TModulInstanzDictionary.InstanzUrl: TLinqProjection;
begin
  Result := Prop('InstanzUrl');
end;

function TModulInstanzDictionary.Modul: IProgrammModulDictionary;
begin
  Result := TProgrammModulDictionary.Create(PropName('Modul'));
end;

{ TModuleBkbtypenDictionary }

function TModuleBkbtypenDictionary.IdModul: TLinqProjection;
begin
  Result := Prop('IdModul');
end;

function TModuleBkbtypenDictionary.Bkbtyp: TLinqProjection;
begin
  Result := Prop('Bkbtyp');
end;

{ TNachrichtDictionary }

function TNachrichtDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TNachrichtDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TNachrichtDictionary.Typ: TLinqProjection;
begin
  Result := Prop('Typ');
end;

function TNachrichtDictionary.AbsenderKz: TLinqProjection;
begin
  Result := Prop('AbsenderKz');
end;

function TNachrichtDictionary.Priori: TLinqProjection;
begin
  Result := Prop('Priori');
end;

function TNachrichtDictionary.Text: TLinqProjection;
begin
  Result := Prop('Text');
end;

function TNachrichtDictionary.Link: TLinqProjection;
begin
  Result := Prop('Link');
end;

function TNachrichtDictionary.GesendetAm: TLinqProjection;
begin
  Result := Prop('GesendetAm');
end;

function TNachrichtDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

function TNachrichtDictionary.Absender: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('Absender'));
end;

function TNachrichtDictionary.Zustellungen: INachrichtenZustellungDictionary;
begin
  Result := TNachrichtenZustellungDictionary.Create(PropName('Zustellungen'));
end;

function TNachrichtDictionary.VNachrichtenUser: IVNachrichtForUserDictionary;
begin
  Result := TVNachrichtForUserDictionary.Create(PropName('VNachrichtenUser'));
end;

{ TNachrichtenZustellungDictionary }

function TNachrichtenZustellungDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TNachrichtenZustellungDictionary.GesehenTstamp: TLinqProjection;
begin
  Result := Prop('GesehenTstamp');
end;

function TNachrichtenZustellungDictionary.GelesenTstamp: TLinqProjection;
begin
  Result := Prop('GelesenTstamp');
end;

function TNachrichtenZustellungDictionary.Nachricht: INachrichtDictionary;
begin
  Result := TNachrichtDictionary.Create(PropName('Nachricht'));
end;

function TNachrichtenZustellungDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

{ TPersonDictionary }

function TPersonDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TPersonDictionary.Titel: TLinqProjection;
begin
  Result := Prop('Titel');
end;

function TPersonDictionary.Vorname: TLinqProjection;
begin
  Result := Prop('Vorname');
end;

function TPersonDictionary.Nachname: TLinqProjection;
begin
  Result := Prop('Nachname');
end;

function TPersonDictionary.AdresseALT: TLinqProjection;
begin
  Result := Prop('AdresseALT');
end;

function TPersonDictionary.OrtALT: TLinqProjection;
begin
  Result := Prop('OrtALT');
end;

function TPersonDictionary.PlzALT: TLinqProjection;
begin
  Result := Prop('PlzALT');
end;

function TPersonDictionary.Landkz: TLinqProjection;
begin
  Result := Prop('Landkz');
end;

function TPersonDictionary.Telefon: TLinqProjection;
begin
  Result := Prop('Telefon');
end;

function TPersonDictionary.Email: TLinqProjection;
begin
  Result := Prop('Email');
end;

function TPersonDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TPersonDictionary.InsDbuser: TLinqProjection;
begin
  Result := Prop('InsDbuser');
end;

function TPersonDictionary.Lastchange: TLinqProjection;
begin
  Result := Prop('Lastchange');
end;

function TPersonDictionary.ChangeDbuser: TLinqProjection;
begin
  Result := Prop('ChangeDbuser');
end;

function TPersonDictionary.Anrede: TLinqProjection;
begin
  Result := Prop('Anrede');
end;

function TPersonDictionary.Adresse: IAdresseDictionary;
begin
  Result := TAdresseDictionary.Create(PropName('Adresse'));
end;

function TPersonDictionary.KontrollberichteErfasser: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('KontrollberichteErfasser'));
end;

function TPersonDictionary.KontrollberichteKontrollorgan: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('KontrollberichteKontrollorgan'));
end;

function TPersonDictionary.Kommunikationswege: IPersonenKommunikationswegeDictionary;
begin
  Result := TPersonenKommunikationswegeDictionary.Create(PropName('Kommunikationswege'));
end;

function TPersonDictionary.Users: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('Users'));
end;

{ TPersonenKommunikationswegeDictionary }

function TPersonenKommunikationswegeDictionary.Kommunikationsweg: IKommunikationswegDictionary;
begin
  Result := TKommunikationswegDictionary.Create(PropName('Kommunikationsweg'));
end;

function TPersonenKommunikationswegeDictionary.Person: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Person'));
end;

{ TProgrammModulDictionary }

function TProgrammModulDictionary.Kurzbezeichnnung: TLinqProjection;
begin
  Result := Prop('Kurzbezeichnnung');
end;

function TProgrammModulDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

function TProgrammModulDictionary.Funktionen: IFunktionDictionary;
begin
  Result := TFunktionDictionary.Create(PropName('Funktionen'));
end;

{ TRechtsgrundlageDictionary }

function TRechtsgrundlageDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TRechtsgrundlageDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TRechtsgrundlageDictionary.Kurzbezeichnung: TLinqProjection;
begin
  Result := Prop('Kurzbezeichnung');
end;

function TRechtsgrundlageDictionary.UnterschriftErforderlich: TLinqProjection;
begin
  Result := Prop('UnterschriftErforderlich');
end;

function TRechtsgrundlageDictionary.Kontrollberichte: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollberichte'));
end;

function TRechtsgrundlageDictionary.Bkbtypen: IBkbtypenRechtsgrundlageDictionary;
begin
  Result := TBkbtypenRechtsgrundlageDictionary.Create(PropName('Bkbtypen'));
end;

{ TRegistrierungDictionary }

function TRegistrierungDictionary.Regnr: TLinqProjection;
begin
  Result := Prop('Regnr');
end;

function TRegistrierungDictionary.FreierBetrieb: TLinqProjection;
begin
  Result := Prop('FreierBetrieb');
end;

function TRegistrierungDictionary.Zulassungen: IZulassungDictionary;
begin
  Result := TZulassungDictionary.Create(PropName('Zulassungen'));
end;

function TRegistrierungDictionary.Betriebe: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betriebe'));
end;

{ TReportDictionary }

function TReportDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TReportDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TReportDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

function TReportDictionary.Reporttyp: TLinqProjection;
begin
  Result := Prop('Reporttyp');
end;

function TReportDictionary.Reportvorlage: TLinqProjection;
begin
  Result := Prop('Reportvorlage');
end;

function TReportDictionary.ReportKontrolltypen: IKontrolltypReportDictionary;
begin
  Result := TKontrolltypReportDictionary.Create(PropName('ReportKontrolltypen'));
end;

{ TReportTypDictionary }

function TReportTypDictionary.Typ: TLinqProjection;
begin
  Result := Prop('Typ');
end;

function TReportTypDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

{ TRevisionsSchemaDictionary }

function TRevisionsSchemaDictionary.RevSchema: TLinqProjection;
begin
  Result := Prop('RevSchema');
end;

function TRevisionsSchemaDictionary.Geplant: TLinqProjection;
begin
  Result := Prop('Geplant');
end;

function TRevisionsSchemaDictionary.Beschreibung: TLinqProjection;
begin
  Result := Prop('Beschreibung');
end;

{ TRevisionsplanDictionary }

function TRevisionsplanDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TRevisionsplanDictionary.Jahr: TLinqProjection;
begin
  Result := Prop('Jahr');
end;

function TRevisionsplanDictionary.RisikoKategorie: TLinqProjection;
begin
  Result := Prop('RisikoKategorie');
end;

function TRevisionsplanDictionary.JMindestKontrollFrequenz: TLinqProjection;
begin
  Result := Prop('JMindestKontrollFrequenz');
end;

function TRevisionsplanDictionary.AnzBetriebeImLand: TLinqProjection;
begin
  Result := Prop('AnzBetriebeImLand');
end;

function TRevisionsplanDictionary.AnzGesamtKontrollen: TLinqProjection;
begin
  Result := Prop('AnzGesamtKontrollen');
end;

function TRevisionsplanDictionary.Gesperrt: TLinqProjection;
begin
  Result := Prop('Gesperrt');
end;

function TRevisionsplanDictionary.Bldcode: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bldcode'));
end;

function TRevisionsplanDictionary.Revisionsstamm: IRevisionsstammDictionary;
begin
  Result := TRevisionsstammDictionary.Create(PropName('Revisionsstamm'));
end;

{ TRevisionsstammDictionary }

function TRevisionsstammDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TRevisionsstammDictionary.Bldcode: TLinqProjection;
begin
  Result := Prop('Bldcode');
end;

function TRevisionsstammDictionary.Sektion: TLinqProjection;
begin
  Result := Prop('Sektion');
end;

function TRevisionsstammDictionary.BetriebsgruppeLm: TLinqProjection;
begin
  Result := Prop('BetriebsgruppeLm');
end;

function TRevisionsstammDictionary.BetriebsgruppeDetail: TLinqProjection;
begin
  Result := Prop('BetriebsgruppeDetail');
end;

function TRevisionsstammDictionary.Betriebsart: TLinqProjection;
begin
  Result := Prop('Betriebsart');
end;

function TRevisionsstammDictionary.Betriebsgruppenkz: TLinqProjection;
begin
  Result := Prop('Betriebsgruppenkz');
end;

function TRevisionsstammDictionary.KontrollTyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create(PropName('KontrollTyp'));
end;

function TRevisionsstammDictionary.Rechtsgrundlage: IRechtsgrundlageDictionary;
begin
  Result := TRechtsgrundlageDictionary.Create(PropName('Rechtsgrundlage'));
end;

function TRevisionsstammDictionary.RevisionsSchema: IRevisionsSchemaDictionary;
begin
  Result := TRevisionsSchemaDictionary.Create(PropName('RevisionsSchema'));
end;

{ TRolleDictionary }

function TRolleDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TRolleDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TRolleDictionary.Defaultrolle: TLinqProjection;
begin
  Result := Prop('Defaultrolle');
end;

function TRolleDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TRolleDictionary.GueltigAb: TLinqProjection;
begin
  Result := Prop('GueltigAb');
end;

function TRolleDictionary.Parameter: TLinqProjection;
begin
  Result := Prop('Parameter');
end;

function TRolleDictionary.FunktionenRollen: IFunktionRolleDictionary;
begin
  Result := TFunktionRolleDictionary.Create(PropName('FunktionenRollen'));
end;

function TRolleDictionary.UserRollen: IUserrolleDictionary;
begin
  Result := TUserrolleDictionary.Create(PropName('UserRollen'));
end;

function TRolleDictionary.RollenBkbtypen: IRollenBkbtypenDictionary;
begin
  Result := TRollenBkbtypenDictionary.Create(PropName('RollenBkbtypen'));
end;

{ TRollenBkbtypenDictionary }

function TRollenBkbtypenDictionary.Bkbtyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create(PropName('Bkbtyp'));
end;

function TRollenBkbtypenDictionary.Rolle: IRolleDictionary;
begin
  Result := TRolleDictionary.Create(PropName('Rolle'));
end;

{ TSysDictionary }

function TSysDictionary.Systemkz: TLinqProjection;
begin
  Result := Prop('Systemkz');
end;

function TSysDictionary.Systemname: TLinqProjection;
begin
  Result := Prop('Systemname');
end;

function TSysDictionary.VersionDb: TLinqProjection;
begin
  Result := Prop('VersionDb');
end;

function TSysDictionary.Bkbnummern: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create(PropName('Bkbnummern'));
end;

{ TTodoDictionary }

function TTodoDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TTodoDictionary.Titel: TLinqProjection;
begin
  Result := Prop('Titel');
end;

function TTodoDictionary.Faellig: TLinqProjection;
begin
  Result := Prop('Faellig');
end;

function TTodoDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

function TTodoDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

function TTodoDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrollbericht'));
end;

{ TUnterschriftDictionary }

function TUnterschriftDictionary.Guid: TLinqProjection;
begin
  Result := Prop('Guid');
end;

function TUnterschriftDictionary.Bild: TLinqProjection;
begin
  Result := Prop('Bild');
end;

function TUnterschriftDictionary.Datum: TLinqProjection;
begin
  Result := Prop('Datum');
end;

function TUnterschriftDictionary.Name: TLinqProjection;
begin
  Result := Prop('Name');
end;

function TUnterschriftDictionary.IdPerson: TLinqProjection;
begin
  Result := Prop('IdPerson');
end;

function TUnterschriftDictionary.KontrollberichteAnwesender: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('KontrollberichteAnwesender'));
end;

function TUnterschriftDictionary.KontrollberichteKontrollorgan: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('KontrollberichteKontrollorgan'));
end;

{ TUserDictionary }

function TUserDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TUserDictionary.Username: TLinqProjection;
begin
  Result := Prop('Username');
end;

function TUserDictionary.Userguid: TLinqProjection;
begin
  Result := Prop('Userguid');
end;

function TUserDictionary.Usertype: TLinqProjection;
begin
  Result := Prop('Usertype');
end;

function TUserDictionary.Gesperrt: TLinqProjection;
begin
  Result := Prop('Gesperrt');
end;

function TUserDictionary.Userpwc: TLinqProjection;
begin
  Result := Prop('Userpwc');
end;

function TUserDictionary.Aktiv: TLinqProjection;
begin
  Result := Prop('Aktiv');
end;

function TUserDictionary.TstampInsert: TLinqProjection;
begin
  Result := Prop('TstampInsert');
end;

function TUserDictionary.InsDbuser: TLinqProjection;
begin
  Result := Prop('InsDbuser');
end;

function TUserDictionary.Lastchange: TLinqProjection;
begin
  Result := Prop('Lastchange');
end;

function TUserDictionary.ChangeDbuser: TLinqProjection;
begin
  Result := Prop('ChangeDbuser');
end;

function TUserDictionary.Email: TLinqProjection;
begin
  Result := Prop('Email');
end;

function TUserDictionary.LastLogin: TLinqProjection;
begin
  Result := Prop('LastLogin');
end;

function TUserDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

function TUserDictionary.Person: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('Person'));
end;

function TUserDictionary.StellvertreterFuerGruppen: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('StellvertreterFuerGruppen'));
end;

function TUserDictionary.Nachrichten: INachrichtDictionary;
begin
  Result := TNachrichtDictionary.Create(PropName('Nachrichten'));
end;

function TUserDictionary.NachrichtenZustellungen: INachrichtenZustellungDictionary;
begin
  Result := TNachrichtenZustellungDictionary.Create(PropName('NachrichtenZustellungen'));
end;

function TUserDictionary.Usergruppen: IUsergruppeDictionary;
begin
  Result := TUsergruppeDictionary.Create(PropName('Usergruppen'));
end;

function TUserDictionary.Userrollen: IUserrolleDictionary;
begin
  Result := TUserrolleDictionary.Create(PropName('Userrollen'));
end;

function TUserDictionary.Todos: ITodoDictionary;
begin
  Result := TTodoDictionary.Create(PropName('Todos'));
end;

function TUserDictionary.HauptverantwortlicherFuerGruppen: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('HauptverantwortlicherFuerGruppen'));
end;

function TUserDictionary.KontrollberichtBilder: IKontrollberichtBildDictionary;
begin
  Result := TKontrollberichtBildDictionary.Create(PropName('KontrollberichtBilder'));
end;

{ TUsergruppeDictionary }

function TUsergruppeDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TUsergruppeDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TUsergruppeDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

function TUsergruppeDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

{ TUserrolleDictionary }

function TUserrolleDictionary.Begdat: TLinqProjection;
begin
  Result := Prop('Begdat');
end;

function TUserrolleDictionary.Enddat: TLinqProjection;
begin
  Result := Prop('Enddat');
end;

function TUserrolleDictionary.Rolle: IRolleDictionary;
begin
  Result := TRolleDictionary.Create(PropName('Rolle'));
end;

function TUserrolleDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

function TUserrolleDictionary.ModulInstanz: IModulInstanzDictionary;
begin
  Result := TModulInstanzDictionary.Create(PropName('ModulInstanz'));
end;

{ TVNachrichtForUserDictionary }

function TVNachrichtForUserDictionary.GesehenTstamp: TLinqProjection;
begin
  Result := Prop('GesehenTstamp');
end;

function TVNachrichtForUserDictionary.GelesenTstamp: TLinqProjection;
begin
  Result := Prop('GelesenTstamp');
end;

function TVNachrichtForUserDictionary.Nachricht: INachrichtDictionary;
begin
  Result := TNachrichtDictionary.Create(PropName('Nachricht'));
end;

function TVNachrichtForUserDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

function TVNachrichtForUserDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

function TVNachrichtForUserDictionary.NachrichtenZustellung: INachrichtenZustellungDictionary;
begin
  Result := TNachrichtenZustellungDictionary.Create(PropName('NachrichtenZustellung'));
end;

{ TVUserGroupMembershipDictionary }

function TVUserGroupMembershipDictionary.MembershipLevel: TLinqProjection;
begin
  Result := Prop('MembershipLevel');
end;

function TVUserGroupMembershipDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

function TVUserGroupMembershipDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

{ TVbetriebbezirksgruppeDictionary }

function TVbetriebbezirksgruppeDictionary.Gemeindekennziffer: TLinqProjection;
begin
  Result := Prop('Gemeindekennziffer');
end;

function TVbetriebbezirksgruppeDictionary.Betrieb: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create(PropName('Betrieb'));
end;

function TVbetriebbezirksgruppeDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Gruppe'));
end;

{ TVbetriebsinfoDictionary }

function TVbetriebsinfoDictionary.BetriebsId: TLinqProjection;
begin
  Result := Prop('BetriebsId');
end;

function TVbetriebsinfoDictionary.Regnr: TLinqProjection;
begin
  Result := Prop('Regnr');
end;

function TVbetriebsinfoDictionary.BetriebsdatenId: TLinqProjection;
begin
  Result := Prop('BetriebsdatenId');
end;

function TVbetriebsinfoDictionary.Name: TLinqProjection;
begin
  Result := Prop('Name');
end;

function TVbetriebsinfoDictionary.IdCckAuftrag: TLinqProjection;
begin
  Result := Prop('IdCckAuftrag');
end;

function TVbetriebsinfoDictionary.Betriebstyp: TLinqProjection;
begin
  Result := Prop('Betriebstyp');
end;

function TVbetriebsinfoDictionary.Betriebsart: TLinqProjection;
begin
  Result := Prop('Betriebsart');
end;

function TVbetriebsinfoDictionary.RegnrHauptbetrieb: TLinqProjection;
begin
  Result := Prop('RegnrHauptbetrieb');
end;

function TVbetriebsinfoDictionary.IdHauptbetrieb: TLinqProjection;
begin
  Result := Prop('IdHauptbetrieb');
end;

function TVbetriebsinfoDictionary.Ort: TLinqProjection;
begin
  Result := Prop('Ort');
end;

function TVbetriebsinfoDictionary.Taetigkeiten: TLinqProjection;
begin
  Result := Prop('Taetigkeiten');
end;

function TVbetriebsinfoDictionary.Zulassungsnummern: TLinqProjection;
begin
  Result := Prop('Zulassungsnummern');
end;

{ TVgroupuserDictionary }

function TVgroupuserDictionary.Membershiplevel: TLinqProjection;
begin
  Result := Prop('Membershiplevel');
end;

function TVgroupuserDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('User'));
end;

function TVgroupuserDictionary.GroupUser: IUserDictionary;
begin
  Result := TUserDictionary.Create(PropName('GroupUser'));
end;

function TVgroupuserDictionary.Group: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create(PropName('Group'));
end;

function TVgroupuserDictionary.GroupPerson: IPersonDictionary;
begin
  Result := TPersonDictionary.Create(PropName('GroupPerson'));
end;

function TVgroupuserDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create(PropName('Bundesland'));
end;

{ TZulassungDictionary }

function TZulassungDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TZulassungDictionary.Zulnr: TLinqProjection;
begin
  Result := Prop('Zulnr');
end;

function TZulassungDictionary.Beginndatum: TLinqProjection;
begin
  Result := Prop('Beginndatum');
end;

function TZulassungDictionary.Enddatum: TLinqProjection;
begin
  Result := Prop('Enddatum');
end;

function TZulassungDictionary.Aktiv: TLinqProjection;
begin
  Result := Prop('Aktiv');
end;

function TZulassungDictionary.Sichtbar: TLinqProjection;
begin
  Result := Prop('Sichtbar');
end;

function TZulassungDictionary.Registrierung: IRegistrierungDictionary;
begin
  Result := TRegistrierungDictionary.Create(PropName('Registrierung'));
end;

{ TZusatztextDictionary }

function TZusatztextDictionary.Id: TLinqProjection;
begin
  Result := Prop('Id');
end;

function TZusatztextDictionary.Tabelle: TLinqProjection;
begin
  Result := Prop('Tabelle');
end;

function TZusatztextDictionary.Bezeichnung: TLinqProjection;
begin
  Result := Prop('Bezeichnung');
end;

function TZusatztextDictionary.Langtext: TLinqProjection;
begin
  Result := Prop('Langtext');
end;

{ TvUserKontrollenDictionary }

function TvUserKontrollenDictionary.IdUser: TLinqProjection;
begin
  Result := Prop('IdUser');
end;

function TvUserKontrollenDictionary.Status: TLinqProjection;
begin
  Result := Prop('Status');
end;

function TvUserKontrollenDictionary.Datum: TLinqProjection;
begin
  Result := Prop('Datum');
end;

function TvUserKontrollenDictionary.Endezeit: TLinqProjection;
begin
  Result := Prop('Endezeit');
end;

function TvUserKontrollenDictionary.Faellig: TLinqProjection;
begin
  Result := Prop('Faellig');
end;

function TvUserKontrollenDictionary.IdErfasser: TLinqProjection;
begin
  Result := Prop('IdErfasser');
end;

function TvUserKontrollenDictionary.IdKontrollorgan: TLinqProjection;
begin
  Result := Prop('IdKontrollorgan');
end;

function TvUserKontrollenDictionary.IdGruppeQuelle: TLinqProjection;
begin
  Result := Prop('IdGruppeQuelle');
end;

function TvUserKontrollenDictionary.EffectiveDate: TLinqProjection;
begin
  Result := Prop('EffectiveDate');
end;

function TvUserKontrollenDictionary.GruppeLevel: TLinqProjection;
begin
  Result := Prop('GruppeLevel');
end;

function TvUserKontrollenDictionary.StatusPrio: TLinqProjection;
begin
  Result := Prop('StatusPrio');
end;

function TvUserKontrollenDictionary.Sort: TLinqProjection;
begin
  Result := Prop('Sort');
end;

function TvUserKontrollenDictionary.Kontrolle: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create(PropName('Kontrolle'));
end;

{ TDictionary }

function TDictionary.APTyp: IAPTypDictionary;
begin
  Result := TAPTypDictionary.Create;
end;

function TDictionary.Adresse: IAdresseDictionary;
begin
  Result := TAdresseDictionary.Create;
end;

function TDictionary.Ansprechpartner: IAnsprechpartnerDictionary;
begin
  Result := TAnsprechpartnerDictionary.Create;
end;

function TDictionary.Anwesender: IAnwesenderDictionary;
begin
  Result := TAnwesenderDictionary.Create;
end;

function TDictionary.Betrieb: IBetriebDictionary;
begin
  Result := TBetriebDictionary.Create;
end;

function TDictionary.BetriebRevstamm: IBetriebRevstammDictionary;
begin
  Result := TBetriebRevstammDictionary.Create;
end;

function TDictionary.BetriebeKommunikationswege: IBetriebeKommunikationswegeDictionary;
begin
  Result := TBetriebeKommunikationswegeDictionary.Create;
end;

function TDictionary.BewerteteFrage: IBewerteteFrageDictionary;
begin
  Result := TBewerteteFrageDictionary.Create;
end;

function TDictionary.Bewertung: IBewertungDictionary;
begin
  Result := TBewertungDictionary.Create;
end;

function TDictionary.BewertungsIcon: IBewertungsIconDictionary;
begin
  Result := TBewertungsIconDictionary.Create;
end;

function TDictionary.Bewertungstyp: IBewertungstypDictionary;
begin
  Result := TBewertungstypDictionary.Create;
end;

function TDictionary.BkbTyp: IBkbTypDictionary;
begin
  Result := TBkbTypDictionary.Create;
end;

function TDictionary.Bkbnummer: IBkbnummerDictionary;
begin
  Result := TBkbnummerDictionary.Create;
end;

function TDictionary.BkbtypenRechtsgrundlage: IBkbtypenRechtsgrundlageDictionary;
begin
  Result := TBkbtypenRechtsgrundlageDictionary.Create;
end;

function TDictionary.Bundesland: IBundeslandDictionary;
begin
  Result := TBundeslandDictionary.Create;
end;

function TDictionary.BundeslandChecklistenKontrolltyp: IBundeslandChecklistenKontrolltypDictionary;
begin
  Result := TBundeslandChecklistenKontrolltypDictionary.Create;
end;

function TDictionary.BundeslandModul: IBundeslandModulDictionary;
begin
  Result := TBundeslandModulDictionary.Create;
end;

function TDictionary.CckAuftrag: ICckAuftragDictionary;
begin
  Result := TCckAuftragDictionary.Create;
end;

function TDictionary.CckAuftragsart: ICckAuftragsartDictionary;
begin
  Result := TCckAuftragsartDictionary.Create;
end;

function TDictionary.CckAuftragsbewertung: ICckAuftragsbewertungDictionary;
begin
  Result := TCckAuftragsbewertungDictionary.Create;
end;

function TDictionary.CckAuswahl: ICckAuswahlDictionary;
begin
  Result := TCckAuswahlDictionary.Create;
end;

function TDictionary.CckBetrieb: ICckBetriebDictionary;
begin
  Result := TCckBetriebDictionary.Create;
end;

function TDictionary.CckModul: ICckModulDictionary;
begin
  Result := TCckModulDictionary.Create;
end;

function TDictionary.CckModulAnforderung: ICckModulAnforderungDictionary;
begin
  Result := TCckModulAnforderungDictionary.Create;
end;

function TDictionary.CckModulKontrolltyp: ICckModulKontrolltypDictionary;
begin
  Result := TCckModulKontrolltypDictionary.Create;
end;

function TDictionary.CckStatus: ICckStatusDictionary;
begin
  Result := TCckStatusDictionary.Create;
end;

function TDictionary.CckStatusMeldung: ICckStatusMeldungDictionary;
begin
  Result := TCckStatusMeldungDictionary.Create;
end;

function TDictionary.CckTierdaten: ICckTierdatenDictionary;
begin
  Result := TCckTierdatenDictionary.Create;
end;

function TDictionary.CckVokSanktion: ICckVokSanktionDictionary;
begin
  Result := TCckVokSanktionDictionary.Create;
end;

function TDictionary.Checkliste: IChecklisteDictionary;
begin
  Result := TChecklisteDictionary.Create;
end;

function TDictionary.ChecklistenKontrolltyp: IChecklistenKontrolltypDictionary;
begin
  Result := TChecklistenKontrolltypDictionary.Create;
end;

function TDictionary.Dokument: IDokumentDictionary;
begin
  Result := TDokumentDictionary.Create;
end;

function TDictionary.EmailArten: IEmailArtenDictionary;
begin
  Result := TEmailArtenDictionary.Create;
end;

function TDictionary.EmailHistory: IEmailHistoryDictionary;
begin
  Result := TEmailHistoryDictionary.Create;
end;

function TDictionary.EmailHistoryAttachment: IEmailHistoryAttachmentDictionary;
begin
  Result := TEmailHistoryAttachmentDictionary.Create;
end;

function TDictionary.EmailTexte: IEmailTexteDictionary;
begin
  Result := TEmailTexteDictionary.Create;
end;

function TDictionary.Formatierung: IFormatierungDictionary;
begin
  Result := TFormatierungDictionary.Create;
end;

function TDictionary.Frage: IFrageDictionary;
begin
  Result := TFrageDictionary.Create;
end;

function TDictionary.FrageBewertung: IFrageBewertungDictionary;
begin
  Result := TFrageBewertungDictionary.Create;
end;

function TDictionary.FrageKontrollbereich: IFrageKontrollbereichDictionary;
begin
  Result := TFrageKontrollbereichDictionary.Create;
end;

function TDictionary.Fragengruppe: IFragengruppeDictionary;
begin
  Result := TFragengruppeDictionary.Create;
end;

function TDictionary.Funktion: IFunktionDictionary;
begin
  Result := TFunktionDictionary.Create;
end;

function TDictionary.FunktionRolle: IFunktionRolleDictionary;
begin
  Result := TFunktionRolleDictionary.Create;
end;

function TDictionary.Gemeinde: IGemeindeDictionary;
begin
  Result := TGemeindeDictionary.Create;
end;

function TDictionary.Gruppe: IGruppeDictionary;
begin
  Result := TGruppeDictionary.Create;
end;

function TDictionary.KbProbe: IKbProbeDictionary;
begin
  Result := TKbProbeDictionary.Create;
end;

function TDictionary.Kommunikationsart: IKommunikationsartDictionary;
begin
  Result := TKommunikationsartDictionary.Create;
end;

function TDictionary.Kommunikationsweg: IKommunikationswegDictionary;
begin
  Result := TKommunikationswegDictionary.Create;
end;

function TDictionary.Kontrollbereich: IKontrollbereichDictionary;
begin
  Result := TKontrollbereichDictionary.Create;
end;

function TDictionary.Kontrollbericht: IKontrollberichtDictionary;
begin
  Result := TKontrollberichtDictionary.Create;
end;

function TDictionary.KontrollberichtBild: IKontrollberichtBildDictionary;
begin
  Result := TKontrollberichtBildDictionary.Create;
end;

function TDictionary.KontrollberichtOertlichkeit: IKontrollberichtOertlichkeitDictionary;
begin
  Result := TKontrollberichtOertlichkeitDictionary.Create;
end;

function TDictionary.Kontrolltyp: IKontrolltypDictionary;
begin
  Result := TKontrolltypDictionary.Create;
end;

function TDictionary.KontrolltypReport: IKontrolltypReportDictionary;
begin
  Result := TKontrolltypReportDictionary.Create;
end;

function TDictionary.Land: ILandDictionary;
begin
  Result := TLandDictionary.Create;
end;

function TDictionary.Mangel: IMangelDictionary;
begin
  Result := TMangelDictionary.Create;
end;

function TDictionary.MangelKontrollbereich: IMangelKontrollbereichDictionary;
begin
  Result := TMangelKontrollbereichDictionary.Create;
end;

function TDictionary.MangelOertlichkeit: IMangelOertlichkeitDictionary;
begin
  Result := TMangelOertlichkeitDictionary.Create;
end;

function TDictionary.MangelStatus: IMangelStatusDictionary;
begin
  Result := TMangelStatusDictionary.Create;
end;

function TDictionary.Mangeltyp: IMangeltypDictionary;
begin
  Result := TMangeltypDictionary.Create;
end;

function TDictionary.Massnahme: IMassnahmeDictionary;
begin
  Result := TMassnahmeDictionary.Create;
end;

function TDictionary.Massnahmenkatalog: IMassnahmenkatalogDictionary;
begin
  Result := TMassnahmenkatalogDictionary.Create;
end;

function TDictionary.Modul: IModulDictionary;
begin
  Result := TModulDictionary.Create;
end;

function TDictionary.ModulInstanz: IModulInstanzDictionary;
begin
  Result := TModulInstanzDictionary.Create;
end;

function TDictionary.ModuleBkbtypen: IModuleBkbtypenDictionary;
begin
  Result := TModuleBkbtypenDictionary.Create;
end;

function TDictionary.Nachricht: INachrichtDictionary;
begin
  Result := TNachrichtDictionary.Create;
end;

function TDictionary.NachrichtenZustellung: INachrichtenZustellungDictionary;
begin
  Result := TNachrichtenZustellungDictionary.Create;
end;

function TDictionary.Person: IPersonDictionary;
begin
  Result := TPersonDictionary.Create;
end;

function TDictionary.PersonenKommunikationswege: IPersonenKommunikationswegeDictionary;
begin
  Result := TPersonenKommunikationswegeDictionary.Create;
end;

function TDictionary.ProgrammModul: IProgrammModulDictionary;
begin
  Result := TProgrammModulDictionary.Create;
end;

function TDictionary.Rechtsgrundlage: IRechtsgrundlageDictionary;
begin
  Result := TRechtsgrundlageDictionary.Create;
end;

function TDictionary.Registrierung: IRegistrierungDictionary;
begin
  Result := TRegistrierungDictionary.Create;
end;

function TDictionary.Report: IReportDictionary;
begin
  Result := TReportDictionary.Create;
end;

function TDictionary.ReportTyp: IReportTypDictionary;
begin
  Result := TReportTypDictionary.Create;
end;

function TDictionary.RevisionsSchema: IRevisionsSchemaDictionary;
begin
  Result := TRevisionsSchemaDictionary.Create;
end;

function TDictionary.Revisionsplan: IRevisionsplanDictionary;
begin
  Result := TRevisionsplanDictionary.Create;
end;

function TDictionary.Revisionsstamm: IRevisionsstammDictionary;
begin
  Result := TRevisionsstammDictionary.Create;
end;

function TDictionary.Rolle: IRolleDictionary;
begin
  Result := TRolleDictionary.Create;
end;

function TDictionary.RollenBkbtypen: IRollenBkbtypenDictionary;
begin
  Result := TRollenBkbtypenDictionary.Create;
end;

function TDictionary.Sys: ISysDictionary;
begin
  Result := TSysDictionary.Create;
end;

function TDictionary.Todo: ITodoDictionary;
begin
  Result := TTodoDictionary.Create;
end;

function TDictionary.Unterschrift: IUnterschriftDictionary;
begin
  Result := TUnterschriftDictionary.Create;
end;

function TDictionary.User: IUserDictionary;
begin
  Result := TUserDictionary.Create;
end;

function TDictionary.Usergruppe: IUsergruppeDictionary;
begin
  Result := TUsergruppeDictionary.Create;
end;

function TDictionary.Userrolle: IUserrolleDictionary;
begin
  Result := TUserrolleDictionary.Create;
end;

function TDictionary.VNachrichtForUser: IVNachrichtForUserDictionary;
begin
  Result := TVNachrichtForUserDictionary.Create;
end;

function TDictionary.VUserGroupMembership: IVUserGroupMembershipDictionary;
begin
  Result := TVUserGroupMembershipDictionary.Create;
end;

function TDictionary.Vbetriebbezirksgruppe: IVbetriebbezirksgruppeDictionary;
begin
  Result := TVbetriebbezirksgruppeDictionary.Create;
end;

function TDictionary.Vbetriebsinfo: IVbetriebsinfoDictionary;
begin
  Result := TVbetriebsinfoDictionary.Create;
end;

function TDictionary.Vgroupuser: IVgroupuserDictionary;
begin
  Result := TVgroupuserDictionary.Create;
end;

function TDictionary.Zulassung: IZulassungDictionary;
begin
  Result := TZulassungDictionary.Create;
end;

function TDictionary.Zusatztext: IZusatztextDictionary;
begin
  Result := TZusatztextDictionary.Create;
end;

function TDictionary.vUserKontrollen: IvUserKontrollenDictionary;
begin
  Result := TvUserKontrollenDictionary.Create;
end;

end.
