package ElkeClasses;

{$R *.res}
{$IFDEF IMPLICITBUILDING This IFDEF should not be used by users}
{$ALIGN 8}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION OFF}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES ON}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DEFINE DEBUG}
{$ENDIF IMPLICITBUILDING}
{$LIBSUFFIX '260'}
{$IMPLICITBUILD ON}

requires
  rtl,
  dbrtl,
  tmsbcl,
  aurelius,
  xdata,
  vcl,
  DbxCommonDriver,
  FireDAC,
  FireDACSqliteDriver,
  FireDACCommonDriver,
  FireDACCommon,
  vclFireDAC,
  FireDACIBDriver;

contains
  ELKE.Classes.Request in 'ELKE.Classes.Request.pas',
  ELKE.Classes.PVP.Token in 'ELKE.Classes.PVP.Token.pas',
  ELKE.Classes.PVP.Roles in 'ELKE.Classes.PVP.Roles.pas',
  ELKE.Classes in 'ELKE.Classes.pas',
  ELKE.Classes.Logging in 'ELKE.Classes.Logging.pas',
  ELKE.Classes.Generated in 'ELKE.Classes.Generated.pas',
  ELKE.Classes.Admin in 'ELKE.Classes.Admin.pas',
  ELKE.Classes.Generated.Dictionary in 'ELKE.Classes.Generated.Dictionary.pas';

end.
