object ServerContainerBase: TServerContainerBase
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 323
  Width = 490
  object SparkleHttpSysDispatcher: TSparkleHttpSysDispatcher
    Left = 72
    Top = 16
  end
  object XDataServer: TXDataServer
    BaseUrl = 'http://+:80/ELKE/API'
    Dispatcher = SparkleHttpSysDispatcher
    Pool = XDataConnectionPool
    DefaultExpandLevel = 1
    FlushMode = All
    EntitySetPermissions = <>
    SwaggerUIOptions.ShowFilter = True
    SwaggerUIOptions.CustomParams.Strings = (
      'defaultModelsExpandDepth=-1')
    OnModuleCreate = XDataServerModuleCreate
    OnModuleException = XDataServerModuleException
    Left = 216
    Top = 16
  end
  object XDataConnectionPool: TXDataConnectionPool
    Connection = AureliusConnection
    Left = 216
    Top = 72
  end
  object AureliusConnection: TAureliusConnection
    AdapterName = 'FireDac'
    AdaptedConnection = DBConnection
    SQLDialect = 'MSSQL'
    Left = 216
    Top = 128
  end
  object DBConnection: TFDConnection
    Params.Strings = (
      'Database=ELKEDB_ENTW'
      'ApplicationName=ELKEServer'
      'OSAuthent=No'
      'Server=10.10.0.67'
      'User_Name=sa'
      'Password=ESA1234!'
      'DriverID=MSSQL')
    FetchOptions.AssignedValues = [evMode, evItems]
    FetchOptions.Mode = fmAll
    FetchOptions.Items = [fiBlobs, fiDetails]
    ResourceOptions.AssignedValues = [rvAutoReconnect]
    ResourceOptions.AutoReconnect = True
    TxOptions.Isolation = xiSnapshot
    ConnectedStoredUsage = []
    LoginPrompt = False
    Left = 216
    Top = 200
  end
  object IntrawebServer: TSparkleStaticServer
    BaseUrl = 'http://+:80/ELKE/API/IW'
    Dispatcher = SparkleHttpSysDispatcher
    Left = 384
    Top = 24
  end
end
