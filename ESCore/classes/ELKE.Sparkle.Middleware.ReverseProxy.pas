﻿unit ELKE.Sparkle.Middleware.ReverseProxy;

interface

uses
  System.Classes, System.SysUtils,
  Sparkle.HttpServer.Module, Sparkle.HttpServer.Context, Sparkle.Uri;

type

  /// <summary>
  /// <para>
  /// TReverseProxyMiddleware implements specific header processing for
  /// applications behind a reverse proxy. If no reverse proxy is found,
  /// behaviour is not changed. Additionally the following is implemented
  /// here:
  /// </para>
  /// <para>
  /// - Access-Control-Expose-Headers <br />- Request Logging
  /// </para>
  /// </summary>
  TReverseProxyMiddleware = class(THttpServerMiddleware, IHttpServerMiddleware)

  protected
    procedure ProcessRequest(
      Context: THttpServerContext;
      Next: THttpServerProc); override;
  public
    class function GetProcessedURI(ARequest: THttpServerRequest): string;

  end;

  TRequestInfo = record
    OriginalUri: TUri;
    PVPOrigUri: TUri;
    TranslatedUri: string;
    TransactionID: string;
    UserEmail: string;
    function Session: string;
  end;

threadvar
  GCurrentRequest: TRequestInfo;

implementation

uses
  Sparkle.Http.Headers,
  XData.Server.Module,
  ELKE.Server.Logger, ELKE.Classes.Logging;

{ TReverseProxyMiddleware }

class function TReverseProxyMiddleware.GetProcessedURI(ARequest: THttpServerRequest): string;
var
  LHost: string;
  LHostAlternative: string;
  LFor: string;
  LScheme: string;
  LQuery: string;
  LPath: string;
  LHeaders: THttpHeaders;
  LOrigPath: string;
begin
  // Find relevant proxy headers

  // nginx
  // X-Real-IP         $remote_addr;
  // X-Forwarded-For   $proxy_add_x_forwarded_for;
  // X-Forwarded-Proto $scheme;
  // X-Forwarded-Host  $host;
  // X-Forwarded-Port  $server_port;

  // apache
  // X-Forwarded-For : The IP address of the client.
  // X-Forwarded-Host : The original host requested by the client in the Host HTTP request header.
  // X-Forwarded-Server : The hostname of the proxy server.

  // Alternative
  // X-Forward-Proto
  // X-Forward-For
  // X-Forward-Host

  // Todo: Konfigurierbar machen
  // if not ARequest.Uri.Path.Contains('/admin/') then
  // begin
  // ELKELog('Proxy processing before: ' + ARequest.Method + ' : ' + ARequest.RawUri, TLogLevel.Trace);
  // end;

  LHeaders := ARequest.Headers;
  LScheme := ARequest.Uri.Scheme;
  LHost := ARequest.Uri.Host;
  LHostAlternative := '';
  // LBasePath := TXDataOperationContext.Current.Handler.XModule.BaseUri.Path;
  LPath := ARequest.Uri.Path;
  // LRelativePath := LPath.ToLower.Replace(LBasePath.ToLower, '');
  LQuery := ARequest.Uri.OriginalQuery;

  ARequest.Headers.GetIfExists('X-Forwarded-Host', LHost);
  ARequest.Headers.GetIfExists('X-Forward-Host', LHostAlternative);
  // nginx/Apache
  ARequest.Headers.GetIfExists('X-Forwarded-For', LFor);
  ARequest.Headers.GetIfExists('X-Forwarded-Proto', LScheme);
  if LHostAlternative > '' then
  begin
    LHost := LHostAlternative;
    ARequest.Headers.GetIfExists('X-Forward-For', LFor);
    ARequest.Headers.GetIfExists('X-Forward-Proto', LScheme);
  end;

  LHeaders.GetIfExists('X-PVP-ORIG-SCHEME', LScheme);
  LHeaders.GetIfExists('X-PVP-ORIG-HOST', LHost);

  // PVP specific header, that holds the original path as issued by client
  LOrigPath := '';
  LHeaders.GetIfExists('X-PVP-ORIG-URI', LOrigPath);

  /// LOrigPath := '/rest/ELKE/v1/openapi/swagger.json';

  // make sure LOrigPath is "clean"
  if LOrigPath > '' then
  begin
    LOrigPath := LOrigPath.Trim;
    if not LOrigPath.StartsWith('/') then
    begin
      LOrigPath := '/' + LOrigPath;
    end;
    // Todo: Konfigurierbar machen
    // if not ARequest.Uri.Path.Contains('/admin/') then
    // begin
    // ELKELog('X-PVP-ORIG-URI: ' + LOrigPath, TLogLevel.Trace);
    // end;
    LPath := LOrigPath;
  end;

  result := Format('%s://%s%s%s', [LScheme, LHost, LPath, LQuery]);

  // Todo: Konfigurierbar machen
  // if not ARequest.Uri.Path.Contains('/admin/') then
  // begin
  // ELKELog('Proxy processing after: ' + ARequest.Method + ' : ' + result, TLogLevel.Trace);
  // end;
end;

procedure TReverseProxyMiddleware.ProcessRequest(
  Context: THttpServerContext;
  Next: THttpServerProc);
var
  LRequestLog: TLogItemRequest;
  LUri: string;
begin

  GCurrentRequest.OriginalUri := Context.Request.Uri.Clone;
  GCurrentRequest.TranslatedUri := GetProcessedURI(Context.Request);
  LUri := GCurrentRequest.OriginalUri.OriginalUri;
  Context.Request.Headers.GetIfExists('X-PVP-ORIG-URI', LUri);
  GCurrentRequest.PVPOrigUri := TUri.Create(LUri);

  GCurrentRequest.TransactionID := 'unknown';
  GCurrentRequest.UserEmail := 'unknown';

{$IFDEF DEBUG}
  GCurrentRequest.TransactionID := 'DEBUG T' + IntToStr(TThread.Current.ThreadID);
{$ENDIF}
  Context.Request.Headers.GetIfExists('X-PVP-TXID', GCurrentRequest.TransactionID);
  GCurrentRequest.TransactionID := GCurrentRequest.TransactionID.Trim;
  Context.Request.Headers.GetIfExists('X-PVP-MAIL', GCurrentRequest.UserEmail);

  // *** Logging
  // die Admin-Endpunkte loggen wir nicht. Insbesondere die ECHO-Abfragen müllen den Log zu.
  // Todo: Config-Parameter für Admin-Logs
  if not Context.Request.Uri.Path.Contains('/admin/') then
  begin
    // ELKELog('Processing incoming request ...', TLogLevel.Trace);
    // Den Request so loggen, wie er original aussieht
    LRequestLog := TLogItemRequest.Create(Context.Request);
    ELKELog(LRequestLog);
  end;

  // Access-Control-Expose-Headers
  // Certain "x-..." Headers are used for information like paging.
  // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose-Headers
  Context.Response.Headers.SetValue('Access-Control-Expose-Headers',
    'X-Records-Total, X-Records-Page, X-Records-Page-Size, X-Records, X-Records-Page-Max');

  Next(Context);

  // if the module doesn't close the request, we should do it now, to force OnHeaders
  // to be called.
  if not Context.Response.Closed then
    Context.Response.Close;
  // Todo: Komplette Response loggen
  if not Context.Request.Uri.Path.Contains('/admin/') then
  begin
    ELKELog('Response sent: ' + Context.Response.StatusCode.ToString
      + ' [' + (Context.Response.Content.Size div 1024).ToString + ' kb]', TLogLevel.Info);
  end;
  FreeAndNil(GCurrentRequest.OriginalUri);
  FreeAndNil(GCurrentRequest.PVPOrigUri);
  GCurrentRequest.TranslatedUri := '';
  GCurrentRequest.TransactionID := '';
end;

{ TRequestInfo }

function TRequestInfo.Session: string;
begin
  if (TransactionID > '') or (UserEmail > '') then
  begin
    result := '[' + TransactionID + ' / ' + UserEmail + '] ';
  end
  else
  begin
    result := '';
  end;
end;

end.

