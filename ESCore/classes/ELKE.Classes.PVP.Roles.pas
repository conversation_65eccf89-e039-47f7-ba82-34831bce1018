unit ELKE.Classes.PVP.Roles;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies, Aurelius.Types.Nullable;

type
  TPVPRoles = class;

  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TParameter = class(TObject)
  private
    FWert: string;
    FTyp: string;
  public
    property Typ: string read FTyp write FTyp;
    property Wert: string read FWert write FWert;
  end;

  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TPVPRole = class(TObject)
  private
    FName: string;
    FParameter: TParameter;
  public
  public
    constructor Create(const ADefinition: string);
    destructor Destroy; override;
    property Name: string read FName write FName;
    property Parameter: TParameter read FParameter write FParameter;
  end;

  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TPVPRoles = class(TObjectList<TPVPRole>)
  public
    class function Parse(AHeader: Nullable<string>): TPVPRoles;
  end;

implementation

{ TRolle }

constructor TPVPRole.Create(const ADefinition: string);
var
  LParts: TArray<string>;
  LGebiet: string;
begin
  FParameter := TParameter.Create;
  // ADefinition = 'ELKE-Kontrolle(OKZ=GGA-30201)'
  LParts := ADefinition.Split(['(', ')']);
  if LParts[0].Trim > '' then
  begin
    FName := LParts[0].Trim;
    if LParts[1].Trim > '' then
    begin
      LGebiet := LParts[1].Trim;
      // LGebiet = 'OKZ=GGA-30201'
      LParts := LGebiet.Split(['=']);
      Parameter.Typ := LParts[0].Trim;
      Parameter.Wert := LParts[1].Trim;
    end;
  end;
end;

destructor TPVPRole.Destroy;
begin
  FreeAndNil(FParameter);
  inherited;
end;

class function TPVPRoles.Parse(AHeader: Nullable<string>): TPVPRoles;
var
  LParts: TArray<string>;
  s: string;
  LRole: TPVPRole;
begin
  result := TPVPRoles.Create;
  if AHeader.HasValue then
  begin
    // AHeader = 'ELKE-Kontrolle(OKZ=GGA-30201);ELKE-Kontrollplanung(OKZ=GGA-30201)'
    LParts := AHeader.Value.Split([';']);
    for s in LParts do
    begin
      LRole := TPVPRole.Create(s);
      if LRole.Name > '' then
      begin
        result.Add(LRole);
      end
      else
      begin
        FreeAndNil(LRole);
      end;
    end;
  end;
end;

end.
