unit ELKE.Service;

interface

uses
  System.SysUtils, System.Classes,
  Vcl.SvcMgr,
  DX.Classes.Service,
  ELKE.Server.Logger,
  ELKE.Server.Modules.ServerContainer.Base;

type
  TELKEService = class(TServiceBase)
    procedure ServiceStart(Sender: TService; var Started: Boolean);
    procedure ServiceStop(Sender: TService; var Stopped: Boolean);
  private
    FServerContainer: TServerContainerBase;
  public
    class procedure Run(AServerContainerClass: TServerContainerClass);
  end;

implementation

{$R *.DFM}

procedure TELKEService.ServiceStop(Sender: TService; var Stopped: Boolean);
begin
  inherited;
  FServerContainer.StopServer;
end;

class procedure TELKEService.Run(AServerContainerClass: TServerContainerClass);
begin
  inherited Run(procedure(AInstance:TServiceBase)
    begin
      Application.CreateForm(AServerContainerClass,  TElkeService(AInstance).FServerContainer);
    end);
end;

procedure TELKEService.ServiceStart(Sender: TService; var Started: Boolean);
begin
  inherited;
  FServerContainer.StartServer;
end;

end.

