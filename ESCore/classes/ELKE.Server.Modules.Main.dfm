object DMMain: TDMMain
  OldCreateOrder = False
  Height = 361
  Width = 454
  object QNachrichtenZustellungenCreate: TFDQuery
    Connection = ServerContainerBase.DBConnection
    SQL.Strings = (
      'insert into BEWEGUNGSDATEN.NACHRICHTEN_ZUSTELLUNG'
      '    (ID_NACHRICHT, ID_USER)'
      ''
      '    select vNU.ID_NACHRICHT, vNU.ID_USER'
      '    from BEWEGUNGSDATEN.vNACHRICHTEN_USER vNU'
      '    where ID_USER = :id_user'
      '      and ID_NACHRICHTEN_ZUSTELLUNG is null')
    Left = 136
    Top = 56
    ParamData = <
      item
        Name = 'ID_USER'
        DataType = ftInteger
        ParamType = ptInput
        Value = 464
      end>
  end
  object QNachrichtenMarkGesehenForUser: TFDQuery
    Connection = ServerContainerBase.DBConnection
    SQL.Strings = (
      
        '--   Alle Nachrichten des Users als gsehen markieren, die noch n' +
        'icht gesehen sind'
      'update BEWEGUNGSDATEN.NACHRICHTEN_ZUSTELLUNG'
      'set GESEHEN_TSTAMP = :currTime'
      'where GESEH<PERSON>_TSTAMP is null  and ID_USER = :id_user')
    Left = 136
    Top = 136
    ParamData = <
      item
        Name = 'CURRTIME'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER'
        DataType = ftInteger
        ParamType = ptInput
        Value = 464
      end>
  end
end
