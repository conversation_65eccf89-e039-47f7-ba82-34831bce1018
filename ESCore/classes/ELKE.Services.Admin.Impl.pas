unit ELKE.Services.Admin.Impl;

interface

uses
  System.Classes, System.SysUtils,
  XData.Server.Module, XData.Service.Common,
  ELKE.Classes, ELKE.Services.Admin.Intf, ELKE.Classes.Admin, Sparkle.Http.Headers, System.Generics.Collections;

type

  [ServiceImplementation]
  TAdmin = class(TInterfacedObject, IAdmin)
  public
    [HttpGet]
    function EchoHeaders: TStringlist;

    [HttpGet]
    function EchoString(Value: string): string;

     [HttpPost]
     function EchoPost(Value: string): string;

    [HttpGet]
    function EchoError(
      StatusCode:   integer;
      ErrorMessage: string): string;

    [HttpGet]
    function Version: TVersionInfo;

  end;

implementation

uses
  Aurelius.Linq, Aurelius.Engine.ObjectManager,
  XData.Sys.Exceptions,
  DX.Utils.Windows, ELKE.Sparkle.Middleware.ReverseProxy, ELKE.Classes.Generated.Dictionary;

function TAdmin.EchoString(Value: string): string;
begin
  Result := Value;
end;

 function TAdmin.EchoPost(Value: string): string;
 begin
 Result := Value;
 end;

function TAdmin.EchoError(
  StatusCode:   integer;
  ErrorMessage: string): string;
begin
  raise EXDataHttpException.Create(StatusCode, ErrorMessage);
end;

function TAdmin.EchoHeaders: TStringlist;
var
  LHeader: THttpHeaderInfo;
begin
  Result := TStringlist.Create;
  Result.TrailingLineBreak := false;
  for LHeader in TXDataOperationContext.Current.Request.Headers.AllHeaders do
  begin
    Result.Add(LHeader.Name + ' : ' + LHeader.Value);
  end;
end;

function TAdmin.Version: TVersionInfo;
begin
  Result := TVersionInfo.Create;
end;

initialization

RegisterServiceType(TAdmin);

end.
