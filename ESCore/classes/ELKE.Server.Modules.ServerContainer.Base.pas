﻿unit ELKE.Server.Modules.ServerContainer.Base;

interface

uses
  System.SysUtils, System.Classes,
  Data.DB,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.MSSQL,
  FireDAC.Phys.MSSQLDef,

  Sparkle.HttpServer.Module, Sparkle.HttpServer.Context, Sparkle.Comp.Server, Sparkle.Comp.HttpSysDispatcher,
  Sparkle.Http.Headers, Sparkle.Uri,

  Aurelius.Sql.MSSQL, Aurelius.Schema.MSSQL, Aurelius.Drivers.Interfaces, Aurelius.Comp.Connection,
  Aurelius.Sql.Register, Aurelius.Drivers.FireDAC, Aurelius.Engine.DatabaseManager, Aurelius.Engine.ObjectManager,

  XData.Module.Events, XData.Comp.ConnectionPool, XData.Comp.Server, XData.Server.Module, FireDAC.Stan.Param,
  FireDAC.DatS, FireDAC.DApt.Intf, FireDAC.DApt, FireDAC.Comp.DataSet, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef,
  FireDAC.Stan.ExprFuncs, Sparkle.Module.Static, Sparkle.Comp.StaticServer;

type
  TServerContainerBase = class(TDataModule)
    SparkleHttpSysDispatcher: TSparkleHttpSysDispatcher;
    XDataServer: TXDataServer;
    XDataConnectionPool: TXDataConnectionPool;
    AureliusConnection: TAureliusConnection;
    DBConnection: TFDConnection;
    IntrawebServer: TSparkleStaticServer;
    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
    procedure XDataServerModuleCreate(
      Sender: TObject;
      Module: TXDataServerModule);
    procedure XDataServerModuleException(
      Sender: TObject;
      Args: TModuleExceptionArgs);
  private
    function GetBaseURLIntraweb: string;
  protected
    function GetBaseURL: string; virtual;
    function GetBaseURLSwagger: string; virtual;
    procedure ReadConfiguration; virtual;
    procedure SetPermissions(
      AModule: TXDataServerModule;
      AEntity: TClass;
      APermissions: TEntitySetPermissions); virtual;
    procedure GetAbsoluteUrl(
      const Schema, Authority, Path, RelativeUrl: string;
      var Url: string);
  public
    procedure DeleteDatabaseLOG; virtual;
    function GetContext: TObjectManager; virtual;
    procedure StartServer; virtual;
    procedure StopServer; virtual;
    procedure UpdateDatabaseLOG;
    property BaseURL: string read GetBaseURL;
    property BaseURLSwagger: string read GetBaseURLSwagger;
    property BaseURLIntraweb: string read GetBaseURLIntraweb;
  end;

  TServerContainerClass = class of TServerContainerBase;

implementation

uses
  Sparkle.Middleware.Compress, Sparkle.Middleware.Logging,
  Aurelius.Schema.Messages, Aurelius.Mapping.Explorer,
  ELKE.Sparkle.Middleware.ReverseProxy, DX.Data.Utils,
  ELKE.Server.Configuration.Base, ELKE.Server.Logger, ELKE.Classes.Logging, DX.SysUtils, DX.Utils.Windows,
  System.IOUtils, DX.Utils.Logger, XData.Utils, System.JSON, XData.Aurelius.ModelBuilder, Aurelius.Global.Config,
  ELKE.Classes.RESTError, ELKE.Sparkle.Middleware.PVPAuth, ELKE.Classes.PVP.Token, Aurelius.Sql.SQLite,
  ELKE.Sparkle.Middleware.TokenAuthGenEndpoints;

{%CLASSGROUP 'Vcl.Controls.TControl'}
{$R *.dfm}

procedure TServerContainerBase.DataModuleCreate(Sender: TObject);
begin
  ELKELog(GetExeVersionData.ProductName);
  ELKELog('Version ' + GetExeVersion);
  // Todo: Formatsettings ggfs als Config anbieten
  Formatsettings := TFormatSettings.Create('de-at');
  var
  LDocDir := Tpath.Combine(Tpath.GetLibraryPath, 'XMLDoc');
  LDocDir := Tpath.Combine(LDocDir, GetExeVersionData.InternalName);
  if not TDirectory.Exists(LDocDir) then
  begin
    ELKELog('XMLDoc existiert nicht: ' + LDocDir);
    TDirectory.CreateDirectory(LDocDir);
  end;
  TXDataModelBuilder.LoadXMLDoc(XDataServer.Model, LDocDir);
  XDataServer.Model.Version := GetExeVersionData.ProductVersion;

  // Todo: Logging für unabhängige Module konfigurieren
  // TModuleReports.LogProc := procedure(AMessage: string)
  // begin
  // ELKELog(AMessage);
  // end;
end;

procedure TServerContainerBase.DataModuleDestroy(Sender: TObject);
begin
  StopServer;
end;

procedure TServerContainerBase.DeleteDatabaseLOG;
var
  LConnection: IDBConnection;
  LStatement: IDBStatement;
  LSQL: string;
begin
  if TConfigurationBase.Default.LogToSQLite then
  begin
    // Todo: SQLite implementieren
  end
  else
  begin
    LSQL := 'EXEC sp_msforeachtable ''ALTER TABLE ? NOCHECK CONSTRAINT all'';';
    LSQL := LSQL + 'drop table logging.pvp_token;';
    LSQL := LSQL + 'drop table logging.REQUEST_HEADERS;';
    LSQL := LSQL + 'drop table LOGGING.REQUESTS;';
    LSQL := LSQL + 'drop table LOGGING.MESSAGES;';
    LSQL := LSQL + 'drop table LOGGING.LOG;';

    LSQL := LSQL + 'EXEC sp_msforeachtable ''ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all'';';
    LConnection := TFireDacConnectionAdapter.Create(DBConnection, false);
    LStatement := LConnection.CreateStatement;
    LStatement.SetSQLCommand(LSQL);
    LStatement.Execute;
  end;
end;

procedure TServerContainerBase.GetAbsoluteUrl(
  const Schema, Authority, Path, RelativeUrl: string;
  var Url: string);
var
  LProcessedUri: string;

begin
  // Assumption: This is called from within a swaggerui request

  // Path is the original path of the BaseUrl e.g. /ELKE/v1
  // RelativeURL is the specific swagger document, which will be inserted into the result.
  // LProcessedUri is the full URI of the current, translated request
  // e.g.:  https://example.com/rest/v1/swaggerui translated from http://10.0.0.1/Elke/v1/swaggerui

  LProcessedUri := GCurrentRequest.TranslatedUri;
  // remove the relative path from the originating call
  LProcessedUri := LProcessedUri.ToLower.Replace('/swaggerui', '').Replace('/openapi/swagger.json', '');
  // remove possible query part
  LProcessedUri := LProcessedUri.ToLower.Replace(TXDataOperationContext.Current.Request.Uri.Query.ToLower, '');
  // append the new relative path
  LProcessedUri := TXDataUtils.CombineUrlFast(LProcessedUri, RelativeUrl);
  Url := LProcessedUri;
  ELKELog('Swagger URL: ' + Url, TLogLevel.Trace);
end;

function TServerContainerBase.GetBaseURL: string;
const
  WILDCARD_URL = 'http://+';
  LOCAL_URL = 'http://127.0.0.1';
begin
  Result := StringReplace(XDataServer.BaseURL, WILDCARD_URL, LOCAL_URL, [rfIgnoreCase])
end;

function TServerContainerBase.GetBaseURLIntraweb: string;
begin
  Result := BaseURL + '/Intraweb';
end;

function TServerContainerBase.GetBaseURLSwagger: string;
begin
  Result := BaseURL + '/SwaggerUI';
end;

function TServerContainerBase.GetContext: TObjectManager;
var
  LConnection: IDBConnection;
begin
  LConnection := TFireDacConnectionAdapter.Create(DBConnection, false);
  Result := TObjectManager.Create(LConnection);
end;

{ TServerContainerBase }

procedure TServerContainerBase.ReadConfiguration;

begin
  ELKELog('Reading configuration ...');
  var LBaseUrl: IUri := TUri.Create(TConfigurationBase.Default.BaseURL);
  XDataServer.BaseURL := LBaseUrl.AbsoluteUri;
  ELKELog('Base URL RestAPI: ' + XDataServer.BaseURL);

  LBaseUrl := TUri.Create(LBaseUrl.AbsoluteUri + '/intraweb');
  IntrawebServer.BaseURL := LBaseUrl.AbsoluteUri;
  ELKELog('Base URL Intraweb : ' + IntrawebServer.BaseURL);
  IntrawebServer.RootDir := Tpath.Combine(Tpath.GetLibraryPath, 'intraweb');
  if not TFile.Exists(IntrawebServer.RootDir) then
  begin
    try
      TDirectory.CreateDirectory(IntrawebServer.RootDir);
    except
      on E: Exception do
      begin
        ELKELog('Error: Cannot create ' + IntrawebServer.RootDir + ' ' + E.Message);
      end;
    end;
  end;

  ELKELog('DB connection string: ' + TConfigurationBase.Default.DBConnectionString);
  ELKELog('DB connection PoolSize: ' + TConfigurationBase.Default.PoolSize.ToString);
  DBConnection.SetParamsFromConnectionString(TConfigurationBase.Default.DBConnectionString);
  var
  LDbInfo := TStringList.Create;
  try
    DBConnection.GetInfoReport(LDbInfo);
    ELKELog(LDbInfo, TLogLevel.Trace);
  finally
    FreeAndNil(LDbInfo);
  end;
  if TConfigurationBase.Default.LogToSQLite then
  begin
    TElkeLogger.Default.LogToSQLite := true;
  end
  else
  begin
    TElkeLogger.Default.LogToSQLite := false;
    TElkeLogger.Default.ConnectionString := TConfigurationBase.Default.DBConnectionString;
  end;
  XDataConnectionPool.Size := TConfigurationBase.Default.PoolSize;

  XDataServer.SwaggerOptions.Enabled := TConfigurationBase.Default.SwaggerEnabled;
  XDataServer.SwaggerUIOptions.Enabled := TConfigurationBase.Default.SwaggerEnabled;
end;

procedure TServerContainerBase.SetPermissions(
  AModule: TXDataServerModule;
  AEntity: TClass;
  APermissions: TEntitySetPermissions);
var
  LEntitySetName: string;
begin
  ELKELog('Set Permissions for ' + AEntity.ClassName);
  LEntitySetName := AEntity.ClassName;
  if LEntitySetName.ToUpper.StartsWith('T') then
  begin
    LEntitySetName := LEntitySetName.Remove(0, 1);
  end;
  AModule.SetEntitySetPermissions(LEntitySetName, APermissions);
end;

procedure TServerContainerBase.StartServer;
begin
  try
    ReadConfiguration;

    UpdateDatabaseLOG;
    TElkeLogger.Default.DBLogActive := true;

    ELKELog('Starting server ...');
    SparkleHttpSysDispatcher.Start;
    ELKELog('Server started!');
  except
    on E: Exception do
    begin
      raise Exception.Create('Run server as Administrator or register URL first!'#13#10 + E.Message);
    end;
  end;
end;

procedure TServerContainerBase.StopServer;
begin
  ELKELog('Stopping server ...');
  SparkleHttpSysDispatcher.Stop;
  DBConnection.Connected := false;
  ELKELog('Server stopped!');
  TElkeLogger.Default.DBLogActive := false;
end;

procedure TServerContainerBase.UpdateDatabaseLOG;
begin
  try
    if TConfigurationBase.Default.LogToSQLite then
    begin
      (TSQLGeneratorRegister.GetInstance.GetGenerator('SQLite') as TSQLiteSQLGenerator).DateType :=
        TSQLiteSQLGenerator.TDateType.Text;
    end
    else
    begin
      // Aurelius Optionen für MSSQL setzen
      (TSQLGeneratorRegister.GetInstance.GetGenerator('MSSQL') as TMSSQLSQLGenerator).UseBoolean := true;
    end;
    var LContext: TObjectManager := nil;
    var LDBManager: TDatabaseManager := nil;
    try
      LContext := TElkeLogger.Default.GetContext;
      LDBManager := TDatabaseManager.Create(LContext.Connection, TMappingExplorer.Get('Logging'));

      ELKELog('Checking log database...');
      LDBManager.UpdateDatabase;
      TElkeLogger.Default.DBLogActive := true;
      ELKELog('Log database ready!');
    finally
      FreeAndNil(LDBManager);
      FreeAndNil(LContext);
    end;
  except
    on E: Exception do
    begin
      raise Exception.Create('Aurelius faild to create/update log database' + #13#10 + E.Message);
    end;
  end;
end;

procedure TServerContainerBase.XDataServerModuleCreate(
  Sender: TObject;
  Module: TXDataServerModule);
begin

  (TSQLGeneratorRegister.GetInstance.GetGenerator('MSSQL') as TMSSQLSQLGenerator).UseBoolean := true;

  TGlobalConfigs.GetInstance.UseTransactionsInManager := false;
  TGlobalConfigs.GetInstance.MaxEagerFetchDepth := 4;

  // Kompression aktivieren
  Module.AddMiddleware(TCompressMiddleware.Create); // Zuerst die Kompression

  // Logging aktivieren
  // Module.AddMiddleware(TElkeLogger.CreateLoggingModule);  //Logging als zweites, um den original Request zu sehen

  // Proxy-Handling
  Module.AddMiddleware(TReverseProxyMiddleware.Create); // Reverse Proxy Verarbeitung zum Schluss

  // Token-Authentifizierung für generische Endpunkte
  Module.AddMiddleware(TTokenAuthGenEndpointsMiddleware.Create('011A280E-A198-40E3-9824-EF17FE90DFB2'));

  // PVPToken Authentifizierung
  Module.AddMiddleware(TPVPAuthMiddleware.Create);

  // CORS aktivieren
  // Todo: Per INI konfogurierbar machen?
  // https://download.tmssoftware.com/business/xdata/doc/web/txdataservermodule.html?zoom_highlightsub=AccessControlAllowOrigin
  Module.AccessControlAllowOrigin := '*';

  // Zus�tzlichen Abfrage-Pfad f�r IDs setzen:  "/entityset/id"
  Module.EnableEntityKeyAsSegment := true;

  // Die AbsoluteURL wird bei der Konstruktion der SwaggerUI f�r die internen Links ben�tigt
  Module.OnGetAbsoluteUrl := GetAbsoluteUrl;

end;

procedure TServerContainerBase.XDataServerModuleException(
  Sender: TObject;
  Args: TModuleExceptionArgs);
var
  LTimeStamp: TDateTime;
  LTimeStampString: string;
  LTransactionId: string;
begin
  LTimeStamp := now;
  LTimeStampString := FormatDateTime('yyyy-mm-dd"T"hh:nn:sss', LTimeStamp);
  LTransactionId := GCurrentRequest.TransactionID;

  // Intern loggen
  ELKELogError('%s - Timestamp: %s, TransactionID: %s, Exception: %s, Status Code: %d',
    [Args.ErrorMessage, LTimeStampString, LTransactionId, Args.Exception.ClassName, Args.StatusCode]);

  // Die Error Details als Json-Payload in ErrorCode einfüllen
  var LErrorJson := TElkeRestError.AsJsonObject(Args.Exception, LTransactionId, Args.StatusCode, LTimeStamp);
  try
    Args.ErrorCode := LErrorJson.ToJSON;
  finally
    FreeAndNil(LErrorJson);
  end;

end;

end.

