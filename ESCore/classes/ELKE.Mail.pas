﻿unit ELKE.Mail;

interface

uses
  System.Classes, System.SysUtils,
  IdMessage,
  XData.Server.Module, ELKE.Classes.Generated;

type
  TElkeMail = class(TObject)
  protected
    class procedure LogEmail(AHost: String; AMessage: TIdMessage; AIdKontrolle: integer; AError: string = '');
  public
    /// <param name="ARecipients">
    /// Hier können mehrere Empfänger mit ";" getrennt drinstehen
    /// </param>
    class procedure SendMail(ASubject, ABody, ARecipients: string; AFiles: TArray<string>; AIdKontrolle: integer);

    class function IsValidEmailAddress(const AEmail: string): boolean;
  end;

implementation

uses
  System.RegularExpressions,
  IdSMTP, IdText, IdAttachmentFile, IdMessageParts,
  ELKE.Server.Configuration.Base, ELKE.Server.Logger, System.IOUtils, Aurelius.Engine.ObjectManager;


function GetMessageBody(AMessage: TIdMessage): string;
var
  i: Integer;
  part: TIdMessagePart;
begin
  Result := '';
  // Durchlaufe alle Teile der Nachricht
  for i := 0 to AMessage.MessageParts.Count - 1 do
  begin
    part := AMessage.MessageParts[i];
    // Überprüfe, ob der Teil ein Text-Teil ist
    if part is TIdText then
    begin
      // Füge den Text des Text-Teils zum Ergebnis hinzu
      Result := Result + TIdText(part).Body.Text;
    end;
  end;
end;



{ TElkeMail }

// Einfaches Regex, das auch einige false positives durchläßt!
// Ein RFC-fester Ausdruck wäre extrem komplex:  https://emailregex.com
// https://regex101.com/r/QZegfr/1
const
  EMAIL_REGEX = '[^@]+@[^\.]+\..+';

class function TElkeMail.IsValidEmailAddress(const AEmail: string): boolean;
begin
  result := TRegEx.IsMatch(AEmail, EMAIL_REGEX);
end;

class procedure TElkeMail.LogEmail(AHost: String; AMessage: TIdMessage; AIdKontrolle: integer; AError: string = '');
var
  LContext: TObjectManager;
begin
  try
    if (TXDataOperationContext.Current = nil) or (TXDataOperationContext.Current.GetManager = nil) then
      raise Exception.Create('XData: Kein Context!');
    LContext := TXDataOperationContext.Current.GetManager;
    var
    LEmailHistory := TEmailHistory.Create;

    LEmailHistory.DatumGesendet := now;
    LEmailHistory.Subject := AMessage.Subject;
    LEmailHistory.Body := GetMessageBody(AMessage);
    LEmailHistory.EmailFrom := AMessage.Sender.Address;
    LEmailHistory.EmailTo := AMessage.Recipients.EMailAddresses;
    LEmailHistory.EmailCc := AMessage.CCList.EMailAddresses;
    LEmailHistory.EmailBc := AMessage.BccList.EMailAddresses;
    LEmailHistory.Host := AHost;
    if AIdKontrolle > 0 then
    begin
      LEmailHistory.Kontrollbericht := LContext.Find<TKontrollbericht>(AIdKontrolle);
    end;

    for var LMessagePart in AMessage.MessageParts do
    begin
      if LMessagePart is TIdAttachmentFile then
      begin
        var
        LAttachment := TIdAttachmentFile(LMessagePart);
        var
        LEMailHistoryAttachment := TEmailHistoryAttachment.Create;
        LEMailHistoryAttachment.Filename := TPath.GetFileName(LAttachment.StoredPathName);
        LEMailHistoryAttachment.Email := LEmailHistory;
        LEmailHistory.Attachments.Add(LEMailHistoryAttachment);
      end;
    end;
    LContext.Save(LEmailHistory);
  except
    on E: Exception do
    begin
      ELKELogError('Log in EmailHistory fehlgeschlagen ' + E.Message);
    end;
  end;
end;

class procedure TElkeMail.SendMail(ASubject, ABody, ARecipients: string; AFiles: TArray<string>;
  AIdKontrolle: integer);
var
  LSMTP: TIdSMTP;
  LMessage: TIdMessage;
  LMessageContent: TIdText;
begin
  if ARecipients.Trim = '' then
    raise Exception.Create('SendMail: Keine Empfänger-EMail angegeben');

  ELKELog('Starte Mailversand: [%s] [%s]', [ASubject, ARecipients]);
  // Nur wenn ein Server konfiguriert ist
  if TConfigurationBase.Default.Server = '' then
  begin
    ELKELogError('Kein Mailserver konfiguriert. Mail wird nicht gesendet!');
  end
  else
  begin
    LSMTP := nil;
    LMessage := nil;
    try
      try
        LSMTP := TIdSMTP.Create(nil);
        LSMTP.Host := TConfigurationBase.Default.Server;
        LSMTP.Port := TConfigurationBase.Default.Port.ToInteger;
        LSMTP.Username := TConfigurationBase.Default.User;
        LSMTP.Password := TConfigurationBase.Default.Password;
        LSMTP.MailAgent := 'ELKE-MAILER';

        // eine Message mit Plain-Text in UTF-8
        LMessage := TIdMessage.Create(nil);
        LMessageContent := TIdText.Create(LMessage.MessageParts);
        LMessageContent.ContentType := 'text/plain';
        LMessageContent.CharSet := 'utf-8';

        // es können mehrere Recipients in ARecipients drin sein
        var
        LTargetRecipients := ARecipients.Split([';']);
        for var LTargetRecipient in LTargetRecipients do
        begin
          var
          LRecipient := LMessage.Recipients.Add;
          LRecipient.Address := LTargetRecipient.Trim;
        end;

        LMessage.Sender.Name := TConfigurationBase.Default.SenderName;
        LMessage.Sender.Address := TConfigurationBase.Default.SenderEmail;
        LMessage.From.Name := TConfigurationBase.Default.SenderName;
        LMessage.From.Address := TConfigurationBase.Default.SenderEmail;
        LMessage.Subject := ASubject;
{$IFDEF DEBUG}
        LMessage.Subject := '[TEST-EMAIL - BITTE IGNORIEREN] ' + LMessage.Subject;
{$ENDIF}
        LMessageContent.Body.Text := ABody;

        for var LFile in AFiles do
        begin
          if LFile > '' then
          begin
            TIdAttachmentFile.Create(LMessage.MessageParts, LFile);
          end;
        end;

        LSMTP.Connect;
        LSMTP.Send(LMessage);
        LogEmail(LSMTP.Host, LMessage, AIdKontrolle);
      except
        on E: Exception do
        begin
          LogEmail(LSMTP.Host, LMessage, AIdKontrolle, E.Message);
          ELKELogError('Mailversand fehlgeschlagen. ' + E.Message);
          raise;
        end;
      end;
    finally
      FreeAndNil(LMessage);
      FreeAndNil(LSMTP);
    end;
  end;
end;

end.
