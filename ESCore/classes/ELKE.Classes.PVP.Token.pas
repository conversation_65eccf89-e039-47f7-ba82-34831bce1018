﻿unit ELKE.Classes.PVP.Token;

interface

uses
  System.Classes, System.SysUtils, System.Rtti,
  Sparkle.Http.Headers,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  Aurelius.Mapping.Attributes, Aurelius.Types.Nullable,
  XData.Service.Common,
  DX.Utils.Rtti,
  ELKE.Classes.PVP.Roles;

type
  EPVPTokenException = class(Exception);

  /// <summary>
  /// <para>
  /// PVP verwendet zumindest im PVP Token eine spezielle SnakeUpperCase
  /// Bennungsstrategie:
  /// </para>
  /// <para>
  /// PVP-VERSION <br /><br />
  /// </para>
  /// <para>
  /// Da ein Dash ("-") in Delphi nicht in Bezeichnern verwendet werden
  /// kann, setzt die TPVPJsonNamingStrategy Klasse das f�r die JSON
  /// Serialisierung um.
  /// </para>
  /// </summary>
  TPVPJsonNamingStrategy = class(TDefaultNamingStrategy)
  protected
    function ResolveName(RttiMember: TRttiMember): string; override;
  end;

  PVPNamesAttribute = class(TCustomAttribute)
  private
    FPVP21: string;
    FAttribute: string;
  public
    constructor Create(
      const AAttribute: string;
      const APVP21Header: string);
    property Attribute: string read FAttribute write FAttribute;
    property PVP21: string read FPVP21 write FPVP21;
  end;

  (*
    // [JsonNamingStrategy(TSnakeCaseNamingStrategy)]
    [JsonNamingStrategy(TPVPJsonNamingStrategy)]
    TFoo = class(TObject)
    private
    FFull_Name: string;
    public
    property Full_Name: string read FFull_Name write FFull_Name;
    end;
  *)

  TOKZ = record

  public
    LandKZ: string;
    OKZ: string;
    procedure Parse(AAOkzPair: string);
  end;

  TPVPToken = class;

  /// <summary>
  /// Das PVP-Token aus den HTTP Headern gem�� <see href="https://teams.microsoft.com/l/file/796DAB5A-67E7-4221-A696-31A9AEB5053D?tenantId=d41f2dec-693b-447d-b923-d1cfcd13e0b8&amp;fileType=pdf&amp;objectUrl=https%3A%2F%2Fesculentagmbh.sharepoint.com%2Fsites%2FELKEProjektteam%2FFreigegebene%20Dokumente%2FGeneral%2FPortalverbund%2FPVP%202.1.3%20Specs%2FGegenu%CC%88berstellung%20PVP%201_x%20zu%202_1.pdf&amp;baseUrl=https%3A%2F%2Fesculentagmbh.sharepoint.com%2Fsites%2FELKEProjektteam&amp;serviceName=teams&amp;threadId=19:<EMAIL>&amp;groupId=a5ef2ad4-cd4e-4cb2-91af-a8f931e5043d">
  /// PVP-Doku</see>
  /// </summary>
  [JsonNamingStrategy(TPVPJsonNamingStrategy)]
  TPVPToken = class(TObject)
  private
    FBIRTHDATE: Nullable<string>;
    FBPK: Nullable<string>;
    // [JsonProperty('CHARGE-CODE')]
    FCHARGE_CODE: Nullable<string>;
    FCOST_CENTER_ID: Nullable<string>;
    FENC_BPK_LIST: Nullable<string>;
    [JsonProperty('FUNKTION')]
    FFUNCTION_: Nullable<string>;
    FGID: Nullable<string>;
    FGIVEN_NAME: Nullable<string>;
    FINVOICE_RECPT_ID: Nullable<string>;
    FMAIL: Nullable<string>;
    FORIG_HOST: Nullable<string>;
    FORIG_SCHEME: Nullable<string>;
    FORIG_URI: Nullable<string>;
    FOU: Nullable<string>;
    FOU_GV_OU_ID: Nullable<string>;
    FOU_OKZ: Nullable<string>;
    FPARTICIPANT_ID: Nullable<string>;
    FPARTICIPANT_OKZ: Nullable<string>;
    FPRINCIPAL_NAME: Nullable<string>;
    FPVP_BINDING: Nullable<string>;
    FPVP_VERSION: Nullable<string>;
    FROLES: Nullable<string>;
    FSECCLASS: integer;
    FTEL: Nullable<string>;
    FTXID: Nullable<string>;
    FUSERID: Nullable<string>;
    // Unmapped, internal fields go here

    [Transient]
    [JsonIgnore]
    FRollen: TPVPRoles;

  protected
    function GetRollen: TPVPRoles;
    function GetOKZ: TOKZ;

  public
    // Per RegEx generiert
    // Step 1: https://regex101.com/r/KBk1Yr/1
    // Step 2: https://regex101.com/r/lv17Il/1/
    constructor Create(AHttpHeaders: THttpHeaders); overload;

    constructor Create(AHttpHeaders: TStrings); overload;

    destructor Destroy; override;

    [PVPNames('PVP-VERSION', 'X-PVP-VERSION')]
    property PVP_VERSION: Nullable<string> read FPVP_VERSION write FPVP_VERSION;

    [PVPNames('SECCLASS', 'X-PVP-SECCLASS')]
    property SECCLASS: integer read FSECCLASS write FSECCLASS;

    [PVPNames('USERID', 'X-PVP-USERID')]
    property USERID: Nullable<string> read FUSERID write FUSERID;

    [PVPNames('GID', 'X-PVP-GID')]
    property GID: Nullable<string> read FGID write FGID;

    [PVPNames('BPK', 'X-PVP-BPK')]
    property BPK: Nullable<string> read FBPK write FBPK;

    [PVPNames('MAIL', 'X-PVP-MAIL')]
    property MAIL: Nullable<string> read FMAIL write FMAIL;

    [PVPNames('TEL', 'X-PVP-TEL')]
    property TEL: Nullable<string> read FTEL write FTEL;

    [PVPNames('PARTICIPANT-ID', 'X-PVP-PARTICIPANT-ID')]
    property PARTICIPANT_ID: Nullable<string> read FPARTICIPANT_ID write FPARTICIPANT_ID;

    [PVPNames('OU-GV-OU-ID', 'X-PVP-OU-GV-OU-ID')]
    property OU_GV_OU_ID: Nullable<string> read FOU_GV_OU_ID write FOU_GV_OU_ID;

    [PVPNames('OU', 'X-PVP-OU')]
    property OU: Nullable<string> read FOU write FOU;

    [PVPNames('FUNCTION', 'X-PVP-FUNCTION')]
    property FUNCTION_: Nullable<string> read FFUNCTION_ write FFUNCTION_;

    [PVPNames('ROLES', 'X-PVP-ROLES')]
    property Roles: Nullable<string> read FROLES write FROLES;

    [PVPNames('INVOICE-RECPT-ID', 'X-PVP-INVOICE-RECPT-ID')]
    property INVOICE_RECPT_ID: Nullable<string> read FINVOICE_RECPT_ID write FINVOICE_RECPT_ID;

    [PVPNames('COST-CENTER-ID', 'X-PVP-COST-CENTER-ID')]
    property COST_CENTER_ID: Nullable<string> read FCOST_CENTER_ID write FCOST_CENTER_ID;

    [PVPNames('CHARGE-CODE', 'X-PVP-CHARGE-CODE')]
    property CHARGE_CODE: Nullable<string> read FCHARGE_CODE write FCHARGE_CODE;

    [PVPNames('TXID', 'X-PVP-TXID')]
    property TXID: Nullable<string> read FTXID write FTXID;

    [PVPNames('ORIG-SCHEME', 'X-PVP-ORIG-SCHEME')]
    property ORIG_SCHEME: Nullable<string> read FORIG_SCHEME write FORIG_SCHEME;

    [PVPNames('ORIG-HOST', 'X-PVP-ORIG-HOST')]
    property ORIG_HOST: Nullable<string> read FORIG_HOST write FORIG_HOST;

    [PVPNames('ORIG-URI', 'X-PVP-ORIG-URI')]
    property ORIG_URI: Nullable<string> read FORIG_URI write FORIG_URI;

    [PVPNames('PRINCIPAL-NAME', 'X-PVP-PRINCIPAL-NAME')]
    property PRINCIPAL_NAME: Nullable<string> read FPRINCIPAL_NAME write FPRINCIPAL_NAME;

    [PVPNames('GIVEN-NAME', 'X-PVP-GIVEN-NAME')]
    property GIVEN_NAME: Nullable<string> read FGIVEN_NAME write FGIVEN_NAME;

    [PVPNames('BIRTHDATE', 'X-PVP-BIRTHDATE')]
    property BIRTHDATE: Nullable<string> read FBIRTHDATE write FBIRTHDATE;

    [PVPNames('ENC-BPK-LIST', 'X-PVP-ENC-BPK-LIST')]
    property ENC_BPK_LIST: Nullable<string> read FENC_BPK_LIST write FENC_BPK_LIST;

    [PVPNames('PARTICIPANT-OKZ', 'X-PVP-PARTICIPANT-OKZ')]
    property PARTICIPANT_OKZ: Nullable<string> read FPARTICIPANT_OKZ write FPARTICIPANT_OKZ;

    [PVPNames('OU-OKZ', 'X-PVP-OU-OKZ')]
    property OU_OKZ: Nullable<string> read FOU_OKZ write FOU_OKZ;

    [PVPNames('PVP-BINDING', 'X-PVP-BINDING')]
    property PVP_BINDING: Nullable<string> read FPVP_BINDING write FPVP_BINDING;

  public
    procedure SetValueByPVP21HeaderName(const AName, AValue: string);
    class function GetHeaderNames: TArray<string>;
    property Rollen: TPVPRoles read GetRollen;
    property OKZ: TOKZ read GetOKZ;

  end;

implementation

uses
  XData.Server.Module, ELKE.Classes.Generated, Aurelius.Engine.ObjectManager, System.Net.URLClient, System.NetEncoding;

{ TXPVToken }

constructor TPVPToken.Create(AHttpHeaders: TStrings);
begin
  AHttpHeaders.NameValueSeparator := ':';
  for var i := 0 to AHttpHeaders.Count - 1 do
  begin
    // Achtung! Das Portal schickt Sonderzeichen in den Header-Values HTML-encoded!
    var
    LName := AHttpHeaders.Names[i];
    var
    LValue := TNetEncoding.HTML.Decode(AHttpHeaders.Values[LName]);
    SetValueByPVP21HeaderName(LName, LValue);
  end;

{$IFDEF DEBUG}
  if GID.ValueOrDefault = '' then
  begin
    GID := 'AT:VKZ:XFN-340968z:0007'; // Olaf
    Roles := 'ELKE-Kontrolle(OKZ=AT:L3);ELKE-Kontrollplanung(OKZ=AT:L3);ELKE-Appadmin(OKZ=AT:L3);DEBUG(OKZ=AT:L3)';
    TXID := 'DEBUG T' + IntToStr(TThread.Current.ThreadID);
    PVP_VERSION := '2.1';
    PRINCIPAL_NAME := 'Monien';
    GIVEN_NAME := 'Olaf';
    MAIL := '<EMAIL>';
  end;
{$ENDIF}
  // olaf:
  // 2020-05-11 18:58:51,921 : Header for Token: x-pvp-userid : <EMAIL>
  // 2020-05-11 18:58:51,921 : Header for Token: x-pvp-participant-id : AT:VKZ:XFN-340968z
  // 2020-05-11 18:58:51,921 : Header for Token: x-pvp-gid : AT:VKZ:XFN-340968z:0007
  // 2020-05-11 18:58:51,921 : Header for Token: x-pvp-ou-gv-ou-id : AT:VKZ:XFN-340968z
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-ou : Esculenta GmbH
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-orig-scheme : https
  // 2020-05-11 18:58:51,920 : Header for Token: x-txid : 185849$<EMAIL>.inst1
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-orig-uri : /ELKE/v1/me
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-version : 2.1
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-cost-center-id : AT:VKZ:XFN-340968z
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-roles : test()
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-ou-okz : XFN-340968z
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-secclass : 1
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-invoice-recpt-id : AT:VKZ:XFN-340968z
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-principal-name : Monien
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-orig-host : stp.esculenta.at
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-mail : <EMAIL>
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-txid : 185849$<EMAIL>.inst1
  // 2020-05-11 18:58:51,920 : Header for Token: x-pvp-given-name : Olaf

  // Explizit OKZ und Rollen parsen, um Fehler zu entdecken
  // Todo: Explizites "Validate"
  var LOKZ := self.OKZ;
  var LRollen := self.Rollen;
  Assert(LRollen <> nil);
end;

destructor TPVPToken.Destroy;
begin
  FreeAndNil(FRollen);
  inherited;
end;

constructor TPVPToken.Create(AHttpHeaders: THttpHeaders);
var
  LHeader: THttpHeaderInfo;
begin
  inherited Create;
  var
  LHeaders := TStringList.Create;
  try
    LHeaders.NameValueSeparator := ':';
    LHeaders.TrailingLineBreak := false;
    for LHeader in AHttpHeaders.AllHeaders do
    begin
      // Achtung! Das Portal schickt Sonderzeichen in den Headern HTML Encoded!
      var
      LValue := TNetEncoding.HTML.Decode(LHeader.Value);
      LHeaders.AddPair(LHeader.Name, LValue);
    end;
    Create(LHeaders);
  finally
    FreeAndNil(LHeaders);
  end;
end;

function TPVPToken.GetOKZ: TOKZ;
var
  LRollen: TPVPRoles;
  LRolle: TPVPRole;
  LOKZ: TParameter;
begin
  // Das Bundesland steckt in der OKZ der Rollen
  // ELKE-Hygienekontrolle(OKZ=AT:L3)
  // Aktuell gehen wir davon aus, dass das Bundesland einheitlich ist, also nicht mehr als eins pro User.
  // Daher schauen wir hier nur auf die *erste* Rolle
  LRollen := Rollen;
  if LRollen.Count = 0 then
    raise EPVPTokenException.Create('User hat keine Rollen, Bundesland kann nicht identifiziert werden!');
  LRolle := LRollen.First;
  LOKZ := LRolle.Parameter;
  if LOKZ.Typ <> 'OKZ' then
    raise EPVPTokenException.Create
      ('User hat keinen OKZ Parameter in seinen Rollen, Bundesland kann nicht identifiziert werden!');

  Result.Parse(LOKZ.Wert);
end;

class function TPVPToken.GetHeaderNames: TArray<string>;
var
  LContext: TRttiContext;
  LType: TRttiType;
  LProperties: TArray<TRttiProperty>;
  LProperty: TRttiProperty;
  LAttributes: TArray<TCustomAttribute>;
  LAttribute: TCustomAttribute;
  LPVPNames: PVPNamesAttribute;
begin
  LContext := TRttiContext.Create;
  LType := LContext.GetType(TPVPToken);
  LProperties := LType.GetProperties;
  SetLength(Result, 0);
  for LProperty in LProperties do
  begin
    LAttributes := LProperty.GetAttributes;
    for LAttribute in LAttributes do
    begin
      if LAttribute is PVPNamesAttribute then
      begin
        SetLength(Result, Length(Result) + 1);
        LPVPNames := PVPNamesAttribute(LAttribute);
        Result[Length(Result) - 1] := LPVPNames.PVP21;
      end;
    end;
  end;
end;

function TPVPToken.GetRollen: TPVPRoles;
begin
  if FRollen = nil then
  begin
    FRollen := TPVPRoles.Parse(Roles);
  end;
  Result := FRollen;
end;

procedure TPVPToken.SetValueByPVP21HeaderName(const AName, AValue: string);
var
  LContext: TRttiContext;
  LType: TRttiType;
  LProperties: TArray<TRttiProperty>;
  LProperty: TRttiProperty;
  LPVPNames: PVPNamesAttribute;
  LAttributes: TArray<TCustomAttribute>;
  LAttribute: TCustomAttribute;
  LFound: Boolean;

begin
  LContext := TRttiContext.Create;
  LType := LContext.GetType(self.ClassType);
  LProperties := LType.GetProperties;
  LFound := false;
  for LProperty in LProperties do
  begin
    LAttributes := LProperty.GetAttributes;
    for LAttribute in LAttributes do
    begin
      if LAttribute is PVPNamesAttribute then
      begin
        LPVPNames := PVPNamesAttribute(LAttribute);
        if SameText(LPVPNames.PVP21, AName) then
        begin
          // There are nullable<> types, which are actually records!
          if LProperty.PropertyType.TypeKind = tkRecord then
          begin
            // It's a Nullable<> record, which has "Value" and "HasValue" fields
            LProperty.SetFieldValue(self, 'FValue', AValue);
            LProperty.SetFieldValue(self, 'FHasValue', true);
          end
          else
          begin
            // Non-Nullables we only check for integer and string
            if LProperty.GetValue(self).Kind = tkInteger then
            begin
              LProperty.SetValue(self, AValue.ToInteger);
            end
            else
            begin
              LProperty.SetValue(self, AValue);
            end;
          end;
          LFound := true;
          break;
        end;
      end;
    end;
    if LFound then
      break;
  end;
  LContext.Free;
end;

{ PVPAttribute }

constructor PVPNamesAttribute.Create(const AAttribute, APVP21Header: string);
begin
  FAttribute := AAttribute;
  FPVP21 := APVP21Header;
end;

{ TPVPJsonNamingStrategy }

function TPVPJsonNamingStrategy.ResolveName(RttiMember: TRttiMember): string;
var
  LJsonName: string;
begin
  // FPVP_Version ->  PVP-VERSION
  LJsonName := inherited ResolveName(RttiMember);
  LJsonName := LJsonName.ToUpper;
  Result := LJsonName.Replace('_', '-', [rfReplaceAll]);
end;

{ TOKZ }

procedure TOKZ.Parse(AAOkzPair: string);
var
  LParts: TArray<string>;
begin
  // Ein OKZ Paar sollte so aussehen:  AT:L3
  // F�r das Bundesministerium sieht das so aus: AT:B:70
  LParts := AAOkzPair.Split([':']);
  if Length(LParts) < 2 then
    raise EPVPTokenException.Create('Ungültige OKZ! (' + AAOKZPair+')');
  LandKZ := LParts[0].ToUpper;

  // Die OKZ wird aus den Teilen nach dem LandKZ zusammen gesetzt
  // Also
  // AT:L3   -> L3
  // AT:B:70 -> B:70
  var
  LOKZ := '';
  for var i := 1 to Length(LParts) - 1 do
  begin
    LOKZ := LOKZ + LParts[i].ToUpper + ':';
  end;

  OKZ := LOKZ.TrimRight([':']).Trim;
end;

end.
