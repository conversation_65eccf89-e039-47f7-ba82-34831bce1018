unit ELKE.Server.Configuration;

interface

uses
  System.Classes, System.SysUtils,
  DX.Classes.Configuration,
  ELKE.Server.Configuration.Base;

type
  [ConfigDescription('ELKE Server Configuration')]
  TConfiguration = class(TConfigurationBase)
  public
    [ConfigValue('Server', 'http://+:80/ELKE/v1')]
    property BaseURL: string read GetBaseURL;
    [ConfigValue('DB', 'Database=ELKE;ApplicationName=ELKEServer;OSAuthent=Yes;Server=localhost\SQLEXPRESS;DriverID=MSSQL')]
    property DBConnectionString: string read GetDBConnectionString;
  end;

implementation



end.
