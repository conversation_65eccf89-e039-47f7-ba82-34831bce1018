unit JQ.Helpers.ComboboxEx;

interface

uses
  System.Classes, System.SysUtils,
  IWCGJQComboBox, IWCGJQControl, IWCGJQCommon;

type
  TIWCGJSContainerHelper = class helper for TIWCGJSContainer
  public
    procedure AddJavaScriptToAsyncResponse(const AScript: string);
  end;

  TComboboxExHelper = class helper for TIWCGJQComboBoxEx
  private
    function GetItemIndex: integer;
    procedure SetItemIndex(const Value: integer);
    function GetSelectedText: string;
    procedure SetSelectedValue(const Value: string);
    function GetSelectedValue: string;

  protected
  public
    procedure SetNoSelectionText(const Value: string);
    property ItemIndex: integer read GetItemIndex write SetItemIndex;
    property SelectedText: string read GetSelectedText;
    property SelectedValue: string read GetSelectedValue write SetSelectedValue;
    property NoSelectionText: string write SetNoSelectionText;
    function IndexOfName(const AName: string): integer;
    function NoItemSelected: Boolean;
    function IsItemSelected: Boolean;
    function HasNoSelctionItem: Boolean;
  end;

type
  TComboboxExItemsHelper = class helper for TIWCGJQComboBoxExItems
  public
    procedure Clear(const ANoSelectionText: string = '');
    procedure ClearNoSelection;
    procedure Add(const ACaption: string); overload;
    procedure Add(const ACaption: string; AValue: string); overload;
    // Name is Key aka Caption - not Value!
    function IndexOf(const AName: string): integer;
    function IndexOfName(const AName: string): integer;
    function ValueFromIndex(AIndex: integer): string;
  end;

implementation

const
  NO_SELECTION_VALUE = 'NOSELECTION';
  NO_SELECTION_TEXT = '-- Keine Auswahl --';
  { TComboboxExHelper }

function TComboboxExHelper.GetItemIndex: integer;
begin
  result := self.SelectedIndex;
end;

function TComboboxExHelper.GetSelectedText: string;
begin
  if (SelectedIndex < 0) or (SelectedIndex >= Items.Count) then
  begin
    result := ''
  end
  else
  begin
    result := Items[SelectedIndex].Caption;
  end;
end;

function TComboboxExHelper.GetSelectedValue: string;
begin
  result := self.Items[self.ItemIndex].Value;
end;

function TComboboxExHelper.HasNoSelctionItem: Boolean;
begin
  // Das No SelectionItem ist immer an Pos 0
  result := (self.Items.Count > 0) and (self.Items[0].Value = NO_SELECTION_VALUE);
end;

function TComboboxExHelper.IndexOfName(const AName: string): integer;
begin
  result := self.Items.IndexOf(AName);
end;

function TComboboxExHelper.NoItemSelected: Boolean;
begin
  result := (self.ItemIndex = -1) or (self.SelectedValue = NO_SELECTION_VALUE);
end;

function TComboboxExHelper.IsItemSelected: Boolean;
begin
  result := not NoItemSelected;
end;

procedure TComboboxExHelper.SetItemIndex(const Value: integer);
begin
  // Die Combobox kann nicht wirklich mit -1 umgehen
  if (self.Items.Count > 0) and (Value = -1) then
  begin
    self.SelectedIndex := 0;
  end
  else
  begin
    self.SelectedIndex := Value;
  end;
  self.JQComboBoxExOptions.RefreshItems;
  AddJavaScriptToAsyncResponse(JQComboBoxExOptions.jsSetSelIndex(SelectedIndex));
end;

procedure TComboboxExHelper.SetNoSelectionText(const Value: string);
begin
  // nothing
end;

procedure TComboboxExHelper.SetSelectedValue(const Value: string);
begin
  var LItem := Items.ItemsByValue[Value];
  if Assigned(LItem) then
  begin
    self.ItemIndex := LItem.Index;
  end
  else
  begin
    self.ItemIndex := -1;
  end;
  self.GetSelectedText
end;

{ TComboboxExItemsHelper }

procedure TComboboxExItemsHelper.Add(const ACaption: string; AValue: string);
begin
  if AValue = '' then
  begin
    AValue := ACaption;
  end;
  AddOption(ACaption, AValue);
  // Das Combo kann nicht wirklich mit -1 umgehen
  if (owner as TIWCGJQComboBoxEx).ItemIndex = -1 then
  begin
    (owner as TIWCGJQComboBoxEx).ItemIndex := 0;
  end;

end;

procedure TComboboxExItemsHelper.Add(const ACaption: string);
begin
  Add(ACaption, '');
end;

procedure TComboboxExItemsHelper.Clear(const ANoSelectionText: string = '');
begin
  inherited Clear;
  if ANoSelectionText.Trim > '' then
  begin
    AddOption(ANoSelectionText.Trim, NO_SELECTION_VALUE);
    (self.owner as TIWCGJQComboBoxEx).ItemIndex := 0;
  end;
end;

procedure TComboboxExItemsHelper.ClearNoSelection;
begin
  Clear(NO_SELECTION_TEXT);
end;

function TComboboxExItemsHelper.IndexOf(const AName: string): integer;
begin
  result := -1;
  for var i := 0 to self.Count - 1 do
  begin
    if Items[i].Caption = AName then
    begin
      result := i;
      break;
    end;
  end;
end;

function TComboboxExItemsHelper.IndexOfName(const AName: string): integer;
begin
  result := IndexOf(AName);
end;

function TComboboxExItemsHelper.ValueFromIndex(AIndex: integer): string;
begin
  result := self.Items[AIndex].Value;
end;

{ TIWCGJSContainerHelper }

procedure TIWCGJSContainerHelper.AddJavaScriptToAsyncResponse(const AScript: string);
begin
  //Is there a script
  if (Trim(AScript) > '') then
  begin
    if GenerateAjaxResponse[nil] then
    begin
      //we are in an async callback
      var LResponse := Dispatcher.AddAjaxResponseScript(AScript) as TIWCGResponseScript;
      Assert(LResponse <> nil);
    end
    else
    begin
      //This is a sync response
      var LResponse :=Dispatcher.AddSubmitResponseScript(AScript) as TIWCGResponseScript;
      Assert(LResponse <> nil);
    end;
  end;
end;

end.

