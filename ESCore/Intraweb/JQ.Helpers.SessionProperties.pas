unit JQ.Helpers.SessionProperties;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  IWApplication, IWUserSessionBase;

type
  TSessionProperties = class(TObject)
  strict private
  type
    TSessionStorage = class(TComponent)
    strict private
      FStorage: TDictionary<string, string>;
    private
      FSessionID: string;
    public
      constructor Create(AOwner: TComponent);override;
      destructor Destroy; override;
      function GetValue(const AKey: string): string;
      procedure SetValue(const AKey, AValue: string);
    end;

  strict private
    class var FStorage: TDictionary<string, TSessionStorage>;
  strict protected
    class function CurrentSessionId: string; inline;
    class function CurrentUserSession: TIWUserSessionBase;
  public
    class constructor Create;
    class destructor Destroy;
    class function GetValue(const AKey: string): string; overload;
    class function GetValue(AComponent: TComponent; const AKey: string): string; overload;
    class procedure SetValue(const AKey, AValue: string); overload;
    class procedure SetValue(AComponent: TComponent; const AKey, AValue: string); overload;

    class procedure RemoveSession(const ASessionId: string);
  end;

implementation

{ TSessionStorage }

constructor TSessionProperties.TSessionStorage.Create(AOwner: TComponent);
begin
  inherited;
  FStorage := TDictionary<string, string>.Create;
  FSessionID := gGetWebApplicationThreadVar.AppID;
end;

destructor TSessionProperties.TSessionStorage.Destroy;
begin
  TSessionProperties.RemoveSession(FSessionID);
  FreeAndNil(FStorage);
  inherited;
end;

function TSessionProperties.TSessionStorage.GetValue(const AKey: string): string;
begin
  result := '';
  FStorage.TryGetValue(AKey, result);
end;

procedure TSessionProperties.TSessionStorage.SetValue(const AKey, AValue: string);
begin
  FStorage.AddOrSetValue(AKey, AValue);
end;

{ TServerStorage }

class constructor TSessionProperties.Create;
begin
  FStorage := TDictionary<string, TSessionStorage>.Create;
end;

class function TSessionProperties.CurrentSessionId: string;
begin
  result := gGetWebApplicationThreadVar.AppID;
end;

class function TSessionProperties.CurrentUserSession: TIWUserSessionBase;
begin
  Assert(gGetWebApplicationThreadVar.Data is TIWUserSessionBase, 'Usersession nicht gefunden!');
  result := TIWUserSessionBase(gGetWebApplicationThreadVar.Data);
end;

class destructor TSessionProperties.Destroy;
begin
  FreeAndNil(FStorage);
end;

class function TSessionProperties.GetValue(AComponent: TComponent; const AKey: string): string;
begin
  Assert(AComponent.Name > '');
  result := GetValue(AComponent.Name + '_' + AKey);
end;

class
  function TSessionProperties.GetValue(const AKey: string): string;
begin
  var
    LSessionStorage: TSessionStorage := nil;
  TMonitor.Enter(FStorage);
  try
    FStorage.TryGetValue(CurrentSessionId, LSessionStorage);
    if LSessionStorage = nil then
    begin
      LSessionStorage := TSessionStorage.Create(CurrentUserSession);
      FStorage.Add(CurrentSessionId, LSessionStorage);
    end;
    result := LSessionStorage.GetValue(AKey);
  finally
    TMonitor.Exit(FStorage);
  end;
end;

class
  procedure TSessionProperties.RemoveSession(const ASessionId: string);
begin
  if FStorage <> nil then
  begin
    TMonitor.Enter(FStorage);
    try
      FStorage.Remove(ASessionId);
    finally
      TMonitor.Exit(FStorage);
    end;
  end;
end;

class procedure TSessionProperties.SetValue(AComponent: TComponent; const AKey, AValue: string);
begin
  Assert(AComponent.Name > '');
  SetValue(AComponent.Name + '_' + AKey, AValue);
end;

class
  procedure TSessionProperties.SetValue(const AKey, AValue: string);
begin
  var
  LSessionId := gGetWebApplicationThreadVar.AppID;
  var
    LSessionStorage: TSessionStorage := nil;
  TMonitor.Enter(FStorage);
  try
    FStorage.TryGetValue(LSessionId, LSessionStorage);
    if LSessionStorage = nil then
    begin
      LSessionStorage := TSessionStorage.Create(CurrentUserSession);
      FStorage.Add(LSessionId, LSessionStorage);
    end;
    LSessionStorage.SetValue(AKey, AValue);
  finally
    TMonitor.Exit(FStorage);
  end;

end;

end.
