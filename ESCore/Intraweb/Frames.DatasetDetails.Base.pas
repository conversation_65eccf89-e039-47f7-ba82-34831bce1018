unit Frames.DatasetDetails.Base;

interface

uses
  System.SysUtils, System.Classes,
  VCL.Controls, VCL.Forms,
  Data.DB,
  IWCGFrame, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWCGJQRegion, IWRegion, IWCGJQControl,
  Frames.Base;

type
  /// <summary>
  /// Diese <PERSON>ame-<PERSON>e ist vorbereitet, Details zu einem Dataset
  /// anzuzeigen.Der Standard-Rahmen des Themes wird angezeigt.
  /// </summary>
  //TFrameDatasetDetailBase = class abstract(TFrameBase)
  TFrameDatasetDetailBase<T: TDataModule> = class abstract(TFrameBase<T>)
  private
    FOnClick: TProc<string>;
  public
    property OnClick: TProc<string> read FOnClick write FOnClick;
  end;


implementation

{$R *.dfm}

end.
