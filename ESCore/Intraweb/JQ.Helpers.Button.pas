unit JQ.Helpers.Button;

interface

uses
  System.SysUtils, System.Classes,
  IWCGJQButton, IWCGJQLockIndicator, IWServerControllerBase;

type
  TIWCGJQButtonHelper = class helper for TIWCGJQButton
  protected
    procedure AsyncShowWaitIndicator(AIndex: Integer);
    function GetIndicatorInstance: TIWCGJQLockIndicator;
  public
    /// <summary>
    /// Zeigt die rotierenden Punkte an, welche normalerweise bei einem
    /// synchronen Request angezeigt werden.
    /// </summary>
    procedure AsyncShowWaitWheel;
    /// <summary>
    /// Zeigt den Cursor als "Eieruhr" an, der normalerweise bei asynchronen
    /// Requests verwendet wird;
    /// </summary>
    procedure AsyncShowHourglass;
  end;

implementation

uses
  IWapplication, IWGlobal;

{ TIWCGJQButtonHelper }

procedure TIWCGJQButtonHelper.AsyncShowHourglass;
begin
  // Der Async Indicator zeigt die "Eieruhr" an
  AsyncShowWaitIndicator(GetIndicatorInstance.AsyncIndicatorIndex);
end;

procedure TIWCGJQButtonHelper.AsyncShowWaitIndicator(AIndex: Integer);
begin
  if AIndex > -1 then
  begin
    var
    LIndicator := GetIndicatorInstance;
    if Assigned(LIndicator) then
    begin
      if Assigned(JQButtonOptions.OnClick.OnEvent) then
      begin
        JQButtonOptions.OnClick.Indicator := LIndicator;
        JQButtonOptions.OnClick.IndicatorIndex := AIndex;
      end;
      if Assigned(JQEvents.OnClick.OnEvent) then
      begin
        JQEvents.OnClick.Indicator := LIndicator;
        JQEvents.OnClick.IndicatorIndex := AIndex;
      end;
    end;
  end;
end;

procedure TIWCGJQButtonHelper.AsyncShowWaitWheel;
begin
  // Der Submit Indicator zeigt das "wheel" an
  AsyncShowWaitIndicator(GetIndicatorInstance.SubmitIndicatorIndex);
end;

function TIWCGJQButtonHelper.GetIndicatorInstance: TIWCGJQLockIndicator;
var
  LIndicator: TIWCGJQLockIndicator;
begin
  LIndicator := nil;
  var LWebApp :=gGetWebApplicationThreadVar;

  for var i := 0 to LWebApp.ActiveForm.ComponentCount - 1 do
  begin
    if LWebApp.ActiveForm.Components[i] is TIWCGJQLockIndicator then
    begin
      LIndicator := TIWCGJQLockIndicator(LWebApp.ActiveForm.Components[i]);
      break
    end;
  end;

  if not Assigned(LIndicator) then
  begin
    for var i := 0 to GServerController.ComponentCount - 1 do
    begin
      if GServerController.Components[i] is TIWCGJQLockIndicator then
      begin
        LIndicator := TIWCGJQLockIndicator(GServerController.Components[i]);
        break
      end;
    end;
  end;

  result := LIndicator;
end;

end.
