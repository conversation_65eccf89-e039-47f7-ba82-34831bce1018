﻿unit Base.Frame;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Collections,
  Vcl.Controls, Vcl.Forms,
  IWCGFrame, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion,
  IWCGJQRegion, IWCGJQControl, IWApplication;

type

  TIWCGJQRegion = IWCGJQRegion.TIWCGJQRegion;
  TIWCGFrame = IWCGFrame.TIWCGFrame;

  /// <summary>
  /// Frame-Vorlage für alle konkreten Frames
  /// </summary>
  TIWBaseFrame = class(TIWCGFrame)
    IWFrameRegion: TIWCGJQRegion;
  public
  end;

  /// <summary>
  /// Frame-Vorlage, der eine Datenmodul-Klasse
  /// mitgegeben werden kann, die automatisch instanziert (und wieder freigegeben) wird.
  /// </summary>
  TIWBaseFrame<T: TDataModule> = class(TIWBaseFrame)
  private
    FDM: T;
    FDataModuleOwned: Boolean;
  public
    constructor Create(AOwner: TComponent); reintroduce; overload;
    constructor Create(AOwner: TComponent; ADatamodule: T); reintroduce; overload;
    destructor Destroy; override;
    property DM: T read FDM;
  end;

  TIWBaseFrameClass = class of TIWBaseFrame;

  TDMContainer<T: TDataModule> = record
  private
    FDM: T;
  public
    property DM: T read FDM;
    procedure Create;
  end;

implementation

{$R *.dfm}


{ TIWBaseFrame<T> }
constructor TIWBaseFrame<T>.Create(AOwner: TComponent);
begin
  inherited;
  FDataModuleOwned := true;
  FDM := T.Create(Nil);
end;

constructor TIWBaseFrame<T>.Create(AOwner: TComponent; ADatamodule: T);
begin
  inherited Create(AOwner);
  // Wenn das Datenmodul von aussen hereingereicht wird, dann übernehmen wir NICHT die Ownership!
  FDataModuleOwned := false;
  FDM := ADatamodule;
end;

destructor TIWBaseFrame<T>.Destroy;
begin
  if FDataModuleOwned then
  begin
    FreeAndNil(FDM);
  end;
  inherited;
end;

{ TDMContainer<T> }

procedure TDMContainer<T>.Create;
begin
  FDM := T.Create(Nil);
end;

end.
