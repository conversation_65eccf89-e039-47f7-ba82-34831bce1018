﻿unit JQ.Helpers.Grid;

interface

uses
  System.Classes, System.SysUtils,
  IWCGJQGrid, IWCGJQGridDataSetProvider, IWCGJQGridCustomProvider,
  FireDAC.Comp.Dataset, IWCGJQCommon, IWCGJQLockIndicator,
  IWApplication, IWGlobal;

type
  TIWCGJQGridDatasetProviderHelper = class helper for TIWCGJQGridDatasetProvider
  public
    procedure ResetDataset;
  end;

  TIWCGJQGridHelper = class helper for TIWCGJQGrid
  strict protected
    procedure GridOnSort(Sender: TObject; ASortItems: TIWCGJQGridSortItems);
    procedure SetTitleAutoWrap;
    procedure AsyncShowWaitIndicator(AIndex: Integer);
    function GetIndicatorInstance: TIWCGJQLockIndicator;
  public
    /// <summary>
    /// Zeigt den Cursor als "Eieruhr" an, der normalerweise bei asynchronen
    /// Requests verwendet wird;
    /// </summary>
    procedure AsyncShowHourglass;
    /// <summary>
    /// Zeigt die rotierenden Punkte an, welche normalerweise bei einem
    /// synchronen Request angezeigt werden.
    /// </summary>
    procedure AsyncShowWaitWheel;
    /// <summary>
    /// SetupDefaults einmalig für jedes Grid aufrufen.
    /// </summary>
    procedure SetupDefaults(AProvider: TIWCGJQGridDatasetProvider);

    /// <summary>
    /// Navigiert im dahinterliegenden Dataset zum Record, dem die ausgewählte Zeile im Grid entspricht.
    /// </summary>
    procedure SelectRecordFromCurrentRow;

    /// <summary>
    /// Selektiert im Grid die Zeile, die dem aktuellen Record antspricht
    /// </summary>
    procedure SelectRowFromCurrentRecord;

    function Dataset: TFDDataSet;
    function Provider: TIWCGJQGridDatasetProvider;

    /// <summary>
    /// Führt einen Refresh auf dem zugrunde liegenden Dataset des Grids
    /// durch und läst einen async Reload im Grid aus, so dass die aktuellen
    /// Daten des Datasetz angzeigt werden.
    /// </summary>
    procedure Refresh;
  end;

implementation

uses
  Data.DB;

{ TIWCGJQGridHelper }

procedure TIWCGJQGridHelper.AsyncShowHourglass;
begin
  // Der Async Indicator zeigt die "Eieruhr" an
  AsyncShowWaitIndicator(GetIndicatorInstance.AsyncIndicatorIndex);
end;

procedure TIWCGJQGridHelper.AsyncShowWaitIndicator(AIndex: Integer);
begin
  if AIndex > -1 then
  begin
    var
    LIndicator := GetIndicatorInstance;
    if Assigned(LIndicator) then
    begin
      if Assigned(JQGridOptions.OnDblClickRow.OnEvent) then
      begin
        JQGridOptions.OnDblClickRow.Indicator := LIndicator;
        JQGridOptions.OnDblClickRow.IndicatorIndex := AIndex;
      end;
      if Assigned(JQEvents.OnClick.OnEvent) then
      begin
        JQEvents.OnClick.Indicator := LIndicator;
        JQEvents.OnClick.IndicatorIndex := AIndex;
      end;
    end;
  end;
end;

procedure TIWCGJQGridHelper.AsyncShowWaitWheel;
begin
  // Der Submit Indicator zeigt das "wheel" an
  AsyncShowWaitIndicator(GetIndicatorInstance.SubmitIndicatorIndex);
end;

procedure TIWCGJQGridHelper.SelectRecordFromCurrentRow;
begin
  var
  LSelectedRowID := JQGridOptions.SelRow;
  Dataset.Locate(Provider.KeyFields, LSelectedRowID, [loCaseInsensitive]);
end;

procedure TIWCGJQGridHelper.SelectRowFromCurrentRecord;
begin
  var
  LKeyField := Dataset.FieldByName(Provider.KeyFields);
  if (LKeyField <> nil) and (LKeyField.AsString <> '') then
  begin
    self.JQGridOptions.SelRow := LKeyField.AsString;
    self.JQGridOptions.SetSelection(LKeyField.AsString);
  end;
end;

procedure TIWCGJQGridHelper.SetTitleAutoWrap;
begin
  // https://stackoverflow.com/questions/7246506/how-to-wrap-single-column-header-text-into-multiple-lines-in-jqgrid/7256972#7256972
  // Hier werden die Titelzeilen so modifiziert, dass ein automatisches Word-wrap stattfindet, wenn mehrere Worte im Titel
  // stehen un diese zu lang sind.
  // Durch einfügen von <br/> kann manuell umgebriochen werden.

  var
  LScript := 'var gridX = ' + JQGridOptions.CGJSContainer.IDJQuery + '.jqGrid();';

  LScript := LScript +
  // https://stackoverflow.com/a/7256972
  // get the header row which contains'
    '/*catchmee*/'
    + 'var headerRow = gridX.closest("div.ui-jqgrid-view")'
    + '.find("table.ui-jqgrid-htable>thead>tr.ui-jqgrid-labels");'

  // increase the height of the resizing span
    + 'var resizeSpanHeight = ''height: '' + headerRow.height() +'
    + '''px !important; cursor: col-resize;'';'
    + 'headerRow.find("span.ui-jqgrid-resize").each(function () {'
    + 'this.style.cssText = resizeSpanHeight;'
    + '});'

  // set position of the div with the column header text to the middle
    + 'var rowHight = headerRow.height();'
    + 'headerRow.find(" div.ui - jqgrid - sortable ").each('
    + 'function() {'
    + 'var ts = $(this);'
    + 'ts.css(''top'', (rowHight - ts.outerHeight()) / 2 + ''px'');'
    + '} );';

  AddJavaScriptToResponse(LScript);
end;

procedure TIWCGJQGridHelper.SetupDefaults(AProvider: TIWCGJQGridDatasetProvider);
begin
  JQGridOptions.LoadText := 'Lade Daten...';
  JQGridOptions.EmptyRecords := '(Keine Daten)';
  JQGridOptions.Locale := 'de';

  JQGridOptions.LoadOnce := false;
  JQGridOptions.RowNum := 100000;
  JQGridOptions.sortable := True;
  // JQGridOptions.OnSelectRow.OnEvent = GridBetriebeOnSelectRow
  JQGridOptions.OnSelectRow.SendAllArguments := True;
  JQGridOptions.SelectTopRowOnLoad := false;
  JQGridOptions.ScrollRows := True;
  JQGridOptions.PagerVisible := false;
  JQGridNav.Active := false;
  JQGridToolbarSearch.Active := True;
  JQGridToolbarSearch.DefaultSearch := gsoContains;
  JQGridOptions.IgnoreCase := True;
  JQGridProvider := AProvider;

  //Warten Icon anzeigen, beim Doppel-Klick
 // AsyncShowWaitWheel;
  var
  LDataset := Dataset;
  Assert(LDataset <> nil, 'Provider hat kein Dataset!');
  // Falls der Dataset kein Key hat, dann versuchen wir das erste Feld zu nehmen
  if (AProvider.KeyFields = '') and (LDataset.FieldCount > 0) then
  begin
    AProvider.KeyFields := LDataset.Fields[0].FieldName;
  end;
  // Case-insensitive
  LDataset.FilterOptions := LDataset.FilterOptions + [foCaseInsensitive];

  // alle Spalten sortierbar machen
  for var LColumn in JQGridColumns do
  begin
    if not(TIWCGJQGridCol(LColumn).Formatter in [gcfActions, gcfControl, gcfButtons, gcfImage]) then
    begin
      TIWCGJQGridCol(LColumn).sortable := True;
    end
    else
    begin
      TIWCGJQGridCol(LColumn).sortable := false;
    end;
  end;

  JQGridOptions.GridView := True;
  OnSort := GridOnSort;

  // Datumsformat korrekt einstellen
  for var i := 0 to JQGridColumns.Count - 1 do
  begin
    var
    LColumn := JQGridColumns.Items[i];
    case LColumn.Formatter of
      gcfDate:
        LColumn.FormatOptionsDate.NewFormatDelphi := FormatSettings.ShortDateFormat;
      gcfDateTime:
      //Achtung: TFormatSettings liefert seit einiger Zeit  dd.mm.yyyy hh:mm  <- Also Minuten in "mm"
      //FormatDateTime kann Minuten in "mm" UND "nn" verarbeiten
      //JCDDevTools interne DelphiToPHPFormat Routine erwartet die Minuten als "nn"!!!
      //(Groß/Klein ist egal)
        LColumn.FormatOptionsDate.NewFormatDelphi :=
          FormatSettings.ShortDateFormat + ' ' + FormatSettings.ShortTimeFormat.Replace('m', 'n');
    end;
  end;

  // Die Title/Header auf Auto Word-wrap setzen
  SetTitleAutoWrap;

end;

function TIWCGJQGridHelper.Dataset: TFDDataSet;
begin
  Result := nil;
  if (JQGridProvider <> nil) then
  begin
    Assert(JQGridProvider is TIWCGJQGridDatasetProvider);
    if TIWCGJQGridDatasetProvider(JQGridProvider).Dataset <> nil then
    begin
      Assert(TIWCGJQGridDatasetProvider(JQGridProvider).Dataset is TFDDataSet);
      Result := TFDDataSet(TIWCGJQGridDatasetProvider(JQGridProvider).Dataset);
    end;
  end
end;

function TIWCGJQGridHelper.GetIndicatorInstance: TIWCGJQLockIndicator;
var
  LIndicator: TIWCGJQLockIndicator;
begin
  LIndicator := nil;
  var LWebApp :=gGetWebApplicationThreadVar;

  for var i := 0 to LWebApp.ActiveForm.ComponentCount - 1 do
  begin
    if LWebApp.ActiveForm.Components[i] is TIWCGJQLockIndicator then
    begin
      LIndicator := TIWCGJQLockIndicator(LWebApp.ActiveForm.Components[i]);
      break
    end;
  end;

  if not Assigned(LIndicator) then
  begin
    for var i := 0 to GServerController.ComponentCount - 1 do
    begin
      if GServerController.Components[i] is TIWCGJQLockIndicator then
      begin
        LIndicator := TIWCGJQLockIndicator(GServerController.Components[i]);
        break
      end;
    end;
  end;

  result := LIndicator;
end;

procedure TIWCGJQGridHelper.GridOnSort(Sender: TObject; ASortItems: TIWCGJQGridSortItems);
begin
  // Hier kommt der Feldname/Indexname nach dem nun sortiert werden soll
  var
  LDataset := Dataset;
  if (ASortItems.Count > 0) and (LDataset <> nil) then
  begin
    LDataset.IndexFieldNames := '';
    var
    LSortName := ASortItems.Items[0].Name;
    // Nur sortieren, wenn es das Feld wirklich gibt
    if LDataset.FindField(LSortName) <> nil then
    begin
      LSortName := LSortName + ':N';
      if ASortItems.Items[0].Order <> gsoAsc then
      begin
        LSortName := LSortName + 'D';
      end;
      LDataset.IndexFieldNames := LSortName;
    end;
  end;
end;

function TIWCGJQGridHelper.Provider: TIWCGJQGridDatasetProvider;
begin
  Assert(JQGridProvider is TIWCGJQGridDatasetProvider, 'Provider ist kein DataSetProvider');
  Result := TIWCGJQGridDatasetProvider(JQGridProvider);
end;

procedure TIWCGJQGridHelper.Refresh;
begin
  Assert(Provider <> nil, 'Grid hat keinen Provider');
  Assert(Provider.Dataset <> nil, 'Provider hat keinDataset');
  if Provider.Dataset.Active then
  begin
    Provider.Dataset.Refresh;
    JQGridOptions.ReloadGrid;
  end;
end;

{ TIWCGJQGridDatasetProviderHelper }

procedure TIWCGJQGridDatasetProviderHelper.ResetDataset;
begin
  if Assigned(Dataset) then
    Dataset.Filtered := false;
end;

end.
