unit JQ.Helpers.FormatSettings;

interface

uses
  System.Classes, System.SysUtils, IWCGJQDatePicker, IWContainer;

type
  TJQFormatSettings = class(TObject)
  public
    class
      procedure FixFormatSettings(AContainer: TIWContainer);
  end;

implementation

{ TJQFormatSettings }

class procedure TJQFormatSettings.FixFormatSettings(AContainer: TIWContainer);
begin
  for var i := 0 to AContainer.ControlCount - 1 do
  begin
    if AContainer.Controls[i] is TIWCGJQCustomDatePicker then
    begin
      TIWCGJQCustomDatePicker(AContainer.Controls[i]).JQDatePickerOptions.DateFormat := FormatSettings.ShortDateFormat;
    end
    else if AContainer.Controls[i] is TIWContainer then
    begin
      FixFormatSettings(TIWContainer(AContainer.Controls[i]));
    end;
  end;
end;

end.
