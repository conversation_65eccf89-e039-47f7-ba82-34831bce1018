object AppMetaData: TAppMetaData
  VersionControlPath = '%projectdir%\versions'
  DataDictionary.DatabaseTypeID = 'mssql2016'
  DataDictionary.NextTableID = 88
  DataDictionary.NextFieldID = 580
  DataDictionary.NextConstraintID = 1
  DataDictionary.NextIndexID = 12
  DataDictionary.NextRelationshipID = 37
  DataDictionary.Domains = <>
  DataDictionary.Tables = <
    item
      TableName = 'ADRESSEN'
      TID = 1
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 189
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 190
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PLZ'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          Required = True
          FID = 191
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STRASSE'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          Required = True
          FID = 192
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_STAMMDATEN_ADRESSEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ANSPRECHPARTNER'
      TID = 2
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 1
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_APTYP'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 2
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_PERSONEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 3
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          Required = True
          FID = 4
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK__ANSPRECH__3214EC271BCE7BB7'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'APTYP'
      TID = 3
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 193
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = True
          FID = 194
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK__APTYP__3214EC2763F7E68A'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'AUFSICHTSORGAN'
      TID = 4
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 5
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KUERZEL'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 6
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__AUFSICHTS__KUERZ__0F975522'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 7
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__AUFSICHTS__BEZEI__108B795B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 8
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__AUFSICHTS__BLDCO__117F9D94'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 9
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__AUFSICHTS__AKTIV__1273C1CD'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 10
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__AUFSICHTS__SICHT__1367E606'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_AUFSICHTSORGAN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BETRIEBE'
      TID = 5
      OidIndex = 0
      Fields = <
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 195
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'NAME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 196
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__NAME__164452B1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STRASSE'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 197
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__STRASS__173876EA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PLZ'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 198
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__PLZ__182C9B23'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 199
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__ORT__1920BF5C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GEMNR'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 200
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__GEMNR__1A14E395'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KATASTRALGEMNAME'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 201
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__KATAST__1B0907CE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AUFSICHTSORGAN'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 202
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__AUFSIC__1BFD2C07'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TELEFON'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 203
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__TELEFO__1CF15040'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'EMAIL'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 204
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__EMAIL__1DE57479'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VERGEBUEHRUNG'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 205
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__VERGEB__1ED998B2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 206
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBE__BLDCOD__1FCDBCEB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VULGO'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = False
          FID = 207
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANMERKUNG'
          DataTypeName = 'NVarChar'
          Size = 500
          Size2 = 0
          Required = False
          FID = 208
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BVBKZ'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = False
          FID = 209
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BETRIEBE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BETRIEBSBEREICHE'
      TID = 6
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 210
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 20
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 211
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__BEZEI__4460231C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 212
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__AKTIV__45544755'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 213
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__SICHT__46486B8E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'01.01.1990'#39')'
          Required = False
          FID = 214
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__BEGDA__473C8FC7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'31.12.2199'#39')'
          Required = False
          FID = 215
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__ENDDA__4830B400'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SORTKZ'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '('#39'AAAAA'#39')'
          Required = True
          FID = 216
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__SORTK__4924D839'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_SYS$MODUL'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 217
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSB__ID_SY__4A18FC72'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BETRIEBSBEREICHE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BETRIEBSTYPEN_TIERART'
      TID = 7
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 218
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TIERART'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          DefaultValue = '('#39'XX'#39')'
          Required = False
          FID = 219
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBST__TIERA__2B3F6F97'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BETRIEBSTYPEN_TIERART'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BEWERTUNGEN'
      TID = 8
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 220
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZECIHNUNG'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 221
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BEWERTUNG__BEZEC__4CF5691D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BEWERTUNGEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BEWERTUNGSPUNKTE'
      TID = 9
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 222
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BEWERTUNGEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 223
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEWERTUNG'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 224
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 250
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 225
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BEWERTUNG__BEZEI__30F848ED'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZUSATZTEXT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 226
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BEWERTUNG__ID_ZU__31EC6D26'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BRAUCHT_BESCHREIB'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 227
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BEWERTUNG__BRAUC__32E0915F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRAFIK'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 228
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'FK_ID_BEWERTUNGEN'
          IndexType = itNone
          IndexOrder = ioAscending
          IID = 1
          IFields = <
            item
              FieldIndex = 1
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BEWERTUNGSPUNKTE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BKBTYPEN'
      TID = 10
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BKBTYP'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 229
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 230
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BKBTYPEN__BEZEIC__35BCFE0A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BKBTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BUNDESLAENDER'
      TID = 11
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 412
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 413
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDE__BEZEI__6EC0713C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZTEXT'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 414
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDE__KURZT__6FB49575'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ELKE'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = True
          FID = 415
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDES__ELKE__70A8B9AE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HCM'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = True
          FID = 416
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDESL__HCM__719CDDE7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGION'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 417
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDE__REGIO__72910220'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANDKZ'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 418
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$BUNDE__LANDK__73852659'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDLOGO'
          DataTypeName = 'Image'
          Size = 0
          Size2 = 0
          Required = False
          FID = 419
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$BUNDESLAND'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BUNDESLAENDER_MODULE'
      TID = 12
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 420
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MODUL_ID'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 421
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_BUNDESLAENDER_MODULE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'BVB'
      TID = 13
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BVBKZ'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 231
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = True
          FID = 232
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK__BVB__2642DB48B662CD69'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'CHECKLISTEN'
      TID = 14
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 233
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 21
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MODUL'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 234
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__ID_SY__6E2152BE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = False
          FID = 235
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 
            'Wenn angegeben, dann wird diese Checkliste nur f'#252'r das jeweilige' +
            ' Bundesland verwendet. '#13#10'Wenn nicht angegeben, dann gilt die Che' +
            'ckliste f'#252'r alle (anderen) Bundeslander (die das zugeh'#246'rige Modu' +
            'l haben)'
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 236
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__BEZEI__68687968'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 237
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__AKTIV__695C9DA1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 238
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__SICHT__6B44E613'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 239
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__BEGDA__6C390A4C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2199-12-31'#39')'
          Required = True
          FID = 240
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__ENDDA__6D2D2E85'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_CHECKLISTEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'CHECKLISTENPUNKT'
      TID = 15
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 241
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_CHECKLISTE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 242
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHUNG'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          Required = True
          FID = 243
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANGTEXT'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 244
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__LANGT__7C6F7215'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BEWERTUNG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 245
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__CHECKLIST__ID_BE__7D63964E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_CHECKLISTENPUNKT'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'CHECKP_FRAGE'
      TID = 16
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 246
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 15
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_CHECKLISTENPUNKT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 247
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FRAGENSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 248
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_CHECKP_FRAGE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'EH_TAETIGKEITEN'
      TID = 17
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 11
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 12
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_TAETIGKEITEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 13
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'01.01.1990'#39')'
          Required = True
          FID = 14
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__EH_TAETIG__BEGDA__6A85CC04'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = True
          FID = 15
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__EH_TAETIG__ENDDA__6B79F03D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_EH_TAETIGKEITEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ESADOMWERTE'
      TID = 18
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 422
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CODE'
          DataTypeName = 'VarChar'
          Size = 20
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 423
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ESADOMWERT__CODE__16644E42'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TEXT'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 424
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ESADOMWERT__TEXT__1758727B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'DOM'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 425
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ESADOMWERTE__DOM__184C96B4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_ESA$DOMWERT'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FRAGEN_TAETGR'
      TID = 19
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 249
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_TAETIGGRUPPE'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 250
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FRAGENSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 251
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_FRAGEN_TAETGR'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FRAGENGRUPPEN'
      TID = 20
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 252
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 253
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__BEZEI__5165187F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FARBCODE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 254
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__FARBC__52593CB8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = True
          FID = 255
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__SICHT__534D60F1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = True
          FID = 256
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__AKTIV__5441852A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 257
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__BLDCO__5535A963'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = False
          FID = 258
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__BEGDA__5629CD9C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2199-12-31'#39')'
          Required = False
          FID = 259
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__ENDDA__571DF1D5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SORTKZ'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '('#39'AAAAA'#39')'
          Required = True
          FID = 260
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__SORTK__5812160E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_HAUPTGRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 261
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__ID_HA__59063A47'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'Zusatztext'
          DataTypeName = 'VarChar(MAX)'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 262
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENGRU__ID_ZU__59FA5E80'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'FK_ID_HAUPTGRUPPE'
          IndexType = itNone
          IndexOrder = ioAscending
          IID = 2
          IFields = <
            item
              FieldIndex = 9
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_FRAGENGRUPPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FRAGENHAUPTGRUPPEN'
      TID = 21
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 263
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 200
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 264
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__BEZEI__5F141958'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FARBCODE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 265
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__FARBC__60083D91'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 266
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__AKTIV__60FC61CA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 267
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__SICHT__61F08603'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = False
          FID = 268
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__BEGDA__62E4AA3C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2199-12-31'#39')'
          Required = False
          FID = 269
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__ENDDA__63D8CE75'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SORTKZ'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '('#39'AAAAA'#39')'
          Required = True
          FID = 270
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__SORTK__64CCF2AE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 271
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__BLDCO__65C116E7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZUSATZTEXT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 272
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENHAU__ID_ZU__66B53B20'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_SYS$MODUL'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 273
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_FRAGENHAUPTGRUPPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FRAGENSTAMM'
      TID = 22
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 274
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_HAUPTGRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 275
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRUPPE'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 276
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LFNR'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 277
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAM__LFNR__50C5FA01'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FRAGENNR'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 278
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTA__FRAGE__51BA1E3A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FRAGE'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 279
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTA__FRAGE__52AE4273'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BWERTUNG'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 280
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTA__BWERT__53A266AC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VERSTOSS'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 281
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTA__VERST__54968AE5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AN'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 282
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__AN__558AAF1E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HSB'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 283
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__HSB__567ED357'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GSB'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 284
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__GSB__5772F790'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZB'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 285
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__ZB__58671BC9'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FZS'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 286
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__FZS__595B4002'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VB'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 287
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__VB__5A4F643B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'WBB'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 288
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FRAGENSTAMM__WBB__5B438874'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'FK_ID_GRUPPE'
          IndexType = itNone
          IndexOrder = ioAscending
          IID = 3
          IFields = <
            item
              FieldIndex = 2
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_FRAGENSTAMM'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FUNKTIONEN'
      TID = 23
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 426
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PROGMODUL'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 427
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 428
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HINTTEXT'
          DataTypeName = 'VarChar'
          Size = 200
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 429
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__HINTT__68536ACF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MUTTER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 430
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__ID_MU__69478F08'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FARBCODE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 431
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__FARBC__6A3BB341'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 432
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__BEGDA__6B2FD77A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2999-12-31'#39')'
          Required = True
          FID = 433
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__ENDDA__6C23FBB3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '('#39'N'#39')'
          Required = True
          FID = 434
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__SICHT__6D181FEC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PROGCALLID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 435
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__PROGC__6E0C4425'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 436
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'OBJEKT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 437
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONE__OBJEK__6F00685E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PRGTEIL'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = False
          FID = 438
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$FUNKTIONEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'FUNKTIONSROLLEN'
      TID = 24
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID_ROLLE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 439
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FUNKTION'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 440
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 441
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONS__BEGDA__71DCD509'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2999-12-31'#39')'
          Required = True
          FID = 442
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__FUNKTIONS__ENDDA__72D0F942'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_FUNKTIONSROLLEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'GEMEINDEN'
      TID = 25
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 289
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GEMEINDEKENNZIFFER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 290
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GEMEINDENAME'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          Required = True
          FID = 291
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GEMEINDECODE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 292
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AMTSPLZ'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 293
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BUNDLANDKENNZIFFER'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 294
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BUNDESLAND'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          Required = True
          FID = 295
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'POL_BEZ_KENNZIF'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 296
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'POL_BEZIRK'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          Required = True
          FID = 297
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'POL_BEZ_CODE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 298
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LAND_ISO2'
          DataTypeName = 'NChar'
          Size = 2
          Size2 = 0
          Required = True
          FID = 299
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BUNDESLANDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = False
          FID = 300
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey25'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'GRUPPEN'
      TID = 26
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 443
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 13
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 444
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$GRUPP__BEZEI__078C1F06'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 445
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MUTTERGRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 446
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$GRUPP__MUTTE__0880433F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_USER_HAUPTVER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 447
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_USER_STELLVER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 448
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'OKZ'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          Required = False
          FID = 449
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$GRUPPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_BETRIEBSBEREICHE'
      TID = 27
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 16
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_BETRIEBSTYPEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 17
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 18
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BETRIEBS__BKB__02284B6B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BETRIEBSBEREICH'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 19
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BETRIE__ID_BE__031C6FA4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_BETRIEBSBEREICHE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_BETRIEBSTYPEN'
      TID = 28
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 20
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 2
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 21
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BETRIEBS__BKB__05F8DC4F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 22
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_BETRIEBSTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_BEWERTUNGEN'
      TID = 29
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 23
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_FRAGEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 24
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 25
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BEWERTUG__BKB__08D548FA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BEWERTUNGSPUNKT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 26
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KOMMENTAR'
          DataTypeName = 'VarChar'
          Size = 800
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 27
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BEWERT__KOMME__09C96D33'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEWERTUNG'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 28
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BEWERT__BEWER__0ABD916C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZUSATZDOKUMENTE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 29
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_BEWERT__ZUSAT__0BB1B5A5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_BEWERTUGEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_FESTSTELLUNGEN'
      TID = 30
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 30
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 31
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FESTSTEL__BKB__0E8E2250'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MANGELTYP'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 32
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BEWERTUNGSPUNKT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 33
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FESTST__ID_BE__0F824689'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZBESCHREIBUNG'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 34
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FESTST__KURZB__10766AC2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STATUS'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 35
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FESTST__STATU__116A8EFB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZUSATZTEXT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 36
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FESTST__ID_ZU__125EB334'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_FESTSTELLUNGEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_FRAGE'
      TID = 31
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 37
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FRAGENSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 38
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STATUS'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 39
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__STATUS__153B1FDF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_FESTSTELLUNG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 40
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__ID_KB___162F4418'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 41
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__BKB__17236851'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_KONTTROLLGEGENSTAND'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 42
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__ID_KB___18178C8A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_BETRIEBSBEREICH'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 43
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__ID_KB___190BB0C3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEMERKUNG'
          DataTypeName = 'VarChar'
          Size = 250
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 44
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGE__BEMERK__19FFD4FC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_FRAGE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_FRAGEN'
      TID = 32
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 45
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KONTROLLBERICHT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 46
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          Required = True
          FID = 47
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FRAGENSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 48
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FRAGENNR'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 49
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VERSION_FRAGENSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 50
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_FRAGEN__VERSI__0F624AF8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_FRAGEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_KONTROLLGEGENSTAND'
      TID = 33
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 51
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 52
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_KONTROLL__BKB__123EB7A3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_BETRIEBSTYPEN'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 53
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KGEGENSTAND'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 54
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_KONTROLLGEGENSTAND'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_MASZNAHME'
      TID = 34
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 55
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          Required = True
          FID = 56
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_KB_FESTSTELLUNG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 57
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_MASZNA__ID_KB__1CDC41A7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MASZNAHME'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 58
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STATUS'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 59
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_MASZNA__STATU__1DD065E0'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FRIST_DATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 60
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_MASZNA__FRIST__1EC48A19'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CLOSE_DATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 61
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_MASZNA__CLOSE__1FB8AE52'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZUSATZTEXT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 62
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_MASZNA__ID_ZU__20ACD28B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KB_MASZNAHME'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KB_PERSONEN'
      TID = 35
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 63
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          Required = True
          FID = 64
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = False
          FID = 65
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FUNKTION'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = False
          FID = 66
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HAUPTVERANTWORTLICH'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = True
          FID = 67
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_PERSON__HAUPT__3B2BBE9D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KOMUNIKATIONSBERECHTIGT'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = True
          FID = 68
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KB_PERSON__KOMUN__3C1FE2D6'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 
            '1 = Die Person erh'#228'lt Berichte und Meldungen zu dieser Kontrolle' +
            #13#10'0 = Person erh'#228'lt keine Berichte udg von dieser Kontrolle'
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK__KB_PERSO__3214EC27C0CE0167'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KONTROLLBERICHT'
      TID = 36
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 69
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          Required = True
          FID = 70
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKBTYP'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 71
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'DATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 72
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__DATUM__1EF99443'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ERFASSER_PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 73
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ERFAS__1FEDB87C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KONTROLLORGAN_PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 74
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__KONTR__20E1DCB5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REF_BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 75
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__REF_B__21D600EE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KONTROLLTYP'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 76
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__KONTR__22CA2527'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANGEMELDET'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 77
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ANGEM__23BE4960'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PROBENZIEHUNG'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 78
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__PROBE__24B26D99'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR_ORT'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 79
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__REGNR__25A691D2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZBEMERKUNG'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 80
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__KURZB__269AB60B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANWESEN_BETRIEBSAN'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 81
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ANWES__278EDA44'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANWESEN_FUNKTION'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 82
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ANWES__2882FE7D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STARTZEIT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 83
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__START__297722B6'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDEZEIT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 84
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ENDEZ__2A6B46EF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BESTAETIGT_BETRIEB'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 85
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__BESTA__2B5F6B28'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BESTAETIGT_TSTAMP'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 86
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__BESTA__2C538F61'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VERWEIGERUNGGRUND'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 87
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__VERWE__2D47B39A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_RECHTSGRUNDLAGE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 88
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__ID_RE__2E3BD7D3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STATUS'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 89
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLB__STATU__2F2FFC0C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANGEMELDET_UM'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 90
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TSTAMP_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 91
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_KONTROLLBERICHT_TSTAMP_INSERT'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LASTCHANGE'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 92
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_KONTROLLBERICHT_LASTCHANGE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KONTROLLBERICHT'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KONTROLLGEGENSTAND'
      TID = 37
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 301
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 100
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MODUL'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 302
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_FRAGNGRUPPE'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 303
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLG__ID_FR__789EE131'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 304
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLG__BEZEI__7993056A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KONTROLLGEGENSTAND'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'KONTROLLTYPEN'
      TID = 38
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BKBTYP'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 305
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KONTROLLTYP'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 306
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 307
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLT__BEZEI__339FAB6E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REFBKB'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 308
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__KONTROLLT__REFBK__3493CFA7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_KONTROLLTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'LAENDER'
      TID = 39
      OidIndex = 0
      Fields = <
        item
          FieldName = 'LANDKZ'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 450
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANDNR'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 451
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LAND_ISO3'
          DataTypeName = 'NChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 452
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'NVarChar'
          Size = 100
          Size2 = 0
          Required = True
          FID = 453
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LaenderNeu'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'LOG'
      TID = 40
      OidIndex = 0
      Fields = <
        item
          FieldName = 'APPLICATION'
          DataTypeName = 'VarChar'
          Size = 260
          Size2 = 0
          Required = True
          FID = 145
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID'
          DataTypeName = 'UniqueIdentifier'
          Size = 0
          Size2 = 0
          Required = True
          FID = 146
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LOG_LEVEL'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          Required = True
          FID = 147
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TIMESTAMP'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = True
          FID = 148
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LOGGING_LOG'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'LOGGING'
      TID = 41
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ZEITPUNKT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = True
          FID = 454
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__LOGGING__ZEITPUN__1A34DF26'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 250
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 455
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__LOGGING__BEZEICH__1B29035F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 456
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey41'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'MAENGEL'
      TID = 42
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 309
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKB'
          DataTypeName = 'VarChar'
          Size = 26
          Size2 = 0
          Required = True
          FID = 310
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MANGELKURZEL'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 311
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MANGETYP_TEXT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 312
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__MANGETY__3CBF0154'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MASZNAHME'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 313
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__ID_MASZ__3DB3258D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MASZNAHME_TEXT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 314
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__MASZNAH__3EA749C6'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BESCHREIBUNG'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 315
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__BESCHRE__3F9B6DFF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_BETRIEBSBEREICH'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 316
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__ID_BETR__408F9238'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSBEREICH_TEXT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 317
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGEL__BETRIEB__4183B671'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_MAENGEL'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'MAENGELTYPEN'
      TID = 43
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 318
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MANGELKURZEL'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 319
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKBTYP'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 320
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 321
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGELTY__BEZEI__3F115E1A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MASZNAHMENKATALOG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 322
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MAENGELTY__ID_MA__40058253'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_MAENGELTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'MASZNAHMENKATALOG'
      TID = 44
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 323
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 324
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MASZNAHME__BEZEI__42E1EEFE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_MASZNAHMENKATALOG'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'MASZNAHMENKATALOG_DETAIL'
      TID = 45
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 325
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_MASZNAHMENKATALOG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 326
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 327
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MASZNAHME__BEZEI__45BE5BA9'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANGTEXT'
          DataTypeName = 'VarChar'
          Size = 300
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 328
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MASZNAHME__LANGT__46B27FE2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FRIST'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 329
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MASZNAHME__FRIST__47A6A41B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_MASZNAHMENKATALOG_DETAIL'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'MESSAGES'
      TID = 46
      OidIndex = 0
      Fields = <
        item
          FieldName = 'LOG_ID'
          DataTypeName = 'UniqueIdentifier'
          Size = 0
          Size2 = 0
          Required = True
          FID = 149
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MESSAGE'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 150
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LOGGING_MESSAGES'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'MODULE'
      TID = 47
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'SmallInt (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 457
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 5
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZTEXT'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 458
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__KURZTEXT__70FDBF69'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 459
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__BEZEICHN__71F1E3A2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TSTAMP_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 460
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__TSTAMP_I__72E607DB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'INS_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 461
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__INS_DBUS__73DA2C14'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LASTCHANGE'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 462
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__LASTCHAN__74CE504D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CHANGE_USER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 463
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__MODULE__CHANGE_U__75C27486'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BKBTYP'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = False
          FID = 464
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$MODULE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'NACHRICHTEN'
      TID = 48
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 93
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 94
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STATUS'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '('#39'O'#39')'
          Required = True
          FID = 95
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_NACHRICHTEN_STATUS'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 'Status '#39'O'#39' = Offen'
        end
        item
          FieldName = 'TYP'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '('#39'T'#39')'
          Required = True
          FID = 96
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_NACHRICHTEN_NTYP'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 'Nachrichtentyp T=Text'
        end
        item
          FieldName = 'ABSENDER_KZ'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '('#39'S'#39')'
          Required = True
          FID = 97
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_NACHRICHTEN_ABSENDER_KZ'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 'S=System, U=User'
        end
        item
          FieldName = 'PRIORI'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((3))'
          Required = True
          FID = 98
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF_NACHRICHTEN_PRIORI'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 
            'Priorit'#228't 5 = mittel 1 = drigend-> muss sofort gelesen werden 10' +
            ' = unwichtig'
        end
        item
          FieldName = 'ID_ABSENDER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 99
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
          FieldCaption = 'Bei Absender_KZ = U -> ID_USER'
        end
        item
          FieldName = 'TEXT'
          DataTypeName = 'NVarChar'
          Size = 1000
          Size2 = 0
          Required = False
          FID = 100
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LINK'
          DataTypeName = 'VarChar'
          Size = 200
          Size2 = 0
          Required = False
          FID = 101
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_NACHRICHTEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'NACHRICHTEN_ZUSTELLUNG'
      TID = 49
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 102
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_NACHRICHT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 103
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_USER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 104
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESEHEN_TSTAMP'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 105
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GELESEN_TSTAMP'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 106
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_NACHRICHTEN_ZUSTELLUNG'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'PERSONEN'
      TID = 50
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 330
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1000
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PRAEFIX'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 331
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSNUMM'
          DataTypeName = 'Char'
          Size = 5
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 332
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__PSNUMM__062DE679'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 333
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__PSKEY__07220AB2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSGUID'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          Required = True
          FID = 334
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TITEL'
          DataTypeName = 'VarChar'
          Size = 20
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 335
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__TITEL__08162EEB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VORNAME'
          DataTypeName = 'VarChar'
          Size = 60
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 336
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__VORNAM__090A5324'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'NACHNAME'
          DataTypeName = 'VarChar'
          Size = 60
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 337
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__NACHNA__09FE775D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ADRESSE'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 338
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__ADRESS__0AF29B96'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 339
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__ORT__0BE6BFCF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PLZ'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 340
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__PLZ__0CDAE408'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANDKZ'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 341
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__LANDKZ__0DCF0841'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TELEFON'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 342
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__TELEFO__0EC32C7A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'EMAIL'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 343
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__EMAIL__0FB750B3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TSTAMP_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 344
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__TSTAMP__10AB74EC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'INS_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 345
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__INS_DB__119F9925'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LASTCHANGE'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 346
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__LASTCH__1293BD5E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CHANGE_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 347
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__PERSONEN__CHANGE__1387E197'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'IDX_PERSONEN'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 4
          IFields = <
            item
              FieldIndex = 3
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_PERSONEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'PROGRAMM_MODULE'
      TID = 51
      OidIndex = 0
      Fields = <
        item
          FieldName = 'KURZBEZEICHNNUNG'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 465
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BESCHREIBUNG'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          Required = True
          FID = 466
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_PROGRAMM_MODULE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'PVP_TOKEN'
      TID = 52
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 151
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BIRTHDATE'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 152
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BPK'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 153
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CHARGE_CODE'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 154
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'COST_CENTER_ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 155
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENC_BPK_LIST'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 156
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FUNCTION_'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 157
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 158
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GIVEN_NAME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 159
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'INVOICE_RECPT_ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 160
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MAIL'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 161
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORIG_HOST'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 162
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORIG_SCHEME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 163
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORIG_URI'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 164
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'OU'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 165
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'OU_GV_OU_ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 166
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'OU_OKZ'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 167
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PARTICIPANT_ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 168
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PARTICIPANT_OKZ'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 169
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PRINCIPAL_NAME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 170
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PVP_BINDING'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 171
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PVP_VERSION'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 172
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ROLES'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 173
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SECCLASS'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 174
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TEL'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 175
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TXID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 176
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 177
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LOGGING_PVP_TOKEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'RECHTSGRUNDLANGEN'
      TID = 53
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 348
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 300
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 349
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__RECHTSGRU__BEZEI__5BAD9CC8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZBEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 350
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__RECHTSGRU__KURZB__5CA1C101'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_RECHTSGRUNDLANGEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'REQUEST_HEADERS'
      TID = 54
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 178
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'NAME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 179
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VALUE'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 180
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HEADERS_LOG_ITEM_REQUEST_LOG_ID'
          DataTypeName = 'UniqueIdentifier'
          Size = 0
          Size2 = 0
          Required = False
          FID = 181
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LOGGING_REQUEST_HEADERS'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'REQUESTS'
      TID = 55
      OidIndex = 0
      Fields = <
        item
          FieldName = 'LOG_ID'
          DataTypeName = 'UniqueIdentifier'
          Size = 0
          Size2 = 0
          Required = True
          FID = 182
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BODY'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = False
          FID = 183
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MESSAGE_LOG_ID'
          DataTypeName = 'UniqueIdentifier'
          Size = 0
          Size2 = 0
          Required = False
          FID = 184
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'METHOD'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 185
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PVPTOKEN_ID'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = False
          FID = 186
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REMOTE_IP'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 187
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'URL'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          Required = True
          FID = 188
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_LOGGING_REQUESTS'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'REVISIONSPLAN'
      TID = 56
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 107
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_REVISIONSSTAMM'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 108
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'JAHR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((2019))'
          Required = False
          FID = 109
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONSP__JAHR__5F7E2DAC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'RISIKO_KATEGORIE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 110
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__RISIK__607251E5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'J_MINDEST_KONTROLL_FREQUENZ'
          DataTypeName = 'Real'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 111
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__J_MIN__6166761E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANZ_BETRIEBE_IM_LAND'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 112
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__ANZ_B__625A9A57'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ANZ_GESAMT_KONTROLLEN'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 113
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__ANZ_G__634EBE90'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_REVISIONSPLAN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'REVISIONSSTAMM'
      TID = 57
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 114
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 115
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__BLDCO__2C1E8537'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SEKTION'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 116
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__SEKTI__2D12A970'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSGRUPPE_LM'
          DataTypeName = 'VarChar'
          Size = 20
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 117
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__BETRI__2E06CDA9'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSGRUPPE_DETAIL'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 118
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__BETRI__2EFAF1E2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSART'
          DataTypeName = 'VarChar'
          Size = 250
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 119
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__REVISIONS__BETRI__2FEF161B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_REVISIONSSTAMM'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ROLLEN'
      TID = 58
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 467
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 468
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'DEFAULTROLLE'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'0'#39')'
          Required = True
          FID = 469
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$ROLLE__DEFAL__1F63A897'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'Bit'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1'#39')'
          Required = True
          FID = 470
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$ROLLE__SICHT__2057CCD0'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GUELTIG_AB'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 471
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$ROLLE__BEGDA__214BF109'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 472
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PARAMETER'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          Required = False
          FID = 473
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$ROLLEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'SYS'
      TID = 59
      OidIndex = 0
      Fields = <
        item
          FieldName = 'VERSION_DB'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 474
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS__VERSION_DB__6CD828CA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey59'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'TAETIG_BETRTYP'
      TID = 60
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID_TAETIGKEIT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 351
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 352
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_TAETIGKEIT_N_ZUGL'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 353
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIG_BE__ID_TA__477199F1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'IDX_TAETIG_BETRTYP'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 5
          IFields = <
            item
              FieldIndex = 0
              FieldOrder = ioAsc
              KeyByRelationship = False
            end
            item
              FieldIndex = 1
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end
        item
          IndexName = 'IDX_TAETIG_BETRTYP1'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 6
          IFields = <
            item
              FieldIndex = 1
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey60'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'TAETIGGRP_BETRTYP'
      TID = 61
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID_TAETIGKEITSGRP'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 354
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 355
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'IDX_TAETIGGRP_BETRTYP'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 7
          IFields = <
            item
              FieldIndex = 0
              FieldOrder = ioAsc
              KeyByRelationship = False
            end
            item
              FieldIndex = 1
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey61'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'TAETIGKEITEN'
      TID = 62
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 356
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KURZ_BEZ'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = True
          FID = 357
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 358
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__BEZEI__4D2A7347'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULASSUNGSPFL'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 359
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__ZULAS__4E1E9780'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 360
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__BLDCO__4F12BBB9'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 361
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__AKTIV__5006DFF2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 362
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__SICHT__50FB042B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VISQUELLE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 363
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__VISQU__51EF2864'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'IDX_TAETIGKEITEN'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 8
          IFields = <
            item
              FieldIndex = 1
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_TAETIGKEITEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'TAETIGKEITSGRUPPEN'
      TID = 63
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Char'
          Size = 3
          Size2 = 0
          Required = True
          FID = 364
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 250
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 365
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__BEZEI__55BFB948'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 366
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__AKTIV__56B3DD81'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 367
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__SICHT__57A801BA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'01.01.1900'#39')'
          Required = False
          FID = 368
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__BEGDA__589C25F3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATE'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'31.12.2199'#39')'
          Required = False
          FID = 369
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__ENDDA__59904A2C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 370
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TAETIGKEI__BLDCO__5A846E65'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_SYS$MODUL'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 371
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_TAETIGKEITSGRUPPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'TERMINE'
      TID = 64
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = True
          FID = 120
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMINE__ID__251C81ED'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETREFF'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          Required = True
          FID = 121
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGINN_DATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          Required = True
          FID = 122
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'END_DATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 123
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__END_D__2704CA5F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORT'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 124
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMINE__ORT__28ED12D1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'WIEDERHOLEND'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 125
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__WIEDE__29E1370A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GANZTAEGIG'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 126
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__GANZT__2AD55B43'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TAGUEBERGREIFEND'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 127
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__TAGUE__2BC97F7C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FARBE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 128
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__FARBE__2CBDA3B5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'DETAILS'
          DataTypeName = 'VarChar'
          Size = 500
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 129
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__DETAI__2DB1C7EE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 130
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 131
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$TERMI__SICHT__2EA5EC27'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'END_ZEIT'
          DataTypeName = 'Time'
          Size = 0
          Size2 = 0
          Required = False
          FID = 132
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGINN_ZEIT'
          DataTypeName = 'Time'
          Size = 0
          Size2 = 0
          Required = False
          FID = 133
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 134
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$TERMINE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'TIERARTEN'
      TID = 65
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 372
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KZ'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 373
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 374
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VIS_TIERART'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          Required = True
          FID = 375
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          Required = True
          FID = 376
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 377
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TIERARTEN__SICHT__5D60DB10'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 378
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__TIERARTEN__AKTIV__5E54FF49'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          Required = True
          FID = 379
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          Required = True
          FID = 380
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_TIERARTEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'TIERARTEN_KATEG'
      TID = 66
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 381
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_TIERART'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 382
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VIS_KATEG'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          Required = True
          FID = 383
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_TIERARTEN_KATEG'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'TODO'
      TID = 67
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 135
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = False
          FID = 136
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TITEL'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 137
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 138
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FAELLIG'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 139
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_USER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = False
          FID = 140
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$TODO'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'USER'
      TID = 68
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 475
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 4
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERNAME'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          DefaultValue = '('#39'PVP_verwaltung'#39')'
          Required = False
          FID = 476
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__USERNAME__7869D707'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PASSWORT'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          DefaultValue = '('#39'PVP'#39')'
          Required = False
          FID = 477
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__PASSWORT__795DFB40'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERGUID'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 478
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__USERGUID__7A521F79'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERTYPE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 479
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__USERTYPE__7B4643B2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESPERRT'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 480
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__GESPERRT__7C3A67EB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '('#39'XX00000'#39')'
          Required = False
          FID = 481
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__PSKEY__7D2E8C24'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 482
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__BLDCODE__7E22B05D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERPWC'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 483
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__USERPWC__7F16D496'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 484
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__AKTIV__000AF8CF'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TSTAMP_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 485
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__TSTAMP_INS__00FF1D08'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'INS_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 486
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__INS_DBUSER__01F34141'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LASTCHANGE'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 487
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__LASTCHANGE__02E7657A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CHANGE_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          DefaultValue = '('#39'user'#39')'
          Required = False
          FID = 488
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__CHANGE_DBU__03DB89B3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'EMAIL'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 489
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__EMAIL__04CFADEC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LAST_LOGIN'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 490
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER__LAST_LOGIN__05C3D225'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'UK_USER_PERSONEN'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 9
          IFields = <
            item
              FieldIndex = 0
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end
        item
          IndexName = 'UK_USER_USERGUID'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 10
          IFields = <
            item
              FieldIndex = 3
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <
        item
          Name = 'USER_HISTROY_UTR'
          ImplementationCode = 
            'CREATE TRIGGER SYSTEMSTAMMDATEN.USER_HISTROY_UTR ON SYSTEMSTAMMD' +
            'ATEN.[USER]'#13#10'WITH EXECUTE AS CALLER'#13#10'FOR UPDATE'#13#10'AS'#13#10'BEGIN'#13#10#13#10'  ' +
            'INSERT INTO'#13#10'    SYSTEMSTAMMDATEN.USER_HIS'#13#10'  ('#13#10'    USERNAME,'#13#10 +
            '    PASSWORT,'#13#10'    USERGUID,'#13#10'    USERTYPE,'#13#10'    GESPERRT,'#13#10'    ' +
            'PSKEY,'#13#10'    BLDCODE,'#13#10'    USERPWC,'#13#10'    AKTIV,'#13#10'    TSTAMP_INSER' +
            'T,'#13#10'    INS_DBUSER,'#13#10'    LASTCHANGE,'#13#10'    CHANGE_DBUSER,'#13#10'    EM' +
            'AIL,'#13#10'    LAST_LOGIN,'#13#10'    ID_ORG,'#13#10'    AKTION_HISTORY,'#13#10'    HIS' +
            '_USER'#13#10'  )'#13#10'  (SELECT'#13#10'    USERNAME,'#13#10'    PASSWORT,'#13#10'    USERGUI' +
            'D,'#13#10'    USERTYPE,'#13#10'    GESPERRT,'#13#10'    PSKEY,'#13#10'    BLDCODE,'#13#10'    ' +
            'USERPWC,'#13#10'    AKTIV,'#13#10'    TSTAMP_INSERT,'#13#10'    INS_DBUSER,'#13#10'    L' +
            'ASTCHANGE,'#13#10'    CHANGE_DBUSER,'#13#10'    EMAIL,'#13#10'    LAST_LOGIN,'#13#10'   ' +
            ' ID, '#39'UPDATE'#39', CURRENT_USER FROM deleted)'#13#10#13#10#13#10'END'
        end>
      PrimaryKeyIndex.IndexName = 'PK_SYS$USER'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'USER_HIS'
      TID = 69
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 491
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 4
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERNAME'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          Required = False
          FID = 492
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PASSWORT'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          DefaultValue = '('#39'PVP'#39')'
          Required = False
          FID = 493
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__PASSWO__0D64F3ED'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERGUID'
          DataTypeName = 'VarChar'
          Size = 40
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 494
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__USERGU__0E591826'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERTYPE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 495
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__USERTY__0F4D3C5F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESPERRT'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 496
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__GESPER__10416098'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = False
          FID = 497
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 498
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__BLDCOD__1229A90A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'USERPWC'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 499
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__USERPW__131DCD43'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 500
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__AKTIV__1411F17C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TSTAMP_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 501
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'INS_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = False
          FID = 502
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LASTCHANGE'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 503
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CHANGE_DBUSER'
          DataTypeName = 'VarChar'
          Size = 30
          Size2 = 0
          Required = False
          FID = 504
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'EMAIL'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 505
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__EMAIL__18D6A699'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LAST_LOGIN'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          Required = False
          FID = 506
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ORG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 507
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTION_HISTORY'
          DataTypeName = 'VarChar'
          Size = 10
          Size2 = 0
          Required = False
          FID = 508
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TS_INSERT'
          DataTypeName = 'Datetime'
          Size = 0
          Size2 = 0
          DefaultValue = '(getdate())'
          Required = False
          FID = 509
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USER_HIS__TS_INS__1CA7377D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HIS_USER'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = False
          FID = 510
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$USER_HIS'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'USERGRUPPEN'
      TID = 70
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID_USER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 511
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_GRUPPE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 512
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 513
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$USERG__BEGDA__3FD07829'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2999-12-31'#39')'
          Required = True
          FID = 514
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__SYS$USERG__ENDDA__40C49C62'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_USERGRUPPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'USERROLLEN'
      TID = 71
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 515
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 10
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_USER'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 516
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ROLLE'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 517
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'1900-01-01'#39')'
          Required = True
          FID = 518
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USERROLLE__BEGDA__40457975'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'2999-12-31'#39')'
          Required = True
          FID = 519
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__USERROLLE__ENDDA__41399DAE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_SYS$USERROLLEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VB_STAMMDATEN'
      TID = 72
      OidIndex = 0
      Fields = <
        item
          FieldName = 'KZ'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 384
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 385
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VB_STAMMD__BEZEI__6319B466'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BLDCODE'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 386
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VB_STAMMD__BLDCO__640DD89F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'VBTYP'
          DataTypeName = 'Char'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 387
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VB_STAMMD__VBTYP__6501FCD8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PSKEY_KONTAKT'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 388
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VB_STAMMD__PSKEY__65F62111'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VB_STAMMDATEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VERGEBUEHRUNG'
      TID = 73
      OidIndex = 0
      Fields = <
        item
          FieldName = 'KUERZEL'
          DataTypeName = 'Char'
          Size = 4
          Size2 = 0
          Required = True
          FID = 389
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 390
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VERGEBUEH__BEZEI__68D28DBC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VERGEBUEHRUNG'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_BETRIEBSSTAMMDATEN'
      TID = 74
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 520
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STICHTAG'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 521
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__STICH__3D7E1B63'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 522
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__REGNR__3E723F9C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'NAME'
          DataTypeName = 'VarChar'
          Size = 255
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 523
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSST__NAME__3F6663D5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GEMNR'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 524
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__GEMNR__405A880E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KATASTRALGMNR'
          DataTypeName = 'VarChar'
          Size = 5
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 525
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__KATAS__414EAC47'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KATASTRALGEMNAME'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 526
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__KATAS__4242D080'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STRASSE'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 527
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__STRAS__4336F4B9'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'HNR'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 528
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSSTA__HNR__442B18F2'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'PLZ'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 529
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSSTA__PLZ__451F3D2B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORT'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 530
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSSTA__ORT__46136164'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TELEFON'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 531
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__TELEF__4707859D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'FAX'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 532
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSSTA__FAX__47FBA9D6'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'EMAIL'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 533
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__BETRIEBSS__EMAIL__48EFCE0F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <
        item
          IndexName = 'IDX_VIS$BETRIEBSSTAMMDATEN'
          IndexType = itUnique
          IndexOrder = ioAscending
          IID = 11
          IFields = <
            item
              FieldIndex = 2
              FieldOrder = ioAsc
              KeyByRelationship = False
            end>
        end>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey74'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'VIS_BETRIEBSTYPEN'
      TID = 75
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 534
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 300
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 535
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$BETRI__BEZEI__79FD19BE'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'01.01.1950'#39')'
          Required = False
          FID = 536
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$BETRI__BEGDA__7AF13DF7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'31.12.2999'#39')'
          Required = False
          FID = 537
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$BETRI__ENDDA__7BE56230'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$BETRIEBSTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_DOMWERT'
      TID = 76
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 538
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORD'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 539
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__DOMWERT__ORD__4BCC3ABA'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CODE'
          DataTypeName = 'Char'
          Size = 20
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 540
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__DOMWERT__CODE__4CC05EF3'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TEXT'
          DataTypeName = 'Char'
          Size = 255
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 541
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__DOMWERT__TEXT__4DB4832C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'DOM'
          DataTypeName = 'Char'
          Size = 32
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 542
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__DOMWERT__DOM__4EA8A765'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$DOMWERT'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_PRBKAT'
      TID = 77
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 543
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TIERART'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 544
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__TIERA__33008CF0'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'KATEG'
          DataTypeName = 'VarChar'
          Size = 3
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 545
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__KATEG__33F4B129'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MINMON'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 546
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__MINMO__34E8D562'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MAXMON'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 547
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__MAXMO__35DCF99B'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESCHLM'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 548
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__GESCH__36D11DD4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESCHLW'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 549
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__GESCH__37C5420D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GESCHLU'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 550
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__GESCH__38B96646'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'MUTTIER'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 551
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKA__MUTTI__39AD8A7F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TEXT'
          DataTypeName = 'VarChar'
          Size = 100
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 552
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS_PRBKAT__TEXT__3AA1AEB8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ORD'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 553
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$PRBKAT'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_SCHLUESSELTYPEN'
      TID = 78
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          Required = True
          FID = 554
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 555
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$SCHLU__BEZEI__0EF836A4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$SCHLUESSELTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_TIERARTEN'
      TID = 79
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 556
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TIERART'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 557
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$TIERA__TIERA__51851410'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'WERT'
          DataTypeName = 'Char'
          Size = 2
          Size2 = 0
          Required = True
          FID = 558
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$TIERARTEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_ZULASSUNGEN'
      TID = 80
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 559
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STICHTAG'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 560
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__STICH__536D5C82'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 561
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 562
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__BETRI__546180BB'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULASSUNG'
          DataTypeName = 'VarChar'
          Size = 4
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 563
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__ZULAS__5555A4F4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGINNDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 564
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__BEGIN__5649C92D'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 565
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__ENDDA__573DED66'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey80'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'VIS_ZULASSUNGSLISTENCODES'
      TID = 81
      OidIndex = 0
      Fields = <
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          Required = True
          FID = 566
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULASSUNGSTYP'
          DataTypeName = 'VarChar'
          Size = 4
          Size2 = 0
          Required = True
          FID = 567
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SEKTION'
          DataTypeName = 'VarChar'
          Size = 20
          Size2 = 0
          Required = True
          FID = 568
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'GRUPPE'
          DataTypeName = 'VarChar'
          Size = 1
          Size2 = 0
          Required = True
          FID = 569
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'CODE'
          DataTypeName = 'VarChar'
          Size = 20
          Size2 = 0
          Required = True
          FID = 570
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$ZULASSUNGSLISTENCODES'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 4
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 1
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 2
          FieldOrder = ioAsc
          KeyByRelationship = False
        end
        item
          FieldIndex = 3
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_ZULASSUNGSNUMMERN'
      TID = 82
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int (identity)'
          Size = 0
          Size2 = 0
          Required = True
          FID = 571
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 1
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'STICHTAG'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 572
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__STICH__5A1A5A11'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 573
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULASSUNGSNUMMER'
          DataTypeName = 'VarChar'
          Size = 7
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 574
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__ZULAS__5B0E7E4A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGINNDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 575
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__BEGIN__5C02A283'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 576
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__ENDDA__5CF6C6BC'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$ZULASSUNGSNUMMERN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'VIS_ZULASSUNGSTYPEN'
      TID = 83
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'VarChar'
          Size = 4
          Size2 = 0
          Required = True
          FID = 577
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 80
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 578
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__BEZEI__22FF2F51'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TAETIGKEITSCODE'
          DataTypeName = 'VarChar'
          Size = 2
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 579
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__VIS$ZULAS__TAETI__23F3538A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_VIS$ZULASSUNGSTYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ZUGL_B_Z_TYPEN'
      TID = 84
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 391
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZULASSUNG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 392
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BETRIEBSTYP'
          DataTypeName = 'VarChar'
          Size = 16
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 393
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_B_Z___BETRI__26CFC035'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULASSUNGSTYP'
          DataTypeName = 'VarChar'
          Size = 4
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 394
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_B_Z___ZULAS__27C3E46E'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'01.01.1900'#39')'
          Required = False
          FID = 395
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_B_Z___BEGDA__28B808A7'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '('#39'31.12.2199'#39')'
          Required = False
          FID = 396
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_B_Z___ENDDA__29AC2CE0'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 397
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_B_Z___AKTIV__2AA05119'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULNR'
          DataTypeName = 'VarChar'
          Size = 9
          Size2 = 0
          Required = True
          FID = 398
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_ZUGL_B_Z_TYPEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ZUGL_TAETIGKEITEN'
      TID = 85
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 399
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULNR'
          DataTypeName = 'VarChar'
          Size = 9
          Size2 = 0
          Required = True
          FID = 400
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          Required = True
          FID = 401
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDAT'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          Required = True
          FID = 402
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_TAETIGKEIT'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 403
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ID_ZULASSUNG'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 404
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUGL_TAET__ID_ZU__2D7CBDC4'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_ZUGL_TAETIGKEITEN'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end
    item
      TableName = 'ZULASSUNGEN'
      TID = 86
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 405
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'REGNR'
          DataTypeName = 'Char'
          Size = 7
          Size2 = 0
          Required = True
          FID = 406
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ZULNR'
          DataTypeName = 'VarChar'
          Size = 9
          Size2 = 0
          Required = True
          FID = 407
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEGINNDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 408
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZULASSUNG__BEGIN__30592A6F'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'ENDDATUM'
          DataTypeName = 'Date'
          Size = 0
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 409
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZULASSUNG__ENDDA__314D4EA8'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'AKTIV'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((0))'
          Required = False
          FID = 410
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZULASSUNG__AKTIV__324172E1'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'SICHTBAR'
          DataTypeName = 'SmallInt'
          Size = 0
          Size2 = 0
          DefaultValue = '((1))'
          Required = False
          FID = 411
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZULASSUNG__SICHT__3335971A'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PrimaryKey86'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <>
    end
    item
      TableName = 'ZUSATZTEXTE'
      TID = 87
      OidIndex = 0
      Fields = <
        item
          FieldName = 'ID'
          DataTypeName = 'Int'
          Size = 0
          Size2 = 0
          Required = True
          FID = 141
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'TABELLE'
          DataTypeName = 'VarChar'
          Size = 50
          Size2 = 0
          Required = True
          FID = 142
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'BEZEICHNUNG'
          DataTypeName = 'VarChar'
          Size = 150
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 143
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUSATZTEX__BEZEI__351DDF8C'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end
        item
          FieldName = 'LANGTEXT'
          DataTypeName = 'VarChar'
          Size = 800
          Size2 = 0
          DefaultValue = '(NULL)'
          Required = False
          FID = 144
          DefaultValueSpecific = False
          RequiredSpecific = False
          ConstraintExprSpecific = False
          ConstraintDefaultName = 'DF__ZUSATZTEX__LANGT__361203C5'
          SeedValue = 0
          IncrementValue = 1
          GeneratedByRelationship = False
        end>
      Indexes = <>
      Constraints = <>
      Triggers = <>
      PrimaryKeyIndex.IndexName = 'PK_ZUSATZTEXTE'
      PrimaryKeyIndex.IndexType = itNone
      PrimaryKeyIndex.IndexOrder = ioAscending
      PrimaryKeyIndex.IID = 0
      PrimaryKeyIndex.IFields = <
        item
          FieldIndex = 0
          FieldOrder = ioAsc
          KeyByRelationship = False
        end>
    end>
  DataDictionary.Relationships = <
    item
      RelationshipName = 'CHECKLISTENPUNKT_fk'
      ParentTableIndex = 7
      ChildTableIndex = 14
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 1
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_BEWERTUNG'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'CHECKP_FRAGE_fk'
      ParentTableIndex = 14
      ChildTableIndex = 15
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 2
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_CHECKLISTENPUNKT'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'CHECKP_FRAGE_fk2'
      ParentTableIndex = 21
      ChildTableIndex = 15
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 3
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_FRAGENSTAMM'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_BUNDESLAENDER_LAENDER'
      ParentTableIndex = 38
      ChildTableIndex = 10
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 4
      FieldLinks = <
        item
          ParentFieldName = 'LANDKZ'
          ChildFieldName = 'LANDKZ'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_BUNDESLAENDER_MODULE_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 11
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 5
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_BUNDESLAENDER_MODULE_MODULE'
      ParentTableIndex = 46
      ChildTableIndex = 11
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 6
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'MODUL_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_CHECKLISTEN_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 13
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 7
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_CHECKLISTEN_MODULE'
      ParentTableIndex = 46
      ChildTableIndex = 13
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 8
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_MODUL'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_CHECKLISTENPUNKT_CHECKLISTEN'
      ParentTableIndex = 13
      ChildTableIndex = 14
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 9
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_CHECKLISTE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_FUNKTIONEN_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 22
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 10
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_FUNKTIONEN_FUNKTIONEN'
      ParentTableIndex = 22
      ChildTableIndex = 22
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 11
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_MUTTER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_FUNKTIONEN_PROGRAMM_MODULE'
      ParentTableIndex = 50
      ChildTableIndex = 22
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 12
      FieldLinks = <
        item
          ParentFieldName = 'KURZBEZEICHNNUNG'
          ChildFieldName = 'PROGMODUL'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_FUNKTIONSROLLEN_FUNKTIONEN'
      ParentTableIndex = 22
      ChildTableIndex = 23
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 13
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_FUNKTION'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_FUNKTIONSROLLEN_ROLLEN'
      ParentTableIndex = 57
      ChildTableIndex = 23
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 14
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_ROLLE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_GRUPPEN_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 25
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 15
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_GRUPPEN_GRUPPEN'
      ParentTableIndex = 25
      ChildTableIndex = 25
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 16
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'MUTTERGRUPPE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_GRUPPEN_USER'
      ParentTableIndex = 67
      ChildTableIndex = 25
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 17
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_USER_HAUPTVER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_GRUPPEN_USER1'
      ParentTableIndex = 67
      ChildTableIndex = 25
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 18
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_USER_STELLVER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_ID_BEWERTUNGEN'
      ParentTableIndex = 7
      ChildTableIndex = 8
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 19
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_BEWERTUNGEN'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_KB_BEWERTUGEN_KB_FRAGEN'
      ParentTableIndex = 31
      ChildTableIndex = 28
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 20
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_KB_FRAGEN'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_LOGGING_MESSAGES_LOGGING_LOG_LOG_ID'
      ParentTableIndex = 39
      ChildTableIndex = 45
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 21
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'LOG_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 
        'FK_LOGGING_REQUEST_HEADERS_LOGGING_REQUESTS_HEADERS_LOG_ITEM_REQ' +
        'UEST_LOG_ID'
      ParentTableIndex = 54
      ChildTableIndex = 53
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 22
      FieldLinks = <
        item
          ParentFieldName = 'LOG_ID'
          ChildFieldName = 'HEADERS_LOG_ITEM_REQUEST_LOG_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_LOGGING_REQUESTS_LOGGING_LOG_LOG_ID'
      ParentTableIndex = 39
      ChildTableIndex = 54
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 23
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'LOG_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_LOGGING_REQUESTS_LOGGING_MESSAGES_MESSAGE_LOG_ID'
      ParentTableIndex = 45
      ChildTableIndex = 54
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 24
      FieldLinks = <
        item
          ParentFieldName = 'LOG_ID'
          ChildFieldName = 'MESSAGE_LOG_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_LOGGING_REQUESTS_LOGGING_PVP_TOKEN_PVPTOKEN_ID'
      ParentTableIndex = 51
      ChildTableIndex = 54
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 25
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'PVPTOKEN_ID'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_NACHRICHTEN_GRUPPEN'
      ParentTableIndex = 25
      ChildTableIndex = 47
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 26
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_GRUPPE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_NACHRICHTEN_USER'
      ParentTableIndex = 67
      ChildTableIndex = 47
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 27
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_ABSENDER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_NACHRICHTEN_ZUSTELLUNG_NACHRICHTEN'
      ParentTableIndex = 47
      ChildTableIndex = 48
      UpdateMethod = umCascade
      DeleteMethod = dmCascade
      RelID = 28
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_NACHRICHT'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_NACHRICHTEN_ZUSTELLUNG_USER'
      ParentTableIndex = 67
      ChildTableIndex = 48
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 29
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_USER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_ROLLEN_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 57
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 30
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USER_BUNDESLAENDER'
      ParentTableIndex = 10
      ChildTableIndex = 67
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 31
      FieldLinks = <
        item
          ParentFieldName = 'BLDCODE'
          ChildFieldName = 'BLDCODE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USER_PERSONEN'
      ParentTableIndex = 49
      ChildTableIndex = 67
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 32
      FieldLinks = <
        item
          ParentFieldName = 'PSKEY'
          ChildFieldName = 'PSKEY'
        end>
      ParentIndexID = 4
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USERGRUPPEN_GRUPPEN'
      ParentTableIndex = 25
      ChildTableIndex = 69
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 33
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_GRUPPE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USERGRUPPEN_USER'
      ParentTableIndex = 67
      ChildTableIndex = 69
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 34
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_USER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USERROLLEN_ROLLEN'
      ParentTableIndex = 57
      ChildTableIndex = 70
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 35
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_ROLLE'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end
    item
      RelationshipName = 'FK_USERROLLEN_USER'
      ParentTableIndex = 67
      ChildTableIndex = 70
      UpdateMethod = umRestrict
      DeleteMethod = dmRestrict
      RelID = 36
      FieldLinks = <
        item
          ParentFieldName = 'ID'
          ChildFieldName = 'ID_USER'
        end>
      ParentIndexID = 0
      RelationshipType = ryUndefined
    end>
  DataDictionary.Categories = <
    item
      CategoryType = ctProcedure
      Objects = <>
    end
    item
      CategoryType = ctView
      Objects = <>
    end>
  DiagramObj.Diagrams = <
    item
      DiagramName = 'Main Diagram'
      DiagramString = 
        'object TDiagramClass'#13#10'  NettoExportOffset = 3'#13#10'  AutomaticNodes ' +
        '= False'#13#10'  AutoScroll = True'#13#10'  Background.Scroll = True'#13#10'  Back' +
        'ground.Style = biTile'#13#10'  Background.Visible = False'#13#10'  Backgroun' +
        'd.Gradient.Direction = grTopBottom'#13#10'  Background.Gradient.StartC' +
        'olor = clWhite'#13#10'  Background.Gradient.EndColor = clYellow'#13#10'  Bac' +
        'kground.Gradient.Visible = False'#13#10'  Background.PrintGradient = F' +
        'alse'#13#10'  SnapGrid.Active = False'#13#10'  SnapGrid.Force = False'#13#10'  Sna' +
        'pGrid.Visible = False'#13#10'  SnapGrid.SizeX = 8.000000000000000000'#13#10 +
        '  SnapGrid.SizeY = 8.000000000000000000'#13#10'  SnapGrid.Style = gsDo' +
        'ts'#13#10'  SnapGrid.SnapToRuler = False'#13#10'  LeftRuler.Visible = False'#13 +
        #10'  LeftRuler.Divisions = 5'#13#10'  LeftRuler.Font.Charset = DEFAULT_C' +
        'HARSET'#13#10'  LeftRuler.Font.Color = clWindowText'#13#10'  LeftRuler.Font.' +
        'Height = -9'#13#10'  LeftRuler.Font.Name = '#39'Arial'#39#13#10'  LeftRuler.Font.S' +
        'tyle = []'#13#10'  LeftRuler.Units = unCenti'#13#10'  LeftRuler.MinorTickLen' +
        'gth = 4'#13#10'  LeftRuler.MajorTickLength = 6'#13#10'  LeftRuler.Color = cl' +
        'White'#13#10'  LeftRuler.TickColor = clBlack'#13#10'  LeftRuler.Size = 16'#13#10' ' +
        ' LeftRuler.AutoFactor = True'#13#10'  LeftRuler.GridColor = clBlack'#13#10' ' +
        ' TopRuler.Visible = False'#13#10'  TopRuler.Divisions = 5'#13#10'  TopRuler.' +
        'Font.Charset = DEFAULT_CHARSET'#13#10'  TopRuler.Font.Color = clWindow' +
        'Text'#13#10'  TopRuler.Font.Height = -9'#13#10'  TopRuler.Font.Name = '#39'Arial' +
        #39#13#10'  TopRuler.Font.Style = []'#13#10'  TopRuler.Units = unCenti'#13#10'  Top' +
        'Ruler.MinorTickLength = 4'#13#10'  TopRuler.MajorTickLength = 6'#13#10'  Top' +
        'Ruler.Color = clWhite'#13#10'  TopRuler.TickColor = clBlack'#13#10'  TopRule' +
        'r.Size = 16'#13#10'  TopRuler.AutoFactor = True'#13#10'  TopRuler.GridColor ' +
        '= clBlack'#13#10'  Zoom = 100'#13#10'  BorderColor = clGray'#13#10'  MouseWheelMod' +
        'e = mwVertical'#13#10'  ActiveLayers = 0'#13#10'  Layers = <>'#13#10'  LinkCursor ' +
        '= crHandPoint'#13#10'  PanCursor = crHandPoint'#13#10'  ZoomCursor = crDefau' +
        'lt'#13#10'  IgnoreScreenDPI = False'#13#10'  ShowCrossIndicators = False'#13#10'  ' +
        'KeyActions = [kaEscape, kaMove, kaPage, kaResize, kaSelect]'#13#10'  S' +
        'moothMode = smAntiAlias'#13#10'  TextRenderingMode = tmAntiAlias'#13#10'  Se' +
        'lectionMode = slmMultiple'#13#10'  CanMoveOutOfBounds = True'#13#10'  PageSe' +
        'ttings.PaperName = '#39'A4'#39#13#10'  PageSettings.PaperId = 9'#13#10'  PageSetti' +
        'ngs.PaperWidth = 210.015666666666700000'#13#10'  PageSettings.PaperHei' +
        'ght = 297.010666666666700000'#13#10'  PageSettings.Orientation = dpoPo' +
        'rtrait'#13#10'  PageSettings.LeftMarginStr = '#39'25.4'#39#13#10'  PageSettings.To' +
        'pMarginStr = '#39'25.4'#39#13#10'  PageSettings.RightMarginStr = '#39'25.4'#39#13#10'  P' +
        'ageSettings.BottomMarginStr = '#39'25.4'#39#13#10'  RulerAutoUnit = False'#13#10' ' +
        ' BorderStyle = bsNone'#13#10'  Color = clWhite'#13#10'  ParentColor = False'#13 +
        #10'  DisplayRelationshipNames = False'#13#10'  LinkRelationshipsToFields' +
        ' = False'#13#10'  ShowCaptions = False'#13#10'  StraightRelationshipLines = ' +
        'True'#13#10'  ExplicitWidth = 1086'#13#10'  ExplicitHeight = 883'#13#10'end'#13#10
    end>
  AureliusExportOptions = 
    '<Mappings OmitDictionary="true" RegisterEntities="true" DefaultA' +
    'ssociationCascadeDefinition="allbutremove">'#13#10'  <ProjectFile/>'#13#10' ' +
    ' <OutputDir>C:\Projekte\Esculenta\ELKE\Model</OutputDir>'#13#10'  <Mai' +
    'nUnitName>ELKE.Classes.Generated</MainUnitName>'#13#10'  <DictionaryNa' +
    'me>Dic</DictionaryName>'#13#10'  <DictionaryUnitName/>'#13#10'  <Script/>'#13#10' ' +
    ' <DefaultAncestorClass>TObject</DefaultAncestorClass>'#13#10'  <TableN' +
    'aming Source="name" Format="T%s" Singularize="true" CamelCase="t' +
    'rue" RemoveUnderline="true"/>'#13#10'  <FieldNaming Source="name" Form' +
    'at="%s" CamelCase="true" RemoveUnderline="true"/>'#13#10'  <Associatio' +
    'nNaming Source="ChildField" Format="%s" CamelCase="true" RemoveU' +
    'nderline="true"/>'#13#10'  <ManyValuedNaming Source="ChildTable" Forma' +
    't="%sList" CamelCase="true" RemoveUnderline="true"/>'#13#10'  <Tables>' +
    #13#10'    <Table TableId="22" Exclude="true"/>'#13#10'    <Table TableId="' +
    '46" Exclude="true"/>'#13#10'    <Table TableId="27" Exclude="true"/>'#13#10 +
    '    <Table TableId="33" Exclude="true"/>'#13#10'    <Table TableId="68' +
    '" Exclude="true"/>'#13#10'    <Table TableId="6" Exclude="true"/>'#13#10'   ' +
    ' <Table TableId="20" Exclude="true"/>'#13#10'    <Table TableId="56" E' +
    'xclude="true"/>'#13#10'    <Table TableId="75" Exclude="true"/>'#13#10'    <' +
    'Table TableId="48" Exclude="true"/>'#13#10'    <Table TableId="8" Excl' +
    'ude="true"/>'#13#10'    <Table TableId="9" Exclude="true"/>'#13#10'    <Tabl' +
    'e TableId="38" Exclude="true"/>'#13#10'    <Table TableId="63" Exclude' +
    '="true"/>'#13#10'    <Table TableId="66" Exclude="true"/>'#13#10'    <Table ' +
    'TableId="72" Exclude="true"/>'#13#10'    <Table TableId="32" Exclude="' +
    'true"/>'#13#10'    <Table TableId="61" Exclude="true"/>'#13#10'    <Table Ta' +
    'bleId="71" Exclude="true"/>'#13#10'    <Table TableId="78" Exclude="tr' +
    'ue"/>'#13#10'    <Table TableId="82" Exclude="true"/>'#13#10'    <Table Tabl' +
    'eId="76" Exclude="true"/>'#13#10'    <Table TableId="7" Exclude="true"' +
    '/>'#13#10'    <Table TableId="1" Exclude="true"/>'#13#10'    <Table TableId=' +
    '"13" Exclude="true"/>'#13#10'    <Table TableId="34" Exclude="true"/>'#13 +
    #10'    <Table TableId="74" Exclude="true"/>'#13#10'    <Table TableId="4' +
    '7" Exclude="true"/>'#13#10'    <Table TableId="52" Exclude="true"/>'#13#10' ' +
    '   <Table TableId="4" Exclude="true"/>'#13#10'    <Table TableId="57" ' +
    'Exclude="true"/>'#13#10'    <Table TableId="64" Exclude="true"/>'#13#10'    ' +
    '<Table TableId="83" Exclude="true"/>'#13#10'    <Table TableId="5" Exc' +
    'lude="true"/>'#13#10'    <Table TableId="19" Exclude="true"/>'#13#10'    <Ta' +
    'ble TableId="26" Exclude="true"/>'#13#10'    <Table TableId="49" Exclu' +
    'de="true"/>'#13#10'    <Table TableId="14" Exclude="true"/>'#13#10'    <Tabl' +
    'e TableId="59" Exclude="true"/>'#13#10'    <Table TableId="36" Exclude' +
    '="true"/>'#13#10'    <Table TableId="54" Exclude="true"/>'#13#10'    <Table ' +
    'TableId="35" Exclude="true"/>'#13#10'    <Table TableId="18" Exclude="' +
    'true"/>'#13#10'    <Table TableId="37" Exclude="true"/>'#13#10'    <Table Ta' +
    'bleId="53" Exclude="true"/>'#13#10'    <Table TableId="60" Exclude="tr' +
    'ue"/>'#13#10'    <Table TableId="15" Exclude="true"/>'#13#10'    <Table Tabl' +
    'eId="42" Exclude="true"/>'#13#10'    <Table TableId="81" Exclude="true' +
    '"/>'#13#10'    <Table TableId="87" Exclude="true"/>'#13#10'    <Table TableI' +
    'd="86" Exclude="true"/>'#13#10'    <Table TableId="2" Exclude="true"/>' +
    #13#10'    <Table TableId="58" ClassName="TRolle"/>'#13#10'    <Table Table' +
    'Id="85" Exclude="true"/>'#13#10'    <Table TableId="40" Exclude="true"' +
    '/>'#13#10'    <Table TableId="3" Exclude="true"/>'#13#10'    <Table TableId=' +
    '"43" Exclude="true"/>'#13#10'    <Table TableId="29" Exclude="true"/>'#13 +
    #10'    <Table TableId="44" Exclude="true"/>'#13#10'    <Table TableId="4' +
    '5" Exclude="true"/>'#13#10'    <Table TableId="62" Exclude="true"/>'#13#10' ' +
    '   <Table TableId="25" Exclude="true"/>'#13#10'    <Table TableId="51"' +
    ' Exclude="true"/>'#13#10'    <Table TableId="70" Exclude="true"/>'#13#10'   ' +
    ' <Table TableId="80" Exclude="true"/>'#13#10'    <Table TableId="55" E' +
    'xclude="true"/>'#13#10'    <Table TableId="84" Exclude="true"/>'#13#10'    <' +
    'Table TableId="73" Exclude="true"/>'#13#10'    <Table TableId="41" Exc' +
    'lude="true"/>'#13#10'    <Table TableId="50" Exclude="true"/>'#13#10'    <Ta' +
    'ble TableId="16" Exclude="true"/>'#13#10'    <Table TableId="21" Exclu' +
    'de="true"/>'#13#10'    <Table TableId="69" Exclude="true"/>'#13#10'    <Tabl' +
    'e TableId="17" Exclude="true"/>'#13#10'    <Table TableId="28" Exclude' +
    '="true"/>'#13#10'    <Table TableId="31" Exclude="true"/>'#13#10'    <Table ' +
    'TableId="39" ClassName="TLand"/>'#13#10'    <Table TableId="65" Exclud' +
    'e="true"/>'#13#10'    <Table TableId="67" Exclude="true"/>'#13#10'    <Table' +
    ' TableId="24" ClassName="TFunktionsrolle"/>'#13#10'    <Table TableId=' +
    '"79" Exclude="true"/>'#13#10'    <Table TableId="10" Exclude="true"/>'#13 +
    #10'    <Table TableId="30" Exclude="true"/>'#13#10'    <Table TableId="7' +
    '7" Exclude="true"/>'#13#10'    <Table TableId="23" ClassName="TFunktio' +
    'n"/>'#13#10'    <Table TableId="12" Exclude="true"/>'#13#10'    <Table Table' +
    'Id="11" ClassName="TBundesland"/>'#13#10'  </Tables>'#13#10'  <Associations>' +
    #13#10'    <Association AssociationId="35" ManyValuedPropertyName="Us' +
    'errollen"/>'#13#10'    <Association AssociationId="15" ManyValuedPrope' +
    'rtyName="Gruppen"/>'#13#10'    <Association AssociationId="31" ManyVal' +
    'uedPropertyName="Users"/>'#13#10'    <Association AssociationId="10" M' +
    'anyValuedPropertyName="Funktionen"/>'#13#10'    <Association Associati' +
    'onId="4" PropertyName="Land" ManyValuedPropertyName="Bundeslaend' +
    'er"/>'#13#10'    <Association AssociationId="30" PropertyName="Bundesl' +
    'and" ManyValuedPropertyName="Rollen"/>'#13#10'    <Association Associa' +
    'tionId="5" ManyValuedPropertyName="BundeslaenderModule"/>'#13#10'    <' +
    'Association AssociationId="14" ManyValuedPropertyName="Funktions' +
    'rollen"/>'#13#10'    <Association AssociationId="7" ManyValuedProperty' +
    'Name="Checklisten"/>'#13#10'  </Associations>'#13#10'</Mappings>'#13#10
end
