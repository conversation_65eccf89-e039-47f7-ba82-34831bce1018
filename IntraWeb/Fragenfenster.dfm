inherited FrmFragenfenster: TFrmFragenfenster
  Width = 1043
  Height = 746
  OnCreate = FragenfensterOnCreate
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1043
    TabOrder = 17
    inherited ImageLogo: TIWImageFile
      Left = 760
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 18
    end
  end
  object IWRegion2: TIWCGJQRegion [1]
    Left = 1023
    Top = 50
    Width = 20
    Height = 671
    RenderInvisibleControls = True
    TabOrder = 19
    Version = '1.0'
    Align = alRight
    Color = clWebSILVER
  end
  object IWRegion3: TIWCGJQRegion [2]
    Left = 0
    Top = 50
    Width = 20
    Height = 671
    RenderInvisibleControls = True
    TabOrder = 20
    Version = '1.0'
    Align = alLeft
    Color = clWebSILVER
  end
  object iwtFragenfenster: TIWTabControl [3]
    Left = 20
    Top = 50
    Width = 1003
    Height = 671
    RenderInvisibleControls = False
    ActiveTabFont.Color = clWebWHITE
    ActiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    ActiveTabFont.Size = 10
    ActiveTabFont.Style = [fsBold]
    InactiveTabFont.Color = clWebBLACK
    InactiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    InactiveTabFont.Size = 10
    InactiveTabFont.Style = []
    ActiveTabColor = clWebCORNFLOWERBLUE
    InactiveTabColor = clWebLIGHTGRAY
    ActivePage = 4
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    Color = clWebSILVER
    ClipRegion = False
    StyleRenderOptions.RenderZIndex = False
    StyleRenderOptions.RenderAbsolute = False
    StyleRenderOptions.RenderPadding = False
    TabMargin = 3
    TabPadding = 0
    TabBorderRadius = 3
    ActiveTabBorder.Color = clWebBLACK
    ActiveTabBorder.Width = 1
    InactiveTabBorder.Color = clWebBLACK
    InactiveTabBorder.Width = 0
    DesignSize = (
      1003
      671)
    object TabFragengruppen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 1
      Title = 'Fragengruppen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgFragengruppen: TIWCGJQGrid
        Left = 0
        Top = 89
        Width = 1003
        Height = 613
        TabOrder = 7
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID'
            Name = 'ID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'ID'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'BEZEICHNUNG'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG_1'
            Name = 'BEZEICHNUNG_1'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'BEZEICHNUNG_1'
          end>
        JQGridOptions.Height = 559
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Fragengruppen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion4: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1003
        Height = 89
        RenderInvisibleControls = True
        TabOrder = 21
        Version = '1.0'
        Align = alTop
        object iwcgcbl_FilterFragenGruppen: TIWCGJQCheckBoxList
          Left = 53
          Top = 32
          Width = 234
          Height = 30
          TabOrder = 13
          Version = '1.0'
          JQCheckBoxListOptions.MinWidth = 234
          JQCheckBoxListOptions.CheckAllText = 'alle Ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Nach Hauptgruppen filtern'
          JQCheckBoxListOptions.OnCheckall.OnEvent = iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsCheckall
          JQCheckBoxListOptions.OnUncheckall.OnEvent = iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsUncheckall
          JQCheckBoxListOptions.OnClick.OnEvent = iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsClick
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <
            item
              Caption = 'Erhebung im Betriebsb'#252'ro'
              Value = '1'
            end
            item
              Caption = 'Vor-Ort-Pr'#252'fung/Nachschau'
              Value = '2'
            end
            item
              Caption = 'Sonstiges'
              Value = '3'
            end>
          Groups = <>
        end
      end
    end
    object TabFragen: TIWTabPage
      Left = 0
      Top = 17
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      Title = 'Fragen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgFragen: TIWCGJQGrid
        Left = 0
        Top = 92
        Width = 1003
        Height = 610
        TabOrder = 6
        Font.Size = 9
        Version = '1.0'
        Align = alClient
        CGScrollStyle = cgsbsBothVisible
        JQGridOptions.AltRows = True
        JQGridOptions.AutoWidth = True
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 190
            Caption = 'Fragengruppe'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FRAGENNR'
            Name = 'FRAGENNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 25
            Caption = 'Fragen Nr.'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FRAGE'
            Name = 'FRAGE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 500
            Caption = 'Frage'
          end>
        JQGridOptions.Height = 556
        JQGridOptions.RowNum = 500
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = IWCGJQGrid1JQGridOptionsSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_FilterFragen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion9: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1003
        Height = 92
        RenderInvisibleControls = True
        TabOrder = 22
        Version = '1.0'
        Align = alTop
        object iwcgcbl_FilterFragen: TIWCGJQCheckBoxList
          Left = 56
          Top = 22
          Width = 289
          Height = 30
          RenderInvisibleControls = True
          TabOrder = 12
          Version = '1.0'
          StyleRenderOptions.RenderZIndex = False
          ZIndex = 100000
          JQCheckBoxListOptions.MinWidth = 289
          JQCheckBoxListOptions.CheckAllText = 'alle Ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Fragen filtern nach Gruppen'
          JQCheckBoxListOptions.OnCheckall.OnEvent = iwcgcbl_FilterFragenJQCheckBoxListOptionsCheckall
          JQCheckBoxListOptions.OnUncheckall.OnEvent = iwcgcbl_FilterFragenJQCheckBoxListOptionsUncheckall
          JQCheckBoxListOptions.OnClick.OnEvent = iwcgcbl_FilterFragenJQCheckBoxListOptionsClick
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <
            item
              Caption = 'Zulassung/Registrierung/Eckdaten'
              Value = 'GRD'
            end
            item
              Caption = 'Pl'#228'ne (ohne HACCP)/Skizzen/Betriebsstruktur'
              Value = 'PLN'
            end
            item
              Caption = 
                'Lfnd. Eigenkontrolle/Dokumentation (ohne HACCP/ R'#220'CKVERFOLGBARKE' +
                'IT/MIKROBIOL. KONTR.)'
              Value = 'LFD'
            end
            item
              Caption = 
                'Tierkennzeichnung, Tierschutz vor und bei der Schlachtung (NUR I' +
                'N SCHLACHTH'#214'FEN)'
              Value = 'TSS'
            end
            item
              Caption = 'Instandhaltung/Ausstattung'
              Value = 'BAU'
            end
            item
              Caption = 'Sch'#228'dlingsbek'#228'mpfung'
              Value = 'GH1'
            end
            item
              Caption = 'Reinigung und Desinfektion'
              Value = 'GH2'
            end
            item
              Caption = 'Arbeitshygiene'
              Value = 'GH3'
            end
            item
              Caption = 
                'Personalhygiene einschlie'#223'lich Fremdpersonen und Personalschulun' +
                'g'
              Value = 'GH4'
            end
            item
              Caption = 'Sonstige GHP/GMP'
              Value = 'GH5'
            end
            item
              Caption = 'Raum- und Fleisch (-erzeugnis) temperaturen'
              Value = 'TMP'
            end
            item
              Caption = 'Entsorgung der Nebenprodukte/Abf'#228'lle'
              Value = 'TNP'
            end
            item
              Caption = 'HACCP und Produktionshygiene'
              Value = 'CCP'
            end
            item
              Caption = 'Warenpr'#252'fung inkl. mikrobiologische Untersuchungen'
              Value = 'PRO'
            end
            item
              Caption = 'R'#252'ckverfolgbarkeit'
              Value = 'RCK'
            end
            item
              Caption = 
                'FCM (Lebensmittelkontaktmaterialien inkl. Verpackung f'#252'r tierisc' +
                'he LM)'
              Value = 'FCM'
            end
            item
              Caption = 'Lebensmittelzusatzstoffe und Aromen'
              Value = 'ZST'
            end
            item
              Caption = 'Sonstiges'
              Value = 'AND'
            end>
          Groups = <>
        end
      end
    end
    object TabHauptgruppen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 3
      Title = 'Hauptgruppen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgHauptgruppen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1003
        Height = 702
        RenderInvisibleControls = True
        TabOrder = 8
        Version = '1.0'
        Align = alClient
        OnCreate = jqgHauptgruppenCreate
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BEZEICHNUNG'
          end>
        JQGridOptions.Height = 648
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_FragenHauptgruppen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabChecklisten: TIWTabPage
      Left = 0
      Top = 20
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 4
      Title = 'Checklisten'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgChecklisten: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1003
        Height = 702
        TabOrder = 9
        Version = '1.0'
        Align = alClient
        OnCreate = jqgChecklistenCreate
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BEZEICHNUNG'
          end>
        JQGridOptions.Height = 648
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Checklisten
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabCheckpunktfrage: TIWTabPage
      Left = 0
      Top = 20
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 6
      Title = 'Checkpunktfrage'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgCheckpunktfrage: TIWCGJQGrid
        Left = 0
        Top = 89
        Width = 1003
        Height = 613
        TabOrder = 11
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHUNG'
            Name = 'BEZEICHUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Width = 100
            Caption = 'Checkpunkt'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FRAGENNR'
            Name = 'FRAGENNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'Fragennummer'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FRAGE'
            Name = 'FRAGE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 350
            Caption = 'Frage'
          end>
        JQGridOptions.Height = 559
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_checkp_Frage
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion8: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1003
        Height = 89
        RenderInvisibleControls = True
        TabOrder = 23
        Version = '1.0'
        Align = alTop
        object iwcgcbl_CheckpunktFragen: TIWCGJQCheckBoxList
          Left = 254
          Top = 32
          Width = 200
          Height = 30
          TabOrder = 15
          Version = '1.0'
          JQCheckBoxListOptions.MinWidth = 200
          JQCheckBoxListOptions.CheckAllText = 'alle Ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Checkpunkt Ausw'#228'hlen'
          JQCheckBoxListOptions.OnCheckall.OnEvent = iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsCheckall
          JQCheckBoxListOptions.OnUncheckall.OnEvent = iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsUncheckall
          JQCheckBoxListOptions.OnClick.OnEvent = iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsClick
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <>
          Groups = <>
        end
        object iwcgcbl_FiltereCheckListe: TIWCGJQCheckBoxList
          Left = 6
          Top = 32
          Width = 200
          Height = 30
          TabOrder = 16
          Version = '1.0'
          JQCheckBoxListOptions.MinWidth = 200
          JQCheckBoxListOptions.CheckAllText = 'alle ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Checkliste Ausw'#228'hlen'
          JQCheckBoxListOptions.OnCheckall.Ajax = False
          JQCheckBoxListOptions.OnCheckall.OnEvent = iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsCheckall
          JQCheckBoxListOptions.OnUncheckall.Ajax = False
          JQCheckBoxListOptions.OnUncheckall.OnEvent = iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsUncheckall
          JQCheckBoxListOptions.OnClick.Ajax = False
          JQCheckBoxListOptions.OnClick.OnEvent = IWCGJQCheckBoxList2JQCheckBoxListOptionsClick
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <
            item
              Caption = 'Test f'#252'r Wildverarbeitungsbetriebe'
              Value = '1'
            end
            item
              Caption = 'Test f'#252'r Schlachtbetriebe'
              Value = '2'
            end>
          Groups = <>
        end
      end
    end
    object TabChecklistenpunkte: TIWTabPage
      Left = 0
      Top = 20
      Width = 1003
      Height = 702
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 5
      Title = 'Checklistenpunkt'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object jqgChecklistenpunkt: TIWCGJQGrid
        Left = 0
        Top = 89
        Width = 1003
        Height = 613
        TabOrder = 10
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Checkliste'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHUNG'
            Name = 'BEZEICHUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Checkpunkt'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LANGTEXT'
            Name = 'LANGTEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end>
        JQGridOptions.Height = 559
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1001
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Checklistenpunkt
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion7: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1003
        Height = 89
        RenderInvisibleControls = True
        TabOrder = 24
        Version = '1.0'
        Align = alTop
        object iwcgcbl_Checklistenpunkte: TIWCGJQCheckBoxList
          Left = 72
          Top = 19
          Width = 200
          Height = 33
          TabOrder = 14
          Version = '1.0'
          JQCheckBoxListOptions.Height = 30
          JQCheckBoxListOptions.MinWidth = 200
          JQCheckBoxListOptions.OnCheckall.OnEvent = IWCGJQCheckBoxList1JQCheckBoxListOptionsCheckall
          JQCheckBoxListOptions.OnUncheckall.OnEvent = iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsUncheckall
          JQCheckBoxListOptions.OnClick.Ajax = False
          JQCheckBoxListOptions.OnClick.OnEvent = iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsClick
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <
            item
              Caption = 'Test f'#252'r Wildverarbeitungsbetriebe'
              Value = '1'
            end
            item
              Caption = 'Test f'#252'r Schlachtbetriebe'
              Value = '2'
            end>
          Groups = <>
        end
      end
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 721
    Width = 1043
  end
  object sa: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 608
    Top = 58
  end
  object ds_FilterFragen: TDataSource
    DataSet = dm_main.qu_FilterFragen
    Left = 816
    Top = 24
  end
  object iwcgprov_FilterFragen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_FilterFragen
    DataSource = ds_FilterFragen
    Left = 888
    Top = 98
  end
  object ds_Fragengruppen: TDataSource
    DataSet = dm_main.qu_Fragengruppen
    Left = 380
    Top = 6
  end
  object ds_FragenHauptgruppen: TDataSource
    DataSet = dm_main.qu_Fragenhauptgruppen
    Left = 492
    Top = 65534
  end
  object ds_Checklisten: TDataSource
    DataSet = dm_main.qu_Checklisten
    Left = 708
    Top = 65534
  end
  object ds_Checklistenpunkt: TDataSource
    DataSet = dm_main.qu_Checklistenpunkte
    Left = 244
    Top = 65534
  end
  object ds_checkp_Frage: TDataSource
    DataSet = dm_main.qu_Checkp_Frage
    Left = 812
    Top = 65526
  end
  object iwcgprov_FragenHauptgruppen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_Fragenhauptgruppen
    DataSource = ds_FragenHauptgruppen
    Left = 772
    Top = 94
  end
  object iwcgprov_Fragengruppen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_Fragengruppen
    DataSource = ds_Fragengruppen
    Left = 660
    Top = 86
  end
  object iwcgprov_Checklisten: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_Checklisten
    DataSource = ds_Checklisten
    Left = 700
    Top = 70
  end
  object iwcgprov_Checklistenpunkt: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_Checklistenpunkte
    DataSource = ds_Checklistenpunkt
    Left = 724
    Top = 126
  end
  object iwcgprov_checkp_Frage: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_Checkp_Frage
    DataSource = ds_checkp_Frage
    Left = 572
    Top = 94
  end
end
