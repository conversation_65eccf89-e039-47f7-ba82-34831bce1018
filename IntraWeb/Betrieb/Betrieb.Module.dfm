﻿object DMBetrieb: TDMBetrieb
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  Height = 434
  Width = 621
  object QMarkierungen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select row_number() over (order by <PERSON><PERSON><PERSON><PERSON><PERSON>) as RowIndex, *'
      'from SYSTEMSTAMMDATEN.MARKIERUNGEN'
      'where BLDCODE = :BLDCODE'
      'order by Beschreibung')
    Left = 88
    Top = 40
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object QMarkierungenID: TGuidField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QMarkierungenBESCHREIBUNG: TWideStringField
      FieldName = 'BESCHREIBUNG'
      Origin = 'BESCHREIBUNG'
      Required = True
      Size = 250
    end
    object QMarkierungenWERT: TWideStringField
      FieldName = 'WERT'
      Origin = 'WERT'
      Required = True
      Size = 250
    end
    object QMarkierungenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QMarkierungenRowIndex: TLargeintField
      FieldName = 'RowIndex'
      Origin = 'RowIndex'
      ReadOnly = True
    end
  end
  object quRevstammSuche: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Bewegungsdaten.Revisionsstamm'
      'WHERE  Bldcode = :bldcode;')
    Left = 89
    Top = 121
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Betriebe_Revstamm'
      
        'WHERE        ID_Betrieb = :idbetrieb AND ID_Revisionsstamm = :id' +
        'revstamm;')
    Left = 217
    Top = 121
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'IDREVSTAMM'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Betriebe_Revstamm (ID_Betrieb, ID_Revisio' +
        'nsstamm, Begdate, Enddate)'
      'VALUES      (:idbetrieb, :idrevstamm, :begdat, :enddat);')
    Left = 265
    Top = 121
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'IDREVSTAMM'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
  end
  object quRevstamm: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'select BRS.*, RS.*, RP.J_MINDEST_KONTROLL_FREQUENZ as KontrollFr' +
        'equenz'
      'from STAMMDATEN.BETRIEBE_REVSTAMM BRS'
      
        '         inner join BEWEGUNGSDATEN.REVISIONSSTAMM RS on BRS.ID_R' +
        'EVISIONSSTAMM = RS.ID'
      ''
      
        '-- hier wird die max Kontrollfrequnez aus allen zugh'#246'rigen Revis' +
        'ionspl'#228'nen (also '#252'ber alle Jahre) geholt'
      
        '-- Alternativ k'#246'nnte man auch die "neueste" Kontrollfrequenz neh' +
        'men'
      '         outer apply ('
      '    select top 1 *'
      '    from BEWEGUNGSDATEN.REVISIONSPLAN RP'
      '    where RP.ID_REVISIONSSTAMM = RS.ID'
      '    order by RP.J_MINDEST_KONTROLL_FREQUENZ desc'
      ') RP'
      'WHERE  ID_Betrieb = :idbetrieb'
      '         AND BRS.Begdate >= :begdat'
      '         AND BRS.Enddate <= :enddat'
      '         AND BRS.ID_Revisionsstamm in &inlist'
      ''
      '-- alt'
      '--SELECT *'
      '--FROM   Stammdaten.Betriebe_Revstamm br'
      
        '--         INNER JOIN Bewegungsdaten.Revisionsstamm r ON br.ID_R' +
        'evisionsstamm = r.id'
      '-- WHERE  ID_Betrieb = :idbetrieb'
      '--         AND br.Begdate >= :begdat'
      '--         AND br.Enddate <= :enddat--'
      '--         AND br.ID_Revisionsstamm IN (:inlist);')
    Left = 321
    Top = 121
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = 686
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = 36526d
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = 401768d
      end>
    MacroData = <
      item
        Value = '(3)'
        Name = 'INLIST'
      end>
    object quRevstammID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quRevstammID_BETRIEB: TIntegerField
      FieldName = 'ID_BETRIEB'
      Origin = 'ID_BETRIEB'
      Required = True
    end
    object quRevstammID_REVISIONSSTAMM: TIntegerField
      FieldName = 'ID_REVISIONSSTAMM'
      Origin = 'ID_REVISIONSSTAMM'
      Required = True
    end
    object quRevstammBEGDATE: TDateField
      FieldName = 'BEGDATE'
      Origin = 'BEGDATE'
      Required = True
    end
    object quRevstammENDDATE: TDateField
      FieldName = 'ENDDATE'
      Origin = 'ENDDATE'
      Required = True
    end
    object quRevstammID_1: TFDAutoIncField
      FieldName = 'ID_1'
      Origin = 'ID'
      ReadOnly = True
    end
    object quRevstammBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object quRevstammSEKTION: TStringField
      FieldName = 'SEKTION'
      Origin = 'SEKTION'
    end
    object quRevstammBETRIEBSGRUPPE_LM: TStringField
      FieldName = 'BETRIEBSGRUPPE_LM'
      Origin = 'BETRIEBSGRUPPE_LM'
      Size = 40
    end
    object quRevstammBETRIEBSGRUPPE_DETAIL: TStringField
      FieldName = 'BETRIEBSGRUPPE_DETAIL'
      Origin = 'BETRIEBSGRUPPE_DETAIL'
      Size = 40
    end
    object quRevstammBETRIEBSART: TStringField
      FieldName = 'BETRIEBSART'
      Origin = 'BETRIEBSART'
      Size = 500
    end
    object quRevstammBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Size = 10
    end
    object quRevstammKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      Size = 3
    end
    object quRevstammKontrollFrequenz: TSingleField
      FieldName = 'KontrollFrequenz'
      Origin = 'KontrollFrequenz'
    end
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Betriebe_Revstamm'
      'SET    Begdate = :begdate, Enddate = :enddate'
      'WHERE  ID = :id')
    Left = 384
    Top = 120
    ParamData = <
      item
        Name = 'BEGDATE'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDATE'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quRevisionsstaemme: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Distinct(Betriebsart) AS "Betriebsart", r.Id AS "Id"'
      'FROM   Stammdaten.Betriebe_Revstamm br'
      
        '         INNER JOIN Bewegungsdaten.Revisionsstamm r ON br.ID_Rev' +
        'isionsstamm = r.id'
      'WHERE  ID_Betrieb = :idbetrieb;')
    Left = 81
    Top = 185
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qBetriebTKV: TFDQuery
    MasterSource = DMStammdaten.DSBetriebeSuche
    MasterFields = 'REGNR'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select *'
      'from ELAI.TKV_DATEN'
      '--BEWEGUNGSDATEN.TKV_DATEN'
      'where TKV_DATEN.LO_TSK_Nr = :regnr'
      'order by Jahr desc, Monat desc')
    Left = 248
    Top = 288
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = '2827492'
      end>
    object qBetriebTKVID: TGuidField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object qBetriebTKVLO_Kd_Nr: TStringField
      FieldName = 'LO_Kd_Nr'
      Origin = 'LO_Kd_Nr'
      Size = 10
    end
    object qBetriebTKVLO_TSK_Nr: TStringField
      FieldName = 'LO_TSK_Nr'
      Origin = 'LO_TSK_Nr'
      FixedChar = True
    end
    object qBetriebTKVLO_Name1: TStringField
      FieldName = 'LO_Name1'
      Origin = 'LO_Name1'
      Size = 255
    end
    object qBetriebTKVLO_Name2: TStringField
      FieldName = 'LO_Name2'
      Origin = 'LO_Name2'
      Size = 255
    end
    object qBetriebTKVLO_Strasse: TStringField
      FieldName = 'LO_Strasse'
      Origin = 'LO_Strasse'
      Size = 150
    end
    object qBetriebTKVLO_Str_PLZ: TStringField
      FieldName = 'LO_Str_PLZ'
      Origin = 'LO_Str_PLZ'
      Size = 7
    end
    object qBetriebTKVLO_Ort: TStringField
      FieldName = 'LO_Ort'
      Origin = 'LO_Ort'
      Size = 50
    end
    object qBetriebTKVLO_GKZ: TStringField
      FieldName = 'LO_GKZ'
      Origin = 'LO_GKZ'
    end
    object qBetriebTKVAbw_RE_Kd_Nr: TStringField
      FieldName = 'Abw_RE_Kd_Nr'
      Origin = 'Abw_RE_Kd_Nr'
      Size = 10
    end
    object qBetriebTKVRE_TSK_Nr: TStringField
      FieldName = 'RE_TSK_Nr'
      Origin = 'RE_TSK_Nr'
      FixedChar = True
    end
    object qBetriebTKVRE_Name1: TStringField
      FieldName = 'RE_Name1'
      Origin = 'RE_Name1'
      Size = 255
    end
    object qBetriebTKVRE_Name2: TStringField
      FieldName = 'RE_Name2'
      Origin = 'RE_Name2'
      Size = 255
    end
    object qBetriebTKVRE_Strasse: TStringField
      FieldName = 'RE_Strasse'
      Origin = 'RE_Strasse'
      Size = 150
    end
    object qBetriebTKVRE_Str_PLZ: TStringField
      FieldName = 'RE_Str_PLZ'
      Origin = 'RE_Str_PLZ'
      Size = 7
    end
    object qBetriebTKVRE_Ort: TStringField
      FieldName = 'RE_Ort'
      Origin = 'RE_Ort'
      Size = 50
    end
    object qBetriebTKVL_Dat: TDateField
      FieldName = 'L_Dat'
      Origin = 'L_Dat'
    end
    object qBetriebTKVAuftrag_Nr: TStringField
      FieldName = 'Auftrag_Nr'
      Origin = 'Auftrag_Nr'
    end
    object qBetriebTKVStoffbezeichnung: TStringField
      FieldName = 'Stoffbezeichnung'
      Origin = 'Stoffbezeichnung'
      Size = 255
    end
    object qBetriebTKVBehältertyp: TStringField
      FieldName = 'Beh'#228'ltertyp'
      Origin = '[Beh'#228'ltertyp]'
      Size = 50
    end
    object qBetriebTKVMenge: TIntegerField
      FieldName = 'Menge'
      Origin = 'Menge'
    end
    object qBetriebTKVLE: TStringField
      FieldName = 'LE'
      Origin = 'LE'
      FixedChar = True
      Size = 3
    end
    object qBetriebTKVMenge2: TIntegerField
      FieldName = 'Menge2'
      Origin = 'Menge2'
    end
    object qBetriebTKVLE2: TStringField
      FieldName = 'LE2'
      Origin = 'LE2'
      FixedChar = True
      Size = 3
    end
    object qBetriebTKVOhrmarken_Nr: TStringField
      FieldName = 'Ohrmarken_Nr'
      Origin = 'Ohrmarken_Nr'
    end
    object qBetriebTKVGeburtsdatum: TDateField
      FieldName = 'Geburtsdatum'
      Origin = 'Geburtsdatum'
    end
    object qBetriebTKVJahr: TIntegerField
      FieldName = 'Jahr'
      Origin = 'Jahr'
    end
    object qBetriebTKVMonat: TIntegerField
      FieldName = 'Monat'
      Origin = 'Monat'
    end
  end
  object QBetriebSFU: TFDQuery
    MasterSource = DMStammdaten.DSBetriebeSuche
    MasterFields = 'REGNR'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select *'
      'from BEWEGUNGSDATEN.SFU_KONTROLLBESUCHE SFU'
      'where SFU.REG_NR_BEST = :REGNR'
      'order by SFU.DATUM desc')
    Left = 336
    Top = 288
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = '2827492'
      end>
    object QBetriebSFUID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QBetriebSFUSTICHTAG: TDateField
      FieldName = 'STICHTAG'
      Origin = 'STICHTAG'
      Required = True
    end
    object QBetriebSFUBKB_NR: TStringField
      FieldName = 'BKB_NR'
      Origin = 'BKB_NR'
      Required = True
      FixedChar = True
      Size = 26
    end
    object QBetriebSFUPARENT_BKB_NR: TStringField
      FieldName = 'PARENT_BKB_NR'
      Origin = 'PARENT_BKB_NR'
      FixedChar = True
      Size = 26
    end
    object QBetriebSFUBKB_NAME: TStringField
      FieldName = 'BKB_NAME'
      Origin = 'BKB_NAME'
      FixedChar = True
      Size = 100
    end
    object QBetriebSFUDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
      Required = True
    end
    object QBetriebSFUART: TStringField
      FieldName = 'ART'
      Origin = 'ART'
      FixedChar = True
      Size = 1
    end
    object QBetriebSFUTIERART: TStringField
      FieldName = 'TIERART'
      Origin = 'TIERART'
      FixedChar = True
      Size = 2
    end
    object QBetriebSFUREG_NR_ORT: TStringField
      FieldName = 'REG_NR_ORT'
      Origin = 'REG_NR_ORT'
      FixedChar = True
      Size = 7
    end
    object QBetriebSFUGEMEINDE_ORT: TStringField
      FieldName = 'GEMEINDE_ORT'
      Origin = 'GEMEINDE_ORT'
      FixedChar = True
      Size = 5
    end
    object QBetriebSFUXKOORD: TFMTBCDField
      FieldName = 'XKOORD'
      Origin = 'XKOORD'
      Precision = 31
    end
    object QBetriebSFUYKOORD: TFMTBCDField
      FieldName = 'YKOORD'
      Origin = 'YKOORD'
      Precision = 31
    end
    object QBetriebSFUREG_NR_BEST: TStringField
      FieldName = 'REG_NR_BEST'
      Origin = 'REG_NR_BEST'
      Required = True
      FixedChar = True
      Size = 7
    end
    object QBetriebSFULAND_BEST: TStringField
      FieldName = 'LAND_BEST'
      Origin = 'LAND_BEST'
      FixedChar = True
      Size = 2
    end
    object QBetriebSFUANZ_TIERE_BESTAND: TIntegerField
      FieldName = 'ANZ_TIERE_BESTAND'
      Origin = 'ANZ_TIERE_BESTAND'
    end
    object QBetriebSFUANZ_TIERE_KONTR: TIntegerField
      FieldName = 'ANZ_TIERE_KONTR'
      Origin = 'ANZ_TIERE_KONTR'
    end
    object QBetriebSFUERGEBNIS: TStringField
      FieldName = 'ERGEBNIS'
      Origin = 'ERGEBNIS'
      FixedChar = True
      Size = 1
    end
    object QBetriebSFUKTR_TIERARZT_NR: TStringField
      FieldName = 'KTR_TIERARZT_NR'
      Origin = 'KTR_TIERARZT_NR'
      FixedChar = True
      Size = 5
    end
    object QBetriebSFUKTR_SONSTIGE: TStringField
      FieldName = 'KTR_SONSTIGE'
      Origin = 'KTR_SONSTIGE'
      FixedChar = True
      Size = 100
    end
    object QBetriebSFUBKB_STATUS: TStringField
      FieldName = 'BKB_STATUS'
      Origin = 'BKB_STATUS'
      FixedChar = True
      Size = 1
    end
    object QBetriebSFUPRIORITAET: TStringField
      FieldName = 'PRIORITAET'
      Origin = 'PRIORITAET'
      FixedChar = True
      Size = 1
    end
    object QBetriebSFUSEUCHEN_ID: TStringField
      FieldName = 'SEUCHEN_ID'
      Origin = 'SEUCHEN_ID'
      FixedChar = True
    end
  end
  object QBetriebKontrollen: TFDQuery
    MasterSource = DMStammdaten.DSBetriebeSuche
    MasterFields = 'REGNR'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * from BEWEGUNGSDATEN.KONTROLLBERICHT'
      'where KONTROLLBERICHT.BETRIEB_REGNR = :regnr'
      'order by effective_date desc')
    Left = 424
    Top = 288
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = '2827492'
      end>
    object QBetriebKontrollenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QBetriebKontrollenGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object QBetriebKontrollenBKB: TStringField
      FieldName = 'BKB'
      Origin = 'BKB'
      Required = True
      Size = 26
    end
    object QBetriebKontrollenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Required = True
      Size = 10
    end
    object QBetriebKontrollenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      FixedChar = True
      Size = 3
    end
    object QBetriebKontrollenDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
    end
    object QBetriebKontrollenERFASSER_PSKEY: TStringField
      FieldName = 'ERFASSER_PSKEY'
      Origin = 'ERFASSER_PSKEY'
      FixedChar = True
      Size = 7
    end
    object QBetriebKontrollenKONTROLLORGAN_PSKEY: TStringField
      FieldName = 'KONTROLLORGAN_PSKEY'
      Origin = 'KONTROLLORGAN_PSKEY'
      FixedChar = True
      Size = 7
    end
    object QBetriebKontrollenID_PERSON_ERFASSER: TIntegerField
      FieldName = 'ID_PERSON_ERFASSER'
      Origin = 'ID_PERSON_ERFASSER'
    end
    object QBetriebKontrollenID_PERSON_KONTROLLORGAN: TIntegerField
      FieldName = 'ID_PERSON_KONTROLLORGAN'
      Origin = 'ID_PERSON_KONTROLLORGAN'
    end
    object QBetriebKontrollenREF_BKB: TStringField
      FieldName = 'REF_BKB'
      Origin = 'REF_BKB'
      Size = 26
    end
    object QBetriebKontrollenPROBENZIEHUNG: TBooleanField
      FieldName = 'PROBENZIEHUNG'
      Origin = 'PROBENZIEHUNG'
      Required = True
    end
    object QBetriebKontrollenID_BETRIEB: TIntegerField
      FieldName = 'ID_BETRIEB'
      Origin = 'ID_BETRIEB'
      Required = True
    end
    object QBetriebKontrollenREGNR_ORT: TStringField
      FieldName = 'REGNR_ORT'
      Origin = 'REGNR_ORT'
      FixedChar = True
      Size = 7
    end
    object QBetriebKontrollenKURZBEMERKUNG: TWideMemoField
      FieldName = 'KURZBEMERKUNG'
      Origin = 'KURZBEMERKUNG'
      BlobType = ftWideMemo
      Size = 2147483647
    end
    object QBetriebKontrollenSTARTZEIT: TSQLTimeStampField
      FieldName = 'STARTZEIT'
      Origin = 'STARTZEIT'
    end
    object QBetriebKontrollenENDEZEIT: TSQLTimeStampField
      FieldName = 'ENDEZEIT'
      Origin = 'ENDEZEIT'
    end
    object QBetriebKontrollenBESTAETIGT_UM: TSQLTimeStampField
      FieldName = 'BESTAETIGT_UM'
      Origin = 'BESTAETIGT_UM'
    end
    object QBetriebKontrollenID_RECHTSGRUNDLAGE: TIntegerField
      FieldName = 'ID_RECHTSGRUNDLAGE'
      Origin = 'ID_RECHTSGRUNDLAGE'
      Required = True
    end
    object QBetriebKontrollenSTATUS: TStringField
      FieldName = 'STATUS'
      Origin = 'STATUS'
      FixedChar = True
      Size = 1
    end
    object QBetriebKontrollenANGEMELDET_UM: TSQLTimeStampField
      FieldName = 'ANGEMELDET_UM'
      Origin = 'ANGEMELDET_UM'
    end
    object QBetriebKontrollenTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object QBetriebKontrollenLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object QBetriebKontrollenBETRIEBSTYP: TStringField
      FieldName = 'BETRIEBSTYP'
      Origin = 'BETRIEBSTYP'
      Required = True
      Size = 2
    end
    object QBetriebKontrollenGUID_UNTERSCHRIFT_ANWESENDER_BETRIEB: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Origin = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Size = 38
    end
    object QBetriebKontrollenGUID_UNTERSCHRIFT_KONTROLLORGAN: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Origin = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Size = 38
    end
    object QBetriebKontrollenVERWEIGERUNGSGRUND_UNTERSCHRIFT: TStringField
      FieldName = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Origin = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Size = 1000
    end
    object QBetriebKontrollenVerweigerunggrund: TStringField
      FieldName = 'Verweigerunggrund'
      Origin = 'Verweigerunggrund'
      Size = 1000
    end
    object QBetriebKontrollenGUID_DOKUMENT: TGuidField
      FieldName = 'GUID_DOKUMENT'
      Origin = 'GUID_DOKUMENT'
      Size = 38
    end
    object QBetriebKontrollenFEHLERHAFT_GESETZT_AM: TSQLTimeStampField
      FieldName = 'FEHLERHAFT_GESETZT_AM'
      Origin = 'FEHLERHAFT_GESETZT_AM'
    end
    object QBetriebKontrollenSTORNIERT_AM: TSQLTimeStampField
      FieldName = 'STORNIERT_AM'
      Origin = 'STORNIERT_AM'
    end
    object QBetriebKontrollenSTORNOGRUND: TStringField
      FieldName = 'STORNOGRUND'
      Origin = 'STORNOGRUND'
      Size = 1000
    end
    object QBetriebKontrollenVERWEIGERT_AM: TSQLTimeStampField
      FieldName = 'VERWEIGERT_AM'
      Origin = 'VERWEIGERT_AM'
    end
    object QBetriebKontrollenVISEXPORT_AM: TSQLTimeStampField
      FieldName = 'VISEXPORT_AM'
      Origin = 'VISEXPORT_AM'
    end
    object QBetriebKontrollenGUID_DOKUMENT_CC: TGuidField
      FieldName = 'GUID_DOKUMENT_CC'
      Origin = 'GUID_DOKUMENT_CC'
      Size = 38
    end
    object QBetriebKontrollenKONTROLL_INFORMATIONEN: TStringField
      FieldName = 'KONTROLL_INFORMATIONEN'
      Origin = 'KONTROLL_INFORMATIONEN'
      Size = 4000
    end
    object QBetriebKontrollenID_GRUPPE_QUELLE: TIntegerField
      FieldName = 'ID_GRUPPE_QUELLE'
      Origin = 'ID_GRUPPE_QUELLE'
    end
    object QBetriebKontrollenID_REVISIONSPLAN: TIntegerField
      FieldName = 'ID_REVISIONSPLAN'
      Origin = 'ID_REVISIONSPLAN'
    end
    object QBetriebKontrollenINTERNE_NOTIZ: TWideMemoField
      FieldName = 'INTERNE_NOTIZ'
      Origin = 'INTERNE_NOTIZ'
      BlobType = ftWideMemo
      Size = 2147483647
    end
    object QBetriebKontrollenEffective_Date: TDateField
      FieldName = 'Effective_Date'
      Origin = 'Effective_Date'
    end
    object QBetriebKontrollenBETRIEB_NAME: TWideStringField
      FieldName = 'BETRIEB_NAME'
      Origin = 'BETRIEB_NAME'
      Size = 255
    end
    object QBetriebKontrollenBETRIEB_REGNR: TStringField
      FieldName = 'BETRIEB_REGNR'
      Origin = 'BETRIEB_REGNR'
      FixedChar = True
      Size = 7
    end
    object QBetriebKontrollenBETRIEB_BLD: TSmallintField
      FieldName = 'BETRIEB_BLD'
      Origin = 'BETRIEB_BLD'
    end
    object QBetriebKontrollenBETRIEB_ADRESSE_STRASSE: TWideStringField
      FieldName = 'BETRIEB_ADRESSE_STRASSE'
      Origin = 'BETRIEB_ADRESSE_STRASSE'
      Size = 150
    end
    object QBetriebKontrollenBETRIEB_ADRESSE_ZUSATZ: TWideStringField
      FieldName = 'BETRIEB_ADRESSE_ZUSATZ'
      Origin = 'BETRIEB_ADRESSE_ZUSATZ'
      Size = 150
    end
    object QBetriebKontrollenBETRIEB_ADRESSE_PLZ: TStringField
      FieldName = 'BETRIEB_ADRESSE_PLZ'
      Origin = 'BETRIEB_ADRESSE_PLZ'
      Size = 7
    end
    object QBetriebKontrollenBETRIEB_ADRESSE_ORT: TWideStringField
      FieldName = 'BETRIEB_ADRESSE_ORT'
      Origin = 'BETRIEB_ADRESSE_ORT'
      Size = 150
    end
    object QBetriebKontrollenKONTROLLTYP_BEZEICHNUNG: TStringField
      FieldName = 'KONTROLLTYP_BEZEICHNUNG'
      Origin = 'KONTROLLTYP_BEZEICHNUNG'
      Size = 80
    end
    object QBetriebKontrollenBKBTYP_BEZEICHNUNG: TStringField
      FieldName = 'BKBTYP_BEZEICHNUNG'
      Origin = 'BKBTYP_BEZEICHNUNG'
      Size = 50
    end
    object QBetriebKontrollenERFASSER_NACHNAME: TWideStringField
      FieldName = 'ERFASSER_NACHNAME'
      Origin = 'ERFASSER_NACHNAME'
      Size = 60
    end
    object QBetriebKontrollenKONTROLLORGAN_NACHNAME: TWideStringField
      FieldName = 'KONTROLLORGAN_NACHNAME'
      Origin = 'KONTROLLORGAN_NACHNAME'
      Size = 60
    end
    object QBetriebKontrollenERFASSER_VORNAME: TWideStringField
      FieldName = 'ERFASSER_VORNAME'
      Origin = 'ERFASSER_VORNAME'
      Size = 60
    end
    object QBetriebKontrollenERFASSER_ANREDE: TWideStringField
      FieldName = 'ERFASSER_ANREDE'
      Origin = 'ERFASSER_ANREDE'
    end
    object QBetriebKontrollenERFASSER_TITEL: TWideStringField
      FieldName = 'ERFASSER_TITEL'
      Origin = 'ERFASSER_TITEL'
    end
    object QBetriebKontrollenKONTROLLORGAN_VORNAME: TWideStringField
      FieldName = 'KONTROLLORGAN_VORNAME'
      Origin = 'KONTROLLORGAN_VORNAME'
      Size = 60
    end
    object QBetriebKontrollenKONTROLLORGAN_ANREDE: TWideStringField
      FieldName = 'KONTROLLORGAN_ANREDE'
      Origin = 'KONTROLLORGAN_ANREDE'
    end
    object QBetriebKontrollenKONTROLLORGAN_TITEL: TWideStringField
      FieldName = 'KONTROLLORGAN_TITEL'
      Origin = 'KONTROLLORGAN_TITEL'
    end
    object QBetriebKontrollenMAENGEL_GESAMT: TIntegerField
      FieldName = 'MAENGEL_GESAMT'
      Origin = 'MAENGEL_GESAMT'
    end
    object QBetriebKontrollenMAENGEL_OFFEN: TIntegerField
      FieldName = 'MAENGEL_OFFEN'
      Origin = 'MAENGEL_OFFEN'
    end
    object QBetriebKontrollenTODO_COUNT: TIntegerField
      FieldName = 'TODO_COUNT'
      Origin = 'TODO_COUNT'
    end
  end
end
