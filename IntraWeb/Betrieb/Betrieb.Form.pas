﻿unit Betrieb.Form;

interface

uses
  System.Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, IWCGJQControl,
  IWCGJQAccordion, IWCGJQGrid, IWCGJQButton, Vcl.Imaging.jpeg, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls,
  Vcl.Forms, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQRegion, Vcl.Graphics,
  IWCompLabel, IWCGJQLabel, IWCGJQEdit, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, Data.DB, IWCompEdit, IWDBStdCtrls, IWCompMemo,
  IWCompGrids, IWDBGrids, IWCompTabControl, IWCGJQResponsiveList, IWCGJQCommon,
  IWCGFrame, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component,
  IWCGJQComp, IWCGJQSweetAlert, IWCGPanelList, IWCGJQPrint, IWCGJQPDFViewer,
  IWCGJQMemo, System.generics.collections, IWCGJQDownload, IWCGJQFileDownload,
  IWCGJQCheckBoxList, FireDAC.Comp.Client, Forms.Base, Betrieb.Revstamm.Frame,
  frxClass, frxDBSet, frxExportBaseDialog, frxExportPDF, DetailFenster,
  Betrieb.Module, Modules.Stammdaten,
  IWCGJQSelectableList, IWCGJQLockIndicatorBase, IWCGJQLockIndicator, frCoreClasses, IWCGJQDropDown;

type
  TFormBetrieb = class(TFormBase)
    GridBetriebe: TIWCGJQGrid;
    RegionMain: TIWCGJQRegion;
    ds_einzelbetrieb: TDataSource;
    ds_Zulassung: TDataSource;
    ds_Betriebstypen: TDataSource;
    ds_Zulnr: TDataSource;
    ds_ZulassungsArten: TDataSource;
    ds_tat: TDataSource;
    saBetriebsfen: TIWCGJQSweetAlert;
    TabControlMain: TIWTabControl;
    TabStammdaten: TIWTabPage;
    TabZulassung: TIWTabPage;
    TabTaetigkeiten: TIWTabPage;
    LabelAdressdaten: TIWCGJQLabel;
    iwcgpl_Taetigkeiten: TIWCGPanelList;
    iwcgpl_TaetigGrp: TIWCGPanelList;
    IWLabel12: TIWCGJQLabel;
    MemoAnmerkung: TIWCGJQMemo;
    iwla_Anmerkungen: TIWCGJQLabel;
    jqdStammdat: TIWCGJQFileDownload;
    iwcgbtn_loschen: TIWCGJQButton;
    ProviderKontrollen: TIWCGJQGridDataSetProvider;
    IWLabel3: TIWCGJQLabel;
    RegionTabTaetigkeiten: TIWCGJQRegion;
    TabRevisionsstamm: TIWTabPage;
    RegionTabs: TIWCGJQRegion;
    frxBetriebsstammdaten: TfrxReport;
    frxStammdaten: TfrxDBDataset;
    frxPDFExportBetriebsstammdaten: TfrxPDFExport;
    LabelCount: TIWLabel;
    ListMarkierungen: TIWCGJQSelectableList;
    EditBetriebsuche: TIWCGJQEdit;
    ProvBetriebe: TIWCGJQGridDataSetProvider;
    RegionTabRevisionsstam: TIWCGJQRegion;
    RegionTabStammdaten: TIWCGJQRegion;
    RegionTabZulassung: TIWCGJQRegion;
    TabKontrollen: TIWTabPage;
    RegionTabKontrollen: TIWCGJQRegion;
    GridKontrollen: TIWCGJQGrid;
    TabTierartenNeu: TIWTabPage;
    RegionTabTierartenNeu: TIWCGJQRegion;
    GridTierarten: TIWCGJQGrid;
    ProviderTierarten: TIWCGJQGridDataSetProvider;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQRegion2: TIWCGJQRegion;
    IWCGJQLabel1: TIWCGJQLabel;
    GridZulassungen: TIWCGJQGrid;
    ProviderZulassungen: TIWCGJQGridDataSetProvider;
    IWCGJQRegion3: TIWCGJQRegion;
    IWCGJQRegion4: TIWCGJQRegion;
    IWCGJQLabel2: TIWCGJQLabel;
    ListStammdatenBetrieb: TIWCGPanelList;
    RegionRevPlan: TIWCGJQRegion;
    EditRevSchema: TIWCGJQDropDown;
    Revisionsschema: TIWCGJQLabel;
    IWCGJQLabel4: TIWCGJQLabel;
    EditRevGruppe: TIWCGJQDropDown;
    LabelRevisionsplan: TIWCGJQLabel;
    procedure iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure ButtonSucheBetriebClick(Sender: TObject; AParams: TStringList);
    procedure GridBetriebeSelectRow(Sender: TObject; AParams: TStringList);
    procedure IWAppFormCreate(Sender: TObject);
    procedure iwcgbt_AnmSpeichernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_loschenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure jqbDownloadStammdatenOnClick(Sender: TObject; AParams: TStringList);
    procedure MemoAnmerkungAsyncChange(Sender: TObject; EventParams: TStringList);
    procedure ListMarkierungenJQSelectableListOptionsSelected(Sender: TObject; AParams: TStringList);
    procedure GridBetriebeJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    regnr: String;
    FDMBetrieb: TDMBetrieb;
    FDMStammdaten: TDMStammdaten;

    FrameRevisionsStamm: TFrameBetriebsRevstamm;

    procedure ResetBetriebssuche;

    function DM: TDMBetrieb;

    procedure MarkierungenLaden;
  public
  end;

implementation

uses FireDAC.Stan.Param, dmmain, JQ.Helpers.Grid, Utility, TierartenFrame, System.DateUtils;

{$R *.dfm}


procedure TFormBetrieb.IWAppFormCreate(Sender: TObject);
begin
  FDMStammdaten := TDMStammdaten.Create(self);
  FDMBetrieb := TDMBetrieb.Create(self);
  MarkierungenLaden;

  TabControlMain.ActivePage := 0;
  TabTaetigkeiten.Visible := false;  //https://esculenta.atlassian.net/browse/EIW-391

  ResetBetriebssuche;

  GridBetriebe.SetupDefaults(ProvBetriebe);
  GridKontrollen.SetupDefaults(ProviderKontrollen);
  GridTierarten.SetupDefaults(ProviderTierarten);
  GridZulassungen.SetupDefaults(ProviderZulassungen);

  // Den Sortier-Index für die FlagColumn hier explizit setzen
  var
  LFlagColumn := GridBetriebe.JQGridOptions.ColModel.ItemsByName['FlagHTML'];
  LFlagColumn.Idx := 'MarkierungBeschreibung';

  // Revisionsstamm als Fraem einhängen
  FrameRevisionsStamm := TFrameBetriebsRevstamm.Create(self, Alert, DM);
  FrameRevisionsStamm.Parent := RegionTabRevisionsstam;
end;

function TFormBetrieb.DM: TDMBetrieb;
begin
  result := FDMBetrieb;
end;

procedure TFormBetrieb.iwcgbtn_loschenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  ResetBetriebssuche;
end;

procedure TFormBetrieb.iwcgbt_AnmSpeichernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  if MemoAnmerkung.Lines.Text.Length <= 500 then
  begin
    dm_main.qu_UpdateAnmerkung.ParamByName('regnr').AsString := regnr;
    dm_main.qu_UpdateAnmerkung.ParamByName('anmerk').AsString := MemoAnmerkung.Lines.Text;
    dm_main.qu_UpdateAnmerkung.ExecSQL;
  end;
end;

procedure TFormBetrieb.ButtonSucheBetriebClick(Sender: TObject; AParams: TStringList);
begin
  if EditBetriebsuche.Text <> '' then
  begin
    try
      var
      LSuche := EditBetriebsuche.Text;
      ResetBetriebssuche;
      FDMStammdaten.BetriebeSuchen(LSuche);
      LabelCount.Caption := ProvBetriebe.DataSet.RecordCount.ToString + ' Ergebnisse gefunden.';
    except
      Alert.Error('Fehler bei Datenabfrage!', 'Bitte probieren Sie es erneut!');
      abort;
    end;
  end
  else
  begin
    Alert.Error('Suchfeld darf nicht leer sein!');
    abort;
  end;
end;

procedure TFormBetrieb.iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  dm_main.qu_Zulassungen.Close;
  dm_main.qu_einzelBetrieb.Close;
  dm_main.qu_Betriebstypen.Close;
  dm_main.qu_zulassungsarten.Close;
  dm_main.qu_Taetigkeiten.Close;
  dm_main.qu_TaetigGrp.Close;
  dm_main.qu_tierarten.Close;
  FDMStammdaten.QBetriebeSuche.Close;
  release;
end;

procedure TFormBetrieb.GridBetriebeJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
 // Keinen Record automatisch auswählen!  GridBetriebe.SelectRowFromCurrentRecord;
end;

{ Wird aufgerufen, wenn ein Betrieb ausgewählt wird und füllt alle Tabs mit den
  benötigten Daten }
procedure TFormBetrieb.GridBetriebeSelectRow(Sender: TObject; AParams: TStringList);
var
  betriebId: Integer;
  Name, adresse, plz, ort, email, telefonnr, gemeinde, zulassungsnummer: String;
  helper: string;
  betrtypen: TStringList;
begin
  // Die Regnr vom ausgewählten Betrieb von der query holen
  GridBetriebe.SelectRecordFromCurrentRow;
  regnr := FDMStammdaten.QBetriebeSucheREGNR.AsString;
  zulassungsnummer := FDMStammdaten.QBetriebeSucheZulassungsNr.AsString;
  betriebId := FDMStammdaten.QBetriebeSucheID.AsInteger;

  // Lösche alles vom vorherig ausgewählten Betrieb
  MemoAnmerkung.Clear;
  ListStammdatenBetrieb.Items.Clear;
  iwcgpl_TaetigGrp.Items.Clear;

  dm_main.qu_Zulassungen.Close;
  dm_main.qu_einzelBetrieb.Close;
  dm_main.qu_Betriebstypen.Close;
  dm_main.qu_zulassungsarten.Close;
  dm_main.qu_Taetigkeiten.Close;
  dm_main.qu_TaetigGrp.Close;
  dm_main.qu_tierarten.Close;

  // Starte die Queries
  dm_main.qu_einzelBetrieb.ParamByName('Id').AsInteger := betriebId;
  dm_main.qu_Zulassungen.ParamByName('regnr').AsString := regnr;
  dm_main.qu_Betriebstypen.ParamByName('regnr').AsString := regnr;
  dm_main.qu_zulassungsarten.ParamByName('regnr').AsString := regnr;
  dm_main.qu_Taetigkeiten.ParamByName('regnr').AsString := regnr;
  dm_main.qu_TaetigGrp.ParamByName('regnr').AsString := regnr;
  dm_main.qu_tierarten.ParamByName('regnr').AsString := regnr;
  dm_main.qu_zulassungenBetr.ParamByName('regnr').AsString := regnr;
  dm_main.qu_TaetigGrp.Open;
  dm_main.qu_Zulassungen.Open;
  dm_main.qu_einzelBetrieb.Open;
  dm_main.qu_Betriebstypen.Open;
  dm_main.qu_zulassungsarten.Open;
  dm_main.qu_Taetigkeiten.Open;
  dm_main.qu_tierarten.Open;
  dm_main.qu_zulassungenBetr.Open;

  // Stammdaten-Tab mit Daten füllen
  dm_main.qu_einzelBetrieb.First;
  name := dm_main.qu_einzelBetrieb.FieldByName('name').AsString;
  adresse := dm_main.qu_einzelBetrieb.FieldByName('strasse').AsString;
  plz := dm_main.qu_einzelBetrieb.FieldByName('plz').AsString;
  ort := dm_main.qu_einzelBetrieb.FieldByName('ort').AsString;
  email := dm_main.qu_einzelBetrieb.FieldByName('email').AsString;
  telefonnr := dm_main.qu_einzelBetrieb.FieldByName('telefon').AsString;
  gemeinde := dm_main.qu_einzelBetrieb.FieldByName('gemeindename').AsString;
  MemoAnmerkung.Lines.Text := dm_main.qu_einzelBetriebAnmerkung.AsString;

  var
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'Name: ' + name;
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'Adresse: ' + adresse;
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'PLZ/Ort: ' + plz + '/' + ort;
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'E-Mail: ' + email;
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'Telefon: ' + telefonnr;

  if gemeinde.trim > '' then
  begin
  LItemBetrieb := ListStammdatenBetrieb.Items.Add;
  LItemBetrieb.Caption := 'Gemeinde: ' + gemeinde;
  end;

  if zulassungsnummer.trim > '' then
  begin
    LItemBetrieb := ListStammdatenBetrieb.Items.Add;
    LItemBetrieb.Caption := 'Zulassungsnummer: ' + zulassungsnummer;
  end;

  betrtypen := TStringList.Create;
  try
    // Revisionsstamm
    FrameRevisionsStamm.InitRevstamm(betriebId);

    // Die Seiten mit Informationen füllen
    dm_main.qu_Betriebstypen.First;
    while not dm_main.qu_Betriebstypen.Eof do
    begin
      helper := dm_main.qu_Betriebstypen.FieldByName('Betriebstypen').AsString;
      betrtypen.Add(helper);
      dm_main.qu_Betriebstypen.Next
    end;
  finally
    betrtypen.Free;
  end;

  var LListItemTaetigkeit:TIWCGPanelListItem;
  dm_main.qu_Taetigkeiten.First;
  while not dm_main.qu_Taetigkeiten.Eof do
  begin
    LListItemTaetigkeit := iwcgpl_Taetigkeiten.Items.Add;
    LListItemTaetigkeit.Caption := dm_main.qu_Taetigkeiten.FieldByName('bezeichnung').AsString;
    dm_main.qu_Taetigkeiten.Next;
  end;

  var LListItemGrp:TIWCGPanelListItem;
  dm_main.qu_TaetigGrp.First;
  while not dm_main.qu_TaetigGrp.Eof do
  begin
    LListItemGrp := iwcgpl_TaetigGrp.Items.Add;
    LListItemGrp.Caption := dm_main.qu_TaetigGrp.FieldByName('Bezeichnung').AsString;
    dm_main.qu_TaetigGrp.Next;
  end;

  // Kontroll-Tab
  dm_main.qu_KontrollenVonBetrieb.Close;
  dm_main.qu_KontrollenVonBetrieb.ParamByName('IDBetrieb').AsInteger := betriebId;
  dm_main.qu_KontrollenVonBetrieb.Open;
  GridKontrollen.JQGridOptions.ReloadGrid;

  // Tierarten-Tab
  FDMStammdaten.QBetriebTierartenTaetigkeiten.Close;
  FDMStammdaten.QBetriebTierartenTaetigkeiten.ParamByName('DATUM').AsDate := Today;
  FDMStammdaten.QBetriebTierartenTaetigkeiten.Open;

  //Zulassungen
  FDMStammdaten.QBetriebZulassungen.Close;
  FDMStammdaten.QBetriebZulassungen.ParamByName('DATUM').AsDate := Today;
  FDMStammdaten.QBetriebZulassungen.Open;


  MarkierungenLaden;

  // Tabs aktivieren
  RegionTabTaetigkeiten.Visible := false;  //https://esculenta.atlassian.net/browse/EIW-391
  RegionTabTierartenNeu.Visible := true;
  RegionTabStammdaten.Visible := true;
  RegionTabRevisionsstam.Visible := true;
  RegionTabZulassung.Visible := true;
  RegionTabKontrollen.Visible := true;

  RegionTabs.AjaxReRender;
end;

procedure TFormBetrieb.ListMarkierungenJQSelectableListOptionsSelected(Sender: TObject; AParams: TStringList);
begin
  var
  LSelectedIndex := StrToIntDef(AParams.Values['LISTMARKIERUNGEN_JQ_DATA'], -1);
  if LSelectedIndex > -1 then
  begin
    var
    LSelected := FDMStammdaten.QBetriebeSuche.GetBookmark;
    FDMStammdaten.QBetriebeSuche.Edit;
    try
      try
        if LSelectedIndex = 0 then
        begin
          FDMStammdaten.QBetriebeSucheID_MARKIERUNG.Clear;
        end
        else
        begin
          if DM.QMarkierungen.Locate('RowIndex', LSelectedIndex) then
          begin
            FDMStammdaten.QBetriebeSucheID_MARKIERUNG.AsGuid := DM.QMarkierungenID.AsGuid;
          end;
        end;
        FDMStammdaten.QBetriebeSuche.Post;
        MarkierungenLaden;

        GridBetriebe.Refresh;
      except
        FDMStammdaten.QBetriebeSuche.Cancel;
        raise;
      end;
    finally
      FDMStammdaten.QBetriebeSuche.GotoBookmark(LSelected);
      //GridBetriebe.SelectRowFromCurrentRecord;
    end;
  end;
end;

procedure TFormBetrieb.ResetBetriebssuche;
begin
  // RegionTabs.Visible := false;
  EditBetriebsuche.Text := '';
  FDMStammdaten.QBetriebeSuche.Close;
  regnr := '';
  MemoAnmerkung.Enabled := false;
  ListStammdatenBetrieb.Items.Clear;
  LabelCount.Caption := '';

  RegionTabTaetigkeiten.Visible := false;
  RegionTabTierartenNeu.Visible;
  RegionTabStammdaten.Visible := false;
  RegionTabRevisionsstam.Visible := false;
  RegionTabZulassung.Visible := false;
  RegionTabKontrollen.Visible := false;
end;

procedure TFormBetrieb.jqbDownloadStammdatenOnClick(
  Sender: TObject; AParams: TStringList);
var
  filename: String;
  date: String;
begin
  if dm_main.qu_einzelBetrieb.RecordCount = 0 then
    Exit;

  if frxBetriebsstammdaten.PrepareReport then
  begin
    if not DirectoryExists('exports') then
      if not CreateDir('exports') then
        Exit;

    filename := dm_main.qu_einzelBetrieb.FieldByName('name').AsString + ' ';
    date := FormatDateTime('dd-mm-yyyy nn-hh', Now);
    filename := filename + date + '.pdf';
    frxPDFExportBetriebsstammdaten.Title := filename;
    frxPDFExportBetriebsstammdaten.filename := 'exports\' + filename;
    frxPDFExportBetriebsstammdaten.OpenAfterExport := false;
    frxBetriebsstammdaten.Export(frxPDFExportBetriebsstammdaten);
    jqdStammdat.DownloadFileName('exports\' + filename);
  end;
end;

procedure TFormBetrieb.MarkierungenLaden;
begin
  // Liste der verfügbaren Markierungen aufbauen
  ListMarkierungen.Items.Clear;
  RefreshQuery(DM.QMarkierungen);

  // Zunächst einen Eintrag für "Keine Markierung" hinzufügen
  var
  LListItemKeine := ListMarkierungen.Items.Add;
  LListItemKeine.Selected := true;

  // Nun die vorhandenen Markierungen aus der DB lesen und hinzufügen
  while not DM.QMarkierungen.Eof do
  begin
    var
    LListItem := ListMarkierungen.Items.Add;
    LListItem.Selected := false;
    var
    LSelectedClass := '';
    // Feststellen ob der Betrieb eine Markierung hat - diese als "selektiert" anzeigen
    if FDMStammdaten.QBetriebeSucheID_MARKIERUNG.AsString = DM.QMarkierungenID.AsString then
    begin
      LListItemKeine.Selected := false;
      LListItem.Selected := true;
      LSelectedClass := 'markierung-selektiert'
    end;
    LListItem.Caption := Format('<div class="markierung %s %s"  title="%s">&nbsp;</div>',
      [LSelectedClass, DM.QMarkierungenWERT.AsString, DM.QMarkierungenBESCHREIBUNG.AsString]);

    DM.QMarkierungen.Next;
  end;

  // Den Eintrag für "keine Markierung" selektieren, wenn der Betrieb bisher keine Markierung hat
  var
  LClass := 'markierung';
  if LListItemKeine.Selected then
  begin
    LClass := LClass + ' markierung-selektiert';
  end;
  // Das DIV für "Keine Markierung" wird erst hier erzeugt, wenn festeht ob selektiert oder nicht.
  LListItemKeine.Caption := Format('<div class="%s" title="%s">&nbsp;&nbsp;</div>', [LClass, 'Keine Markierung']);

  if gGetWebApplicationThreadVar.CallBackProcessing then
  begin
    ListMarkierungen.AjaxReRender;
  end;
end;

procedure TFormBetrieb.MemoAnmerkungAsyncChange(Sender: TObject; EventParams: TStringList);
begin
  if MemoAnmerkung.Lines.Text.Length > 500 then
  begin
    MemoAnmerkung.Lines.Text := Copy(MemoAnmerkung.Lines.Text, 500);
  end;

end;

end.
