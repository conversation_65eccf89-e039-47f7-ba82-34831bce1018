inherited FrameBetriebsRevstamm: TFrameBetriebsRevstamm
  Width = 1288
  Height = 591
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1288
    Height = 591
    TabOrder = 1
    inherited jqdDialog: TIWCGJQDialog [0]
      Left = 30
      Top = 20
      Width = 1000
      Height = 500
      TabOrder = 11
      JQDialogOptions.Height = 500
      JQDialogOptions.Width = 1000
      object jqgRevstamm: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1000
        Height = 500
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SEKTION'
            Name = 'SEKTION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'SEKTION'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSGRUPPE_LM'
            Name = 'BETRIEBSGRUPPE_LM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BETRIEBSGRUPPE_LM'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSGRUPPE_DETAIL'
            Name = 'BETRIEBSGRUPPE_DETAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BETRIEBSGRUPPE_DETAIL'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSART'
            Name = 'BETRIEBSART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BETRIEBSART'
          end>
        JQGridOptions.Height = 446
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 998
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderRevstamm
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    inherited iwrMid: TIWCGJQRegion [1]
      Top = 100
      Width = 1288
      Height = 444
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        Width = 1288
        Height = 444
        TabOrder = 13
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SEKTION'
            Name = 'SEKTION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Sektion'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSGRUPPE_LM'
            Name = 'BETRIEBSGRUPPE_LM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe LM'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSGRUPPE_DETAIL'
            Name = 'BETRIEBSGRUPPE_DETAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe Detail'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSART'
            Name = 'BETRIEBSART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsart'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfNumber
            Idx = 'KontrollFrequenz'
            Name = 'KontrollFrequenz'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollfrequenz'
            Position = 6
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'BEGDATE'
            Name = 'BEGDATE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Begdat'
            Position = 4
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'ENDDATE'
            Name = 'ENDDATE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Enddat'
            Position = 5
          end>
        JQGridOptions.Height = 417
        JQGridOptions.Width = 1286
      end
    end
    inherited iwrTop: TIWCGJQRegion [2]
      Width = 1288
      Height = 100
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        Width = 544
        Height = 100
        TabOrder = 4
        object IWLabel1: TIWCGJQLabel
          Left = 16
          Top = 19
          Width = 106
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Revisionsstamm:'
        end
        object IWLabel2: TIWCGJQLabel
          Left = 282
          Top = 19
          Width = 29
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Von:'
        end
        object IWLabel3: TIWCGJQLabel
          Left = 409
          Top = 19
          Width = 23
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Bis:'
        end
        object iwcVon: TIWCheckBox
          Left = 282
          Top = 68
          Width = 121
          Height = 21
          Caption = 'Nach Von filtern'
          Editable = True
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          SubmitOnAsyncEvent = True
          Style = stNormal
          TabOrder = 8
          Checked = False
          FriendlyName = 'iwcVon'
        end
        object iwcBis: TIWCheckBox
          Left = 409
          Top = 68
          Width = 121
          Height = 21
          Caption = 'Nach Bis filtern'
          Editable = True
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          SubmitOnAsyncEvent = True
          Style = stNormal
          TabOrder = 10
          Checked = False
          FriendlyName = 'iwcBis'
        end
        object jqdVon: TIWCGJQDatePicker
          Left = 282
          Top = 41
          Width = 121
          Height = 21
          TabOrder = 12
          Version = '1.0'
          ZIndex = 5001
          Caption = ''
          JQDatePickerOptions.CurrentText = 'Heute'
          JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
          JQDatePickerOptions.NextText = 'N'#228'chstes'
          JQDatePickerOptions.PrevText = 'Vorheriges'
          JQDatePickerOptions.Regional = dporGerman
        end
        object jqdBis: TIWCGJQDatePicker
          Left = 409
          Top = 41
          Width = 121
          Height = 21
          TabOrder = 14
          Version = '1.0'
          ZIndex = 5001
          Caption = ''
          JQDatePickerOptions.CurrentText = 'Heute'
          JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
          JQDatePickerOptions.NextText = 'N'#228'chstes'
          JQDatePickerOptions.PrevText = 'Vorheriges'
          JQDatePickerOptions.Regional = dporGerman
        end
        object jqclRevstamm: TIWCGJQCheckBoxList
          Left = 16
          Top = 41
          Width = 250
          Height = 21
          TabOrder = 15
          Version = '1.0'
          ZIndex = 5001
          JQCheckBoxListOptions.MinWidth = 250
          JQCheckBoxListOptions.CheckAllText = 'Alle ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Revisionsstamm ausw'#228'hlen'
          JQCheckBoxListOptions.SelectedText = '# ausgew'#228'hlt'
          JQCheckBoxListOptions.MenuZIndex = 10000
          Items = <>
          Groups = <>
        end
      end
      inherited iwrButtons: TIWCGJQRegion
        Left = 544
        Height = 100
        TabOrder = 5
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 16
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 8
        end
        inherited jqbLoeschen: TIWCGJQButton
          Left = 387
          TabOrder = 10
        end
      end
    end
    object iwrBot: TIWCGJQRegion
      Left = 0
      Top = 544
      Width = 1288
      Height = 47
      TabOrder = 17
      Version = '1.0'
      Align = alBottom
      DesignSize = (
        1288
        47)
      object LabelEndDat: TIWCGJQLabel
        Left = 897
        Top = 13
        Width = 73
        Height = 16
        Anchors = [akRight, akBottom]
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelEndDat'
        Caption = 'End Datum:'
      end
      object labelBegdat: TIWCGJQLabel
        Left = 662
        Top = 13
        Width = 84
        Height = 16
        Anchors = [akRight, akBottom]
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'labelBegdat'
        Caption = 'Begin Datum:'
      end
      object ButtonChange: TIWCGJQButton
        Left = 1131
        Top = 9
        Width = 137
        Height = 27
        TabOrder = 19
        Version = '1.0'
        Anchors = [akRight, akBottom]
        JQButtonOptions.Label_ = 'Datum '#228'ndern'
        JQButtonOptions.OnClick.OnEvent = ButtonChangeJQButtonOptionsClick
      end
      object DatepickerEnd: TIWCGJQDatePicker
        Left = 982
        Top = 13
        Width = 121
        Height = 21
        TabOrder = 20
        Version = '1.0'
        Anchors = [akRight, akBottom]
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
      end
      object DatepickerBegin: TIWCGJQDatePicker
        Left = 756
        Top = 13
        Width = 121
        Height = 21
        TabOrder = 18
        Version = '1.0'
        Anchors = [akRight, akBottom]
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = DMBetrieb.quRevstamm
    KeyFields = 'ID'
    Left = 1240
  end
  object ProviderRevstamm: TIWCGJQGridDataSetProvider
    DataSet = DMBetrieb.quRevstammSuche
    KeyFields = 'ID'
    Left = 825
    Top = 25
  end
end
