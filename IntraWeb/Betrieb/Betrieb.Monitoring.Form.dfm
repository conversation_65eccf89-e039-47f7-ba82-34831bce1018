﻿inherited FormBetriebsMonitoring: TFormBetriebsMonitoring
  Height = 697
  Title = 'Betriebsmonitoring'
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    TabOrder = 0
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 1
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 672
    TabOrder = 10
  end
  object RegionTop: TIWCGJQRegion [2]
    Left = 0
    Top = 50
    Width = 1081
    Height = 247
    RenderInvisibleControls = True
    TabOrder = 2
    Version = '1.0'
    Align = alTop
    object RegionSearch: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1081
      Height = 65
      TabOrder = 4
      Version = '1.0'
      Align = alTop
      object LabelCount: TIWLabel
        Left = 725
        Top = 27
        Width = 69
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        NoWrap = True
        HasTabOrder = False
        FriendlyName = 'LabelCount'
        Caption = 'LabelCount'
      end
      object ButtonBetriebssuche: TIWCGJQButton
        Left = 596
        Top = 19
        Width = 75
        Height = 30
        TabOrder = 5
        Version = '1.0'
        JQButtonOptions.Label_ = 'Suchen'
        JQButtonOptions.OnClick.Ajax = False
        JQButtonOptions.OnClick.OnEvent = ButtonBetriebssucheJQButtonOptionsClick
        JQButtonOptions.OnClick.SendAllArguments = True
        Default.Enabled = True
      end
      object ButtonSucheLöschen: TIWCGJQButton
        Left = 678
        Top = 19
        Width = 30
        Height = 30
        TabOrder = 6
        Version = '1.0'
        JQButtonOptions.Text = False
        JQButtonOptions.Icons.Primary = 'ui-icon-close'
        JQButtonOptions.OnClick.OnEvent = ButtonSucheLöschenJQButtonOptionsClick
      end
      object EditBetriebsuche: TIWCGJQEdit
        Left = 24
        Top = 19
        Width = 551
        Height = 30
        TabOrder = 7
        Font.Size = 12
        Version = '1.0'
        ScriptEvents = <>
        Text = ''
      end
    end
    object GridBetriebe: TIWCGJQGrid
      AlignWithMargins = True
      Left = 8
      Top = 68
      Width = 1065
      Height = 171
      Margins.Left = 8
      Margins.Right = 8
      Margins.Bottom = 8
      TabOrder = 8
      Version = '1.0'
      Align = alClient
      JQGridOptions.ColModel = <
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'FlagHTML'
          Name = 'FlagHTML'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 25
          Caption = #55356#57332
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'BVBKZ'
          Name = 'BVBKZ'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 100
          Caption = 'BVB'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          FirstSortOrder = gcfsoDesc
          Idx = 'REGNR'
          Name = 'REGNR'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Sortable = False
          Width = 100
          Caption = 'Reg. Nr.'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'ZulassungsNr'
          Name = 'ZulassungsNr'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Zulassungsnummer'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'NAME'
          Name = 'NAME'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 200
          Caption = 'Name'
        end
        item
          Align = gaRight
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'GEMEINDEKENNZIFFER'
          Name = 'GEMEINDEKENNZIFFER'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'GEMEINDEKENNZIFFER'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'GEMEINDENAME'
          Name = 'GEMEINDENAME'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Gemeinde'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'STRASSE'
          Name = 'STRASSE'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Stra'#223'e'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'PLZ'
          Name = 'PLZ'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 75
          Caption = 'PLZ'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'ORT'
          Name = 'ORT'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Ort'
        end>
      JQGridOptions.DeselectAfterSort = False
      JQGridOptions.Height = 144
      JQGridOptions.PgButtons = False
      JQGridOptions.Sortable = True
      JQGridOptions.SortName = 'NAME'
      JQGridOptions.SubGridModel = <>
      JQGridOptions.Width = 1063
      JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
      JQGridOptions.OnSelectRow.OnEvent = GridBetriebeJQGridOptionsSelectRow
      JQGridOptions.OnSelectRow.SendAllArguments = True
      JQGridOptions.AjaxSelectOptions.Async = False
      JQGridOptions.PagerVisible = False
      JQGridNav.Active = False
      JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
      JQGridCustomButtons = <>
      JQGridProvider = ProvBetriebe
      JQGridToolbarSearch.DefaultSearch = gsoContains
      JQDragAndDropOptions.ConnectWith = <>
    end
  end
  object RegionMain: TIWCGJQRegion [3]
    Left = 0
    Top = 297
    Width = 1081
    Height = 375
    TabOrder = 3
    Version = '1.0'
    Align = alClient
    object IWCGJQLabel1: TIWCGJQLabel
      Left = 0
      Top = 0
      Width = 1081
      Height = 16
      Align = alTop
      Css = 'ui-widget h2'
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWCGJQLabel1'
      Caption = 'Monitoring Details'
    end
    object TabsBetriebsdaten: TIWCGJQTabs
      Left = 0
      Top = 16
      Width = 1081
      Height = 359
      TabOrder = 9
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      ActiveTab = TabKontrollen
      object TabTKV: TIWCGJQTab
        Css = ''
        Version = '1.0'
        Caption = 'TKV-Daten'
        TabIndex = 2
        Tabs = TabsBetriebsdaten
        object GridTKV: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 0
          Height = 0
          TabOrder = 12
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'L_Dat'
              Name = 'L_Dat'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'L_Dat'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Auftrag_Nr'
              Name = 'Auftrag_Nr'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Auftrag Nr.'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Beh'#228'ltertyp'
              Name = 'Beh'#228'ltertyp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Beh'#228'ltertyp'
            end
            item
              Align = gaRight
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Menge'
              Name = 'Menge'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Menge'
              Position = 5
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'LE'
              Name = 'LE'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Einheit'
              Position = 3
            end
            item
              Align = gaRight
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Menge2'
              Name = 'Menge2'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Menge 2'
              Position = 6
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'LE2'
              Name = 'LE2'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Einheit 2'
              Position = 4
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'LO_Name1'
              Name = 'LO_Name1'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'LO_Name2'
              Name = 'LO_Name2'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Name 2'
            end>
          JQGridOptions.Height = -54
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = -2
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProvTKV
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object TabSFU: TIWCGJQTab
        Css = ''
        Version = '1.0'
        Caption = 'SFU-Daten'
        TabIndex = 1
        Tabs = TabsBetriebsdaten
        object GridSFU: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 0
          Height = 0
          TabOrder = 11
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'DATUM'
              Name = 'DATUM'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'DATUM'
              Position = 3
            end
            item
              Align = gaRight
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ANZ_TIERE_BESTAND'
              Name = 'ANZ_TIERE_BESTAND'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'ANZ_TIERE_BESTAND'
              Position = 0
            end
            item
              Align = gaRight
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ANZ_TIERE_KONTR'
              Name = 'ANZ_TIERE_KONTR'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'ANZ_TIERE_KONTR'
              Position = 1
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ART'
              Name = 'ART'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'ART'
              Position = 2
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ERGEBNIS'
              Name = 'ERGEBNIS'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'ERGEBNIS'
            end>
          JQGridOptions.Height = -54
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = -2
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProvSFU
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object TabKontrollen: TIWCGJQTab
        Css = ''
        Version = '1.0'
        Caption = 'ELKE-Kontrollen'
        TabIndex = 0
        Tabs = TabsBetriebsdaten
        object GridKontrollen: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 1077
          Height = 321
          TabOrder = 13
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Effective_Date'
              Name = 'Effective_Date'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Datum'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BKB'
              Name = 'BKB'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'BKB'
              Position = 4
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'KONTROLLTYP_BEZEICHNUNG'
              Name = 'KONTROLLTYP_BEZEICHNUNG'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kontrolltyp'
              Position = 1
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'KONTROLL_INFORMATIONEN'
              Name = 'KONTROLL_INFORMATIONEN'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kontrollinformationen'
              Position = 2
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'KONTROLLORGAN_NACHNAME'
              Name = 'KONTROLLORGAN_NACHNAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kontrollorgan'
              Position = 3
            end>
          JQGridOptions.Height = 267
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 1075
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProvKontrollen
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
  end
  object ProvBetriebe: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebeSuche
    KeyFields = 'FullID'
    Left = 600
    Top = 8
  end
  object ProvSFU: TIWCGJQGridDataSetProvider
    DataSet = DMBetrieb.QBetriebSFU
    Left = 304
    Top = 368
  end
  object ProvTKV: TIWCGJQGridDataSetProvider
    DataSet = DMBetrieb.qBetriebTKV
    Left = 416
    Top = 368
  end
  object ProvKontrollen: TIWCGJQGridDataSetProvider
    DataSet = DMBetrieb.QBetriebKontrollen
    Left = 192
    Top = 368
  end
end
