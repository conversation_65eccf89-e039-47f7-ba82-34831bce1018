﻿unit Betrieb.RevStamm.Frame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCGJQDatePicker, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQComboBox, IWCGJQCheckBoxList,
  IWCompCheckbox, IWCGJQRegion, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQSweetAlert, IWApplication, IWCGJQLabel, Betrieb.Module;

type
  TFrameBetriebsRevstamm = class(TCRUDGrid)
    jqgRevstamm: TIWCGJQGrid;
    ProviderRevstamm: TIWCGJQGridDataSetProvider;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqdVon: TIWCGJQDatePicker;
    jqdBis: TIWCGJQDatePicker;
    IWLabel3: TIWCGJQLabel;
    jqclRevstamm: TIWCGJQCheckBoxList;
    iwcVon: TIWCheckBox;
    iwcBis: TIWCheckBox;
    iwrBot: TIWCGJQRegion;
    LabelEndDat: TIWCGJQLabel;
    labelBegdat: TIWCGJQLabel;
    ButtonChange: TIWCGJQButton;
    DatepickerEnd: TIWCGJQDatePicker;
    DatepickerBegin: TIWCGJQDatePicker;
    procedure jqgGridJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
    procedure ButtonChangeJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FDM: TDMBetrieb;

    idBetrieb: Integer;

    procedure FillCheckBoxList(ajax: boolean);
    procedure RevstammAbfragen;
    procedure InitDialog;
    procedure NeuBestaetigt;
    procedure AendernBestaetigt;
    procedure LoeschenBestaetigt;
  protected
    procedure ResetModal; Override;
    function DM: TDMBetrieb;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; ADM: TDMBetrieb); reintroduce;
    procedure InitRevstamm(idBetrieb: Integer);
  end;

implementation

uses Utility, JQ.Helpers.Grid, dmmain, System.DateUtils;

{$R *.dfm}


procedure TFrameBetriebsRevstamm.ButtonChangeJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  DM.quAendern.ParamByName('id').AsInteger := DM.quRevstammID.Value;
  DM.quAendern.ParamByName('begdate').AsDate := DatepickerBegin.Date;
  DM.quAendern.ParamByName('enddate').AsDate := DatepickerEnd.Date;
  DM.quAendern.Execute;
  DM.quRevstamm.Refresh;
  jqgGrid.JQGridOptions.ReloadGrid;
end;

constructor TFrameBetriebsRevstamm.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; ADM: TDMBetrieb);
begin
  inherited Create(AOwner, AAlert);
  FDM := ADM;
  idBetrieb := -1;
  EnableAbfragen(DM.quRevstamm, RevstammAbfragen);
  EnableSearch;
  EnableNeu('Neue Zuweisung', InitDialog, NeuBestaetigt);
  EnableAendern('Zuweisung ändern', InitDialog, AendernBestaetigt);
  EnableLoeschen('Wollen Sie die Zuweisung wirklich löschen?', LoeschenBestaetigt);

end;

function TFrameBetriebsRevstamm.DM: TDMBetrieb;
begin
  result := FDM;
end;

procedure TFrameBetriebsRevstamm.InitRevstamm(idBetrieb: Integer);
begin
  Self.idBetrieb := idBetrieb;

  DM.quRevstamm.Active := false;
  FillCheckBoxList(false);
  jqdVon.Date := IncMonth(Now, -12);
  jqdBis.Date := Now;
end;

procedure TFrameBetriebsRevstamm.jqgGridJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
begin
  inherited;
  jqgGrid.SelectRecordFromCurrentRow;
  DatepickerEnd.Date := DM.quRevstamm.FieldByName('enddate').AsDateTime;
  DatepickerBegin.Date := DM.quRevstamm.FieldByName('begdate').AsDateTime;
end;

procedure TFrameBetriebsRevstamm.FillCheckBoxList(ajax: boolean);
var
  item: TIWCGJQCheckBoxItem;
begin
  // Die CheckBoxList mit den Revisionsstämmen füllen
  DM.quRevisionsstaemme.Prepare;
  DM.quRevisionsstaemme.ParamByName('idbetrieb').AsInteger := idBetrieb;
  DM.quRevisionsstaemme.Open;
  DM.quRevisionsstaemme.First;
  jqclRevstamm.Items.Clear;
  while not DM.quRevisionsstaemme.Eof do
  begin
    item := jqclRevstamm.Items.Add;
    item.Caption := DM.quRevisionsstaemme.FieldByName('Betriebsart').AsString;
    item.Value := DM.quRevisionsstaemme.FieldByName('Id').AsString;
    DM.quRevisionsstaemme.Next;
  end;
  DM.quRevisionsstaemme.Close;
  if ajax then
    jqclRevstamm.AjaxReRender;
  jqclRevstamm.CheckAll;
end;

procedure TFrameBetriebsRevstamm.RevstammAbfragen;
begin
  DM.quRevstamm.Close;

  // Die selektierten Revisionstämme in den List-Parameter (Macro) packen
  var
    LInlist := '(-1,';  //-1 ist ein dummy fals nichst selektiert ist
  for var i := 0 to jqclRevstamm.Items.count - 1 do
  begin
    if jqclRevstamm.Items[i].Selected then
    begin
      LInlist := LInlist + jqclRevstamm.Items[i].Value + ',';
    end;
  end;
  if LInlist.EndsWith(',') then
  begin
    LInlist := LInlist.Remove(LInlist.Length - 1);
  end;
  LInlist := LInlist + ')';
  DM.quRevstamm.MacroByName('inlist').AsRaw := LInlist;

  DM.quRevstamm.ParamByName('idbetrieb').AsInteger := idBetrieb;
  if iwcVon.Checked then
  begin
    DM.quRevstamm.ParamByName('begdat').AsDate := jqdVon.Date;
  end
  else
  begin
    DM.quRevstamm.ParamByName('begdat').AsDate := EncodeDate(2000, 01, 01);
  end;
  if iwcBis.Checked then
  begin
    DM.quRevstamm.ParamByName('enddat').AsDate := jqdBis.Date;
  end
  else
  begin
    DM.quRevstamm.ParamByName('enddat').AsDate := EncodeDate(2999, 12, 31);
  end;
  DM.quRevstamm.Active := true;
end;

procedure TFrameBetriebsRevstamm.InitDialog;
begin
  DM.quRevstammSuche.Close;
  DM.quRevstammSuche.Prepare;
  DM.quRevstammSuche.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  DM.quRevstammSuche.Active := true;
end;

procedure TFrameBetriebsRevstamm.NeuBestaetigt;
var
  idrevstamm: Integer;
  begdat, enddat: TDate;
begin
  if not MoveQueryToRow(DM.quRevstammSuche, jqgRevstamm) then
  begin
    Alert.Error('Es muss eine Reihe ausgewählt werden');
    Abort;
  end;
  idrevstamm := DM.quRevstammSuche.FieldByName('id').AsInteger;
  begdat := Now;
  enddat := Now;
  enddat := IncMonth(enddat, 12 * 500);

  DM.quNeu.Close;
  DM.quNeu.Prepare;
  DM.quNeu.ParamByName('idbetrieb').AsInteger := idBetrieb;
  DM.quNeu.ParamByName('idrevstamm').AsInteger := idrevstamm;
  DM.quNeu.ParamByName('begdat').AsDate := begdat;
  DM.quNeu.ParamByName('enddat').AsDate := enddat;
  DM.quNeu.Execute;

  FillCheckBoxList(true);
end;

// Da man die Zuweisung nicht ändern kann (Primary-Key Beschränkung) wird die
// alte Zuweisung gelöscht und eine Neue hinzugefügt.
procedure TFrameBetriebsRevstamm.AendernBestaetigt;
begin
  NeuBestaetigt;
  LoeschenBestaetigt;
end;

procedure TFrameBetriebsRevstamm.LoeschenBestaetigt;
var
  id: Integer;
begin
  id := DM.quRevstamm.FieldByName('id_revisionsstamm').AsInteger;
  DM.quLoeschen.Close;
  DM.quLoeschen.Prepare;
  DM.quLoeschen.ParamByName('idrevstamm').AsInteger := id;
  DM.quLoeschen.ParamByName('idbetrieb').AsInteger := idBetrieb;
  DM.quLoeschen.Execute;
end;

procedure TFrameBetriebsRevstamm.ResetModal;
begin
  DM.quRevstammSuche.Close;
end;

end.
