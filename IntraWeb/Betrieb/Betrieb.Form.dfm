inherited FormBetrieb: TFormBetrieb
  Width = 1177
  Height = 711
  Title = 'Betrieb'
  HandleTabs = True
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1177
    TabOrder = 11
    inherited ImageLogo: TIWImageFile
      Left = 894
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 685
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 17
    end
  end
  object RegionMain: TIWCGJQRegion [1]
    Left = 0
    Top = 50
    Width = 1177
    Height = 636
    RenderInvisibleControls = True
    TabOrder = 12
    Version = '1.0'
    Align = alClient
    object GridBetriebe: TIWCGJQGrid
      AlignWithMargins = True
      Left = 8
      Top = 75
      Width = 1161
      Height = 146
      Margins.Left = 8
      Margins.Right = 8
      Margins.Bottom = 8
      TabOrder = 4
      Version = '1.0'
      Align = alTop
      JQGridOptions.ColModel = <
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'FlagHTML'
          Name = 'FlagHTML'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 25
          Caption = #55356#57332
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'BVBKZ'
          Name = 'BVBKZ'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 100
          Caption = 'BVB'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          FirstSortOrder = gcfsoDesc
          Idx = 'REGNR'
          Name = 'REGNR'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Sortable = False
          Width = 100
          Caption = 'Reg. Nr.'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'ZulassungsNr'
          Name = 'ZulassungsNr'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Zulassungsnummer'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'NAME'
          Name = 'NAME'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 200
          Caption = 'Name'
        end
        item
          Align = gaRight
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'GEMEINDEKENNZIFFER'
          Name = 'GEMEINDEKENNZIFFER'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'GEMEINDEKENNZIFFER'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'GEMEINDENAME'
          Name = 'GEMEINDENAME'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Gemeinde'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'STRASSE'
          Name = 'STRASSE'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Stra'#223'e'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'PLZ'
          Name = 'PLZ'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Width = 75
          Caption = 'PLZ'
        end
        item
          Editable = True
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'ORT'
          Name = 'ORT'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Ort'
        end>
      JQGridOptions.DeselectAfterSort = False
      JQGridOptions.Height = 119
      JQGridOptions.PgButtons = False
      JQGridOptions.Sortable = True
      JQGridOptions.SortName = 'NAME'
      JQGridOptions.SubGridModel = <>
      JQGridOptions.Width = 1159
      JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
      JQGridOptions.OnLoadComplete.OnEvent = GridBetriebeJQGridOptionsLoadComplete
      JQGridOptions.OnSelectRow.OnEvent = GridBetriebeSelectRow
      JQGridOptions.OnSelectRow.SendAllArguments = True
      JQGridOptions.AjaxSelectOptions.Async = False
      JQGridOptions.PagerVisible = False
      JQGridNav.Active = False
      JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
      JQGridCustomButtons = <>
      JQGridProvider = ProvBetriebe
      JQGridToolbarSearch.DefaultSearch = gsoContains
      JQDragAndDropOptions.ConnectWith = <>
    end
    object RegionTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1177
      Height = 72
      RenderInvisibleControls = True
      TabOrder = 13
      Version = '1.0'
      Align = alTop
      object LabelCount: TIWLabel
        Left = 725
        Top = 27
        Width = 69
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        NoWrap = True
        HasTabOrder = False
        FriendlyName = 'LabelCount'
        Caption = 'LabelCount'
      end
      object ButtonBetriebssuche: TIWCGJQButton
        Left = 596
        Top = 19
        Width = 75
        Height = 30
        TabOrder = 6
        Version = '1.0'
        JQButtonOptions.Label_ = 'Suchen'
        JQButtonOptions.OnClick.Ajax = False
        JQButtonOptions.OnClick.OnEvent = ButtonSucheBetriebClick
        JQButtonOptions.OnClick.SendAllArguments = True
        Default.Enabled = True
      end
      object iwcgbtn_loschen: TIWCGJQButton
        Left = 678
        Top = 19
        Width = 30
        Height = 30
        TabOrder = 7
        Version = '1.0'
        JQButtonOptions.Text = False
        JQButtonOptions.Icons.Primary = 'ui-icon-close'
        JQButtonOptions.OnClick.OnEvent = iwcgbtn_loschenJQButtonOptionsClick
      end
      object EditBetriebsuche: TIWCGJQEdit
        Left = 24
        Top = 19
        Width = 551
        Height = 30
        TabOrder = 18
        Font.Size = 12
        Version = '1.0'
        ScriptEvents = <>
        Text = ''
      end
    end
    object RegionTabs: TIWCGJQRegion
      Left = 0
      Top = 229
      Width = 1177
      Height = 407
      TabOrder = 14
      Version = '1.0'
      Align = alClient
      object TabControlMain: TIWTabControl
        AlignWithMargins = True
        Left = 8
        Top = 3
        Width = 1161
        Height = 401
        Margins.Left = 8
        Margins.Right = 8
        RenderInvisibleControls = False
        ActiveTabFont.Color = clWebWHITE
        ActiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
        ActiveTabFont.Size = 10
        ActiveTabFont.Style = [fsBold]
        InactiveTabFont.Color = clWebBLACK
        InactiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
        InactiveTabFont.Size = 10
        InactiveTabFont.Style = []
        ActiveTabColor = clWebCORNFLOWERBLUE
        InactiveTabColor = clWebLIGHTGRAY
        ActivePage = 0
        Align = alClient
        BorderOptions.NumericWidth = 0
        BorderOptions.Style = cbsNone
        Color = clWebSILVER
        ClipRegion = False
        TabMargin = 3
        TabPadding = 0
        TabRowHeight = 25
        TabBorderRadius = 3
        ActiveTabBorder.Color = clWebBLACK
        ActiveTabBorder.Width = 1
        InactiveTabBorder.Color = clWebBLACK
        InactiveTabBorder.Width = 1
        DesignSize = (
          1161
          401)
        object TabKontrollen: TIWTabPage
          Left = 0
          Top = 25
          Width = 1161
          Height = 376
          RenderInvisibleControls = True
          TabOrder = 9
          Title = 'Kontrollen'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabKontrollen: TIWCGJQRegion
            AlignWithMargins = True
            Left = 16
            Top = 16
            Width = 1129
            Height = 344
            Margins.Left = 16
            Margins.Top = 16
            Margins.Right = 16
            Margins.Bottom = 16
            TabOrder = 23
            Version = '1.0'
            Align = alClient
            object GridKontrollen: TIWCGJQGrid
              Left = 0
              Top = 0
              Width = 1129
              Height = 344
              TabOrder = 24
              Version = '1.0'
              Align = alClient
              JQGridOptions.ColModel = <
                item
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Idx = 'BKB'
                  Name = 'BKB'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'BKB'
                end
                item
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Formatter = gcfDate
                  Idx = 'ENDEZEIT'
                  Name = 'ENDEZEIT'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'ENDEZEIT'
                end
                item
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Idx = 'KONTROLLTYP'
                  Name = 'KONTROLLTYP'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'KONTROLLTYP'
                end
                item
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Idx = 'BKBTYP'
                  Name = 'BKBTYP'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'BKBTYP'
                end
                item
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Idx = 'KURZBEMERKUNG'
                  Name = 'KURZBEMERKUNG'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'KURZBEMERKUNG'
                end>
              JQGridOptions.Height = 290
              JQGridOptions.SubGridModel = <>
              JQGridOptions.Width = 1127
              JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
              JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
              JQGridCustomButtons = <>
              JQGridProvider = ProviderKontrollen
              JQGridToolbarSearch.DefaultSearch = gsoContains
              JQDragAndDropOptions.ConnectWith = <>
            end
          end
        end
        object TabTaetigkeiten: TIWTabPage
          Left = 5
          Top = 20
          Width = 1161
          Height = 365
          RenderInvisibleControls = False
          TabOrder = 3
          Title = 'T'#228'tigkeiten'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabTaetigkeiten: TIWCGJQRegion
            Left = 0
            Top = 0
            Width = 1161
            Height = 365
            RenderInvisibleControls = True
            TabOrder = 15
            Version = '1.0'
            Align = alClient
            DesignSize = (
              1161
              365)
            object IWLabel12: TIWCGJQLabel
              Left = 30
              Top = 36
              Width = 191
              Height = 23
              StyleRenderOptions.RenderFont = True
              Font.Color = clNone
              Font.Size = 14
              Font.Style = [fsBold]
              HasTabOrder = False
              FriendlyName = 'IWLabel12'
              Caption = 'T'#228'tigkeitsgruppen'
            end
            object IWLabel3: TIWCGJQLabel
              Left = 710
              Top = 36
              Width = 118
              Height = 23
              StyleRenderOptions.RenderFont = True
              Font.Color = clNone
              Font.Size = 14
              Font.Style = [fsBold]
              HasTabOrder = False
              FriendlyName = 'IWLabel12'
              Caption = 'T'#228'tigkeiten'
            end
            object iwcgpl_Taetigkeiten: TIWCGPanelList
              Left = 710
              Top = 72
              Width = 416
              Height = 272
              TabOrder = 10
              Version = '1.0'
              Anchors = [akLeft, akTop, akRight, akBottom]
              Items = <>
              ItemHeight = 28
            end
            object iwcgpl_TaetigGrp: TIWCGPanelList
              Left = 30
              Top = 72
              Width = 650
              Height = 272
              TabOrder = 9
              Version = '1.0'
              Anchors = [akLeft, akTop, akBottom]
              Items = <>
              ItemHeight = 28
            end
          end
        end
        object TabZulassung: TIWTabPage
          Left = 0
          Top = 20
          Width = 1161
          Height = 365
          RenderInvisibleControls = False
          TabOrder = 2
          Title = 'Zulassungen'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabZulassung: TIWCGJQRegion
            AlignWithMargins = True
            Left = 8
            Top = 8
            Width = 1145
            Height = 349
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            TabOrder = 22
            Version = '1.0'
            Align = alClient
            object IWCGJQRegion3: TIWCGJQRegion
              Left = 0
              Top = 0
              Width = 1145
              Height = 24
              TabOrder = 29
              Version = '1.0'
              Align = alTop
              object IWCGJQLabel2: TIWCGJQLabel
                Left = 0
                Top = 0
                Width = 346
                Height = 24
                Align = alLeft
                Font.Color = clNone
                Font.Size = 12
                Font.Style = [fsBold]
                HasTabOrder = False
                FriendlyName = 'IWCGJQLabel1'
                Caption = 'Aktuelle Zulassungen'
              end
            end
            object IWCGJQRegion4: TIWCGJQRegion
              AlignWithMargins = True
              Left = 0
              Top = 32
              Width = 1145
              Height = 317
              Margins.Left = 0
              Margins.Top = 8
              Margins.Right = 0
              Margins.Bottom = 0
              TabOrder = 30
              Version = '1.0'
              Align = alClient
              object GridZulassungen: TIWCGJQGrid
                Left = 0
                Top = 0
                Width = 1145
                Height = 317
                TabOrder = 31
                Version = '1.0'
                Align = alClient
                JQGridOptions.ColModel = <
                  item
                    EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                    EditOptions.CustomElements = <>
                    Idx = 'ZULASSUNGSNUMMER'
                    Name = 'ZULASSUNGSNUMMER'
                    SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                    Caption = 'Zulassungsnummer'
                    ProviderName = 'ZULASSUNGSNUMMER'
                  end
                  item
                    EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                    EditOptions.CustomElements = <>
                    Idx = 'Betriebstyp'
                    Name = 'Betriebstyp'
                    SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                    Caption = 'Betriebstyp'
                    ProviderName = 'Betriebstyp'
                  end
                  item
                    EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                    EditOptions.CustomElements = <>
                    Idx = 'Zulassungstyp'
                    Name = 'Zulassungstyp'
                    SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                    Caption = 'Zulassungstyp'
                    ProviderName = 'Zulassungstyp'
                  end>
                JQGridOptions.Height = 263
                JQGridOptions.SubGridModel = <>
                JQGridOptions.Width = 1143
                JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
                JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
                JQGridCustomButtons = <>
                JQGridProvider = ProviderZulassungen
                JQGridToolbarSearch.DefaultSearch = gsoContains
                JQDragAndDropOptions.ConnectWith = <>
              end
            end
          end
        end
        object TabTierartenNeu: TIWTabPage
          Left = 0
          Top = 25
          Width = 1161
          Height = 376
          RenderInvisibleControls = True
          TabOrder = 4
          Title = 'Tierarten'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabTierartenNeu: TIWCGJQRegion
            AlignWithMargins = True
            Left = 8
            Top = 8
            Width = 1145
            Height = 360
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            TabOrder = 25
            Version = '1.0'
            Align = alClient
            object IWCGJQRegion1: TIWCGJQRegion
              Left = 0
              Top = 0
              Width = 1145
              Height = 24
              TabOrder = 26
              Version = '1.0'
              Align = alTop
              object IWCGJQLabel1: TIWCGJQLabel
                Left = 0
                Top = 0
                Width = 346
                Height = 24
                Align = alLeft
                Font.Color = clNone
                Font.Size = 12
                Font.Style = [fsBold]
                HasTabOrder = False
                FriendlyName = 'IWCGJQLabel1'
                Caption = 'Zugelassene Tierarten und T'#228'tigkeiten'
              end
            end
            object IWCGJQRegion2: TIWCGJQRegion
              AlignWithMargins = True
              Left = 0
              Top = 32
              Width = 1145
              Height = 328
              Margins.Left = 0
              Margins.Top = 8
              Margins.Right = 0
              Margins.Bottom = 0
              TabOrder = 27
              Version = '1.0'
              Align = alClient
              object GridTierarten: TIWCGJQGrid
                Left = 0
                Top = 0
                Width = 465
                Height = 328
                TabOrder = 28
                Version = '1.0'
                Align = alLeft
                JQGridOptions.ColModel = <
                  item
                    EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                    EditOptions.CustomElements = <>
                    Idx = 'TIERART'
                    Name = 'TIERART'
                    SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                    Caption = 'Tierart'
                    ProviderName = 'TIERART'
                  end
                  item
                    EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                    EditOptions.CustomElements = <>
                    Idx = 'Taetigkeit'
                    Name = 'Taetigkeit'
                    SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                    Caption = 'Taetigkeit'
                    ProviderName = 'Taetigkeit'
                  end>
                JQGridOptions.Height = 274
                JQGridOptions.SubGridModel = <>
                JQGridOptions.Width = 463
                JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
                JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
                JQGridCustomButtons = <>
                JQGridProvider = ProviderTierarten
                JQGridToolbarSearch.DefaultSearch = gsoContains
                JQDragAndDropOptions.ConnectWith = <>
              end
            end
          end
        end
        object TabRevisionsstamm: TIWTabPage
          Left = 0
          Top = 30
          Width = 1161
          Height = 365
          RenderInvisibleControls = False
          TabOrder = 6
          Title = 'Revisionsstamm'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabRevisionsstam: TIWCGJQRegion
            AlignWithMargins = True
            Left = 16
            Top = 16
            Width = 1129
            Height = 333
            Margins.Left = 16
            Margins.Top = 16
            Margins.Right = 16
            Margins.Bottom = 16
            TabOrder = 19
            Version = '1.0'
            Align = alClient
          end
        end
        object TabStammdaten: TIWTabPage
          Left = 0
          Top = 20
          Width = 1161
          Height = 365
          RenderInvisibleControls = False
          TabOrder = 1
          Title = 'Stammdaten'
          BorderOptions.NumericWidth = 0
          BorderOptions.Style = cbsNone
          Color = clWebWHITE
          object RegionTabStammdaten: TIWCGJQRegion
            Left = 0
            Top = 0
            Width = 1161
            Height = 365
            TabOrder = 20
            Version = '1.0'
            Align = alClient
            DesignSize = (
              1161
              365)
            object LabelAdressdaten: TIWCGJQLabel
              Left = 88
              Top = 16
              Width = 113
              Height = 23
              Font.Color = clNone
              Font.Size = 14
              Font.Style = []
              HasTabOrder = False
              FriendlyName = 'LabelAdressdaten'
              Caption = 'Adressdaten'
            end
            object iwla_Anmerkungen: TIWCGJQLabel
              Left = 635
              Top = 24
              Width = 129
              Height = 23
              Font.Color = clNone
              Font.Size = 14
              Font.Style = []
              HasTabOrder = False
              FriendlyName = 'iwla_Anmerkungen'
              Caption = 'Anmerkungen'
            end
            object MemoAnmerkung: TIWCGJQMemo
              Left = 635
              Top = 76
              Width = 510
              Height = 276
              Anchors = [akLeft, akTop, akRight, akBottom]
              StyleRenderOptions.RenderFont = False
              StyleRenderOptions.RenderBorder = False
              BGColor = clNone
              Editable = True
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              InvisibleBorder = False
              HorizScrollBar = False
              VertScrollBar = True
              Required = False
              SubmitOnAsyncEvent = True
              ResizeDirection = rdNone
              Enabled = False
              FriendlyName = 'MemoAnmerkung'
              OnAsyncChange = MemoAnmerkungAsyncChange
            end
            object ListMarkierungen: TIWCGJQSelectableList
              Left = 16
              Top = 79
              Width = 40
              Height = 25
              TabOrder = 21
              Version = '1.0'
              Style.Selecting = <>
              Style.Selected = <>
              Style.TagLi = <>
              Style.TagSpan = <>
              JQSelectableListOptions.OnSelected.OnEvent = ListMarkierungenJQSelectableListOptionsSelected
              Items = <
                item
                  Caption = 'aaa'
                  Selected = True
                  CaptionPaddingLeft = 0
                end
                item
                  Caption = 'bbb'
                  CaptionPaddingLeft = 0
                end
                item
                  Caption = 'ccc'
                  CaptionPaddingLeft = 0
                end>
              ItemHeight = 15
            end
            object ListStammdatenBetrieb: TIWCGPanelList
              Left = 88
              Top = 76
              Width = 521
              Height = 157
              TabOrder = 32
              Version = '1.0'
              Anchors = [akLeft, akTop, akBottom]
              Items = <>
            end
            object RegionRevPlan: TIWCGJQRegion
              Left = 88
              Top = 268
              Width = 521
              Height = 76
              Visible = False
              TabOrder = 35
              Version = '1.0'
              Anchors = [akLeft, akBottom]
              object Revisionsschema: TIWCGJQLabel
                Left = 3
                Top = 26
                Width = 106
                Height = 16
                Font.Color = clNone
                Font.Size = 10
                Font.Style = []
                HasTabOrder = False
                FriendlyName = 'Revisionsschema'
                Caption = 'Revisionsschema'
              end
              object IWCGJQLabel4: TIWCGJQLabel
                Left = 215
                Top = 26
                Width = 102
                Height = 16
                Font.Color = clNone
                Font.Size = 10
                Font.Style = []
                HasTabOrder = False
                FriendlyName = 'Revisionsschema'
                Caption = 'Revisionsgruppe'
              end
              object LabelRevisionsplan: TIWCGJQLabel
                Left = 3
                Top = -3
                Width = 123
                Height = 23
                Font.Color = clNone
                Font.Size = 14
                Font.Style = []
                HasTabOrder = False
                FriendlyName = 'LabelRevisionsPlan'
                Caption = 'Revisionsplan'
              end
              object EditRevSchema: TIWCGJQDropDown
                Left = 0
                Top = 48
                Width = 209
                Height = 25
                TabOrder = 33
                Version = '1.0'
                Groups = <>
                Items = <>
              end
              object EditRevGruppe: TIWCGJQDropDown
                Left = 215
                Top = 48
                Width = 209
                Height = 25
                TabOrder = 34
                Version = '1.0'
                Groups = <>
                Items = <>
              end
            end
          end
        end
      end
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 686
    Width = 1177
    TabOrder = 16
  end
  object ds_alleBetriebe: TDataSource
    DataSet = DMStammdaten.QBetriebeSuche
    Left = 528
    Top = 32
  end
  object ds_einzelbetrieb: TDataSource
    DataSet = dm_main.qu_einzelBetrieb
    Left = 312
    Top = 16
  end
  object ds_Zulassung: TDataSource
    DataSet = dm_main.qu_Zulassungen
    Left = 400
    Top = 40
  end
  object ds_Betriebstypen: TDataSource
    DataSet = dm_main.qu_Betriebstypen
    Left = 216
    Top = 8
  end
  object ds_Zulnr: TDataSource
    DataSet = dm_main.qu_Zulassungsnummern
    Left = 360
  end
  object ds_ZulassungsArten: TDataSource
    DataSet = dm_main.qu_ZulassungsArten
    Left = 464
    Top = 65528
  end
  object ds_tat: TDataSource
    DataSet = dm_main.qu_Taetigkeiten
    Left = 1032
    Top = 8
  end
  object saBetriebsfen: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 960
    Top = 16
  end
  object jqdStammdat: TIWCGJQFileDownload
    Version = '1.0'
    Left = 884
    Top = 2
  end
  object ProviderKontrollen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_KontrollenVonBetrieb
    KeyFields = 'ID'
    Left = 600
    Top = 64
  end
  object frxBetriebsstammdaten: TfrxReport
    Version = '2023.2.1'
    DotMatrixReport = False
    IniFile = '\Software\Fast Reports'
    PreviewOptions.Buttons = [pbPrint, pbLoad, pbSave, pbExport, pbZoom, pbFind, pbOutline, pbPageSetup, pbTools, pbEdit, pbNavigator, pbExportQuick, pbCopy, pbSelection]
    PreviewOptions.Zoom = 1.000000000000000000
    PrintOptions.Printer = 'Default'
    PrintOptions.PrintOnSheet = 0
    PrintOptions.ShowDialog = False
    ReportOptions.CreateDate = 44113.417891030100000000
    ReportOptions.LastChange = 44113.546854062500000000
    ScriptLanguage = 'PascalScript'
    ScriptText.Strings = (
      'begin'
      ''
      'end.')
    Left = 1260
    Top = 66
    Datasets = <
      item
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
      end>
    Variables = <>
    Style = <>
    object Data: TfrxDataPage
      Height = 1000.000000000000000000
      Width = 1000.000000000000000000
    end
    object Page1: TfrxReportPage
      PaperWidth = 210.000000000000000000
      PaperHeight = 297.000000000000000000
      PaperSize = 9
      LeftMargin = 10.000000000000000000
      RightMargin = 10.000000000000000000
      TopMargin = 10.000000000000000000
      BottomMargin = 10.000000000000000000
      Frame.Typ = []
      MirrorMode = []
      object ReportTitle1: TfrxReportTitle
        FillType = ftBrush
        FillGap.Top = 0
        FillGap.Left = 0
        FillGap.Bottom = 0
        FillGap.Right = 0
        Frame.Typ = []
        Height = 49.133890000000000000
        Top = 18.897650000000000000
        Width = 718.110700000000000000
        object Memo1: TfrxMemoView
          AllowVectorExport = True
          Left = 226.771800000000000000
          Top = 11.338590000000000000
          Width = 230.551330000000000000
          Height = 30.236240000000000000
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -24
          Font.Name = 'Arial'
          Font.Style = []
          Frame.Typ = []
          Memo.UTF8W = (
            'Betriebsstammdaten')
          ParentFont = False
        end
      end
      object frxStammdatenStrasse: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 158.740260000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Frame.Typ = []
        Memo.UTF8W = (
          'Adresse: [frxStammdaten."Strasse"]')
      end
      object frxStammdatenOrt: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 192.756030000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Frame.Typ = []
        Memo.UTF8W = (
          'Ort/Plz: [frxStammdaten."Ort"]/[frxStammdaten."Plz"]')
      end
      object frxStammdatenEmail: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 226.771800000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Frame.Typ = []
        Memo.UTF8W = (
          'Email: [frxStammdaten."Email"]')
      end
      object frxStammdatenTelefon: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 260.787570000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Frame.Typ = []
        Memo.UTF8W = (
          'Telefon: [frxStammdaten."Telefon"]')
      end
      object frxStammdatenGemeindename: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 294.803340000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Frame.Typ = []
        Memo.UTF8W = (
          'Gemeindename: [frxStammdaten."Gemeindename"]')
      end
      object frxStammdatenName: TfrxMemoView
        IndexTag = 1
        AllowVectorExport = True
        Left = 3.779530000000000000
        Top = 124.724490000000000000
        Width = 400.630180000000000000
        Height = 18.897650000000000000
        DataSet = frxStammdaten
        DataSetName = 'frxStammdaten'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = 'Arial'
        Font.Style = []
        Frame.Typ = []
        Memo.UTF8W = (
          'Name: [frxStammdaten."Name"]')
        ParentFont = False
      end
    end
  end
  object frxStammdaten: TfrxDBDataset
    UserName = 'frxStammdaten'
    CloseDataSource = False
    DataSet = dm_main.qu_einzelBetrieb
    BCDToCurrency = False
    DataSetOptions = []
    Left = 1360
    Top = 64
  end
  object frxPDFExportBetriebsstammdaten: TfrxPDFExport
    ShowDialog = False
    UseFileCache = True
    ShowProgress = True
    OverwritePrompt = False
    DataOnly = False
    EmbedFontsIfProtected = False
    InteractiveFormsFontSubset = 'A-Z,a-z,0-9,#43-#47 '
    OpenAfterExport = True
    PrintOptimized = False
    Outline = False
    Background = False
    HTMLTags = True
    Quality = 95
    Transparency = False
    Author = 'Esculenta'
    Subject = 'FastReport PDF export'
    Creator = 'FastReport'
    ProtectionFlags = [ePrint, eModify, eCopy, eAnnot]
    HideToolbar = False
    HideMenubar = False
    HideWindowUI = True
    FitWindow = True
    CenterWindow = False
    PrintScaling = False
    PdfA = False
    PDFStandard = psNone
    PDFVersion = pv17
    Left = 1116
    Top = 66
  end
  object ProvBetriebe: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebeSuche
    DataSource = ds_alleBetriebe
    KeyFields = 'FullID'
    Left = 600
    Top = 8
  end
  object ProviderTierarten: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebTierartenTaetigkeiten
    KeyFields = 'ID'
    Left = 600
    Top = 141
  end
  object ProviderZulassungen: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebZulassungen
    Left = 736
    Top = 144
  end
end
