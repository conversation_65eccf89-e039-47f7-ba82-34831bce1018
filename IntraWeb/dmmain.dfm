object dm_main: Tdm_main
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  Height = 827
  Width = 2186
  object FBC_MAIN: TFDConnection
    Params.Strings = (
      'Database=ELKEDB_ENTW'
      'User_Name=sa'
      'Password=ESA1234!'
      'Server=10.10.0.67'
      'DriverID=MSSQL')
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvAutoReconnect]
    ResourceOptions.AutoReconnect = True
    ConnectedStoredUsage = []
    LoginPrompt = False
    Transaction = TransactionMain
    BeforeConnect = FBC_MAINBeforeConnect
    Left = 32
    Top = 8
  end
  object TransactionMain: TFDTransaction
    Connection = FBC_MAIN
    Left = 152
    Top = 8
  end
  object qu_user: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    UpdateOptions.AssignedValues = [uvUpdateChngFields, uvUpdateMode, uvLockMode, uvRefreshMode, uvGeneratorName, uvCheckRequired, uvCheckReadOnly, uvCheckUpdatable]
    UpdateOptions.UpdateChangedFields = False
    UpdateOptions.RefreshMode = rmManual
    UpdateOptions.GeneratorName = 'STALLBUCH_ID_GEN'
    UpdateOptions.CheckRequired = False
    UpdateOptions.CheckReadOnly = False
    UpdateOptions.CheckUpdatable = False
    UpdateOptions.KeyFields = 'ID'
    SQL.Strings = (
      'select * '
      'from SYSTEMSTAMMDATEN.[USER] ')
    Left = 272
    Top = 16
  end
  object qu_checkland: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    UpdateOptions.AssignedValues = [uvUpdateChngFields, uvUpdateMode, uvLockMode, uvRefreshMode, uvGeneratorName, uvCheckRequired, uvCheckReadOnly, uvCheckUpdatable]
    UpdateOptions.UpdateChangedFields = False
    UpdateOptions.RefreshMode = rmManual
    UpdateOptions.GeneratorName = 'STALLBUCH_ID_GEN'
    UpdateOptions.CheckRequired = False
    UpdateOptions.CheckReadOnly = False
    UpdateOptions.CheckUpdatable = False
    UpdateOptions.KeyFields = 'ID'
    SQL.Strings = (
      'select * from esa$user ')
    Left = 448
    Top = 16
  end
  object qu_funktionen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    UpdateOptions.AssignedValues = [uvUpdateChngFields, uvUpdateMode, uvLockMode, uvRefreshMode, uvGeneratorName, uvCheckRequired, uvCheckReadOnly, uvCheckUpdatable]
    UpdateOptions.UpdateChangedFields = False
    UpdateOptions.RefreshMode = rmManual
    UpdateOptions.GeneratorName = 'STALLBUCH_ID_GEN'
    UpdateOptions.CheckRequired = False
    UpdateOptions.CheckReadOnly = False
    UpdateOptions.CheckUpdatable = False
    UpdateOptions.KeyFields = 'ID'
    SQL.Strings = (
      'SELECT * '
      'FROM SYSTEMSTAMMDATEN.FUNKTIONEN;')
    Left = 536
    Top = 16
  end
  object qu_VisBStammdaten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_Betriebsstammdaten')
    Left = 136
    Top = 208
  end
  object qu_VisZulassungen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evMode, evRowsetSize]
    FetchOptions.Mode = fmAll
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_Zulassungen')
    Left = 32
    Top = 208
  end
  object qu_VisBetriebstypen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_Betriebstypen')
    Left = 32
    Top = 264
  end
  object qu_VisDom: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'SELECT *'
      'From VISDATEN.VIS_DOMWERT')
    Left = 136
    Top = 264
  end
  object qu_VisListencodes: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_Zulassungslistencodes')
    Left = 136
    Top = 320
  end
  object qu_VISTierarten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.vis_tierarten')
    Left = 32
    Top = 320
  end
  object qu_VISZulassungstypen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.Vis_Zulassungstypen')
    Left = 32
    Top = 384
  end
  object qu_VISSchluesseltypen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_schluesseltypen')
    Left = 136
    Top = 384
  end
  object qu_VISprbkat: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from VISDATEN.VIS_prbkat')
    Left = 32
    Top = 440
  end
  object qu_VISZulassungsNr: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'SELECT *'
      'From VISDATEN.VIS_Zulassungsnummern')
    Left = 136
    Top = 440
  end
  object qu_StDat_Tierarten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select *'
      'from stammdaten.tierarten')
    Left = 32
    Top = 616
  end
  object qu_StDat_TAkat: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select k.vis_kateg, t.KZ, t.bezeichnung, t.vis_tierart'
      
        'from stammdaten.tierarten_kateg k left join stammdaten.tierarten' +
        ' t '
      'on k.id_tierart = t.id')
    Left = 136
    Top = 560
  end
  object quBetriebssuche: TFDQuery
    ActiveStoredUsage = []
    OnCalcFields = quBetriebssucheCalcFields
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 5
    SQL.Strings = (
      'select '
      '  case    '
      '    when M.WERT is null then 0'
      '    else 1'
      '  end as Flag,'
      ''
      '  M.WERT as MarkierungWert,'
      '  M.Beschreibung as MarkierungBeschreibung,'
      ''
      '  z.ZULNR, '
      '  b.*'
      'from STAMMDATEN.BETRIEBE b'
      '         left join STAMMDATEN.ADRESSEN a on b.ID_ADRESSE = a.ID'
      
        '         left join STAMMDATEN.GEMEINDEN g on a.ID_GEMEINDE = g.I' +
        'D'
      '         left join STAMMDATEN.ZULASSUNGEN z on b.REGNR = z.REGNR'
      
        '         left join SYSTEMSTAMMDATEN.MARKIERUNGEN M on b.ID_MARKI' +
        'ERUNG = M.ID'
      'where b.BLDCODE = :bldcode'
      '  and b.NAME like '#39'%'#39' + :name + '#39'%'#39
      '  and b.REGNR like '#39'%'#39' + :regnr + '#39'%'#39
      '  and a.STRASSE like '#39'%'#39' + :strasse + '#39'%'#39
      '  and a.PLZ like '#39'%'#39' + :plz + '#39'%'#39
      '  and z.BEGINNDATUM <= getdate()'
      '  and z.ENDDATUM >= getdate()')
    Left = 288
    Top = 208
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'STRASSE'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
    object quBetriebssucheZulnr: TStringField
      FieldName = 'Zulnr'
      Origin = 'Zulnr'
      Size = 9
    end
    object quBetriebssucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quBetriebssucheREGNR: TStringField
      FieldName = 'REGNR'
      Origin = 'REGNR'
      FixedChar = True
      Size = 7
    end
    object quBetriebssucheNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Required = True
      Size = 255
    end
    object quBetriebssucheID_ADRESSE: TIntegerField
      FieldName = 'ID_ADRESSE'
      Origin = 'ID_ADRESSE'
      Required = True
    end
    object quBetriebssucheSTRASSE: TStringField
      FieldName = 'STRASSE'
      Origin = 'STRASSE'
      Size = 150
    end
    object quBetriebssuchePLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Size = 7
    end
    object quBetriebssucheORT: TStringField
      FieldName = 'ORT'
      Origin = 'ORT'
      Size = 50
    end
    object quBetriebssucheGEMNR: TStringField
      FieldName = 'GEMNR'
      Origin = 'GEMNR'
      Size = 5
    end
    object quBetriebssucheKATASTRALGEMNAME: TStringField
      FieldName = 'KATASTRALGEMNAME'
      Origin = 'KATASTRALGEMNAME'
      Size = 50
    end
    object quBetriebssucheAUFSICHTSORGAN: TStringField
      FieldName = 'AUFSICHTSORGAN'
      Origin = 'AUFSICHTSORGAN'
      FixedChar = True
      Size = 2
    end
    object quBetriebssucheTELEFON: TStringField
      FieldName = 'TELEFON'
      Origin = 'TELEFON'
      Size = 50
    end
    object quBetriebssucheEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 50
    end
    object quBetriebssucheVERGEBUEHRUNG: TStringField
      FieldName = 'VERGEBUEHRUNG'
      Origin = 'VERGEBUEHRUNG'
      FixedChar = True
      Size = 2
    end
    object quBetriebssucheBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object quBetriebssucheVULGO: TStringField
      FieldName = 'VULGO'
      Origin = 'VULGO'
      Size = 30
    end
    object quBetriebssucheANMERKUNG: TWideStringField
      FieldName = 'ANMERKUNG'
      Origin = 'ANMERKUNG'
      Size = 500
    end
    object quBetriebssucheBVBKZ: TStringField
      FieldName = 'BVBKZ'
      Origin = 'BVBKZ'
      Size = 3
    end
    object quBetriebssucheBBKNR: TStringField
      FieldName = 'BBKNR'
      Origin = 'BBKNR'
      Size = 15
    end
    object quBetriebssucheMarkierungWert: TWideStringField
      FieldName = 'MarkierungWert'
      Origin = 'MarkierungWert'
      Size = 250
    end
    object quBetriebssucheID_MARKIERUNG: TGuidField
      FieldName = 'ID_MARKIERUNG'
      Origin = 'ID_MARKIERUNG'
      Size = 38
    end
    object quBetriebssucheFlagHTML: TStringField
      FieldKind = fkCalculated
      FieldName = 'FlagHTML'
      Size = 255
      Calculated = True
    end
    object quBetriebssucheMarkierungBeschreibung: TWideStringField
      FieldName = 'MarkierungBeschreibung'
      Origin = 'MarkierungBeschreibung'
      Size = 250
    end
    object quBetriebssucheFlag: TIntegerField
      FieldName = 'Flag'
      Origin = 'Flag'
      ReadOnly = True
      Required = True
    end
  end
  object qu_einzelBetrieb: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'SELECT b.Name, b.Email, b.Telefon, b.Anmerkung, a.Strasse, a.Plz' +
        ', a.Ort, g.Gemeindename'
      'FROM   Stammdaten.Betriebe b'
      '         LEFT JOIN Stammdaten.Adressen a ON b.ID_Adresse = a.ID'
      
        '         LEFT JOIN Stammdaten.Gemeinden g on a.ID_Gemeinde = g.I' +
        'D'
      'WHERE  b.Id = :id;')
    Left = 384
    Top = 208
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object qu_einzelBetriebName: TStringField
      FieldName = 'Name'
      Origin = 'Name'
      Required = True
      Size = 255
    end
    object qu_einzelBetriebEmail: TStringField
      FieldName = 'Email'
      Origin = 'Email'
      Size = 50
    end
    object qu_einzelBetriebTelefon: TStringField
      FieldName = 'Telefon'
      Origin = 'Telefon'
      Size = 50
    end
    object qu_einzelBetriebAnmerkung: TWideStringField
      FieldName = 'Anmerkung'
      Origin = 'Anmerkung'
      Size = 500
    end
    object qu_einzelBetriebStrasse: TWideStringField
      FieldName = 'Strasse'
      Origin = 'Strasse'
      Size = 150
    end
    object qu_einzelBetriebPlz: TStringField
      FieldName = 'Plz'
      Origin = 'Plz'
      Size = 7
    end
    object qu_einzelBetriebOrt: TWideStringField
      FieldName = 'Ort'
      Origin = 'Ort'
      Size = 150
    end
    object qu_einzelBetriebGemeindename: TStringField
      FieldName = 'Gemeindename'
      Origin = 'Gemeindename'
      Size = 80
    end
  end
  object qu_Zulassungen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select distinct v.BEZEICHNUNG'
      'from VISDATEN.VIS_ZULASSUNGEN z'
      
        '         left join VISDATEN.VIS_ZULASSUNGSTYPEN v on z.ZULASSUNG' +
        ' = v.ID'
      'where z.REGNR = :regnr')
    Left = 384
    Top = 264
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Betriebstypen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'select t.bezeichnung as "Betriebstypen", z.betriebstyp as "btrty' +
        'p"'
      
        'from Stammdaten.betriebe b left join visdaten.vis_zulassungen z ' +
        'on b.regnr = z.regnr'
      'left join visdaten.vis_betriebstypen t on z.betriebstyp = t.id '
      'where b.regnr = :regnr'
      'order by t.bezeichnung')
    Left = 296
    Top = 264
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Zulassungsnummern: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select zulnr'
      'from stammdaten.zulassungen'
      'where regnr= :regnr'
      'group by zulnr'
      '')
    Left = 288
    Top = 320
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftOraInterval
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_FilterFragen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 0
    SQL.Strings = (
      'Select g.bezeichnung, s.fragennr, s.frage'
      
        'From stammdaten.fragenstamm s left join stammdaten.fragengruppen' +
        ' g on s.id_gruppe = g.id')
    Left = 408
    Top = 616
  end
  object qu_ZulassungsArten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select t.bezeichnung as "Zulassungstypen"'
      
        'from visdaten.VIS_Zulassungen z left join visdaten.VIS_Zulassung' +
        'stypen t'
      'on z.zulassung=t.id'
      'where z.regnr = :regnr'
      'group by t.bezeichnung'
      '')
    Left = 400
    Top = 320
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Taetigkeiten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select t.BEZEICHNUNG as bezeichnung'
      'from VISDATEN.VIS_ZULASSUNGEN v'
      
        '         left join STAMMDATEN.TAETIG_BETRTYP b on b.BETRIEBSTYP ' +
        '= v.BETRIEBSTYP'
      
        '         left join STAMMDATEN.TAETIGKEITEN t on t.ID = b.ID_TAET' +
        'IGKEIT'
      'where v.REGNR = :regnr'
      'and not t.BEZEICHNUNG is null'
      'group by t.BEZEICHNUNG')
    Left = 288
    Top = 384
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftWideString
        ParamType = ptInput
        Value = ''
      end>
    object qu_Taetigkeitenbezeichnung: TStringField
      FieldName = 'bezeichnung'
      Origin = 'bezeichnung'
      Size = 50
    end
  end
  object qu_kontrollfragen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'From stammdaten.fragengruppen')
    Left = 904
    Top = 464
  end
  object qu_unterfragen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'From stammdaten.fragenstamm'
      'where id_gruppe = :idgrp')
    Left = 928
    Top = 560
    ParamData = <
      item
        Name = 'IDGRP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_StDat_TaetBetrtyp: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select t.bezeichnung, betriebstyp'
      
        'from stammdaten.taetig_betrtyp b left join stammdaten.taetigkeit' +
        'en t on b.id_taetigkeit = t.id')
    Left = 240
    Top = 552
  end
  object qu_StDat_BetrtypTA: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'From stammdaten.Betriebstypen_tierart')
    Left = 240
    Top = 616
  end
  object qu_stdat_revstamm_ins: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.Revisionsstamm (Bldcode, Sektion, Bet' +
        'riebsgruppe_Lm, Betriebsgruppe_Detail, Betriebsart)'
      
        'VALUES      (:bldcode, :sektion, :betriebsgruppe_lm, :betriebsgr' +
        'uppe_detail, :betriebsart);')
    Left = 1544
    Top = 160
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SEKTION'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSGRUPPE_LM'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSGRUPPE_DETAIL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Fragengruppen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select f.id, f.bezeichnung, h.bezeichnung'
      
        'from stammdaten.fragengruppen f left join stammdaten.fragenhaupt' +
        'gruppen h on f.id_hauptgruppe = h.id')
    Left = 400
    Top = 680
  end
  object qu_Fragenhauptgruppen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 0
    SQL.Strings = (
      'Select *'
      'from stammdaten.fragenhauptgruppen')
    Left = 608
    Top = 672
  end
  object qu_Checklistenpunkte: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 200
    SQL.Strings = (
      'Select c.bezeichnung, p.bezeichung, p.langtext'
      
        'from stammdaten.checklistenpunkt p left join stammdaten.checklis' +
        'ten c on p.id_checkliste = c.id')
    Left = 496
    Top = 672
  end
  object qu_Checklisten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 0
    SQL.Strings = (
      'Select * '
      'from stammdaten.checklisten')
    Left = 488
    Top = 616
  end
  object qu_Checkp_Frage: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    FetchOptions.AssignedValues = [evRowsetSize]
    FetchOptions.RowsetSize = 0
    SQL.Strings = (
      'Select c.bezeichung,  f.fragennr, f.frage'
      
        'from stammdaten.checkp_frage p left join stammdaten.fragenstamm ' +
        'f on p.id_fragenstamm=f.id'
      
        'left join stammdaten.checklistenpunkt c on p.id_checklistenpunkt' +
        '=c.id')
    Left = 592
    Top = 616
  end
  object qu_filterCheckpunkte: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 400
    Top = 736
  end
  object qu_Termine: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM bewegungsdaten.TERMINE'
      'where PSKEY = :personkey'
      'order by id')
    Left = 856
    Top = 40
    ParamData = <
      item
        Name = 'PERSONKEY'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Ins_Termin: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 928
    Top = 40
  end
  object qu_Update_Termin: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 1008
    Top = 40
  end
  object qu_ZulassungenBetr: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'select v.bezeichnung as "Zulassungsname",  z.zulassung as "Zulas' +
        'sung"'
      
        'from Stammdaten.betriebe b left join visdaten.vis_zulassungen z ' +
        'on b.regnr = z.regnr'
      'left join visdaten.vis_zulassungstypen v on z.zulassung = v.id'
      'where b.regnr = :regnr'
      'group by v.bezeichnung, z.zulassung')
    Left = 392
    Top = 384
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object qu_HTTP_Header: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 32
    Top = 776
  end
  object qu_TaetigGrp: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select g.BEZEICHNUNG as Bezeichnung'
      'from VISDATEN.VIS_ZULASSUNGEN z'
      
        '         left join STAMMDATEN.TAETIGGRP_BETRTYP t on t.BETRIEBST' +
        'YP = z.BETRIEBSTYP'
      
        '         left join STAMMDATEN.TAETIGKEITSGRUPPEN g on g.ID = t.I' +
        'D_TAETIGKEITSGRP'
      'where z.REGNR = :regnr'
      '  and not g.BEZEICHNUNG is null'
      'group by g.BEZEICHNUNG')
    Left = 392
    Top = 440
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftWideString
        ParamType = ptInput
        Value = ''
      end>
    object qu_TaetigGrpBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Size = 250
    end
  end
  object qu_tierarten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select t.TIERART as Tierart'
      'from VISDATEN.VIS_ZULASSUNGEN z'
      
        '         left join STAMMDATEN.BETRIEBSTYPEN_TIERART b on b.BETRI' +
        'EBSTYP = z.BETRIEBSTYP'
      
        '         left join VISDATEN.VIS_TIERARTEN t on t.WERT = b.TIERAR' +
        'T'
      'where z.REGNR = :regnr'
      '  and b.TIERART <> '#39'XX'#39
      '  and not t.TIERART is null'
      'group by t.TIERART')
    Left = 288
    Top = 448
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
    object qu_tierartenTierart: TStringField
      FieldName = 'Tierart'
      Origin = 'Tierart'
      Size = 50
    end
  end
  object qu_LetzterLogin: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select username, pskey'
      'from systemstammdaten.[user]'
      'where userguid=:guid')
    Left = 688
    Top = 16
    ParamData = <
      item
        Name = 'GUID'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Rolle: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT Bezeichnung'
      'FROM   Systemstammdaten.Rollen'
      'WHERE  id= :id;')
    Left = 688
    Top = 72
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object ds_Betriebe: TDataSource
    DataSet = qu_stdat_revstamm_ins
    Left = 824
    Top = 416
  end
  object qu_btrtypenTA: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from stammdaten.betriebstypen_tierart'
      'where tierart = :ta')
    Left = 392
    Top = 504
    ParamData = <
      item
        Name = 'TA'
        DataType = ftString
        ParamType = ptInput
        Value = 'SA'
      end>
  end
  object qu_btrtypenTat: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select a.bezeichnung'
      
        'from stammdaten.betriebstypen_tierart b left join stammdaten.tae' +
        'tig_betrtyp t on b.betriebstyp = t.betriebstyp'
      'left join stammdaten.taetigkeiten a on a.id = t.id_taetigkeit'
      'where b.tierart = :ta'
      'and b.betriebstyp in '
      'group by bezeichnung')
    Left = 296
    Top = 504
    ParamData = <
      item
        Name = 'TA'
        DataType = ftString
        ParamType = ptInput
        Value = 'SW'
      end>
  end
  object qu_UpdateAnmerkung: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Update Stammdaten.betriebe'
      'set Anmerkung = :anmerk'
      'where regnr = :regnr')
    Left = 392
    Top = 552
    ParamData = <
      item
        Name = 'ANMERK'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Ins_TodoListe: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'Insert into bewegungsdaten.todo (PSKEY, Titel, Faellig, ID_Grupp' +
        'e)'
      'values('#39'ES00001'#39', :titel,:faellig, 1)')
    Left = 144
    Top = 88
    ParamData = <
      item
        Name = 'TITEL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FAELLIG'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_TodoListe: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from BEWEGUNGSDATEN.TODO')
    Left = 144
    Top = 144
  end
  object qu_Bundesland: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Systemstammdaten.bundesland'
      'where bldcode=300')
    Left = 752
    Top = 536
  end
  object ds_Bundesland: TDataSource
    DataSet = qu_Bundesland
    Left = 752
    Top = 592
  end
  object qu_StDat_RevisionsStamm: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   BEWEGUNGSDATEN.REVISIONSSTAMM'
      'WHERE  Bldcode = :bldcode'
      'order by sektion, BETRIEBSGRUPPE_LM, BETRIEBSGRUPPE_DETAIL')
    Left = 1544
    Top = 64
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Kontr_bkbtypen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Stammdaten.bkbtypen')
    Left = 1232
    Top = 56
  end
  object qu_kontr_betrieb: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select REGNR, NAME, STRASSE'
      'from Stammdaten.betriebe')
    Left = 1232
    Top = 120
  end
  object qu_kontr_benutzer: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Systemstammdaten.user')
    Left = 1232
    Top = 176
  end
  object qu_kontr_bkbUnterart: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Stammdaten.kontrolltypen'
      
        'where BKBTyp = (Select BKBTyp FROM Stammdaten.BKBTypen where bez' +
        'eichnung = :bezeichnung)')
    Left = 1232
    Top = 232
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object qu_kontr_refbkb: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select refbkb'
      'from Stammdaten.kontrolltypen'
      'where bezeichnung = :bezeichnung'
      
        'and bkbtyp=(Select bkbtyp from Stammdaten.bkbtypen where bezeich' +
        'nung = :bkbtype)')
    Left = 1232
    Top = 288
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYPE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_kontr_Kontrollorgan: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select username'
      'from Systemstammdaten.[user]')
    Left = 1336
    Top = 56
  end
  object qu_kontr_bkbtypKZ: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select bkbtyp'
      'from Stammdaten.bkbtypen'
      'where bezeichnung = :bez')
    Left = 1328
    Top = 120
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_sendNachricht: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'Insert Into Bewegungsdaten.Nachrichten(ID_Gruppe, Status, Typ, A' +
        'bsender_KZ, Priori, ID_absender, Text, Link)'
      
        'Values (:idgruppe, :Status, :Typ, :absenderkz, :priori, :absende' +
        'rid, :text, :link);')
    Left = 912
    Top = 624
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STATUS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ABSENDERKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PRIORI'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ABSENDERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LINK'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_kontr_Berichte: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Bewegungsdaten.Kontrollbericht'
      'order by datum desc')
    Left = 1320
    Top = 184
  end
  object qu_autofillBetriebsname: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Stammdaten.Betriebe')
    Left = 1232
    Top = 360
  end
  object qu_KontrJahre: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select year(datum) as "jahr"'
      'from bewegungsdaten.kontrollbericht'
      'where regnr_ort=:regnr'
      'group by year(datum)')
    Left = 456
    Top = 208
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Kontrolljahre: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT DISTINCT YEAR(datum) as "jahr"'
      'FROM   bewegungsdaten.kontrollbericht'
      'ORDER BY YEAR(datum) desc;')
    Left = 1328
    Top = 248
  end
  object qu_kontrollListe: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from bewegungsdaten.kontrollbericht')
    Left = 1328
    Top = 304
  end
  object qu_gruppen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM systemstammdaten.[gruppen]'
      'WHERE UPPER(bezeichnung) LIKE '#39'%'#39' + :BEZ + '#39'%'#39';')
    Left = 1208
    Top = 536
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object qu_user_suche: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT   *'
      'FROM     systemstammdaten.[user]'
      'WHERE    UPPER(username) LIKE '#39'%'#39' + :username + '#39'%'#39
      '          AND BLDCODE = :bldcode'
      'ORDER BY username;')
    Left = 1280
    Top = 536
    ParamData = <
      item
        Name = 'USERNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
    object qu_user_sucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object qu_user_sucheID_PERSON: TIntegerField
      FieldName = 'ID_PERSON'
      Origin = 'ID_PERSON'
    end
    object qu_user_sucheUSERNAME: TStringField
      FieldName = 'USERNAME'
      Origin = 'USERNAME'
      Size = 40
    end
    object qu_user_suchePASSWORT: TStringField
      FieldName = 'PASSWORT'
      Origin = 'PASSWORT'
      Size = 40
    end
    object qu_user_sucheUSERGUID: TStringField
      FieldName = 'USERGUID'
      Origin = 'USERGUID'
      Size = 40
    end
    object qu_user_sucheUSERTYPE: TSmallintField
      FieldName = 'USERTYPE'
      Origin = 'USERTYPE'
    end
    object qu_user_sucheGESPERRT: TSmallintField
      FieldName = 'GESPERRT'
      Origin = 'GESPERRT'
    end
    object qu_user_sucheBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object qu_user_sucheUSERPWC: TSmallintField
      FieldName = 'USERPWC'
      Origin = 'USERPWC'
    end
    object qu_user_sucheAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object qu_user_sucheTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object qu_user_sucheINS_DBUSER: TStringField
      FieldName = 'INS_DBUSER'
      Origin = 'INS_DBUSER'
      Size = 30
    end
    object qu_user_sucheLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object qu_user_sucheCHANGE_DBUSER: TStringField
      FieldName = 'CHANGE_DBUSER'
      Origin = 'CHANGE_DBUSER'
      Size = 30
    end
    object qu_user_sucheEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 100
    end
    object qu_user_sucheLAST_LOGIN: TSQLTimeStampField
      FieldName = 'LAST_LOGIN'
      Origin = 'LAST_LOGIN'
    end
  end
  object qu_gruppe_speichern: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'INSERT INTO systemstammdaten.gruppen '
      '            (bezeichnung, bldcode, muttergruppe, '
      '             id_user_hauptver, id_user_stellver, okz)'
      'VALUES      (:bezeichnung, :bldcode, :muttergruppe,'
      '             :id_user_hauptver, :id_user_stellver, :okz);')
    Left = 1208
    Top = 584
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Size = 50
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MUTTERGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_HAUPTVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_STELLVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Size = 100
        Value = Null
      end>
  end
  object qu_gruppe_loesche: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'DELETE FROM systemstammdaten.gruppen'
      'WHERE       ID = :id;')
    Left = 1208
    Top = 632
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_gruppe_aendern: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE systemstammdaten.gruppen'
      
        'SET    BEZEICHNUNG = :bezeichnung, BLDCODE = :bldcode, MUTTERGRU' +
        'PPE = :muttergruppe,'
      
        '       ID_USER_HAUPTVER = :id_user_hauptver, ID_USER_STELLVER = ' +
        ':id_user_stellver, OKZ = :okz'
      'WHERE  ID = :id;')
    Left = 1208
    Top = 680
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MUTTERGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_HAUPTVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_STELLVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_usergruppen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      
        'FROM   Systemstammdaten.Usergruppen AS ug INNER JOIN Systemstamm' +
        'daten.[User] AS u'
      
        '         ON (ug.ID_USER = u.ID) INNER JOIN Systemstammdaten.Grup' +
        'pen AS g '
      '         ON (ug.ID_GRUPPE = g.ID)'
      'WHERE  u.BLDCODE = :bldcode '
      '         AND UPPER(Username) LIKE '#39'%'#39' + :username + '#39'%'#39
      '         AND UPPER(g.Bezeichnung) LIKE '#39'%'#39' + :bezeichnung + '#39'%'#39';')
    Left = 1208
    Top = 728
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USERNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object quAutocompleteUsername: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.[User]'
      'WHERE  Username LIKE '#39'%'#39' + :username + '#39'%'#39
      '        AND Bldcode = :bldcode;')
    Left = 928
    Top = 400
    ParamData = <
      item
        Name = 'USERNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_KontrollenVonBetrieb: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select *'
      'from BEWEGUNGSDATEN.KONTROLLBERICHT'
      'where ID_BETRIEB = :IDBetrieb'
      'order by ENDEZEIT desc')
    Left = 1232
    Top = 416
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object qu_KontrollenVonBetriebID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object qu_KontrollenVonBetriebGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object qu_KontrollenVonBetriebBKB: TStringField
      FieldName = 'BKB'
      Origin = 'BKB'
      Required = True
      Size = 26
    end
    object qu_KontrollenVonBetriebBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Required = True
      Size = 10
    end
    object qu_KontrollenVonBetriebKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      FixedChar = True
      Size = 3
    end
    object qu_KontrollenVonBetriebDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
    end
    object qu_KontrollenVonBetriebERFASSER_PSKEY: TStringField
      FieldName = 'ERFASSER_PSKEY'
      Origin = 'ERFASSER_PSKEY'
      FixedChar = True
      Size = 7
    end
    object qu_KontrollenVonBetriebKONTROLLORGAN_PSKEY: TStringField
      FieldName = 'KONTROLLORGAN_PSKEY'
      Origin = 'KONTROLLORGAN_PSKEY'
      FixedChar = True
      Size = 7
    end
    object qu_KontrollenVonBetriebID_PERSON_ERFASSER: TIntegerField
      FieldName = 'ID_PERSON_ERFASSER'
      Origin = 'ID_PERSON_ERFASSER'
    end
    object qu_KontrollenVonBetriebID_PERSON_KONTROLLORGAN: TIntegerField
      FieldName = 'ID_PERSON_KONTROLLORGAN'
      Origin = 'ID_PERSON_KONTROLLORGAN'
    end
    object qu_KontrollenVonBetriebREF_BKB: TStringField
      FieldName = 'REF_BKB'
      Origin = 'REF_BKB'
      Size = 26
    end
    object qu_KontrollenVonBetriebPROBENZIEHUNG: TBooleanField
      FieldName = 'PROBENZIEHUNG'
      Origin = 'PROBENZIEHUNG'
      Required = True
    end
    object qu_KontrollenVonBetriebID_BETRIEB: TIntegerField
      FieldName = 'ID_BETRIEB'
      Origin = 'ID_BETRIEB'
      Required = True
    end
    object qu_KontrollenVonBetriebREGNR_ORT: TStringField
      FieldName = 'REGNR_ORT'
      Origin = 'REGNR_ORT'
      FixedChar = True
      Size = 7
    end
    object qu_KontrollenVonBetriebSTARTZEIT: TSQLTimeStampField
      FieldName = 'STARTZEIT'
      Origin = 'STARTZEIT'
    end
    object qu_KontrollenVonBetriebENDEZEIT: TSQLTimeStampField
      FieldName = 'ENDEZEIT'
      Origin = 'ENDEZEIT'
    end
    object qu_KontrollenVonBetriebBESTAETIGT_UM: TSQLTimeStampField
      FieldName = 'BESTAETIGT_UM'
      Origin = 'BESTAETIGT_UM'
    end
    object qu_KontrollenVonBetriebID_RECHTSGRUNDLAGE: TIntegerField
      FieldName = 'ID_RECHTSGRUNDLAGE'
      Origin = 'ID_RECHTSGRUNDLAGE'
      Required = True
    end
    object qu_KontrollenVonBetriebSTATUS: TStringField
      FieldName = 'STATUS'
      Origin = 'STATUS'
      FixedChar = True
      Size = 1
    end
    object qu_KontrollenVonBetriebANGEMELDET_UM: TSQLTimeStampField
      FieldName = 'ANGEMELDET_UM'
      Origin = 'ANGEMELDET_UM'
    end
    object qu_KontrollenVonBetriebTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object qu_KontrollenVonBetriebLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object qu_KontrollenVonBetriebBETRIEBSTYP: TStringField
      FieldName = 'BETRIEBSTYP'
      Origin = 'BETRIEBSTYP'
      Required = True
      Size = 2
    end
    object qu_KontrollenVonBetriebGUID_UNTERSCHRIFT_ANWESENDER_BETRIEB: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Origin = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Size = 38
    end
    object qu_KontrollenVonBetriebGUID_UNTERSCHRIFT_KONTROLLORGAN: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Origin = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Size = 38
    end
    object qu_KontrollenVonBetriebVERWEIGERUNGSGRUND_UNTERSCHRIFT: TStringField
      FieldName = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Origin = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Size = 255
    end
    object qu_KontrollenVonBetriebVerweigerunggrund: TStringField
      FieldName = 'Verweigerunggrund'
      Origin = 'Verweigerunggrund'
      Size = 150
    end
    object qu_KontrollenVonBetriebGUID_DOKUMENT: TGuidField
      FieldName = 'GUID_DOKUMENT'
      Origin = 'GUID_DOKUMENT'
      Size = 38
    end
    object qu_KontrollenVonBetriebFEHLERHAFT_GESETZT_AM: TSQLTimeStampField
      FieldName = 'FEHLERHAFT_GESETZT_AM'
      Origin = 'FEHLERHAFT_GESETZT_AM'
    end
    object qu_KontrollenVonBetriebSTORNIERT_AM: TSQLTimeStampField
      FieldName = 'STORNIERT_AM'
      Origin = 'STORNIERT_AM'
    end
    object qu_KontrollenVonBetriebSTORNOGRUND: TStringField
      FieldName = 'STORNOGRUND'
      Origin = 'STORNOGRUND'
      Size = 255
    end
    object qu_KontrollenVonBetriebVERWEIGERT_AM: TSQLTimeStampField
      FieldName = 'VERWEIGERT_AM'
      Origin = 'VERWEIGERT_AM'
    end
    object qu_KontrollenVonBetriebGUID_DOKUMENT_CC: TGuidField
      FieldName = 'GUID_DOKUMENT_CC'
      Origin = 'GUID_DOKUMENT_CC'
      Size = 38
    end
    object qu_KontrollenVonBetriebVISEXPORT_AM: TSQLTimeStampField
      FieldName = 'VISEXPORT_AM'
      Origin = 'VISEXPORT_AM'
    end
    object qu_KontrollenVonBetriebKONTROLL_INFORMATIONEN: TStringField
      FieldName = 'KONTROLL_INFORMATIONEN'
      Origin = 'KONTROLL_INFORMATIONEN'
      Size = 4000
    end
    object qu_KontrollenVonBetriebID_GRUPPE_QUELLE: TIntegerField
      FieldName = 'ID_GRUPPE_QUELLE'
      Origin = 'ID_GRUPPE_QUELLE'
    end
    object qu_KontrollenVonBetriebKURZBEMERKUNG: TWideMemoField
      FieldName = 'KURZBEMERKUNG'
      Origin = 'KURZBEMERKUNG'
      BlobType = ftWideMemo
      Size = 2147483647
    end
    object qu_KontrollenVonBetriebID_REVISIONSPLAN: TIntegerField
      FieldName = 'ID_REVISIONSPLAN'
      Origin = 'ID_REVISIONSPLAN'
    end
  end
  object qu_logs: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   [Logging].[Log]'
      'WHERE  [Application] IN (:application)'
      '        AND Log_Level IN (:loglevel)'
      '        AND Timestamp BETWEEN :startdatum AND :enddatum;')
    Left = 1384
    Top = 544
    ParamData = <
      item
        Name = 'APPLICATION'
        DataType = ftDataSet
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LOGLEVEL'
        DataType = ftDataSet
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STARTDATUM'
        DataType = ftDateTime
        ParamType = ptInput
        Value = 36526d
      end
      item
        Name = 'ENDDATUM'
        DataType = ftDateTime
        ParamType = ptInput
        Value = 401768d
      end>
  end
  object qu_logdetail: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Logging.Log l '
      '        LEFT JOIN Logging.Messages m         ON l.id = m.Log_Id'
      '        LEFT JOIN Logging.Requests r         ON l.id = r.Log_Id '
      'WHERE  l.Id = :id;')
    Left = 1384
    Top = 640
    ParamData = <
      item
        Name = 'ID'
        DataType = ftString
        ParamType = ptInput
        Value = '{58688BD8-C346-4976-AED8-39F7D27383BA}'
      end>
  end
  object qu_requestheaders: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Logging.Request_Headers'
      'WHERE  Headers_Log_Item_Request_Log_Id = :id;')
    Left = 1384
    Top = 592
    ParamData = <
      item
        Name = 'ID'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object qu_stdat_revisionsstamm_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Bewegungsdaten.Revisionsstamm'
      'WHERE       ID = :id;')
    Left = 1544
    Top = 112
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_stdat_revstamm_update: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Revisionsstamm'
      
        'SET    Sektion = :sektion, Betriebsgruppe_Lm = :betriebsgruppe_l' +
        'm, '
      
        '        Betriebsgruppe_Detail = :betriebsgruppe_detail, Betriebs' +
        'art = :betriebsart'
      'WHERE  Id = :id;')
    Left = 1544
    Top = 208
    ParamData = <
      item
        Name = 'SEKTION'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSGRUPPE_LM'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSGRUPPE_DETAIL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_Ins_Stammdaten: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 40
    Top = 680
  end
  object qu_stdat_revplan: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select *'
      'from BEWEGUNGSDATEN.REVISIONSPLAN rp'
      
        '         inner join BEWEGUNGSDATEN.REVISIONSSTAMM r on rp.ID_REV' +
        'ISIONSSTAMM = r.ID'
      'where JAHR = :jahr'
      '  and r.BLDCODE = :BLDCODE')
    Left = 1544
    Top = 256
    ParamData = <
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 2020
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object qu_stdat_revplanID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object qu_stdat_revplanID_REVISIONSSTAMM: TIntegerField
      FieldName = 'ID_REVISIONSSTAMM'
      Origin = 'ID_REVISIONSSTAMM'
      Required = True
    end
    object qu_stdat_revplanJAHR: TSmallintField
      FieldName = 'JAHR'
      Origin = 'JAHR'
    end
    object qu_stdat_revplanRISIKO_KATEGORIE: TSmallintField
      FieldName = 'RISIKO_KATEGORIE'
      Origin = 'RISIKO_KATEGORIE'
    end
    object qu_stdat_revplanJ_MINDEST_KONTROLL_FREQUENZ: TSingleField
      FieldName = 'J_MINDEST_KONTROLL_FREQUENZ'
      Origin = 'J_MINDEST_KONTROLL_FREQUENZ'
    end
    object qu_stdat_revplanANZ_BETRIEBE_IM_LAND: TSmallintField
      FieldName = 'ANZ_BETRIEBE_IM_LAND'
      Origin = 'ANZ_BETRIEBE_IM_LAND'
    end
    object qu_stdat_revplanANZ_GESAMT_KONTROLLEN: TSmallintField
      FieldName = 'ANZ_GESAMT_KONTROLLEN'
      Origin = 'ANZ_GESAMT_KONTROLLEN'
    end
    object qu_stdat_revplanGESPERRT: TBooleanField
      FieldName = 'GESPERRT'
      Origin = 'GESPERRT'
      Required = True
    end
    object qu_stdat_revplanID_1: TFDAutoIncField
      FieldName = 'ID_1'
      Origin = 'ID'
      ReadOnly = True
    end
    object qu_stdat_revplanBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object qu_stdat_revplanSEKTION: TStringField
      FieldName = 'SEKTION'
      Origin = 'SEKTION'
    end
    object qu_stdat_revplanBETRIEBSGRUPPE_LM: TStringField
      FieldName = 'BETRIEBSGRUPPE_LM'
      Origin = 'BETRIEBSGRUPPE_LM'
      Size = 40
    end
    object qu_stdat_revplanBETRIEBSGRUPPE_DETAIL: TStringField
      FieldName = 'BETRIEBSGRUPPE_DETAIL'
      Origin = 'BETRIEBSGRUPPE_DETAIL'
      Size = 40
    end
    object qu_stdat_revplanBETRIEBSART: TStringField
      FieldName = 'BETRIEBSART'
      Origin = 'BETRIEBSART'
      Size = 500
    end
    object qu_stdat_revplanBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Size = 10
    end
    object qu_stdat_revplanKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      Size = 3
    end
  end
  object qu_stdat_revplan_erzeugen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'EXEC RevisionsplanErzeugen @Jahr = :jahr, @BLDCode = :bldcode;')
    Left = 1552
    Top = 304
    ParamData = <
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_stdat_revplan_update: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'update BEWEGUNGSDATEN.REVISIONSPLAN'
      'set RISIKO_KATEGORIE            = :risiko_kategorie,'
      '    J_MINDEST_KONTROLL_FREQUENZ = :mindestkontrollfrequenz'
      'where ID = :id'
      '  and BLDCODE = :BLDCODE')
    Left = 1552
    Top = 352
    ParamData = <
      item
        Name = 'RISIKO_KATEGORIE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MINDESTKONTROLLFREQUENZ'
        DataType = ftFloat
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end>
  end
  object qu_stdat_revplan_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'delete rp'
      'from BEWEGUNGSDATEN.REVISIONSPLAN rp'
      
        '         inner join BEWEGUNGSDATEN.REVISIONSSTAMM r on rp.ID_REV' +
        'ISIONSSTAMM = r.ID'
      'where rp.JAHR = :jahr'
      '  and rp.BLDCODE = :bldcode;')
    Left = 1552
    Top = 400
    ParamData = <
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = -1
      end>
  end
  object qu_user_gid: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.[User]'
      'WHERE  Userguid = :gid;')
    Left = 1552
    Top = 504
    ParamData = <
      item
        Name = 'GID'
        DataType = ftString
        ParamType = ptInput
        Value = '-1'
      end>
  end
  object qu_rollen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Rollen;')
    Left = 1696
    Top = 64
    object qu_rollenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object qu_rollenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 50
    end
    object qu_rollenDEFAULTROLLE: TBooleanField
      FieldName = 'DEFAULTROLLE'
      Origin = 'DEFAULTROLLE'
      Required = True
    end
    object qu_rollenSICHTBAR: TBooleanField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
      Required = True
    end
    object qu_rollenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
      Required = True
    end
    object qu_rollenPARAMETER: TStringField
      FieldName = 'PARAMETER'
      Origin = 'PARAMETER'
      Size = 500
    end
  end
  object qu_rollen_neu: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.Rollen (Bezeichnung, Defaultrolle, ' +
        'Sichtbar, Gueltig_ab, Parameter)'
      'VALUES      (:bez, :default, :sichtbar, :ab, :parameter);')
    Left = 1696
    Top = 112
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DEFAULT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AB'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PARAMETER'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_rollen_update: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Rollen'
      
        'SET    Bezeichnung = :bez, Defaultrolle = :default, Sichtbar = :' +
        'sichtbar, Gueltig_ab = :ab,'
      '       Parameter = :parameter'
      'WHERE  Id = :id;')
    Left = 1696
    Top = 160
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DEFAULT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AB'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PARAMETER'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_rollen_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Rollen'
      'WHERE       Id = :id;')
    Left = 1696
    Top = 208
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_funktionsrollen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Funktionsrollen fr'
      
        '        INNER JOIN Systemstammdaten.Rollen r ON fr.Id_Rolle = r.' +
        'Id'
      
        '        INNER JOIN Systemstammdaten.Funktionen f on fr.Id_Funkti' +
        'on = f.Id'
      'WHERE  r.Bezeichnung LIKE '#39'%'#39' + :rbez + '#39'%'#39
      '        AND f.Bezeichnung LIKE '#39'%'#39' + :fbez + '#39'%'#39';')
    Left = 1808
    Top = 64
    ParamData = <
      item
        Name = 'RBEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'FBEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
    object qu_funktionsrollenID_ROLLE: TIntegerField
      FieldName = 'ID_ROLLE'
      Origin = 'ID_ROLLE'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object qu_funktionsrollenID_FUNKTION: TIntegerField
      FieldName = 'ID_FUNKTION'
      Origin = 'ID_FUNKTION'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object qu_funktionsrollenBEGDAT: TDateField
      FieldName = 'BEGDAT'
      Origin = 'BEGDAT'
      Required = True
    end
    object qu_funktionsrollenENDDAT: TDateField
      FieldName = 'ENDDAT'
      Origin = 'ENDDAT'
      Required = True
    end
    object qu_funktionsrollenSICHTBAR: TBooleanField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
      Required = True
    end
    object qu_funktionsrollenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
    end
    object qu_funktionsrollenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 50
    end
    object qu_funktionsrollenDEFAULTROLLE: TBooleanField
      FieldName = 'DEFAULTROLLE'
      Origin = 'DEFAULTROLLE'
      Required = True
    end
    object qu_funktionsrollenSICHTBAR_1: TBooleanField
      FieldName = 'SICHTBAR_1'
      Origin = 'SICHTBAR'
      Required = True
    end
    object qu_funktionsrollenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
      Required = True
    end
    object qu_funktionsrollenPARAMETER: TStringField
      FieldName = 'PARAMETER'
      Origin = 'PARAMETER'
      Size = 500
    end
    object qu_funktionsrollenID_1: TFDAutoIncField
      FieldName = 'ID_1'
      Origin = 'ID'
      ReadOnly = True
    end
    object qu_funktionsrollenPROGMODUL: TStringField
      FieldName = 'PROGMODUL'
      Origin = 'PROGMODUL'
      Required = True
      FixedChar = True
      Size = 3
    end
    object qu_funktionsrollenBEZEICHNUNG_1: TStringField
      FieldName = 'BEZEICHNUNG_1'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 50
    end
    object qu_funktionsrollenHINTTEXT: TStringField
      FieldName = 'HINTTEXT'
      Origin = 'HINTTEXT'
      Size = 200
    end
    object qu_funktionsrollenID_MUTTER: TIntegerField
      FieldName = 'ID_MUTTER'
      Origin = 'ID_MUTTER'
    end
    object qu_funktionsrollenFARBCODE: TIntegerField
      FieldName = 'FARBCODE'
      Origin = 'FARBCODE'
    end
    object qu_funktionsrollenBEGDAT_1: TDateField
      FieldName = 'BEGDAT_1'
      Origin = 'BEGDAT'
      Required = True
    end
    object qu_funktionsrollenENDDAT_1: TDateField
      FieldName = 'ENDDAT_1'
      Origin = 'ENDDAT'
      Required = True
    end
    object qu_funktionsrollenSICHTBAR_2: TBooleanField
      FieldName = 'SICHTBAR_2'
      Origin = 'SICHTBAR'
      Required = True
    end
    object qu_funktionsrollenPROGCALLID: TIntegerField
      FieldName = 'PROGCALLID'
      Origin = 'PROGCALLID'
    end
    object qu_funktionsrollenOBJEKT: TStringField
      FieldName = 'OBJEKT'
      Origin = 'OBJEKT'
      Size = 50
    end
    object qu_funktionsrollenPRGTEIL: TStringField
      FieldName = 'PRGTEIL'
      Origin = 'PRGTEIL'
      Size = 50
    end
  end
  object qu_funktionsrollen_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Funktionsrollen'
      'SET    Enddat = :enddat'
      'WHERE  Id_Funktion = :id_funktion AND Id_Rolle = :id_rolle;')
    Left = 1808
    Top = 112
    ParamData = <
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_FUNKTION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ROLLE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_funktionsrollen_neu: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.Funktionsrollen (ID_Rolle, ID_Funkt' +
        'ion, Begdat, Enddat)'
      'VALUES      (:id_rolle, :id_funktion, :begdat, :enddat);')
    Left = 1808
    Top = 160
    ParamData = <
      item
        Name = 'ID_ROLLE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_FUNKTION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_funktionsrollen_funktion: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT Id, Bezeichnung, Progmodul'
      'FROM   Systemstammdaten.[Funktionen]'
      'WHERE  Bezeichnung LIKE '#39'%'#39' + :bez + '#39'%'#39' AND Bldcode = :bldcode;')
    Left = 1808
    Top = 208
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_funktionsrollen_rollen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT Id, Bezeichnung'
      'FROM   Systemstammdaten.Rollen'
      
        'WHERE  Bezeichnung LIKE '#39'%'#39' + :bez + '#39'%'#39' AND Bldcode = :bldcode;' +
        ' ')
    Left = 1808
    Top = 256
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_existiert_funktionsrolle: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT count(ID_Funktion) AS Anzahl'
      'FROM   Systemstammdaten.Funktionsrollen'
      'WHERE  ID_Funktion = :id_funktion AND ID_Rolle = :id_rolle;')
    Left = 1808
    Top = 304
    ParamData = <
      item
        Name = 'ID_FUNKTION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ROLLE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_funktionsrollen_update: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Funktionsrollen'
      'SET    Begdat = :begdat, Enddat = :enddat'
      'WHERE  ID_Funktion = :id_funktion AND ID_Rolle = :id_rolle;')
    Left = 1808
    Top = 352
    ParamData = <
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_FUNKTION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ROLLE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_rechtsgrundlagen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Rechtsgrundlagen;')
    Left = 1544
    Top = 592
    object qu_rechtsgrundlagenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object qu_rechtsgrundlagenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 300
    end
    object qu_rechtsgrundlagenKURZBEZEICHNUNG: TStringField
      FieldName = 'KURZBEZEICHNUNG'
      Origin = 'KURZBEZEICHNUNG'
      Required = True
      Size = 50
    end
  end
  object qu_rechtsgrundlagen_neu: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Rechtsgrundlagen (Bezeichnung, Kurzbezeic' +
        'hnung)'
      'VALUES      (:bez, :kurzbez);')
    Left = 1544
    Top = 640
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KURZBEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_rechtsgrundlagen_update: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Rechtsgrundlagen'
      'SET    Bezeichnung = :bez, Kurzbezeichnung = :kurzbez'
      'WHERE  Id = :id;')
    Left = 1544
    Top = 688
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KURZBEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_rechtsgrundlagen_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Rechtsgrundlagen'
      'WHERE       Id = :id;')
    Left = 1544
    Top = 736
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = -1
      end>
  end
  object qu_bkb_rechtsgrundlage: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'SELECT b.Bkbtyp, b.Bezeichnung AS "BkbBezeichnung", r.Id, r.Beze' +
        'ichnung, r.Kurzbezeichnung'
      'FROM   Stammdaten.Bkbtypen_Rechtsgrundlage br'
      '        INNER JOIN Stammdaten.Bkbtypen b ON br.bkbtyp = b.bkbtyp'
      
        '        INNER JOIN Stammdaten.Rechtsgrundlagen r ON br.Id_Rechts' +
        'grundlage = r.Id;')
    Left = 1696
    Top = 544
  end
  object qu_bkb_rechtsgrundlage_neu: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Bkbtypen_Rechtsgrundlage (Bkbtyp, Id_Rech' +
        'tsgrundlage)'
      'VALUES      (:bkbtyp, :id_rechtsgrundlage);')
    Left = 1696
    Top = 592
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_RECHTSGRUNDLAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object qu_bkb_rechtsgrundlage_loeschen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Bkbtypen_Rechtsgrundlage'
      'WHERE       Id_Rechtsgrundlage = :id AND Bkbtyp = :bkbtyp;')
    Left = 1696
    Top = 640
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = -1
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = '-1'
      end>
  end
  object qu_bkb_rechtsgrundlage_bkb: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bkbtypen;')
    Left = 1696
    Top = 696
  end
  object qu_bkb_rechtsgrundlage_rechtsgrundlage: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Rechtsgrundlagen;')
    Left = 1696
    Top = 752
  end
  object QSys: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select * from Systemstammdaten.SYS')
    Left = 24
    Top = 80
    object QSysSYSTEMKZ: TStringField
      FieldName = 'SYSTEMKZ'
      Origin = 'SYSTEMKZ'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 3
    end
    object QSysSYSTEMNAME: TStringField
      FieldName = 'SYSTEMNAME'
      Origin = 'SYSTEMNAME'
      Size = 50
    end
    object QSysVERSION_DB: TStringField
      FieldName = 'VERSION_DB'
      Origin = 'VERSION_DB'
      Size = 10
    end
    object QSysVERSION_MOPED: TStringField
      FieldName = 'VERSION_MOPED'
      Origin = 'VERSION_MOPED'
      Size = 10
    end
    object QSysVERSION_IW: TStringField
      FieldName = 'VERSION_IW'
      Origin = 'VERSION_IW'
      Size = 10
    end
    object QSysVERSION_REST: TStringField
      FieldName = 'VERSION_REST'
      Origin = 'VERSION_REST'
      Size = 10
    end
    object QSysVERSION_CCK: TStringField
      FieldName = 'VERSION_CCK'
      Origin = 'VERSION_CCK'
      Size = 10
    end
  end
  object qu_free: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    Left = 824
    Top = 216
  end
  object qu_stdat_revplan_abschliessen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      
        'select distinct b.ID_BETRIEB                                    ' +
        '                                         as ID,'
      '                first_value(s.KONTROLLTYP)'
      
        '                            over (partition by b.ID_BETRIEB orde' +
        'r by p.J_MINDEST_KONTROLL_FREQUENZ desc) as KONTROLLTYP,'
      '                first_value(s.BKBTYP)'
      
        '                            over (partition by b.ID_BETRIEB orde' +
        'r by p.J_MINDEST_KONTROLL_FREQUENZ desc) as BKBTYP,'
      '                first_value(p.J_MINDEST_KONTROLL_FREQUENZ)'
      
        '                            over (partition by b.ID_BETRIEB orde' +
        'r by p.J_MINDEST_KONTROLL_FREQUENZ desc) as FREQUENZ'
      'from STAMMDATEN.BETRIEBE_REVSTAMM b'
      
        '         inner join BEWEGUNGSDATEN.REVISIONSSTAMM s on s.ID = b.' +
        'ID_REVISIONSSTAMM'
      '         inner join BEWEGUNGSDATEN.REVISIONSPLAN p'
      
        '                    on s.ID = p.ID_REVISIONSSTAMM and p.JAHR = :' +
        'jahr and p.BLDCODE = :bldcode'
      'where s.BLDCODE = :bldcode'
      '  and p.J_MINDEST_KONTROLL_FREQUENZ > 0'
      '  and s.KONTROLLTYP is not null'
      '  and s.BKBTYP is not null'
      '  and p.GESPERRT != 1')
    Left = 1552
    Top = 448
    ParamData = <
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
    object qu_stdat_revplan_abschliessenID: TIntegerField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object qu_stdat_revplan_abschliessenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      ReadOnly = True
      Size = 3
    end
    object qu_stdat_revplan_abschliessenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ReadOnly = True
      Size = 10
    end
    object qu_stdat_revplan_abschliessenFREQUENZ: TSingleField
      FieldName = 'FREQUENZ'
      Origin = 'FREQUENZ'
      ReadOnly = True
    end
  end
  object qu_stdat_revplan_sperren: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'update BEWEGUNGSDATEN.REVISIONSPLAN'
      'set GESPERRT = 1'
      'where JAHR = :jahr'
      '  and BLDCODE = :bldcode')
    Left = 1640
    Top = 376
    ParamData = <
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
  end
  object qu_stdat_revplan_kontrollen: TFDQuery
    ActiveStoredUsage = []
    Connection = FBC_MAIN
    SQL.Strings = (
      'select count(*) as Anzahl'
      'from BEWEGUNGSDATEN.KONTROLLBERICHT'
      'where ID_BETRIEB = :Betrieb'
      '  and KONTROLLTYP = :Kontrolltyp'
      
        '  and convert(datetime, cast((:Jahr + 1 - cast(1 / :Frequenz as ' +
        'int)) as char(4)) + '#39'-01-01'#39', 102) < ENDEZEIT;')
    Left = 1640
    Top = 432
    ParamData = <
      item
        Name = 'BETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FREQUENZ'
        DataType = ftFloat
        ParamType = ptInput
        Value = Null
      end>
    object qu_stdat_revplan_kontrollenAnzahl: TIntegerField
      FieldName = 'Anzahl'
      Origin = 'Anzahl'
      ReadOnly = True
    end
  end
end
