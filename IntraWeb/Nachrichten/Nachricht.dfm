object NachrichtFrame: TNachrichtFrame
  Left = 0
  Top = 0
  Width = 380
  Height = 478
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 380
    Height = 478
    Version = '1.0'
    Align = alClient
    Color = clWebWHITE
    object IWLabel1: TIWCGJQLabel
      Left = 16
      Top = 16
      Width = 29
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel1'
      Caption = 'Von:'
    end
    object iwlAbsender: TIWCGJQLabel
      Left = 105
      Top = 16
      Width = 248
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'iwlAbsender'
      Caption = 'absender'
    end
    object iwtText: TIWText
      Left = 16
      Top = 80
      Width = 337
      Height = 348
      BGColor = clWebTransparent
      ConvertSpaces = False
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      FriendlyName = 'iwtText'
      Lines.Strings = (
        'iwtText')
      RawText = False
      UseFrame = False
      WantReturns = True
    end
    object IWLabel2: TIWCGJQLabel
      Left = 16
      Top = 36
      Width = 26
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel1'
      Caption = 'Am:'
    end
    object iwlDatum: TIWCGJQLabel
      Left = 105
      Top = 38
      Width = 248
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'iwlAbsender'
      Caption = 'datum'
    end
    object iwuLink: TIWURL
      Left = 16
      Top = 425
      Width = 200
      Height = 17
      Alignment = taLeftJustify
      Color = clNone
      Font.Color = clNone
      Font.Size = 10
      Font.Style = [fsUnderline]
      HasTabOrder = True
      TargetOptions.AddressBar = False
      TerminateApp = False
      UseTarget = False
      FriendlyName = 'iwuLink'
      TabOrder = 2
      RawText = False
    end
    object IWLabel3: TIWCGJQLabel
      Left = 17
      Top = 58
      Width = 88
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel1'
      Caption = 'Gesendet am:'
    end
    object iwlGesendetAm: TIWCGJQLabel
      Left = 105
      Top = 58
      Width = 248
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'iwlAbsender'
      Caption = 'datum'
    end
    object jqbBestaetigen: TIWCGJQButton
      Left = 16
      Top = 448
      Width = 105
      Height = 21
      TabOrder = 2
      Version = '1.0'
      JQButtonOptions.Label_ = 'Best'#228'tigen'
      JQButtonOptions.OnClick.OnEvent = jqbBestaetigenOnClick
    end
  end
end
