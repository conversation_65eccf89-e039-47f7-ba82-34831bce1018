object DMNachrichten: TDMNachrichten
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  Height = 618
  Width = 916
  object QNachrichten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT n.ID,'
      '       n.ID_GRUPPE,'
      '       g.BEZEICHNUNG as Gruppe,'
      '       u.ID as ID_USER,'
      '       u.USERNAME,'
      '       n.text AS "Nachricht",'
      '       n.Gesendet_Am,'
      
        '       CASE WHEN nz.gesehen_tstamp IS NULL THEN 0 ELSE 1 END AS ' +
        '"gesehen_tstamp",'
      
        '       CASE WHEN nz.gelesen_tstamp IS NULL THEN 0 ELSE 1 END AS ' +
        '"gelesen_tstamp"'
      'FROM Bewegungsdaten.nachrichten n'
      
        '    JOIN SYSTEMSTAMMDATEN.USERGRUPPEN ug ON n.ID_GRUPPE=ug.ID_GR' +
        'UPPE'
      '    JOIN SYSTEMSTAMMDATEN.[USER] u on ug.ID_USER=u.ID'
      '    JOIN SYSTEMSTAMMDATEN.GRUPPEN g on n.ID_GRUPPE=g.ID'
      
        '    LEFT OUTER JOIN Bewegungsdaten.nachrichten_zustellung nz ON ' +
        'n.id = nz.id_nachricht AND nz.ID_USER=u.ID'
      'where'
      '     u.Username like :Username'
      'and  n.text like :Nachrichtentext'
      'and n.ID_gruppe in &gruppen'
      'and &Gelesen --and &Gesehen'
      'and u.BLDCODE = :BLDCODE'
      ''
      ''
      'ORDER BY GESENDET_AM desc, id')
    Left = 50
    Top = 50
    ParamData = <
      item
        Name = 'USERNAME'
        DataType = ftString
        ParamType = ptInput
        Value = '%'
      end
      item
        Name = 'NACHRICHTENTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = '%'
      end
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end>
    MacroData = <
      item
        Value = '(select distinct n.ID_gruppe)'
        Name = 'GRUPPEN'
      end
      item
        Value = '1=1'
        Name = 'GELESEN'
      end
      item
        Value = '1=1'
        Name = 'GESEHEN'
      end>
    object QNachrichtenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QNachrichtenID_GRUPPE: TIntegerField
      FieldName = 'ID_GRUPPE'
      Origin = 'ID_GRUPPE'
      Required = True
    end
    object QNachrichtenGruppe: TStringField
      FieldName = 'Gruppe'
      Origin = 'Gruppe'
      Required = True
      Size = 100
    end
    object QNachrichtenID_USER: TFDAutoIncField
      FieldName = 'ID_USER'
      Origin = 'ID_USER'
      ReadOnly = True
    end
    object QNachrichtenUSERNAME: TStringField
      FieldName = 'USERNAME'
      Origin = 'USERNAME'
      Size = 40
    end
    object QNachrichtenNachricht: TWideStringField
      FieldName = 'Nachricht'
      Origin = 'Nachricht'
      Size = 1000
    end
    object QNachrichtenGesendet_Am: TSQLTimeStampField
      FieldName = 'Gesendet_Am'
      Origin = 'Gesendet_Am'
      Required = True
    end
    object QNachrichtengesehen_tstamp: TIntegerField
      FieldName = 'gesehen_tstamp'
      Origin = 'gesehen_tstamp'
      ReadOnly = True
      Required = True
    end
    object QNachrichtengelesen_tstamp: TIntegerField
      FieldName = 'gelesen_tstamp'
      Origin = 'gelesen_tstamp'
      ReadOnly = True
      Required = True
    end
  end
  object QUserGruppen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'Select *'
      'from Systemstammdaten.Gruppen'
      'where BLDCODE = :BLDCODE'
      'Order by Bezeichnung')
    Left = 152
    Top = 50
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end>
    object QUserGruppenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QUserGruppenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QUserGruppenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QUserGruppenMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QUserGruppenID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QUserGruppenID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QUserGruppenOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QUserGruppenPERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
  end
end
