﻿unit Nachricht;

interface

uses
  SysUtils,Classes,Controls,IWCGFrame,IWVCLBaseContainer,IWContainer,IWHTMLContainer,IWHTML40Container,IWCGJQRegion,
  Vcl.Forms, IWRegion, IWCGJQControl, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompLabel, IWCompText, IWCGJQButton,
  IWHTMLControls, IWCGJQLabel;

type
  TNachrichtFrame = class(TIWCGJQFrame)
    IWFrameRegion: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    iwlAbsender: TIWCGJQLabel;
    iwtText: TIWText;
    jqbBestaetigen: TIWCGJQButton;
    IWLabel2: TIWCGJQLabel;
    iwlDatum: TIWCGJQLabel;
    iwuLink: TIWURL;
    IWLabel3: TIWCGJQLabel;
    iwlGesendetAm: TIWCGJQLabel;
    procedure jqbBestaetigenOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    id: Integer;
  public
    { Public declarations }
    procedure setText(text: String);
    procedure setAbsender(absender: String);
    procedure setDatum(datum: String);
    procedure setLink(link: String);
    procedure setId(id: Integer);
    procedure setGesendetAm(datum: String);
  end;

implementation

uses ServerController;

{$R *.dfm}

procedure TNachrichtFrame.setText(text: String);
begin
  iwtText.Text := text;
end;

procedure TNachrichtFrame.setAbsender(absender: String);
begin
  iwlAbsender.Caption := absender;
end;

procedure TNachrichtFrame.setLink(link: String);
begin
  iwuLink.URL := link;
  iwuLink.Caption := link;
end;

procedure TNachrichtFrame.setDatum(datum: String);
begin
  iwlDatum.Caption := datum;
end;

procedure TNachrichtFrame.setGesendetAm(datum: string);
begin
  iwlGesendetAm.Caption := datum;
end;

{Setzt die Id der angezeigten Nachricht um bei einem Klick auf Bestätigen die
 Nachricht bestätigen zu können. Ist die Id -1, wird der Button ausgeblendet.}
procedure TNachrichtFrame.setId(id: Integer);
begin
  self.id := id;
  if id = - 1 then begin
    jqbBestaetigen.Visible := false;
  end;
end;

procedure TNachrichtFrame.jqbBestaetigenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if id <> -1 then begin
    UserSession.ELKERest.NachrichtGelesen(id);
    id := -1;
    jqbBestaetigen.Visible := false;
  end;
end;

end.
