﻿unit Forms.Nachrichten;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, IWRegion,
  IWCGJQButton, Vcl.Imaging.jpeg, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls, Vcl.Forms,
  IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container,
  IWCGJQControl, IWCGJQRegion, IWCompLabel, IWCompMemo, IWCGJQGrid,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component,
  Data.DB, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQCheckBoxList, System.Variants,
  IWCGJQAutoComplete, IWCompEdit, IWCompCheckbox, IWCGJQDropDown, IWCompListbox,
  FireDAC.Comp.Client, Forms.Base, IWCompTabControl, IWCompGrids, IWCompText,
  IWCGJQGridCollectionProvider, IWCGJQResponsiveList, IWCGJQEdit, IWCGJQComp,
  IWCGJQSweetAlert,  IWCGJQLabel, dmmain, Modules.Nachrichten, IWCGJQTabs, IWCGJQCombobox, JQ.Helpers.ComboboxEx;

type
  TFormNachrichten = class(TFormBase)
    iwrTop: TIWCGJQRegion;
    GridNachrichten: TIWCGJQGrid;
    ProviderNachrichten: TIWCGJQGridDataSetProvider;
    EditSucheNachrichtentext: TIWCGJQEdit;
    ButtonSuchen: TIWCGJQButton;
    dsAutocompleteUsername: TDataSource;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    ComboSucheStatus: TIWCGJQComboBoxEx;
    IWLabel4: TIWCGJQLabel;
    EditSucheBenutzer: TIWCGJQEdit;
    ComboSucheGruppen: TIWCGJQCheckBoxList;
    IWLabel5: TIWCGJQLabel;
    iwrNeueNachricht: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    jqclGruppen: TIWCGJQCheckBoxList;
    iwlNachricht: TIWCGJQLabel;
    iwmNachrichtentext: TIWMemo;
    iwlZeichen: TIWCGJQLabel;
    jqbNachrichtVersenden: TIWCGJQButton;
    iwrEigeneNachrichten: TIWCGJQRegion;
    jqrlEigeneNachrichten: TIWCGJQResponsiveList;
    IWLabel7: TIWCGJQLabel;
    jqeExternerLink: TIWCGJQEdit;
    jqsAlert: TIWCGJQSweetAlert;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQButton1: TIWCGJQButton;
    RegionNachrichten: TIWCGJQRegion;
    TabsNachrichten: TIWCGJQTabs;
    TabNachrichtenuebersicht: TIWCGJQTab;
    TabEigeneNachrichten: TIWCGJQTab;
    TabNeueNachricht: TIWCGJQTab;
    AlertNachrichtVersenden: TIWCGJQSweetAlert;
    procedure iwcgb_zurueckJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwmNachrichtentextAsyncKeyDown(Sender: TObject; EventParams: TStringList);
    procedure IWAppFormCreate(Sender: TObject);
    procedure jqbNachrichtVersendenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonSuchenOnClick(Sender: TObject; AParams: TStringList);
    procedure JqclGruppenOnClick(Sender: TObject; AParams: TStringList);
    procedure JqclGruppenOnUncheckAll(Sender: TObject; AParams: TStringList);
    procedure IWCGJQButton1JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure TabsNachrichtenJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
    procedure AlertNachrichtVersendenJQSweetAlertOptionsBtnClick(Sender: TObject; AParams: TStringList);
  private
    FDM: TDMNAchrichten;
    procedure EigeneNachrichtenLaden;
    procedure AlleNachrichtenLaden;
    procedure NachrichtVersenden;
    procedure FunktionenKonfigurieren;
    procedure CheckSendenErlaubt;
  public
    function DM: TDMNAchrichten;
  end;

implementation

uses
  FireDAC.Stan.Param, System.Generics.Collections, IWContainerBorderOptions,
  IWCGJQCommon, Funktionen, ELKE.Classes, ServerController, Nachricht;

{$R *.dfm}


procedure TFormNachrichten.IWAppFormCreate(Sender: TObject);
var
  clItem: TIWcgJQCheckboxitem;
  clItemSuche: TIWcgJQCheckboxitem;
begin
  DM.QUserGruppen.Open;
  DM.QUserGruppen.First;
  while not DM.QUserGruppen.Eof do
  begin
    clItem := jqclGruppen.Items.Add;
    clItem.Value := DM.QUserGruppenID.AsString;
    clItem.Caption := DM.QUserGruppenBEZEICHNUNG.AsString;

    clItemSuche := ComboSucheGruppen.Items.Add;
    clItemSuche.Value := DM.QUserGruppenID.AsString;
    clItemSuche.Caption := DM.QUserGruppenBEZEICHNUNG.AsString;
    clItemSuche.Selected := false;

    DM.QUserGruppen.Next;
  end;
  DM.QUserGruppen.Close;

  ComboSucheStatus.Items.Add('Gelesen');
  ComboSucheStatus.Items.Add('Ungelesen');
  ComboSucheStatus.NoSelectionText := 'Keine Auswahl';

  ProviderNachrichten.DataSet := DM.QNachrichten;
  ProviderNachrichten.KeyFields := 'ID;ID_GRUPPE';

  FunktionenKonfigurieren;
end;

/// Konfiguriert die Funktionen, die der Benutzer verwenden darf.
procedure TFormNachrichten.FunktionenKonfigurieren;
var
  LFunktionen: TFunktionenManager;
begin
  LFunktionen := Usersession.FunktionenManager;

  TabNeueNachricht.Visible := LFunktionen.HatFunktion(Nachrichten_senden);
  TabNachrichtenuebersicht.Visible := LFunktionen.HatFunktion(Alle_Nachrichten_anzeigen);
  TabEigeneNachrichten.Visible := LFunktionen.HatFunktion(Eigene_Nachrichten_anzeigen);

  TabsNachrichten.ActiveTab := nil;
  if TabNachrichtenuebersicht.Visible then
    TabsNachrichten.ActiveTab := TabNachrichtenuebersicht
  else if TabEigeneNachrichten.Visible then
    TabsNachrichten.ActiveTab := TabEigeneNachrichten
  else if TabNeueNachricht.Visible then
    TabsNachrichten.ActiveTab := TabNeueNachricht;
  if TabsNachrichten.ActiveTab <> nil then
    TabsNachrichtenJQTabOptionsSelect(nil, nil);
end;

procedure TFormNachrichten.iwcgb_zurueckJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  Release;
end;

procedure TFormNachrichten.IWCGJQButton1JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  EigeneNachrichtenLaden;
end;

procedure TFormNachrichten.ButtonSuchenOnClick(Sender: TObject; AParams: TStringList);
begin
  AlleNachrichtenLaden;
end;

// ******************************************************************************
// ****************************Nachricht verfassen*******************************

// Wenn der Benutzer eine Gruppe anklickt
procedure TFormNachrichten.JqclGruppenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  CheckSendenErlaubt;
end;

procedure TFormNachrichten.JqclGruppenOnUncheckAll(Sender: TObject;
  AParams: TStringList);
begin
  CheckSendenErlaubt;
end;

procedure TFormNachrichten.NachrichtVersenden;
begin
  var
  link := jqeExternerLink.Text;
  // Check ob der Link ein gültiges Format hat
  if jqeExternerLink.Text.Length > 0 then
  begin
    if not((link.StartsWith('http://') or link.StartsWith('https://')) and
      (link.CountChar('.') >= 2)) then
    begin
      jqsAlert.Warning('Der Link ist ungültig.' + sLineBreak +
        'Format: http(s)://beispiel.beispiel.beispiel');
      Exit;
    end;
  end;

  for var i := 0 to jqclGruppen.Items.Count - 1 do
  begin
    if jqclGruppen.Items[i].Selected then
    begin
      dm_main.qu_sendNachricht.Close;
      dm_main.qu_sendNachricht.Prepare;
      dm_main.qu_sendNachricht.ParamByName('idGruppe').AsSmallInt := strtoint(jqclGruppen.Items[i].Value);
      dm_main.qu_sendNachricht.ParamByName('status').AsString := 'O';
      dm_main.qu_sendNachricht.ParamByName('typ').AsString := 'T';
      dm_main.qu_sendNachricht.ParamByName('absenderkz').AsString := 'U';
      dm_main.qu_sendNachricht.ParamByName('priori').AsSmallInt := 3;
      dm_main.qu_sendNachricht.ParamByName('absenderid').AsInteger := dm_main.UserId;
      dm_main.qu_sendNachricht.ParamByName('text').AsString := iwmNachrichtentext.Text;
      if jqeExternerLink.Text.Length > 0 then
      begin
        dm_main.qu_sendNachricht.ParamByName('link').AsString := link;
      end
      else
      begin
        dm_main.qu_sendNachricht.ParamByName('link').Clear;
      end;
      dm_main.qu_sendNachricht.Execute;
    end;
  end;
  jqclGruppen.UncheckAll;
  iwmNachrichtentext.Clear;
  jqeExternerLink.Text := '';
  jqbNachrichtVersenden.Enabled := false;
  iwlZeichen.Caption := 'Anzahl Zeichen: 0';
end;

{ Wird aufgerufen, wenn ein KeyDown Event im Nachrichten-Textfeld passiert.
  Überprüft ob die Textlänge 1000 Zeichen überschreitet und schneidet den Text im
  Bedarfsfall ab. }
procedure TFormNachrichten.iwmNachrichtentextAsyncKeyDown(Sender: TObject; EventParams: TStringList);
var
  zaehler: Integer;
begin
  zaehler := iwmNachrichtentext.Lines.Text.Length;
  if not(zaehler = 0) then
  begin
    zaehler := zaehler - 2;
  end;
  if zaehler > 1000 then
  begin
    iwmNachrichtentext.Lines.Text := iwmNachrichtentext.Lines.Text.Remove(1000);
    zaehler := iwmNachrichtentext.Lines.Text.Length - 2;
  end;
  iwlZeichen.Caption := 'Anzahl Zeichen:' + IntToStr(zaehler) + '/1000';
  CheckSendenErlaubt;
end;

// Überprüft ob schon eine Nachricht versendet werden darf und enabled dann den Knopf
procedure TFormNachrichten.CheckSendenErlaubt;
begin
  jqbNachrichtVersenden.Enabled := (jqclGruppen.SelectedCount > 0) and
    (iwmNachrichtentext.Lines.Text.Length > 0);
end;

function TFormNachrichten.DM: TDMNAchrichten;
begin
  if FDM = nil then
  begin
    FDM := TDMNAchrichten.Create(self);
  end;
  result := FDM;
end;

{ Öffnet ein Modalfenster in welchem der User eine neue Nachricht verfassen kann. }
procedure TFormNachrichten.jqbNachrichtVersendenOnClick(Sender: TObject; AParams: TStringList);
begin
  if (not(iwmNachrichtentext.Text = '')) and (jqclGruppen.SelectedCount <> 0) then
  begin
    AlertNachrichtVersenden.Show;
  end;
end;

procedure TFormNachrichten.AlertNachrichtVersendenJQSweetAlertOptionsBtnClick(Sender: TObject; AParams: TStringList);
begin
  if AParams.Values['isConfirm'] = 'true' then
  begin
    NachrichtVersenden;
  end;
end;

procedure TFormNachrichten.AlleNachrichtenLaden;
var
  LGruppenIds: TArray<Integer>;
begin
  // Selektierte Gruppen
  SetLength(LGruppenIds, ComboSucheGruppen.SelectedCount);
  var
  j := 0;
  for var i := 0 to ComboSucheGruppen.Items.Count - 1 do
  begin
    var
    LItem := TIWcgJQCheckboxitem(ComboSucheGruppen.Items[i]);
    if LItem.Selected and (LItem.Value.ToInteger > 0) then
    begin
      SetLength(LGruppenIds, j + 1);
      LGruppenIds[j] := LItem.Value.ToInteger;
      inc(j);
    end;
  end;

  // Selektierte Stati
  var
    LStati: TNachrichtenStati;
  if ComboSucheStatus.SelectedText.ToLower = 'gelesen' then
    LStati := LStati + [TNachrichtenStatus.Gelesen]
  else if ComboSucheStatus.SelectedText.ToLower = 'ungelesen' then
    LStati := LStati + [TNachrichtenStatus.UnGelesen];

  DM.NachrichtenLaden(EditSucheBenutzer.Text, EditSucheNachrichtentext.Text, LGruppenIds, LStati);
end;

procedure TFormNachrichten.TabsNachrichtenJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
begin
  if TabsNachrichten.ActiveTab = TabNachrichtenuebersicht then
  begin
    EditSucheNachrichtentext.Text := '';
    EditSucheBenutzer.Text := '';
    ComboSucheStatus.ItemIndex := -1;
    AlleNachrichtenLaden;
  end
  else if TabsNachrichten.ActiveTab = TabNeueNachricht then
  begin
    jqbNachrichtVersenden.Enabled := false;
    iwmNachrichtentext.Clear;
    jqclGruppen.UncheckAll;
  end
  else if TabsNachrichten.ActiveTab = TabEigeneNachrichten then
  begin
    EigeneNachrichtenLaden;
  end;
end;

// ******************************************************************************
// ****************************Eigene Nachrichten********************************

{ Ladet die eigenen Nachrichten von der Rest-Schnittstelle und erstellt für
  jede Nachricht ein TNachrichtFrame in der sie angezeigt wird. }
procedure TFormNachrichten.EigeneNachrichtenLaden;
var
  Nachricht: TNachrichtKurz;
  Nachrichten: TList<TNachrichtKurz>;
  i, counter: Integer;
  LItem: TIWCGJQResponsiveListItem;
  nachrichtFrame: TNachrichtFrame;
  name: string;
begin

  // Erst alles löschen
  for var j := self.ComponentCount - 1 downto 0 do
  begin
    var
    LComponent := self.Components[j];
    if LComponent is TNachrichtFrame then
      LComponent.Free;
  end;
  jqrlEigeneNachrichten.ClearPanels;

  Nachrichten := Usersession.ELKERest.GetNachrichten;
  try
    // Nachrichten werden sortiert, sodass die ungelesenen immer als erstes
    // angezeigt werden.
    counter := Nachrichten.Count - 1;
    i := 0;
    while i < counter do
    begin
      if Nachrichten[i].Gelesen.HasValue then
      begin
        Nachricht := Nachrichten[counter];
        Nachrichten[counter] := Nachrichten[i];
        Nachrichten[i] := Nachricht;
        counter := counter - 1;
      end
      else
      begin
        i := i + 1;
      end;
    end;

    for i := 0 to Nachrichten.Count - 1 do
    begin
      LItem := jqrlEigeneNachrichten.AddNewPanel;
      LItem.BorderOptions.NumericWidth := 1;
      LItem.BorderOptions.Style := cbsSolid;
      LItem.BorderOptions.Color := clWebBLACK;
      nachrichtFrame := TNachrichtFrame.Create(self);
      nachrichtFrame.name := CGFindUniqueComponentName(jqrlEigeneNachrichten, 'FRAME' + IntToStr(i));
      nachrichtFrame.Parent := LItem;
      nachrichtFrame.Align := alClient;

      Nachricht := Nachrichten[i];
      if Nachricht.Text.HasValue then
        nachrichtFrame.setText(Nachricht.Text);
      if Nachricht.link.HasValue then
        nachrichtFrame.setLink(Nachricht.link);
      if Nachricht.Gesehen.HasValue then
        nachrichtFrame.setDatum(DateTimeToStr(Nachricht.Gesehen.Value));
      nachrichtFrame.setGesendetAm(DateTimeToStr(Nachricht.Gesendet));
      name := '';
      if Nachricht.AbsenderKz = 'U' then
      begin
        if not Nachricht.Absender.Titel.IsNull then
        begin
          name := Nachricht.Absender.Titel;
        end;
        name := name + Nachricht.Absender.Vorname + ' ' + Nachricht.Absender.Nachname;
      end
      else
      begin
        name := 'von ELKE generiert';
      end;
      nachrichtFrame.setAbsender(name);
      if Nachricht.Gelesen.IsNull then
      begin
        nachrichtFrame.setId(Nachricht.Id);
      end
      else
      begin
        nachrichtFrame.setId(-1);
      end;
    end;
  finally
    Nachrichten.Free;
  end;
end;

end.
