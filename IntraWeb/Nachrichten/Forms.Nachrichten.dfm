inherited FormNachrichten: TFormNachrichten
  Width = 1102
  Height = 644
  Title = 'Nachrichten'
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1102
    TabOrder = 9
    inherited ImageLogo: TIWImageFile
      Left = 819
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 610
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 11
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 619
    Width = 1102
    TabOrder = 20
  end
  object TabsNachrichten: TIWCGJQTabs [2]
    Left = 0
    Top = 50
    Width = 1102
    Height = 569
    TabOrder = 13
    Version = '1.0'
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    ActiveTab = TabNachrichtenuebersicht
    JQTabOptions.OnSelect.Ajax = False
    JQTabOptions.OnSelect.AjaxAppend = False
    JQTabOptions.OnSelect.OnEvent = TabsNachrichtenJQTabOptionsSelect
    object TabNachrichtenuebersicht: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Nachrichten'#252'bersicht'
      TabIndex = 0
      Tabs = TabsNachrichten
      object iwrTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1098
        Height = 105
        RenderInvisibleControls = True
        TabOrder = 14
        Version = '1.0'
        Align = alTop
        StyleRenderOptions.UseDisplay = True
        object IWLabel2: TIWCGJQLabel
          Left = 20
          Top = 37
          Width = 102
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel2'
          Caption = 'Nachrichtentext:'
        end
        object IWLabel3: TIWCGJQLabel
          Left = 20
          Top = 75
          Width = 102
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Benutzer:'
        end
        object IWLabel4: TIWCGJQLabel
          Left = 373
          Top = 73
          Width = 60
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel4'
          Caption = 'Status:'
        end
        object IWLabel5: TIWCGJQLabel
          Left = 373
          Top = 37
          Width = 60
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel4'
          Caption = 'Gruppen:'
        end
        object ComboSucheStatus: TIWCGJQComboBoxEx
          Left = 442
          Top = 68
          Width = 229
          Height = 21
          TabOrder = 2
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Items = <>
          Groups = <>
          SelectedIndex = 0
          JQComboBoxExOptions.Width = 227
          Caption = ''
        end
        object EditSucheNachrichtentext: TIWCGJQEdit
          Left = 126
          Top = 32
          Width = 229
          Height = 21
          TabOrder = 1
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object EditSucheBenutzer: TIWCGJQEdit
          Left = 126
          Top = 70
          Width = 229
          Height = 21
          TabOrder = 3
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object ButtonSuchen: TIWCGJQButton
          Left = 708
          Top = 58
          Width = 116
          Height = 33
          TabOrder = 19
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = ButtonSuchenOnClick
        end
        object ComboSucheGruppen: TIWCGJQCheckBoxList
          Left = 442
          Top = 32
          Width = 229
          Height = 25
          Version = '1.0'
          StyleRenderOptions.RenderVisibility = True
          StyleRenderOptions.RenderAbsolute = False
          StyleRenderOptions.RenderBorder = False
          StyleRenderOptions.UseDisplay = True
          ZIndex = 5000
          JQCheckBoxListOptions.MinWidth = 229
          JQCheckBoxListOptions.CheckAllText = 'alle Gruppen ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Gruppen ausw'#228'hlen'
          JQCheckBoxListOptions.SelectedText = '# Gruppen ausgew'#228'hlt'
          JQCheckBoxListOptions.MenuZIndex = 5000
          Items = <>
          Groups = <>
        end
      end
      object RegionNachrichten: TIWCGJQRegion
        Left = 0
        Top = 105
        Width = 1098
        Height = 426
        TabOrder = 15
        Version = '1.0'
        Align = alClient
        object GridNachrichten: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 1098
          Height = 426
          TabOrder = 12
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              DateFmt = 'dd.mm.yyyy'
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Gesendet_Am'
              Name = 'Gesendet_Am'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 75
              Caption = 'Gesendet_Am'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'nachricht'
              Name = 'nachricht'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Width = 300
              Caption = 'Nachricht'
              ProviderName = 'nachricht'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Benutzer'
              Name = 'Benutzer'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 100
              Caption = 'Empf'#228'nger'
              ProviderName = 'USERNAME'
            end
            item
              Align = gaCenter
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Formatter = gcfCheckBox
              FormatOptions.Disabled = False
              Idx = 'gesehen_tstamp'
              Name = 'gesehen_tstamp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 30
              Caption = 'Gesehen'
            end
            item
              Align = gaCenter
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Formatter = gcfCheckBox
              FormatOptions.Disabled = False
              Idx = 'gelesen_tstamp'
              Name = 'gelesen_tstamp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 30
              Caption = 'Gelesen'
            end>
          JQGridOptions.Height = 399
          JQGridOptions.IgnoreCase = True
          JQGridOptions.LoadOnce = True
          JQGridOptions.LoadText = 'Lade Daten...'
          JQGridOptions.RowNum = 100000
          JQGridOptions.ScrollRows = True
          JQGridOptions.Sortable = True
          JQGridOptions.SortName = 'Gesendet_Am'
          JQGridOptions.SortOrder = gsoDesc
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 1096
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.PagerVisible = False
          JQGridNav.Active = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.RefreshTitle = 'aktualisieren'
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderNachrichten
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object TabEigeneNachrichten: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Eigene Nachrichten'
      TabIndex = 1
      Tabs = TabsNachrichten
      object IWCGJQRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 0
        Height = 50
        Margins.Left = 0
        Margins.Top = 0
        Margins.Right = 5
        Margins.Bottom = 0
        TabOrder = 16
        Version = '1.0'
        Align = alTop
        object IWCGJQButton1: TIWCGJQButton
          AlignWithMargins = True
          Left = 16
          Top = 5
          Width = 105
          Height = 40
          Margins.Left = 16
          Margins.Top = 5
          Margins.Right = 0
          Margins.Bottom = 5
          TabOrder = 10
          Version = '1.0'
          Align = alLeft
          JQButtonOptions.Label_ = 'Aktualisieren'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = IWCGJQButton1JQButtonOptionsClick
        end
      end
      object iwrEigeneNachrichten: TIWCGJQRegion
        Left = 0
        Top = 50
        Width = 0
        Height = 549
        RenderInvisibleControls = True
        TabOrder = 17
        Version = '1.0'
        Align = alClient
        object jqrlEigeneNachrichten: TIWCGJQResponsiveList
          Left = 0
          Top = 0
          Width = 0
          Height = 549
          TabOrder = 6
          Version = '1.0'
          Align = alClient
          CGScrollStyle = cgsbsVerticalAuto
          JQResponsiveListOptions.ItemWidth = 382
          JQResponsiveListOptions.ItemHeight = 482
          JQResponsiveListOptions.Padding = 5
        end
      end
    end
    object TabNeueNachricht: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Neue Nachricht'
      TabIndex = 2
      Tabs = TabsNachrichten
      object iwrNeueNachricht: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 0
        Height = 0
        RenderInvisibleControls = True
        TabOrder = 18
        Version = '1.0'
        Align = alClient
        object IWLabel1: TIWCGJQLabel
          Left = 42
          Top = 6
          Width = 73
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Empf'#228'nger:'
        end
        object iwlNachricht: TIWCGJQLabel
          Left = 42
          Top = 68
          Width = 63
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwlNachricht'
          Caption = 'Nachricht:'
        end
        object iwmNachrichtentext: TIWMemo
          Left = 42
          Top = 90
          Width = 601
          Height = 193
          StyleRenderOptions.RenderBorder = False
          BGColor = clNone
          Editable = True
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          InvisibleBorder = False
          HorizScrollBar = False
          VertScrollBar = True
          Required = False
          TabOrder = 5
          SubmitOnAsyncEvent = True
          ResizeDirection = rdNone
          FriendlyName = 'iwmNachrichtentext'
          OnAsyncKeyDown = iwmNachrichtentextAsyncKeyDown
        end
        object iwlZeichen: TIWCGJQLabel
          Left = 42
          Top = 289
          Width = 392
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'iwlZeichen'
          Caption = 'Anzahl Zeichen: 0/1000'
        end
        object IWLabel7: TIWCGJQLabel
          Left = 42
          Top = 311
          Width = 86
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Externer Link:'
        end
        object jqbNachrichtVersenden: TIWCGJQButton
          Left = 440
          Top = 296
          Width = 200
          Height = 36
          TabOrder = 5
          Version = '1.0'
          JQButtonOptions.Label_ = 'Nachricht versenden'
          JQButtonOptions.OnClick.OnEvent = jqbNachrichtVersendenOnClick
        end
        object jqclGruppen: TIWCGJQCheckBoxList
          Left = 42
          Top = 28
          Width = 598
          Height = 26
          TabOrder = 8
          Version = '1.0'
          StyleRenderOptions.RenderVisibility = True
          StyleRenderOptions.RenderAbsolute = False
          StyleRenderOptions.RenderBorder = False
          StyleRenderOptions.UseDisplay = True
          ZIndex = 10000
          JQCheckBoxListOptions.MinWidth = 598
          JQCheckBoxListOptions.CheckAllText = 'Alle Gruppen ausw'#228'hlen'
          JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
          JQCheckBoxListOptions.NoneSelectedText = 'Gruppen ausw'#228'hlen'
          JQCheckBoxListOptions.SelectedText = '# Gruppen ausgew'#228'hlt'
          JQCheckBoxListOptions.OnUncheckall.OnEvent = JqclGruppenOnUncheckAll
          JQCheckBoxListOptions.OnClick.OnEvent = JqclGruppenOnClick
          JQCheckBoxListOptions.MenuZIndex = 10000
          Items = <>
          Groups = <>
        end
        object jqeExternerLink: TIWCGJQEdit
          Left = 134
          Top = 311
          Width = 300
          Height = 21
          TabOrder = 7
          Version = '1.0'
          ScriptEvents = <>
          Text = ''
        end
      end
    end
  end
  object ProviderNachrichten: TIWCGJQGridDataSetProvider
    DataSet = DMNachrichten.QNachrichten
    KeyFields = 'ID;ID_GRUPPE'
    Left = 957
    Top = 11
  end
  object dsAutocompleteUsername: TDataSource
    DataSet = dm_main.quAutocompleteUsername
    Left = 832
    Top = 8
  end
  object jqsAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 472
    Top = 24
  end
  object AlertNachrichtVersenden: TIWCGJQSweetAlert
    Version = '1.0'
    JQSweetAlertOptions.Title = 'Best'#228'tigung'
    JQSweetAlertOptions.Text = 'Wollen Sie die Nachricht wirklich versenden?'
    JQSweetAlertOptions.AlertType = jqsatInfo
    JQSweetAlertOptions.AllowEscapeKey = False
    JQSweetAlertOptions.ShowCancelButton = True
    JQSweetAlertOptions.CancelButtonText = 'Abbrechen'
    JQSweetAlertOptions.OnBtnClick.OnEvent = AlertNachrichtVersendenJQSweetAlertOptionsBtnClick
    Left = 640
    Top = 24
  end
end
