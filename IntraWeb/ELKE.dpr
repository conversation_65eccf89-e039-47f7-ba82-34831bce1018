﻿program ELKE;
uses
  FastMM4,
  madExcept,
  madLinkDisAsm,
  madListHardware,
  madListProcesses,
  madListModules,
  IWRtlFix,
  Forms,
  iwjcldebug,
  IWJclStackTrace,
  UTF8ContentParser,
  IWCGLicenseKey,
  IWStartHSys,
  IWCGJQCommon,
  IWUtils,
  DX.Classes.Service,
  WinAPI.Windows,
  ServerController in 'ServerController.pas' {IWServerController: TIWServerControllerBase},
  Forms.Main in 'Forms.Main.pas' {FormMain: TIWAppForm},
  UserSessionUnit in 'UserSessionUnit.pas' {IWUserSession: TIWUserSessionBase},
  dmmain in 'dmmain.pas' {dm_main: TDataModule},
  Forms.Administrator in 'Adminfenster\Forms.Administrator.pas' {FormAdministrator: TIWAppForm},
  Forms.VisDaten in 'Forms.VisDaten.pas' {FormVisDaten: TIWAppForm},
  Forms.Stammdaten in 'Stammdaten\Forms.Stammdaten.pas' {FormStammdaten: TIWAppForm},
  Fragenfenster in 'Fragenfenster.pas' {FrmFragenfenster: TIWAppForm},
  BuntelisteU in 'BuntelisteU.pas' {FrmBunteliste: TIWAppForm},
  Willkommensfenster in 'Willkommensfenster.pas' {FrmWillkommen: TIWAppForm},
  DetailFenster in 'Kontrollform\DetailFenster.pas' {DetailFrm: TIWAppForm},
  Forms.Nachrichten in 'Nachrichten\Forms.Nachrichten.pas' {FormNachrichten: TIWAppForm},
  Forms.VersionInfo in 'Forms.VersionInfo.pas' {FormVersionInfo: TIWAppForm},
  Forms.Base in 'Forms.Base.pas' {FormBase: TIWAppForm},
  Teamfenster in 'Teamfenster.pas' {TeamFensterForm: TIWAppForm},
  ELKE.Classes.REST.Client in '..\ESCore\classes\ELKE.Classes.REST.Client.pas',
  Config in 'Config.pas',
  Forms.Logs in 'Forms.Logs.pas' {FormLogs: TIWAppForm},
  Nachricht in 'Nachrichten\Nachricht.pas' {NachrichtFrame: TIWCGJQFrame},
  RollenBkbtypFrame in 'Adminfenster\RollenBkbtypFrame.pas' {RollenBkbtyp: TFrame},
  Utility in 'Utility.pas',
  PersonenprefixFrame in 'Adminfenster\PersonenprefixFrame.pas' {Personenpraefix: TFrame},
  BundeslaenderFrame in 'Adminfenster\BundeslaenderFrame.pas' {Bundeslaender: TFrame},
  GemeindenFrame in 'Adminfenster\GemeindenFrame.pas' {Gemeinden: TFrame},
  RollenFrame in 'Adminfenster\RollenFrame.pas' {Rollen: TFrame},
  KontrolltypenFrame in 'Adminfenster\KontrolltypenFrame.pas' {Kontrolltypen: TFrame},
  CRUDGridFrame in 'CRUDGridFrame.pas' {CRUDGrid: TFrame},
  BkbtypenFrame in 'Adminfenster\BkbtypenFrame.pas' {Bkbtypen: TFrame},
  Funktionen in 'Funktionen.pas',
  KommunikationsartenFrame in 'Adminfenster\KommunikationsartenFrame.pas' {Kommunikationsarten: TFrame},
  Forms.Kontrollen in 'Kontrollform\Forms.Kontrollen.pas' {FormKontrollen: TIWAppForm},
  Frames.Kontrollverlauf in 'Kontrollform\Frames.Kontrollverlauf.pas' {FrameKontrollverlauf: TFrame},
  KontrolleErstellenFrame in 'Kontrollform\KontrolleErstellenFrame.pas' {KontrolleErstellen: TFrame},
  Frames.UngeplanteKontrolle in 'Kontrollform\Frames.UngeplanteKontrolle.pas' {FrameUngeplanteKontrolle: TFrame},
  KommunikationswegeFrame in 'Adminfenster\KommunikationswegeFrame.pas' {Kommunikationswege: TFrame},
  BetriebeKommunikationswegeFrame in 'Adminfenster\BetriebeKommunikationswegeFrame.pas' {BetriebeKommunikationswege: TFrame},
  Forms.AMACCK in 'AMA\Forms.AMACCK.pas' {FormAMACCK: TIWAppForm},
  AMACCKImportFrame in 'AMA\AMACCKImportFrame.pas' {AMACCKImport: TIWCGJQFrame},
  VisOeOxImportFrame in 'AMA\VisOeOxImportFrame.pas' {VisOExImport: TIWCGJQFrame},
  AMAClasses in 'AMA\AMAClasses.pas',
  AMACCKUebersichtFrame in 'AMA\AMACCKUebersichtFrame.pas' {AMACCKUebersicht: TFrame},
  KontrollbereicheFrame in 'Fragen\KontrollbereicheFrame.pas' {Kontrollbereiche: TFrame},
  MassnahmenkatalogFrame in 'Fragen\MassnahmenkatalogFrame.pas' {Massnahmenkatalog: TFrame},
  MassnahmenFrame in 'Fragen\MassnahmenFrame.pas' {Massnahmen: TFrame},
  MangeltypenFrame in 'Fragen\MangeltypenFrame.pas' {Mangeltypen: TFrame},
  MangelStatusFrame in 'Fragen\MangelStatusFrame.pas' {MangelStatus: TFrame},
  Frames.Checklisten in 'Fragen\Frames.Checklisten.pas' {FrameChecklisten: TFrame},
  Dialogs.FrageBewertungenBearbeiten in 'Fragen\Dialogs.FrageBewertungenBearbeiten.pas' {DialogFrBewEdit: TFrame},
  AgesXmlFrame in 'Kontrollform\AgesXmlFrame.pas' {AgesXML: TFrame},
  TierartenFrame in 'Stammdaten\TierartenFrame.pas' {Tierarten: TFrame},
  CSVImportFrame in 'Kontrollform\CSVImportFrame.pas' {CSVImport: TFrame},
  VisOExCSVImportFrame in 'Kontrollform\VisOExCSVImportFrame.pas' {VisOExCSVImport: TFrame},
  GruppenFrame in 'Adminfenster\GruppenFrame.pas' {GruppenF: TFrame},
  Modules.Kontrolle in 'Kontrollform\Modules.Kontrolle.pas' {DMKontrolle: TDataModule},
  Dialogs.Base in '..\ESCore\Intraweb\Dialogs.Base.pas' {DialogBase: TIWCGJQFrame},
  Dialogs.Kontrolldetails in 'Kontrollform\Dialogs.Kontrolldetails.pas' {DialogKontrollDetails: TIWCGJQFrame},
  Dialogs.ProbeErstellen in 'Kontrollform\Dialogs.ProbeErstellen.pas' {ProbeErstellen: TIWCGJQFrame},
  Frames.GeplanteKontrollen in 'Kontrollform\Frames.GeplanteKontrollen.pas' {FrameGeplanteKontrollen: TFrame},
  Dialogs.Betriebdetails in 'Kontrollform\Dialogs.Betriebdetails.pas' {DialogBetriebdetails: TIWCGJQFrame},
  Dialogs.Anwesende in 'Kontrollform\Dialogs.Anwesende.pas' {DialogAnwesende: TIWCGJQFrame},
  Frames.UngeplanteKontrollen in 'Kontrollform\Frames.UngeplanteKontrollen.pas' {UngeplanteKontrollen: TFrame},
  Dialogs.Stornierungsgrund in 'Kontrollform\Dialogs.Stornierungsgrund.pas' {DialogStornierungsgrund: TIWCGJQFrame},
  Dialogs.KontrolleWeitergeben in 'Kontrollform\Dialogs.KontrolleWeitergeben.pas' {DialogKontrolleWeitergeben: TIWCGJQFrame},
  Forms.Fragenkatalog in 'Fragen\Forms.Fragenkatalog.pas' {FormFragenkatalog: TIWAppForm},
  BewertungenFrame in 'Fragen\BewertungenFrame.pas' {Bewertungen: TIWCGJQFrame},
  Classes.Fragen in 'Fragen\Classes.Fragen.pas',
  Dialogs.FrageBearbeiten in 'Fragen\Dialogs.FrageBearbeiten.pas' {DialogFrageBearbeiten: TIWCGJQFrame},
  Dialogs.FrageVerschieben in 'Fragen\Dialogs.FrageVerschieben.pas',
  FormatierungenFrame in 'Fragen\FormatierungenFrame.pas' {Formatierungen: TIWCGJQFrame},
  Forms.ChecklisteDetails in 'Fragen\Forms.ChecklisteDetails.pas' {ChecklistenDetails: TIWAppForm},
  Modules.Checklisten in 'Fragen\Modules.Checklisten.pas' {DMChecklisten: TDataModule},
  Dialogs.FristVerlaengern in 'Kontrollform\Dialogs.FristVerlaengern.pas' {DialogFristVerlaengern: TIWCGJQFrame},
  Dialogs.GeplanteKontrolleBearbeiten in 'Kontrollform\Dialogs.GeplanteKontrolleBearbeiten.pas' {DialogGeplanteKontrolleBearbeiten: TIWCGJQFrame},
  Frames.GruppenAdmin in 'Adminfenster\Frames.GruppenAdmin.pas' {GruppenAdmin: TIWCGJQFrame},
  Modules.Admin in 'Adminfenster\Modules.Admin.pas' {DMAdmin: TDataModule},
  Dialogs.GruppeHinzufuegen in 'Adminfenster\Dialogs.GruppeHinzufuegen.pas' {DialogGruppeHinzufuegen: TIWCGJQFrame},
  Frames.UserGruppenAdmin in 'Adminfenster\Frames.UserGruppenAdmin.pas' {UsergruppenAdmin: TIWCGJQFrame},
  Modules.Nachrichten in 'Nachrichten\Modules.Nachrichten.pas' {DMNachrichten: TDataModule},
  Frames.Base in '..\ESCore\Intraweb\Frames.Base.pas' {FrameBase: TIWCGJQFrame},
  Modules.Stammdaten in 'Stammdaten\Modules.Stammdaten.pas' {DMStammdaten: TDataModule},
  JQ.Helpers.TreeView in '..\ESCore\Intraweb\JQ.Helpers.TreeView.pas',
  JQ.Helpers.SessionProperties in '..\ESCore\Intraweb\JQ.Helpers.SessionProperties.pas',
  JQ.Helpers.Grid in '..\ESCore\Intraweb\JQ.Helpers.Grid.pas',
  Modules.AMA in 'AMA\Modules.AMA.pas' {DMAma: TDataModule},
  JQ.Helpers.Button in '..\ESCore\Intraweb\JQ.Helpers.Button.pas',
  Forms.UngeplanteKontrolle in 'Kontrollform\Forms.UngeplanteKontrolle.pas' {FormUngeplanteKontrolle: TIWAppForm},
  Dialogs.MangelAbschliessen in 'Kontrollform\Dialogs.MangelAbschliessen.pas' {DialogMangelAbschliessen: TIWCGJQFrame},
  Dialogs.UngeplanteKontrolleZuweisen in 'Kontrollform\Dialogs.UngeplanteKontrolleZuweisen.pas' {DialogUngeplanteKontrolleZuweisen: TIWCGJQFrame},
  Forms.Base.Core in '..\ESCore\Intraweb\Forms.Base.Core.pas' {FormBaseCore: TIWAppForm},
  JQ.Helpers.FormatSettings in '..\ESCore\Intraweb\JQ.Helpers.FormatSettings.pas',
  Dialogs.AMAAuftragDetail in 'AMA\Dialogs.AMAAuftragDetail.pas' {DialogAMAAuftragsDetails: TIWCGJQFrame},
  Frames.BetriebsdatenDetails in 'AMA\Frames.BetriebsdatenDetails.pas' {FrameBetriebsdatenDetails: TIWAppForm},
  Frames.Auswahldaten.Module in 'AMA\Frames.Auswahldaten.Module.pas' {FrameAuswahldatenModule: TIWCGJQFrame},
  Frames.Auswahldaten.Sanktionen in 'AMA\Frames.Auswahldaten.Sanktionen.pas' {FrameAuswahldatenSanktionen: TIWCGJQFrame},
  Frames.Auswahldaten.Tiere in 'AMA\Frames.Auswahldaten.Tiere.pas' {FrameAuswahldatenTiere: TIWCGJQFrame},
  JQ.Helpers.ResponsiveList in '..\ESCore\Intraweb\JQ.Helpers.ResponsiveList.pas',
  Frames.DatasetDetails.Base in '..\ESCore\Intraweb\Frames.DatasetDetails.Base.pas' {FrameDatasetDetailBase: TIWCGJQFrame},
  Dialogs.MassnahmenBilder in 'Kontrollform\Dialogs.MassnahmenBilder.pas' {DialogMassnahmenBilder: TIWCGJQFrame},
  Dialogs.AMAAuftragAbschliessen in 'AMA\Dialogs.AMAAuftragAbschliessen.pas' {DialogAMAAuftragAbschliessen: TIWCGJQFrame},
  Dialogs.UngeplanteKontrolleEditNeu in 'Kontrollform\Dialogs.UngeplanteKontrolleEditNeu.pas' {UngeplanteKontrolleEditNeu: TIWCGJQFrame},
  Dialogs.UngeplanteKontrolleBetrieb in 'Kontrollform\Dialogs.UngeplanteKontrolleBetrieb.pas' {DialogUngeplanteKontrolleBetrieb: TIWCGJQFrame},
  Dialogs.Pdf in 'AMA\Dialogs.Pdf.pas' {DialogPdf: TIWCGJQFrame},
  JQ.Helpers.Alert in '..\ESCore\Intraweb\JQ.Helpers.Alert.pas',
  Frames.KontrolleZuordnen in 'AMA\Frames.KontrolleZuordnen.pas' {DialogKontrolleZuordnen: TIWCGJQFrame},
  ELKE.Classes.RESTError in '..\ESCore\classes\ELKE.Classes.RESTError.pas',
  ELKE.Classes.Admin in '..\ESCore\classes\ELKE.Classes.Admin.pas',
  ELKE.Classes.Generated.Dictionary in '..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas',
  ELKE.Classes.Generated in '..\ESCore\classes\ELKE.Classes.Generated.pas',
  ELKE.Classes.Logging in '..\ESCore\classes\ELKE.Classes.Logging.pas',
  ELKE.Classes in '..\ESCore\classes\ELKE.Classes.pas',
  ELKE.Classes.PVP.Roles in '..\ESCore\classes\ELKE.Classes.PVP.Roles.pas',
  ELKE.Classes.PVP.Token in '..\ESCore\classes\ELKE.Classes.PVP.Token.pas',
  ELKE.Classes.Request in '..\ESCore\classes\ELKE.Classes.Request.pas',
  ELKE.Services.Admin.Intf in '..\ESCore\classes\ELKE.Services.Admin.Intf.pas',
  ELKE.Services.Me.Intf in '..\ESCore\classes\ELKE.Services.Me.Intf.pas',
  ELKE.Sparkle.Middleware.ReverseProxy in '..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas',
  ELKE.Server.Logger in '..\ESCore\classes\ELKE.Server.Logger.pas',
  ELKE.Server.Configuration.Base in '..\ESCore\classes\ELKE.Server.Configuration.Base.pas',
  ELKE.Sparkle.Middleware.PVPAuth in '..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas',
  Dialogs.ChecklisteKontrolltypenZuordnen in 'Fragen\Dialogs.ChecklisteKontrolltypenZuordnen.pas' {DialogChecklisteKontrolltypenZuordnen: TIWCGJQFrame},
  Dialogs.GeplanteKontrolleEditNeu in 'Kontrollform\Dialogs.GeplanteKontrolleEditNeu.pas' {DialogGeplanteKontrolleEditNeu: TIWCGJQFrame},
  Frames.Kontrolltypen in 'Fragen\Frames.Kontrolltypen.pas' {FrameKontrolltypen: TIWCGJQFrame},
  JQ.Helpers.ComboboxEx in '..\ESCore\Intraweb\JQ.Helpers.ComboboxEx.pas',
  Dialogs.ChecklistenVerwendung in 'Fragen\Dialogs.ChecklistenVerwendung.pas' {DialogChecklistenVerwendung: TIWCGJQFrame},
  ELKE.Sparkle.Middleware.TokenAuthGenEndpoints in '..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas',
  Dialogs.StatusMeldungen in 'AMA\Dialogs.StatusMeldungen.pas' {DialogStatusmeldungen: TIWCGJQFrame},
  Betrieb.Form in 'Betrieb\Betrieb.Form.pas',
  Betrieb.Module in 'Betrieb\Betrieb.Module.pas',
  Betrieb.RevStamm.Frame in 'Betrieb\Betrieb.RevStamm.Frame.pas',
  Betrieb.Monitoring.Form in 'Betrieb\Betrieb.Monitoring.Form.pas' {FormBetriebsMonitoring: TIWAppForm};

{$R *.res}

// See:
// WinAPI.Windows.pas
// http://docwiki.embarcadero.com/RADStudio/Sydney/en/PE_(portable_executable)_header_flags_(Delphi)
// https://blog.dummzeuch.de/2017/11/02/using-pe-flags-in-delphi/

{$SETPEFLAGS IMAGE_FILE_LARGE_ADDRESS_AWARE}

begin
  //CGDevTools und IW Versionsprüfung
  //Neue Versionskombinationen werden nur nach ausdrücklicher Prüfung einkompiliert!
  Assert(IWCGJQVersion = '4.1.0.263', 'Falsche CGDevTools Version');
  Assert(GVersion = '15.2.21', 'Falsche Intraweb Version');

  //ReportMemoryLeaksOnShutDown := DebugHook <> 0;
  //Memory Leaks werden per madExcept geprüft. Siehe define "leakchecking" im Projekt

  TIWStartHsys.Execute(not TServiceBase.IsService);
  if TServiceBase.IsServiceInstalling then
    TServiceBase.RegisterService;

end.
