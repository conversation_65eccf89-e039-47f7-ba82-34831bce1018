﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Console</AppType>
        <Config Condition="'$(Config)'==''">Olaf</Config>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>ELKE.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{EEC64A79-1834-423B-92C9-572064DCE04E}</ProjectGuid>
        <ProjectName Condition="'$(ProjectName)'==''">ELKE</ProjectName>
        <ProjectVersion>20.2</ProjectVersion>
        <TargetedPlatforms>1</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Olaf NOE' or '$(Cfg_3)'!=''">
        <Cfg_3>true</Cfg_3>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_3)'=='true') or '$(Cfg_3_Win32)'!=''">
        <Cfg_3_Win32>true</Cfg_3_Win32>
        <CfgParent>Cfg_3</CfgParent>
        <Cfg_3>true</Cfg_3>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Olaf OOE' or '$(Cfg_4)'!=''">
        <Cfg_4>true</Cfg_4>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Florian NOE' or '$(Cfg_6)'!=''">
        <Cfg_6>true</Cfg_6>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>ELKE</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)\dcu</DCC_DcuOutput>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_SYMBOL_PLATFORM>false</DCC_SYMBOL_PLATFORM>
        <DCC_UNIT_PLATFORM>false</DCC_UNIT_PLATFORM>
        <DCC_UnitSearchPath>..\ESCore;..\ESCore\IntraWeb;..\_libs\dx-library;..\_libs\TMSAurelius\source;..\_libs\TMSAurelius\source\core;..\_libs\TMSAurelius\source\drivers;..\_libs\TMSBCL\source;..\_libs\TMSBCL\source\core;..\_libs\TMSBCL\source\core\common;..\_libs\TMSBCL\source\extra;..\_libs\TMSXData\source;..\_libs\TMSXData\source\core;..\_libs\TMSXData\source\core\common;..\_libs\TMSSparkle\source;..\_libs\TMSSparkle\source\core;..\_libs\TMSSparkle\source\core\common;..\_libs\TMSSparkle\source\extra;..\_libs\TMSSparkle\source\app;..\_libs\FastReport\FastCore\VCL\Sources;..\_libs\FastReport\FastGraphics\VCL\Sources;..\_libs\FastReport\FastLocalization\VCL\Sources;..\_libs\FastReport\FastQueryBuilder\VCL\Sources;..\_libs\FastReport\FastReport\VCL\Sources;..\_libs\FastReport\FastScript\VCL\Sources;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <Icns_MainIcns>$(BDS)\bin\delphi_PROJECTICNS.icns</Icns_MainIcns>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <ImageDebugInfo>true</ImageDebugInfo>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=EsCulenta GmbH;FileVersion=********;InternalName=$(MSBuildProjectName);OriginalFilename=ELKE.exe;ProductVersion=1.64.2;ProgramID=at.esculenta.$(MSBuildProjectName);FileDescription=Implementiert eine auf http.sys basierenden Web Administration für ELKE;ProductName=ELKE Intraweb;LegalCopyright=2020 - 2025 © EsCulenta GmbH</VerInfo_Keys>
        <VerInfo_Locale>3079</VerInfo_Locale>
        <VerInfo_MinorVer>64</VerInfo_MinorVer>
        <VerInfo_Release>2</VerInfo_Release>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>FireDACSqliteDriver;FireDACDSDriver;DBXSqliteDriver;FireDACPgDriver;fmx;IndySystem;TeeDB;tethering;inetdbbde;vclib;DBXInterBaseDriver;DataSnapClient;DataSnapServer;DataSnapCommon;DataSnapProviderClient;DBXSybaseASEDriver;DbxCommonDriver;vclimg;dbxcds;DatasnapConnectorsFreePascal;MetropolisUILiveTile;vcldb;vcldsnap;fmxFireDAC;DBXDb2Driver;DBXOracleDriver;CustomIPTransport;vclribbon;dsnap;IndyIPServer;fmxase;vcl;IndyCore;DBXMSSQLDriver;IndyIPCommon;CloudService;FmxTeeUI;FireDACIBDriver;CodeSiteExpressPkg;DataSnapFireDAC;FireDACDBXDriver;soapserver;inetdbxpress;dsnapxml;FireDACInfxDriver;FireDACDb2Driver;adortl;FireDACASADriver;bindcompfmx;vcldbx;FireDACODBCDriver;RESTBackendComponents;rtl;dbrtl;DbxClientDriver;FireDACCommon;bindcomp;inetdb;Tee;DBXOdbcDriver;vclFireDAC;xmlrtl;DataSnapNativeClient;svnui;ibxpress;IndyProtocols;DBXMySQLDriver;FireDACCommonDriver;bindengine;vclactnband;bindcompdbx;soaprtl;FMXTee;TeeUI;bindcompvcl;Intraweb_14_DXE6;vclie;FireDACADSDriver;vcltouch;VclSmp;FireDACMSSQLDriver;FireDAC;DBXInformixDriver;VCLRESTComponents;DataSnapConnectors;DataSnapServerMidas;dclCrossTalk_XE6;dsnapcon;DBXFirebirdDriver;inet;fmxobj;FireDACMySQLDriver;soapmidas;vclx;svn;DBXSybaseASADriver;FireDACOracleDriver;fmxdae;RESTComponents;bdertl;FireDACMSAccDriver;dbexpress;DataSnapIndy10ServerTransport;IndyIPClient;rbRIDE1926;rbIDE1926;rbRAP1926;rbRCL1926;dclRBADO1926;rbADO1926;rbDB1926;rbDAD1926;rbTCUI1926;rbTC1926;dclRBDBE1926;rbDBE1926;dclRBE1926;rbCIDE1926;rbBDE1926;dclRBFireDAC1926;rbFireDAC1926;dclRBIBE1926;rbIBE1926;rbUSERDesign1926;rbUSER1926;rbRTL1926;rbRest1926;$(DCC_UsePackage)</DCC_UsePackage>
        <Icon_MainIcon>APP Icon.ico</Icon_MainIcon>
        <Manifest_File>(Ohne)</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DcuOutput>.\$(Platform)\DEBUG\dcu</DCC_DcuOutput>
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>.\$(Platform)\DEBUG</DCC_ExeOutput>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_RangeChecking>true</DCC_RangeChecking>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
        <VerInfo_Debug>true</VerInfo_Debug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_Define>madExcept;$(DCC_Define)</DCC_Define>
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
        <DCC_SYMBOL_PLATFORM>false</DCC_SYMBOL_PLATFORM>
        <DCC_UNIT_PLATFORM>false</DCC_UNIT_PLATFORM>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_3)'!=''">
        <DCC_Define>OM;$(DCC_Define)</DCC_Define>
        <DCC_UnitSearchPath>C:\Projekte\Atozed\IntraWeb15\FullSource\common;C:\Projekte\Atozed\IntraWeb15\FullSource\core;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_3_Win32)'!=''">
        <DCC_MapFile>3</DCC_MapFile>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_4)'!=''">
        <DCC_Define>OOE;$(DCC_Define)</DCC_Define>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_6)'!=''">
        <DCC_Define>FF;$(DCC_Define)</DCC_Define>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DcuOutput>.\$(Platform)\RELEASE\dcu</DCC_DcuOutput>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>..\_Release-Binaries\$(Platform)\</DCC_ExeOutput>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <DCC_Define>madExcept;$(DCC_Define)</DCC_Define>
        <DCC_MapFile>3</DCC_MapFile>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="ServerController.pas">
            <Form>IWServerController</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWServerControllerBase</DesignClass>
        </DCCReference>
        <DCCReference Include="Forms.Main.pas">
            <Form>FormMain</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="UserSessionUnit.pas">
            <Form>IWUserSession</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWUserSessionBase</DesignClass>
        </DCCReference>
        <DCCReference Include="dmmain.pas">
            <Form>dm_main</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\Forms.Administrator.pas">
            <Form>FormAdministrator</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Forms.VisDaten.pas">
            <Form>FormVisDaten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Stammdaten\Forms.Stammdaten.pas">
            <Form>FormStammdaten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragenfenster.pas">
            <Form>FrmFragenfenster</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="BuntelisteU.pas">
            <Form>FrmBunteliste</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Willkommensfenster.pas">
            <Form>FrmWillkommen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\DetailFenster.pas">
            <Form>DetailFrm</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Nachrichten\Forms.Nachrichten.pas">
            <Form>FormNachrichten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Forms.VersionInfo.pas">
            <Form>FormVersionInfo</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Forms.Base.pas">
            <Form>FormBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Teamfenster.pas">
            <Form>TeamFensterForm</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.REST.Client.pas"/>
        <DCCReference Include="Config.pas"/>
        <DCCReference Include="Forms.Logs.pas">
            <Form>FormLogs</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Nachrichten\Nachricht.pas">
            <Form>NachrichtFrame</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\RollenBkbtypFrame.pas">
            <Form>RollenBkbtyp</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Utility.pas"/>
        <DCCReference Include="Adminfenster\PersonenprefixFrame.pas">
            <Form>Personenpraefix</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\BundeslaenderFrame.pas">
            <Form>Bundeslaender</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\GemeindenFrame.pas">
            <Form>Gemeinden</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\RollenFrame.pas">
            <Form>Rollen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\KontrolltypenFrame.pas">
            <Form>Kontrolltypen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="CRUDGridFrame.pas">
            <Form>CRUDGrid</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\BkbtypenFrame.pas">
            <Form>Bkbtypen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Funktionen.pas"/>
        <DCCReference Include="Adminfenster\KommunikationsartenFrame.pas">
            <Form>Kommunikationsarten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Forms.Kontrollen.pas">
            <Form>FormKontrollen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Frames.Kontrollverlauf.pas">
            <Form>FrameKontrollverlauf</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\KontrolleErstellenFrame.pas">
            <Form>KontrolleErstellen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Frames.UngeplanteKontrolle.pas">
            <Form>FrameUngeplanteKontrolle</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\KommunikationswegeFrame.pas">
            <Form>Kommunikationswege</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\BetriebeKommunikationswegeFrame.pas">
            <Form>BetriebeKommunikationswege</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Forms.AMACCK.pas">
            <Form>FormAMACCK</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\AMACCKImportFrame.pas">
            <Form>AMACCKImport</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\VisOeOxImportFrame.pas">
            <Form>VisOExImport</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\AMAClasses.pas"/>
        <DCCReference Include="AMA\AMACCKUebersichtFrame.pas">
            <Form>AMACCKUebersicht</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\KontrollbereicheFrame.pas">
            <Form>Kontrollbereiche</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\MassnahmenkatalogFrame.pas">
            <Form>Massnahmenkatalog</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\MassnahmenFrame.pas">
            <Form>Massnahmen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\MangeltypenFrame.pas">
            <Form>Mangeltypen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\MangelStatusFrame.pas">
            <Form>MangelStatus</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Frames.Checklisten.pas">
            <Form>FrameChecklisten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Dialogs.FrageBewertungenBearbeiten.pas">
            <Form>DialogFrBewEdit</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\AgesXmlFrame.pas">
            <Form>AgesXML</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Stammdaten\TierartenFrame.pas">
            <Form>Tierarten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\CSVImportFrame.pas">
            <Form>CSVImport</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\VisOExCSVImportFrame.pas">
            <Form>VisOExCSVImport</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\GruppenFrame.pas">
            <Form>GruppenF</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Modules.Kontrolle.pas">
            <Form>DMKontrolle</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\Dialogs.Base.pas">
            <Form>DialogBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.Kontrolldetails.pas">
            <Form>DialogKontrollDetails</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.ProbeErstellen.pas">
            <Form>ProbeErstellen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Frames.GeplanteKontrollen.pas">
            <Form>FrameGeplanteKontrollen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.Betriebdetails.pas">
            <Form>DialogBetriebdetails</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.Anwesende.pas">
            <Form>DialogAnwesende</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Frames.UngeplanteKontrollen.pas">
            <Form>UngeplanteKontrollen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.Stornierungsgrund.pas">
            <Form>DialogStornierungsgrund</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.KontrolleWeitergeben.pas">
            <Form>DialogKontrolleWeitergeben</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Forms.Fragenkatalog.pas">
            <Form>FormFragenkatalog</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\BewertungenFrame.pas">
            <Form>Bewertungen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Classes.Fragen.pas"/>
        <DCCReference Include="Fragen\Dialogs.FrageBearbeiten.pas">
            <Form>DialogFrageBearbeiten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Dialogs.FrageVerschieben.pas"/>
        <DCCReference Include="Fragen\FormatierungenFrame.pas">
            <Form>Formatierungen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Forms.ChecklisteDetails.pas">
            <Form>ChecklistenDetails</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Modules.Checklisten.pas">
            <Form>DMChecklisten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.FristVerlaengern.pas">
            <Form>DialogFristVerlaengern</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.GeplanteKontrolleBearbeiten.pas">
            <Form>DialogGeplanteKontrolleBearbeiten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\Frames.GruppenAdmin.pas">
            <Form>GruppenAdmin</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\Modules.Admin.pas">
            <Form>DMAdmin</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\Dialogs.GruppeHinzufuegen.pas">
            <Form>DialogGruppeHinzufuegen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Adminfenster\Frames.UserGruppenAdmin.pas">
            <Form>UsergruppenAdmin</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Nachrichten\Modules.Nachrichten.pas">
            <Form>DMNachrichten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\Frames.Base.pas">
            <Form>FrameBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Stammdaten\Modules.Stammdaten.pas">
            <Form>DMStammdaten</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.TreeView.pas"/>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.SessionProperties.pas"/>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.Grid.pas"/>
        <DCCReference Include="AMA\Modules.AMA.pas">
            <Form>DMAma</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.Button.pas"/>
        <DCCReference Include="Kontrollform\Forms.UngeplanteKontrolle.pas">
            <Form>FormUngeplanteKontrolle</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.MangelAbschliessen.pas">
            <Form>DialogMangelAbschliessen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.UngeplanteKontrolleZuweisen.pas">
            <Form>DialogUngeplanteKontrolleZuweisen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\Forms.Base.Core.pas">
            <Form>FormBaseCore</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.FormatSettings.pas"/>
        <DCCReference Include="AMA\Dialogs.AMAAuftragDetail.pas">
            <Form>DialogAMAAuftragsDetails</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Frames.BetriebsdatenDetails.pas">
            <Form>FrameBetriebsdatenDetails</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Frames.Auswahldaten.Module.pas">
            <Form>FrameAuswahldatenModule</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Frames.Auswahldaten.Sanktionen.pas">
            <Form>FrameAuswahldatenSanktionen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Frames.Auswahldaten.Tiere.pas">
            <Form>FrameAuswahldatenTiere</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.ResponsiveList.pas"/>
        <DCCReference Include="..\ESCore\Intraweb\Frames.DatasetDetails.Base.pas">
            <Form>FrameDatasetDetailBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.MassnahmenBilder.pas">
            <Form>DialogMassnahmenBilder</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Dialogs.AMAAuftragAbschliessen.pas">
            <Form>DialogAMAAuftragAbschliessen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.UngeplanteKontrolleEditNeu.pas">
            <Form>UngeplanteKontrolleEditNeu</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.UngeplanteKontrolleBetrieb.pas">
            <Form>DialogUngeplanteKontrolleBetrieb</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA\Dialogs.Pdf.pas">
            <Form>DialogPdf</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.Alert.pas"/>
        <DCCReference Include="AMA\Frames.KontrolleZuordnen.pas">
            <Form>DialogKontrolleZuordnen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.RESTError.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.Admin.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.Generated.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.Logging.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.PVP.Roles.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.PVP.Token.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Classes.Request.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Services.Admin.Intf.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Services.Me.Intf.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Server.Logger.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Server.Configuration.Base.pas"/>
        <DCCReference Include="..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas"/>
        <DCCReference Include="Fragen\Dialogs.ChecklisteKontrolltypenZuordnen.pas">
            <Form>DialogChecklisteKontrolltypenZuordnen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Kontrollform\Dialogs.GeplanteKontrolleEditNeu.pas">
            <Form>DialogGeplanteKontrolleEditNeu</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Fragen\Frames.Kontrolltypen.pas">
            <Form>FrameKontrolltypen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\Intraweb\JQ.Helpers.ComboboxEx.pas"/>
        <DCCReference Include="Fragen\Dialogs.ChecklistenVerwendung.pas">
            <Form>DialogChecklistenVerwendung</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas"/>
        <DCCReference Include="AMA\Dialogs.StatusMeldungen.pas">
            <Form>DialogStatusmeldungen</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWCGJQFrame</DesignClass>
        </DCCReference>
        <DCCReference Include="Betrieb\Betrieb.Form.pas"/>
        <DCCReference Include="Betrieb\Betrieb.Module.pas"/>
        <DCCReference Include="Betrieb\Betrieb.RevStamm.Frame.pas"/>
        <DCCReference Include="Betrieb\Betrieb.Monitoring.Form.pas">
            <Form>FormBetriebsMonitoring</Form>
            <FormType>dfm</FormType>
            <DesignClass>TIWAppForm</DesignClass>
        </DCCReference>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Olaf NOE">
            <Key>Cfg_3</Key>
            <CfgParent>Cfg_1</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Olaf OOE">
            <Key>Cfg_4</Key>
            <CfgParent>Cfg_1</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Florian NOE">
            <Key>Cfg_6</Key>
            <CfgParent>Cfg_1</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType/>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">ELKE.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">False</Platform>
                <Platform value="Linux64">False</Platform>
                <Platform value="OSX64">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
                <Platform value="iOSDevice64">False</Platform>
            </Platforms>
            <ModelSupport>False</ModelSupport>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
