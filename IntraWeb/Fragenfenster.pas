﻿unit Fragenfenster;

interface

uses
	Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes,
  IWCGJQButton, Vcl.Imaging.jpeg, IWVCLBaseControl,
	IWBaseControl, IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls,
	Vcl.Forms, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
	IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, IWCGJQCheckBoxList,
	IWCGJQGrid, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component,
	IWCGJQComp, IWCGJQSweetAlert, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, Data.DB, IWCompTabControl,Forms.Base,
  IWCompLabel, IWCGJQLabel ;

type
	TFrmFragenfenster = class(TFormBase)
		IWRegion2: TIWCGJQRegion;
		IWRegion3: TIWCGJQRegion;
    jqgFragen: TIWCGJQGrid;
		iwcgcbl_FilterFragen: TIWCGJQCheckBoxList;
		sa: TIWCGJQSweetAlert;
    ds_FilterFragen: TDataSource;
    iwcgprov_FilterFragen: TIWCGJQGridDataSetProvider;
    iwtFragenfenster: TIWTabControl;
    TabFragen: TIWTabPage;
    TabFragengruppen: TIWTabPage;
    jqgFragengruppen: TIWCGJQGrid;
    IWRegion4: TIWCGJQRegion;
    TabHauptgruppen: TIWTabPage;
    jqgHauptgruppen: TIWCGJQGrid;
    TabChecklisten: TIWTabPage;
    jqgChecklisten: TIWCGJQGrid;
    TabChecklistenpunkte: TIWTabPage;
    jqgChecklistenpunkt: TIWCGJQGrid;
    IWRegion7: TIWCGJQRegion;
    TabCheckpunktfrage: TIWTabPage;
    jqgCheckpunktfrage: TIWCGJQGrid;
    IWRegion8: TIWCGJQRegion;
    ds_Fragengruppen: TDataSource;
    ds_FragenHauptgruppen: TDataSource;
    ds_Checklisten: TDataSource;
    ds_Checklistenpunkt: TDataSource;
    ds_checkp_Frage: TDataSource;
    iwcgprov_FragenHauptgruppen: TIWCGJQGridDataSetProvider;
    iwcgprov_Fragengruppen: TIWCGJQGridDataSetProvider;
    iwcgprov_Checklisten: TIWCGJQGridDataSetProvider;
    iwcgprov_Checklistenpunkt: TIWCGJQGridDataSetProvider;
    iwcgprov_checkp_Frage: TIWCGJQGridDataSetProvider;
    IWRegion9: TIWCGJQRegion;
    iwcgcbl_FilterFragenGruppen: TIWCGJQCheckBoxList;
    iwcgcbl_Checklistenpunkte: TIWCGJQCheckBoxList;
    iwcgcbl_CheckpunktFragen: TIWCGJQCheckBoxList;
    iwcgcbl_FiltereCheckListe: TIWCGJQCheckBoxList;
		procedure iwcgcbl_FilterFragenJQCheckBoxListOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_FilterFragenJQCheckBoxListOptionsCheckall(Sender: TObject;
      AParams: TStringList);
    procedure iwcgcbl_FilterFragenJQCheckBoxListOptionsUncheckall(
      Sender: TObject; AParams: TStringList);
    procedure IWCGJQGrid1JQGridOptionsSelectRow(Sender: TObject;
      AParams: TStringList);
    procedure iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsClick(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsCheckall(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsUncheckall(
      Sender: TObject; AParams: TStringList);
    procedure jqgHauptgruppenCreate(Sender: TObject);
    procedure jqgChecklistenCreate(Sender: TObject);
    procedure IWCGJQCheckBoxList1JQCheckBoxListOptionsCheckall(Sender: TObject;
      AParams: TStringList);
    procedure iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsClick(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsUncheckall(
      Sender: TObject; AParams: TStringList);
    procedure IWCGJQCheckBoxList2JQCheckBoxListOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsUncheckall(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsCheckall(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsClick(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsCheckall(
      Sender: TObject; AParams: TStringList);
    procedure iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsUncheckall(
      Sender: TObject; AParams: TStringList);
    procedure iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure FragenfensterOnCreate(Sender: TObject);
	public
  private
    procedure FunktionenKonfigurieren;
	end;

implementation

uses dmmain, Funktionen, ServerController;

{$R *.dfm}

procedure TFrmFragenfenster.FragenfensterOnCreate(Sender: TObject);
begin
  inherited;
  FunktionenKonfigurieren;
  iwtFragenfenster.ActivePage := 0;
end;

procedure TFrmFragenfenster.FunktionenKonfigurieren;
var
  funktionen: TFunktionenManager;
begin
  funktionen := Usersession.FunktionenManager;

  // Anzeigen
  if funktionen.HatFunktion(Fragen_anzeigen) then
    TabFragen.Visible := true;
  if funktionen.HatFunktion(Fragengruppen_anzeigen) then
    TabFragengruppen.Visible := true;
  if funktionen.HatFunktion(Hauptgruppen_anzeigen) then
    TabHauptgruppen.Visible := true;
  if funktionen.HatFunktion(Checklisten_anzeigen) then
    TabChecklisten.Visible := true;
  if funktionen.HatFunktion(Checklistenpunkte_anzeigen) then
    TabChecklistenpunkte.Visible := true;
  if funktionen.HatFunktion(Checkpunktfragen_anzeigen) then
    TabCheckpunktfrage.Visible := true;
end;

procedure TFrmFragenfenster.iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
	Release;
end;

procedure TFrmFragenfenster.iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	i: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin

  dm_main.qu_Checklistenpunkte.close;
	filterliste := TStringList.Create;
	for i := 0 to iwcgcbl_Checklistenpunkte.Items.Count - 1 do
	begin
		if iwcgcbl_Checklistenpunkte.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_Checklistenpunkte.Items.Items[i].Value);
		end;
	end;

	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_Checklistenpunkte.SQL.Clear;
  	dm_main.qu_Checklistenpunkte.SQL.Add('Select c.bezeichnung, p.bezeichung, p.langtext');
  	dm_main.qu_Checklistenpunkte.SQL.Add('from Stammdaten.checklistenpunkt p left join Stammdaten.checklisten c on p.id_checkliste = c.id');
  	dm_main.qu_Checklistenpunkte.SQL.Add('where p.id_checkliste in '+sqlFilter);
  	dm_main.qu_Checklistenpunkte.Prepare;
  	dm_main.qu_Checklistenpunkte.Active:=true;
	end;
end;

procedure TFrmFragenfenster.iwcgcbl_ChecklistenpunkteJQCheckBoxListOptionsUncheckall(
  Sender: TObject; AParams: TStringList);
begin
	dm_main.qu_Checklistenpunkte.Close;
end;

procedure TFrmFragenfenster.iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsCheckall(
  Sender: TObject; AParams: TStringList);
var
	i: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin
  dm_main.qu_Checkp_Frage.close;
	filterliste := TStringList.Create;
	for i := 0 to iwcgcbl_CheckpunktFragen.Items.Count - 1 do
	begin
		if iwcgcbl_CheckpunktFragen.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_CheckpunktFragen.Items.Items[i].Value);
		end;
	end;

	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_Checkp_Frage.SQL.Clear;
  	dm_main.qu_Checkp_Frage.SQL.Add('Select c.bezeichung,  f.fragennr, f.frage');
  	dm_main.qu_Checkp_Frage.SQL.Add('from Stammdaten.checkp_frage p left join Stammdaten.fragenstamm f on p.id_fragenstamm=f.id');
  	dm_main.qu_Checkp_Frage.SQL.Add('left join Stammdaten.checklistenpunkt c on p.id_checklistenpunkt=c.id');
  	dm_main.qu_Checkp_Frage.SQL.Add('where p.id_checklistenpunkt in '+sqlFilter);
  	dm_main.qu_Checkp_Frage.Prepare;
  	dm_main.qu_Checkp_Frage.Active:=true;
  end;
end;

procedure TFrmFragenfenster.iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	i: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin
  dm_main.qu_Checkp_Frage.close;
	filterliste := TStringList.Create;
	for i := 0 to iwcgcbl_CheckpunktFragen.Items.Count - 1 do
	begin
		if iwcgcbl_CheckpunktFragen.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_CheckpunktFragen.Items.Items[i].Value);
		end;
	end;

	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_Checkp_Frage.SQL.Clear;
  	dm_main.qu_Checkp_Frage.SQL.Add('Select c.bezeichung,  f.fragennr, f.frage');
  	dm_main.qu_Checkp_Frage.SQL.Add('from Stammdaten.checkp_frage p left join Stammdaten.fragenstamm f on p.id_fragenstamm=f.id');
  	dm_main.qu_Checkp_Frage.SQL.Add('left join Stammdaten.checklistenpunkt c on p.id_checklistenpunkt=c.id');
  	dm_main.qu_Checkp_Frage.SQL.Add('where p.id_checklistenpunkt in '+sqlFilter);
  	dm_main.qu_Checkp_Frage.Prepare;
  	dm_main.qu_Checkp_Frage.Active:=true;
  end;
end;

procedure TFrmFragenfenster.iwcgcbl_CheckpunktFragenJQCheckBoxListOptionsUncheckall(
  Sender: TObject; AParams: TStringList);
begin
	dm_main.qu_filterCheckpunkte.close;
end;

procedure TFrmFragenfenster.iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsCheckall(
  Sender: TObject; AParams: TStringList);
var
  counter: integer;
begin
  iwcgcbl_CheckpunktFragen.Items.Clear;
	dm_main.qu_filterCheckpunkte.close;
    dm_main.qu_filterCheckpunkte.SQL.Clear;
  dm_main.qu_filterCheckpunkte.SQL.Add('Select  bezeichung');
  dm_main.qu_filterCheckpunkte.SQL.Add('From Stammdaten.checklistenpunkt');
  dm_main.qu_filterCheckpunkte.Prepare;
  dm_main.qu_filterCheckpunkte.Active:=true;
	dm_main.qu_filterCheckpunkte.First;
  counter:=0;
  while not dm_main.qu_filterCheckpunkte.Eof do
  begin
    with iwcgcbl_CheckpunktFragen.Items.Add do
    begin
    	Caption:=dm_main.qu_filterCheckpunkte.fieldbyname('bezeichung').AsString;
    	Value:=inttostr(counter+1);
      Selected:= false;
      Disabled:= false;
    end;
    dm_main.qu_filterCheckpunkte.next;
    counter:=counter+1;
  end;
	dm_main.qu_filterCheckpunkte.close;
end;

procedure TFrmFragenfenster.iwcgcbl_FiltereCheckListeJQCheckBoxListOptionsUncheckall(
  Sender: TObject; AParams: TStringList);
begin
  iwcgcbl_CheckpunktFragen.Items.Clear;
	dm_main.qu_filterCheckpunkte.close;

end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsCheckall(
  Sender: TObject; AParams: TStringList);
begin
	dm_main.qu_Fragengruppen.close;
  dm_main.qu_Fragengruppen.SQL.Clear;
  dm_main.qu_Fragengruppen.SQL.Add('Select f.id, f.bezeichnung, h.bezeichnung');
  dm_main.qu_Fragengruppen.SQL.Add('from Stammdaten.fragengruppen f left join Stammdaten.fragenhauptgruppen h on f.id_hauptgruppe = h.id');
  dm_main.qu_Fragengruppen.Prepare;
  dm_main.qu_Fragengruppen.Active:=true;
end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	i: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin
     dm_main.qu_Fragengruppen.close;
	filterliste := TStringList.Create;
	for i := 0 to iwcgcbl_FilterFragenGruppen.Items.Count - 1 do
	begin
		if iwcgcbl_FilterFragenGruppen.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_FilterFragenGruppen.Items.Items[i].Value);
		end;
	end;

	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_Fragengruppen.SQL.Clear;
  dm_main.qu_Fragengruppen.SQL.Add('Select f.id, f.bezeichnung, h.bezeichnung');
  dm_main.qu_Fragengruppen.SQL.Add('From Stammdaten.fragengruppen f left join Stammdaten.fragenhauptgruppen h on f.id_hauptgruppe = h.id');
  dm_main.qu_Fragengruppen.SQL.Add('where h.id in '+sqlFilter);
  dm_main.qu_Fragengruppen.Prepare;
  dm_main.qu_Fragengruppen.Active:=true;
	end;
end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenGruppenJQCheckBoxListOptionsUncheckall(
  Sender: TObject; AParams: TStringList);
begin
	dm_main.qu_Fragengruppen.Close;
end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenJQCheckBoxListOptionsCheckall(
  Sender: TObject; AParams: TStringList);
begin
  dm_main.qu_FilterFragen.close;
  dm_main.qu_FilterFragen.SQL.Clear;
  dm_main.qu_FilterFragen.SQL.Add('Select g.bezeichnung, s.fragennr, s.frage');
  dm_main.qu_FilterFragen.SQL.Add('From Stammdaten.fragenstamm s left join Stammdaten.fragengruppen g on s.id_gruppe = g.id');
  dm_main.qu_FilterFragen.Prepare;
  dm_main.qu_FilterFragen.Active:=true;

end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenJQCheckBoxListOptionsClick(Sender: TObject; AParams: TStringList);
var
	i: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin
  dm_main.qu_FilterFragen.close;
	filterliste := TStringList.Create;
	for i := 0 to iwcgcbl_FilterFragen.Items.Count - 1 do
	begin
		if iwcgcbl_FilterFragen.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_FilterFragen.Items.Items[i].Value);
		end;
	end;

	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_FilterFragen.SQL.Clear;
  dm_main.qu_FilterFragen.SQL.Add('Select g.bezeichnung, s.fragennr, s.frage');
  dm_main.qu_FilterFragen.SQL.Add('From Stammdaten.fragenstamm s left join Stammdaten.fragengruppen g on s.id_gruppe = g.id');
  dm_main.qu_FilterFragen.SQL.Add('where g.id in '+sqlFilter);
  dm_main.qu_FilterFragen.Prepare;
  dm_main.qu_FilterFragen.Active:=true;
	end;
end;

procedure TFrmFragenfenster.iwcgcbl_FilterFragenJQCheckBoxListOptionsUncheckall(
  Sender: TObject; AParams: TStringList);
begin

  dm_main.qu_FilterFragen.close;
end;

procedure TFrmFragenfenster.jqgChecklistenCreate(Sender: TObject);
begin
	dm_main.qu_Checklisten.open;
end;

procedure TFrmFragenfenster.jqgHauptgruppenCreate(Sender: TObject);
begin
dm_main.qu_Fragenhauptgruppen.Open();
end;

procedure TFrmFragenfenster.IWCGJQCheckBoxList1JQCheckBoxListOptionsCheckall(
  Sender: TObject; AParams: TStringList);
begin
	dm_main.qu_Checklistenpunkte.close;
  dm_main.qu_Checklistenpunkte.SQL.Clear;
  dm_main.qu_Checklistenpunkte.SQL.Add('Select c.bezeichnung, p.bezeichung, p.langtext');
  dm_main.qu_Checklistenpunkte.SQL.Add('from Stammdaten.checklistenpunkt p left join Stammdaten.checklisten c on p.id_checkliste = c.id');
  dm_main.qu_Checklistenpunkte.Prepare;
  dm_main.qu_Checklistenpunkte.Active:=true;
end;

procedure TFrmFragenfenster.IWCGJQCheckBoxList2JQCheckBoxListOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	i, counter: Integer;
	filterliste: TStringList;
	sqlFilter: String;
begin
  dm_main.qu_filterCheckpunkte.close;
	filterliste := TStringList.Create;
  iwcgcbl_CheckpunktFragen.Items.clear;
	for i := 0 to iwcgcbl_FiltereCheckListe.Items.Count - 1 do
	begin
		if iwcgcbl_FiltereCheckListe.Items.Items[i].Selected = true then
		begin
			filterliste.Add(iwcgcbl_FiltereCheckListe.Items.Items[i].Value);
		end;
	end;
  for i := 0 to iwcgcbl_CheckpunktFragen.Items.Count-1 do
  begin
    iwcgcbl_CheckpunktFragen.Items.Delete(i);
  end;
	if filterliste.Count > 0 then
	begin
		sqlFilter := '(';
		for i := 0 to filterliste.Count - 1 do
		begin
			if i > 0 then
			begin
				sqlFilter := sqlFilter + ',';
			end;
				sqlFilter := sqlFilter + #39 + filterliste.Strings[i] + #39;
		end;
		sqlFilter := sqlFilter + ')';
    dm_main.qu_filterCheckpunkte.SQL.Clear;
  dm_main.qu_filterCheckpunkte.SQL.Add('Select bezeichung');
  dm_main.qu_filterCheckpunkte.SQL.Add('From Stammdaten.checklistenpunkt');
  dm_main.qu_filterCheckpunkte.SQL.Add('where id_checkliste in '+sqlFilter);
  dm_main.qu_filterCheckpunkte.Prepare;
  dm_main.qu_filterCheckpunkte.Active:=true;
	dm_main.qu_filterCheckpunkte.First;
  counter:=0;
  while not dm_main.qu_filterCheckpunkte.Eof do
  begin
    with iwcgcbl_CheckpunktFragen.Items.Add do
    begin
    	Caption:=dm_main.qu_filterCheckpunkte.fieldbyname('bezeichung').AsString;
    	Value:=inttostr(counter+1);
      Selected:= false;
      Disabled:= false;
    end;
    dm_main.qu_filterCheckpunkte.next;
    counter:=counter+1;
  end;
  dm_main.qu_filterCheckpunkte.Close;
	end;
end;

procedure TFrmFragenfenster.IWCGJQGrid1JQGridOptionsSelectRow(Sender: TObject;
  AParams: TStringList);
var
index, i:Integer;
fragennr, fragentext : String;
begin
	index:= strtoint(AParams.Values['rowid']);
  dm_main.qu_FilterFragen.First;
  for i := 0 to dm_main.qu_FilterFragen.RecordCount-1 do
  begin
  	if i = index-1 then
    begin
    	fragennr:=dm_main.qu_FilterFragen.FieldByName('fragennr').AsString;
      fragentext:=dm_main.qu_FilterFragen.FieldByName('frage').AsString;
    end;
    dm_main.qu_FilterFragen.Next;
  end;
  sa.Info(fragennr, fragentext);
end;

end.
