﻿unit Forms.Main;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, Vcl.Controls,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompButton,
  IWCompMemo, Vcl.Imaging.jpeg, IWCompExtCtrls, Vcl.Forms, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQRegion, IWCGJQControl,
  IWCGJQButton, IWCGJQScheduler,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp,
  IWCGJQSweetAlert, generics.collections, IWCGJQDatePicker, BuntelisteU,
  IWCompLabel, IWCGJQDialog, IWCGJQMemo, IWCGJQEdit, IWCGJQLabel,
  IWCGJQResponsiveList, IWCGPanelList, IWCGJQDateTime<PERSON>icker, IWCGJQMessageDlg,
  FireDAC.Stan.Param, Dialogs, Forms.Base, Forms.AMACCK;

type
  TFormMain = class(TFormBase)
    RegionMenuButtonsLeft: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqbNaechsterMonat: TIWCGJQButton;
    jqbVorherigerMonat: TIWCGJQButton;
    iwcgDP_Datumsanzeige: TIWCGJQDatePicker;
    iwcgSched_Kalender: TIWCGJQScheduler;
    iwrRightInner: TIWCGJQRegion;
    iwrTodoErstellen: TIWCGJQRegion;
    iwrTodoAnzeigen: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    iwcged_todo: TIWCGJQEdit;
    jqbTodoSpeichern: TIWCGJQButton;
    iwplTodo: TIWCGPanelList;
    IWRegion10: TIWCGJQRegion;
    jqbTerminTodo: TIWCGJQButton;
    IWLabel2: TIWCGJQLabel;
    iwcgdtp_termintodo: TIWCGJQDateTimePicker;
    ButtonBetrieb: TIWCGJQButton;
    ButtonAdmin: TIWCGJQButton;
    ButtonStammdaten: TIWCGJQButton;
    ButtonVis: TIWCGJQButton;
    ButtonFragen: TIWCGJQButton;
    modalfenster: TIWModalWindow;
    modalregion: TIWCGJQRegion;
    modalLabel2: TIWCGJQLabel;
    modalLabel1: TIWCGJQLabel;
    iwcgdtp_Faelligkeit: TIWCGJQDateTimePicker;
    ButtonKontrollen: TIWCGJQButton;
    ButtonNachrichten: TIWCGJQButton;
    ButtonVersionInfo: TIWCGJQButton;
    iwbRefresh: TIWCGJQButton;
    ButtonLogs: TIWCGJQButton;
    ButtonAMA: TIWCGJQButton;
    IWRegion1: TIWCGJQRegion;
    ButtonMonitoring: TIWCGJQButton;
    procedure IWAppFormCreate(Sender: TObject);
    procedure jqbVorherigerMonatOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNaechsterMonatOnClick(Sender: TObject; AParams: TStringList);
    procedure iwcgDP_DatumsanzeigeJQDatePickerOptionsSelect(Sender: TObject; AParams: TStringList);
    procedure iwcgSched_KalenderSchedulerEventAdded(Sender: TObject; const AEvent: TIWCGJQSchedulerEvent);
    procedure iwcgSched_KalenderSchedulerFrameControlsToEvent(Sender: TObject; AEventItem: TIWCGJQSchedulerEvent;
      var AChanged: Boolean);
    procedure iwcgSched_KalenderSchedulerEventDeleting(Sender: TObject; const AEvent: TIWCGJQSchedulerEvent);
    procedure iwcgSched_KalenderGetNewEventID(Sender: TObject; AEventItem: TIWCGJQSchedulerEvent; var AID: Variant);
    procedure jqbTodoSpeichernOnClick(Sender: TObject; AParams: TStringList);
    procedure iwplTodoClick(Sender: TObject; const AItemIndex: Integer);
    procedure jqbTerminTodoOnClick(Sender: TObject; AParams: TStringList);
    procedure iwbBetriebsfensterClick(Sender: TObject; AParams: TStringList);
    procedure iwbAdministratorfensterClick(Sender: TObject; AParams: TStringList);
    procedure iwbStammdatenfensterClick(Sender: TObject; AParams: TStringList);
    procedure iwbVisClick(Sender: TObject; AParams: TStringList);
    procedure iwbFragenkatalogClick(Sender: TObject; AParams: TStringList);
    procedure modalfensterAsyncClick(Sender: TObject; EventParams: TStringList);
    procedure iwbKontrolleClick(Sender: TObject; AParams: TStringList);
    procedure iwbNachrichtenClick(Sender: TObject; AParams: TStringList);
    procedure iwbVersionInfoOnClick(Sender: TObject; AParams: TStringList);
    procedure iwbLogsOnClick(Sender: TObject; AParams: TStringList);
    procedure UngeleseneNachrichten;
    procedure iwbAMACCKOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonMonitoringJQButtonOptionsClick(Sender: TObject; AParams: TStringList);

  public
    destructor Destroy; override;
  private
    counter, todocounter: Integer;
    idList: TList<Integer>;

    procedure FunktionenKonfigurieren;
  end;

implementation

uses ServerController, dmmain, UserSessionUnit, Forms.Administrator,
  Forms.VisDaten, Forms.Stammdaten, Betrieb.Form, Fragenfenster,
  Forms.Nachrichten, Forms.VersionInfo, Forms.Logs,
  ELKE.Classes, Funktionen, Forms.Kontrollen, Forms.FragenKatalog,
  DX.Utils.Logger, JQ.Helpers.Button, ELKE.Classes.RestError, ELKE.Server.Logger,
  Betrieb.Monitoring.Form;

{$R *.dfm}


procedure TFormMain.ButtonMonitoringJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  TFormBetriebsMonitoring.Create(self).Show;
  ButtonBetrieb.Caption
end;

destructor TFormMain.Destroy;
begin
  idList.Free;
  inherited;
end;

procedure TFormMain.IWAppFormCreate(Sender: TObject);
var
  betreff, beginnD, beginnZ, endD, endZ, Ort, details: String;
  wieder, ganz, farbe, ueber, id, counter: Integer;
  datZeit: TDateTime;
  event: TIWCGJQSchedulerEvent;
  iwcgplItem: TIWCGPanelListItem;
begin
  inherited;
  HasBackButton := false;
  dm_main.qu_TodoListe.Open;
  todocounter := dm_main.qu_TodoListe.Table.Rows.Count;
  dm_main.qu_TodoListe.first;
  while not dm_main.qu_TodoListe.eof do
  begin
    iwcgplItem := iwplTodo.Items.Add;
    iwcgplItem.Caption := dm_main.qu_TodoListe.fieldbyname('titel').AsString;
    dm_main.qu_TodoListe.Next;
  end;
  dm_main.qu_TodoListe.Close;

  iwcgdtp_termintodo.DateTime := now;
  idList := TList<Integer>.Create;
  iwcgSched_Kalender.JQSchedulerOptions.ShowDay := now;
  iwcgDP_Datumsanzeige.Date := now;
  counter := 1;
  iwcgSched_Kalender.JQSchedulerLocale.FormCaptionParticipants := 'Beteiligte:';
  iwcgSched_Kalender.JQSchedulerLocale.HintParticipant := 'Beteiligte:';
  dm_main.qu_Termine.parambyname('PERSONKEY').AsString := dm_main.login_nummer;
  dm_main.qu_Termine.Open;
  dm_main.qu_Termine.first;
  while not dm_main.qu_Termine.eof do
  begin
    if dm_main.qu_Termine.fieldbyname('SICHTBAR').asinteger > 0 then
    begin
      event := iwcgSched_Kalender.JQSchedulerOptions.EventItems.Add;
      betreff := dm_main.qu_Termine.fieldbyname('BETREFF').AsString;
      beginnD := dm_main.qu_Termine.fieldbyname('BEGINN_DATUM').AsString;
      beginnZ := dm_main.qu_Termine.fieldbyname('BEGINN_ZEIT').AsString;
      endD := dm_main.qu_Termine.fieldbyname('END_DATUM').AsString;
      endZ := dm_main.qu_Termine.fieldbyname('END_ZEIT').AsString;
      Ort := dm_main.qu_Termine.fieldbyname('ORT').AsString;
      wieder := dm_main.qu_Termine.fieldbyname('WIEDERHOLEND').asinteger;
      ganz := dm_main.qu_Termine.fieldbyname('GANZTAEGIG').asinteger;
      ueber := dm_main.qu_Termine.fieldbyname('TAGUEBERGREIFEND').asinteger;
      farbe := dm_main.qu_Termine.fieldbyname('FARBE').asinteger;
      details := dm_main.qu_Termine.fieldbyname('DETAILS').AsString;
      id := dm_main.qu_Termine.fieldbyname('ID').asinteger;
      event.EventContent := inttostr(id);
      idList.Add(id);
      event.id := counter;
      counter := counter + 1;
      event.Editable := true;
      event.Subject := betreff;
      event.Location := Ort;
      event.Participants := details;
      event.ColorIndex := farbe;
      datZeit := StrtoDateTime(beginnD + ' ' + beginnZ);
      event.StartTime := datZeit;
      datZeit := StrtoDateTime(endD + ' ' + endZ);
      event.EndTime := datZeit;
      if wieder > 0 then
      begin
        event.Recurring := true;
      end
      else
      begin
        event.Recurring := false;
      end;
      if ganz > 0 then
      begin
        event.AllDay := true
      end
      else
      begin
        event.AllDay := false;
      end;
      if ueber > 0 then
      begin
        event.CrossDay := true;
      end
      else
      begin
        event.CrossDay := false
      end;
    end;
    dm_main.qu_Termine.Next;
  end;
  dm_main.qu_Termine.Close;

  FunktionenKonfigurieren;
end;

/// Blendet Funktionen aus wenn sie für den Benutzer nicht erlaubt sind.
procedure TFormMain.FunktionenKonfigurieren;
var
  Funktionen: TFunktionenManager;
begin
  Funktionen := UserSession.FunktionenManager;
  try
    // Buttons für die Fenster
    Funktionen.Enable(ButtonKontrollen, Kontrollfenster_anzeigen);
    Funktionen.Enable(ButtonBetrieb, Betriebsfenster_anzeigen);
    Funktionen.Enable(ButtonMonitoring, Betriebsmonitoring_anzeigen);
    Funktionen.Enable(ButtonAdmin, Administratorfenster_anzeigen);
    Funktionen.Enable(ButtonStammdaten, Stammdatenfenster_anzeigen);
    Funktionen.Enable(ButtonFragen, Fragenkatalogsfenster_anzeigen);
    Funktionen.Enable(ButtonNachrichten, Nachrichtenfenster_anzeigen);
    Funktionen.Enable(ButtonLogs, Logfenster_anzeigen);
    Funktionen.Enable(ButtonAMA, AMA_Fenster_anzeigen);
    Funktionen.Enable(ButtonVis, VIS_Datenfenster_anzeigen);
    Funktionen.Enable(ButtonVersionInfo, Version_Infofenster_anzeigen);
    Funktionen.SortControls(RegionMenuButtonsLeft);
    // ToDo
    if not Funktionen.HatFunktion(ToDos_erstellen) then
      iwrTodoErstellen.Visible := false;
    if not Funktionen.HatFunktion(ToDos_anzeigen) then
      iwrTodoAnzeigen.Visible := false;

    // Ungelesene Nachrichten
    if Funktionen.HatFunktion(Eigene_Nachrichten_anzeigen) then
      UngeleseneNachrichten;
  except
    on E: Exception do
    begin
      DXLog('Es gab einen Fehler beim Laden der Funktionen.' + E.Message);
      Alert.Error('Es gab einen Fehler beim Laden der Funktionen.'
        + sLineBreak + E.Message);
    end;
  end;

end;

procedure TFormMain.UngeleseneNachrichten;
var
  Nachrichten: TList<TNachrichtKurz>;
  nachricht: TNachrichtKurz;
begin
  // Gibt es ungelesene Nachrichten?
  Nachrichten := Nil;
  try
    try
      Nachrichten := UserSession.ELKERest.GetNachrichten;
      counter := 0;
      for nachricht in Nachrichten do
      begin
        if nachricht.Gelesen.IsNull then
        begin
          counter := counter + 1;
        end;
      end;

      if counter = 0 then
      begin
        ButtonNachrichten.Caption := 'Nachrichten';
      end
      else
      begin
        ButtonNachrichten.Caption := Format('Nachrichten (%d)', [counter]);
      end;
    except
      on E: Exception do
      begin
        ELKELog('Fehler beim Abrufen der Nachrichten: ' + E.Message);
      end;
    end;
  finally
    Nachrichten.Free;
  end;
end;

procedure TFormMain.jqbTerminTodoOnClick(Sender: TObject; AParams: TStringList);
begin
  if not''.equals(IWLabel2.Caption) then
  begin
    with modalfenster do
    begin
      Reset;
      Buttons.CommaText := '&OK,&Cancel';
      Title := 'Achtung!';
      ContentElement := modalregion;
      OnAsyncClick := modalfensterAsyncClick;
      FriendlyName := 'mydlg';
      CloseButtonVisible := false;
      CloseOnEscKey := false;
      Show;
    end;
  end;
end;

procedure TFormMain.jqbTodoSpeichernOnClick(Sender: TObject; AParams: TStringList);
var
  LItem: TIWCGPanelListItem;
begin
  if not iwcged_todo.Text.IsEmpty then
  begin
    LItem := iwplTodo.Items.Add;
    LItem.Caption := datetimetostr(iwcgdtp_Faelligkeit.DateTime) + ': ' + iwcged_todo.Text;
    dm_main.qu_Ins_TodoListe.parambyname('titel').AsString := iwcged_todo.Text;
    dm_main.qu_Ins_TodoListe.parambyname('faellig').AsDateTime := iwcgdtp_Faelligkeit.DateTime;
    dm_main.qu_Ins_TodoListe.ExecSQL;
    iwcged_todo.Text := '';
  end;
end;

procedure TFormMain.jqbNaechsterMonatOnClick(Sender: TObject; AParams: TStringList);
var
  datum: TDate;
  str: string;
  feld: TStringList;
  index: Integer;
begin
  feld := TStringList.Create;
  datum := iwcgSched_Kalender.JQSchedulerOptions.ShowDay;
  str := datetostr(datum);
  feld.Add(str.Substring(0, 2));
  feld.Add(str.Substring(3, 2));
  feld.Add(str.Substring(6));
  if feld[1].contains('12') then
  begin
    index := strtoint(feld[2]);
    index := index + 1;
    str := '01.12.' + inttostr(index);
  end
  else
  begin
    index := strtoint(feld[1]);
    index := index + 1;
    str := '01.' + inttostr(index) + '.' + feld[2];
  end;

  iwcgSched_Kalender.JQSchedulerOptions.ShowDay := strtodate(str);
  iwcgDP_Datumsanzeige.Date := strtodate(str);
end;

procedure TFormMain.jqbVorherigerMonatOnClick(Sender: TObject; AParams: TStringList);
var
  datum: TDate;
  str: string;
  feld: TStringList;
  index: Integer;
begin
  feld := TStringList.Create;
  datum := iwcgSched_Kalender.JQSchedulerOptions.ShowDay;
  str := datetostr(datum);
  feld.Add(str.Substring(0, 2));
  feld.Add(str.Substring(3, 2));
  feld.Add(str.Substring(6));
  if feld[1].contains('01') then
  begin
    index := strtoint(feld[2]);
    index := index - 1;
    str := '01.12.' + inttostr(index);
  end
  else
  begin
    index := strtoint(feld[1]);
    index := index - 1;
    str := '01.' + inttostr(index) + '.' + feld[2];
  end;

  iwcgSched_Kalender.JQSchedulerOptions.ShowDay := strtodate(str);
  iwcgDP_Datumsanzeige.Date := strtodate(str);
end;

procedure TFormMain.iwcgDP_DatumsanzeigeJQDatePickerOptionsSelect(Sender: TObject; AParams: TStringList);
var
  datum: TDate;
begin
  datum := iwcgDP_Datumsanzeige.Date;
  iwcgSched_Kalender.JQSchedulerOptions.ShowDay := datum;
end;

procedure TFormMain.iwplTodoClick(Sender: TObject; const AItemIndex: Integer);
begin
  IWLabel2.Caption := iwplTodo.Items[AItemIndex].Caption;
end;

procedure TFormMain.iwcgSched_KalenderGetNewEventID(Sender: TObject; AEventItem: TIWCGJQSchedulerEvent;
  var AID: Variant);
begin
  AID := counter;
  counter := counter + 1;
end;

procedure TFormMain.iwcgSched_KalenderSchedulerEventAdded(Sender: TObject; const AEvent: TIWCGJQSchedulerEvent);
var
  betreff, beginnD, beginnZ, endD, endZ, Ort, details: String;
  datum: TDate;
  zeit: TTime;
  SQL: TStringList;
begin
  if AEvent.StartTime > now then
  begin
    SQL := TStringList.Create;
    SQL.Clear;

    betreff := AEvent.Subject;
    datum := AEvent.StartTime;
    zeit := AEvent.StartTime;
    beginnD := datetostr(datum);
    beginnZ := TimeToStr(zeit);
    datum := AEvent.EndTime;
    zeit := AEvent.EndTime;
    endD := datetostr(datum);
    endZ := TimeToStr(zeit);
    Ort := AEvent.Location;
    details := AEvent.Participants;
    // aevent.ID:=counter;
    // counter:=counter+1;
    { SQL.Add('SELECT * FROM ADD_TERMIN ');
      SQL.Add('(' + #39 + betreff + #39 + ',' + #39 + beginnD + #39 + ',' + #39 + beginnZ + #39 + ',' + #39 + endD + #39 + ',' + #39 + endZ + #39 + ',' + #39 + Ort + #39 + ',' + inttostr(wieder) + ',' + inttostr(ganz) + ',' + inttostr(ueber) + ',' + inttostr(farbe) + ',' + #39 + details + #39 + ',' + #39 + dm_main.login_nummer + #39 + ')');
      dm_main.qu_Ins_Termin.SQL.Clear;
      dm_main.qu_Ins_Termin.SQL := SQL;
      dm_main.qu_Ins_Termin.Open;
      dm_main.qu_Ins_Termin.first;
      id := strtoint(dm_main.qu_Ins_Termin.fieldbyname('FID').AsString);
      AEvent.EventContent := inttostr(id);
      dm_main.qu_Ins_Termin.Close;
    } SQL.Clear;
  end
  else
  begin
    Alert.Error('Ungültiges Datum!', 'Der Termin darf nicht an einem Tag vor dem heutigen Datum sein!');
  end;
end;

procedure TFormMain.iwcgSched_KalenderSchedulerEventDeleting(Sender: TObject; const AEvent: TIWCGJQSchedulerEvent);
var
  help: String;
  SQL: TStringList;
  id: Integer;
begin
  SQL := TStringList.Create;
  help := AEvent.EventContent;
  id := strtoint(help);
  idList.Remove(id);
  dm_main.qu_Update_Termin.SQL.Clear;
  SQL.Add('update bewegungsdaten.Termine set SICHTBAR = 0 where id = ' + help);
  dm_main.qu_Update_Termin.SQL := SQL;
  dm_main.qu_Update_Termin.ExecSQL;
end;

procedure TFormMain.iwcgSched_KalenderSchedulerFrameControlsToEvent(Sender: TObject; AEventItem: TIWCGJQSchedulerEvent;
  var AChanged: Boolean);
var
  betreff, beginnD, beginnZ, endD, endZ, Ort, details, help: String;
  wieder, ganz, ueber, farbe: Integer;
  datum: TDate;
  zeit: TTime;
  SQL: TStringList;
begin
  if (AChanged = true) and not AEventItem.EventContent.IsEmpty then
  begin
    try
      begin
        SQL := TStringList.Create;
        wieder := 0;
        ganz := 0;
        ueber := 0;
        betreff := AEventItem.Subject;
        datum := AEventItem.StartTime;
        zeit := AEventItem.StartTime;
        beginnD := datetostr(datum);
        beginnZ := TimeToStr(zeit);
        datum := AEventItem.EndTime;
        zeit := AEventItem.EndTime;
        endD := datetostr(datum);
        endZ := TimeToStr(zeit);
        Ort := AEventItem.Location;
        details := AEventItem.Participants;
        if AEventItem.AllDay = true then
        begin
          ganz := 1;
        end;
        if AEventItem.CrossDay = true then
        begin
          ueber := 1;
        end;
        if AEventItem.Recurring = true then
        begin
          wieder := 1;
        end;
        farbe := AEventItem.ColorIndex;
        SQL.Add('UPDATE bewegungsdaten.Termine ');
        // sql.add('(Betreff, Beginn_Datum, Beginn_Zeit, End_Datum, End_Zeit, Ort,Wiederholend, Ganztaegig, Taguebergreifend, Farbe, Details)');
        SQL.Add('SET Betreff=' + #39 + betreff + #39 + ',Beginn_Datum=' + #39 + beginnD + #39 + ',Beginn_Zeit=' + #39 +
          beginnZ + #39 + ',End_Datum=' + #39 + endD + #39 + ',End_Zeit=' + #39 + endZ + #39 + ',Ort=' + #39 + Ort + #39
          + ',Wiederholend=' + inttostr(wieder) + ',Ganztaegig=' + inttostr(ganz) + ',Taguebergreifend=' +
          inttostr(ueber) + ',Farbe=' + inttostr(farbe) + ',Details=' + #39 +
          details + #39);
        help := AEventItem.EventContent;
        SQL.Add('where id = ' + help);
        dm_main.qu_Update_Termin.SQL.Clear;
        dm_main.qu_Update_Termin.SQL := SQL;
        // iwmemo1.Lines.Text:=dm_main.qu_Update_Termin.sql.text;
        dm_main.qu_Update_Termin.ExecSQL;
      end;
    except
      Alert.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
    end;
  end;
end;

procedure TFormMain.modalfensterAsyncClick(Sender: TObject; EventParams: TStringList);
var
  event: TIWCGJQSchedulerEvent;
begin
  if modalfenster.ButtonIndex = 1 then
  begin
    event := iwcgSched_Kalender.JQSchedulerOptions.EventItems.Add;
    event.id := counter;
    event.Editable := true;
    event.StartTime := iwcgdtp_termintodo.DateTime;
    event.EndTime := now;
    counter := counter + 1;
    event.Subject := IWLabel2.Caption;
  end;
  IWLabel2.Caption := '';
  Refresh;
end;

procedure TFormMain.iwbStammdatenfensterClick(Sender: TObject; AParams: TStringList);
begin
  TFormStammdaten.Create(self).Show;
end;

procedure TFormMain.iwbNachrichtenClick(Sender: TObject; AParams: TStringList);
begin
  TFormNachrichten.Create(self).Show;
end;

procedure TFormMain.iwbBetriebsfensterClick(Sender: TObject; AParams: TStringList);
begin
  TFormBetrieb.Create(self).Show;
end;

procedure TFormMain.iwbAdministratorfensterClick(Sender: TObject; AParams: TStringList);
begin
  TFormAdministrator.Create(self).Show;
end;

procedure TFormMain.iwbVersionInfoOnClick(Sender: TObject; AParams: TStringList);
begin
  TFormVersionInfo.Create(self).Show;
end;

procedure TFormMain.iwbVisClick(Sender: TObject; AParams: TStringList);
begin
  TFormVisDaten.Create(self).Show;
end;

procedure TFormMain.iwbKontrolleClick(Sender: TObject; AParams: TStringList);
begin
  TFormKontrollen.Create(self).Show;
end;

procedure TFormMain.iwbLogsOnClick(Sender: TObject; AParams: TStringList);
begin
  TFormLogs.Create(self).Show;
end;

procedure TFormMain.iwbFragenkatalogClick(Sender: TObject; AParams: TStringList);
begin
  TFormFragenkatalog.Create(self).Show;
end;

procedure TFormMain.iwbAMACCKOnClick(Sender: TObject; AParams: TStringList);
begin
  TFormAMACCK.Create(self).Show;
end;

end.
