inherited FormLogs: TFormLogs
  Width = 1200
  Height = 665
  Title = 'Logs'
  OnCreate = logfensterOnCreate
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1200
    TabOrder = 10
    inherited ImageLogo: TIWImageFile
      Left = 917
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 708
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 13
    end
  end
  object iwrMain: TIWCGJQRegion [1]
    Left = 0
    Top = 50
    Width = 1200
    Height = 590
    RenderInvisibleControls = True
    TabOrder = 11
    Version = '1.0'
    Align = alClient
    object IWRegion1: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1200
      Height = 100
      RenderInvisibleControls = True
      TabOrder = 12
      Version = '1.0'
      Align = alTop
      object IWLabel1: TIWCGJQLabel
        Left = 296
        Top = 34
        Width = 29
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Von:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 296
        Top = 56
        Width = 23
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bis:'
      end
      object iwcbSortierung: TIWCGJQComboBox
        Left = 544
        Top = 29
        Width = 120
        Height = 21
        StyleRenderOptions.RenderBorder = False
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        TabOrder = 1
        ItemIndex = -1
        FriendlyName = 'iwcbSortierung'
        NoSelectionText = '-- No Selection --'
      end
      object jqbSuchen: TIWCGJQButton
        Left = 544
        Top = 58
        Width = 120
        Height = 21
        TabOrder = 2
        Version = '1.0'
        JQButtonOptions.Label_ = 'Suchen'
        JQButtonOptions.OnClick.OnEvent = jqbSuchenOnClick
      end
      object jqdpBis: TIWCGJQDateTimePicker
        Left = 345
        Top = 56
        Width = 180
        Height = 21
        TabOrder = 4
        Css = 'ui-widget ui-widget-content ui-corner-all'
        Version = '1.0'
        ZIndex = 1001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd/mm/yyyy'
      end
      object jqdpVon: TIWCGJQDateTimePicker
        Left = 345
        Top = 29
        Width = 180
        Height = 21
        TabOrder = 5
        Css = 'ui-widget ui-widget-content ui-corner-all'
        Version = '1.0'
        ZIndex = 1001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd/mm/yyyy'
      end
      object jqcbLogLevel: TIWCGJQCheckBoxList
        Left = 24
        Top = 58
        Width = 250
        Height = 21
        TabOrder = 3
        Version = '1.0'
        ZIndex = 5000
        JQCheckBoxListOptions.MinWidth = 250
        JQCheckBoxListOptions.CheckAllText = 'Alle ausw'#228'hlen'
        JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
        JQCheckBoxListOptions.NoneSelectedText = 'Log Level ausw'#228'hlen'
        JQCheckBoxListOptions.SelectedText = '# Log Levels ausgew'#228'hlt'
        JQCheckBoxListOptions.MenuZIndex = 5000
        Items = <>
        Groups = <>
      end
      object jqcbApplikation: TIWCGJQCheckBoxList
        Left = 24
        Top = 29
        Width = 250
        Height = 21
        TabOrder = 6
        Version = '1.0'
        ZIndex = 5000
        JQCheckBoxListOptions.MinWidth = 250
        JQCheckBoxListOptions.CheckAllText = 'Alle ausw'#228'hlen'
        JQCheckBoxListOptions.UncheckAllText = 'Auswahl aufheben'
        JQCheckBoxListOptions.NoneSelectedText = 'Applikation ausw'#228'hlen'
        JQCheckBoxListOptions.SelectedText = '# Applikationen ausgew'#228'hlt'
        JQCheckBoxListOptions.MenuZIndex = 5000
        Items = <>
        Groups = <>
      end
    end
    object jqgLogs: TIWCGJQGrid
      Left = 0
      Top = 100
      Width = 1200
      Height = 490
      TabOrder = 9
      Version = '1.0'
      Align = alClient
      JQGridOptions.ColModel = <
        item
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'Application'
          Name = 'Application'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        end
        item
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'Log_Level'
          Name = 'Log_Level'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        end
        item
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'Timestamp'
          Name = 'Timestamp'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        end>
      JQGridOptions.Height = 436
      JQGridOptions.RowNum = 40
      JQGridOptions.SubGridModel = <>
      JQGridOptions.Width = 1198
      JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
      JQGridOptions.OnSelectRow.OnEvent = jqgLogsOnSelectRow
      JQGridOptions.OnSelectRow.SendAllArguments = True
      JQGridNav.Add = False
      JQGridNav.Del = False
      JQGridNav.Edit = False
      JQGridNav.Refresh = False
      JQGridNav.Search = False
      JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
      JQGridCustomButtons = <>
      JQGridProvider = jqdpLogs
      JQGridToolbarSearch.DefaultSearch = gsoContains
      JQDragAndDropOptions.ConnectWith = <>
    end
  end
  object jqdLog: TIWCGJQDialog [2]
    Left = 79
    Top = 35
    Width = 1009
    Height = 630
    Visible = False
    TabOrder = 7
    Version = '1.0'
    Align = alNone
    ZIndex = 5000
    JQDialogOptions.AutoOpen = False
    JQDialogOptions.Height = 630
    JQDialogOptions.Modal = True
    JQDialogOptions.Width = 1009
    JQDialogOptions.zIndex = 5000
    object IWLabel10: TIWCGJQLabel
      Left = 16
      Top = 198
      Width = 35
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel10'
      Caption = 'Body:'
    end
    object IWLabel11: TIWCGJQLabel
      Left = 16
      Top = 18
      Width = 84
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Allgemein:'
    end
    object IWLabel12: TIWCGJQLabel
      Left = 16
      Top = 82
      Width = 71
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Message:'
    end
    object IWLabel13: TIWCGJQLabel
      Left = 16
      Top = 153
      Width = 68
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Request:'
    end
    object IWLabel14: TIWCGJQLabel
      Left = 16
      Top = 254
      Width = 137
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Request-Headers:'
    end
    object IWLabel3: TIWCGJQLabel
      Left = 16
      Top = 48
      Width = 73
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Applikation:'
    end
    object IWLabel4: TIWCGJQLabel
      Left = 397
      Top = 48
      Width = 63
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Log Level:'
    end
    object IWLabel5: TIWCGJQLabel
      Left = 766
      Top = 48
      Width = 75
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Timestamp:'
    end
    object IWLabel6: TIWCGJQLabel
      Left = 16
      Top = 107
      Width = 60
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Message:'
    end
    object IWLabel7: TIWCGJQLabel
      Left = 16
      Top = 176
      Width = 51
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Method:'
    end
    object IWLabel8: TIWCGJQLabel
      Left = 764
      Top = 176
      Width = 70
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'Remote IP:'
    end
    object IWLabel9: TIWCGJQLabel
      Left = 395
      Top = 176
      Width = 27
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel3'
      Caption = 'URI:'
    end
    object iwlApplikation: TIWCGJQLabel
      Left = 95
      Top = 48
      Width = 210
      Height = 33
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'iwlApplikation'
      Caption = 'Text'
    end
    object iwlIP: TIWCGJQLabel
      Left = 837
      Top = 176
      Width = 148
      Height = 38
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel16'
      Caption = 'Text'
    end
    object iwlLogLevel: TIWCGJQLabel
      Left = 458
      Top = 47
      Width = 27
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel16'
      Caption = 'Text'
    end
    object iwlMethod: TIWCGJQLabel
      Left = 95
      Top = 176
      Width = 27
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'iwlMethod'
      Caption = 'Text'
    end
    object iwlTimestamp: TIWCGJQLabel
      Left = 839
      Top = 48
      Width = 158
      Height = 42
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel16'
      Caption = 'Text'
    end
    object iwlURI: TIWCGJQLabel
      Left = 458
      Top = 176
      Width = 287
      Height = 60
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel16'
      Caption = 'Text'
    end
    object iwtBody: TIWText
      Left = 95
      Top = 198
      Width = 294
      Height = 50
      BGColor = clNone
      ConvertSpaces = False
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      FriendlyName = 'iwtMessage'
      Lines.Strings = (
        'iwtMessage')
      RawText = False
      UseFrame = False
      WantReturns = True
    end
    object iwtMessage: TIWText
      Left = 93
      Top = 103
      Width = 392
      Height = 50
      BGColor = clNone
      ConvertSpaces = False
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      FriendlyName = 'iwtMessage'
      Lines.Strings = (
        'iwtMessage')
      RawText = False
      UseFrame = False
      WantReturns = True
    end
    object IWCGJQGrid1: TIWCGJQGrid
      Left = 0
      Top = 310
      Width = 1009
      Height = 320
      TabOrder = 8
      Version = '1.0'
      Align = alBottom
      JQGridOptions.ColModel = <
        item
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'Name'
          Name = 'Name'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Sortable = False
          Caption = 'Name'
        end
        item
          EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
          EditOptions.CustomElements = <>
          Idx = 'Value'
          Name = 'Value'
          SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
          Caption = 'Value'
        end>
      JQGridOptions.Height = 293
      JQGridOptions.PgButtons = False
      JQGridOptions.PgInput = False
      JQGridOptions.RowNum = 100
      JQGridOptions.SubGridModel = <>
      JQGridOptions.Width = 1007
      JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
      JQGridOptions.PagerVisible = False
      JQGridNav.Add = False
      JQGridNav.Del = False
      JQGridNav.Edit = False
      JQGridNav.Refresh = False
      JQGridNav.Search = False
      JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
      JQGridCustomButtons = <>
      JQGridProvider = jqdpRequestHeaders
      JQGridToolbarSearch.DefaultSearch = gsoContains
      JQDragAndDropOptions.ConnectWith = <>
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 640
    Width = 1200
  end
  object jqdpLogs: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_logs
    Left = 625
    Top = 9
  end
  object jqdpRequestHeaders: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_requestheaders
    Left = 544
    Top = 8
  end
  object query: TFDQuery
    Connection = dm_main.FBC_MAIN
    Left = 696
    Top = 16
  end
end
