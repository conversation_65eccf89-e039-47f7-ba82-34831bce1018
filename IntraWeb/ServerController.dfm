object IWServerController: TIWServerController
  OldCreateOrder = False
  AppName = 'ELKEIntraweb'
  ComInitialization = ciMultiThreaded
  Description = 'ELKE Intraweb Administration'
  DebugHTML = True
  DisplayName = 'EsCulenta ELKE Intraweb System'
  HTMLHeaders.Strings = (
    
      '<link rel="shortcut icon" type="image/x-icon" href="Bilder/favic' +
      'on.ico" sizes="32x32">'
    
      '<meta name="viewport" content="width=device-width, initial-scale' +
      '=1">')
  Port = 80
  StyleSheet.Filename = '\ui-themes\ELKE\jquery-ui.css'
  URLBase = '/at.esculenta.elkeiw-cg/'
  Version = '15.2.21'
  DocType = '<!DOCTYPE html>'
  ShowStartParams = False
  ExceptionLogger.Enabled = True
  ExceptionLogger.PurgeAfterDays = 30
  JavaScriptOptions.Debug = True
  JavaScriptOptions.UseUncompressedFiles = True
  JavaScriptOptions.AjaxErrorMode = emConsole
  HTMLLanguage = 'de-AT'
  SessionOptions.SessionTimeout = 20
  SessionOptions.RestartExpiredSession = True
  OnConfig = IWServerControllerOnConfig
  OnException = IWServerControllerBaseException
  OnNewSession = IWServerControllerBaseNewSession
  OnBeforeRender = IWServerControllerBaseBeforeRender
  Height = 402
  Width = 547
end
