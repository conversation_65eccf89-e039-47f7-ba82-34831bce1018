﻿unit Funktionen;

interface

uses

  System.Classes, System.Generics.Collections, System.SysUtils,
  System.Generics.Defaults,
  Vcl.Controls,
  IWBaseHTMLInterfaces,
  ELKE.Classes, ELKE.Classes.Generated, ELKE.Classes.Rest.Client,
  IWBaseInterfaces, Config;

type
  TFunktionsName = (
    Kontrollfenster_anzeigen = 1010,
    Kontrolle_anlegen = 1011,
    Kontrollen_anzeigen = 1012,
    Betriebsfenster_anzeigen = 1020,
    Administratorfenster_anzeigen = 1030,
    Funktionen_anzeigen = 1031,
    User_anzeigen = 1032,
    Gruppen_anzeigen = 1033,
    Gruppen_bearbeiten = 1034,
    Usergruppen_anzeigen = 1035,
    Usergruppen_bearbeiten = 1036,
    Stammdatenfenster_anzeigen = 1040,
    Betriebsübersicht_anzeigen = 1041,
    Betriebe_bearbeiten = 1042,
    Tierarten_anzeigen = 1043,
    Tierarten_bearbeiten = 1044,
    Tierartenkategorien_anzeigen = 1045,
    Tätigkeiten_anzeigen = 1046,
    Tätigkeiten_bearbeiten = 1047,
    Tätigkeitenbetriebstypen_anzeigen = 1048,
    Betriebstypen_Tierart_anzeigen = 1049,
    Betriebstypen_Tierart_bearbeiten = 1050,
    Revisionsstamm_anzeigen = 1051,
    Revisionsstamm_bearbeiten = 1052,
    VIS_Datenfenster_anzeigen = 1060,
    Betriebsstammdaten_anzeigen = 1061,
    Zulassungen_anzeigen = 1062,
    Betriebstypen_anzeigen = 1063,
    DOM_Werte_anzeigen = 1064,
    Zulassungslistencodes_anzeigen = 1065,
    Tierarten_VIS_anzeigen = 1066,
    Zulassungstypen_anzeigen = 1067,
    Schlüsseltypen_anzeigen = 1068,
    PRBKAT_anzeigen = 1069,
    Zulassungsnummern_anzeigen = 1070,
    Fragenkatalogsfenster_anzeigen = 1080,
    Fragen_anzeigen = 1081,
    Fragengruppen_anzeigen = 1082,
    Hauptgruppen_anzeigen = 1083,
    Checklisten_anzeigen = 1084,
    Checklistenpunkte_anzeigen = 1085,
    Checkpunktfragen_anzeigen = 1086,
    Nachrichtenfenster_anzeigen = 1090,
    Nachrichten_senden = 1091,
    Version_Infofenster_anzeigen = 1100,
    ToDos_anzeigen = 1110,
    ToDos_erstellen = 1111,
    Rollen_anzeigen = 1122,
    Rollen_bearbeiten = 1123,
    Funktionsrollen_anzeigen = 1124,
    Funktionsrollen_bearbeiten = 1125,
    Rechtsgrundlagen_anzeigen = 1126,
    Rechtsgrundlagen_bearbeiten = 1127,
    Bkbtypen_Rechtsgrundlage_anzeigen = 1128,
    Bkbtypen_Rechtsgrundlage_bearbeiten = 1129,
    Bkbtypen_anzeigen = 1130,
    Bkbtypen_bearbeiten = 1131,
    Rollen_Bkbtyp_anzeigen = 1132,
    Rollen_Bkbtyp_bearbeiten = 1133,
    Personenpraefix_anzeigen = 1134,
    Personenpraefix_bearbeiten = 1135,
    Bundeslaender_anzeigen = 1136,
    Bundeslaender_bearbeiten = 1137,
    Gemeinden_anzeigen = 1138,
    Gemeinden_bearbeiten = 1139,
    Kontrolltypen_anzeigen = 1140,
    Kontrolltypen_bearbeiten = 1141,
    Revisionsplan_anzeigen = 1053,
    Revisionsplan_bearbeiten = 1054,
    Eigene_Nachrichten_anzeigen = 1092,
    Alle_Nachrichten_anzeigen = 1093,
    Logfenster_anzeigen = 1160,
    AMA_Fenster_anzeigen = 1170,
    Betriebsmonitoring_anzeigen = 1180,

    Fragen_bearbeiten = 1094,
    Fragen_Checklisten_Bearbeiten = 1201,
    Fragen_Formatierungen_Bearbeiten = 1202,
    Fragen_Bewertungen_Bearbeiten = 1203,
    Fragen_Kontrollbereiche_Bearbeiten = 1204,
    Fragen_Maßnahmenkatalog_Bearbeiten = 1205,
    Fragen_Maßnahmen_Bearbeiten = 1206,
    Fragen_Mangeltypen_Bearbeiten = 1207

    );

  TFunktionenManager = class(TObject)
  private
    FRest: TELKERestClient;
    FFunktionen: TDictionary<TFunktionsName, TFunktionKurz>;
  protected
    procedure FunktionenAktualisieren;
  public
    constructor Create;
    destructor Destroy; override;
    function HatFunktion(AFunktion: TFunktionsName): boolean;
    procedure Enable(AControl: IIWBaseControl; AFunktion: TFunktionsName);
    procedure SortControls(AContainer: IIWBaseContainer);
    property Funktionen: TDictionary<TFunktionsName, TFunktionKurz> read FFunktionen;
  end;

  TControlHelper = class helper for TControl
  public
    function Funktion: TFunktionKurz;
    function TabOrder: IIWTaborder;
  end;

implementation

uses
  ServerController, IWCGJQCommon;

constructor TFunktionenManager.Create;
begin
  inherited Create;
  FRest := TElkeRestClient.Create(TConfig.Default.RestURI);
  FFunktionen := TDictionary<TFunktionsName, TFunktionKurz>.Create;
  FunktionenAktualisieren;
end;

destructor TFunktionenManager.Destroy;
begin
  FFunktionen.Free;
  FRest.Free;
  inherited;
end;

procedure TFunktionenManager.Enable(AControl: IIWBaseControl; AFunktion: TFunktionsName);
begin
  // Prüfen ob der User für die angegebene Funktion berechtigt ist und entsprechend das Control
  // auf visible stellen
  // Zusätzlich wird
  // - der Funktions-Wert im Tag des Kontrols hinterlegt
  // - die Beschriftung ggfs. übernommen
  AControl.Visible := HatFunktion(AFunktion);
  if AControl is TComponent then
  begin
    TComponent(AControl).Tag := Ord(AFunktion);
  end;

  var
    LControl: IIWCGActionControl;
  if Supports(AControl, IIWCGActionControl, LControl) then
  begin
    var
      LFunktion: TFunktionKurz;
    FFunktionen.TryGetValue(AFunktion, LFunktion);
    if Assigned(LFunktion) and (LFunktion.Beschriftung.HasValue) then
    begin
      (AControl as IIWCGActionControl).ControlCaption := LFunktion.Beschriftung;
    end;
  end;
end;

procedure TFunktionenManager.FunktionenAktualisieren;
var
  LFunktionen: TObjectList<TFunktionKurz>;
  LFunktion: TFunktionKurz;
begin
  FFunktionen.Clear;
  LFunktionen := FRest.ErlaubteFunktionen;
  try
    for LFunktion in LFunktionen do
    begin
      FFunktionen.Add(TFunktionsName(LFunktion.Progcallid.ValueOrDefault), LFunktion)
    end;
  finally
    LFunktionen.Free;
  end;
end;

function TFunktionenManager.HatFunktion(AFunktion: TFunktionsName): boolean;
begin
  result := false;
  var
    LFunktion: TFunktionKurz := nil;
  FFunktionen.TryGetValue(AFunktion, LFunktion);
  if Assigned(LFunktion) then
  begin
    result := LFunktion.Sichtbar;
  end;
end;

procedure TFunktionenManager.SortControls(AContainer: IIWBaseContainer);
begin
  // Wir sortieren hier die Elemente in einem Container (aka Region) von oben nach unten
  // gemäß ihrer Position die über die zugeordnete Funktion ermittelt wird.
  // Die Funktion (die Nummer) ist vorher manuell oder per Enable() im Tag des Controls zu hinterlegen.
  // Zusätzlich wird die TabOrder entsprechend eingestellt.
  var
  LControls := TList<TControl>.Create;
  try
    for var i := 0 to AContainer.IWComponentsCount - 1 do
    begin
      var
      LComponent := AContainer.Component[i];
      // Es sollten alles TControls sein. Nur die haben eine Position
      if LComponent is TControl then
      begin
        LControls.Add(TControl(LComponent));
      end;

    end;
    // Controls-Liste sortieren
    LControls.Sort(TComparer<TControl>.Construct(
      function(const Left, Right: TControl): Integer
      begin
        if (Left.Funktion <> nil) and (Right.Funktion <> nil) then
        begin
          result := Left.Funktion.Position.ValueOrDefault - Right.Funktion.Position.ValueOrDefault;
        end
        else
        begin
          result := 0;
        end;
      end));

    // Position von oben nach unten setzen
    var
    i := 0;
    // wir orientieren uns an der Höhe des ersten Elements
    var
    LHeight := LControls[0].Height;
    for var LControl in LControls do
    begin
      LControl.Align := alNone;
      LControl.Top := i * LHeight;
      LControl.Height := LHeight;
      i := i + 1;
    end;

    // hier ist die Liste nach Position sortiert. Erst jetzt darf die Taborder eingestellt werden.
    for var LControl in LControls do
    begin
      LControl.Align := alTop;

      // Wir schauen ob das Control eine Taborder hat. Die Taborder muss entsprechend angepasst werden
      // und nimmt gleichzeitig die Position des Controls auf
      var
      LTabOrder := LControl.TabOrder;
      if (LTabOrder <> nil) and (LControl.Funktion <> nil) then
      begin
        LTabOrder.TabOrder := LControl.Funktion.Position.ValueOrDefault;
      end;

    end;
  finally
    FreeAndNil(LControls);
  end;
end;

function TControlHelper.TabOrder: IIWTaborder;
begin
  result := TabOrderInterface(self);
end;

function TControlHelper.Funktion: TFunktionKurz;
begin
  var
    LFunktion: TFunktionKurz := nil;
    // Beim "Enable" Aufruf wird die Funktionsnummer in das Tag kodiert.
  UserSession.FunktionenManager.Funktionen.TryGetValue(TFunktionsName(self.Tag), LFunktion);
  result := LFunktion;
end;

end.
