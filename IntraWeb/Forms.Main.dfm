inherited FormMain: TFormMain
  Width = 1298
  Height = 760
  Title = 'ELKE - '#220'bersicht'
  DesignLeft = 2
  DesignTop = 2
  object modalregion: TIWCGJQRegion [0]
    Left = 308
    Top = 151
    Width = 276
    Height = 97
    Visible = False
    RenderInvisibleControls = True
    TabOrder = 9
    Version = '1.0'
    object modalLabel2: TIWCGJQLabel
      Left = 0
      Top = 24
      Width = 276
      Height = 24
      Align = alTop
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'modalLabel2'
      Caption = 'einen Termin erzeugen?'
    end
    object modalLabel1: TIWCGJQLabel
      Left = 0
      Top = 0
      Width = 276
      Height = 24
      Align = alTop
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'modalLabel'
      Caption = 'Wollen Sie aus den To-Do Eintrag '
    end
  end
  object iwrMid: TIWCGJQRegion [1]
    Left = 280
    Top = 50
    Width = 1018
    Height = 685
    TabOrder = 10
    Version = '1.0'
    Align = alClient
    StyleRenderOptions.RenderZIndex = False
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1018
      Height = 100
      RenderInvisibleControls = True
      TabOrder = 11
      Version = '1.0'
      Align = alTop
      StyleRenderOptions.RenderZIndex = False
      DesignSize = (
        1018
        100)
      object iwbRefresh: TIWCGJQButton
        Left = 760
        Top = 56
        Width = 1
        Height = 1
        Visible = False
        TabOrder = 22
        Version = '1.0'
        Color = clBtnFace
        JQButtonOptions.Label_ = 'iwbRefresh'
      end
      object jqbNaechsterMonat: TIWCGJQButton
        Left = 520
        Top = 14
        Width = 180
        Height = 48
        TabOrder = 24
        Version = '1.0'
        Anchors = [akLeft]
        StyleRenderOptions.RenderPadding = False
        JQButtonOptions.Label_ = 'N'#228'chster Monat >'
        JQButtonOptions.OnClick.Ajax = False
        JQButtonOptions.OnClick.OnEvent = jqbNaechsterMonatOnClick
        JQEvents.OnClick.Ajax = False
      end
      object jqbVorherigerMonat: TIWCGJQButton
        Left = 18
        Top = 14
        Width = 184
        Height = 48
        TabOrder = 25
        Version = '1.0'
        JQButtonOptions.Label_ = '< Vorheriger Monat'
        JQButtonOptions.OnClick.Ajax = False
        JQButtonOptions.OnClick.OnEvent = jqbVorherigerMonatOnClick
        JQEvents.OnClick.Ajax = False
      end
      object iwcgDP_Datumsanzeige: TIWCGJQDatePicker
        Left = 208
        Top = 6
        Width = 185
        Height = 48
        TabOrder = 26
        Version = '1.0'
        Caption = 'aktuelles Datum:'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.DayNamesMin.Strings = (
          'Mo'
          'Di'
          'Mi'
          'Do'
          'Fr'
          'Sa'
          'So')
        JQDatePickerOptions.DayNamesShort.Strings = (
          'Mo'
          'Di'
          'Mi'
          'Do'
          'Fr'
          'Sa'
          'So')
        JQDatePickerOptions.MonthNames.Strings = (
          'J'#228'nner'
          'Februar'
          'M'#228'rz'
          'April'
          'Mai'
          'Juni'
          'Juli'
          'August'
          'September'
          'Oktober'
          'November'
          'Dezember')
        JQDatePickerOptions.MonthNamesShort.Strings = (
          'J'#228'nner'
          'Februar'
          'M'#228'rz'
          'April'
          'Mai'
          'Juni'
          'Juli'
          'August'
          'September'
          'Oktober'
          'November'
          'Dezember')
        JQDatePickerOptions.OnSelect.Ajax = False
        JQDatePickerOptions.OnSelect.OnEvent = iwcgDP_DatumsanzeigeJQDatePickerOptionsSelect
      end
    end
    object iwcgSched_Kalender: TIWCGJQScheduler
      Left = 0
      Top = 100
      Width = 718
      Height = 585
      RenderInvisibleControls = True
      TabOrder = 27
      Version = '1.0'
      Align = alClient
      StyleRenderOptions.RenderVisibility = True
      ParentShowHint = False
      ShowHint = False
      JQSchedulerOptions.AutoLoad = True
      JQSchedulerOptions.Height = 585
      JQSchedulerOptions.View = svMonth
      JQSchedulerOptions.ShowEditBtnOnNewEvent = True
      JQSchedulerLocale.HintToDateView = 'Klicken um Details '#252'ber den Tag anzusehen'
      JQSchedulerLocale.Undefined = 'Undefiniert'
      JQSchedulerLocale.HintAllDayEvent = 'T'#228'glicher Termin'
      JQSchedulerLocale.HintRepeatEvent = 'Wiederhole Termin'
      JQSchedulerLocale.HintTime = 'Zeit'
      JQSchedulerLocale.HintEvent = 'Termin'
      JQSchedulerLocale.HintLocation = 'Ort'
      JQSchedulerLocale.HintParticipant = 'Beteiligte:'
      JQSchedulerLocale.ExceptionGetData = 'Fehler beim Datenempfang!'
      JQSchedulerLocale.HintNewEvent = 'Neuer Termin'
      JQSchedulerLocale.ConfirmDeleteEvent = 'Wollen Sie diesen Termin wirklich l'#246'schen'
      JQSchedulerLocale.ConfirmDeleteRecurringEvent = 
        'M'#246'chten Sie alle Wiederholungen dieses Termins l'#246'schen oder nur ' +
        'diesen?'
      JQSchedulerLocale.ExceptionDataFormatError = 'Formatfehler!'
      JQSchedulerLocale.ExceptionInvalidSubject = 'Betreff ist leer oder beinhaltet ung'#252'ltige Zeichen!'
      JQSchedulerLocale.ExceptionViewNoReady = 'Ansicht ist nicht bereit!'
      JQSchedulerLocale.CaptionExample = 'Bsp.: Kontrolle um 8:00 Uhr'
      JQSchedulerLocale.CaptionSubject = 'Betreff:'
      JQSchedulerLocale.CaptionCreateEvent = 'Neuer Termin'
      JQSchedulerLocale.CaptionUpdateDetail = 'Details bearbeiten'
      JQSchedulerLocale.HintClickToDetail = 'Details ansehen'
      JQSchedulerLocale.DialogCaptionDelete = 'L'#246'schen'
      JQSchedulerLocale.DayPlural = 'Tage'
      JQSchedulerLocale.Others = 'Andere'
      JQSchedulerLocale.DialogTitleViewEvent = 'Termin ansicht'
      JQSchedulerLocale.DialogTitleNewEvent = 'Neuer Termin'
      JQSchedulerLocale.DateFormat.FullDayFormat = 'ddd dd.mm.yyyy'
      JQSchedulerLocale.DateFormat.WorkingFormat = 'dd.mm.yyyy'
      JQSchedulerLocale.DateFormat.DayWeekViewDateFormat = 'ddd dd.mm.yyyy'
      JQSchedulerLocale.DateFormat.Sunday = 'Sonntag'
      JQSchedulerLocale.DateFormat.Monday = 'Montag'
      JQSchedulerLocale.DateFormat.Tuesday = 'Dienstag'
      JQSchedulerLocale.DateFormat.Wednesday = 'Mittwoch'
      JQSchedulerLocale.DateFormat.Thursday = 'Donnerstag'
      JQSchedulerLocale.DateFormat.Friday = 'Freitag'
      JQSchedulerLocale.DateFormat.Saturday = 'Samstag'
      JQSchedulerLocale.DateFormat.January = 'J'#228'nner'
      JQSchedulerLocale.DateFormat.February = 'Februar'
      JQSchedulerLocale.DateFormat.March = 'M'#228'rz'
      JQSchedulerLocale.DateFormat.April = 'April'
      JQSchedulerLocale.DateFormat.May = 'Mai'
      JQSchedulerLocale.DateFormat.June = 'Juni'
      JQSchedulerLocale.DateFormat.July = 'Juli'
      JQSchedulerLocale.DateFormat.August = 'August'
      JQSchedulerLocale.DateFormat.September = 'September'
      JQSchedulerLocale.DateFormat.October = 'Oktober'
      JQSchedulerLocale.DateFormat.November = 'November'
      JQSchedulerLocale.DateFormat.December = 'Dezember'
      JQSchedulerLocale.FormTitleView = 'Termin ansehen'
      JQSchedulerLocale.FormTitleEdit = 'Termin bearbeiten'
      JQSchedulerLocale.FormTitleNew = 'Neuer Termin'
      JQSchedulerLocale.FormCaptionSubject = 'Betreff:'
      JQSchedulerLocale.FormCaptionStartTime = 'Anfangszeit:'
      JQSchedulerLocale.FormCaptionEndTime = 'Endzeit:'
      JQSchedulerLocale.FormCaptionAllDay = 'Ganzt'#228'gig'
      JQSchedulerLocale.FormCaptionCrossDay = 'Tag'#252'bergreifend'
      JQSchedulerLocale.FormCaptionRecurring = 'Wiederholend:'
      JQSchedulerLocale.FormCaptionColor = 'Farbe'
      JQSchedulerLocale.FormCaptionLocation = 'Ort:'
      JQSchedulerLocale.FormCaptionParticipants = 'Beteiligete:'
      JQSchedulerLocale.ExceptionStartTimeEmpty = 'Anfangszeit darf nicht leer sein'
      JQSchedulerLocale.ExceptionEndTimeEmpty = 'Endzeit darf nicht leer sein!'
      JQSchedulerLocale.ExceptionEndTimeLowerThanStartTime = 'Endzeit darf nicht for Anfangszeit liegen!'
      JQSchedulerLocale.ExceptionInvalidColor = 'Farbindex au'#223'erhalb des g'#252'ltigen Bereichs'
      JQSchedulerLocale.DialogViewEventDaysNames.Strings = (
        'Montag'
        'Dienstag'
        'Mittwoch'
        'Donnerstag'
        'Freitag'
        'Samstag'
        'Sonntag')
      JQSchedulerLocale.DialogViewEventFullDaysNames.Strings = (
        'Montag'
        'Dienstag'
        'Mittwoch'
        'Donnerstag'
        'Freitag'
        'Samstag'
        'Sonntag')
      JQSchedulerLocale.DialogViewEventMonthsNames.Strings = (
        'J'#228'nner'
        'Februar'
        'M'#228'rz'
        'April'
        'Mai'
        'Juni'
        'Juli'
        'August'
        'September'
        'Oktober'
        'November'
        'Dezember')
      JQSchedulerLocale.DialogViewEventFullMonthsNames.Strings = (
        'J'#228'nner'
        'Februar'
        'M'#228'rz'
        'April'
        'Mai'
        'Juni'
        'Juli'
        'August'
        'September'
        'Oktober'
        'November'
        'Dezember')
      OnGetNewEventID = iwcgSched_KalenderGetNewEventID
      OnSchedulerFrameControlsToEvent = iwcgSched_KalenderSchedulerFrameControlsToEvent
      OnSchedulerEventAdded = iwcgSched_KalenderSchedulerEventAdded
      OnSchedulerEventDeleting = iwcgSched_KalenderSchedulerEventDeleting
    end
    object iwrRightInner: TIWCGJQRegion
      Left = 718
      Top = 100
      Width = 300
      Height = 585
      Visible = False
      RenderInvisibleControls = True
      TabOrder = 12
      Version = '1.0'
      Align = alRight
      object iwrTodoErstellen: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 300
        Height = 129
        HorzScrollBar.Increment = 1
        RenderInvisibleControls = True
        TabOrder = 13
        Version = '1.0'
        Align = alTop
        object IWLabel1: TIWCGJQLabel
          Left = 6
          Top = 5
          Width = 56
          Height = 19
          Font.Color = clNone
          Font.Size = 12
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'To-Do:'
        end
        object jqbTodoSpeichern: TIWCGJQButton
          Left = 158
          Top = 91
          Width = 137
          Height = 35
          TabOrder = 21
          Version = '1.0'
          JQButtonOptions.Label_ = 'Speichern'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = jqbTodoSpeichernOnClick
        end
        object iwcgdtp_Faelligkeit: TIWCGJQDateTimePicker
          Left = 6
          Top = 33
          Width = 275
          Height = 24
          TabOrder = 23
          Css = 'ui-widget ui-widget-content ui-corner-all'
          Version = '1.0'
          Caption = 'F'#228'lligkeitsdatum'
          JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
        end
        object iwcged_todo: TIWCGJQEdit
          Left = 86
          Top = 6
          Width = 209
          Height = 21
          TabOrder = 18
          Font.Size = 12
          Version = '1.0'
          ScriptEvents = <>
          Text = ''
        end
      end
      object iwrTodoAnzeigen: TIWCGJQRegion
        Left = 0
        Top = 129
        Width = 300
        Height = 456
        RenderInvisibleControls = True
        TabOrder = 16
        Version = '1.0'
        Align = alClient
        object IWRegion10: TIWCGJQRegion
          Left = 0
          Top = 386
          Width = 300
          Height = 70
          RenderInvisibleControls = True
          TabOrder = 19
          Version = '1.0'
          Align = alBottom
          object IWLabel2: TIWCGJQLabel
            Left = 0
            Top = 0
            Width = 300
            Height = 25
            Align = alTop
            Font.Color = clNone
            Font.Size = 12
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel2'
          end
          object iwcgdtp_termintodo: TIWCGJQDateTimePicker
            Left = 6
            Top = 32
            Width = 121
            Height = 32
            TabOrder = 17
            Css = 'ui-widget ui-widget-content ui-corner-all'
            Version = '1.0'
            Caption = ''
            JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
          end
          object jqbTerminTodo: TIWCGJQButton
            Left = 148
            Top = 32
            Width = 129
            Height = 32
            TabOrder = 6
            Version = '1.0'
            JQButtonOptions.Label_ = 'Termin erstellen'
            JQButtonOptions.OnClick.OnEvent = jqbTerminTodoOnClick
          end
        end
        object IWRegion1: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 300
          Height = 386
          RenderInvisibleControls = True
          TabOrder = 14
          Version = '1.0'
          Align = alClient
          object iwplTodo: TIWCGPanelList
            Left = 0
            Top = 0
            Width = 300
            Height = 386
            TabOrder = 28
            Font.Size = 12
            Version = '1.0'
            Align = alClient
            Items = <>
            Caption = 'To-Do'#39's'
            HeaderOptions.Text = 'To-Do'#39's'
            OnClick = iwplTodoClick
          end
        end
      end
    end
  end
  object RegionMenuButtonsLeft: TIWCGJQRegion [2]
    Left = 0
    Top = 50
    Width = 280
    Height = 685
    HorzScrollBar.Style = ssFlat
    VertScrollBar.Color = clWhite
    VertScrollBar.ParentColor = False
    TabOrder = 15
    Version = '1.0'
    Align = alLeft
    Color = clWebSILVER
    object ButtonBetrieb: TIWCGJQButton
      Left = 0
      Top = 98
      Width = 280
      Height = 54
      Visible = False
      TabOrder = 30
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Betriebe'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbBetriebsfensterClick
    end
    object ButtonAdmin: TIWCGJQButton
      Left = 0
      Top = 197
      Width = 280
      Height = 46
      Visible = False
      TabOrder = 33
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Administrator'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbAdministratorfensterClick
    end
    object ButtonStammdaten: TIWCGJQButton
      Left = 0
      Top = 243
      Width = 280
      Height = 46
      Visible = False
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Stammdaten'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbStammdatenfensterClick
    end
    object ButtonVis: TIWCGJQButton
      Left = 0
      Top = 289
      Width = 280
      Height = 42
      Visible = False
      TabOrder = 1
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'VIS-Daten'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbVisClick
    end
    object ButtonFragen: TIWCGJQButton
      Left = 0
      Top = 331
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 2
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Fragen'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbFragenkatalogClick
    end
    object ButtonKontrollen: TIWCGJQButton
      Left = 0
      Top = 0
      Width = 280
      Height = 53
      Visible = False
      TabOrder = 29
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Kontrollen'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbKontrolleClick
    end
    object ButtonNachrichten: TIWCGJQButton
      Left = 0
      Top = 376
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 3
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Nachrichten'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbNachrichtenClick
    end
    object ButtonVersionInfo: TIWCGJQButton
      Left = 0
      Top = 466
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 4
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Version / Info'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbVersionInfoOnClick
    end
    object ButtonLogs: TIWCGJQButton
      Left = 0
      Top = 421
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 5
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Logs'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbLogsOnClick
    end
    object ButtonAMA: TIWCGJQButton
      Left = 0
      Top = 53
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 32
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'AMA-CCK'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwbAMACCKOnClick
    end
    object ButtonMonitoring: TIWCGJQButton
      Left = 0
      Top = 152
      Width = 280
      Height = 45
      Visible = False
      TabOrder = 31
      Version = '1.0'
      Align = alTop
      Color = clBtnFace
      JQButtonOptions.Label_ = 'Betriebsmonitoring'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonMonitoringJQButtonOptionsClick
    end
  end
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1298
    TabOrder = 7
    inherited ImageLogo: TIWImageFile
      Left = 1015
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 806
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 8
      JQButtonOptions.OnClick.AjaxAppend = False
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 735
    Width = 1298
    TabOrder = 20
  end
  object modalfenster: TIWModalWindow
    Title = 'Warnung'
    WindowWidth = 300
    WindowHeight = 180
    SizeUnit = suPixel
    Buttons.Strings = (
      '&Ja'
      '&Nein')
    CloseButtonVisible = False
    CloseOnEscKey = False
    OnAsyncClick = modalfensterAsyncClick
    Left = 743
    Top = 3
  end
end
