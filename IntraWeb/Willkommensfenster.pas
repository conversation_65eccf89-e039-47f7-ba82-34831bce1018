﻿unit Willkommensfenster;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, IWCGJQButton,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQLabel, IWCGJQRegion, Vcl.Controls, Vcl.Forms, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQShowMessage, Vcl.Imaging.jpeg,
  IWCompExtCtrls, Vcl.Imaging.pngimage, IWCGJQNotification, IWCompButton,
  Vcl.Imaging.GIFImg, IWCGJQImage, IWCGJQSweetAlert, Forms.Base, IWCGJQEdit, IWCGJQCarousel;

type
  TFrmWillkommen = class(TFormBase)
    laWillkommen: TIWCGJQLabel;
    PopupCookieHinweis: TIWCGJQShowMessage;
    ButtonWeiter: TIWCGJQButton;
    jqsaAlert: TIWCGJQSweetAlert;
    IWImageFile1: TIWImageFile;
    RegionCenter: TIWCGJQRegion;
    AlertError: TIWCGJQSweetAlert;
    procedure IWAppFormCreate(Sender: TObject);
    procedure iwcgbtn_WeiterJQButtonOptionsClick(
      Sender: TObject;
      AParams: TStringList);
    procedure IWAppFormShow(Sender: TObject);
    procedure IWImageFile1Click(Sender: TObject);
  public

    Procedure ShowMainForm;
  end;

implementation

uses FireDAC.Stan.Param, Forms.Main, dmmain, DX.Utils.Windows, ServerController, JQ.Helpers.Button;

{$R *.dfm}


procedure TFrmWillkommen.IWAppFormCreate(Sender: TObject);
begin
  inherited;
  ToolbarVisible := False;
end;

procedure TFrmWillkommen.IWAppFormShow(Sender: TObject);
begin
  PopupCookieHinweis.ResetJSonProps;
  PopupCookieHinweis.ShowNotification('Zur vollen Funktionsfähigkeit der Anwendung werden Cookies verwendet',
    jqsntLog, 10000);

  if UserSession.User = nil then
  begin
    AlertError.JQSweetAlertOptions.Text := UserSession.Error;
    AlertError.Show;
  end;
end;

procedure TFrmWillkommen.iwcgbtn_WeiterJQButtonOptionsClick(
  Sender: TObject;
  AParams: TStringList);
begin
  ShowMainForm;
end;

procedure TFrmWillkommen.IWImageFile1Click(Sender: TObject);
begin
  ShowMainForm;
end;

procedure TFrmWillkommen.ShowMainForm;
begin
  if UserSession.User <> nil then
  begin
    TFormMain.Create(self).Show;
  end
  else
  begin
    TerminateAndRestartSession;
  end;
end;

initialization
TFrmWillkommen.SetAsMainForm;

end.
