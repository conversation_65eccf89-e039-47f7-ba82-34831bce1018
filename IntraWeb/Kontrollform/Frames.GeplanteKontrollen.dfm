inherited FrameGeplanteKontrollen: TFrameGeplanteKontrollen
  Width = 1252
  Height = 782
  Align = alClient
  OnCreate = IWCGJQFrameCreate
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1252
    Height = 782
    RenderInvisibleControls = True
    TabOrder = 5
    object DialogPlanen: TIWCGJQDialog
      Left = 130
      Top = 61
      Width = 927
      Height = 540
      Visible = False
      TabOrder = 2
      Version = '1.0'
      Align = alNone
      ZIndex = 2000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.CloseOnEscape = False
      JQDialogOptions.Height = 540
      JQDialogOptions.Modal = True
      JQDialogOptions.Resizable = False
      JQDialogOptions.Title = 'Neue Kontrolle planen'
      JQDialogOptions.Width = 927
      JQDialogOptions.zIndex = 2000
    end
    object iwrBot: TIWCGJQRegion
      Left = 0
      Top = 732
      Width = 1252
      Height = 50
      RenderInvisibleControls = True
      TabOrder = 6
      Version = '1.0'
      Align = alBottom
      object LabelCount: TIWLabel
        Left = 177
        Top = 17
        Width = 0
        Height = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        NoWrap = True
        HasTabOrder = False
        FriendlyName = 'LabelCount'
      end
      object ButtonCSVExport: TIWCGJQButton
        Left = 17
        Top = 16
        Width = 150
        Height = 21
        TabOrder = 14
        Version = '1.0'
        JQButtonOptions.Label_ = 'Ergebnis exportieren'
        JQEvents.OnClick.OnEvent = ButtonCSVExportOnClick
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 80
      Width = 1252
      Height = 652
      RenderInvisibleControls = True
      TabOrder = 7
      Version = '1.0'
      Align = alClient
      object jqgKontrolluebersicht: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1252
        Height = 652
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Datum'
            Name = 'Datum'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Durchf'#252'hrungsdatum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollbericht Typ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrolltypTyp'
            Name = 'KontrolltypTyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll Typ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BetriebName'
            Name = 'BetriebName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betrieb'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GemeindeNummer'
            Name = 'GemeindeNummer'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gemeinde'
            Position = 11
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LFBIS'
            Name = 'LFBIS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'LFBIS'
            Position = 4
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebstyp'
            Name = 'Betriebstyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'Typ'
            Position = 5
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'RechtsgrundlageBez'
            Name = 'RechtsgrundlageBez'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollgrund'
            Position = 6
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ErfasserName'
            Name = 'ErfasserName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollorgan'
            Position = 7
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y H:i'
            Idx = 'AngemeldetUm'
            Name = 'AngemeldetUm'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Angemeldet Um'
            Position = 8
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrollInformationen'
            Name = 'KontrollInformationen'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll-Informationen'
            ProviderName = 'KontrollInformationen'
            Position = 9
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GruppeQuelle'
            Name = 'GruppeQuelle'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
            Position = 10
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Gruppe'
            Name = 'Gruppe'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ'
            Name = 'PLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'PLZ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Ort'
            Name = 'Ort'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ort'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Strasse'
            Name = 'Strasse'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Strasse'
          end>
        JQGridOptions.Height = 604
        JQGridOptions.LoadOnce = True
        JQGridOptions.RowNum = 200
        JQGridOptions.Sortable = True
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1250
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = jqgKontrolluebersichtJQGridOptionsLoadComplete
        JQGridOptions.OnSelectRow.Ajax = False
        JQGridOptions.OnSelectRow.AjaxAppend = False
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridOptions.PagerVisible = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderGeplanteKontrollen
        JQGridToolbarSearch.Active = True
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1252
      Height = 80
      RenderInvisibleControls = True
      TabOrder = 8
      Version = '1.0'
      Align = alTop
      object IWLabel4: TIWCGJQLabel
        Left = 17
        Top = 11
        Width = 29
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Von:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 173
        Top = 11
        Width = 23
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bis:'
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 329
        Top = 33
        Width = 100
        Height = 21
        TabOrder = 1
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQEvents.OnClick.Ajax = False
        JQEvents.OnClick.AjaxAppend = False
        JQEvents.OnClick.OnEvent = ButtonAbfragenOnClick
      end
      object jqdVon: TIWCGJQDatePicker
        Left = 17
        Top = 33
        Width = 150
        Height = 21
        TabOrder = 3
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object jqdBis: TIWCGJQDatePicker
        Left = 173
        Top = 33
        Width = 150
        Height = 21
        TabOrder = 4
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object IWCGJQRegion1: TIWCGJQRegion
        Left = 675
        Top = 0
        Width = 577
        Height = 80
        TabOrder = 9
        Version = '1.0'
        Align = alRight
        object IWCGJQButton1: TIWCGJQButton
          Left = 146
          Top = 34
          Width = 100
          Height = 21
          TabOrder = 10
          Version = '1.0'
          JQButtonOptions.Label_ = 'Weitergeben'
          JQButtonOptions.OnClick.OnEvent = ButtonWeitergebenOnClick
        end
        object IWCGJQButton2: TIWCGJQButton
          Left = 464
          Top = 34
          Width = 100
          Height = 21
          TabOrder = 11
          Version = '1.0'
          JQButtonOptions.Label_ = 'Stornieren'
          JQEvents.OnClick.OnEvent = ButtonStornierenOnClick
        end
        object IWCGJQButton3: TIWCGJQButton
          Left = 252
          Top = 34
          Width = 100
          Height = 21
          TabOrder = 12
          Version = '1.0'
          JQButtonOptions.Label_ = 'Bearbeiten'
          JQButtonOptions.OnClick.OnEvent = ButtonBearbeitenOnClick
        end
        object ButtonHinzufuegen: TIWCGJQButton
          Left = 358
          Top = 34
          Width = 100
          Height = 21
          TabOrder = 13
          Version = '1.0'
          JQButtonOptions.Label_ = 'Hinzuf'#252'gen'
          JQEvents.OnClick.OnEvent = ButtonHinzufuegenOnClick
        end
      end
    end
  end
  object DownloadGeplant: TIWCGJQFileDownload
    Version = '1.0'
    Left = 577
    Top = 17
  end
  object jqsAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 649
    Top = 18
  end
  object ProviderGeplanteKontrollen: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.MTGeplanteKontrollen
    KeyFields = 'ID'
    Left = 744
    Top = 16
  end
end
