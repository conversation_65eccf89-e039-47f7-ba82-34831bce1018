﻿unit Dialogs.Kontrolldetails;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton,
  IWCGJQRegion, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, Modules.Kontrolle,
  UserSessionUnit, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQGrid, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQDownload, IWCGJQFileDownload,
  IWCGJQSweet<PERSON><PERSON>t, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>WCompEdit, IWDBStdCtrls,
  IW.<PERSON>TT<PERSON>.Reply, IW.HTTP.Request, IWCompMemo, IWCGJQMemo;

type
  // ACHTUNG: Wegen einer Schwäche im DFM Designer ist hier ein kleiner Trick notwendig!
  // Um den den Designer aufrufen zu können, diese Zeile aktivieren:
  //TDialogKontrollDetails = class(TDialogBase)
  // Diese Zeile zum Kompilieren aktivieren:
  TDialogKontrollDetails = class(TDialogBase<TDMKontrolle>)
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    LabelUnterschriftVerweigert: TIWCGJQLabel;
    LabelBemerkung: TIWCGJQLabel;
    LblBetrieb: TIWCGJQLabel;
    LblAnwesende: TIWCGJQLabel;
    LblBetriebstyp: TIWCGJQLabel;
    LabelUnterschriftVerweigertInhalt: TIWCGJQLabel;
    ButtonAnwesende: TIWCGJQButton;
    ButtonBetrieb: TIWCGJQButton;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    LabelKontrollgrundBeschriftung: TIWCGJQLabel;
    LblKontrollgrund: TIWCGJQLabel;
    LblErfasser: TIWCGJQLabel;
    LblKontrollorgan: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    LblGeplantAm: TIWCGJQLabel;
    LblBegin: TIWCGJQLabel;
    LabelStornoDatumBeschriftung: TIWCGJQLabel;
    LabelStornoDatumInhalt: TIWCGJQLabel;
    LabelStornoGrundInhalt: TIWCGJQLabel;
    LabelStornoGrundBeschriftung: TIWCGJQLabel;
    IWLabel12: TIWCGJQLabel;
    LblEnde: TIWCGJQLabel;
    LblAngemeldet: TIWCGJQLabel;
    IWLabel16: TIWCGJQLabel;
    IWLabel13: TIWCGJQLabel;
    RegionAllgemein: TIWCGJQRegion;
    RegionMassnahmen: TIWCGJQRegion;
    GridMassnahmen: TIWCGJQGrid;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQRegion2: TIWCGJQRegion;
    ButtonMangelAbschliesen: TIWCGJQButton;
    ButtonFristVerlaengern: TIWCGJQButton;
    RegionProben: TIWCGJQRegion;
    GridProben: TIWCGJQGrid;
    IWCGJQRegion4: TIWCGJQRegion;
    IWLabel14: TIWCGJQLabel;
    IWCGJQRegion5: TIWCGJQRegion;
    ButtonAgesErgebnisbericht: TIWCGJQButton;
    ButtonProbenbegleitschein: TIWCGJQButton;
    ButtonEntfernen: TIWCGJQButton;
    ButtonHinzufuegen: TIWCGJQButton;
    ProviderMassnahmen: TIWCGJQGridDataSetProvider;
    ProviderProben: TIWCGJQGridDataSetProvider;
    Downloader: TIWCGJQFileDownload;
    DSMassnahmen: TDataSource;
    ButtonBilderAnzeigen: TIWCGJQButton;
    IWCGJQLabel1: TIWCGJQLabel;
    IWCGJQLabel2: TIWCGJQLabel;
    MemoInterneNotiz: TIWCGJQMemo;
    MemoKontrollInfo: TIWCGJQMemo;
    MemoBemerkung: TIWCGJQMemo;
    IWCGJQLabel3: TIWCGJQLabel;
    LabelGruppe: TIWCGJQLabel;
    ButtonKontrollbericht: TIWCGJQButton;
    procedure ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonAGESErgebnisOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonProbenbegleitscheinOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBetriebOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonAnwesendeOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonKontrollberichtOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonFirstVerlaengernOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonMangelAbschliessenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBilderAnzeigenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
  private
    IDKontrollbericht: integer;
  public
    constructor Create(AOwner: TComponent; LIDKontrollbericht: integer); reintroduce;
    procedure DoButtonCancel(ASender: TObject; AParams: TStringList); override;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}

uses Utility, Dialogs.ProbeErstellen, Dialogs.Betriebdetails, Dialogs.Anwesende, Dialogs.FristVerlaengern,
  Dialogs.MangelAbschliessen, Dialogs.MassnahmenBilder;

constructor TDialogKontrollDetails.Create(AOwner: TComponent; LIDKontrollbericht: integer);
begin
  self := inherited Create(AOwner);
  if LIDKontrollbericht > -1 then
  begin
    IDKontrollbericht := LIDKontrollbericht;
    DM.OpenKontrollDetails(LIDKontrollbericht);
    DM.OpenBetriebDetails(DM.QKontrollDetailsID_Betrieb.AsInteger);
    DM.OpenAnwesende(LIDKontrollbericht);

    DSMassnahmen.DataSet := DM.QMassnahmen;
    ProviderMassnahmen.DataSource := DSMassnahmen;
    ProviderProben.DataSet := DM.QProben;

        //RefBKB optional anzeigen
    var LRefBkb := DM.QKontrollDetailsRef_bkb.AsString.Trim;
    if LRefBkb > '' then begin
        LREfBkb := Format(' - Referenz BKB: %s', [LRefBkb]);
    end;


    IWFrameRegion.JQDialogOptions.Title := 'Kontrolle: ' + DM.QKontrollDetailsBKB.AsString + ' - ' +
      DM.QKontrollDetailsBkbBezeichnung.AsString + ' - ' + DM.QKontrollDetailsSTATUS.AsString + LRefBkb;

    LblAnwesende.Caption := IntToStr(DM.QAnwesende.RecordCount);
    ButtonAnwesende.Enabled := DM.QAnwesende.RecordCount > 0;

    LabelUnterschriftVerweigertInhalt.Caption := DM.QKontrollDetailsVerweigerungsgrund_unterschrift.AsString.Trim;
    LabelUnterschriftVerweigert.Visible := LabelUnterschriftVerweigertInhalt.Caption > '';

    LabelStornoDatumBeschriftung.Visible := true;
    LabelStornoGrundBeschriftung.Visible := true;
    if not DM.QKontrollDetailsstornogrund.IsNull then
    begin
      LabelStornoGrundInhalt.Caption := DM.QKontrollDetails.FieldByName('stornogrund').AsString;
      LabelStornoDatumBeschriftung.Caption := 'Storniert am:';
      LabelStornoDatumInhalt.Caption := FormatDateTime('dd.mm.yyyy hh:nn:ss',
        DM.QKontrollDetailsstorniert_am.AsDateTime);
    end
    else if not DM.QKontrollDetailsVerweigerunggrund.IsNull then
    begin
      LabelStornoGrundInhalt.Caption := DM.QKontrollDetailsVerweigerunggrund.AsString;
      LabelStornoDatumBeschriftung.Caption := 'Verweigert am:';
      LabelStornoDatumInhalt.Caption := FormatDateTime('dd.mm.yyyy hh:nn:ss',
        DM.QKontrollDetailsverweigert_am.AsDateTime);
    end
    else
    begin
      LabelStornoGrundInhalt.caption := '';
      LabelStornoDatumInhalt.Caption := '';
      LabelStornoDatumBeschriftung.Visible := false;
      LabelStornoGrundBeschriftung.Visible := false;
    end;

    MemoBemerkung.Lines.Text := DM.QKontrollDetailsKurzbemerkung.AsString;
    MemoKontrollInfo.Lines.Text := DM.QKontrollDetailsKONTROLL_INFORMATIONEN.AsString;
    MemoInterneNotiz.Lines.Text := DM.QKontrollDetailsINTERNE_NOTIZ.AsString;

    LblBetrieb.Caption := DM.QBetriebDetailName.AsString;
    LblBetriebstyp.Caption := DM.QKontrollDetailsBetriebstyp.AsString;
    LblKontrollorgan.Caption := DM.QKontrollDetailsKontrollorganTitel.AsString + ' ' +
      DM.QKontrollDetailsKontrollorganName.AsString;
    LblErfasser.Caption := DM.QKontrollDetailsErfasserTitel.AsString + ' ' + DM.QKontrollDetailsErfasserName.AsString;
    LblKontrollgrund.Caption := DM.QKontrollDetailsRechtsgrundlageBezeichnung.AsString;
    if not DM.QKontrollDetailsStartzeit.IsNull then
      LblBegin.Caption := DateTimeToStr(DM.QKontrollDetailsStartzeit.AsDateTime);
    if not DM.QKontrollDetailsEndezeit.IsNull then
      LblEnde.Caption := DateTimeToStr(DM.QKontrollDetailsEndezeit.AsDateTime);
    if not DM.QKontrollDetailsangemeldet_um.IsNull then
      LblAngemeldet.Caption := DateTimeToStr(DM.QKontrollDetailsangemeldet_um.AsDateTime);
  
    LabelGruppe.Caption := DM.QKontrollDetailsQuellGruppe.AsString;
  end;

  TDialogProbeErstellen.Create(self, -1);
  TDialogFristVerlaengern.Create(self, now);
  TDialogMangelAbschliessen.Create(self);
  TDialogAnwesende.Create(self, -1);
  TDialogBetriebdetails.Create(self, -1);
  TDialogMassnahmenBilder.Create(self, -1, -1);
end;

procedure TDialogKontrollDetails.ButtonAGESErgebnisOnClick(Sender: TObject; AParams: TStringList);
var
  ProbenId: integer;
begin
  if not moveQueryToRow(DM.QProben, GridProben) then
  begin
    Exit;
  end;
  ProbenId := DM.QProben.FieldByName('id').AsInteger;

  DM.DownloadAGESProbenErgebnis(ProbenId, Downloader, Alert);
end;

procedure TDialogKontrollDetails.ButtonAnwesendeOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  TDialogAnwesende.Create(RegionContent, IDKontrollbericht).Show(false);
end;

procedure TDialogKontrollDetails.ButtonBetriebOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  TDialogBetriebdetails.Create(self, DM.QKontrollDetailsID_Betrieb.AsInteger).Show(true);
end;

procedure TDialogKontrollDetails.ButtonBilderAnzeigenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
var
  LMassnahmeID: integer;
  LBilderCount: integer;
begin
  inherited;
  if not moveQueryToRow(DM.QMassnahmen, GridMassnahmen) then
  begin
    Alert.Warning('Keine Maßnahme ausgewählt.');
    Abort;
  end;
  LMassnahmeID := DM.QMassnahmenMangelID.Value;
  LBilderCount := DM.QMassnahmenBilder.Value;
  if LBilderCount > 0 then
  begin
    TDialogMassnahmenBilder.Create(self, LMassnahmeID, LBilderCount).Show(true);
  end
  else
  begin
    Alert.Warning('Keine Bilder vorhanden.');
  end;
end;

procedure TDialogKontrollDetails.ButtonFirstVerlaengernOnClick(Sender: TObject; AParams: TStringList);
var
  LDialog: TDialogFristVerlaengern;
  LFrist: TDate;
  LMassnahmeID: integer;
begin
  inherited;
  if not moveQueryToRow(DM.QMassnahmen, GridMassnahmen) then
    Abort;

  LFrist := DM.QMassnahmenFrist.Value;
  LMassnahmeID := DM.QMassnahmenMangelID.Value;
  LDialog := TDialogFristVerlaengern.Create(RegionContent, LFrist);
  LDialog.OnOk := procedure
    begin
      DM.FristVerlaengern(LMassnahmeID, LDialog.Frist, LDialog.Bemerkung);
      DM.OpenMassnahmenDetails(IDKontrollbericht);
    end;
  LDialog.Show(true);
end;

procedure TDialogKontrollDetails.ButtonMangelAbschliessenOnClick(Sender: TObject; AParams: TStringList);
var
  LMassnahmeID: integer;
  LDialog: TDialogMangelAbschliessen;
begin
  inherited;
  if not moveQueryToRow(DM.QMassnahmen, GridMassnahmen) then
    Abort;

  LMassnahmeID := DM.QMassnahmenMangelID.Value;
  LDialog := TDialogMangelAbschliessen.Create(RegionContent);
  LDialog.OnOk := procedure
    begin
      DM.MangelAbschliessen(LMassnahmeID, LDialog.Bemerkung, LDialog.Bilder);
      DM.OpenMassnahmenDetails(IDKontrollbericht);
    end;
  LDialog.Show(true);
end;

procedure TDialogKontrollDetails.ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
var
  LDialog: TDialogProbeErstellen;
begin
  inherited;
  LDialog := TDialogProbeErstellen.Create(self, IDKontrollbericht);
  LDialog.OnOk := procedure
    begin
      DM.OpenProbenDetails(IDKontrollbericht);
      GridProben.JQGridOptions.ReloadGrid;
    end;
  LDialog.Show(true);
end;

procedure TDialogKontrollDetails.ButtonKontrollberichtOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  DM.DownloadFile(DM.QKontrollDetailsGuid_Dokument.AsGuid, Downloader, Alert);
end;

procedure TDialogKontrollDetails.ButtonProbenbegleitscheinOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if not moveQueryToRow(DM.QProben, GridProben) then
    Abort;
  DM.DownloadFile(DM.QProbenGUID_Dokument.AsGuid, Downloader, Alert);
end;

procedure TDialogKontrollDetails.DoButtonCancel(ASender: TObject; AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogKontrollDetails.DoButtonOK(ASender: TObject; AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogKontrollDetails.InitializeControls;
begin
  inherited;
  // todo 1 -IoH -cImplementieren: Felder initialisieren
end;

end.
