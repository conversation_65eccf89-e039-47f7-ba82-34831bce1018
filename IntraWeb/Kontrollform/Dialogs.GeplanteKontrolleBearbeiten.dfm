inherited DialogGeplanteKontrolleBearbeiten: TDialogGeplanteKontrolleBearbeiten
  Width = 648
  Height = 610
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 648
    Height = 610
    TabOrder = 10
    JQDialogOptions.Height = 610
    JQDialogOptions.Title = 'Geplante Kontrolle'
    JQDialogOptions.Width = 648
    inherited RegionContent: TIWCGJQRegion
      Width = 648
      Height = 550
      TabOrder = 1
      object IWLabel1: TIWCGJQLabel
        Left = 11
        Top = 24
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollbericht Typ:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 11
        Top = 64
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontroll Typ:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 11
        Top = 89
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Betrieb:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 11
        Top = 176
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollgrund:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 11
        Top = 216
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Geplantes Datum:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 11
        Top = 256
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Angemeldet:'
      end
      object jqcAngemeldetUm: TIWCGJQCheckBox
        Left = 201
        Top = 256
        Width = 24
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 5
        OnAsyncChange = AngemeldetOnAsyncChange
        Checked = False
        FriendlyName = 'jqcAngemeldetUm'
      end
      object LabelBkbtyp: TIWCGJQLabel
        Left = 201
        Top = 27
        Width = 419
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
      end
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 13
        Top = 299
        Width = 168
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelAngemeldet'
        Caption = 'Kontroll-Informationen:'
      end
      object IWCGJQLabel2: TIWCGJQLabel
        Left = 83
        Top = 411
        Width = 98
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelNotiz'
        Caption = 'Interne Notiz:'
      end
      object DateAngemeldetUm: TIWCGJQDateTimePicker
        Left = 231
        Top = 256
        Width = 389
        Height = 21
        TabOrder = 5
        Css = 'ui-widget ui-widget-content ui-corner-all'
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorheriger'
        JQDatePickerOptions.Regional = dporGerman
      end
      object ComboBoxKontrollgrund: TIWCGJQComboBoxEx
        Left = 200
        Top = 176
        Width = 420
        Height = 21
        TabOrder = 6
        Version = '1.0'
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        Caption = ''
      end
      object DateGeplantesDatum: TIWCGJQDatePicker
        Left = 201
        Top = 216
        Width = 420
        Height = 21
        TabOrder = 7
        Version = '1.0'
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
      end
      object ComboKontrolltyp: TIWCGJQComboBoxEx
        Left = 201
        Top = 64
        Width = 419
        Height = 21
        TabOrder = 9
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 417
        Caption = ''
      end
      object MemoBetrieb: TIWCGJQMemoEx
        Left = 201
        Top = 91
        Width = 420
        Height = 79
        TabOrder = 11
        Css = ''
        Version = '1.0'
        ZIndex = 0
        Style.Strings = (
          'width:420px !important')
        BGColor = clNone
        Editable = False
        Required = False
        SubmitOnAsyncEvent = True
        ReadOnly = True
      end
      object MemoKontrollInformationen: TIWCGJQMemoEx
        Left = 200
        Top = 299
        Width = 419
        Height = 90
        TabOrder = 12
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:420px !important')
      end
      object MemoInterneNotizen: TIWCGJQMemoEx
        Left = 200
        Top = 411
        Width = 419
        Height = 90
        TabOrder = 13
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:420px !important')
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 560
      Width = 646
      TabOrder = 8
      inherited ButtonCancel: TIWCGJQButton
        Left = 538
        TabOrder = 2
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 530
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          Left = 422
          TabOrder = 3
        end
      end
    end
  end
end
