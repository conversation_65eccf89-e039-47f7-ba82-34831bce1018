﻿unit AgesXmlFrame;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGPanelList, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQRegion;

type
  TAgesXML = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwplProben: TIWCGPanelList;
    quGetKontrollId: TFDQuery;
  private
    { Private declarations }
  public
    { Public declarations }
    probenBkbs: TStringList;
    procedure ShowProben(proben: TStringList; probenBkbs: TStringList);
    procedure ProbenOnClick(Sender: TObject; AParams: TStringList);
    constructor Create(aOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation

uses Utility, DetailFenster, dmmain;

{$R *.dfm}

constructor TAgesXML.Create(aOwner: TComponent);
begin
  inherited;
  probenBkbs := Nil;
end;

destructor TAgesXML.Destroy;
begin
  probenBkbs.Free;
  inherited;
end;

procedure TAgesXML.ShowProben(proben: TStringList; probenBkbs: TStringList);
var
  probe: string;
  item: TIWCGPanelListItem;
begin
  if Assigned(Self.probenBkbs) then begin
    FreeAndNil(Self.probenBkbs);
  end;
  Self.probenBkbs := probenBkbs;

  iwplProben.Items.Clear;
  for probe in proben do
  begin
    item := iwplProben.Items.Add;
    item.Caption := probe;
    item.OnClick.Ajax := true;
    item.OnClick.OnEvent := ProbenOnClick;
  end;
  iwplProben.AjaxReRender(false, true);
end;

procedure TAgesXML.ProbenOnClick(Sender: TObject; AParams: TStringList);
var
  index, id: integer;
  bkb: string;
begin
  index := GetIndexFromPanelListOnClick(AParams);
  bkb := probenBkbs[index];

  quGetKontrollId.Close;
  quGetKontrollId.ParamByName('probenbkb').AsString := bkb;
  quGetKontrollId.Open;
  id := quGetKontrollId.FieldByName('id').AsInteger;

  TDetailFrm.Create(Self, id).Show;
end;

end.