﻿unit DetailFenster;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, IWCompLabel,
  IWRegion, IWCGJQButton, Vcl.Imaging.jpeg, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls, Vcl.Forms,
  IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container,
  IWCGJQControl, IWCGJQRegion, Forms.Base,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCompCheckbox, IWCompText, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQGrid, IWCGJQComp, IWCGJQDownload,
  IWCGJQFileDownload, IWCGJQSweetAlert, frxClass, frxExportBaseDialog,
  frxExportPDF, frxDBSet, IWCGJQDialog, IWCompListbox, IWCGJQDatePicker, IWCGJQLabel;

type
  TDetailFrm = class(TFormBase)
    iwrLeft: TIWCGJQRegion;
    iwrRight: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    quKontrollDetails: TFDQuery;
    iwrInfo: TIWCGJQRegion;
    jqgKontrolldetails: TIWCGJQGrid;
    jqdpKontrollDetails: TIWCGJQGridDataSetProvider;
    iwrProben: TIWCGJQRegion;
    IWLabel2: TIWCGJQLabel;
    jqgProben: TIWCGJQGrid;
    quProben: TFDQuery;
    jqdpProben: TIWCGJQGridDataSetProvider;
    IWRegion1: TIWCGJQRegion;
    jqbProbenPdf: TIWCGJQButton;
    quProbenPdf: TFDQuery;
    jqdDownload: TIWCGJQFileDownload;
    jqsAlert: TIWCGJQSweetAlert;
    IWRegion2: TIWCGJQRegion;
    iwrMaengel: TIWCGJQRegion;
    IWRegion4: TIWCGJQRegion;
    IWLabel3: TIWCGJQLabel;
    jqgMaengel: TIWCGJQGrid;
    jqdpMaengel: TIWCGJQGridDataSetProvider;
    quMaengel: TFDQuery;
    jqbProbenbegleitschein: TIWCGJQButton;
    IWRegion3: TIWCGJQRegion;
    jqbMangelAbschließen: TIWCGJQButton;
    quMangelAbschliessen: TFDQuery;
    IWRegion5: TIWCGJQRegion;
    IWLabel4: TIWCGJQLabel;
    IWRegion6: TIWCGJQRegion;
    jqbKontrollberichtAnzeigen: TIWCGJQButton;
    quExistierenBewFragen: TFDQuery;
    jqdFristVerlaengern: TIWCGJQDialog;
    IWLabel1: TIWCGJQLabel;
    jqbMaengelAktualisieren: TIWCGJQButton;
    jqbFristVerlaengern: TIWCGJQButton;
    jqdFrist: TIWCGJQDatePicker;
    quFristVerlaengern: TFDQuery;
    quKontrollDetailsBkb: TStringField;
    quKontrollDetailsBkbtyp: TStringField;
    quKontrollDetailsKontrolltyp: TStringField;
    quKontrollDetailsDatum: TDateField;
    quKontrollDetailsTstamp_Insert: TSQLTimeStampField;
    quKontrollDetailsErfasser_Vorname: TStringField;
    quKontrollDetailsErfasser_Nachname: TStringField;
    quKontrollDetailsErfasser: TStringField;
    quKontrollDetailsKontrollorgan_Vorname: TStringField;
    quKontrollDetailsKontrollorgan_Nachname: TStringField;
    quKontrollDetailsKontrollorgan: TStringField;
    quKontrollDetailsProbenziehung: TBooleanField;
    quKontrollDetailsBetriebsname: TStringField;
    quKontrollDetailsStartzeit: TSQLTimeStampField;
    quKontrollDetailsEndezeit: TSQLTimeStampField;
    quKontrollDetailsVerweigerunggrund: TStringField;
    quKontrollDetailsAngemeldet_Um: TSQLTimeStampField;
    quKontrollDetailsRechtsgrundlage: TStringField;
    quKontrollDetailsStatus: TStringField;
    quKontrollDetailsbetriebstyp: TStringField;
    quKontrollDetailslastchange: TSQLTimeStampField;
    quKontrollDetailsRef_bkb: TStringField;
    quKontrollDetailsBetriebID: TIntegerField;
    quKontrollDetailsKontrollorganID: TFDAutoIncField;
    quKontrollDetailsguid_dokument: TGuidField;
    quKontrollDetailsid: TFDAutoIncField;
    quProbenId: TFDAutoIncField;
    quProbenProbenbkb: TStringField;
    quProbenProbenart: TStringField;
    quProbenBemerkung: TStringField;
    quProbenDatum: TDateField;
    quProbenVorg_Menge: TStringField;
    quProbenBeschaffenheit: TStringField;
    quProbenFuttertyp: TStringField;
    quProbenVerwendungszweck: TStringField;
    quProbenTier_ARt_Lisa: TStringField;
    quProbenTier_Kategorie: TStringField;
    quProbenBeimischrate: TBCDField;
    quProbenVerpackung: TStringField;
    quProbenVerschluss: TStringField;
    quProbenVersiegelt: TStringField;
    quProbenHerk_Zukauf: TStringField;
    quProbenUntersuchungsauftrag: TStringField;
    quProbenSTatus: TStringField;
    quProbenVerdacht: TStringField;
    quProbenGegenprobe_Belassen: TBooleanField;
    quProbenExportname: TStringField;
    quProbenExporttime: TSQLTimeStampField;
    quProbenAgesauftragsnummer: TStringField;
    quProbenagesprobennummer: TStringField;
    quProbenAges_Auftragsstatus: TStringField;
    quProbenAges_Probenstatus: TStringField;
    quProbenProbenbezeichnung: TStringField;
    quMaengelID: TFDAutoIncField;
    quMaengelGUID: TGuidField;
    quMaengelID_Mangeltyp: TIntegerField;
    quMaengelID_Massnahme: TIntegerField;
    quMaengelFrist: TDateField;
    quMaengelText: TMemoField;
    quMaengelBeseitigt_am: TDateField;
    quMaengelStatus: TStringField;
    quMaengelMangeltypBez: TStringField;
    quMaengelMassnahmeBez: TMemoField;
    quMaengeliD_1: TFDAutoIncField;
    quMaengelFrist_1: TDateField;
    quKontrollDetailsKurzbemerkung: TWideMemoField;
    procedure JqbZurueckOnClick(Sender: TObject;
      AParams: TStringList);
    constructor Create(aOwner: TComponent; id: Integer); reintroduce;
    procedure JqbProbenPdfOnClick(Sender: TObject; AParams: TStringList);
    procedure JqbProbenbegleitscheinOnClick(Sender: TObject;
      AParams: TStringList);
    procedure JqbMangelAbschließenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure JqbKontrollberichtAnzeigenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure JqbMaengelAktualisierenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure JqbFristVerlaengernOnClick(Sender: TObject; AParams: TStringList);
  public

  private
    id: Integer;
    procedure InitData;
    procedure InitMaengel;
    procedure DialogAbbrechen(Sender: TObject; AURLParams: TStringList);
    procedure FristVerlaengern(Sender: TObject; AParams: TStringList);
  end;

implementation

uses dmmain, Utility, DX.Utils.Windows, Vcl.Graphics, System.Variants,
  XData.Client, ELKE.Services.Me.Intf, DX.Classes.ObjectFactory,
  ELKE.Classes.PVP.Token, IW.HTTP.Request, ServerController,
  ELKE.Classes.Generated;

{$R *.dfm}


constructor TDetailFrm.Create(aOwner: TComponent; id: Integer);
begin
  inherited Create(aOwner);
  Self.id := id;

  try
    InitData;
  except
    Alert.Error('Es gab ein Problem beim Abrufen der Daten.');
  end;
end;

procedure TDetailFrm.InitData;
begin
  quKontrollDetails.Close;
  quKontrollDetails.ParamByName('id').AsInteger := id;
  quKontrollDetails.Open;

  quProben.Close;
  quProben.ParamByName('id').AsInteger := id;
  quProben.Open;

  InitMaengel;
end;

procedure TDetailFrm.InitMaengel;
begin
  quMaengel.Close;
  quMaengel.ParamByName('kontrollbericht').AsInteger := id;
  quMaengel.Open;
end;

procedure TDetailFrm.JqbMaengelAktualisierenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  InitMaengel;
end;

// Laded die Ages-Proben-Pdf herunter
procedure TDetailFrm.JqbProbenPdfOnClick(Sender: TObject; AParams: TStringList);
var
  filename: string;
  probenId: Integer;
  Data: TArray<byte>;
  Stream: TStream;
begin
  if not moveQueryToRow(quProben, jqgProben) then
  begin
    Exit;
  end;
  probenId := quProben.FieldByName('id').AsInteger;
  quProbenPdf.Close;
  quProbenPdf.ParamByName('id').AsInteger := probenId;
  quProbenPdf.Open;

  if quProbenPdf.FieldByName('Ergebnis_Pdf').IsNull then
  begin
    jqsAlert.Info('Diese Probe hat noch kein Ergebnis.');
    Exit;
  end;
  filename := 'exports/' + quProbenPdf.FieldByName('Probenbkb').AsString + '.pdf';
  Data := quProbenPdf.FieldByName('Ergebnis_Pdf').AsBytes;
  Stream := TFileStream.Create(filename, fmCreate);
  try
    Stream.WriteBuffer(Data, Length(Data));
    jqdDownload.DownloadFileName(filename);
  finally
    Stream.Free;
  end;
end;

// ******************************************************************************
// ***********************************Mängel*************************************
// ******************************************************************************

procedure TDetailFrm.JqbFristVerlaengernOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(quMaengel, jqgMaengel) then
  begin
    Exit;
  end;
  jqdFrist.Date := quMaengel.FieldByName('frist').AsDateTime;
  DialogErstellen(jqdFristVerlaengern, FristVerlaengern, DialogAbbrechen,
    'Frist verlängern', 'Verlängern');
end;

procedure TDetailFrm.FristVerlaengern(Sender: TObject;
  AParams: TStringList);
begin
  quFristVerlaengern.Close;
  quFristVerlaengern.ParamByName('id').AsInteger :=
    quMaengel.FieldByName('id').AsInteger;
  quFristVerlaengern.ParamByName('frist').AsDate := jqdFrist.Date;
  quFristVerlaengern.Execute;
  jqdFristVerlaengern.Visible := false;
  InitMaengel;
end;

procedure TDetailFrm.JqbMangelAbschließenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(quMaengel, jqgMaengel) then
  begin
    Exit;
  end;
  quMangelAbschliessen.Close;
  quMangelAbschliessen.ParamByName('id').AsInteger :=
    quMaengel.FieldByName('id').AsInteger;
  quMangelAbschliessen.ParamByName('beseitigt_am').AsDate := Now;
  quMangelAbschliessen.Execute;
  InitMaengel;
end;

procedure TDetailFrm.DialogAbbrechen(Sender: TObject; AURLParams: TStringList);
begin
  jqdFristVerlaengern.Visible := false;
end;

// ******************************************************************************
// **********************Reports erzeugen und herunterladen**********************
// ******************************************************************************

// Erzeuge und downloade den Probenbegleitschein
procedure TDetailFrm.JqbProbenbegleitscheinOnClick(Sender: TObject;
  AParams: TStringList);
begin
  moveQueryToRow(quProben, jqgProben);
  // Todo 1 -oCH -cTesten: Wird der Probenbegleitschein hier wirklich herunter geladen?
end;

procedure TDetailFrm.JqbKontrollberichtAnzeigenOnClick(Sender: TObject;
  AParams: TStringList);
var
  filename: string;
  service: IMe;
  dokumentGuid: TGuid;
  dokument: TDokument;
  Stream: TStream;
begin
  service := UserSession.ELKERest.GetMeService;
  try
    dokumentGuid := StringToGUID
      (quKontrollDetails.FieldByName('guid_dokument').AsString);
    dokument := service.dokument(dokumentGuid);
    filename := dokument.Dateiname;

    Stream := TFileStream.Create(filename, fmCreate);
    try
      dokument.dokument.SaveToStream(Stream);
      // Stream.WriteBuffer(data, Length(data));
      jqdDownload.DownloadFileName(filename);
    finally
      Stream.Free;
    end;

  except
    on E: Exception do
    begin
      jqsAlert.Info('Für diesen Kontrollbericht gibt es noch keinen ' +
        'Kontrollbericht');
    end;
  end;
end;

procedure TDetailFrm.JqbZurueckOnClick(Sender: TObject;
  AParams: TStringList);
begin
  Release;
end;

end.
