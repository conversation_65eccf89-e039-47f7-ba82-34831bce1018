﻿unit Modules.Kontrolle;

interface

uses
  System.SysUtils, ELKE.Classes.Generated, System.Generics.Collections,
  System.Classes, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQDownload, IWCGJQFileDownload,
  IWCGJQSweetAlert, IWCGJQGrid, DX.Classes.Strings, FireDAC.UI.Intf, FireDAC.VCLUI.Error, FireDAC.Comp.UI,
  ELKE.Classes;

type
  TKontrollberichtItem = class(TCollectionItem)
  private
    FId: Integer;
    FBkb: string;
    FDatum: string;
    FRefBkb: string;
    FProbenziehung: Boolean;
    FRegnrOrt: string;
    FKurzbemerkung: string;
    FStartzeit: TDateTime;
    FEndezeit: TDateTime;
    FBestaetigtUm: TDateTime;
    FVerweigerunggrund: string;
    FAngemeldetUm: TDateTime;
    FTstampInsert: TDateTime;
    FLastchange: TDateTime;
    FBetriebName: string;
    FRechtsgrundlageBez: string;
    FBetrieb: TBetrieb;
    FErfasser: TPerson;
    FKontrollorgan: TPerson;
    FKontrolltyp: TKontrolltyp;
    FRechtsgrundlage: TRechtsgrundlage;
    FErfasserName: string;
    FKontrollorganName: string;
    FTodos: TList<TTodo>;
    FKontrolltypTyp: string;
    FBkbtyp: string;
    FStatus: string;
    FOrt: string;
  published
    property Id: Integer read FId write FId;
    property Bkb: string read FBkb write FBkb;
    property Datum: string read FDatum write FDatum;
    property RefBkb: string read FRefBkb write FRefBkb;
    property Probenziehung: Boolean read FProbenziehung write FProbenziehung;
    property RegnrOrt: string read FRegnrOrt write FRegnrOrt;
    property Kurzbemerkung: string read FKurzbemerkung write FKurzbemerkung;
    property Startzeit: TDateTime read FStartzeit write FStartzeit;
    property Endezeit: TDateTime read FEndezeit write FEndezeit;
    property BestaetigtTstamp: TDateTime read FBestaetigtUm write FBestaetigtUm;
    property Verweigerunggrund: string read FVerweigerunggrund
      write FVerweigerunggrund;
    property AngemeldetUm: TDateTime read FAngemeldetUm write FAngemeldetUm;
    property TstampInsert: TDateTime read FTstampInsert write FTstampInsert;
    property Lastchange: TDateTime read FLastchange write FLastchange;
    property BetriebName: string read FBetriebName write FBetriebName;
    property Betrieb: TBetrieb read FBetrieb write FBetrieb;
    property Erfasser: TPerson read FErfasser write FErfasser;
    property Kontrollorgan: TPerson read FKontrollorgan write FKontrollorgan;
    property Kontrolltyp: TKontrolltyp read FKontrolltyp write FKontrolltyp;
    property Rechtsgrundlage: TRechtsgrundlage read FRechtsgrundlage
      write FRechtsgrundlage;
    property RechtsgrundlageBez: string read FRechtsgrundlageBez
      write FRechtsgrundlageBez;
    property ErfasserName: string read FErfasserName write FErfasserName;
    property KontrollorganName: string read FKontrollorganName
      write FKontrollorganName;
    property Todos: TList<TTodo>read FTodos write FTodos;
    property KontrolltypTyp: string read FKontrolltypTyp write FKontrolltypTyp;
    property Bkbtyp: string read FBkbtyp write FBkbtyp;
    property Status: string read FStatus write FStatus;
    property Ort: string read FOrt write FOrt;
  end;

type
  TKontrollberichte = class(TCollection)
  private
    function GetItem(Index: Integer): TKontrollberichtItem;
  public
    function Add: TKontrollberichtItem;
    property Item[Index: Integer]: TKontrollberichtItem read GetItem;
  end;

type
  TDMKontrolle = class(TDataModule)
    QProben: TFDQuery;
    QProbenID: TFDAutoIncField;
    QProbenGUID: TGuidField;
    QProbenProbenbkb: TStringField;
    QProbenProbenart: TStringField;
    QProbenBemerkung: TStringField;
    QProbenDatum: TDateField;
    QProbenVerdacht: TStringField;
    QProbenAges_Probenstatus: TStringField;
    QMassnahmen: TFDQuery;
    QMassnahmenMassnahme: TMemoField;
    QMassnahmenFrist: TDateField;
    QMassnahmenBeschreibung: TMemoField;
    QMassnahmenBeseitigt_am: TDateField;
    QMassnahmenMangel: TStringField;
    QMassnahmenMangelID: TFDAutoIncField;
    QMassnahmenMangeltypID: TFDAutoIncField;
    QKontrollDetails: TFDQuery;
    QKontrollDetailsID: TFDAutoIncField;
    QKontrollDetailsGUID: TGuidField;
    QKontrollDetailsBkb: TStringField;
    QKontrollDetailsBkbtyp: TStringField;
    QKontrollDetailsKontrolltyp: TStringField;
    QKontrollDetailsDatum: TDateField;
    QKontrollDetailsID_Person_Erfasser: TIntegerField;
    QKontrollDetailsID_Person_Kontrollorgan: TIntegerField;
    QKontrollDetailsRef_bkb: TStringField;
    QKontrollDetailsProbenziehung: TBooleanField;
    QKontrollDetailsID_Betrieb: TIntegerField;
    QKontrollDetailsStartzeit: TSQLTimeStampField;
    QKontrollDetailsEndezeit: TSQLTimeStampField;
    QKontrollDetailsbestaetigt_um: TSQLTimeStampField;
    QKontrollDetailsangemeldet_um: TSQLTimeStampField;
    QKontrollDetailsBetriebstyp: TStringField;
    QKontrollDetailsVerweigerungsgrund_unterschrift: TStringField;
    QKontrollDetailsGuid_Dokument: TGuidField;
    QKontrollDetailsfehlerhaft_gesetzt_am: TSQLTimeStampField;
    QKontrollDetailsstorniert_am: TSQLTimeStampField;
    QKontrollDetailsstornogrund: TStringField;
    QKontrollDetailsverweigert_am: TSQLTimeStampField;
    QKontrollDetailsVerweigerunggrund: TStringField;
    QKontrollDetailsBkbBezeichnung: TStringField;
    QKontrollDetailsKontrolltypBezeichnung: TStringField;
    QKontrollDetailsRechtsgrundlageBezeichnung: TStringField;
    QKontrollDetailsRechtsgrundlageKurzbezeichnung: TStringField;
    QKontrollDetailsStatus: TStringField;
    QKontrollDetailsKontrollorganName: TStringField;
    QKontrollDetailsErfasserName: TStringField;
    QKontrollDetailsKontrollorganTitel: TStringField;
    QKontrollDetailsErfasserTitel: TStringField;
    QESADomWerte: TFDQuery;
    QESADomWerteCode: TStringField;
    QESADomWerteText: TStringField;
    quProbenPdf: TFDQuery;
    QBetriebZulassung: TFDQuery;
    QBetriebDetail: TFDQuery;
    QBetriebZulassungID: TIntegerField;
    QBetriebZulassungREGNR: TStringField;
    QBetriebZulassungZULNR: TStringField;
    QBetriebZulassungBEGINNDATUM: TDateField;
    QBetriebZulassungENDDATUM: TDateField;
    QBetriebZulassungAKTIV: TSmallintField;
    QBetriebZulassungSICHTBAR: TSmallintField;
    QBetriebDetailRegnr: TStringField;
    QBetriebDetailVulgo: TStringField;
    QBetriebDetailKatastralgemname: TStringField;
    QBetriebDetailtelefon: TStringField;
    QBetriebDetailemail: TStringField;
    QBetriebDetailAufsichtsorgan: TStringField;
    QBetriebDetailAnmerkung: TWideStringField;
    QBetriebDetailStrasse: TWideStringField;
    QBetriebDetailOrt: TWideStringField;
    QBetriebDetailPLZ: TStringField;
    QBetriebDetailName: TStringField;
    QAnwesende: TFDQuery;
    QAnwesendeGUID: TGuidField;
    QAnwesendeID_KONTROLLBERICHT: TIntegerField;
    QAnwesendeNAME: TStringField;
    QAnwesendeEMAIL: TStringField;
    QAnwesendeKOMMUNIKATIONSBERECHTIGT: TBooleanField;
    QProbenGUID_Dokument: TGuidField;
    QDokument: TFDQuery;
    QDokumentDateiname: TWideStringField;
    QDokumentDokument: TBlobField;
    MTKontrollverlauf: TFDMemTable;
    MTGeplanteKontrollen: TFDMemTable;
    MTUngeplanteKontrollen: TFDMemTable;
    MTGruppenPersonen: TFDMemTable;
    QFristVerlaengern: TFDQuery;
    QMassnahmenMassnahmeID: TFDAutoIncField;
    QMangelAbschliessen: TFDQuery;
    QMassnahmenBehebungsauftrag: TStringField;
    QMassnahmenStatus: TStringField;
    QMassnahmenID: TFDAutoIncField;
    quProbenPdfProbenbkb: TStringField;
    quProbenPdfGUID_DOKUMENT_AGES_ERGEBNIS: TGuidField;
    DSProbenPDF: TDataSource;
    QProbenPDFDokument: TFDQuery;
    QProbenPDFDokumentGUID: TGuidField;
    QProbenPDFDokumentBLDCODE: TSmallintField;
    QProbenPDFDokumentBEZEICHNUNG: TWideStringField;
    QProbenPDFDokumentTYP: TStringField;
    QProbenPDFDokumentDATEINAME: TWideStringField;
    QProbenPDFDokumentERSTELLT_AM: TSQLTimeStampField;
    QProbenPDFDokumentDOKUMENT: TBlobField;
    MTUngeplanteKontrollenID: TIntegerField;
    MTUngeplanteKontrollenBkb: TStringField;
    MTUngeplanteKontrollenRefBkb: TStringField;
    MTUngeplanteKontrollenProbenziehung: TBooleanField;
    MTUngeplanteKontrollenRegnrOrt: TStringField;
    MTUngeplanteKontrollenBestaetigtUm: TDateTimeField;
    MTUngeplanteKontrollenverweigerunggrund: TStringField;
    MTUngeplanteKontrollenAngemeldetUm: TDateTimeField;
    MTUngeplanteKontrollenBetriebName: TStringField;
    MTUngeplanteKontrollenRechtsgrundlageBez: TStringField;
    MTUngeplanteKontrollenErfasserName: TStringField;
    MTUngeplanteKontrollenKontrollorganName: TStringField;
    MTUngeplanteKontrollenKontrolltypTyp: TStringField;
    MTUngeplanteKontrollenBkbtyp: TStringField;
    MTUngeplanteKontrollenStatus: TStringField;
    MTUngeplanteKontrollenOrt: TStringField;
    MTUngeplanteKontrollenGuid: TGuidField;
    MTUngeplanteKontrollenStartzeit: TDateTimeField;
    MTUngeplanteKontrollenEndezeit: TDateTimeField;
    MTUngeplanteKontrollenLFBIS: TStringField;
    MTUngeplanteKontrollenBetriebstyp: TStringField;
    QMangelbildSpeichern: TFDQuery;
    QMangelbildSpeichernID: TGuidField;
    QMangelbildSpeichernBILD: TBlobField;
    QMangelbildSpeichernFORMAT: TStringField;
    QMangelbildSpeichernID_BEWERTETEFRAGE: TIntegerField;
    QMangelbildSpeichernID_MANGEL: TIntegerField;
    QMangelbildSpeichernID_PROBE: TIntegerField;
    QMangelbildSpeichernBEMERKUNG: TMemoField;
    QMangelbildSpeichernAUFNAHMEDATUM: TSQLTimeStampField;
    QMangelbildSpeichernID_AUFGENOMMEN_VON: TIntegerField;
    MTUngeplanteKontrollenDatum: TDateField;
    MTUngeplanteKontrollenFaelligkeitsdatum: TDateField;
    MTGeplanteKontrollenID: TIntegerField;
    MTGeplanteKontrollenBkb: TStringField;
    MTGeplanteKontrollenDatum: TDateField;
    MTGeplanteKontrollenRefBkb: TStringField;
    MTGeplanteKontrollenProbenziehung: TBooleanField;
    MTGeplanteKontrollenRegnrOrt: TStringField;
    MTGeplanteKontrollenBestaetigtUm: TDateTimeField;
    MTGeplanteKontrollenverweigerunggrund: TStringField;
    MTGeplanteKontrollenAngemeldetUm: TDateTimeField;
    MTGeplanteKontrollenBetriebName: TStringField;
    MTGeplanteKontrollenRechtsgrundlageBez: TStringField;
    MTGeplanteKontrollenErfasserName: TStringField;
    MTGeplanteKontrollenKontrollorganName: TStringField;
    MTGeplanteKontrollenKontrolltypTyp: TStringField;
    MTGeplanteKontrollenBkbtyp: TStringField;
    MTGeplanteKontrollenStatus: TStringField;
    MTGeplanteKontrollenOrt: TStringField;
    MTGeplanteKontrollenFaelligkeitsdatum: TDateField;
    MTGeplanteKontrollenGuid: TGuidField;
    MTGeplanteKontrollenStartzeit: TDateTimeField;
    MTGeplanteKontrollenEndezeit: TDateTimeField;
    MTGeplanteKontrollenLFBIS: TStringField;
    MTGeplanteKontrollenBetriebstyp: TStringField;
    MTKontrollverlaufID: TIntegerField;
    MTKontrollverlaufBkb: TStringField;
    MTKontrollverlaufDatum: TDateField;
    MTKontrollverlaufRefBkb: TStringField;
    MTKontrollverlaufProbenziehung: TBooleanField;
    MTKontrollverlaufRegnrOrt: TStringField;
    MTKontrollverlaufBestaetigtUm: TDateTimeField;
    MTKontrollverlaufverweigerunggrund: TStringField;
    MTKontrollverlaufAngemeldetUm: TDateTimeField;
    MTKontrollverlaufBetriebName: TStringField;
    MTKontrollverlaufRechtsgrundlageBez: TStringField;
    MTKontrollverlaufErfasserName: TStringField;
    MTKontrollverlaufKontrollorganName: TStringField;
    MTKontrollverlaufKontrolltypTyp: TStringField;
    MTKontrollverlaufBkbtyp: TStringField;
    MTKontrollverlaufStatus: TStringField;
    MTKontrollverlaufOrt: TStringField;
    MTKontrollverlaufFaelligkeitsdatum: TDateField;
    MTKontrollverlaufGuid: TGuidField;
    MTKontrollverlaufStartzeit: TDateTimeField;
    MTKontrollverlaufEndezeit: TDateTimeField;
    MTKontrollverlaufLFBIS: TStringField;
    MTKontrollverlaufBetriebstyp: TStringField;
    QMassnahmenBilder: TIntegerField;
    QMangelBilder: TFDQuery;
    QMangelBilderBild: TBlobField;
    QKontrollorgan: TFDQuery;
    QKontrollorganid: TFDAutoIncField;
    QKontrollorgantitel: TStringField;
    QKontrollorganName: TStringField;
    QGruppen: TFDQuery;
    QGruppenID: TFDAutoIncField;
    QGruppenBEZEICHNUNG: TStringField;
    QGruppenBLDCODE: TSmallintField;
    QGruppenMUTTERGRUPPE: TIntegerField;
    QGruppenID_USER_HAUPTVER: TIntegerField;
    QGruppenID_USER_STELLVER: TIntegerField;
    QGruppenOKZ: TStringField;
    QGruppenPERSOENLICH: TBooleanField;
    QGruppenEMAIL: TStringField;
    QUngeplanteKontrolle: TFDQuery;
    QTodoUngeplanteKontrolle: TFDQuery;
    QUngeplanteKontrolleID: TFDAutoIncField;
    QUngeplanteKontrolleGUID: TGuidField;
    QUngeplanteKontrolleBKB: TStringField;
    QUngeplanteKontrolleBKBTYP: TStringField;
    QUngeplanteKontrolleKONTROLLTYP: TStringField;
    QUngeplanteKontrolleDATUM: TDateField;
    QUngeplanteKontrolleERFASSER_PSKEY: TStringField;
    QUngeplanteKontrolleKONTROLLORGAN_PSKEY: TStringField;
    QUngeplanteKontrolleID_PERSON_ERFASSER: TIntegerField;
    QUngeplanteKontrolleID_PERSON_KONTROLLORGAN: TIntegerField;
    QUngeplanteKontrolleREF_BKB: TStringField;
    QUngeplanteKontrollePROBENZIEHUNG: TBooleanField;
    QUngeplanteKontrolleID_BETRIEB: TIntegerField;
    QUngeplanteKontrolleREGNR_ORT: TStringField;
    QUngeplanteKontrolleSTARTZEIT: TSQLTimeStampField;
    QUngeplanteKontrolleENDEZEIT: TSQLTimeStampField;
    QUngeplanteKontrolleBESTAETIGT_UM: TSQLTimeStampField;
    QUngeplanteKontrolleID_RECHTSGRUNDLAGE: TIntegerField;
    QUngeplanteKontrolleSTATUS: TStringField;
    QUngeplanteKontrolleANGEMELDET_UM: TSQLTimeStampField;
    QUngeplanteKontrolleTSTAMP_INSERT: TSQLTimeStampField;
    QUngeplanteKontrolleLASTCHANGE: TSQLTimeStampField;
    QUngeplanteKontrolleBETRIEBSTYP: TStringField;
    QUngeplanteKontrolleGUID_UNTERSCHRIFT_ANWESENDER_BETRIEB: TGuidField;
    QUngeplanteKontrolleGUID_UNTERSCHRIFT_KONTROLLORGAN: TGuidField;
    QUngeplanteKontrolleVERWEIGERUNGSGRUND_UNTERSCHRIFT: TStringField;
    QUngeplanteKontrolleVerweigerunggrund: TStringField;
    QUngeplanteKontrolleGUID_DOKUMENT: TGuidField;
    QUngeplanteKontrolleFEHLERHAFT_GESETZT_AM: TSQLTimeStampField;
    QUngeplanteKontrolleSTORNIERT_AM: TSQLTimeStampField;
    QUngeplanteKontrolleSTORNOGRUND: TStringField;
    QUngeplanteKontrolleVERWEIGERT_AM: TSQLTimeStampField;
    QProbenStatus: TStringField;
    QProbenGrenzwertueberschreitung: TBooleanField;
    QAnwesendeID_APTYP: TGuidField;
    QAnwesendeBezeichnung: TStringField;
    MTKontrollverlaufKontrollInformationen: TStringField;
    MTGeplanteKontrollenKontrollInformationen: TStringField;
    MTUngeplanteKontrollenKontrollInformationen: TStringField;
    QKontrollDetailsKONTROLL_INFORMATIONEN: TStringField;
    QUngeplanteKontrolleGUID_DOKUMENT_CC: TGuidField;
    QUngeplanteKontrolleVISEXPORT_AM: TSQLTimeStampField;
    QUngeplanteKontrolleKONTROLL_INFORMATIONEN: TStringField;
    QUngeplanteKontrolleID_GRUPPE_QUELLE: TIntegerField;
    MTGruppenPersonenGruppe: TStringField;
    MTGruppenPersonenGruppeID: TIntegerField;
    MTGruppenPersonenPersonVorname: TStringField;
    MTGruppenPersonenPersonNachname: TStringField;
    MTGruppenPersonenPersonID: TIntegerField;
    QKontrollDetailsKURZBEMERKUNG: TWideMemoField;
    MTKontrollverlaufKurzbemerkung: TWideMemoField;
    MTGeplanteKontrollenKurzbemerkung: TWideMemoField;
    MTUngeplanteKontrollenKurzbemerkung: TWideMemoField;
    QUngeplanteKontrolleKURZBEMERKUNG: TWideMemoField;
    MTGruppenPersonenID: TIntegerField;
    MTKontrollverlaufGruppeQuelle: TStringField;
    MTGeplanteKontrollenGruppeQuelle: TStringField;
    MTUngeplanteKontrollenGruppeQuelle: TStringField;
    QKontrollDetailsINTERNE_NOTIZ: TWideMemoField;
    QKontrollDetailsGUID_DOKUMENT_1: TGuidField;
    QKontrollDetailsQuellGruppe: TStringField;
    MTKontrollverlaufGemeindeNummer: TIntegerField;
    MTGeplanteKontrollenGemeindeNummer: TIntegerField;
    MTUngeplanteKontrollenGemeindeNummer: TIntegerField;
    MTGeplanteKontrollenPLZ: TStringField;
    MTGeplanteKontrollenStrasse: TStringField;
    MTKontrollverlaufPLZ: TStringField;
    MTKontrollverlaufStrasse: TStringField;
    MTUngeplanteKontrollenPLZ: TStringField;
    MTUngeplanteKontrollenStrasse: TStringField;
  private
    { Private declarations }
    function BuildName(person: TPerson): string;
    function BuildGruppen(person: TPerson): string;
    procedure BuildMemTable(kontrollen: TList<TKontrollbericht>; MT: TFDMemTable);
    function FindStatus(LStatusCode: string): string;
  public
    { Public declarations }
    type
      TEditMode = (New, Edit);
    procedure GetKontrollverlauf(von, bis: TDate);
    procedure GetGeplanteKontrollen(von, bis: TDate);
    procedure GetUngeplanteKontrollen(von, bis: TDate);
    procedure OpenKontrollDetails(LIDKontrollbericht: Integer);
    procedure OpenMassnahmenDetails(LIDKontrollbericht: Integer);
    procedure OpenMangelBilder(LMassnahmenID: Integer);
    procedure OpenProbenDetails(LIDKontrollbericht: Integer);
    procedure OpenBetriebDetails(LIDBetrieb: Integer);
    procedure OpenAnwesende(LIDKontrollbericht: Integer);
    procedure OpenKontrollorgane;
    procedure OpenGruppen;
    procedure FreeCollection(collection: TKontrollberichte);
    function ExportAsCSV(memoryTable: TFDMemTable; alert: TIWCGJQSweetAlert): string;
    procedure DownloadAGESProbenErgebnis(ProbenID: Integer; download: TIWCGJQFileDownload; alert: TIWCGJQSweetAlert);
    procedure DownloadFile(GUIDDokument: TGUID; download: TIWCGJQFileDownload; alert: TIWCGJQSweetAlert);
    procedure KontrolleStornieren(GUIDKontrolle: TGUID; Stornogrund: string);
    procedure KontrolleWeitergeben(AKontrollberichtID, APersonID, AGruppeID: Integer);
    procedure KontrolleZuweisen(APersonID, AGruppeID: Integer; APlanungsdatum: TDate; AAngemeldetUm: TDateTime);
    procedure GruppenPersonen();
    procedure FristVerlaengern(AMangelID: Integer; AFrist: TDate; ABemerkung: string);
    procedure MangelAbschliessen(LMangelID: Integer; ABemerkung: string; ABilder: TStrings);
    procedure UngeplanteKontrolleStornieren(LIDKontrollbericht: Integer; AGrund: string);
    procedure UngeplanteKontrolleBearbeiten(AIDKontrollbericht: Integer);
    procedure UngeplanteKontrolleSpeichern(AIDGruppe: Integer; AIDBetrieb: Integer;
      AIDUser: Integer; ADate: TDate; ABkb: string; AModus: TEditMode;
      AKontrolltyp: string; AKontrollgrundID: Integer; ABkbTyp: string;
      AKontrollInformationen: string; AInterneNotiz: string);
    procedure UngeplanteKontrolleAbbrechen;
    function GetKontrollbericht(LIDKontrollbericht: Integer): TKontrollbericht;
    function MoveTableToSelected(Grid: TIWCGJQGrid; MemTable: TFDMemTable): Boolean;
  end;

// Diese Funktion wird nur für den Designer benötigt und darf zur Laufzeit nicht verwendet werden!
function DMKontrolle: TDMKontrolle;

implementation

{%CLASSGROUP 'Vcl.Controls.TControl'}

uses
  dmmain, ServerController, System.DateUtils, System.IOUtils, UserSessionUnit;

{$R *.dfm}

function DMKontrolle: TDMKontrolle;
begin
  raise Exception.Create('"function DMKontrolle" darf zur Laufzeit nicht verwendet werden!');
end;

procedure TDMKontrolle.GetKontrollverlauf(von, bis: TDate);
var
  kontrollen: TList<TKontrollbericht>;
begin
  // Todo: Weitere Filter (speziell den Status) über GetKontrollen einbauen!
  kontrollen := UserSession.ELKERest.GetKontrollen(von, bis);
  try
    BuildMemTable(kontrollen, MTKontrollverlauf);
  finally
    kontrollen.Free;
  end;
end;

procedure TDMKontrolle.GetGeplanteKontrollen(von, bis: TDate);
var
  kontrollen: TList<TKontrollbericht>;
begin
  // Todo: Weitere Filter (speziell den Status) über GetKontrollen einbauen!
  kontrollen := UserSession.ELKERest.GetGeplanteKontrollen(von, bis);
  try
    BuildMemTable(kontrollen, MTGeplanteKontrollen);
  finally
    kontrollen.Free;
  end;
end;

procedure TDMKontrolle.GetUngeplanteKontrollen(von, bis: TDate);
var
  LKontrollen: TList<TKontrollbericht>;
begin
  // Todo: Weitere Filter (speziell den Status) über GetKontrollen einbauen!
  LKontrollen := UserSession.ELKERest.GetUngeplanteKontrollen(von, bis);
  try
    BuildMemTable(LKontrollen, MTUngeplanteKontrollen);
  finally
    LKontrollen.Free;
  end;
end;

function TDMKontrolle.BuildGruppen(person: TPerson): string;
begin
  var LGruppen := '';
  for var LUserGruppe in person.Users.First.Usergruppen do
  begin
    if not LUserGruppe.Gruppe.Persoenlich then
    begin
      LGruppen := LGruppen + LUserGruppe.Gruppe.Bezeichnung + ', ';
    end;
  end;
  LGruppen := LGruppen.Remove(Length(LGruppen) - 2, 2);
  result := LGruppen;
end;

procedure TDMKontrolle.BuildMemTable(kontrollen: TList<TKontrollbericht>; MT: TFDMemTable);
var
  Kontrolle: TKontrollbericht;
  Item: TKontrollberichtItem;
  name: string;
  collection: TKontrollberichte;
  MTKontrollen: TFDMemTable;
begin
  MTKontrollen := MT;
  QESADomWerte.Close;
  QESADomWerte.Prepare;
  QESADomWerte.Open;
  MTKontrollen.Close;
  MTKontrollen.Active := true;
  MTKontrollen.EmptyDataSet;

  for Kontrolle in kontrollen do
  begin
    MTKontrollen.Insert;
    MTKontrollen.FieldByName('ID').Value := Kontrolle.Id;
    MTKontrollen.FieldByName('Bkb').Value := Kontrolle.Bkb;
    if Kontrolle.Datum.ValueOrDefault > 0 then
      MTKontrollen.FieldByName('Datum').Value := FormatDateTime('dd.mm.yyyy', Kontrolle.Datum)
    else
      MTKontrollen.FieldByName('Datum').Clear;
    MTKontrollen.FieldByName('Probenziehung').Value := Kontrolle.Probenziehung;
    if Kontrolle.RegnrOrt.HasValue then
      MTKontrollen.FieldByName('RegnrOrt').Value := Kontrolle.RegnrOrt;
    if Kontrolle.Kurzbemerkung.HasValue then
      MTKontrollen.FieldByName('Kurzbemerkung').Value := Kontrolle.Kurzbemerkung;

    if Kontrolle.KontrollInformationen.HasValue then
      MTKontrollen.FieldByName('KontrollInformationen').Value := Kontrolle.KontrollInformationen
    else
    begin
{$IFDEF DEBUG}
      MTKontrollen.FieldByName('KontrollInformationen').Value :=
        'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat,'#13#10
        + 'sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. '
{$ENDIF}
    end;
    MTKontrollen.FieldByName('Betriebstyp').Value := Kontrolle.Betriebstyp.ToUpper.Trim;
    if (Kontrolle.Betrieb <> nil) and (Kontrolle.Betrieb.Registrierung <> nil) then
    begin
      MTKontrollen.FieldByName('LFBIS').Value := Kontrolle.Betrieb.Registrierung.Regnr;
    end
    else
    begin
      MTKontrollen.FieldByName('LFBIS').Value := '';
    end;

    if Kontrolle.Startzeit.ValueOrDefault > 0 then
    begin
      MTKontrollen.FieldByName('Startzeit').Value := Kontrolle.Startzeit;
    end
    else
    begin
      MTKontrollen.FieldByName('Startzeit').Clear;
    end;
    if Kontrolle.Endezeit.ValueOrDefault > 0 then
    begin
      MTKontrollen.FieldByName('Endezeit').Value := Kontrolle.Endezeit;
    end
    else
    begin
      MTKontrollen.FieldByName('Endezeit').Clear;
    end;
    if Kontrolle.BestaetigtUm.ValueOrDefault > 0 then
      MTKontrollen.FieldByName('BestaetigtUm').Value := Kontrolle.BestaetigtUm;
    if Kontrolle.VerweigerungsgrundUnterschrift.HasValue then
      MTKontrollen.FieldByName('verweigerunggrund').Value := Kontrolle.VerweigerungsgrundUnterschrift;
    if Kontrolle.AngemeldetUm.ValueOrDefault > 0 then
    begin
      MTKontrollen.FieldByName('AngemeldetUm').Value := Kontrolle.AngemeldetUm;
    end
    else
    begin
      MTKontrollen.FieldByName('AngemeldetUm').Clear;
    end;
    MTKontrollen.FieldByName('BetriebName').Value := Kontrolle.Betrieb.name;

    if Assigned(Kontrolle.Betrieb.Adresse) and Assigned(Kontrolle.Betrieb.Adresse.Gemeinde) then
    begin
      MTKontrollen.FieldByName('GemeindeNummer').Value := Kontrolle.Betrieb.Adresse.Gemeinde.Gemeindekennziffer;
    end
    else
    begin
      MTKontrollen.FieldByName('GemeindeNummer').Value := 0;
    end;

    MTKontrollen.FieldByName('RechtsgrundlageBez').Value := Kontrolle.Rechtsgrundlage.Bezeichnung;

    // todo: Erfasser ist bei einer ungeplanten Kontrolle leer!
    if Kontrolle.Erfasser <> nil then
      MTKontrollen.FieldByName('ErfasserName').Value := BuildName(Kontrolle.Erfasser);

    if Kontrolle.Kontrollorgan <> nil then
    begin
      MTKontrollen.FieldByName('KontrollorganName').Value := BuildName(Kontrolle.Kontrollorgan);
    end
    else
    begin
      // Wenn (noch) kein Kontrollorgan eingetargen ist, dann gibt es eine Person in Todo, die quasi als Vorschlag dient
      if (Kontrolle.Todos.Count > 0) and (Kontrolle.Todos.First.User <> nil) then
        MTKontrollen.FieldByName('KontrollorganName').Value := BuildName(Kontrolle.Todos.First.User.person);
    end;

    MTKontrollen.FieldByName('KontrolltypTyp').Value := Kontrolle.Kontrolltyp.Bezeichnung.Value;
    MTKontrollen.FieldByName('Bkbtyp').Value := Kontrolle.Kontrolltyp.Bkbtyp.Bezeichnung;
    if Kontrolle.Status.HasValue then
      MTKontrollen.FieldByName('Status').Value := FindStatus(Kontrolle.Status.Value)
    else
      MTKontrollen.FieldByName('Status').Value := '';

    if Assigned(Kontrolle.Betrieb.Adresse) then
    begin
      MTKontrollen.FieldByName('PLZ').Value := Kontrolle.Betrieb.Adresse.Plz;
      MTKontrollen.FieldByName('Ort').Value := Kontrolle.Betrieb.Adresse.Ort;
      MTKontrollen.FieldByName('Strasse').Value := Kontrolle.Betrieb.Adresse.Strasse;
    end;
    MTKontrollen.FieldByName('GUID').AsGuid := Kontrolle.Guid;

    // Quell-Gruppe
    MTKontrollen.FieldByName('GruppeQuelle').AsString := Kontrolle.GruppeQuelle.Bezeichnung;

    // Gruppe aus den Todos (im prinzip obsolet)
    if (Kontrolle.Todos <> nil) and (Kontrolle.Todos.Count > 0) then
    begin
      (*
      if Kontrolle.Todos.First.Gruppe <> Nil then
      begin
      //xxx  MTKontrollen.FieldByName('Gruppe').Value := Kontrolle.Todos.First.Gruppe.Bezeichnung;
      end;
      *)
      if Kontrolle.Todos.First.Faellig.ValueOrDefault > 0 then
      begin
        MTKontrollen.FieldByName('Faelligkeitsdatum').AsDateTime := Kontrolle.Todos.First.Faellig.Value;
      end
      else
      begin
        MTKontrollen.FieldByName('Faelligkeitsdatum').Clear;
      end;
    end;

    MTKontrollen.Post;
  end;
end;

function TDMKontrolle.FindStatus(LStatusCode: string): string;
begin
  result := '';
  QESADomWerte.First;
  while not QESADomWerte.Eof do
  begin
    if QESADomWerteCode.AsString.Equals(LStatusCode) then
    begin
      result := QESADomWerteText.AsString;
      Exit;
    end;
    QESADomWerte.Next;
  end;
end;

procedure TDMKontrolle.FreeCollection(collection: TKontrollberichte);
begin
  while collection.Count > 0 do
  begin
    collection.Item[0].Free;
  end;
  FreeAndNil(collection);
end;

procedure TDMKontrolle.OpenKontrollDetails(LIDKontrollbericht: Integer);
begin
  QKontrollDetails.Close;
  QKontrollDetails.ParamByName('IDKontrollbericht').AsInteger :=
    LIDKontrollbericht;
  QKontrollDetails.Active := true;
  QProben.Close;
  QProben.ParamByName('IDKontrollbericht').AsInteger := LIDKontrollbericht;
  QProben.Active := true;
  OpenMassnahmenDetails(LIDKontrollbericht);
  QESADomWerte.Prepare;
  QESADomWerte.Open;
end;

procedure TDMKontrolle.OpenKontrollorgane;
begin
  QKontrollorgan.Close;
  QKontrollorgan.ParamByName('UserId').AsInteger := dm_main.UserId;
  QKontrollorgan.Open;
end;

procedure TDMKontrolle.OpenProbenDetails(LIDKontrollbericht: Integer);
begin
  QProben.Close;
  QProben.ParamByName('IDKontrollbericht').AsInteger := LIDKontrollbericht;
  QProben.Open;
end;

procedure TDMKontrolle.OpenMassnahmenDetails(LIDKontrollbericht: Integer);
begin
  QMassnahmen.Close;
  QMassnahmen.ParamByName('IDKontrollbericht').AsInteger := LIDKontrollbericht;
  QMassnahmen.Active := true;
end;

procedure TDMKontrolle.OpenMangelBilder(LMassnahmenID: Integer);
begin
  QMangelBilder.Close;
  QMangelBilder.ParamByName('MassnahmenID').AsInteger := LMassnahmenID;
  QMangelBilder.Active := true;
end;

procedure TDMKontrolle.OpenBetriebDetails(LIDBetrieb: Integer);
begin
  QBetriebDetail.Close;
  QBetriebDetail.ParamByName('IDBetrieb').AsInteger := LIDBetrieb;
  QBetriebDetail.Open;
  QBetriebZulassung.Close;
  QBetriebZulassung.ParamByName('Regnr').AsString := QBetriebDetailRegnr.AsString;
  QBetriebZulassung.Open;
end;

procedure TDMKontrolle.OpenGruppen;
begin
  QGruppen.Close;
  QGruppen.ParamByName('Bldcode').AsInteger := dm_main.BLDCODE;
  QGruppen.Open;
end;

procedure TDMKontrolle.OpenAnwesende(LIDKontrollbericht: Integer);
begin
  QAnwesende.Close;
  QAnwesende.ParamByName('IDKontrollbericht').AsInteger := LIDKontrollbericht;
  QAnwesende.Open;
end;

function TDMKontrolle.BuildName(person: TPerson): string;
var
  name: string;
begin
  name := '';
  if person.Titel.HasValue then
    name := person.Titel.Value;
  name := name + ' ' + person.Vorname;
  name := name + ' ' + person.Nachname;

  result := name.Trim;
end;

// Exportiert das gerade angezeigte Ergebnis als CSV.
// Es werden genau die selben Spalten/Daten verwendet wie im Grid
// Der Export wird über den Download heruntergeladen
function TDMKontrolle.ExportAsCSV(memoryTable: TFDMemTable;
  alert: TIWCGJQSweetAlert): string;
var
  text, filename: string;
begin
  if not memoryTable.Active then
    abort;
  text :=
    'Status;Startzeit;Endzeit;BKB;KontrollberichtTyp;KontrollTyp;Betrieb;Kontrollgrund;Kontrollorgan;LFBIS;Gruppe;Gemeinde;PLZ;Ort;Strasse';

  memoryTable.First;
  while not memoryTable.Eof do
  begin
    var LLine := '';
    LLine := LLine + memoryTable.FieldByName('status').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('startzeit').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('endezeit').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('bkb').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('bkbtyp').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('kontrolltyptyp').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('betriebname').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('rechtsgrundlagebez').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('kontrollorganname').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('LFBIS').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('GruppeQuelle').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('GemeindeNummer').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('PLZ').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('Ort').AsString + ';';
    LLine := LLine + memoryTable.FieldByName('Strasse').AsString + ';';
    // Kein Zeilenumbruch mmittendrin
    // https://esculenta.atlassian.net/browse/EIW-415
    LLine := LLine.Replace(#10, ' ').Replace(#13, ' ');
    // Nur der abschließende Zeilenumbruch
    text := text + sLineBreak + LLine;
    memoryTable.Next;
  end;

  if not DirectoryExists('exports') then
    if not CreateDir('exports') then
      Exit;
  filename := 'exports\KontrollberichtExport_' +
    FormatDateTime('dd-mm-yyyy hh-nn', Now) + '.csv';
  TFile.WriteAllText(filename, text, TEncoding.UTF8);
  result := filename;
end;

procedure TDMKontrolle.DownloadAGESProbenErgebnis(ProbenID: Integer;
  download: TIWCGJQFileDownload; alert: TIWCGJQSweetAlert);
var
  LFilename: string;
begin
  quProbenPdf.Close;
  quProbenPdf.ParamByName('id').AsInteger := ProbenID;
  quProbenPdf.Open;

  if quProbenPdfGUID_DOKUMENT_AGES_ERGEBNIS.IsNull then
  begin
    alert.Info('Diese Probe hat noch kein Ergebnis.');
    abort;
  end;
  QProbenPDFDokument.Open;
  LFilename := 'exports/' + quProbenPdfProbenbkb.AsString + '.pdf';
  QProbenPDFDokumentDOKUMENT.SaveToFile(LFilename);
  QProbenPDFDokument.Close;
  download.DownloadFileName(LFilename);
end;

procedure TDMKontrolle.DownloadFile(GUIDDokument: TGUID; download: TIWCGJQFileDownload; alert: TIWCGJQSweetAlert);
var
  filename: string;
  Data: TArray<byte>;
  Stream: TStream;
begin
  QDokument.Close;
  QDokument.ParamByName('GUID').AsGuid := GUIDDokument;
  QDokument.Open;

  if QDokument.RecordCount = 0 then
  begin
    alert.Info('Dieses Dokument gibt es nicht');
    Exit;
  end;

  filename := 'exports/' + QDokumentDateiname.AsString;
  Data := QDokumentDokument.AsBytes;
  Stream := TFileStream.Create(filename, fmCreate);
  try
    Stream.WriteBuffer(Data, Length(Data));
    download.DownloadFileName(filename);
  finally
    Stream.Free;
  end;
end;

procedure TDMKontrolle.GruppenPersonen;
var
  LPersonen: TList<TKontakt>;
begin
  LPersonen := UserSession.ELKERest.GruppenPersonen;

  MTGruppenPersonen.Close;
  MTGruppenPersonen.Active := true;
  MTGruppenPersonen.EmptyDataSet;

  var i := 0;
  for var LPerson in LPersonen do
  begin
    MTGruppenPersonen.Insert;
    MTGruppenPersonenID.Value := i; // Künstliches PK Feld
    inc(i);
    MTGruppenPersonenPersonID.Value := LPerson.PersonID;
    MTGruppenPersonenPersonVorname.Value := LPerson.Vorname;
    MTGruppenPersonenPersonNachname.Value := LPerson.Nachname;
    MTGruppenPersonenGruppe.AsString := LPerson.Gruppe.Bezeichnung;
    MTGruppenPersonenGruppeID.Value := LPerson.Gruppe.Id;

    MTGruppenPersonen.Post;
  end;
end;

procedure TDMKontrolle.FristVerlaengern(AMangelID: Integer; AFrist: TDate; ABemerkung: string);
begin
  QFristVerlaengern.Close;
  QFristVerlaengern.ParamByName('id').AsInteger := AMangelID;
  QFristVerlaengern.ParamByName('frist').AsDate := AFrist;
  QFristVerlaengern.ParamByName('bemerkung').AsString :=
    Format(
    'Fristverlängerung am %s'#13#10 +
    'Neue Frist: %s'#13#10 +
    '%s'#13#10,
    [DateToStr(Today), DateToStr(AFrist), ABemerkung]);
  QFristVerlaengern.Execute;
end;

procedure TDMKontrolle.MangelAbschliessen(LMangelID: Integer; ABemerkung: string; ABilder: TStrings);
begin
  QMangelAbschliessen.Close;
  QMangelAbschliessen.ParamByName('id').AsInteger := LMangelID;
  QMangelAbschliessen.ParamByName('beseitigt_am').AsDate := Now;
  QMangelAbschliessen.ParamByName('bemerkung').AsString :=
    Format('Bemerkung zur Mangelbeseitigung:'#13#10'%s '#13#10, [ABemerkung]);
  QMangelAbschliessen.Execute;

  QMangelbildSpeichern.Close;
  QMangelbildSpeichern.Open;
  for var LBild in ABilder do
  begin
    QMangelbildSpeichern.Insert;
    try
      QMangelbildSpeichernID.AsGuid := TGUID.NewGuid;
      QMangelbildSpeichernBILD.LoadFromFile(LBild);
      QMangelbildSpeichernFORMAT.AsString := TPath.GetExtension(LBild).Replace('.', '');
      QMangelbildSpeichernID_MANGEL.Value := LMangelID;
      QMangelbildSpeichernBEMERKUNG.Value := 'Mangel beseitigt';
      QMangelbildSpeichernAUFNAHMEDATUM.AsDateTime := TFile.GetCreationTime(LBild);
      QMangelbildSpeichernID_AUFGENOMMEN_VON.Value := UserSession.User.Id;
      QMangelbildSpeichern.Post;
    except
      // Todo: Fehler loggen/melden
      QMangelbildSpeichern.Cancel;
    end;
  end;
end;

procedure TDMKontrolle.UngeplanteKontrolleAbbrechen;
begin
  QUngeplanteKontrolle.Cancel;
end;

procedure TDMKontrolle.UngeplanteKontrolleBearbeiten(AIDKontrollbericht: Integer);
begin
  QUngeplanteKontrolle.Close;
  QUngeplanteKontrolle.ParamByName('ID').AsInteger := AIDKontrollbericht;
  QUngeplanteKontrolle.Open;
  QUngeplanteKontrolle.Edit; // Todo: Das "Edit" sollte erst beim Speichern gemacht werden!
end;

procedure TDMKontrolle.UngeplanteKontrolleStornieren(LIDKontrollbericht: Integer; AGrund: string);
begin
  if MTUngeplanteKontrollen.Locate('ID', LIDKontrollbericht) then
  begin
    UserSession.ELKERest.UngeplanteKontrolleStornieren(LIDKontrollbericht, AGrund);
    // Aus der Ansicht entfernen
    MTUngeplanteKontrollen.Delete;
  end;
end;

procedure TDMKontrolle.UngeplanteKontrolleSpeichern(AIDGruppe: Integer;
  AIDBetrieb: Integer; AIDUser: Integer; ADate: TDate; ABkb: string; AModus:
  TEditMode; AKontrolltyp: string; AKontrollgrundID: Integer; ABkbTyp:
  string; AKontrollInformationen: string; AInterneNotiz: string);
var
  LKontrolle: TKontrollbericht;
  LTodo: TTodo;
begin
  if AModus = New then
  begin
    // Todo: "New" muss noch ausprogrammiert werden! U.a. die Erstellung der Todos ist hier noch nihct implementiert!
    LKontrolle := TKontrollbericht.Create;
    LTodo := TTodo.Create;

    LKontrolle.Bkb := ABkb;
    var LKontrolltypen := UserSession.ELKERest.GetKontrolltypenForBkbtyp(ABkbTyp);
    for var LKontrolltyp in LKontrolltypen do
    begin
      if LKontrolltyp.Kontrolltyp = AKontrolltyp then
      begin
        LKontrolle.Kontrolltyp := LKontrolltyp;
        break;
      end;
    end;
    Assert(LKontrolle.Kontrolltyp <> nil, 'Kein Kontrolltyp gefunden!');
    var LRechtsgrundlagen := UserSession.ELKERest.Rechtsgrundlagen(ABkbTyp);
    for var LRechtsgrundlage in LRechtsgrundlagen do
    begin
      if LRechtsgrundlage.Id = AKontrollgrundID then
      begin
        LKontrolle.Rechtsgrundlage := LRechtsgrundlage;
        break;
      end;
    end;
    Assert(LKontrolle.Rechtsgrundlage <> nil, 'Kein Kontrollgrund gefunden!');

    LKontrolle.Betrieb := UserSession.ELKERest.GetBetrieb(AIDBetrieb);
    Assert(LKontrolle.Betrieb <> nil, 'Kein Betrieb gefunden!');
    // Todo: -- check ob nur HB sein kann --
    LKontrolle.Betriebstyp := 'HB';

    LTodo.Titel := ABkb;
    LTodo.Faellig := ADate;
    if AIDGruppe > -1 then
    begin
      LTodo.Gruppe := UserSession.ELKERest.GetGruppe(AIDGruppe);
      Assert(LTodo.Gruppe <> nil, 'Keine Gruppe gefunden!');
      // Gruppe in die Kontrolle als "Quelle" eintragen
      LKontrolle.GruppeQuelle := LTodo.Gruppe;
    end;
    if AIDUser > -1 then
    begin
      LTodo.User := UserSession.ELKERest.GetUser(AIDUser);
      Assert(LTodo.User <> nil, 'Kein User gefunden!');

      // Falls keine Gruppe, dann persönliche Gruppe des Users.
      if LKontrolle.GruppeQuelle = nil then
      begin
        for var LUserGruppe in LTodo.User.Usergruppen do
        begin
          if LUserGruppe.Gruppe.Persoenlich then
          begin
            LKontrolle.GruppeQuelle := LUserGruppe.Gruppe;
          end;
        end;
      end;

    end;

    LKontrolle.KontrollInformationen := AKontrollInformationen;
    LKontrolle.InterneNotiz := AInterneNotiz;
    var Id := UserSession.ELKERest.UngeplantenKontrollberichtErstellen(LKontrolle, LTodo);
    Assert(Id > 0, 'Keine gültige Kontrolle');
  end
  else
  begin
    QUngeplanteKontrolleID_BETRIEB.AsInteger := AIDBetrieb;
    QUngeplanteKontrolleBKB.AsString := ABkb;
    QUngeplanteKontrolleKONTROLL_INFORMATIONEN.AsString := AKontrollInformationen;

    if AIDGruppe > -1 then
    begin
      //Gruppe im Todo ändern
      QTodoUngeplanteKontrolle.ParamByName('gruppenid').AsInteger := AIDGruppe;
      //Quellgruppe ändern
      QUngeplanteKontrolleID_GRUPPE_QUELLE.AsInteger := AIDGruppe;
    end
    else
    begin
      QTodoUngeplanteKontrolle.ParamByName('gruppenid').Clear;
      QUngeplanteKontrolleID_GRUPPE_QUELLE.Clear;
    end;
    if AIDUser > -1 then
    begin
      QTodoUngeplanteKontrolle.ParamByName('userid').AsInteger := AIDUser;
    end
    else
    begin
      QTodoUngeplanteKontrolle.ParamByName('userid').Clear;
    end;
    // In der Kontrolle kann bereits ein Kontrollorgan eingetragen sein.
    QUngeplanteKontrolleID_PERSON_KONTROLLORGAN.Clear;
    dm_main.TransactionMain.StartTransaction;
    try
      QUngeplanteKontrolle.Post;

      QTodoUngeplanteKontrolle.ParamByName('titel').AsString := ABkb;
      QTodoUngeplanteKontrolle.ParamByName('datum').AsDate := ADate;
      QTodoUngeplanteKontrolle.ParamByName('kontrollid').AsInteger := QUngeplanteKontrolleID.AsInteger;
      QTodoUngeplanteKontrolle.Execute;

      dm_main.TransactionMain.Commit;
      // Todo: das hier ist noch ein HACK, damit die Verifikation über REST läuft!
      var
      LKontrolleUpdate := UserSession.ELKERest.GetKontrollbericht(QUngeplanteKontrolleID.AsInteger);
      UserSession.ELKERest.KontrolleAenderungenSpeichern(LKontrolleUpdate, nil);

    except
      dm_main.TransactionMain.Rollback;
      raise;
    end;
  end;
end;

function TDMKontrolle.GetKontrollbericht(LIDKontrollbericht: Integer): TKontrollbericht;
begin
  result := UserSession.ELKERest.GetKontrollbericht(LIDKontrollbericht);
end;

procedure TDMKontrolle.KontrolleStornieren(GUIDKontrolle: TGUID; Stornogrund: string);
begin
  UserSession.ELKERest.KontrolleStornieren(GUIDKontrolle, Stornogrund);
end;

procedure TDMKontrolle.KontrolleWeitergeben(AKontrollberichtID, APersonID, AGruppeID: Integer);
begin
  UserSession.ELKERest.KontrolleWeitergeben(AKontrollberichtID, APersonID, AGruppeID);
end;

procedure TDMKontrolle.KontrolleZuweisen(
  APersonID, AGruppeID: Integer;
  APlanungsdatum: TDate; AAngemeldetUm: TDateTime);
begin
  UserSession.ELKERest.KontrolleZuweisen(MTUngeplanteKontrollenID.AsInteger, APersonID, AGruppeID, APlanungsdatum,
    AAngemeldetUm);
end;

function TDMKontrolle.MoveTableToSelected(Grid: TIWCGJQGrid; MemTable: TFDMemTable): Boolean;
var
  Index: string;
begin
  Index := Grid.JQGridOptions.SelRow;
  if Index.Equals('') then
  begin
    result := false;
    Exit;
  end;
  MemTable.Locate('ID', StrToInt(Index));
  result := true;
end;

function TKontrollberichte.Add: TKontrollberichtItem;
begin
  result := inherited Add as TKontrollberichtItem;
end;

function TKontrollberichte.GetItem(Index: Integer): TKontrollberichtItem;
begin
  result := inherited Items[Index] as TKontrollberichtItem;
end;

end.

