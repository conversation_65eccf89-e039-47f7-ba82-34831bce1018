inherited DialogMangelAbschliessen: TDialogMangelAbschliessen
  Width = 598
  Height = 421
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 598
    Height = 421
    TabOrder = 1
    JQDialogOptions.Height = 421
    JQDialogOptions.Title = 'Mangel abschlie'#223'en'
    JQDialogOptions.Width = 598
    inherited RegionContent: TIWCGJQRegion
      Width = 598
      Height = 371
      TabOrder = 2
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 32
        Top = 6
        Width = 228
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Bemerkung zum beseitigten Mangel:'
      end
      object IWCGJQLabel2: TIWCGJQLabel
        Left = 32
        Top = 218
        Width = 271
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Erg'#228'nzende Bilder zum beseitigten Mangel:'
      end
      object FileUpload: TIWCGJQFileUpload
        Left = 264
        Top = 327
        Width = 297
        Height = 21
        TabOrder = 4
        Version = '1.0'
        JQFileUploadOptions.Multiple = False
        JQFileUploadOptions.AllowedExtensions.Strings = (
          'jpg'
          'png')
        JQFileUploadOptions.SizeLimit = 10000000
        JQFileUploadOptions.OnComplete.OnEvent = IWCGJQFileUpload1JQFileUploadOptionsComplete
        JQFileUploadOptions.Messages.TypeError = 
          '{file} hat eine unzul'#228'ssige Dateiendung. Erlaubt ist: {extension' +
          's}.'
        JQFileUploadOptions.Messages.SizeError = '{file} ist zu gro'#223', das Maximum ist: {sizeLimit}.'
        JQFileUploadOptions.Messages.EmptyError = '{file} ist leer.'
        JQFileUploadOptions.Messages.OnLeave = 
          'Es werden noch Dateien hochgeladen, wenn Sie die Seite jetzt ver' +
          'lassen, wird das Hochladen abgebrochen!'
        JQFileUploadOptions.Messages.NoFilesError = 'Keine Dateien zum Hochladen.'
        JQFileUploadOptions.Classes.Button = 'ui-button'
        JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
        JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
        JQFileUploadOptions.Classes.Fail = 'ui-state-error'
        JQFileUploadOptions.OnUpload.OnEvent = FileUploadJQFileUploadOptionsUpload
        JQFileUploadOptions.Tip = 'Datei hier ablegen zum Hochladen'
        JQFileUploadOptions.DragText = 'Datei hier ablegen zum Hochladen'
        JQFileUploadOptions.UploadButtonText = 'Bild hochladen (max. 10MB p. Bild)'
        JQFileUploadOptions.MultipleFileDropNotAllowedMessage = 'Es darf nur eine Datei abgelegt werden'
        JQFileUploadOptions.ShowUploadedFileList = False
        CGProgressBar = ProgressUpload
      end
      object ProgressUpload: TIWCGJQProgressBar
        Left = 32
        Top = 327
        Width = 226
        Height = 21
        TabOrder = 5
        Version = '1.0'
      end
      object MemoBemerkung: TIWCGJQMemoEx
        Left = 32
        Top = 28
        Width = 527
        Height = 173
        TabOrder = 6
        Css = ''
        Version = '1.0'
        Anchors = [akLeft, akTop, akRight]
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
      end
      object ListBilder: TIWCGJQMemoEx
        Left = 32
        Top = 240
        Width = 527
        Height = 81
        TabOrder = 7
        Css = ''
        Version = '1.0'
        Anchors = [akLeft, akTop, akRight]
        ZIndex = 0
        BGColor = clNone
        Editable = False
        Required = False
        SubmitOnAsyncEvent = True
        ReadOnly = True
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 371
      Width = 598
      TabOrder = 0
      inherited ButtonCancel: TIWCGJQButton
        Left = 490
        TabOrder = 8
      end
      inherited ButtonOK: TIWCGJQButton
        Left = 374
        TabOrder = 3
      end
    end
  end
end
