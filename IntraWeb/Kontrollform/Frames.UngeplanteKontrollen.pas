﻿unit Frames.UngeplanteKontrollen;

interface

uses
  Frames.Base, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQSweetAlert, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQDownload, IWCGJQFileDownload, IWCGJQDatePicker,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel, IWCGJQGrid, IWCGJQButton,
  IWCGJQDialog, System.Classes, System.SysUtils, Vcl.Controls, Vcl.Forms, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, Modules.Kontrolle, JQ.Helpers.Alert;

type
  // TUngeplanteKontrollen = class(TFrameBase)
  TUngeplanteKontrollen = class(TFrameBase<TDMKontrolle>)
    iwrBot: TIWCGJQRegion;
    ButtonCSVExport: TIWCGJQButton;
    iwrMid: TIWCGJQRegion;
    GridKontrollen: TIWCGJQGrid;
    iwrTop: TIWCGJQRegion;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    jqbAbfragen: TIWCGJQButton;
    jqdVon: TIWCGJQDatePicker;
    jqdBis: TIWCGJQDatePicker;
    IWCGJQRegion1: TIWCGJQRegion;
    ButtonStornieren: TIWCGJQButton;
    ButtonBearbeiten: TIWCGJQButton;
    ButtonHinzufuegen: TIWCGJQButton;
    DownloadUngeplant: TIWCGJQFileDownload;
    Alert: TIWCGJQSweetAlert;
    ProviderUngGeplanteKontrollen: TIWCGJQGridDataSetProvider;
    DialogUngeplanteKontrolle: TIWCGJQDialog;
    ButtonZuweisen: TIWCGJQButton;
    LabelCount: TIWLabel;
    procedure ButtonAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonCSVExportOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure DialogAbbrechen(Sender: TObject; AURLParams: TStringList);
    procedure ButtonStornierenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBearbeitenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonZuweisenOnClick(Sender: TObject; AParams: TStringList);
    procedure GridKontrollenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    procedure ResetSearch;

  public
    constructor Create(AOwner: TComponent; ADataModule: TDMKontrolle); override;
    procedure Search;
  end;

implementation

{$R *.dfm}


uses ServerController, Utility, JQ.Helpers.Grid, Forms.UngeplanteKontrolle, ELKE.Classes.RESTError, System.DateUtils,
  Dialogs.UngeplanteKontrolleZuweisen, Dialogs.Base, Dialogs.UngeplanteKontrolleEditNeu;

constructor TUngeplanteKontrollen.Create(AOwner: TComponent; ADataModule: TDMKontrolle);
begin
  inherited;
  ResetSearch;
  ProviderUngGeplanteKontrollen.DataSet := DM.MTUngeplanteKontrollen;
  GridKontrollen.SetUpDefaults(ProviderUngGeplanteKontrollen);
  // Alle benötigten Dialoge vorab erzeugen
  TDialogUngeplanteKontrolleZuweisen.Create(self, DM);
  TUngeplanteKontrolleEditNeu.Create(self, -1);
end;

procedure TUngeplanteKontrollen.ResetSearch;
begin
  jqdVon.Date := IncMonth(Now, -12);
  jqdBis.Date := EndOfTheYear(Now);
end;

procedure TUngeplanteKontrollen.ButtonAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  Search;
end;

procedure TUngeplanteKontrollen.ButtonBearbeitenOnClick(Sender: TObject; AParams: TStringList);
var
  LUngeplanteKontrolleEdit: TUngeplanteKontrolleEditNeu;
  i: integer;
begin
  if GridKontrollen.JQGridOptions.SelRow = '' then
    abort;
  i := DM.GetKontrollbericht(GridKontrollen.JQGridOptions.SelRow.ToInteger).Id;
  LUngeplanteKontrolleEdit := TUngeplanteKontrolleEditNeu.Create(self, i);
  LUngeplanteKontrolleEdit.Show(false, true, nil);
end;

procedure TUngeplanteKontrollen.Search;
begin
  DM.GetUngeplanteKontrollen(jqdVon.Date, jqdBis.Date);
end;

procedure TUngeplanteKontrollen.ButtonCSVExportOnClick(Sender: TObject;
  AParams: TStringList);
var
  filename: string;
begin
  try
    filename := DM.ExportAsCSV(DM.MTUngeplanteKontrollen, Alert);
    DownloadUngeplant.DownloadFileName(filename);
  except
    on e: EAbort do
      raise;
    on e: Exception do
    begin
      Alert.Error('Die Kontrollberichte konnten nicht exportiert werden.' + sLineBreak + e.Message);
    end;
  end;
end;

procedure TUngeplanteKontrollen.ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
var
  LUngeplanteKontrolleNeu: TUngeplanteKontrolleEditNeu;
begin
  LUngeplanteKontrolleNeu := TUngeplanteKontrolleEditNeu.Create(self);
  LUngeplanteKontrolleNeu.Show(true);
end;

procedure TUngeplanteKontrollen.ButtonStornierenOnClick(Sender: TObject; AParams: TStringList);
var
  LIDKontrollbericht: integer;
begin
  inherited;
  if not DM.MoveTableToSelected(GridKontrollen, DM.MTUngeplanteKontrollen) then
  begin
    Alert.Info('Es muss eine Kontrolle ausgewählt sein');
    abort;
  end;

  Alert.Prompt('Stornogrund', '',
    procedure(AGrund: string)
    begin
      LIDKontrollbericht := DM.MTUngeplanteKontrollen.FieldByName('id').AsInteger;
      try
        DM.UngeplanteKontrolleStornieren(LIDKontrollbericht, AGrund);
        GridKontrollen.JQGridOptions.ReloadGrid;
        Alert.Success('Kontrolle erfolgreich storniert.');
      except
        on e: Exception do
        begin
          Alert.Error('Es gab einen Problem beim Stornieren der Kontrolle.' + sLineBreak + e.Message);
          abort;
        end;
      end;
    end);
end;

procedure TUngeplanteKontrollen.DialogAbbrechen(Sender: TObject; AURLParams: TStringList);
begin
  DialogUngeplanteKontrolle.Visible := false;
end;

procedure TUngeplanteKontrollen.GridKontrollenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
  inherited;
  LabelCount.Caption := GridKontrollen.RowCount.ToString + ' Ergebnisse gefunden.';
end;

procedure TUngeplanteKontrollen.ButtonZuweisenOnClick(Sender: TObject; AParams:
  TStringList);
var
  LDialog: TDialogUngeplanteKontrolleZuweisen;
begin
  inherited;
  if moveQueryToRow(ProviderUngGeplanteKontrollen.DataSet, GridKontrollen) then
  begin
    LDialog := TDialogUngeplanteKontrolleZuweisen.Create(self, DM);
    LDialog.Show(true);
  end;
end;

end.
