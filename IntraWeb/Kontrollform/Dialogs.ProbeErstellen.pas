﻿unit Dialogs.ProbeErstellen;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton,
  IWCGJQRegion, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQEdit,
  IWCompCheckbox, IWCompListbox, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompLabel, Modules.Kontrolle,
  IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, System.Generics.Collections, Elke.Classes.Generated, IWCGJQSweetAlert,
  IWCGJQCombobox, IWCGJQLabel,
  IWCGJQDatePicker, JQ.Helpers.ComboboxEx, IWCGJQCheckBox;

type
  TDialogProbeErstellen = class(TDialogBase)
    RegionMid: TIWCGJQRegion;
    RegionLeft: TIWCGJQRegion;
    LabelBeschaffenheit: TIWCGJQLabel;
    LabelProbenverpackung: TIWCGJQLabel;
    LabelProbenverschluss: TIWCGJQLabel;
    LabelVersiegelt: TIWCGJQLabel;
    LabelHerkunft: TIWCGJQLabel;
    LabelSackanhänger: TIWCGJQLabel;
    LabelGegenprobe: TIWCGJQLabel;
    LabelZukauf: TIWCGJQLabel;
    LabelBemerkung: TIWCGJQLabel;
    LabelProbenbeschreibung: TIWCGJQLabel;
    LabelVerdacht: TIWCGJQLabel;
    LabelProbenart: TIWCGJQLabel;
    LabelUParameter: TIWCGJQLabel;
    LabelFuttertyp: TIWCGJQLabel;
    LabelBeimischrate: TIWCGJQLabel;
    LabelEinzelfutterart: TIWCGJQLabel;
    LabelTierart: TIWCGJQLabel;
    LabelTierkategorie: TIWCGJQLabel;
    LabelMenge: TIWCGJQLabel;
    RegionRight: TIWCGJQRegion;
    ComboBeschaffenheit: TIWCGJQComboBoxEx;
    ComboEinzelfutterart: TIWCGJQComboBoxEx;
    ComboFuttermitteltyp: TIWCGJQComboBoxEx;
    ComboHerkunft: TIWCGJQComboBoxEx;
    ComboProbenart: TIWCGJQComboBoxEx;
    ComboTierart: TIWCGJQComboBoxEx;
    ComboTierkategorie: TIWCGJQComboBoxEx;
    ComboVerpackung: TIWCGJQComboBoxEx;
    ComboVerschluss: TIWCGJQComboBoxEx;
    jqeBeimischrate: TIWCGJQEdit;
    jqeBemerkung: TIWCGJQEdit;
    jqeGewUnt: TIWCGJQEdit;
    jqeHerkZukauf: TIWCGJQEdit;
    jqeProbenbezeichnung: TIWCGJQEdit;
    jqeVerdacht: TIWCGJQEdit;
    jqeVorgMenge: TIWCGJQEdit;
    LabelProbendatum: TIWCGJQLabel;
    DatepickerProbendatum: TIWCGJQDatePicker;
    LabelProbenkennung: TIWCGJQLabel;
    jqeProbenkennung: TIWCGJQEdit;
    LabelGegenprobeKennzeichnung: TIWCGJQLabel;
    jqeGegenprobe: TIWCGJQEdit;
    iwcGegenprobe: TIWCGJQCheckBoxEx;
    iwcSackanhaenger: TIWCGJQCheckBoxEx;
    iwcVersiegelt: TIWCGJQCheckBoxEx;
    procedure ComboProbenartSelect(Sender: TObject; AParams: TStringList);
    procedure ComboFuttermitteltypSelect(Sender: TObject; AParams: TStringList);
    procedure ComboTierartSelect(Sender: TObject; AParams: TStringList);
    procedure ComboHerkunftSelect(Sender: TObject; AParams: TStringList);
    procedure iwcGegenprobeJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    IDKontrollbericht: integer;
    verdacht: TBkbtyp;
    monitoring: TBkbtyp;
    procedure FuttermitteltypDependencies(einzelfutter, beimischrate : boolean);
    procedure Reset;
  public
    constructor Create(AOwner: TComponent; IDKontrollbericht: integer); reintroduce;
    procedure DoButtonCancel(ASender: TObject; AParams: TStringList); override;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

uses Utility, dmmain, System.IOUtils, ServerController, Elke.Services.Me.Intf,
  DX.Classes.ObjectFactory;

{$R *.dfm}


constructor TDialogProbeErstellen.Create(AOwner: TComponent; IDKontrollbericht: integer);
var
  bkbtypen: TList<TBkbtyp>;
  bkbtyp: TBkbtyp;
begin
  self := inherited Create(AOwner);
  if IDKontrollbericht > -1 then
  begin
    IWFrameRegion.JQDialogOptions.Title := 'Probe erstellen';
    self.IDKontrollbericht := IDKontrollbericht;
    // Probendatum
    DatepickerProbendatum.Date := Now;
    // Probenart
    ComboProbenart.Items.ClearNoSelection;
    ComboProbenart.Items.Add('Planprobe');
    ComboProbenart.Items.Add('Verdachtsprobe');
    ComboProbenart.Items.Add('Verdachtsprobe gemäß §56 LMSVG');
    // Futtermitteltyp
    ComboFuttermitteltyp.Items.ClearNoSelection;
    ComboFuttermitteltyp.Items.Add('Einzelfuttermittel');
    ComboFuttermitteltyp.Items.Add('Alleinfuttermittel');
    ComboFuttermitteltyp.Items.Add('Ergänzungsfuttermittel');
    ComboFuttermitteltyp.Items.Add('Tränkewasser');
    // Einzelfutterart
    ComboEinzelfutterart.Items.ClearNoSelection;
    ComboEinzelfutterart.Items.Add('Getreide');
    ComboEinzelfutterart.Items.Add('Ölsaaten/-früchte');
    ComboEinzelfutterart.Items.Add('Körnerleguminosen');
    ComboEinzelfutterart.Items.Add('Wurzeln und Knollen');
    ComboEinzelfutterart.Items.Add('Grünfutter/Rauhfutter');
    ComboEinzelfutterart.Items.Add('sonstiges');
    // Tierart
    ComboTierart.Items.ClearNoSelection;
    ComboTierart.Items.Add('Schweine');
    ComboTierart.Items.Add('Rinder');
    ComboTierart.Items.Add('Schafe');
    ComboTierart.Items.Add('Ziegen');
    ComboTierart.Items.Add('Geflügel');
    ComboTierart.Items.Add('Fisch');
    ComboTierart.Items.Add('Pferd');
    ComboTierart.Items.Add('andere Tiere');
    // Beschaffenheit
    ComboBeschaffenheit.Items.ClearNoSelection;
    ComboBeschaffenheit.Items.Add('ganz');
    ComboBeschaffenheit.Items.Add('geschrotet');
    ComboBeschaffenheit.Items.Add('gemahlen');
    ComboBeschaffenheit.Items.Add('gepresst');
    ComboBeschaffenheit.Items.Add('feucht');
    ComboBeschaffenheit.Items.Add('flüssig');
    ComboBeschaffenheit.Items.Add('pelliert');
    // Verpackung der Probe
    ComboVerpackung.Items.ClearNoSelection;
    ComboVerpackung.Items.Add('Kunststoffsack');
    ComboVerpackung.Items.Add('Papiersack');
    ComboVerpackung.Items.Add('Glasgefäß');
    ComboVerpackung.Items.Add('Kunststoffbehälter');
    // Verschluss der Probe
    ComboVerschluss.Items.ClearNoSelection;
    ComboVerschluss.Items.Add('offen');
    ComboVerschluss.Items.Add('Heftklammer');
    ComboVerschluss.Items.Add('verklebt');
    ComboVerschluss.Items.Add('verknotet');
    ComboVerschluss.Items.Add('vernäht');
    ComboVerschluss.Items.Add('verschraubt');
    ComboVerschluss.Items.Add('verschweißt');
    // Herkunft
    ComboHerkunft.Items.ClearNoSelection;
    ComboHerkunft.Items.Add('wirtschaftseigenes Futter');
    ComboHerkunft.Items.Add('zugekaufte Ware');
    ComboHerkunft.Items.Add('wirtschaftseigenes Futter und zugekaufte Ware');

    monitoring := Nil;
    verdacht := Nil;
    bkbtypen := Usersession.ELKERest.Berichtstypen;
    try
      for bkbtyp in bkbtypen do
      begin
        if bkbtyp.Probe = true then
        begin
          if bkbtyp.Bezeichnung.Contains('Verdacht') then
          begin
            verdacht := bkbtyp;
          end
          else if bkbtyp.Bezeichnung.Contains('Monitoring') then
          begin
            monitoring := bkbtyp;
          end;
        end;
      end;
    finally
      bkbtypen.Free;
    end;

    Reset;
  end;
end;

procedure TDialogProbeErstellen.FuttermitteltypDependencies(einzelfutter, beimischrate: boolean);
begin
  ComboEinzelfutterart.Enabled := einzelfutter;
  jqeBeimischrate.Enabled := beimischrate;
end;

procedure TDialogProbeErstellen.DoButtonCancel(ASender: TObject;
  AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogProbeErstellen.DoButtonOK(ASender: TObject; AParams: TStringList);
var
  Probe: TKbProbe;
  service: IMe;
  factory: IObjectFactory;
begin
  try
    service := Usersession.ELKERest.GetMeService;
    factory := TObjectFactory.Create;
    Probe := factory.Instance.Create<TKbProbe>;

    // Probenkennung
    if Length(jqeProbenkennung.Text) >= 3 then
    begin
      Probe.Probenkennung := jqeProbenkennung.Text
    end
    else
      raise Exception.Create('Probenkennungsfeld darf nicht leer sein und muss mindestens 3 Zeichen haben!');


    // Probenbezeichnung
    if jqeProbenbezeichnung.Text <> '' then
    begin
      Probe.Probenbezeichnung := jqeProbenbezeichnung.Text;
    end
    else
      raise Exception.Create('Probenbeschreibungsfeld darf nicht leer sein!');

    // Probenart
    if ComboProbenart.SelectedItem.Caption = 'Planprobe' then
    begin
      Probe.bkbtyp := monitoring;
    end
    else
    begin
      Probe.bkbtyp := verdacht;
    end;

    if not(ComboProbenart.NoItemSelected) then
    begin
      Probe.Probenart := ComboProbenart.SelectedItem.Caption;
      if ComboProbenart.SelectedItem.Caption.Contains('Verdacht') then
      begin
        if jqeVerdacht.Text <> '' then
        begin
          Probe.verdacht := jqeVerdacht.Text;
        end
        else
          raise Exception.Create('Verdachtsfeld darf nicht leer sein!');
        if jqeGewUnt.Text <> '' then
        begin
          Probe.Untersuchungsauftrag := jqeGewUnt.Text;
        end
        else
          raise Exception.Create('Untersuchungsparameterfeld darf nicht leer sein!');
      end;
    end
    else
      raise Exception.Create('Probenfeld darf nicht leer sein!');

    // Futtermitteltyp
    if ComboFuttermitteltyp.IsItemSelected then
    begin
      Probe.Verwendungszweck := ComboFuttermitteltyp.SelectedText;
      // Einzelfutter
      if ComboFuttermitteltyp.SelectedText = 'Einzelfuttermittel' then
      begin
        if ComboEinzelfutterart.IsItemSelected then
        begin
          Probe.Futterart := ComboEinzelfutterart.SelectedText;
        end
        else
        begin
          raise Exception.Create('Einzelfutterartfeld darf nicht leer sein!');
        end;
      end // Ergänzungsfutter
      else if ComboFuttermitteltyp.SelectedText = 'Ergänzungsfuttermittel' then
      begin
        if jqeBeimischrate.Text <> '' then
        begin
          Probe.beimischrate := StrToFloat(jqeBeimischrate.Text);
        end
        else
        begin
          raise Exception.Create('Beimischratefeld darf nicht leer sein!');
        end;
      end;
    end
    else
    begin
      raise Exception.Create('Futtermittelfeld darf nicht leer sein!');
    end;

    // Tierart und Kategorie
    if ComboTierart.IsItemSelected then
    begin
      Probe.TierArtLisa := ComboTierart.SelectedText;
    end
    else
    begin
      raise Exception.Create('Tierartfeld darf nicht leer sein!');
    end;
    if ComboTierkategorie.IsItemSelected then
    begin
      Probe.tierkategorie := ComboTierkategorie.SelectedText;
    end
    else
    begin
      raise Exception.Create('Tierkategoriefeld darf nicht leer sein!');
    end;

    // Menge
    if jqeVorgMenge.Text <> '' then
    begin
      Probe.VorgMenge := jqeVorgMenge.Text;
    end
    else
      raise Exception.Create('Mengenfeld darf nicht leer sein!');

    // Beschaffenheit
    if ComboBeschaffenheit.IsItemSelected then
    begin
      Probe.Beschaffenheit := ComboBeschaffenheit.SelectedText;
    end
    else
      raise Exception.Create('Beschaffenheitsfeld darf nicht leer sein!');
    // Verpackung
    if ComboVerpackung.IsItemSelected then
    begin
      Probe.Verpackung := ComboVerpackung.SelectedText;
    end
    else
      raise Exception.Create('Verpackungsfeld darf nicht leer sein!');
    // Verschluss
    if ComboVerschluss.IsItemSelected then
    begin
      Probe.Verschluss := ComboVerschluss.SelectedText;
    end
    else
      raise Exception.Create('Verschlusssfeld darf nicht leer sein!');
    // Versiegelt
    if iwcVersiegelt.Checked then
    begin
      Probe.Versiegelt := 'ja'
    end
    else
    begin
      Probe.Versiegelt := 'nein';
    end;
    if ComboHerkunft.IsItemSelected then
    begin
      Probe.Futtertyp := ComboHerkunft.SelectedText;
    end
    else
      raise Exception.Create('Herkunftsfeld darf nicht leer sein!');
    // Wirtschaftseigenes Futter
    if not(ComboHerkunft.SelectedText = 'wirtschaftseigenes Futter') then
    begin
      if iwcSackanhaenger.Checked then
      begin
        Probe.HerkZukauf := 'Sackanhaenger';
      end
      else
      begin
        if jqeHerkZukauf.Text <> '' then
        begin
          Probe.HerkZukauf := jqeHerkZukauf.Text;
        end
        else
          raise Exception.Create('Herkunft Zukauffeld darf nicht leer sein!');
      end;
    end;

    // Gegenprobe
    Probe.GegenprobeBelassen := iwcGegenprobe.Checked;
    if iwcGegenprobe.Checked = true then
    begin
      if Length(jqeGegenprobe.Text) >= 3 then
      begin
        Probe.KennzeichnungGegenprobe := jqeGegenprobe.Text;
      end
      else
        raise Exception.Create('Kenneichnung Gegenprobe Feld darf nicht leer sein und muss mindestens 3 Zeichen haben!');
    end;

    // Restliche daten
    Probe.Bemerkung := jqeBemerkung.Text;
    Probe.Datum := DatepickerProbendatum.Date;
    service.ProbeEintragen(IDKontrollbericht, Probe, true);

    alert.Info('Probe wurde erfolgreich eingetragen!');
    Close;
  except
    on E: Exception do
    begin
      alert.Error(E.Message);
    end;
  end;
end;

procedure TDialogProbeErstellen.InitializeControls;
begin
  inherited;
  // todo 1 -oCH -cImplementieren: Felder initialisieren
end;

procedure TDialogProbeErstellen.iwcGegenprobeJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
begin
  inherited;
  jqeGegenprobe.Enabled := iwcGegenprobe.Checked;
end;

procedure TDialogProbeErstellen.ComboFuttermitteltypSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if ComboFuttermitteltyp.SelectedText = 'Einzelfuttermittel' then
  begin
    FuttermitteltypDependencies(true, false);
  end
  else if ComboFuttermitteltyp.SelectedText = 'Alleinfuttermittel' then
  begin
    FuttermitteltypDependencies(false, false);
  end
  else if ComboFuttermitteltyp.SelectedText = 'Ergänzungsfuttermittel' then
  begin
    FuttermitteltypDependencies(false, true);
  end
  else if ComboFuttermitteltyp.SelectedText = 'Tränkewasser' then
  begin
    FuttermitteltypDependencies(false, false);
  end;
  ComboEinzelfutterart.ItemIndex := -1;
  jqeBeimischrate.Text := '';
  ComboTierart.ItemIndex := -1;
  ComboTierkategorie.ItemIndex := -1;
end;

procedure TDialogProbeErstellen.ComboHerkunftSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if ComboHerkunft.SelectedText = 'wirtschaftseigenes Futter' then
  begin
    iwcSackanhaenger.Enabled := false;
    jqeHerkZukauf.Enabled := false;
  end
  else
  begin
    iwcSackanhaenger.Enabled := true;
    jqeHerkZukauf.Enabled := true;
  end;
  iwcSackanhaenger.Checked := false;
  jqeHerkZukauf.Text := '';
end;

procedure TDialogProbeErstellen.ComboTierartSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if ComboTierart.SelectedText = 'Schweine' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Ferkel');
    ComboTierkategorie.Items.Add('Mastschweine');
    ComboTierkategorie.Items.Add('Sauen/Zuchtschweine');
  end
  else if ComboTierart.SelectedText = 'Rinder' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Kälber');
    ComboTierkategorie.Items.Add('Milchkühe');
    ComboTierkategorie.Items.Add('Mastrinder');
  end
  else if ComboTierart.SelectedText = 'Schafe' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Lämmer');
    ComboTierkategorie.Items.Add('Schafe');
    ComboTierkategorie.Items.Add('Wider');
  end
  else if ComboTierart.SelectedText = 'Ziegen' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Kitze');
    ComboTierkategorie.Items.Add('Geißen');
    ComboTierkategorie.Items.Add('Bock');
  end
  else if ComboTierart.SelectedText = 'Geflügel' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Masthüner');
    ComboTierkategorie.Items.Add('Legehennen');
    ComboTierkategorie.Items.Add('Mastputen');
    ComboTierkategorie.Items.Add('sonstiges Geflügel');
  end
  else if ComboTierart.SelectedText = 'Fisch' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Fisch');
  end
  else if ComboTierart.SelectedText = 'Pferd' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('Pferde');
  end
  else if ComboTierart.SelectedText = 'andere Tiere' then
  begin
    ComboTierkategorie.Items.ClearNoSelection;
    ComboTierkategorie.Items.Add('andere Tierarten');
  end;
   ComboTierkategorie.ItemIndex := -1;
end;

procedure TDialogProbeErstellen.ComboProbenartSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if ComboProbenart.SelectedItem.Caption.Contains('Verdacht') then
  begin
    jqeVerdacht.Enabled := true;
    jqeGewUnt.Enabled := true;
  end
  else
  begin
    jqeVerdacht.Enabled := false;
    jqeGewUnt.Enabled := false;
  end;
  jqeVerdacht.Text := '';
  jqeGewUnt.Text := '';
end;

procedure TDialogProbeErstellen.Reset;
begin
  ComboProbenart.ItemIndex := -1;
  ComboTierart.ItemIndex := -1;
  ComboTierkategorie.ItemIndex := -1;
  ComboVerpackung.ItemIndex := -1;
  ComboVerschluss.ItemIndex := -1;
  ComboHerkunft.ItemIndex := -1;
  ComboBeschaffenheit.ItemIndex := -1;
  ComboFuttermitteltyp.ItemIndex := -1;
  ComboTierkategorie.ItemIndex := -1;
  jqeProbenkennung.Text := '';
  jqeGegenprobe.Text := '';
  jqeGegenprobe.Enabled := false;
  jqeHerkZukauf.Text := '';
  jqeBemerkung.Text := '';
  jqeProbenbezeichnung.Text := '';
  jqeVorgMenge.Text := '';
  jqeBeimischrate.Text := '';
  jqeGewUnt.Text := '';
  jqeVerdacht.Text := '';
  jqeVerdacht.Enabled := false;
  jqeGewUnt.Enabled := false;
  jqeBeimischrate.Enabled := false;
  ComboEinzelfutterart.Enabled := false;
  ComboTierart.Enabled := true;
  ComboTierkategorie.Enabled := true;
  iwcSackanhaenger.Enabled := false;
  jqeHerkZukauf.Enabled := false;
  ComboProbenart.Enabled := true;
  iwcGegenprobe.Checked := false;
  iwcSackanhaenger.Checked := false;
  iwcVersiegelt.Checked := false;
end;

end.
