inherited DialogBetriebdetails: TDialogBetriebdetails
  Width = 519
  Height = 665
  ExplicitWidth = 519
  ExplicitHeight = 665
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 519
    Height = 665
    TabOrder = 2
    JQDialogOptions.Height = 665
    JQDialogOptions.Width = 519
    ExplicitWidth = 519
    ExplicitHeight = 665
    inherited RegionContent: TIWCGJQRegion
      Width = 519
      Height = 605
      ExplicitWidth = 519
      ExplicitHeight = 605
      object RegionMid: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 519
        Height = 605
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        object IWCGJQRegion1: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 519
          Height = 337
          TabOrder = 7
          Version = '1.0'
          Align = alTop
          object IWLabel1: TIWCGJQLabel
            Left = 16
            Top = 16
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Registrierungsnummer:'
          end
          object IWLabel10: TIWCGJQLabel
            Left = 16
            Top = 146
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Katastralgemeinde:'
          end
          object IWLabel2: TIWCGJQLabel
            Left = 16
            Top = 48
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Vulgo:'
          end
          object IWLabel3: TIWCGJQLabel
            Left = 16
            Top = 82
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Adresse:'
          end
          object IWLabel5: TIWCGJQLabel
            Left = 16
            Top = 178
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Telefon:'
          end
          object IWLabel6: TIWCGJQLabel
            Left = 16
            Top = 210
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'eMail:'
          end
          object IWLabel7: TIWCGJQLabel
            Left = 16
            Top = 240
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Aufsichtsorgan:'
          end
          object IWLabel8: TIWCGJQLabel
            Left = 16
            Top = 272
            Width = 140
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel1'
            Caption = 'Bemerkung:'
          end
          object LblAdresse: TIWCGJQLabel
            Left = 176
            Top = 82
            Width = 335
            Height = 50
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
          object LblAufischtsorgan: TIWCGJQLabel
            Left = 176
            Top = 240
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
          object LblBemerkung: TIWCGJQLabel
            Left = 176
            Top = 272
            Width = 313
            Height = 59
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
          object LblEmail: TIWCGJQLabel
            Left = 176
            Top = 210
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
          object LblKastralgemeinde: TIWCGJQLabel
            Left = 176
            Top = 146
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LblKastralgemeinde'
            Caption = 'LblKastralgemeinde'
          end
          object LblRegistrierungsnummer: TIWCGJQLabel
            Left = 176
            Top = 16
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LblRegistrierungsnummer'
            Caption = 'LblRegistrierungsnummer'
          end
          object LblTelefon: TIWCGJQLabel
            Left = 176
            Top = 178
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
          object LblVulgo: TIWCGJQLabel
            Left = 176
            Top = 48
            Width = 313
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel9'
            Caption = 'IWLabel9'
          end
        end
        object IWCGJQRegion2: TIWCGJQRegion
          Left = 0
          Top = 337
          Width = 519
          Height = 268
          TabOrder = 8
          Version = '1.0'
          Align = alClient
          object GridZulassung: TIWCGJQGrid
            Left = 0
            Top = 0
            Width = 519
            Height = 268
            TabOrder = 9
            Version = '1.0'
            Align = alClient
            JQGridOptions.ColModel = <
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'ZULNR'
                Name = 'ZULNR'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'ZulassungsNr'
              end
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Formatter = gcfDate
                FormatOptions.NewFormat = 'd.m.Y'
                Idx = 'BEGINNDATUM'
                Name = 'BEGINNDATUM'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'BEGINNDATUM'
              end
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Formatter = gcfDate
                FormatOptions.NewFormat = 'd.m.Y'
                Idx = 'ENDDATUM'
                Name = 'ENDDATUM'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'ENDDATUM'
              end
              item
                Align = gaRight
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Formatter = gcfCheckBox
                Idx = 'AKTIV'
                Name = 'AKTIV'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'AKTIV'
              end>
            JQGridOptions.Height = 214
            JQGridOptions.SubGridModel = <>
            JQGridOptions.Width = 517
            JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
            JQGridNav.Active = False
            JQGridNav.Add = False
            JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
            JQGridCustomButtons = <>
            JQGridProvider = ProviderZulassungen
            JQGridToolbarSearch.DefaultSearch = gsoContains
            JQDragAndDropOptions.ConnectWith = <>
          end
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 615
      Width = 517
      TabOrder = 0
      ExplicitTop = 615
      ExplicitWidth = 517
      inherited ButtonCancel: TIWCGJQButton
        Left = 409
        TabOrder = 5
        ExplicitLeft = 409
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 401
        inherited ButtonOK: TIWCGJQButton
          TabOrder = 4
          ExplicitLeft = 293
        end
      end
    end
  end
  object ProviderZulassungen: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.QBetriebZulassung
    Left = 376
    Top = 16
  end
end
