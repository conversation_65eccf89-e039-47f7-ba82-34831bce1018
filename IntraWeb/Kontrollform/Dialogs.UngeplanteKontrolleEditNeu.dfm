﻿inherited UngeplanteKontrolleEditNeu: TUngeplanteKontrolleEditNeu
  Width = 803
  Height = 697
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 803
    Height = 697
    TabOrder = 1
    JQDialogOptions.Height = 697
    JQDialogOptions.Width = 803
    inherited RegionContent: TIWCGJQRegion
      Width = 803
      Height = 637
      TabOrder = 4
      object LabelKontrollberichttyp: TIWCGJQLabel
        Left = 124
        Top = 44
        Width = 138
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrollberichttyp'
        Caption = 'Kontrollberichtstyp:'
      end
      object LabelKontrolltyp: TIWCGJQLabel
        Left = 179
        Top = 80
        Width = 83
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrolltyp'
        Caption = 'Kontrolltyp:'
      end
      object LabelGruppe: TIWCGJQLabel
        Left = 204
        Top = 336
        Width = 58
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelGruppe'
        Caption = 'Gruppe:'
      end
      object LabelKontrollorgan: TIWCGJQLabel
        Left = 160
        Top = 298
        Width = 102
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrollorgan'
        Caption = 'Kontrollorgan:'
      end
      object LabelFälligkeitsdatum: TIWCGJQLabel
        Left = 142
        Top = 261
        Width = 120
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelF'#228'lligkeitsdatum'
        Caption = 'F'#228'lligkeitsdatum:'
      end
      object LabelKontrollgrund: TIWCGJQLabel
        Left = 159
        Top = 220
        Width = 103
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrollgrund'
        Caption = 'Kontrollgrund:'
      end
      object LabelBetrieb: TIWCGJQLabel
        Left = 207
        Top = 114
        Width = 55
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelBetrieb'
        Caption = 'Betrieb:'
      end
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 174
        Top = 494
        Width = 98
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelAngemeldet'
        Caption = 'Interne Notiz:'
      end
      object IWCGJQLabel2: TIWCGJQLabel
        Left = 104
        Top = 375
        Width = 168
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelAngemeldet'
        Caption = 'Kontroll-Informationen:'
      end
      object MemoBetrieb: TIWCGJQMemoEx
        Left = 278
        Top = 114
        Width = 370
        Height = 90
        TabOrder = 5
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = False
        Required = False
        SubmitOnAsyncEvent = True
        ReadOnly = True
      end
      object DatePickFaelligkeit: TIWCGJQDatePicker
        Left = 278
        Top = 261
        Width = 370
        Height = 21
        TabOrder = 8
        Version = '1.0'
        Caption = ''
        JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
      end
      object CheckKontrollorgan: TIWCGJQCheckBoxEx
        Left = 278
        Top = 298
        Width = 18
        Height = 21
        TabOrder = 6
        Version = '1.0'
        Caption = ''
        JQCheckExOptions.OnChange.OnEvent = CheckKontrollorganJQCheckExOptionsChange
      end
      object CheckGruppe: TIWCGJQCheckBoxEx
        Left = 278
        Top = 336
        Width = 18
        Height = 21
        TabOrder = 7
        Version = '1.0'
        Caption = ''
        JQCheckExOptions.OnChange.OnEvent = CheckGruppeJQCheckExOptionsChange
      end
      object ButtonBetrieb: TIWCGJQButton
        Left = 656
        Top = 114
        Width = 33
        Height = 21
        TabOrder = 9
        Version = '1.0'
        JQButtonOptions.Icons.Primary = 'ui-icon-pencil'
        JQButtonOptions.OnClick.OnEvent = ButtonBetriebJQButtonOptionsClick
      end
      object DropKontrollorgan: TIWCGJQDropDown
        Left = 302
        Top = 296
        Width = 346
        Height = 25
        TabOrder = 10
        Version = '1.0'
        JQDropDownOptions.OnChange.OnEvent = DropKontrollorganJQDropDownOptionsChange
        JQDropDownOptions.InfiniteScroll = True
        Groups = <>
        Items = <>
      end
      object DropGruppe: TIWCGJQDropDown
        Left = 302
        Top = 336
        Width = 346
        Height = 25
        TabOrder = 11
        Version = '1.0'
        Enabled = False
        JQDropDownOptions.OnChange.OnEvent = DropGruppeJQDropDownOptionsChange
        Groups = <>
        Items = <>
      end
      object DropKontrollgrund: TIWCGJQDropDown
        Left = 278
        Top = 220
        Width = 370
        Height = 25
        TabOrder = 12
        Version = '1.0'
        DataLink.DataSource = DSKontrolle
        DataLink.FieldName = 'ID_RECHTSGRUNDLAGE'
        Groups = <>
        Items = <>
      end
      object DropKontrolltyp: TIWCGJQDropDown
        Left = 278
        Top = 80
        Width = 370
        Height = 25
        TabOrder = 13
        Version = '1.0'
        DataLink.DataSource = DSKontrolle
        DataLink.FieldName = 'KONTROLLTYP'
        Groups = <>
        Items = <>
      end
      object DropBerichtstyp: TIWCGJQDropDown
        Left = 278
        Top = 38
        Width = 370
        Height = 25
        TabOrder = 14
        Version = '1.0'
        DataLink.DataSource = DSKontrolle
        DataLink.FieldName = 'BKBTYP'
        JQDropDownOptions.OnChange.OnEvent = DropBerichtstypJQDropDownOptionsChange
        Groups = <>
        Items = <>
      end
      object MemoKontrollInformationen: TIWCGJQMemoEx
        Left = 278
        Top = 375
        Width = 370
        Height = 105
        TabOrder = 16
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:370px !important')
      end
      object MemoInterneNotiz: TIWCGJQMemoEx
        Left = 278
        Top = 494
        Width = 370
        Height = 105
        TabOrder = 17
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:370px !important')
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 647
      Width = 801
      TabOrder = 0
      inherited ButtonCancel: TIWCGJQButton
        Left = 693
        TabOrder = 15
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 685
        TabOrder = 2
        inherited ButtonOK: TIWCGJQButton
          Left = 577
          TabOrder = 3
        end
      end
    end
  end
  object DSBetrieb: TDataSource
    DataSet = DMKontrolle.QBetriebDetail
    Left = 24
    Top = 431
  end
  object DSKontrolle: TDataSource
    DataSet = DMKontrolle.QUngeplanteKontrolle
    Left = 88
    Top = 431
  end
end
