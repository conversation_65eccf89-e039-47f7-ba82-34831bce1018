inherited UngeplanteKontrollen: TUngeplanteKontrollen
  Width = 1192
  Height = 796
  Align = alClient
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1192
    Height = 796
    RenderInvisibleControls = True
    TabOrder = 6
    object DialogUngeplanteKontrolle: TIWCGJQDialog
      Left = 130
      Top = 61
      Width = 927
      Height = 540
      Visible = False
      TabOrder = 13
      Version = '1.0'
      Align = alNone
      ZIndex = 2000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.CloseOnEscape = False
      JQDialogOptions.Height = 640
      JQDialogOptions.Modal = True
      JQDialogOptions.Resizable = False
      JQDialogOptions.Width = 927
      JQDialogOptions.zIndex = 2000
      JQDialogOptions.Buttons = <
        item
          Text = 'Schliessen'
          OnClick.OnEvent = DialogAbbrechen
        end>
    end
    object iwrBot: TIWCGJQRegion
      Left = 0
      Top = 746
      Width = 1192
      Height = 50
      RenderInvisibleControls = True
      TabOrder = 14
      Version = '1.0'
      Align = alBottom
      object LabelCount: TIWLabel
        Left = 173
        Top = 16
        Width = 0
        Height = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        NoWrap = True
        HasTabOrder = False
        FriendlyName = 'LabelCount'
      end
      object ButtonCSVExport: TIWCGJQButton
        Left = 17
        Top = 16
        Width = 150
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Ergebnis exportieren'
        JQEvents.OnClick.OnEvent = ButtonCSVExportOnClick
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 80
      Width = 1192
      Height = 666
      RenderInvisibleControls = True
      Version = '1.0'
      Align = alClient
      object GridKontrollen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1192
        Height = 666
        TabOrder = 4
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Faelligkeitsdatum'
            Name = 'Faelligkeitsdatum'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'F'#228'lligkeitsdatum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollbericht Typ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrolltypTyp'
            Name = 'KontrolltypTyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll Typ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BetriebName'
            Name = 'BetriebName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betrieb'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GemeindeNummer'
            Name = 'GemeindeNummer'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gemeinde'
            Position = 10
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LFBIS'
            Name = 'LFBIS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'LFBIS'
            Position = 4
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebstyp'
            Name = 'Betriebstyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'Typ'
            Position = 5
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'RechtsgrundlageBez'
            Name = 'RechtsgrundlageBez'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollgrund'
            Position = 6
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrollorganName'
            Name = 'KontrollorganName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollorgan'
            Position = 7
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrollInformationen'
            Name = 'KontrollInformationen'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll-Informationen'
            ProviderName = 'KontrollInformationen'
            Position = 8
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GruppeQuelle'
            Name = 'GruppeQuelle'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
            Position = 9
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ'
            Name = 'PLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'PLZ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Ort'
            Name = 'Ort'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ort'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Strasse'
            Name = 'Strasse'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Strasse'
          end>
        JQGridOptions.Height = 618
        JQGridOptions.LoadOnce = True
        JQGridOptions.RowNum = 200
        JQGridOptions.Sortable = True
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1190
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = GridKontrollenJQGridOptionsLoadComplete
        JQGridOptions.OnSelectRow.Ajax = False
        JQGridOptions.OnSelectRow.AjaxAppend = False
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridOptions.PagerVisible = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderUngGeplanteKontrollen
        JQGridToolbarSearch.Active = True
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1192
      Height = 80
      RenderInvisibleControls = True
      TabOrder = 2
      Version = '1.0'
      Align = alTop
      object IWLabel4: TIWCGJQLabel
        Left = 17
        Top = 11
        Width = 29
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Von:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 173
        Top = 11
        Width = 23
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bis:'
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 329
        Top = 33
        Width = 100
        Height = 21
        TabOrder = 12
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQEvents.OnClick.Ajax = False
        JQEvents.OnClick.AjaxAppend = False
        JQEvents.OnClick.OnEvent = ButtonAbfragenOnClick
      end
      object jqdVon: TIWCGJQDatePicker
        Left = 17
        Top = 33
        Width = 150
        Height = 21
        TabOrder = 5
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object jqdBis: TIWCGJQDatePicker
        Left = 173
        Top = 33
        Width = 150
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object IWCGJQRegion1: TIWCGJQRegion
        Left = 615
        Top = 0
        Width = 577
        Height = 80
        TabOrder = 8
        Version = '1.0'
        Align = alRight
        DesignSize = (
          577
          80)
        object ButtonStornieren: TIWCGJQButton
          Left = 463
          Top = 33
          Width = 100
          Height = 21
          TabOrder = 9
          Version = '1.0'
          JQButtonOptions.Label_ = 'Stornieren'
          JQButtonOptions.OnClick.OnEvent = ButtonStornierenOnClick
        end
        object ButtonBearbeiten: TIWCGJQButton
          Left = 252
          Top = 34
          Width = 100
          Height = 21
          TabOrder = 10
          Version = '1.0'
          JQButtonOptions.Label_ = 'Bearbeiten'
          JQButtonOptions.OnClick.OnEvent = ButtonBearbeitenOnClick
        end
        object ButtonHinzufuegen: TIWCGJQButton
          Left = 358
          Top = 33
          Width = 100
          Height = 21
          TabOrder = 11
          Version = '1.0'
          JQButtonOptions.Label_ = 'Hinzuf'#252'gen'
          JQButtonOptions.OnClick.OnEvent = ButtonHinzufuegenOnClick
        end
        object ButtonZuweisen: TIWCGJQButton
          Left = 146
          Top = 34
          Width = 100
          Height = 21
          Hint = 'Ausgew'#228'hlte Kontrolle einem Kontrollorgan zuweisen'
          TabOrder = 1
          Version = '1.0'
          Anchors = [akTop, akRight]
          JQButtonOptions.Label_ = 'Zuweisen'
          JQButtonOptions.OnClick.OnEvent = ButtonZuweisenOnClick
        end
      end
    end
  end
  object DownloadUngeplant: TIWCGJQFileDownload
    Version = '1.0'
    Left = 625
    Top = 17
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 705
    Top = 18
  end
  object ProviderUngGeplanteKontrollen: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.MTUngeplanteKontrollen
    KeyFields = 'ID'
    Left = 496
    Top = 16
  end
end
