inherited DialogAnwesende: TDialogAnwesende
  Height = 414
  ExplicitHeight = 414
  inherited IWFrameRegion: TIWCGJQDialog
    Height = 414
    TabOrder = 2
    JQDialogOptions.Height = 414
    ExplicitHeight = 414
    inherited RegionContent: TIWCGJQRegion
      Height = 354
      ExplicitHeight = 354
      object IWCGJQRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 600
        Height = 354
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        object GridAnwesende: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 600
          Height = 354
          TabOrder = 7
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NAME'
              Name = 'NAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'EMAIL'
              Name = 'EMAIL'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'eMail'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'KOMMUNIKATIONSBERECHTIGT'
              Name = 'KOMMUNIKATIONSBERECHTIGT'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kommunikationsberechtigt'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 300
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 598
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Active = False
          JQGridNav.Add = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderAnwesende
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 364
      TabOrder = 0
      ExplicitTop = 364
      inherited ButtonCancel: TIWCGJQButton
        TabOrder = 5
      end
      inherited RegionOK: TIWCGJQRegion
        inherited ButtonOK: TIWCGJQButton
          TabOrder = 4
        end
      end
    end
  end
  object ProviderAnwesende: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.QAnwesende
    Left = 520
    Top = 24
  end
end
