inherited DialogKontrolleWeitergeben: TDialogKontrolleWeitergeben
  Width = 700
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 700
    TabOrder = 0
    JQDialogOptions.Width = 700
    inherited RegionContent: TIWCGJQRegion
      Width = 700
      TabOrder = 1
      object GridPersonen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 700
        Height = 340
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PersonNachname'
            Name = 'PersonNachname'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Nachname'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PersonVorname'
            Name = 'PersonVorname'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Vorname'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Gruppe'
            Name = 'Gruppe'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
          end>
        JQGridOptions.Height = 286
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 698
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderGruppenPersonen
        JQGridToolbarSearch.Active = True
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Width = 698
      TabOrder = 5
      inherited ButtonCancel: TIWCGJQButton
        Left = 590
        TabOrder = 4
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 582
        inherited ButtonOK: TIWCGJQButton
          Left = 474
          TabOrder = 2
        end
      end
    end
  end
  object ProviderGruppenPersonen: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.MTGruppenPersonen
    KeyFields = 'ID'
    Left = 512
    Top = 8
  end
end
