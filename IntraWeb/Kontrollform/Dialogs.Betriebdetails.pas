﻿unit Dialogs.Betriebdetails;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, Modules.Kontrolle, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQGrid, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQLabel, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp,
  IWCGJQSweetAlert;

type
  //TDialogBetriebdetails = class(TDialogBase)
  TDialogBetriebdetails = class(TDialogBase<TDMKontrolle>)
    RegionMid: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    LblRegistrierungsnummer: TIWCGJQLabel;
    LblVulgo: TIWCGJQLabel;
    LblAdresse: TIWCGJQLabel;
    LblTelefon: TIWCGJQLabel;
    LblEmail: TIWCGJQLabel;
    LblAufischtsorgan: TIWCGJQLabel;
    LblBemerkung: TIWCGJQLabel;
    LblKastralgemeinde: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    GridZulassung: TIWCGJQGrid;
    ProviderZulassungen: TIWCGJQGridDataSetProvider;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQRegion2: TIWCGJQRegion;
  private
    IDBetrieb: integer;
  public
    constructor Create(AOwner: TComponent; LIDBetrieb: integer); reintroduce;
    procedure DoButtonCancel(ASender: TObject; AParams: TStringList); override;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}


constructor TDialogBetriebdetails.Create(AOwner: TComponent; LIDBetrieb: integer);
begin
  self := inherited Create(AOwner);
  if LIDBetrieb > -1 then
  begin

    IDBetrieb := LIDBetrieb;
    DM.OpenBetriebDetails(IDBetrieb);

    ProviderZulassungen.DataSet := DM.QBetriebZulassung;

    IWFrameRegion.JQDialogOptions.Title := 'Betrieb: ' + DM.QBetriebDetailName.AsString;

    LblRegistrierungsnummer.Caption := DM.QBetriebDetailRegnr.AsString;
    LblVulgo.Caption := DM.QBetriebDetailVulgo.AsString;
    LblAdresse.Caption := DM.QBetriebDetailStrasse.AsString + sLineBreak + DM.QBetriebDetailPLZ.AsString + ', ' +
      DM.QBetriebDetailOrt.AsString;
    LblKastralgemeinde.Caption := DM.QBetriebDetailKatastralgemname.AsString;
    LblTelefon.Caption := DM.QBetriebDetailtelefon.AsString;
    LblEmail.Caption := DM.QBetriebDetailemail.AsString;
    LblAufischtsorgan.Caption := DM.QBetriebDetailAufsichtsorgan.AsString;
    LblBemerkung.Caption := DM.QBetriebDetailAnmerkung.AsString;
  end;
end;

procedure TDialogBetriebdetails.DoButtonCancel(ASender: TObject;
  AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogBetriebdetails.DoButtonOK(ASender: TObject; AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogBetriebdetails.InitializeControls;
begin
  inherited;
   //todo 1 -oCH -cImplementieren: Felder initialisieren
end;

end.
