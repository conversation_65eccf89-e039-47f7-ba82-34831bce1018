inherited DialogUngeplanteKontrolleBetrieb: TDialogUngeplanteKontrolleBetrieb
  Width = 813
  Height = 465
  ExplicitWidth = 813
  ExplicitHeight = 465
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 813
    Height = 465
    TabOrder = 5
    JQDialogOptions.Height = 465
    JQDialogOptions.Width = 813
    ExplicitWidth = 813
    ExplicitHeight = 465
    inherited RegionContent: TIWCGJQRegion
      Width = 813
      Height = 405
      TabOrder = 2
      ExplicitWidth = 813
      ExplicitHeight = 405
      object iwrBetriebe: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 813
        Height = 345
        RenderInvisibleControls = True
        TabOrder = 7
        Version = '1.0'
        Align = alClient
        object GridBetriebe: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 813
          Height = 345
          TabOrder = 8
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'REGNR'
              Name = 'REGNR'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'REGNR'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NAME'
              Name = 'NAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'STRASSE'
              Name = 'STRASSE'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Stra'#223'e'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'PLZ'
              Name = 'PLZ'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'PLZ'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ORT'
              Name = 'ORT'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'ORT'
            end>
          JQGridOptions.Height = 318
          JQGridOptions.RowNum = 200
          JQGridOptions.Sortable = True
          JQGridOptions.SubGridModel = <>
          JQGridOptions.ViewRecords = True
          JQGridOptions.Width = 811
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.OnLoadComplete.OnEvent = GridBetriebeJQGridOptionsLoadComplete
          JQGridOptions.PagerVisible = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = DSPBetriebe
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrBetriebSuchen: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 813
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 9
        Version = '1.0'
        Align = alTop
        object jqbBetriebSuchen: TIWCGJQButton
          Left = 669
          Top = 16
          Width = 134
          Height = 30
          TabOrder = 6
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.OnEvent = jqbBetriebSuchenJQButtonOptionsClick
        end
        object EditBetriebsuche: TIWCGJQEdit
          Left = 16
          Top = 16
          Width = 647
          Height = 30
          TabOrder = 10
          Version = '1.0'
          ScriptEvents = <>
          Text = ''
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 415
      Width = 811
      TabOrder = 4
      ExplicitTop = 415
      ExplicitWidth = 811
      inherited ButtonCancel: TIWCGJQButton
        Left = 703
        TabOrder = 3
        ExplicitLeft = 703
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 695
        TabOrder = 0
        ExplicitWidth = 695
        object LabelCount: TIWLabel [0]
          Left = 24
          Top = 16
          Width = 0
          Height = 0
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          NoWrap = True
          HasTabOrder = False
          FriendlyName = 'LabelCount'
        end
        inherited ButtonOK: TIWCGJQButton
          Left = 587
          TabOrder = 1
          ExplicitLeft = 587
        end
      end
    end
  end
  object DSPBetriebe: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebeSuche
    KeyFields = 'ID'
    Left = 321
    Top = 420
  end
  object DSBetriebe: TDataSource
    DataSet = DMStammdaten.QBetriebeSuche
    Left = 380
    Top = 420
  end
end
