inherited DialogKontrollDetails: TDialogKontrollDetails
  Width = 1200
  Height = 725
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 1200
    Height = 725
    TabOrder = 1
    JQDialogOptions.Height = 725
    JQDialogOptions.Width = 1200
    inherited RegionContent: TIWCGJQRegion
      Top = 32
      Width = 1200
      Height = 627
      Margins.Top = 32
      Margins.Bottom = 16
      TabOrder = 4
      object RegionAllgemein: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1200
        Height = 220
        TabOrder = 5
        Version = '1.0'
        Align = alTop
        DesignSize = (
          1200
          220)
        object IWLabel1: TIWCGJQLabel
          Left = 8
          Top = 16
          Width = 136
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Betrieb:'
        end
        object IWLabel10: TIWCGJQLabel
          Left = 620
          Top = 38
          Width = 100
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Geplant am:'
        end
        object IWLabel12: TIWCGJQLabel
          Left = 923
          Top = 16
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Ende:'
        end
        object LabelStornoDatumBeschriftung: TIWCGJQLabel
          Left = 837
          Top = 60
          Width = 176
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Storniert/Verweigert am:'
        end
        object IWLabel16: TIWCGJQLabel
          Left = 923
          Top = 38
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Angemeldet:'
        end
        object LabelStornoGrundBeschriftung: TIWCGJQLabel
          Left = 971
          Top = 82
          Width = 42
          Height = 16
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Grund:'
        end
        object IWLabel2: TIWCGJQLabel
          Left = 8
          Top = 38
          Width = 136
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Anwesende:'
        end
        object IWLabel3: TIWCGJQLabel
          Left = 8
          Top = 60
          Width = 136
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Betriebstyp:'
        end
        object LabelUnterschriftVerweigert: TIWCGJQLabel
          Left = 13
          Top = 82
          Width = 131
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Keine Unterschrift:'
        end
        object LabelBemerkung: TIWCGJQLabel
          Left = 13
          Top = 104
          Width = 75
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Bemerkung:'
        end
        object IWLabel6: TIWCGJQLabel
          Left = 366
          Top = 16
          Width = 99
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Kontrollorgan:'
        end
        object IWLabel7: TIWCGJQLabel
          Left = 375
          Top = 38
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Erfasser:'
        end
        object LabelKontrollgrundBeschriftung: TIWCGJQLabel
          Left = 366
          Top = 82
          Width = 99
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Kontrollgrund:'
        end
        object IWLabel9: TIWCGJQLabel
          Left = 620
          Top = 16
          Width = 100
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Beginn:'
        end
        object LblAngemeldet: TIWCGJQLabel
          Left = 1019
          Top = 38
          Width = 164
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblAnwesende: TIWCGJQLabel
          Left = 150
          Top = 38
          Width = 140
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblBegin: TIWCGJQLabel
          Left = 726
          Top = 16
          Width = 136
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblBetrieb: TIWCGJQLabel
          Left = 150
          Top = 16
          Width = 140
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblBetriebstyp: TIWCGJQLabel
          Left = 150
          Top = 60
          Width = 140
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblEnde: TIWCGJQLabel
          Left = 1019
          Top = 16
          Width = 164
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblErfasser: TIWCGJQLabel
          Left = 471
          Top = 38
          Width = 140
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblGeplantAm: TIWCGJQLabel
          Left = 726
          Top = 38
          Width = 136
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LabelStornoGrundInhalt: TIWCGJQLabel
          Left = 1019
          Top = 82
          Width = 164
          Height = 38
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblKontrollgrund: TIWCGJQLabel
          Left = 471
          Top = 82
          Width = 249
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LblKontrollorgan: TIWCGJQLabel
          Left = 471
          Top = 16
          Width = 140
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LabelStornoDatumInhalt: TIWCGJQLabel
          Left = 1019
          Top = 60
          Width = 164
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object LabelUnterschriftVerweigertInhalt: TIWCGJQLabel
          Left = 150
          Top = 82
          Width = 219
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = '{Grund}'
        end
        object IWCGJQLabel1: TIWCGJQLabel
          Left = 398
          Top = 104
          Width = 146
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Kontroll-Informationen:'
        end
        object IWCGJQLabel2: TIWCGJQLabel
          Left = 786
          Top = 104
          Width = 85
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Interne Notiz:'
        end
        object MemoInterneNotiz: TIWCGJQMemo
          Left = 784
          Top = 126
          Width = 399
          Height = 83
          Anchors = [akTop, akRight]
          StyleRenderOptions.RenderFont = False
          StyleRenderOptions.RenderBorder = False
          BGColor = clNone
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          InvisibleBorder = False
          HorizScrollBar = False
          VertScrollBar = True
          Required = False
          SubmitOnAsyncEvent = True
          FriendlyName = 'MemoInterneNotiz'
        end
        object MemoKontrollInfo: TIWCGJQMemo
          Left = 398
          Top = 126
          Width = 380
          Height = 83
          Anchors = [akLeft, akTop, akRight]
          StyleRenderOptions.RenderFont = False
          StyleRenderOptions.RenderBorder = False
          BGColor = clNone
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          InvisibleBorder = False
          HorizScrollBar = False
          VertScrollBar = True
          Required = False
          SubmitOnAsyncEvent = True
          FriendlyName = 'IWCGJQMemo1'
        end
        object MemoBemerkung: TIWCGJQMemo
          Left = 12
          Top = 126
          Width = 380
          Height = 83
          StyleRenderOptions.RenderFont = False
          StyleRenderOptions.RenderBorder = False
          BGColor = clNone
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          InvisibleBorder = False
          HorizScrollBar = False
          VertScrollBar = True
          Required = False
          SubmitOnAsyncEvent = True
          FriendlyName = 'MemoBemerkung'
        end
        object IWCGJQLabel3: TIWCGJQLabel
          Left = 375
          Top = 60
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
          Caption = 'Gruppe:'
        end
        object LabelGruppe: TIWCGJQLabel
          Left = 471
          Top = 60
          Width = 381
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel1'
        end
        object ButtonAnwesende: TIWCGJQButton
          Left = 309
          Top = 43
          Width = 60
          Height = 21
          TabOrder = 6
          Version = '1.0'
          JQButtonOptions.Label_ = 'Details'
          JQEvents.OnClick.OnEvent = ButtonAnwesendeOnClick
        end
        object ButtonBetrieb: TIWCGJQButton
          Left = 309
          Top = 16
          Width = 60
          Height = 21
          TabOrder = 7
          Version = '1.0'
          JQButtonOptions.Label_ = 'Details'
          JQEvents.OnClick.OnEvent = ButtonBetriebOnClick
        end
      end
      object RegionMassnahmen: TIWCGJQRegion
        Left = 0
        Top = 220
        Width = 1200
        Height = 247
        TabOrder = 8
        Version = '1.0'
        Align = alClient
        object GridMassnahmen: TIWCGJQGrid
          Left = 0
          Top = 36
          Width = 1200
          Height = 211
          TabOrder = 9
          Version = '1.0'
          Align = alClient
          ZIndex = 2001
          JQGridOptions.AutoWidth = True
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Massnahme'
              Name = 'Massnahme'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 250
              Caption = 'Massnahme'
              ProviderName = 'Massnahme'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Status'
              Name = 'Status'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 75
              Caption = 'Status'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Formatter = gcfDate
              FormatOptions.NewFormat = 'd.m.Y'
              Idx = 'Frist'
              Name = 'Frist'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 75
              Caption = 'Frist'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Beschreibung'
              Name = 'Beschreibung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 250
              Caption = 'Beschreibung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Formatter = gcfDate
              FormatOptions.NewFormat = 'd.m.Y'
              Idx = 'Beseitigt_am'
              Name = 'Beseitigt_am'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 75
              Caption = 'Beseitigt Am'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Mangel'
              Name = 'Mangel'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 250
              Caption = 'Mangel'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Behebungsauftrag'
              Name = 'Behebungsauftrag'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Width = 250
              Caption = 'Behebungsauftrag'
            end
            item
              Align = gaRight
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bilder'
              Name = 'Bilder'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bilder'
            end>
          JQGridOptions.Height = 157
          JQGridOptions.SubGridModel = <>
          JQGridOptions.ViewRecords = True
          JQGridOptions.Width = 1198
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Active = False
          JQGridNav.Del = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderMassnahmen
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
        object IWCGJQRegion1: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 1200
          Height = 36
          TabOrder = 10
          Version = '1.0'
          Align = alTop
          object IWLabel13: TIWCGJQLabel
            Left = 3
            Top = 6
            Width = 85
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = [fsBold]
            HasTabOrder = False
            FriendlyName = 'IWLabel13'
            Caption = 'Ma'#223'nahmen'
          end
          object IWCGJQRegion2: TIWCGJQRegion
            Left = 670
            Top = 0
            Width = 530
            Height = 36
            TabOrder = 13
            Version = '1.0'
            Align = alRight
            object ButtonMangelAbschliesen: TIWCGJQButton
              Left = 354
              Top = 6
              Width = 160
              Height = 21
              TabOrder = 11
              Version = '1.0'
              JQButtonOptions.Label_ = 'Mangel abschlie'#223'en'
              JQButtonOptions.OnClick.OnEvent = ButtonMangelAbschliessenOnClick
            end
            object ButtonFristVerlaengern: TIWCGJQButton
              Left = 188
              Top = 6
              Width = 160
              Height = 21
              TabOrder = 12
              Version = '1.0'
              JQButtonOptions.Label_ = 'Frist verl'#228'ngern'
              JQButtonOptions.OnClick.OnEvent = ButtonFirstVerlaengernOnClick
            end
          end
        end
        object ButtonBilderAnzeigen: TIWCGJQButton
          Left = 692
          Top = 6
          Width = 160
          Height = 21
          TabOrder = 23
          Version = '1.0'
          JQButtonOptions.Label_ = 'Bilder anzeigen'
          JQButtonOptions.OnClick.OnEvent = ButtonBilderAnzeigenJQButtonOptionsClick
        end
      end
      object RegionProben: TIWCGJQRegion
        Left = 0
        Top = 467
        Width = 1200
        Height = 160
        TabOrder = 14
        Version = '1.0'
        Align = alBottom
        object GridProben: TIWCGJQGrid
          Left = 0
          Top = 36
          Width = 1200
          Height = 124
          TabOrder = 15
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Grenzwertueberschreitung'
              Name = 'Grenzwertueberschreitung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'GW'#220
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Status'
              Name = 'Status'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Status'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Datum'
              Name = 'Datum'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Datum'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Probenbkb'
              Name = 'Probenbkb'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Probenbkb'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Probenart'
              Name = 'Probenart'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Probenart'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bemerkung'
              Name = 'Bemerkung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bemerkung'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Verdacht'
              Name = 'Verdacht'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Verdacht'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Ages_Probenstatus'
              Name = 'Ages_Probenstatus'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'AGES Status'
            end>
          JQGridOptions.Height = 70
          JQGridOptions.SubGridModel = <>
          JQGridOptions.ViewRecords = True
          JQGridOptions.Width = 1198
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.RowAttrFn.Script = 
            'function (ts, rd, cur, id){'#13#10'  if(ts.Grenzwertueberschreitung ==' +
            ' "true"){'#13#10'    return {"style":"font-style:normal; color:red"}'#13#10 +
            '  }'#13#10'  else{'#13#10'    return {"style":"font-style:normal; color:blac' +
            'k"}    '#13#10'  }'#13#10'}'
          JQGridNav.Active = False
          JQGridNav.Del = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderProben
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
        object IWCGJQRegion4: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 1200
          Height = 36
          TabOrder = 16
          Version = '1.0'
          Align = alTop
          object IWLabel14: TIWCGJQLabel
            Left = 3
            Top = 6
            Width = 50
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = [fsBold]
            HasTabOrder = False
            FriendlyName = 'IWLabel13'
            Caption = 'Proben'
          end
          object IWCGJQRegion5: TIWCGJQRegion
            Left = 366
            Top = 0
            Width = 834
            Height = 36
            TabOrder = 17
            Version = '1.0'
            Align = alRight
            object ButtonAgesErgebnisbericht: TIWCGJQButton
              Left = 657
              Top = 6
              Width = 160
              Height = 21
              TabOrder = 18
              Version = '1.0'
              JQButtonOptions.Label_ = 'AGES Ergebnisbericht'
              JQEvents.OnClick.OnEvent = ButtonAGESErgebnisOnClick
            end
            object ButtonProbenbegleitschein: TIWCGJQButton
              Left = 491
              Top = 6
              Width = 160
              Height = 21
              TabOrder = 19
              Version = '1.0'
              JQButtonOptions.Label_ = 'Probenbegleitschein'
              JQEvents.OnClick.OnEvent = ButtonProbenbegleitscheinOnClick
            end
            object ButtonEntfernen: TIWCGJQButton
              Left = 279
              Top = 6
              Width = 100
              Height = 21
              Visible = False
              TabOrder = 20
              Version = '1.0'
              JQButtonOptions.Label_ = 'Entfernen'
            end
            object ButtonHinzufuegen: TIWCGJQButton
              Left = 385
              Top = 6
              Width = 100
              Height = 21
              TabOrder = 21
              Version = '1.0'
              JQButtonOptions.Label_ = 'Hinzuf'#252'gen'
              JQButtonOptions.OnClick.Ajax = False
              JQButtonOptions.OnClick.AjaxAppend = False
              JQEvents.OnClick.OnEvent = ButtonHinzufuegenOnClick
            end
          end
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 675
      Width = 1198
      TabOrder = 0
      inherited ButtonCancel: TIWCGJQButton
        Left = 1090
        TabOrder = 22
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 1082
        TabOrder = 2
        inherited ButtonOK: TIWCGJQButton
          Left = 974
          TabOrder = 3
        end
        object ButtonKontrollbericht: TIWCGJQButton
          AlignWithMargins = True
          Left = 8
          Top = 8
          Width = 241
          Height = 34
          Margins.Left = 8
          Margins.Top = 8
          Margins.Right = 8
          Margins.Bottom = 8
          TabOrder = 24
          Version = '1.0'
          Align = alLeft
          JQButtonOptions.Label_ = 'Kontrollbericht'
          JQEvents.OnClick.OnEvent = ButtonKontrollberichtOnClick
        end
      end
    end
  end
  object ProviderMassnahmen: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.QMassnahmen
    KeyFields = 'MangelID'
    Left = 680
    Top = 336
  end
  object ProviderProben: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.QProben
    KeyFields = 'ID'
    Left = 688
    Top = 552
  end
  object Downloader: TIWCGJQFileDownload
    Version = '1.0'
    Left = 480
    Top = 544
  end
  object DSMassnahmen: TDataSource
    DataSet = DMKontrolle.QMassnahmen
    Left = 784
    Top = 336
  end
end
