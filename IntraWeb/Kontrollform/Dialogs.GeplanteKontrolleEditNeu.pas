﻿unit Dialogs.GeplanteKontrolleEditNeu;

interface

uses
  System.SysUtils, System.Variants, System.Classes, System.Generics.Collections,
  Winapi.Windows, Winapi.Messages,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40<PERSON>omponent,
  IWCGJQComp, IWCGJQSweet<PERSON>lert, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWCGJQDropDown,
  IWCGJQDatePicker, IWCGJQMemo, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog,
  Dialogs.Base, Modules.Kontrolle, ELKE.Classes.Generated, Dialogs.UngeplanteKontrolleBetrieb, Data.DB, IWCompCheckbox,
  IWCGJQCheckBox;

type
  // TDialogGeplanteKontrolleEditNeu = class(TDialogBase)
  TDialogGeplanteKontrolleEditNeu = class(TDialogBase<TDMKontrolle>)
    LabelKontrollberichttyp: TIWCGJQLabel;
    LabelKontrolltyp: TIWCGJQLabel;
    LabelKontrollgrund: TIWCGJQLabel;
    LabelBetrieb: TIWCGJQLabel;
    MemoBetrieb: TIWCGJQMemoEx;
    DatePickFaelligkeit: TIWCGJQDatePicker;
    ButtonBetrieb: TIWCGJQButton;
    DropKontrollgrund: TIWCGJQDropDown;
    DropKontrolltyp: TIWCGJQDropDown;
    DropBerichtstyp: TIWCGJQDropDown;
    LabelAngemeldet: TIWCGJQLabel;
    DatePickAngemeldet: TIWCGJQDatePicker;
    IWCGJQLabel1: TIWCGJQLabel;
    MemoKontrollInformationen: TIWCGJQMemoEx;
    LabelFaelligkeitsdatum: TIWCGJQLabel;
    CheckAngemeldet: TIWCGJQCheckBoxEx;
    IWCGJQLabel2: TIWCGJQLabel;
    MemoInterneNotiz: TIWCGJQMemoEx;
    procedure ButtonBetriebJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure DropBerichtstypJQDropDownOptionsChange(Sender: TObject; AParams: TStringList);
    procedure CheckAngemeldetJQCheckExOptionsClick(Sender: TObject; AParams: TStringList);
  private
    FKontrollID: integer;
    FKontrollbericht: TKontrollbericht;
    berichtstypen: TList<TBkbTyp>;
    kontrolltypen: TList<TKontrolltyp>;
    rechtsgrundlagen: TList<TRechtsgrundlage>;
    FBetriebsID: integer;
    FEditMode: TDMKontrolle.TEditMode;
    FBetrieb: TBetrieb;
  public
    constructor Create(AOwner: TComponent); overload; override;
    constructor Create(AOwner: TComponent; AIDKontrollbericht: integer); reintroduce; overload;
    destructor Destroy; override;
    procedure InitializeControls; override;
    procedure Startup;
    procedure UpdateDropdowns;
    procedure Save;
  end;

implementation

uses ServerController, System.DateUtils, Aurelius.Types.Nullable;

{$R *.dfm}


constructor TDialogGeplanteKontrolleEditNeu.Create(AOwner: TComponent; AIDKontrollbericht: integer);
var
  s: string;
  i: integer;
begin
  self := inherited Create(AOwner);
  InitializeControls;
  FKontrollID := AIDKontrollbericht;
  FKontrollbericht := DM.GetKontrollbericht(FKontrollID);
  Title := 'Kontrolle bearbeiten';
  FEditMode := TDMKontrolle.TEditMode.Edit;
  TDialogUngeplanteKontrolleBetrieb.Create(self);
  if FKontrollID > -1 then
  begin
    try
      FKontrollbericht := DM.GetKontrollbericht(FKontrollID);
      FBetriebsID := FKontrollbericht.Betrieb.Id;
      Startup;
      DropBerichtstyp.Enabled := false;
      ButtonBetrieb.Enabled := false;

      s := FKontrollbericht.Betrieb.Adresse.Plz + ', ' + FKontrollbericht.Betrieb.Adresse.Ort;
      MemoBetrieb.Lines.Add(FKontrollbericht.Betrieb.Name);
      MemoBetrieb.Lines.Add(FKontrollbericht.Betrieb.Adresse.Strasse);
      if s <> ', ' then
      begin
        MemoBetrieb.Lines.Add(s);
      end;
      if FKontrollbericht.Betrieb.Registrierung <> nil then
      begin
        MemoBetrieb.Lines.Add(FKontrollbericht.Betrieb.Registrierung.Regnr);
      end;
    except
      on E: Exception do
        Alert.Warning('Beim Bearbeiten ist ein Fehler aufgetreten' + sLineBreak + E.Message);
    end;

    for i := 0 to DropKontrolltyp.Items.Count - 1 do
    begin
      s := FKontrollbericht.Kontrolltyp.Bezeichnung;
      if DropKontrolltyp.Items[i].Caption = s then
      begin
        DropKontrolltyp.SelectedIndex := i;
        break;
      end;
    end;

    for i := 0 to DropKontrollgrund.Items.Count - 1 do
    begin
      s := FKontrollbericht.Rechtsgrundlage.Bezeichnung;
      if DropKontrollgrund.Items[i].Caption = s then
      begin
        DropKontrollgrund.SelectedIndex := i;
        break;
      end;
    end;
    if FKontrollbericht.Datum.HasValue then
    begin
      DatePickFaelligkeit.Date := FKontrollbericht.Datum.Value;
    end;

    MemoKontrollInformationen.Text := FKontrollbericht.KontrollInformationen.ValueOrDefault;
    MemoInterneNotiz.Text := FKontrollbericht.InterneNotiz.ValueOrDefault;

    if FKontrollbericht.AngemeldetUm.HasValue then
    begin
      DatePickAngemeldet.Date := FKontrollbericht.AngemeldetUm.Value;
      // CheckAngemeldet.Checked := true;
    end
    else
    begin
      CheckAngemeldet.Checked := false;
    end;
    // DatePickAngemeldet.Enabled := CheckAngemeldet.Checked;
  end;
end;

constructor TDialogGeplanteKontrolleEditNeu.Create(AOwner: TComponent);
begin
  self := inherited Create(AOwner);
  InitializeControls;
  FKontrollID := -1;
  Title := 'Kontrolle erstellen';
  FEditMode := TDMKontrolle.TEditMode.New;
  TDialogUngeplanteKontrolleBetrieb.Create(self);
  Startup;
  DropBerichtstyp.Enabled := true;
  DropKontrolltyp.Enabled := true;
  DropKontrollgrund.Enabled := true;
  CheckAngemeldet.Checked := false;
  // DatePickAngemeldet.Enabled := CheckAngemeldet.Checked;
end;

procedure TDialogGeplanteKontrolleEditNeu.ButtonBetriebJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
var
  LKontrolleBetrieb: TDialogUngeplanteKontrolleBetrieb;
  s: string;
begin
  inherited;
  LKontrolleBetrieb := TDialogUngeplanteKontrolleBetrieb.Create(self);
  LKontrolleBetrieb.Show(false, true,
    procedure
    begin
      DM.OpenBetriebDetails(LKontrolleBetrieb.Id);
      FBetrieb := Usersession.ELKERest.GetBetrieb(LKontrolleBetrieb.Id);
      MemoBetrieb.Lines.Clear;
      s := DM.QBetriebDetailPLZ.AsString + ', ' + DM.QBetriebDetailOrt.AsString;
      MemoBetrieb.Lines.Add(DM.QBetriebDetailName.AsString);
      MemoBetrieb.Lines.Add(DM.QBetriebDetailStrasse.AsString);
      if s <> ', ' then
      begin
        MemoBetrieb.Lines.Add(s);
      end;
      if DM.QBetriebDetailRegnr.AsString <> '' then
      begin
        MemoBetrieb.Lines.Add(DM.QBetriebDetailRegnr.AsString);
      end;
      MemoBetrieb.AjaxReRender;
      FBetriebsID := LKontrolleBetrieb.Id;
    end);
end;

procedure TDialogGeplanteKontrolleEditNeu.InitializeControls;
begin
  ButtonBetrieb.Enabled := true;
  MemoBetrieb.Lines.Clear;
  DropKontrollgrund.Items.Clear;
  DropKontrolltyp.Items.Clear;
  DropBerichtstyp.Items.Clear;
  DropBerichtstyp.Enabled := true;
  ButtonBetrieb.Enabled := true;
  DatePickFaelligkeit.Date := Today;
  CheckAngemeldet.Checked := false;
  DatePickAngemeldet.Date := Today;
  inherited;
end;

destructor TDialogGeplanteKontrolleEditNeu.Destroy;
begin
  berichtstypen.Free;
  kontrolltypen.Free;
  rechtsgrundlagen.Free;
  inherited;
end;

procedure TDialogGeplanteKontrolleEditNeu.CheckAngemeldetJQCheckExOptionsClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  // DatePickAngemeldet.Enabled := CheckAngemeldet.Checked;
end;

procedure TDialogGeplanteKontrolleEditNeu.DropBerichtstypJQDropDownOptionsChange(Sender: TObject; AParams: TStringList);
begin
  inherited;
  UpdateDropdowns;
end;

procedure TDialogGeplanteKontrolleEditNeu.Startup;
var
  LItem: TIWCGJQDropDownItem;
  i: integer;
begin
  berichtstypen := Nil;
  kontrolltypen := Nil;
  rechtsgrundlagen := Nil;
  berichtstypen := Usersession.ELKERest.berichtstypen;

  if FKontrollID > -1 then
  begin
    LItem := DropBerichtstyp.Items.Add;
    LItem.Caption := FKontrollbericht.Kontrolltyp.Bkbtyp.Bezeichnung;
    LItem.Value := FKontrollbericht.Kontrolltyp.Bkbtyp.Typ;
  end
  else
  begin
    for i := 0 to berichtstypen.Count - 1 do
    begin
      if berichtstypen[i].Probe = false then
      begin
        LItem := DropBerichtstyp.Items.Add;
        LItem.Caption := berichtstypen[i].Bezeichnung;
        LItem.Value := berichtstypen[i].Typ;
      end;
    end;
  end;
  DropBerichtstyp.AjaxReRender;
  UpdateDropdowns;
end;

procedure TDialogGeplanteKontrolleEditNeu.UpdateDropdowns;
var
  LItem: TIWCGJQDropDownItem;
  i: integer;
  Value: string;
begin
  DropKontrolltyp.Items.Clear;
  DropKontrolltyp.SelectedIndex := -1;
  if Assigned(kontrolltypen) then
  begin
    FreeAndNil(kontrolltypen);
  end;
  Value := DropBerichtstyp.val;
  kontrolltypen := Usersession.ELKERest.kontrolltypen(Value);
  for i := 0 to kontrolltypen.Count - 1 do
  begin
    if Usersession.ELKERest.kontrolltypen(Value)[i].Bezeichnung.HasValue then
    begin
      LItem := DropKontrolltyp.Items.Add;
      LItem.Caption := kontrolltypen[i].Bezeichnung;
      LItem.Value := i.ToString;
    end;
  end;
  if Assigned(rechtsgrundlagen) then
  begin
    FreeAndNil(rechtsgrundlagen);
  end;
  rechtsgrundlagen := Usersession.ELKERest.rechtsgrundlagen(Value);
  DropKontrollgrund.Items.Clear;
  DropKontrollgrund.SelectedIndex := -1;
  for i := 0 to rechtsgrundlagen.Count - 1 do
  begin
    LItem := DropKontrollgrund.Items.Add;
    LItem.Caption := rechtsgrundlagen[i].Bezeichnung;
    LItem.Value := i.ToString;
  end;
  DropKontrolltyp.AjaxReRender;
  DropKontrollgrund.AjaxReRender;
end;

procedure TDialogGeplanteKontrolleEditNeu.Save;
begin
  if FEditMode = TDMKontrolle.TEditMode.New then
  begin
    FKontrollbericht := TKontrollbericht.Create;
  end;
  if CheckAngemeldet.Checked then
  begin
    FKontrollbericht.AngemeldetUm := DatePickAngemeldet.Date;
  end
  else
  begin
    FKontrollbericht.AngemeldetUm := snull;
  end;
  FKontrollbericht.Datum := DatePickFaelligkeit.Date;
  FKontrollbericht.Rechtsgrundlage := rechtsgrundlagen[DropKontrollgrund.val.ToInteger];
  FKontrollbericht.Kontrolltyp := kontrolltypen[DropKontrolltyp.val.ToInteger];

  FKontrollbericht.KontrollInformationen := MemoKontrollInformationen.Text.Trim;
  FKontrollbericht.InterneNotiz := MemoInterneNotiz.Text.Trim;

  if FEditMode = TDMKontrolle.TEditMode.Edit then
  begin
    Usersession.ELKERest.KontrollberichtBearbeiten(FKontrollbericht);
  end
  else
  begin
    FKontrollbericht.Betrieb := FBetrieb;
    Usersession.ELKERest.KontrollberichtErstellen(FKontrollbericht);
  end;

end;

end.
