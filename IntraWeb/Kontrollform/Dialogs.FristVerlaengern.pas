﻿unit Dialogs.FristVerlaengern;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQDatePicker, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel, IWCompMemo, IWCGJQMemo, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert;

type
  TDialogFristVerlaengern = class(TDialogBase)
    IWLabel1: TIWCGJQLabel;
    EditFrist: TIWCGJQDatePicker;
    IWCGJQLabel1: TIWCGJQLabel;
    MemoBemerkung: TIWCGJQMemoEx;
  private
    { Private declarations }
  public
    Frist: TDateTime;
    Bemerkung: string;
    constructor Create(AOwner: TComponent; AFrist: TDate); reintroduce;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}


constructor TDialogFristVerlaengern.Create(AOwner: TComponent; AFrist: TDate);
begin
  self := inherited Create(AOwner);
  EditFrist.Date := AFrist;
  IWFrameRegion.JQDialogOptions.Title := 'Frist verlängern';
end;

procedure TDialogFristVerlaengern.DoButtonOK(ASender: TObject; AParams: TStringList);
begin
  if MemoBemerkung.Lines.Text.Trim.Length < 3 then
  begin
    Alert.Error('Es muss eine Bemerkung/Begründung für die Fristverlängerung angegeben werden!');
  end
  else
  begin
    Frist := EditFrist.Date;
    Bemerkung := MemoBemerkung.Lines.Text;
    inherited;
    Close;
  end;
end;

procedure TDialogFristVerlaengern.InitializeControls;
begin
  inherited;
  EditFrist.Date := now;
  MemoBemerkung.Lines.Clear;
end;

end.
