object VisOExCSVImport: TVisOExCSVImport
  Left = 0
  Top = 0
  Width = 852
  Height = 572
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 852
    Height = 572
    RenderInvisibleControls = True
    Version = '1.0'
    Align = alClient
    BorderOptions.NumericWidth = 1
    BorderOptions.Style = cbsSolid
    object FileBkbImport: TIWCGJQFileUpload
      Left = 63
      Top = 64
      Width = 322
      Height = 57
      TabOrder = 1
      Font.Size = 12
      Version = '1.0'
      JQFileUploadOptions.Multiple = False
      JQFileUploadOptions.AllowedExtensions.Strings = (
        'csv'
        'txt')
      JQFileUploadOptions.OnComplete.OnEvent = FileBkbImportOnUploadComplete
      JQFileUploadOptions.OnComplete.SendAllArguments = True
      JQFileUploadOptions.Classes.Button = 'ui-button'
      JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
      JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
      JQFileUploadOptions.Classes.Fail = 'ui-state-error'
      JQFileUploadOptions.OnUpload.SendAllArguments = True
      JQFileUploadOptions.Name = 'fileBkb'
      JQFileUploadOptions.Tip = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.CancelCaption = 'Abbrechen'
      JQFileUploadOptions.FailedCaption = 'Fehlgeschlagen'
      JQFileUploadOptions.CanOverrideFile = True
      JQFileUploadOptions.DragText = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.UploadButtonText = 'BKB-CSV hochladen'
      JQFileUploadOptions.CancelButtonText = 'Abbrechen'
      JQFileUploadOptions.FailUploadText = 'Fehlgeschlagen'
      JQFileUploadOptions.MultipleFileDropNotAllowedMessage = 'Sie d'#252'rfen nur ein File hochladen'
      JQFileUploadOptions.ShowUploadedFileList = False
      CanOverrideFile = True
      OnGetFileName = UploadGetFileName
    end
    object FileKKImport: TIWCGJQFileUpload
      Left = 63
      Top = 144
      Width = 322
      Height = 57
      TabOrder = 2
      Font.Size = 12
      Version = '1.0'
      JQFileUploadOptions.Multiple = False
      JQFileUploadOptions.AllowedExtensions.Strings = (
        'csv'
        'txt')
      JQFileUploadOptions.OnComplete.OnEvent = FileKKImportJQFileUploadOptionsComplete
      JQFileUploadOptions.OnComplete.SendAllArguments = True
      JQFileUploadOptions.Classes.Button = 'ui-button'
      JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
      JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
      JQFileUploadOptions.Classes.Fail = 'ui-state-error'
      JQFileUploadOptions.OnUpload.SendAllArguments = True
      JQFileUploadOptions.Name = 'fileKK'
      JQFileUploadOptions.Tip = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.CancelCaption = 'Abbrechen'
      JQFileUploadOptions.FailedCaption = 'Fehlgeschlagen'
      JQFileUploadOptions.CanOverrideFile = True
      JQFileUploadOptions.DragText = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.UploadButtonText = 'KK-CSV hochladen'
      JQFileUploadOptions.CancelButtonText = 'Abbrechen'
      JQFileUploadOptions.FailUploadText = 'Fehlgeschlagen'
      JQFileUploadOptions.MultipleFileDropNotAllowedMessage = 'Sie d'#252'rfen nur ein File hochladen'
      JQFileUploadOptions.ShowUploadedFileList = False
      CanOverrideFile = True
    end
  end
  object quExistiertBetrieb: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Betriebe'
      'WHERE  REGNR = :lfbis;')
    Left = 40
    Top = 504
    ParamData = <
      item
        Name = 'LFBIS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quBetriebErstellen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Betriebe (REGNR, Name, Telefon, ID_Adress' +
        'e, GEMNR, Bldcode)'
      
        'VALUES      (:lfbis, :name, :telefon, :id_adresse, :gemnr, :bldc' +
        'ode);')
    Left = 136
    Top = 504
    ParamData = <
      item
        Name = 'LFBIS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TELEFON'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ADRESSE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GEMNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeueAdresse: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Adressen (Ort, Plz, Strasse, Bldcode, ID_' +
        'Gemeinde)'
      'VALUES      (:Ort, :Plz, :Strasse, :Bldcode, :id_gemeinde);')
    Left = 224
    Top = 504
    ParamData = <
      item
        Name = 'ORT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STRASSE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_GEMEINDE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFindeGemeinde: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Gemeinden'
      'WHERE  Gemeindekennziffer = :gemeindekennziffer;')
    Left = 312
    Top = 504
    ParamData = <
      item
        Name = 'GEMEINDEKENNZIFFER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 784
    Top = 16
  end
  object QBKBInsert: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select *'
      'from Bewegungsdaten.BKBNummern'
      'where nummer = :BKB')
    Left = 416
    Top = 272
    ParamData = <
      item
        Name = 'BKB'
        DataType = ftString
        ParamType = ptInput
        Value = 'abc'
      end>
    object QBKBInsertNUMMER: TStringField
      FieldName = 'NUMMER'
      Origin = 'NUMMER'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 26
    end
    object QBKBInsertSYSTEMKZ: TStringField
      FieldName = 'SYSTEMKZ'
      Origin = 'SYSTEMKZ'
      Required = True
      Size = 3
    end
    object QBKBInsertBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Required = True
      Size = 10
    end
    object QBKBInsertJAHR: TBCDField
      FieldName = 'JAHR'
      Origin = 'JAHR'
      Required = True
      Precision = 4
      Size = 0
    end
    object QBKBInsertBUNDESLAND: TSmallintField
      FieldName = 'BUNDESLAND'
      Origin = 'BUNDESLAND'
      Required = True
    end
    object QBKBInsertLFD_NR: TIntegerField
      FieldName = 'LFD_NR'
      Origin = 'LFD_NR'
      Required = True
    end
    object QBKBInsertLFD_NR_HEX: TStringField
      FieldName = 'LFD_NR_HEX'
      Origin = 'LFD_NR_HEX'
      Size = 5
    end
  end
  object QGruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * from SYSTEMSTAMMDATEN.GRUPPEN'
      'where okz = :okz')
    Left = 432
    Top = 464
    ParamData = <
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = '404'
      end>
    object QGruppeID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGruppeBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QGruppeBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QGruppeMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QGruppeID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QGruppeID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QGruppeOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QGruppePERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
    object QGruppeEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 250
    end
  end
end
