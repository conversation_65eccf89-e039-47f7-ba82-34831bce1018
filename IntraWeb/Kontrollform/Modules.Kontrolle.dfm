object DMKontrolle: TDMKontrolle
  OldCreateOrder = False
  Height = 449
  Width = 870
  object QProben: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT p.ID, p.GUI<PERSON>, p.<PERSON>, p<PERSON><PERSON>, p<PERSON>, p.<PERSON>' +
        'tum, p.<PERSON>, p.Ages_Probenstatus, p.G<PERSON>D_Doku<PERSON>, e.Text AS' +
        ' "Status", p.Grenz<PERSON>tueberschreitung'
      'FROM   Bewegungsdaten.KB_Proben p'
      
        '        LEFT OUTER JOIN Systemstammdaten.ESADomwerte e ON p.Stat' +
        'us = e.Code AND e.DOM = '#39'vKONTROLLBERICHT_STATUS.STATUS'#39
      'WHERE  ID_Kontrollbericht = :idkontrollbericht;')
    Left = 40
    Top = 16
    ParamData = <
      item
        Name = 'IDKONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QProbenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QProbenGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object QProbenProbenbkb: TStringField
      FieldName = 'Probenbkb'
      Origin = 'Probenbkb'
      Size = 26
    end
    object QProbenProbenart: TStringField
      FieldName = 'Probenart'
      Origin = 'Probenart'
      Size = 50
    end
    object QProbenBemerkung: TStringField
      FieldName = 'Bemerkung'
      Origin = 'Bemerkung'
      Size = 250
    end
    object QProbenDatum: TDateField
      FieldName = 'Datum'
      Origin = 'Datum'
    end
    object QProbenVerdacht: TStringField
      FieldName = 'Verdacht'
      Origin = 'Verdacht'
      Size = 250
    end
    object QProbenAges_Probenstatus: TStringField
      FieldName = 'Ages_Probenstatus'
      Origin = 'Ages_Probenstatus'
      Size = 50
    end
    object QProbenGUID_Dokument: TGuidField
      FieldName = 'GUID_Dokument'
      Origin = 'GUID_Dokument'
      Size = 38
    end
    object QProbenStatus: TStringField
      FieldName = 'Status'
      Origin = 'Status'
      Size = 255
    end
    object QProbenGrenzwertueberschreitung: TBooleanField
      FieldName = 'Grenzwertueberschreitung'
      Origin = 'Grenzwertueberschreitung'
    end
  end
  object QMassnahmen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT DISTINCT m.ID AS "MangelID", ma.ID, ma.Langtext AS "Massn' +
        'ahme", m.Frist, m.Text AS "Beschreibung", m.Beseitigt_am, '
      
        '       mt.ID AS "MangeltypID", ma.ID AS "MassnahmeID", mt.Bezeic' +
        'hnung AS "Mangel", m.Behebungsauftrag,'
      '       ms.Beschreibung as "Status", Count(b.Bild) as "Bilder"'
      'FROM   Bewegungsdaten.Bewertete_Fragen bf'
      
        '         INNER JOIN Bewegungsdaten.Maengel m  ON bf.ID_Mangel   ' +
        '= m.ID'
      
        '         INNER JOIN Stammdaten.Mangeltypen mt ON m.ID_Mangeltyp ' +
        '= mt.ID'
      
        '         INNER JOIN Stammdaten.Massnahmen  ma ON m.ID_Massnahme ' +
        '= ma.ID'
      
        '         LEFT OUTER JOIN Bewegungsdaten.KONTROLLBERICHT_BILDER b' +
        ' ON b.ID_MANGEL = m.ID'
      
        '         LEFT OUTER JOIN Stammdaten.Mangel_Status ms ON m.Status' +
        ' = ms.Status'
      'WHERE  bf.ID_Bericht = :IDKontrollbericht'
      
        'GROUP BY m.ID, ma.ID, ma.Langtext, m.Frist, m.Text, m.Beseitigt_' +
        'am, mt.ID, ma.ID, mt.Bezeichnung, m.Behebungsauftrag, ms.Beschre' +
        'ibung;')
    Left = 104
    Top = 16
    ParamData = <
      item
        Name = 'IDKONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = 837
      end>
    object QMassnahmenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
    end
    object QMassnahmenMassnahme: TMemoField
      FieldName = 'Massnahme'
      Origin = 'Massnahme'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object QMassnahmenFrist: TDateField
      FieldName = 'Frist'
      Origin = 'Frist'
    end
    object QMassnahmenBeschreibung: TMemoField
      FieldName = 'Beschreibung'
      Origin = 'Beschreibung'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object QMassnahmenBeseitigt_am: TDateField
      FieldName = 'Beseitigt_am'
      Origin = 'Beseitigt_am'
    end
    object QMassnahmenMangel: TStringField
      FieldName = 'Mangel'
      Origin = 'Mangel'
      Size = 200
    end
    object QMassnahmenMangelID: TFDAutoIncField
      FieldName = 'MangelID'
      Origin = 'MangelID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QMassnahmenMangeltypID: TFDAutoIncField
      FieldName = 'MangeltypID'
      Origin = 'MangeltypID'
      ReadOnly = True
    end
    object QMassnahmenMassnahmeID: TFDAutoIncField
      FieldName = 'MassnahmeID'
      Origin = 'MassnahmeID'
      ReadOnly = True
    end
    object QMassnahmenBehebungsauftrag: TStringField
      FieldName = 'Behebungsauftrag'
      Origin = 'Behebungsauftrag'
      Size = 4000
    end
    object QMassnahmenStatus: TStringField
      FieldName = 'Status'
      Origin = 'Status'
      Size = 200
    end
    object QMassnahmenBilder: TIntegerField
      FieldName = 'Bilder'
      Origin = 'Bilder'
      ReadOnly = True
    end
  end
  object QKontrollDetails: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select kb.ID,'
      '       kb.GUID,'
      '       kb.BKB,'
      '       kb.BKBTYP,'
      '       kb.KONTROLLTYP,'
      '       kb.DATUM,'
      '       kb.ID_PERSON_ERFASSER,'
      '       kb.ID_PERSON_KONTROLLORGAN,'
      '       kb.REF_BKB,'
      '       kb.PROBENZIEHUNG,'
      '       kb.ID_BETRIEB,'
      '       kb.KURZBEMERKUNG,'
      '       kb.STARTZEIT,'
      '       kb.ENDEZEIT,'
      '       kb.BESTAETIGT_UM,'
      '       kb.ANGEMELDET_UM,'
      '       kb.BETRIEBSTYP,'
      '       kb.VERWEIGERUNGSGRUND_UNTERSCHRIFT,'
      '       kb.GUID_DOKUMENT,'
      '       kb.FEHLERHAFT_GESETZT_AM,'
      '       kb.STORNIERT_AM,'
      '       kb.STORNOGRUND,'
      '       kb.VERWEIGERT_AM,'
      '       kb.Verweigerunggrund,'
      '       kb.KONTROLL_INFORMATIONEN,'
      '       kb.INTERNE_NOTIZ,'
      '       g.BEZEICHNUNG QuellGruppe,'
      '       bkb.BEZEICHNUNG                as BkbBezeichnung,'
      '       kt.BEZEICHNUNG                 as KontrolltypBezeichnung,'
      
        '       rg.BEZEICHNUNG                 as RechtsgrundlageBezeichn' +
        'ung,'
      
        '       rg.KURZBEZEICHNUNG             as RechtsgrundlageKurzbeze' +
        'ichnung,'
      '       ed.TEXT                        as Status,'
      '       pk.VORNAME + '#39' '#39' + pk.NACHNAME as KontrollorganName,'
      '       pk.TITEL                       as KontrollorganTitel,'
      '       pe.VORNAME + '#39' '#39' + pe.NACHNAME as ErfasserName,'
      '       pe.TITEL                       as ErfasserTitel,'
      '       kb.GUID_DOKUMENT'
      'from BEWEGUNGSDATEN.KONTROLLBERICHT kb'
      
        '         inner join STAMMDATEN.BKBTYPEN bkb on kb.BKBTYP = bkb.B' +
        'KBTYP'
      
        '         inner join STAMMDATEN.KONTROLLTYPEN kt on kb.BKBTYP = k' +
        't.BKBTYP and kb.KONTROLLTYP = kt.KONTROLLTYP'
      
        '         left outer join STAMMDATEN.RECHTSGRUNDLAGEN rg on kb.ID' +
        '_RECHTSGRUNDLAGE = rg.ID'
      
        '         left outer join STAMMDATEN.PERSONEN pk on kb.ID_PERSON_' +
        'KONTROLLORGAN = pk.ID'
      
        '         left outer join STAMMDATEN.PERSONEN pe on kb.ID_PERSON_' +
        'ERFASSER = pe.ID'
      
        '         left outer join SYSTEMSTAMMDATEN.GRUPPEN g on kb.ID_GRU' +
        'PPE_QUELLE = g.ID'
      
        '         left outer join BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS ' +
        'ks on ks.ID = kb.ID'
      '         left outer join SYSTEMSTAMMDATEN.ESADOMWERTE ed'
      
        '                         on ks.STATUS = ed.CODE and ed.DOM = '#39'vK' +
        'ONTROLLBERICHT_STATUS.STATUS'#39
      'where kb.ID = :IDKontrollbericht')
    Left = 184
    Top = 16
    ParamData = <
      item
        Name = 'IDKONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QKontrollDetailsID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QKontrollDetailsGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object QKontrollDetailsBkb: TStringField
      FieldName = 'Bkb'
      Origin = 'Bkb'
      Required = True
      Size = 26
    end
    object QKontrollDetailsBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Origin = 'Bkbtyp'
      Required = True
      Size = 10
    end
    object QKontrollDetailsKontrolltyp: TStringField
      FieldName = 'Kontrolltyp'
      Origin = 'Kontrolltyp'
      FixedChar = True
      Size = 3
    end
    object QKontrollDetailsDatum: TDateField
      FieldName = 'Datum'
      Origin = 'Datum'
    end
    object QKontrollDetailsID_Person_Erfasser: TIntegerField
      FieldName = 'ID_Person_Erfasser'
      Origin = 'ID_Person_Erfasser'
    end
    object QKontrollDetailsID_Person_Kontrollorgan: TIntegerField
      FieldName = 'ID_Person_Kontrollorgan'
      Origin = 'ID_Person_Kontrollorgan'
    end
    object QKontrollDetailsRef_bkb: TStringField
      FieldName = 'Ref_bkb'
      Origin = 'Ref_bkb'
      Size = 26
    end
    object QKontrollDetailsProbenziehung: TBooleanField
      FieldName = 'Probenziehung'
      Origin = 'Probenziehung'
      Required = True
    end
    object QKontrollDetailsID_Betrieb: TIntegerField
      FieldName = 'ID_Betrieb'
      Origin = 'ID_Betrieb'
      Required = True
    end
    object QKontrollDetailsStartzeit: TSQLTimeStampField
      FieldName = 'Startzeit'
      Origin = 'Startzeit'
    end
    object QKontrollDetailsEndezeit: TSQLTimeStampField
      FieldName = 'Endezeit'
      Origin = 'Endezeit'
    end
    object QKontrollDetailsbestaetigt_um: TSQLTimeStampField
      FieldName = 'bestaetigt_um'
      Origin = 'bestaetigt_um'
    end
    object QKontrollDetailsangemeldet_um: TSQLTimeStampField
      FieldName = 'angemeldet_um'
      Origin = 'angemeldet_um'
    end
    object QKontrollDetailsBetriebstyp: TStringField
      FieldName = 'Betriebstyp'
      Origin = 'Betriebstyp'
      Required = True
      Size = 2
    end
    object QKontrollDetailsVerweigerungsgrund_unterschrift: TStringField
      FieldName = 'Verweigerungsgrund_unterschrift'
      Origin = 'Verweigerungsgrund_unterschrift'
      Size = 255
    end
    object QKontrollDetailsGuid_Dokument: TGuidField
      FieldName = 'Guid_Dokument'
      Origin = 'Guid_Dokument'
      Size = 38
    end
    object QKontrollDetailsfehlerhaft_gesetzt_am: TSQLTimeStampField
      FieldName = 'fehlerhaft_gesetzt_am'
      Origin = 'fehlerhaft_gesetzt_am'
    end
    object QKontrollDetailsstorniert_am: TSQLTimeStampField
      FieldName = 'storniert_am'
      Origin = 'storniert_am'
    end
    object QKontrollDetailsstornogrund: TStringField
      FieldName = 'stornogrund'
      Origin = 'stornogrund'
      Size = 255
    end
    object QKontrollDetailsverweigert_am: TSQLTimeStampField
      FieldName = 'verweigert_am'
      Origin = 'verweigert_am'
    end
    object QKontrollDetailsVerweigerunggrund: TStringField
      FieldName = 'Verweigerunggrund'
      Origin = 'Verweigerunggrund'
      Size = 150
    end
    object QKontrollDetailsBkbBezeichnung: TStringField
      FieldName = 'BkbBezeichnung'
      Origin = 'BkbBezeichnung'
      Required = True
      Size = 50
    end
    object QKontrollDetailsKontrolltypBezeichnung: TStringField
      FieldName = 'KontrolltypBezeichnung'
      Origin = 'KontrolltypBezeichnung'
      Size = 80
    end
    object QKontrollDetailsRechtsgrundlageBezeichnung: TStringField
      FieldName = 'RechtsgrundlageBezeichnung'
      Origin = 'RechtsgrundlageBezeichnung'
      Size = 300
    end
    object QKontrollDetailsRechtsgrundlageKurzbezeichnung: TStringField
      FieldName = 'RechtsgrundlageKurzbezeichnung'
      Origin = 'RechtsgrundlageKurzbezeichnung'
      Size = 50
    end
    object QKontrollDetailsStatus: TStringField
      FieldName = 'Status'
      Origin = 'Status'
    end
    object QKontrollDetailsKontrollorganName: TStringField
      FieldName = 'KontrollorganName'
      Origin = 'KontrollorganName'
      ReadOnly = True
      Size = 142
    end
    object QKontrollDetailsErfasserName: TStringField
      FieldName = 'ErfasserName'
      Origin = 'ErfasserName'
      ReadOnly = True
      Size = 142
    end
    object QKontrollDetailsKontrollorganTitel: TStringField
      FieldName = 'KontrollorganTitel'
      Origin = 'KontrollorganTitel'
    end
    object QKontrollDetailsErfasserTitel: TStringField
      FieldName = 'ErfasserTitel'
      Origin = 'ErfasserTitel'
    end
    object QKontrollDetailsKONTROLL_INFORMATIONEN: TStringField
      FieldName = 'KONTROLL_INFORMATIONEN'
      Origin = 'KONTROLL_INFORMATIONEN'
      Size = 4000
    end
    object QKontrollDetailsKURZBEMERKUNG: TWideMemoField
      FieldName = 'KURZBEMERKUNG'
      Origin = 'KURZBEMERKUNG'
      BlobType = ftWideMemo
      Size = **********
    end
    object QKontrollDetailsINTERNE_NOTIZ: TWideMemoField
      FieldName = 'INTERNE_NOTIZ'
      Origin = 'INTERNE_NOTIZ'
      BlobType = ftWideMemo
      Size = **********
    end
    object QKontrollDetailsGUID_DOKUMENT_1: TGuidField
      FieldName = 'GUID_DOKUMENT_1'
      Origin = 'GUID_DOKUMENT'
      Size = 38
    end
    object QKontrollDetailsQuellGruppe: TStringField
      FieldName = 'QuellGruppe'
      Origin = 'QuellGruppe'
      Size = 100
    end
  end
  object QESADomWerte: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Code, Text'
      'FROM   Systemstammdaten.ESADomWerte'
      'WHERE  Dom = '#39'vKONTROLLBERICHT_STATUS.STATUS'#39';')
    Left = 264
    Top = 16
    object QESADomWerteCode: TStringField
      FieldName = 'Code'
      Origin = 'Code'
    end
    object QESADomWerteText: TStringField
      FieldName = 'Text'
      Origin = 'Text'
      Size = 255
    end
  end
  object quProbenPdf: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Probenbkb, GUID_DOKUMENT_AGES_ERGEBNIS'
      'FROM   Bewegungsdaten.KB_Proben'
      'WHERE  ID = :id;')
    Left = 342
    Top = 16
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object quProbenPdfProbenbkb: TStringField
      FieldName = 'Probenbkb'
      Origin = 'Probenbkb'
      Size = 26
    end
    object quProbenPdfGUID_DOKUMENT_AGES_ERGEBNIS: TGuidField
      FieldName = 'GUID_DOKUMENT_AGES_ERGEBNIS'
      Origin = 'GUID_DOKUMENT_AGES_ERGEBNIS'
      Size = 38
    end
  end
  object QBetriebZulassung: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Zulassungen'
      'WHERE  Regnr = :regnr;')
    Left = 424
    Top = 16
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
    object QBetriebZulassungID: TIntegerField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QBetriebZulassungREGNR: TStringField
      FieldName = 'REGNR'
      Origin = 'REGNR'
      Required = True
      FixedChar = True
      Size = 7
    end
    object QBetriebZulassungZULNR: TStringField
      FieldName = 'ZULNR'
      Origin = 'ZULNR'
      Required = True
      Size = 9
    end
    object QBetriebZulassungBEGINNDATUM: TDateField
      FieldName = 'BEGINNDATUM'
      Origin = 'BEGINNDATUM'
    end
    object QBetriebZulassungENDDATUM: TDateField
      FieldName = 'ENDDATUM'
      Origin = 'ENDDATUM'
    end
    object QBetriebZulassungAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object QBetriebZulassungSICHTBAR: TSmallintField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
    end
  end
  object QBetriebDetail: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT b.Regnr, b.Vulgo, b.Katastralgemname, b.telefon, b.email,' +
        ' b.Aufsichtsorgan, b.Anmerkung, a.Strasse, a.Ort, a.PLZ, b.Name'
      'FROM   Stammdaten.Betriebe b '
      
        '        LEFT OUTER JOIN Stammdaten.Adressen a ON b.ID_Adresse = ' +
        'a.ID'
      'WHERE  b.ID = :IDBetrieb;')
    Left = 512
    Top = 16
    ParamData = <
      item
        Name = 'IDBETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QBetriebDetailRegnr: TStringField
      FieldName = 'Regnr'
      Origin = 'Regnr'
      FixedChar = True
      Size = 7
    end
    object QBetriebDetailVulgo: TStringField
      FieldName = 'Vulgo'
      Origin = 'Vulgo'
      Size = 30
    end
    object QBetriebDetailKatastralgemname: TStringField
      FieldName = 'Katastralgemname'
      Origin = 'Katastralgemname'
      Size = 50
    end
    object QBetriebDetailtelefon: TStringField
      FieldName = 'telefon'
      Origin = 'telefon'
      Size = 50
    end
    object QBetriebDetailemail: TStringField
      FieldName = 'email'
      Origin = 'email'
      Size = 50
    end
    object QBetriebDetailAufsichtsorgan: TStringField
      FieldName = 'Aufsichtsorgan'
      Origin = 'Aufsichtsorgan'
      FixedChar = True
      Size = 2
    end
    object QBetriebDetailAnmerkung: TWideStringField
      FieldName = 'Anmerkung'
      Origin = 'Anmerkung'
      Size = 500
    end
    object QBetriebDetailStrasse: TWideStringField
      FieldName = 'Strasse'
      Origin = 'Strasse'
      Size = 150
    end
    object QBetriebDetailOrt: TWideStringField
      FieldName = 'Ort'
      Origin = 'Ort'
      Size = 150
    end
    object QBetriebDetailPLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Size = 7
    end
    object QBetriebDetailName: TStringField
      FieldName = 'Name'
      Origin = 'Name'
      Required = True
      Size = 255
    end
  end
  object QAnwesende: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT a.*, ap.Bezeichnung'
      'FROM   Bewegungsdaten.Anwesende a'
      '     LEFT JOIN Stammdaten.[Aptyp] ap ON a.ID_APTYP = ap.ID'
      'WHERE  ID_Kontrollbericht = :IDKontrollbericht;')
    Left = 592
    Top = 16
    ParamData = <
      item
        Name = 'IDKONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QAnwesendeGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QAnwesendeID_KONTROLLBERICHT: TIntegerField
      FieldName = 'ID_KONTROLLBERICHT'
      Origin = 'ID_KONTROLLBERICHT'
      Required = True
    end
    object QAnwesendeNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Required = True
      Size = 255
    end
    object QAnwesendeEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 255
    end
    object QAnwesendeKOMMUNIKATIONSBERECHTIGT: TBooleanField
      FieldName = 'KOMMUNIKATIONSBERECHTIGT'
      Origin = 'KOMMUNIKATIONSBERECHTIGT'
      Required = True
    end
    object QAnwesendeID_APTYP: TGuidField
      FieldName = 'ID_APTYP'
      Origin = 'ID_APTYP'
      Required = True
      Size = 38
    end
    object QAnwesendeBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Size = 255
    end
  end
  object QDokument: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Dateiname, Dokument'
      'FROM   Bewegungsdaten.Dokumente'
      'WHERE  GUID = :guid;')
    Left = 664
    Top = 16
    ParamData = <
      item
        Name = 'GUID'
        DataType = ftGuid
        ParamType = ptInput
        Value = Null
      end>
    object QDokumentDateiname: TWideStringField
      FieldName = 'Dateiname'
      Origin = 'Dateiname'
      Required = True
      Size = 250
    end
    object QDokumentDokument: TBlobField
      FieldName = 'Dokument'
      Origin = 'Dokument'
      Required = True
      Size = **********
    end
  end
  object MTKontrollverlauf: TFDMemTable
    FieldDefs = <>
    IndexDefs = <>
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvSilentMode]
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    StoreDefs = True
    Left = 40
    Top = 88
    object MTKontrollverlaufID: TIntegerField
      FieldName = 'ID'
    end
    object MTKontrollverlaufBkb: TStringField
      FieldName = 'Bkb'
      Size = 26
    end
    object MTKontrollverlaufDatum: TDateField
      FieldName = 'Datum'
    end
    object MTKontrollverlaufRefBkb: TStringField
      FieldName = 'RefBkb'
      Size = 26
    end
    object MTKontrollverlaufProbenziehung: TBooleanField
      FieldName = 'Probenziehung'
    end
    object MTKontrollverlaufRegnrOrt: TStringField
      FieldName = 'RegnrOrt'
      Size = 255
    end
    object MTKontrollverlaufBestaetigtUm: TDateTimeField
      FieldName = 'BestaetigtUm'
    end
    object MTKontrollverlaufverweigerunggrund: TStringField
      FieldName = 'verweigerunggrund'
      Size = 255
    end
    object MTKontrollverlaufAngemeldetUm: TDateTimeField
      FieldName = 'AngemeldetUm'
    end
    object MTKontrollverlaufBetriebName: TStringField
      FieldName = 'BetriebName'
      Size = 255
    end
    object MTKontrollverlaufRechtsgrundlageBez: TStringField
      FieldName = 'RechtsgrundlageBez'
      Size = 255
    end
    object MTKontrollverlaufErfasserName: TStringField
      FieldName = 'ErfasserName'
      Size = 255
    end
    object MTKontrollverlaufKontrollorganName: TStringField
      FieldName = 'KontrollorganName'
      Size = 255
    end
    object MTKontrollverlaufKontrolltypTyp: TStringField
      FieldName = 'KontrolltypTyp'
      Size = 255
    end
    object MTKontrollverlaufBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Size = 255
    end
    object MTKontrollverlaufStatus: TStringField
      FieldName = 'Status'
      Size = 255
    end
    object MTKontrollverlaufOrt: TStringField
      FieldName = 'Ort'
      Size = 255
    end
    object MTKontrollverlaufFaelligkeitsdatum: TDateField
      FieldName = 'Faelligkeitsdatum'
    end
    object MTKontrollverlaufGuid: TGuidField
      FieldName = 'Guid'
      Size = 38
    end
    object MTKontrollverlaufStartzeit: TDateTimeField
      FieldName = 'Startzeit'
    end
    object MTKontrollverlaufEndezeit: TDateTimeField
      FieldName = 'Endezeit'
    end
    object MTKontrollverlaufLFBIS: TStringField
      FieldName = 'LFBIS'
      Size = 255
    end
    object MTKontrollverlaufBetriebstyp: TStringField
      FieldName = 'Betriebstyp'
      Size = 10
    end
    object MTKontrollverlaufKontrollInformationen: TStringField
      FieldName = 'KontrollInformationen'
      Size = 4000
    end
    object MTKontrollverlaufKurzbemerkung: TWideMemoField
      FieldName = 'Kurzbemerkung'
      BlobType = ftWideMemo
    end
    object MTKontrollverlaufGruppeQuelle: TStringField
      FieldName = 'GruppeQuelle'
      Size = 255
    end
    object MTKontrollverlaufGemeindeNummer: TIntegerField
      FieldName = 'GemeindeNummer'
    end
    object MTKontrollverlaufPLZ: TStringField
      FieldName = 'PLZ'
      Size = 8
    end
    object MTKontrollverlaufStrasse: TStringField
      FieldName = 'Strasse'
      Size = 100
    end
  end
  object MTGeplanteKontrollen: TFDMemTable
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvSilentMode]
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    Left = 40
    Top = 136
    object MTGeplanteKontrollenID: TIntegerField
      FieldName = 'ID'
    end
    object MTGeplanteKontrollenBkb: TStringField
      FieldName = 'Bkb'
      Size = 26
    end
    object MTGeplanteKontrollenDatum: TDateField
      FieldName = 'Datum'
    end
    object MTGeplanteKontrollenRefBkb: TStringField
      FieldName = 'RefBkb'
      Size = 26
    end
    object MTGeplanteKontrollenProbenziehung: TBooleanField
      FieldName = 'Probenziehung'
    end
    object MTGeplanteKontrollenRegnrOrt: TStringField
      FieldName = 'RegnrOrt'
      Size = 255
    end
    object MTGeplanteKontrollenBestaetigtUm: TDateTimeField
      FieldName = 'BestaetigtUm'
    end
    object MTGeplanteKontrollenverweigerunggrund: TStringField
      FieldName = 'verweigerunggrund'
      Size = 255
    end
    object MTGeplanteKontrollenAngemeldetUm: TDateTimeField
      FieldName = 'AngemeldetUm'
    end
    object MTGeplanteKontrollenBetriebName: TStringField
      FieldName = 'BetriebName'
      Size = 255
    end
    object MTGeplanteKontrollenRechtsgrundlageBez: TStringField
      FieldName = 'RechtsgrundlageBez'
      Size = 255
    end
    object MTGeplanteKontrollenErfasserName: TStringField
      FieldName = 'ErfasserName'
      Size = 255
    end
    object MTGeplanteKontrollenKontrollorganName: TStringField
      FieldName = 'KontrollorganName'
      Size = 255
    end
    object MTGeplanteKontrollenKontrolltypTyp: TStringField
      FieldName = 'KontrolltypTyp'
      Size = 255
    end
    object MTGeplanteKontrollenBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Size = 255
    end
    object MTGeplanteKontrollenStatus: TStringField
      FieldName = 'Status'
      Size = 255
    end
    object MTGeplanteKontrollenOrt: TStringField
      FieldName = 'Ort'
      Size = 255
    end
    object MTGeplanteKontrollenFaelligkeitsdatum: TDateField
      FieldName = 'Faelligkeitsdatum'
    end
    object MTGeplanteKontrollenGuid: TGuidField
      FieldName = 'Guid'
      Size = 38
    end
    object MTGeplanteKontrollenStartzeit: TDateTimeField
      FieldName = 'Startzeit'
    end
    object MTGeplanteKontrollenEndezeit: TDateTimeField
      FieldName = 'Endezeit'
    end
    object MTGeplanteKontrollenLFBIS: TStringField
      FieldName = 'LFBIS'
      Size = 255
    end
    object MTGeplanteKontrollenBetriebstyp: TStringField
      FieldName = 'Betriebstyp'
      Size = 10
    end
    object MTGeplanteKontrollenKontrollInformationen: TStringField
      FieldName = 'KontrollInformationen'
      Size = 4000
    end
    object MTGeplanteKontrollenKurzbemerkung: TWideMemoField
      FieldName = 'Kurzbemerkung'
      BlobType = ftWideMemo
    end
    object MTGeplanteKontrollenGruppeQuelle: TStringField
      FieldName = 'GruppeQuelle'
      Size = 255
    end
    object MTGeplanteKontrollenGemeindeNummer: TIntegerField
      FieldName = 'GemeindeNummer'
    end
    object MTGeplanteKontrollenPLZ: TStringField
      FieldName = 'PLZ'
      Size = 8
    end
    object MTGeplanteKontrollenStrasse: TStringField
      FieldName = 'Strasse'
      Size = 100
    end
  end
  object MTUngeplanteKontrollen: TFDMemTable
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvSilentMode]
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    Left = 40
    Top = 184
    object MTUngeplanteKontrollenID: TIntegerField
      FieldName = 'ID'
    end
    object MTUngeplanteKontrollenBkb: TStringField
      FieldName = 'Bkb'
      Size = 26
    end
    object MTUngeplanteKontrollenDatum: TDateField
      FieldName = 'Datum'
    end
    object MTUngeplanteKontrollenRefBkb: TStringField
      FieldName = 'RefBkb'
      Size = 26
    end
    object MTUngeplanteKontrollenProbenziehung: TBooleanField
      FieldName = 'Probenziehung'
    end
    object MTUngeplanteKontrollenRegnrOrt: TStringField
      FieldName = 'RegnrOrt'
      Size = 255
    end
    object MTUngeplanteKontrollenBestaetigtUm: TDateTimeField
      FieldName = 'BestaetigtUm'
    end
    object MTUngeplanteKontrollenverweigerunggrund: TStringField
      FieldName = 'verweigerunggrund'
      Size = 255
    end
    object MTUngeplanteKontrollenAngemeldetUm: TDateTimeField
      FieldName = 'AngemeldetUm'
    end
    object MTUngeplanteKontrollenBetriebName: TStringField
      FieldName = 'BetriebName'
      Size = 255
    end
    object MTUngeplanteKontrollenRechtsgrundlageBez: TStringField
      FieldName = 'RechtsgrundlageBez'
      Size = 255
    end
    object MTUngeplanteKontrollenErfasserName: TStringField
      FieldName = 'ErfasserName'
      Size = 255
    end
    object MTUngeplanteKontrollenKontrollorganName: TStringField
      FieldName = 'KontrollorganName'
      Size = 255
    end
    object MTUngeplanteKontrollenKontrolltypTyp: TStringField
      FieldName = 'KontrolltypTyp'
      Size = 255
    end
    object MTUngeplanteKontrollenBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Size = 255
    end
    object MTUngeplanteKontrollenStatus: TStringField
      FieldName = 'Status'
      Size = 255
    end
    object MTUngeplanteKontrollenOrt: TStringField
      FieldName = 'Ort'
      Size = 255
    end
    object MTUngeplanteKontrollenFaelligkeitsdatum: TDateField
      FieldName = 'Faelligkeitsdatum'
    end
    object MTUngeplanteKontrollenGuid: TGuidField
      FieldName = 'Guid'
      Size = 38
    end
    object MTUngeplanteKontrollenStartzeit: TDateTimeField
      FieldName = 'Startzeit'
    end
    object MTUngeplanteKontrollenEndezeit: TDateTimeField
      FieldName = 'Endezeit'
    end
    object MTUngeplanteKontrollenLFBIS: TStringField
      FieldName = 'LFBIS'
      Size = 255
    end
    object MTUngeplanteKontrollenBetriebstyp: TStringField
      FieldName = 'Betriebstyp'
      Size = 10
    end
    object MTUngeplanteKontrollenKontrollInformationen: TStringField
      FieldName = 'KontrollInformationen'
      Size = 4000
    end
    object MTUngeplanteKontrollenKurzbemerkung: TWideMemoField
      FieldName = 'Kurzbemerkung'
      BlobType = ftWideMemo
    end
    object MTUngeplanteKontrollenGruppeQuelle: TStringField
      FieldName = 'GruppeQuelle'
      Size = 255
    end
    object MTUngeplanteKontrollenGemeindeNummer: TIntegerField
      FieldName = 'GemeindeNummer'
    end
    object MTUngeplanteKontrollenPLZ: TStringField
      FieldName = 'PLZ'
      Size = 8
    end
    object MTUngeplanteKontrollenStrasse: TStringField
      FieldName = 'Strasse'
      Size = 100
    end
  end
  object MTGruppenPersonen: TFDMemTable
    Active = True
    FieldDefs = <
      item
        Name = 'PersonNachname'
        DataType = ftString
        Size = 100
      end
      item
        Name = 'PersonVorname'
        DataType = ftString
        Size = 100
      end
      item
        Name = 'Gruppe'
        DataType = ftString
        Size = 100
      end
      item
        Name = 'GruppeID'
        DataType = ftInteger
      end
      item
        Name = 'PersonID'
        DataType = ftInteger
      end
      item
        Name = 'ID'
        DataType = ftInteger
      end>
    IndexDefs = <>
    IndexFieldNames = 'PersonNachname;PersonVorname;Gruppe'
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvSilentMode]
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    StoreDefs = True
    Left = 176
    Top = 88
    object MTGruppenPersonenPersonNachname: TStringField
      FieldName = 'PersonNachname'
      KeyFields = 'PersonID'
      Size = 100
    end
    object MTGruppenPersonenPersonVorname: TStringField
      FieldName = 'PersonVorname'
      Size = 100
    end
    object MTGruppenPersonenGruppe: TStringField
      FieldName = 'Gruppe'
      Size = 100
    end
    object MTGruppenPersonenGruppeID: TIntegerField
      FieldName = 'GruppeID'
    end
    object MTGruppenPersonenPersonID: TIntegerField
      FieldName = 'PersonID'
    end
    object MTGruppenPersonenID: TIntegerField
      FieldName = 'ID'
    end
  end
  object QFristVerlaengern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Maengel'
      'SET    '
      '  Frist = :frist,'
      '  Text = :Bemerkung + Text  '
      ''
      'WHERE  ID = :id;')
    Left = 30
    Top = 385
    ParamData = <
      item
        Name = 'FRIST'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEMERKUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object QMangelAbschliessen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Maengel'
      'SET    '
      '  Beseitigt_Am = :beseitigt_am,'
      '  Text = :Bemerkung + Text'
      'WHERE  Id = :id')
    Left = 134
    Top = 385
    ParamData = <
      item
        Name = 'BESEITIGT_AM'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEMERKUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object DSProbenPDF: TDataSource
    DataSet = quProbenPdf
    Left = 344
    Top = 96
  end
  object QProbenPDFDokument: TFDQuery
    MasterSource = DSProbenPDF
    MasterFields = 'GUID_DOKUMENT_AGES_ERGEBNIS'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * from Bewegungsdaten.Dokumente'
      'where GUID = :GUID_DOKUMENT_AGES_ERGEBNIS')
    Left = 344
    Top = 160
    ParamData = <
      item
        Name = 'GUID_DOKUMENT_AGES_ERGEBNIS'
        DataType = ftGuid
        ParamType = ptInput
        Value = Null
      end>
    object QProbenPDFDokumentGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QProbenPDFDokumentBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QProbenPDFDokumentBEZEICHNUNG: TWideStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 250
    end
    object QProbenPDFDokumentTYP: TStringField
      FieldName = 'TYP'
      Origin = 'TYP'
      Required = True
      Size = 4
    end
    object QProbenPDFDokumentDATEINAME: TWideStringField
      FieldName = 'DATEINAME'
      Origin = 'DATEINAME'
      Required = True
      Size = 250
    end
    object QProbenPDFDokumentERSTELLT_AM: TSQLTimeStampField
      FieldName = 'ERSTELLT_AM'
      Origin = 'ERSTELLT_AM'
      Required = True
    end
    object QProbenPDFDokumentDOKUMENT: TBlobField
      FieldName = 'DOKUMENT'
      Origin = 'DOKUMENT'
      Required = True
      Size = **********
    end
  end
  object QMangelbildSpeichern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * from BEWEGUNGSDATEN.KONTROLLBERICHT_BILDER'
      'where id is null -- wir wollen hier nur Daten einf'#252'gen!')
    Left = 248
    Top = 384
    object QMangelbildSpeichernID: TGuidField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QMangelbildSpeichernBILD: TBlobField
      FieldName = 'BILD'
      Origin = 'BILD'
      Required = True
      Size = **********
    end
    object QMangelbildSpeichernFORMAT: TStringField
      FieldName = 'FORMAT'
      Origin = 'FORMAT'
      Required = True
      FixedChar = True
      Size = 3
    end
    object QMangelbildSpeichernID_BEWERTETEFRAGE: TIntegerField
      FieldName = 'ID_BEWERTETEFRAGE'
      Origin = 'ID_BEWERTETEFRAGE'
    end
    object QMangelbildSpeichernID_MANGEL: TIntegerField
      FieldName = 'ID_MANGEL'
      Origin = 'ID_MANGEL'
    end
    object QMangelbildSpeichernID_PROBE: TIntegerField
      FieldName = 'ID_PROBE'
      Origin = 'ID_PROBE'
    end
    object QMangelbildSpeichernBEMERKUNG: TMemoField
      FieldName = 'BEMERKUNG'
      Origin = 'BEMERKUNG'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object QMangelbildSpeichernAUFNAHMEDATUM: TSQLTimeStampField
      FieldName = 'AUFNAHMEDATUM'
      Origin = 'AUFNAHMEDATUM'
      Required = True
    end
    object QMangelbildSpeichernID_AUFGENOMMEN_VON: TIntegerField
      FieldName = 'ID_AUFGENOMMEN_VON'
      Origin = 'ID_AUFGENOMMEN_VON'
      Required = True
    end
  end
  object QMangelBilder: TFDQuery
    Connection = dm_main.FBC_MAIN
    FetchOptions.AssignedValues = [evMode, evRowsetSize, evCache, evRecordCountMode]
    FetchOptions.RowsetSize = 1
    FetchOptions.RecordCountMode = cmTotal
    SQL.Strings = (
      'SELECT kb.Bild '
      'FROM Bewegungsdaten.Kontrollbericht_Bilder kb'
      'WHERE kb.ID_Mangel = :MassnahmenID')
    Left = 736
    Top = 16
    ParamData = <
      item
        Name = 'MASSNAHMENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QMangelBilderBild: TBlobField
      FieldName = 'Bild'
      Origin = 'Bild'
      Required = True
      Size = **********
    end
  end
  object QKontrollorgan: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select distinct P.VORNAME + '#39' '#39' + P.NACHNAME as Name,'
      '                vGU.GroupUserID                 ID,'
      '                P.TITEL'
      'from SYSTEMSTAMMDATEN.vGroupUsers vGU'
      '         join STAMMDATEN.PERSONEN P on P.ID = vGU.ID_PERSON'
      'where vGU.UserID = :UserId'
      '  and vGU.MembershipLevel < 100 --alle Untergruppen'
      'order by Name')
    Left = 512
    Top = 88
    ParamData = <
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = 464
      end>
    object QKontrollorganid: TFDAutoIncField
      FieldName = 'id'
      Origin = 'id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QKontrollorgantitel: TStringField
      FieldName = 'titel'
      Origin = 'titel'
    end
    object QKontrollorganName: TStringField
      FieldName = 'Name'
      Origin = 'Name'
      ReadOnly = True
      Required = True
      Size = 121
    end
  end
  object QGruppen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE  Bldcode = :bldcode AND persoenlich = 0;')
    Left = 512
    Top = 152
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QGruppenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGruppenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QGruppenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QGruppenMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QGruppenID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QGruppenID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QGruppenOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QGruppenPERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
    object QGruppenEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 250
    end
  end
  object QUngeplanteKontrolle: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * '
      'from Bewegungsdaten.Kontrollbericht'
      'where ID = :id')
    Left = 512
    Top = 216
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = -1
      end>
    object QUngeplanteKontrolleID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QUngeplanteKontrolleGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Size = 38
    end
    object QUngeplanteKontrolleBKB: TStringField
      FieldName = 'BKB'
      Origin = 'BKB'
      Required = True
      Size = 26
    end
    object QUngeplanteKontrolleBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Required = True
      Size = 10
    end
    object QUngeplanteKontrolleKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      FixedChar = True
      Size = 3
    end
    object QUngeplanteKontrolleDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
    end
    object QUngeplanteKontrolleERFASSER_PSKEY: TStringField
      FieldName = 'ERFASSER_PSKEY'
      Origin = 'ERFASSER_PSKEY'
      FixedChar = True
      Size = 7
    end
    object QUngeplanteKontrolleKONTROLLORGAN_PSKEY: TStringField
      FieldName = 'KONTROLLORGAN_PSKEY'
      Origin = 'KONTROLLORGAN_PSKEY'
      FixedChar = True
      Size = 7
    end
    object QUngeplanteKontrolleID_PERSON_ERFASSER: TIntegerField
      FieldName = 'ID_PERSON_ERFASSER'
      Origin = 'ID_PERSON_ERFASSER'
    end
    object QUngeplanteKontrolleID_PERSON_KONTROLLORGAN: TIntegerField
      FieldName = 'ID_PERSON_KONTROLLORGAN'
      Origin = 'ID_PERSON_KONTROLLORGAN'
    end
    object QUngeplanteKontrolleREF_BKB: TStringField
      FieldName = 'REF_BKB'
      Origin = 'REF_BKB'
      Size = 26
    end
    object QUngeplanteKontrollePROBENZIEHUNG: TBooleanField
      FieldName = 'PROBENZIEHUNG'
      Origin = 'PROBENZIEHUNG'
    end
    object QUngeplanteKontrolleID_BETRIEB: TIntegerField
      FieldName = 'ID_BETRIEB'
      Origin = 'ID_BETRIEB'
      Required = True
    end
    object QUngeplanteKontrolleREGNR_ORT: TStringField
      FieldName = 'REGNR_ORT'
      Origin = 'REGNR_ORT'
      FixedChar = True
      Size = 7
    end
    object QUngeplanteKontrolleSTARTZEIT: TSQLTimeStampField
      FieldName = 'STARTZEIT'
      Origin = 'STARTZEIT'
    end
    object QUngeplanteKontrolleENDEZEIT: TSQLTimeStampField
      FieldName = 'ENDEZEIT'
      Origin = 'ENDEZEIT'
    end
    object QUngeplanteKontrolleBESTAETIGT_UM: TSQLTimeStampField
      FieldName = 'BESTAETIGT_UM'
      Origin = 'BESTAETIGT_UM'
    end
    object QUngeplanteKontrolleID_RECHTSGRUNDLAGE: TIntegerField
      FieldName = 'ID_RECHTSGRUNDLAGE'
      Origin = 'ID_RECHTSGRUNDLAGE'
      Required = True
    end
    object QUngeplanteKontrolleSTATUS: TStringField
      FieldName = 'STATUS'
      Origin = 'STATUS'
      FixedChar = True
      Size = 1
    end
    object QUngeplanteKontrolleANGEMELDET_UM: TSQLTimeStampField
      FieldName = 'ANGEMELDET_UM'
      Origin = 'ANGEMELDET_UM'
    end
    object QUngeplanteKontrolleTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object QUngeplanteKontrolleLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object QUngeplanteKontrolleBETRIEBSTYP: TStringField
      FieldName = 'BETRIEBSTYP'
      Origin = 'BETRIEBSTYP'
      Size = 2
    end
    object QUngeplanteKontrolleGUID_UNTERSCHRIFT_ANWESENDER_BETRIEB: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Origin = 'GUID_UNTERSCHRIFT_ANWESENDER_BETRIEB'
      Size = 38
    end
    object QUngeplanteKontrolleGUID_UNTERSCHRIFT_KONTROLLORGAN: TGuidField
      FieldName = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Origin = 'GUID_UNTERSCHRIFT_KONTROLLORGAN'
      Size = 38
    end
    object QUngeplanteKontrolleVERWEIGERUNGSGRUND_UNTERSCHRIFT: TStringField
      FieldName = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Origin = 'VERWEIGERUNGSGRUND_UNTERSCHRIFT'
      Size = 255
    end
    object QUngeplanteKontrolleVerweigerunggrund: TStringField
      FieldName = 'Verweigerunggrund'
      Origin = 'Verweigerunggrund'
      Size = 150
    end
    object QUngeplanteKontrolleGUID_DOKUMENT: TGuidField
      FieldName = 'GUID_DOKUMENT'
      Origin = 'GUID_DOKUMENT'
      Size = 38
    end
    object QUngeplanteKontrolleFEHLERHAFT_GESETZT_AM: TSQLTimeStampField
      FieldName = 'FEHLERHAFT_GESETZT_AM'
      Origin = 'FEHLERHAFT_GESETZT_AM'
    end
    object QUngeplanteKontrolleSTORNIERT_AM: TSQLTimeStampField
      FieldName = 'STORNIERT_AM'
      Origin = 'STORNIERT_AM'
    end
    object QUngeplanteKontrolleSTORNOGRUND: TStringField
      FieldName = 'STORNOGRUND'
      Origin = 'STORNOGRUND'
      Size = 255
    end
    object QUngeplanteKontrolleVERWEIGERT_AM: TSQLTimeStampField
      FieldName = 'VERWEIGERT_AM'
      Origin = 'VERWEIGERT_AM'
    end
    object QUngeplanteKontrolleGUID_DOKUMENT_CC: TGuidField
      FieldName = 'GUID_DOKUMENT_CC'
      Origin = 'GUID_DOKUMENT_CC'
      Size = 38
    end
    object QUngeplanteKontrolleVISEXPORT_AM: TSQLTimeStampField
      FieldName = 'VISEXPORT_AM'
      Origin = 'VISEXPORT_AM'
    end
    object QUngeplanteKontrolleKONTROLL_INFORMATIONEN: TStringField
      FieldName = 'KONTROLL_INFORMATIONEN'
      Origin = 'KONTROLL_INFORMATIONEN'
      Size = 4000
    end
    object QUngeplanteKontrolleID_GRUPPE_QUELLE: TIntegerField
      FieldName = 'ID_GRUPPE_QUELLE'
      Origin = 'ID_GRUPPE_QUELLE'
    end
    object QUngeplanteKontrolleKURZBEMERKUNG: TWideMemoField
      FieldName = 'KURZBEMERKUNG'
      Origin = 'KURZBEMERKUNG'
      BlobType = ftWideMemo
      Size = **********
    end
  end
  object QTodoUngeplanteKontrolle: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'delete'
      'from BEWEGUNGSDATEN.TODO'
      'where ID_KONTROLLBERICHT = :kontrollid;'
      ''
      'insert into BEWEGUNGSDATEN.TODO'
      '( TITEL'
      ', FAELLIG'
      ', ID_GRUPPE'
      ', ID_USER'
      ', ID_KONTROLLBERICHT)'
      'values (:titel,'
      '        :datum,'
      '        :gruppenid,'
      '        :userid,'
      '        :kontrollid);')
    Left = 512
    Top = 264
    ParamData = <
      item
        Name = 'KONTROLLID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TITEL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DATUM'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
