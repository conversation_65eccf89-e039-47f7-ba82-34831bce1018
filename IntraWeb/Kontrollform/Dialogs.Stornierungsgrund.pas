unit Dialogs.Stornierungsgrund;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQEdit, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWCGJQDialogEx;

type
  TDialogStornierungsgrund = class(TDialogBase)
    IWLabel1: TIWCGJQLabel;
    IWCGJQRegion1: TIWCGJQRegion;
    EditStornierungsgrund: TIWCGJQEdit;
    procedure EditStornierungsgrundJQEventsKeyUp(Sender: TObject; AParams: TStringList);
  private
  public
    Stornierungsgrund: string;
    constructor Create(AOwner: TComponent); override;
    procedure DoButtonCancel(ASender: TObject; AParams: TStringList); override;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}


constructor TDialogStornierungsgrund.Create(AOwner: TComponent);
begin
  self := inherited Create(AOwner);
  IWFrameRegion.JQDialogOptions.Title := 'Kontrolle stornieren';
end;

procedure TDialogStornierungsgrund.DoButtonCancel(ASender: TObject;
  AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogStornierungsgrund.DoButtonOK(ASender: TObject; AParams: TStringList);
begin
  Stornierungsgrund := EditStornierungsgrund.Text;
  if Stornierungsgrund.Trim.Length < 3 then
  begin
    Alert.Error('Es muss ein Stornierungsgrund angegeben werden!');
    abort;
  end;
  inherited;
  Close;
end;

procedure TDialogStornierungsgrund.EditStornierungsgrundJQEventsKeyUp(Sender: TObject; AParams: TStringList);
begin
  inherited;
  var
  LText := EditStornierungsgrund.Text;
  ButtonOK.Enabled := LText.Length > 0;
end;

procedure TDialogStornierungsgrund.InitializeControls;
begin
  inherited;
  EditStornierungsgrund.Text := '';
end;

end.
