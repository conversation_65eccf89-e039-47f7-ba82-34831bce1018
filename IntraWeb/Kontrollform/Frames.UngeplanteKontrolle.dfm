inherited FrameUngeplanteKontrolle: TFrameUngeplanteKontrolle
  Width = 930
  Height = 600
  Align = alClient
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 930
    Height = 600
    RenderInvisibleControls = True
    TabOrder = 7
    DesignSize = (
      930
      600)
    object jqdBetriebe: TIWCGJQDialog
      Left = 40
      Top = 16
      Width = 800
      Height = 400
      Visible = False
      TabOrder = 15
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 400
      JQDialogOptions.Modal = True
      JQDialogOptions.Resizable = False
      JQDialogOptions.Width = 800
      JQDialogOptions.zIndex = 5000
      object iwrBetriebSuchen: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 800
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 8
        Version = '1.0'
        Align = alTop
        object IWLabel7: TIWCGJQLabel
          Left = 3
          Top = 3
          Width = 42
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Regnr:'
        end
        object IWLabel8: TIWCGJQLabel
          Left = 167
          Top = 3
          Width = 41
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Name:'
        end
        object IWLabel10: TIWCGJQLabel
          Left = 493
          Top = 3
          Width = 23
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Plz:'
        end
        object IWLabel9: TIWCGJQLabel
          Left = 329
          Top = 3
          Width = 47
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Stra'#223'e:'
        end
        object jqeRegnr: TIWCGJQEdit
          Left = 3
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 16
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqeName: TIWCGJQEdit
          Left = 167
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 17
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqbBetriebSuchen: TIWCGJQButton
          Left = 659
          Top = 25
          Width = 75
          Height = 21
          TabOrder = 18
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.OnEvent = jqbBetriebSuchenOnClick
        end
        object jqeStrasse: TIWCGJQEdit
          Left = 329
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 19
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqePlz: TIWCGJQEdit
          Left = 493
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 20
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
      end
      object iwrBetriebe: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 800
        Height = 340
        RenderInvisibleControls = True
        TabOrder = 9
        Version = '1.0'
        Align = alClient
        object jqgBetriebe: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 800
          Height = 340
          TabOrder = 21
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Regnr'
              Name = 'Regnr'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Regnr'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NAME'
              Name = 'NAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'STRASSE'
              Name = 'STRASSE'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Stra'#223'e'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'PLZ'
              Name = 'PLZ'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'PLZ'
            end>
          JQGridOptions.Height = 313
          JQGridOptions.RowNum = 200
          JQGridOptions.Sortable = True
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 798
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.OnSelectRow.OnEvent = JqgBetriebeOnSelectRow
          JQGridOptions.PagerVisible = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderBetriebe
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object jqdKontrollorgan: TIWCGJQDialog
      Left = 40
      Top = 19
      Width = 800
      Height = 397
      Visible = False
      TabOrder = 26
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 397
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 800
      JQDialogOptions.zIndex = 5000
      object iwrTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 800
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 10
        Version = '1.0'
        Align = alTop
        object IWLabel6: TIWCGJQLabel
          Left = 16
          Top = 3
          Width = 62
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Vorname:'
        end
        object IWLabel13: TIWCGJQLabel
          Left = 195
          Top = 3
          Width = 71
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Nachname:'
        end
        object jqeVorname: TIWCGJQEdit
          Left = 16
          Top = 25
          Width = 150
          Height = 21
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqeNachname: TIWCGJQEdit
          Left = 195
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 1
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqbKontrollorganSuchen: TIWCGJQButton
          Left = 376
          Top = 25
          Width = 75
          Height = 21
          TabOrder = 2
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.OnEvent = jqbKontrollorganSuchenOnClick
        end
      end
      object iwrMid: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 800
        Height = 337
        RenderInvisibleControls = True
        TabOrder = 11
        Version = '1.0'
        Align = alClient
        object jqgKontrollorgan: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 800
          Height = 337
          TabOrder = 27
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'TITEL'
              Name = 'TITEL'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Titel'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'VORNAME'
              Name = 'VORNAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Vorname'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NACHNAME'
              Name = 'NACHNAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Nachname'
            end>
          JQGridOptions.Height = 283
          JQGridOptions.Sortable = True
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 798
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.OnSelectRow.OnEvent = JqgKontrollorganOnSelectRow
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderKontrollorgan
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object iwrEingabe: TIWCGJQRegion
      Left = 122
      Top = 71
      Width = 680
      Height = 400
      RenderInvisibleControls = True
      TabOrder = 12
      Version = '1.0'
      Anchors = []
      object IWLabel1: TIWCGJQLabel
        Left = 11
        Top = 24
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollbericht Typ:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 11
        Top = 64
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontroll Typ:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 11
        Top = 104
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Betrieb:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 11
        Top = 176
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollgrund:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 11
        Top = 216
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'F'#228'lligkeitsdatum:'
      end
      object iwlBetrieb: TIWCGJQLabel
        Left = 202
        Top = 104
        Width = 415
        Height = 66
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'iwlBetrieb'
      end
      object IWLabel11: TIWCGJQLabel
        Left = 11
        Top = 256
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollorgan:'
      end
      object IWLabel12: TIWCGJQLabel
        Left = 11
        Top = 312
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Gruppe:'
      end
      object iwlKontrollorgan: TIWCGJQLabel
        Left = 224
        Top = 256
        Width = 393
        Height = 50
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'iwlBetrieb'
      end
      object iwlGruppe: TIWCGJQLabel
        Left = 201
        Top = 304
        Width = 200
        Height = 19
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'iwlBetrieb'
      end
      object CheckboxKontrollorgan: TIWCGJQCheckBoxEx
        Left = 201
        Top = 254
        Width = 24
        Height = 21
        TabOrder = 22
        Version = '1.0'
        Caption = ''
        JQCheckExOptions.OnChange.OnEvent = CheckboxKontrollorganJQCheckExOptionsChange
      end
      object CheckboxGruppe: TIWCGJQCheckBoxEx
        Left = 201
        Top = 312
        Width = 24
        Height = 21
        TabOrder = 23
        Version = '1.0'
        Caption = ''
        JQCheckExOptions.OnChange.OnEvent = CheckboxGruppeJQCheckExOptionsChange
      end
      object ButtonSpeichern: TIWCGJQButton
        Left = 521
        Top = 361
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Speichern'
        JQButtonOptions.OnClick.OnEvent = ButtonSpeichernOnClick
      end
      object jqdDatum: TIWCGJQDatePicker
        Left = 201
        Top = 216
        Width = 420
        Height = 21
        TabOrder = 4
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
      end
      object ComboBKBTyp: TIWCGJQComboBoxEx
        Left = 201
        Top = 22
        Width = 420
        Height = 21
        TabOrder = 5
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = jqcbKontrollberichtOnChange
        Caption = ''
      end
      object ComboKontrolltyp: TIWCGJQComboBoxEx
        Left = 201
        Top = 64
        Width = 420
        Height = 21
        TabOrder = 6
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = JqcKontrolltypOnChange
        Caption = ''
      end
      object ComboKontrollgrund: TIWCGJQComboBoxEx
        Left = 201
        Top = 176
        Width = 420
        Height = 21
        TabOrder = 13
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = JqcKontrollgrundOnChange
        Caption = ''
      end
      object ButtonBetriebsuche: TIWCGJQButton
        Left = 623
        Top = 102
        Width = 30
        Height = 21
        TabOrder = 14
        Version = '1.0'
        JQButtonOptions.Icons.Primary = 'ui-icon-pencil'
        JQButtonOptions.OnClick.OnEvent = jqbBetriebOnClick
        JQButtonOptions.OnClick.SendAllArguments = True
      end
      object jqbKontrollorgan: TIWCGJQButton
        Left = 623
        Top = 254
        Width = 30
        Height = 21
        TabOrder = 24
        Version = '1.0'
        JQButtonOptions.Icons.Primary = 'ui-icon-pencil'
        JQButtonOptions.OnClick.OnEvent = jqbKontrollorganOnClick
      end
      object ComboGruppe: TIWCGJQComboBoxEx
        Left = 224
        Top = 312
        Width = 398
        Height = 21
        TabOrder = 25
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 396
        JQComboBoxExOptions.OnChange.OnEvent = JqcbGruppeOnChange
        Caption = ''
      end
    end
  end
  object ProviderBetriebe: TIWCGJQGridDataSetProvider
    DataSet = dm_main.quBetriebssuche
    KeyFields = 'ID'
    Left = 872
    Top = 96
  end
  object ProviderKontrollorgan: TIWCGJQGridDataSetProvider
    DataSet = quKontrollorgan
    KeyFields = 'ID'
    Left = 872
    Top = 144
  end
  object quKontrollorgan: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT p.Vorname, p.nachname, u.id, p.titel'
      'FROM   Stammdaten.Personen p '
      
        '        INNER JOIN Systemstammdaten.[User] u ON u.Id_Person = p.' +
        'Id'
      'WHERE  Vorname LIKE '#39'%'#39' + :vorname + '#39'%'#39
      '        AND Nachname LIKE '#39'%'#39' + :nachname + '#39'%'#39
      '        AND Bldcode = :bldcode;')
    Left = 872
    Top = 200
    ParamData = <
      item
        Name = 'VORNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'NACHNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
    object quKontrollorganVorname: TStringField
      FieldName = 'Vorname'
      Origin = 'Vorname'
      Required = True
      Size = 60
    end
    object quKontrollorgannachname: TStringField
      FieldName = 'nachname'
      Origin = 'nachname'
      Required = True
      Size = 60
    end
    object quKontrollorganid: TFDAutoIncField
      FieldName = 'id'
      Origin = 'id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quKontrollorgantitel: TStringField
      FieldName = 'titel'
      Origin = 'titel'
    end
  end
  object quGruppen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE  Bldcode = :bldcode AND persoenlich = 0;')
    Left = 872
    Top = 256
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
    object quGruppenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quGruppenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object quGruppenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object quGruppenMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object quGruppenID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object quGruppenID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object quGruppenOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object quGruppenPERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
    object quGruppenEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 250
    end
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 768
    Top = 3
  end
end
