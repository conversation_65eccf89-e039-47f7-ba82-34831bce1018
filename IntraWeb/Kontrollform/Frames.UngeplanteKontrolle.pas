﻿unit Frames.UngeplanteKontrolle;

interface

uses
  System.SysUtils, System.Classes, VCL.Controls, VCL.Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQControl, IWCGJQButton, IWCGJQDialog,
  IWCompCheckbox, IWCGJQCheckBox, IWCGJQDatePicker, IWCGJQComboBox, IWCGJQGrid,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider,
  IWCGJQEdit, IWCGJQDateTimePicker, ELKE.Classes.Generated,
  System.Generics.Collections, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQShowMessage, IWCGJQSweetAlert, IWCGJQRegion, IWCGJQLabel,
  Frames.Base;

type
  TFrameUngeplanteKontrolle = class(TFrameBase)
    iwrEingabe: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    iwlBetrieb: TIWCGJQLabel;
    ButtonSpeichern: TIWCGJQButton;
    jqdDatum: TIWCGJQDatePicker;
    ComboBKBTyp: TIWCGJQComboBoxEx;
    ComboKontrolltyp: TIWCGJQComboBoxEx;
    ComboKontrollgrund: TIWCGJQComboBoxEx;
    ButtonBetriebsuche: TIWCGJQButton;
    jqdBetriebe: TIWCGJQDialog;
    iwrBetriebSuchen: TIWCGJQRegion;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    jqeRegnr: TIWCGJQEdit;
    jqeName: TIWCGJQEdit;
    jqbBetriebSuchen: TIWCGJQButton;
    jqeStrasse: TIWCGJQEdit;
    jqePlz: TIWCGJQEdit;
    iwrBetriebe: TIWCGJQRegion;
    jqgBetriebe: TIWCGJQGrid;
    ProviderBetriebe: TIWCGJQGridDataSetProvider;
    IWLabel11: TIWCGJQLabel;
    IWLabel12: TIWCGJQLabel;
    jqbKontrollorgan: TIWCGJQButton;
    iwlKontrollorgan: TIWCGJQLabel;
    iwlGruppe: TIWCGJQLabel;
    CheckboxKontrollorgan: TIWCGJQCheckBoxEx;
    CheckboxGruppe: TIWCGJQCheckBoxEx;
    ComboGruppe: TIWCGJQComboBoxEx;
    jqdKontrollorgan: TIWCGJQDialog;
    iwrTop: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    jqgKontrollorgan: TIWCGJQGrid;
    ProviderKontrollorgan: TIWCGJQGridDataSetProvider;
    IWLabel6: TIWCGJQLabel;
    jqeVorname: TIWCGJQEdit;
    jqeNachname: TIWCGJQEdit;
    IWLabel13: TIWCGJQLabel;
    jqbKontrollorganSuchen: TIWCGJQButton;
    quKontrollorgan: TFDQuery;
    quGruppen: TFDQuery;
    Alert: TIWCGJQSweetAlert;
    quKontrollorganVorname: TStringField;
    quKontrollorgannachname: TStringField;
    quKontrollorganid: TFDAutoIncField;
    quKontrollorgantitel: TStringField;
    quGruppenID: TFDAutoIncField;
    quGruppenBEZEICHNUNG: TStringField;
    quGruppenBLDCODE: TSmallintField;
    quGruppenMUTTERGRUPPE: TIntegerField;
    quGruppenID_USER_HAUPTVER: TIntegerField;
    quGruppenID_USER_STELLVER: TIntegerField;
    quGruppenOKZ: TStringField;
    quGruppenPERSOENLICH: TBooleanField;
    quGruppenEMAIL: TStringField;

    procedure jqbBetriebOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbBetriebSuchenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbKontrollorganOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbKontrollorganSuchenOnClick(Sender: TObject; AParams: TStringList);
    procedure JqgBetriebeOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure JqgKontrollorganOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure ButtonSpeichernOnClick(Sender: TObject; AParams: TStringList);

    procedure jqcbKontrollberichtOnChange(Sender: TObject; AParams: TStringList);
    procedure JqcKontrolltypOnChange(Sender: TObject; AParams: TStringList);
    procedure JqcKontrollgrundOnChange(Sender: TObject; AParams: TStringList);
    procedure JqcbGruppeOnChange(Sender: TObject; AParams: TStringList);

    procedure CheckboxGruppeJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
    procedure CheckboxKontrollorganJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    idBetrieb: Integer;
    idKontrollorgan: Integer;
    berichtstypen: TList<TBkbTyp>;
    FIsEditMode: Boolean;
    kontrolltypen: TList<TKontrolltyp>;
    rechtsgrundlagen: TList<TRechtsgrundlage>;
    gruppen: TList<TGruppe>;
    FKontrolle: TKontrollbericht;
    FSaveProc: TProc;

    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure betriebAusgewaehlt(Sender: TObject; AURLParams: TStringList);
    procedure KontrollorganAusgewaehlt(Sender: TObject; AURLParams: TStringList);
    procedure CheckObSpeichernErlaubt;
    procedure KontrolltypenLaden(ABkbTyp: string);
    procedure RechtsgrundlagenLaden(ABkbTyp: string);

    procedure DoOnSave;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; ASaveProc: TProc); reintroduce;
    destructor Destroy; override;
    procedure InitialisiereAusKontrolle(AKontrolle: TKontrollbericht);
    procedure ResetFields(resetBkbtyp: Boolean = true; resetBetrieb: Boolean = true);
    property IsEditMode: Boolean read FIsEditMode write FIsEditMode;
  end;

implementation

uses ServerController, dmmain, Utility, ELKE.Services.Me.Intf,
  DX.Classes.ObjectFactory, JQ.Helpers.Button, ELKE.Classes.RESTError;

{$R *.dfm}


constructor TFrameUngeplanteKontrolle.Create(AOwner: TComponent; ASaveProc: TProc);
var
  bkbtyp: TBkbTyp;
  gruppe: TGruppe;
  item: TIWCGJQComboBoxExItem;
  i: Integer;
begin
  inherited Create(AOwner);
  FSaveProc := ASaveProc;
  berichtstypen := Usersession.ELKERest.berichtstypen;
  idKontrollorgan := -1;

  ComboBoxDefaultEintrag(ComboBKBTyp, false);
  // Alle erlaubten Bkbtypen zur Auswahl stellen
  for i := 0 to berichtstypen.Count - 1 do
  begin
    bkbtyp := berichtstypen[i];
    if bkbtyp.Probe = false then
    begin
      item := ComboBKBTyp.Items.Add;
      item.Caption := bkbtyp.Bezeichnung;
      item.Value := bkbtyp.Typ
    end;
  end;

  // Alle Gruppen in denen der Benutzer Mitglied ist als Auswahl setzen
  ComboBoxDefaultEintrag(ComboGruppe, false);
  gruppen := Usersession.ELKERest.GetGruppenzugehoerigkeitenMitUntergruppen;
  for i := 0 to gruppen.Count - 1 do
  begin
    gruppe := gruppen[i];
    gruppe.Untergruppen;
    item := ComboGruppe.Items.Add;
    item.Caption := gruppe.Bezeichnung;
    item.Value := gruppe.Id.ToString;
    item.UserData := gruppe;
  end;

  // Heutiges Datum setzen
  jqdDatum.Date := Now;
  // Reset Fields
  ResetFields;

  ButtonSpeichern.AsyncShowWaitWheel;
end;

destructor TFrameUngeplanteKontrolle.Destroy;
begin
  FreeAndNil(berichtstypen);
  FreeAndNil(kontrolltypen);
  FreeAndNil(rechtsgrundlagen);
  FreeAndNil(gruppen);
  inherited;
end;

procedure TFrameUngeplanteKontrolle.DoOnSave;
begin
  if Assigned(FSaveProc) then
  begin
    FSaveProc;
  end;
end;

procedure TFrameUngeplanteKontrolle.InitialisiereAusKontrolle(AKontrolle: TKontrollbericht);
begin
  ResetFields;
  FKontrolle := AKontrolle;
  IsEditMode := true;

  ComboBKBTyp.SelectByValue(AKontrolle.Kontrolltyp.bkbtyp.Typ);
  ComboBKBTyp.Enabled := false;

  iwlBetrieb.Caption := AKontrolle.Betrieb.Name;
  iwlBetrieb.Enabled := false;
  idBetrieb := AKontrolle.Betrieb.Id;

  KontrolltypenLaden(AKontrolle.Kontrolltyp.bkbtyp.Typ);
  ComboKontrolltyp.SelectByValue(AKontrolle.Kontrolltyp.Kontrolltyp);
  ComboKontrolltyp.Enabled := true;

  RechtsgrundlagenLaden(AKontrolle.Kontrolltyp.bkbtyp.Typ);
  ComboKontrollgrund.SelectByValue(AKontrolle.Rechtsgrundlage.Id.ToString);
  ComboKontrollgrund.Enabled := true;

  var
  LGruppe := -1;
  if (AKontrolle.Todos.First <> nil) and (AKontrolle.Todos.First.gruppe <> nil) then
  begin
    LGruppe := AKontrolle.Todos.First.gruppe.Id;
  end;

  ComboGruppe.SelectByValue(LGruppe.ToString);
  ComboGruppe.Visible := true;
  CheckboxGruppe.Checked := LGruppe > -1;
  CheckboxGruppe.Enabled := true;
  ComboGruppe.Enabled := CheckboxGruppe.Checked;

  if AKontrolle.Kontrollorgan <> nil then
  begin
    iwlKontrollorgan.Caption := Format('%s %s %s', [AKontrolle.Kontrollorgan.Titel.ValueOrDefault,
      AKontrolle.Kontrollorgan.Vorname, AKontrolle.Kontrollorgan.Nachname]).Trim;
    idKontrollorgan := AKontrolle.Kontrollorgan.Id;
    CheckboxKontrollorgan.Checked := true;
  end;
  CheckboxKontrollorgan.Enabled := true;
  jqbKontrollorgan.Enabled := true;

  if AKontrolle.Todos.First <> nil then
  begin
    jqdDatum.Date := AKontrolle.Todos.First.Faellig;
  end
  else
  begin
    jqdDatum.Date := AKontrolle.Datum.ValueOrDefault;
  end;
  jqdDatum.Enabled := true;

  ButtonBetriebsuche.Enabled := false;

  CheckObSpeichernErlaubt;
end;

// Checkt ob alle nötigen Felder ausgefüllt sind um die Kontrolle abzuspeichern
procedure TFrameUngeplanteKontrolle.CheckboxGruppeJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
begin
  if CheckboxGruppe.Checked then
  begin
    // Disable Kontrollorgan
    CheckboxKontrollorgan.Checked := false;
    iwlKontrollorgan.Caption := '';
    jqbKontrollorgan.Enabled := false;
    idKontrollorgan := -1;
    // Enable Gruppe
    ComboGruppe.Visible := true;
    ComboGruppe.Enabled := true;
  end
  else
  begin
    // Disable Gruppe
    ComboGruppe.Visible := false;
    ComboGruppe.Enabled := false;
    ComboGruppe.SelectedIndex := 0;

    // Enable Kontrollorgan
    CheckboxKontrollorgan.Checked := true;
    iwlKontrollorgan.Caption := '';
    jqbKontrollorgan.Enabled := true;
    idKontrollorgan := -1;
  end;
  CheckObSpeichernErlaubt;

end;

procedure TFrameUngeplanteKontrolle.CheckboxKontrollorganJQCheckExOptionsChange(Sender: TObject; AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.CheckObSpeichernErlaubt;
begin
  ButtonSpeichern.Enabled :=
    ((ComboBKBTyp.SelectedItem <> nil) and (ComboBKBTyp.SelectedItem.Value <> '-1')) and
    ((ComboKontrolltyp.SelectedItem <> nil) and (ComboKontrolltyp.SelectedItem.Value <> '-1')) and
    (idBetrieb <> -1) and
    ((ComboKontrollgrund.SelectedItem <> nil) and (ComboKontrollgrund.SelectedItem.Value <> '-1')) and
    (jqdDatum.Date > 0) and
    ((idKontrollorgan <> -1) or ((ComboGruppe.SelectedItem <> nil) and (ComboGruppe.SelectedItem.Value <> '-1')));

  if CheckboxKontrollorgan.Checked then
  begin
    // Disable Gruppe
    CheckboxGruppe.Checked := false;
    ComboGruppe.Visible := false;
    // Enable Kontrollorgan
    jqbKontrollorgan.Enabled := true;
  end
  else
  begin
    jqbKontrollorgan.Enabled := false;
    idKontrollorgan := -1;
    iwlKontrollorgan.Caption := '';
  end;
end;

{ Kontrollbericht }
procedure TFrameUngeplanteKontrolle.jqcbKontrollberichtOnChange(Sender: TObject;
  AParams: TStringList);
var
  bkbtyp: String;
begin
  ResetFields(false, false);
  bkbtyp := ComboBKBTyp.SelectedItem.Value;
  if bkbtyp = '-1' then
    Abort;

  KontrolltypenLaden(bkbtyp);
  RechtsgrundlagenLaden(bkbtyp);

  // Ausgeschalteten Felder einschalten
  CheckboxGruppe.Enabled := true;
  CheckboxKontrollorgan.Enabled := true;
  ComboKontrolltyp.Enabled := true;
  ComboKontrollgrund.Enabled := true;
  iwlBetrieb.Enabled := true;
  jqdDatum.Enabled := true;

  CheckObSpeichernErlaubt;
end;

{ Öffnet den Dialog mit der Betriebssuche }
procedure TFrameUngeplanteKontrolle.jqbBetriebOnClick(Sender: TObject;
  AParams: TStringList);
begin
  dm_main.quBetriebssuche.Close;
  jqeRegnr.Text := '';
  jqeName.Text := '';
  jqePlz.Text := '';
  jqeStrasse.Text := '';
  jqgBetriebe.JQGridOptions.ResetSelection;
  DialogErstellen(jqdBetriebe, betriebAusgewaehlt, jqdAbbrechenEvent, 'Betrieb suchen');
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := true;
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.JqgBetriebeOnSelectRow(Sender: TObject;
  AParams: TStringList);
begin
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := false;
  CheckObSpeichernErlaubt;
end;

{ Sucht im Dialog nach einem Betrieb }
procedure TFrameUngeplanteKontrolle.jqbBetriebSuchenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  var
  LSearchtext := jqeStrasse.Text + jqeRegnr.Text + jqeName.Text + jqePlz.Text;
  if LSearchtext.Length < 3 then
  begin
    Alert.Info('Sie müssen zuerst Suchparameter eingeben');
    Abort;
  end;
  jqgBetriebe.JQGridOptions.ResetSelection;
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := true;
  dm_main.quBetriebssuche.Close;
  dm_main.quBetriebssuche.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  dm_main.quBetriebssuche.ParamByName('strasse').AsString := jqeStrasse.Text;
  dm_main.quBetriebssuche.ParamByName('regnr').AsString := jqeRegnr.Text;
  dm_main.quBetriebssuche.ParamByName('name').AsString := jqeName.Text;
  dm_main.quBetriebssuche.ParamByName('plz').AsString := jqePlz.Text;
  dm_main.quBetriebssuche.Active := true;
  CheckObSpeichernErlaubt;
end;

{ Wenn der User den Betriebssuchen Dialog bestätigt. }
procedure TFrameUngeplanteKontrolle.betriebAusgewaehlt(Sender: TObject; AURLParams: TStringList);
begin
  jqdBetriebe.Visible := false;
  if not moveQueryToRow(dm_main.quBetriebssuche, jqgBetriebe) then
  begin
    Alert.Warning('Sie haben keinen Betrieb ausgewählt.');
    Abort;
  end;
  iwlBetrieb.Caption := dm_main.quBetriebssuche.FieldByName('name').AsString;
  idBetrieb := dm_main.quBetriebssuche.FieldByName('id').AsInteger;
  dm_main.quBetriebssuche.Close;
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.JqcKontrolltypOnChange(Sender: TObject;
  AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.JqcKontrollgrundOnChange(
  Sender: TObject; AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.JqcbGruppeOnChange(Sender: TObject;
  AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

{ Öffnet einen Dialog, in welchem der User das Kontrollorgan setzen kann }
procedure TFrameUngeplanteKontrolle.jqbKontrollorganOnClick(Sender: TObject;
  AParams: TStringList);
begin
  quKontrollorgan.Close;
  jqeVorname.Text := '';
  jqeNachname.Text := '';
  jqgKontrollorgan.JQGridOptions.ResetSelection;
  DialogErstellen(jqdKontrollorgan, KontrollorganAusgewaehlt, jqdAbbrechenEvent, 'Kontrollorgan auswählen');
  jqdKontrollorgan.JQDialogOptions.Buttons.Items[0].Disabled := true;
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.JqgKontrollorganOnSelectRow(
  Sender: TObject; AParams: TStringList);
begin
  jqdKontrollorgan.JQDialogOptions.Buttons.Items[0].Disabled := false;
  CheckObSpeichernErlaubt;
end;

{ Startet die Query für die Kontrollorgane im Dialog }
procedure TFrameUngeplanteKontrolle.jqbKontrollorganSuchenOnClick(
  Sender: TObject; AParams: TStringList);
begin
  jqgKontrollorgan.JQGridOptions.ResetSelection;
  jqdKontrollorgan.JQDialogOptions.Buttons.Items[0].Disabled := true;
  quKontrollorgan.Close;
  quKontrollorgan.Prepare;
  quKontrollorgan.ParamByName('vorname').AsString := jqeVorname.Text;
  quKontrollorgan.ParamByName('nachname').AsString := jqeNachname.Text;
  quKontrollorgan.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quKontrollorgan.Active := true;
  CheckObSpeichernErlaubt;
end;

{ Wird aufgerufen, wenn der Kontrollorgan-Dialog mit Auswählen geschlossen wird.
  Setzt den Namen und die ID des Kontrollorgans }
procedure TFrameUngeplanteKontrolle.KontrollorganAusgewaehlt(Sender: TObject; AURLParams: TStringList);
begin
  jqdKontrollorgan.Visible := false;
  if not moveQueryToRow(quKontrollorgan, jqgKontrollorgan) then
    Exit;
  iwlKontrollorgan.Caption := quKontrollorgan.FieldByName('titel').AsString + ' '
    + quKontrollorgan.FieldByName('vorname').AsString + ' '
    + quKontrollorgan.FieldByName('nachname').AsString;
  idKontrollorgan := quKontrollorgan.FieldByName('id').AsInteger;
  quKontrollorgan.Close;
  CheckObSpeichernErlaubt;
end;

procedure TFrameUngeplanteKontrolle.KontrolltypenLaden(ABkbTyp: string);
begin
  // Alle erlaubten Bkbtypen zur Auswahl stellen
  FreeAndNil(kontrolltypen);
  kontrolltypen := Usersession.ELKERest.kontrolltypen(ABkbTyp);
  ComboKontrolltyp.Items.Clear;
  ComboBoxDefaultEintrag(ComboKontrolltyp, false);
  for var i := 0 to kontrolltypen.Count - 1 do
  begin
    var
    LKontrolltyp := kontrolltypen[i];
    if LKontrolltyp.Bezeichnung.HasValue then
    begin
      var
      LItem := ComboKontrolltyp.Items.Add;
      LItem.Caption := LKontrolltyp.Bezeichnung;
      LItem.Value := LKontrolltyp.Kontrolltyp;
      LItem.UserData := LKontrolltyp;
    end;
  end;
  ComboKontrolltyp.Enabled := true;
  if WebApplication.CallBackProcessing then
    ComboKontrolltyp.AjaxReRender;
end;

{ Speichert die eingegebene Kontrolle auf dem Server }
procedure TFrameUngeplanteKontrolle.ButtonSpeichernOnClick(Sender: TObject; AParams: TStringList);
var
  todo: TTodo;
  Rechtsgrundlage: TRechtsgrundlage;
  Kontrolltyp: TKontrolltyp;
  formatedDate: string;

  service: IMe;
  kb: TKontrollbericht;
  factory: IObjectFactory;

begin
  if (idBetrieb = -1) then
  begin
    Alert.Warning('Es muss ein Betrieb ausgewählt werden.');
    Abort;
  end;
  if (ComboKontrolltyp.SelectedItem.Value = '') or
    (ComboKontrollgrund.SelectedItem.Value = '') then
  begin
    Alert.Warning('Es muss ein Kontrolltyp und eine Rechtsgrundlage ausgewählt werden.');
    Abort;
  end;
  if ((not CheckboxGruppe.Checked) and (not CheckboxKontrollorgan.Checked)) then
  begin
    Alert.Warning('Es muss entweder eine Gruppe oder ein Kontrollorgan ausgewählt werden.');
    Exit;
  end;

  service := Usersession.ELKERest.GetMeService;
  try
    Kontrolltyp := TKontrolltyp(ComboKontrolltyp.SelectedItem.UserData);
    Rechtsgrundlage := TRechtsgrundlage(ComboKontrollgrund.SelectedItem.UserData);

    factory := TObjectFactory.Create;

    if IsEditMode and (FKontrolle.Todos.First <> nil) then
    begin
      todo := FKontrolle.Todos.First;
    end
    else
    begin
      todo := factory.Instance.Create<TTodo>;
    end;

    if IsEditMode then
    begin
      kb := FKontrolle;
    end
    else
    begin
      // Spezielle Art des Constructor Aufrufs beachten!
      kb := factory.Instance.Create<TKontrollbericht>;
    end;

    todo.Faellig := jqdDatum.Date;
    DateTimeToString(formatedDate, 'dd.mm.yyyy', todo.Faellig);
    todo.Titel := Kontrolltyp.bkbtyp.Typ + ' ' + iwlBetrieb.Caption + ' ' + formatedDate;
    if CheckboxGruppe.Checked then
    begin
      if ComboGruppe.SelectedIndex = -1 then
      begin
        raise Exception.Create('Es muss eine Gruppe ausgewählt werden.');
      end;
      todo.gruppe := TGruppe(ComboGruppe.SelectedItem.UserData);
      todo.User := nil;
      kb.Kontrollorgan := nil;
    end
    else if CheckboxKontrollorgan.Checked then
    begin
      // Kontrollorgan
      if idKontrollorgan = -1 then
      begin
        raise Exception.Create('Es muss ein Kontrollorgan ausgewählt werden.');
      end;
      todo.gruppe := nil;
      todo.User := Usersession.ELKERest.GetUser(idKontrollorgan);
      // Ungeplante Kontrollen dürfen weder Erfasser noch Kontrollorgan haben
      kb.Kontrollorgan := nil;
      kb.Erfasser := nil;
    end
    else
    begin
      raise Exception.Create('Kein Kontrollorgan/ Gruppe ausgewählt');
    end;

    // Kontrollbericht ausfüllen
    kb.Kontrolltyp := Kontrolltyp;
    kb.Rechtsgrundlage := Rechtsgrundlage;

    if not IsEditMode then
    begin
      kb.Betrieb := factory.Instance.Create<TBetrieb>;
      kb.Betrieb.Id := idBetrieb;
    end;

    // Kontrollbericht abspeichern
    if IsEditMode then
    begin
      Usersession.ELKERest.KontrolleAenderungenSpeichern(kb, todo);
    end
    else
    begin
      service.NeuerUngeplanterKontrollBericht(kb, todo);
    end;

  except
    on E: Exception do
    begin
      Alert.Error('Es gab einen Fehler beim Erstellen des Kontrollberichts.' +
        sLineBreak + E.Message);
      Abort;
    end;
  end;

  if not IsEditMode then
  begin
    ResetFields;
  end;
  DoOnSave;
end;

procedure TFrameUngeplanteKontrolle.RechtsgrundlagenLaden(ABkbTyp: string);
begin
  // Rechtsgrundlagen zur Auswahl stellen
  FreeAndNil(rechtsgrundlagen);
  ComboKontrollgrund.Items.Clear;
  rechtsgrundlagen := Usersession.ELKERest.rechtsgrundlagen(ABkbTyp);
  ComboBoxDefaultEintrag(ComboKontrollgrund, false);
  for var i := 0 to rechtsgrundlagen.Count - 1 do
  begin
    var
    LRechtsgrundlage := rechtsgrundlagen[i];
    var
    LItem := ComboKontrollgrund.Items.Add;
    LItem.Caption := LRechtsgrundlage.Kurzbezeichnung;
    LItem.Value := LRechtsgrundlage.Id.ToString;
    LItem.UserData := LRechtsgrundlage;
  end;
  if WebApplication.CallBackProcessing then
    ComboKontrollgrund.AjaxReRender;
end;

procedure TFrameUngeplanteKontrolle.ResetFields(resetBkbtyp: Boolean = true;
  resetBetrieb: Boolean = true);
begin
  FreeAndNil(FKontrolle);
  IsEditMode := false;
  if resetBkbtyp then
    ComboBKBTyp.SelectedIndex := 0;
  if resetBetrieb then
  begin
    iwlBetrieb.Caption := '';
    iwlBetrieb.Enabled := false;
    idBetrieb := -1;
  end;

  ComboKontrolltyp.SelectedIndex := 0;
  ComboKontrollgrund.SelectedIndex := 0;
  ComboKontrolltyp.Items.Clear;
  ComboKontrollgrund.Items.Clear;
  ComboGruppe.SelectedIndex := 0;
  iwlKontrollorgan.Caption := '';
  jqdDatum.Date := Now;
  CheckboxKontrollorgan.Checked := false;
  CheckboxGruppe.Checked := false;

  ButtonBetriebsuche.Enabled := true;

  ComboGruppe.Enabled := false;
  CheckboxKontrollorgan.Enabled := false;
  ComboKontrolltyp.Enabled := false;
  ComboKontrollgrund.Enabled := false;
  jqbKontrollorgan.Enabled := false;
  jqdDatum.Enabled := false;
  CheckboxGruppe.Visible := true;

  idKontrollorgan := -1;

  ButtonSpeichern.Enabled := false;
end;

procedure TFrameUngeplanteKontrolle.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdBetriebe.Visible := false;
  jqdKontrollorgan.Visible := false;
  CheckObSpeichernErlaubt;
end;

end.
