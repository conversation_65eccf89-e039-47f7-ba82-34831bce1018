﻿unit Frames.GeplanteKontrollen;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQRegion, IWCGJQDatePicker, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQGrid,
  IWCGJQControl, IWCGJQButton, ELKE.Classes.Generated, Modules.Kontrolle,
  IWCGJQGridCustomProvider, IWCGJQGridCollectionProvider, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQDownload,
  IWCGJQFileDownload, System.Generics.Collections, IWCGJQSweetAlert,
  Dialogs.Kontrolldetails, IWCGJQGridDataSetProvider, IWCGJQDialog,
  <PERSON>nt<PERSON>eE<PERSON>ellenFrame, Frames.Base, IWCGJQEdit, Dialogs.KontrolleWeitergeben, IWCGJQLabel;

type
  // ACHTUNG: Wegen einer Schwäche im DFM Designer ist hier ein kleiner Trick notwendig!
  // Um den den Designer aufrufen zu können, diese Zeile aktivieren:
  //TFrameGeplanteKontrollen = class(TFrameBase)
  // Diese Zeile zum Kompilieren aktivieren:
  TFrameGeplanteKontrollen = class(TFrameBase<TDMKontrolle>)
    iwrBot: TIWCGJQRegion;
    ButtonCSVExport: TIWCGJQButton;
    iwrMid: TIWCGJQRegion;
    jqgKontrolluebersicht: TIWCGJQGrid;
    iwrTop: TIWCGJQRegion;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    jqbAbfragen: TIWCGJQButton;
    jqdVon: TIWCGJQDatePicker;
    jqdBis: TIWCGJQDatePicker;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQButton1: TIWCGJQButton;
    IWCGJQButton2: TIWCGJQButton;
    IWCGJQButton3: TIWCGJQButton;
    ButtonHinzufuegen: TIWCGJQButton;
    DownloadGeplant: TIWCGJQFileDownload;
    jqsAlert: TIWCGJQSweetAlert;
    ProviderGeplanteKontrollen: TIWCGJQGridDataSetProvider;
    DialogPlanen: TIWCGJQDialog;
    LabelCount: TIWLabel;
    procedure IWCGJQFrameCreate(Sender: TObject);
    procedure ButtonAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonCSVExportOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonStornierenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonWeitergebenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBearbeitenOnClick(Sender: TObject; AParams: TStringList);
    procedure DialogPlanenJQDialogOptionsButtons0Click(Sender: TObject; AParams: TStringList);
    procedure jqgKontrolluebersichtJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    collection: TKontrollberichte;
    kontrolleErstellen: TKontrolleErstellen;
    procedure ResetSearch;
    procedure DialogPlanenSchliessen;

  public
    constructor Create(AOwner: TComponent; ADataModule: TDMKontrolle); override;
    destructor Destroy; override;
    procedure Search;
  end;

implementation

{$R *.dfm}


uses ServerController, Utility, Dialogs.Stornierungsgrund, Dialogs.GeplanteKontrolleBearbeiten, JQ.Helpers.Grid,
  ELKE.Classes.RESTError, Dialogs.GeplanteKontrolleEditNeu;

constructor TFrameGeplanteKontrollen.Create(AOwner: TComponent; ADataModule: TDMKontrolle);
begin
  inherited;
  ResetSearch;
  kontrolleErstellen := TKontrolleErstellen.Create(Self,
    procedure
    begin
      DialogPlanenSchliessen;
    end);
  kontrolleErstellen.Parent := DialogPlanen;
  jqgKontrolluebersicht.SetupDefaults(ProviderGeplanteKontrollen);
end;

procedure TFrameGeplanteKontrollen.IWCGJQFrameCreate(Sender: TObject);
begin
  inherited;
    //dialoge vorab erstellen
  TDialogGeplanteKontrolleBearbeiten.Create(self, -1);
  TDialogKontrolleWeitergeben.Create(self);
  TDialogStornierungsgrund.Create(self);
  TDialogGeplanteKontrolleEditNeu.Create(self, -1);
end;

procedure TFrameGeplanteKontrollen.jqgKontrolluebersichtJQGridOptionsLoadComplete(Sender: TObject;
  AParams: TStringList);
begin
  inherited;
  LabelCount.Caption := jqgKontrolluebersicht.RowCount.ToString + ' Ergebnisse gefunden.';
end;

destructor TFrameGeplanteKontrollen.Destroy;
begin
  collection.Free;
  inherited;
end;

procedure TFrameGeplanteKontrollen.ResetSearch;
begin
  jqdVon.Date := IncMonth(Now, -12);
  jqdBis.Date := IncMonth(Now, 1);
end;

procedure TFrameGeplanteKontrollen.ButtonAbfragenOnClick(Sender: TObject;
AParams: TStringList);
begin
  Search;
end;

procedure TFrameGeplanteKontrollen.ButtonBearbeitenOnClick(Sender: TObject; AParams: TStringList);
var
  IDKontrollbericht: integer;
  LDialog: TDialogGeplanteKontrolleBearbeiten;
  //LDialog: TDialogGeplanteKontrolleEditNeu;
begin
  inherited;
  if not DM.MoveTableToSelected(jqgKontrolluebersicht, DM.MTGeplanteKontrollen) then
  begin
    jqsAlert.Info('Es muss eine Kontrolle ausgewählt sein');
   Abort;
  end;

  IDKontrollbericht := DM.MTGeplanteKontrollen.FieldByName('ID').AsInteger;
  LDialog := TDialogGeplanteKontrolleBearbeiten.Create(self, IDKontrollbericht);
  //LDialog := TDialogGeplanteKontrolleEditNeu.Create(self, IDKontrollbericht);
  LDialog.OnOk := procedure
    begin
      try
        LDialog.Save;
        LDialog.Close;
      except
        on E: Exception do
        begin
          jqsAlert.Error('Es gab einen Fehler beim Bearbeiten der Kontrolle.' + sLineBreak + E.Message);
          Exit;
        end
      end;
      jqsAlert.Info('Kontrolle erfolgreich bearbeitet.');
      Search;
    end;
  LDialog.Show(false);
end;

procedure TFrameGeplanteKontrollen.Search;
begin
  DM.GetGeplanteKontrollen(jqdVon.Date, jqdBis.Date);
  // jqgKontrolluebersicht.JQGridOptions.ReloadGrid(true);
end;

procedure TFrameGeplanteKontrollen.ButtonCSVExportOnClick(Sender: TObject;
AParams: TStringList);
var
  filename: string;
begin
  try
    filename := DM.ExportAsCSV(DM.MTGeplanteKontrollen, jqsAlert);
    DownloadGeplant.DownloadFileName(filename);
  except
    on E: EAbort do
      raise;
    on E: Exception do
    begin
      jqsAlert.Error('Die Kontrollberichte konnten nicht exportiert werden.' + sLineBreak + E.Message);
    end;
  end;
end;

procedure TFrameGeplanteKontrollen.ButtonHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
var
  LDialog: TDialogGeplanteKontrolleEditNeu;
begin
  LDialog := TDialogGeplanteKontrolleEditNeu.Create(self);
  LDialog.OnOk := procedure
    begin
      try
        LDialog.Save;
        LDialog.Close;
      except
        on E: Exception do
        begin
          jqsAlert.Error('Es gab einen Fehler beim Bearbeiten der Kontrolle.' + sLineBreak + E.Message);
          Exit;
        end
      end;
      jqsAlert.Info('Kontrolle erfolgreich erstellt.');
      Search;
    end;
  LDialog.Show(false);
end;

procedure TFrameGeplanteKontrollen.ButtonStornierenOnClick(Sender: TObject; AParams: TStringList);
var
  LDialog: TDialogStornierungsgrund;
  LGUID: TGuid;
begin
  inherited;
  if not DM.MoveTableToSelected(jqgKontrolluebersicht, DM.MTGeplanteKontrollen) then
  begin
    jqsAlert.Info('Es muss eine Kontrolle ausgewählt sein');
    Exit;
  end;
  LGUID := DM.MTGeplanteKontrollenGuid.AsGuid;

  LDialog := TDialogStornierungsgrund.Create(self);
  LDialog.OnOk :=
      procedure
    begin
      try
        DM.KontrolleStornieren(LGUID, LDialog.Stornierungsgrund);
      except
        on E: Exception do
        begin
          jqsAlert.Error('Es gab einen Fehler beim Stornieren der Kontrolle.' + sLineBreak + E.Message);
          Exit;
        end
      end;
      jqsAlert.Info('Kontrolle erfolgreich storniert.');
      Search;
    end;
  LDialog.Show(true);
end;

procedure TFrameGeplanteKontrollen.ButtonWeitergebenOnClick(Sender: TObject; AParams: TStringList);
var
  LDialog: TDialogKontrolleWeitergeben;
  LIDKontrollbericht, LIDPerson, LIDGruppe: integer;
begin
  inherited;
  if not DM.MoveTableToSelected(jqgKontrolluebersicht, DM.MTGeplanteKontrollen) then
  begin
    jqsAlert.Info('Es muss eine Kontrolle ausgewählt sein');
    Exit;
  end;

  LIDKontrollbericht := DM.MTGeplanteKontrollen.FieldByName('ID').AsInteger;
  LDialog := TDialogKontrolleWeitergeben.Create(self);
  LDialog.OnOk := procedure
    begin
      try
        LIDPerson := LDialog.IDPerson;
        LIDGruppe := LDialog.IDGruppe;
        DM.KontrolleWeitergeben(LIDKontrollbericht, LIDPerson, LIDGruppe);
      except
        on E: Exception do
        begin
          jqsAlert.Error('Es gab einen Fehler beim Weitergeben der Kontrolle.' + sLineBreak + E.Message);
          Exit;
        end
      end;
      jqsAlert.Info('Kontrolle erfolgreich weitergegeben.');
      Search;
    end;
  LDialog.Show(true);
end;

procedure TFrameGeplanteKontrollen.DialogPlanenJQDialogOptionsButtons0Click(Sender: TObject; AParams: TStringList);
begin
  inherited;
  DialogPlanenSchliessen;
end;

procedure TFrameGeplanteKontrollen.DialogPlanenSchliessen;
begin
  DialogPlanen.Visible := false;
  Search;
end;

end.
