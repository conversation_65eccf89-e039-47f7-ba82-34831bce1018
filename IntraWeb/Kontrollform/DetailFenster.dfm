﻿inherited DetailFrm: TDetailFrm
  Width = 1400
  Height = 822
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1400
    TabOrder = 12
    inherited ImageLogo: TIWImageFile
      Left = 1117
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 908
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 13
    end
  end
  object iwrLeft: TIWCGJQRegion [1]
    Left = 0
    Top = 50
    Width = 22
    Height = 747
    HorzScrollBar.Style = ssFlat
    VertScrollBar.Color = clWhite
    VertScrollBar.ParentColor = False
    RenderInvisibleControls = True
    TabOrder = 14
    Version = '1.0'
    Align = alLeft
    Color = clWebSILVER
  end
  object iwrRight: TIWCGJQRegion [2]
    Left = 1380
    Top = 50
    Width = 20
    Height = 747
    RenderInvisibleControls = True
    TabOrder = 15
    Version = '1.0'
    Align = alRight
    Color = clWebSILVER
  end
  object iwrMid: TIWCGJQRegion [3]
    Left = 22
    Top = 50
    Width = 1358
    Height = 747
    RenderInvisibleControls = True
    TabOrder = 16
    Version = '1.0'
    Align = alClient
    object iwrInfo: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1358
      Height = 104
      RenderInvisibleControls = True
      TabOrder = 17
      Version = '1.0'
      Align = alTop
      object jqgKontrolldetails: TIWCGJQGrid
        Left = 0
        Top = 30
        Width = 1358
        Height = 74
        TabOrder = 1
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkb'
            Name = 'Bkb'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bkb'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bkbtyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Kontrolltyp'
            Name = 'Kontrolltyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrolltyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Datum'
            Name = 'Datum'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Datum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Erfasser'
            Name = 'Erfasser'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Erfasser'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Erfasser_Nachname'
            Name = 'Erfasser_Nachname'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Erfasser_Nachname'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Kontrollorgan'
            Name = 'Kontrollorgan'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollorgan'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Ref_bkb'
            Name = 'Ref_bkb'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ref_bkb'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Angemeldet_Um'
            Name = 'Angemeldet_Um'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Angemeldet_Um'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Probenziehung'
            Name = 'Probenziehung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Probenziehung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebsname'
            Name = 'Betriebsname'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsname'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y H:i:s'
            Idx = 'Startzeit'
            Name = 'Startzeit'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Startzeit'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y H:i:s'
            Idx = 'Endezeit'
            Name = 'Endezeit'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Endezeit'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'betriebstyp'
            Name = 'betriebstyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'betriebstyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Rechtsgrundlage'
            Name = 'Rechtsgrundlage'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Rechtsgrundlage'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Status'
            Name = 'Status'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Status'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Kurzbemerkung'
            Name = 'Kurzbemerkung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kurzbemerkung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Verweigerunggrund'
            Name = 'Verweigerunggrund'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Verweigerunggrund'
          end>
        JQGridOptions.Height = 47
        JQGridOptions.PgButtons = False
        JQGridOptions.PgInput = False
        JQGridOptions.RowNum = 3
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1356
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.PagerVisible = False
        JQGridNav.Active = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpKontrollDetails
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion5: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1358
        Height = 30
        RenderInvisibleControls = True
        TabOrder = 18
        Version = '1.0'
        Align = alTop
        object IWLabel4: TIWCGJQLabel
          Left = 0
          Top = 0
          Width = 81
          Height = 30
          Align = alLeft
          Font.Color = clNone
          Font.Size = 11
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Allgemein:'
        end
        object IWRegion6: TIWCGJQRegion
          Left = 812
          Top = 0
          Width = 546
          Height = 30
          RenderInvisibleControls = True
          TabOrder = 19
          Version = '1.0'
          Align = alRight
          object jqbKontrollberichtAnzeigen: TIWCGJQButton
            Left = 312
            Top = 5
            Width = 220
            Height = 21
            TabOrder = 7
            Version = '1.0'
            JQButtonOptions.Label_ = 'Kontrollbericht anzeigen'
            JQButtonOptions.OnClick.OnEvent = JqbKontrollberichtAnzeigenOnClick
          end
        end
      end
    end
    object iwrProben: TIWCGJQRegion
      Left = 0
      Top = 104
      Width = 1358
      Height = 216
      RenderInvisibleControls = True
      TabOrder = 20
      Version = '1.0'
      Align = alTop
      object jqgProben: TIWCGJQGrid
        Left = 0
        Top = 30
        Width = 1358
        Height = 186
        TabOrder = 2
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Probenbkb'
            Name = 'Probenbkb'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Probenbkb'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Probenart'
            Name = 'Probenart'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Probenart'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bemerkung'
            Name = 'Bemerkung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bemerkung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Probenbezeichnung'
            Name = 'Probenbezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Probenbezeichnung'
            Position = 25
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'DATUM'
            Name = 'DATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Datum'
            Position = 3
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VORG_MENGE'
            Name = 'VORG_MENGE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Vorgefundene Menge'
            Position = 4
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BESCHAFFENHEIT'
            Name = 'BESCHAFFENHEIT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beschaffenheit'
            Position = 5
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FUTTERTYP'
            Name = 'FUTTERTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Futtertyp'
            Position = 6
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERWENDUNGSZWECK'
            Name = 'VERWENDUNGSZWECK'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Verwendungszweck'
            Position = 7
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TIER_ART_LISA'
            Name = 'TIER_ART_LISA'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tierart Lisa'
            Position = 8
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TIER_KATEGORIE'
            Name = 'TIER_KATEGORIE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tier Kategorie'
            Position = 9
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEIMISCHRATE'
            Name = 'BEIMISCHRATE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beimischrate'
            Position = 10
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERPACKUNG'
            Name = 'VERPACKUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Verpackung'
            Position = 11
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERSCHLUSS'
            Name = 'VERSCHLUSS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Verschluss'
            Position = 12
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERSIEGELT'
            Name = 'VERSIEGELT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Versiegelt'
            Position = 13
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'HERK_ZUKAUF'
            Name = 'HERK_ZUKAUF'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Herk_Zukauf'
            Position = 14
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'UNTERSUCHUNGSAUFTRAG'
            Name = 'UNTERSUCHUNGSAUFTRAG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Untersuchungsauftrag'
            Position = 15
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STATUS'
            Name = 'STATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Status'
            Position = 16
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERDACHT'
            Name = 'VERDACHT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Verdacht'
            Position = 17
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEGENPROBE_BELASSEN'
            Name = 'GEGENPROBE_BELASSEN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gegenprobe belassen'
            Position = 18
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'EXPORTNAME'
            Name = 'EXPORTNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Exportname'
            Position = 19
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'EXPORTTIME'
            Name = 'EXPORTTIME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Exporttime'
            Position = 20
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AGESAUFTRAGSNUMMER'
            Name = 'AGESAUFTRAGSNUMMER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Agesauftragsnummer'
            Position = 21
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AGESPROBENNUMMER'
            Name = 'AGESPROBENNUMMER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Agesprobennummer'
            Position = 22
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AGES_AUFTRAGSSTATUS'
            Name = 'AGES_AUFTRAGSSTATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ages Auftragsstatus'
            Position = 23
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AGES_PROBENSTATUS'
            Name = 'AGES_PROBENSTATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ages Probenstatus'
            Position = 24
          end>
        JQGridOptions.Height = 159
        JQGridOptions.PgButtons = False
        JQGridOptions.PgInput = False
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1356
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.PagerVisible = False
        JQGridNav.Active = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpProben
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1358
        Height = 30
        RenderInvisibleControls = True
        TabOrder = 21
        Version = '1.0'
        Align = alTop
        object IWLabel2: TIWCGJQLabel
          Left = 0
          Top = 0
          Width = 81
          Height = 30
          Align = alLeft
          Font.Color = clNone
          Font.Size = 11
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Proben:'
        end
        object IWRegion2: TIWCGJQRegion
          Left = 812
          Top = 0
          Width = 546
          Height = 30
          RenderInvisibleControls = True
          TabOrder = 22
          Version = '1.0'
          Align = alRight
          object jqbProbenPdf: TIWCGJQButton
            Left = 312
            Top = 5
            Width = 220
            Height = 21
            TabOrder = 3
            Version = '1.0'
            JQButtonOptions.Label_ = 'AGES Proben-PDF anzeigen'
            JQButtonOptions.OnClick.OnEvent = JqbProbenPdfOnClick
          end
          object jqbProbenbegleitschein: TIWCGJQButton
            Left = 64
            Top = 6
            Width = 235
            Height = 21
            TabOrder = 5
            Version = '1.0'
            JQButtonOptions.Label_ = 'Probenbegleitschein anzeigen'
            JQButtonOptions.OnClick.OnEvent = JqbProbenbegleitscheinOnClick
          end
        end
      end
    end
    object iwrMaengel: TIWCGJQRegion
      Left = 0
      Top = 320
      Width = 1358
      Height = 192
      RenderInvisibleControls = True
      TabOrder = 23
      Version = '1.0'
      Align = alTop
      object IWRegion4: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1358
        Height = 30
        RenderInvisibleControls = True
        TabOrder = 24
        Version = '1.0'
        Align = alTop
        object IWLabel3: TIWCGJQLabel
          Left = 0
          Top = 0
          Width = 81
          Height = 30
          Align = alLeft
          Font.Color = clNone
          Font.Size = 11
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'M'#228'ngel:'
        end
        object IWRegion3: TIWCGJQRegion
          Left = 812
          Top = 0
          Width = 546
          Height = 30
          RenderInvisibleControls = True
          TabOrder = 25
          Version = '1.0'
          Align = alRight
          object jqbMangelAbschließen: TIWCGJQButton
            Left = 384
            Top = 5
            Width = 150
            Height = 21
            TabOrder = 6
            Version = '1.0'
            JQButtonOptions.Label_ = 'Mangel abschlie'#223'en'
            JQButtonOptions.OnClick.OnEvent = JqbMangelAbschließenOnClick
          end
          object jqbFristVerlaengern: TIWCGJQButton
            Left = 216
            Top = 4
            Width = 150
            Height = 21
            TabOrder = 10
            Version = '1.0'
            JQButtonOptions.Label_ = 'Frist verl'#228'ngern'
            JQButtonOptions.OnClick.OnEvent = JqbFristVerlaengernOnClick
          end
        end
        object jqbMaengelAktualisieren: TIWCGJQButton
          Left = 72
          Top = 4
          Width = 100
          Height = 21
          TabOrder = 9
          Version = '1.0'
          JQButtonOptions.Label_ = 'Aktualisieren'
          JQButtonOptions.OnClick.OnEvent = JqbMaengelAktualisierenOnClick
        end
      end
      object jqgMaengel: TIWCGJQGrid
        Left = 0
        Top = 30
        Width = 1358
        Height = 162
        TabOrder = 4
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MangeltypBez'
            Name = 'MangeltypBez'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Mangeltyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MassnahmeBez'
            Name = 'MassnahmeBez'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ma'#223'nahme'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Status'
            Name = 'Status'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Status'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Text'
            Name = 'Text'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Text'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Frist'
            Name = 'Frist'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Frist'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Beseitigt_am'
            Name = 'Beseitigt_am'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beseitigt am'
          end>
        JQGridOptions.Height = 135
        JQGridOptions.PgButtons = False
        JQGridOptions.PgInput = False
        JQGridOptions.RowNum = 100
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1356
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.PagerVisible = False
        JQGridNav.Active = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpMaengel
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 797
    Width = 1400
    TabOrder = 0
  end
  object jqdFristVerlaengern: TIWCGJQDialog [5]
    Left = 448
    Top = 240
    Width = 313
    Height = 150
    Visible = False
    TabOrder = 8
    Version = '1.0'
    Align = alNone
    ZIndex = 5000
    JQDialogOptions.AutoOpen = False
    JQDialogOptions.Height = 150
    JQDialogOptions.Modal = True
    JQDialogOptions.Width = 313
    JQDialogOptions.zIndex = 5000
    object IWLabel1: TIWCGJQLabel
      Left = 24
      Top = 24
      Width = 33
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel1'
      Caption = 'Frist:'
    end
    object jqdFrist: TIWCGJQDatePicker
      Left = 72
      Top = 24
      Width = 200
      Height = 21
      TabOrder = 11
      Version = '1.0'
      ZIndex = 5001
      Caption = ''
      JQDatePickerOptions.CurrentText = 'Heute'
      JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
      JQDatePickerOptions.NextText = 'N'#228'chster'
      JQDatePickerOptions.PrevText = 'Vorherhiger'
      JQDatePickerOptions.Regional = dporGerman
    end
  end
  object quKontrollDetails: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT k.id, k.Bkb, bt.Bezeichnung AS "Bkbtyp", kt.Bezeichnung A' +
        'S "Kontrolltyp", k.Datum, k.Kurzbemerkung, k.Tstamp_Insert,'
      
        '       p.Vorname AS "Erfasser_Vorname", p.Nachname AS "Erfasser_' +
        'Nachname", CONCAT(p.Vorname, '#39' '#39', p.Nachname) AS "Erfasser",'
      
        '       pk.Vorname AS "Kontrollorgan_Vorname", pk.Nachname AS "Ko' +
        'ntrollorgan_Nachname", CONCAT(pk.Vorname, '#39' '#39', pk.Nachname) AS "' +
        'Kontrollorgan",'
      
        '       k.Probenziehung, b.Name AS "Betriebsname", k.Startzeit, k' +
        '.Endezeit, k.Verweigerunggrund, '
      
        '       k.Angemeldet_Um, r.Kurzbezeichnung AS "Rechtsgrundlage", ' +
        'k.Status, k.betriebstyp, k.lastchange, k.Ref_bkb, k.Id_Betrieb A' +
        'S "BetriebID", '
      '       pk.Id AS "KontrollorganID", k.guid_dokument'
      'FROM   Bewegungsdaten.Kontrollbericht k '
      '        LEFT JOIN Stammdaten.Bkbtypen bt ON k.bkbtyp = bt.bkbtyp'
      
        '        LEFT JOIN Stammdaten.Kontrolltypen kt ON k.bkbtyp = kt.b' +
        'kbtyp AND k.kontrolltyp = kt.kontrolltyp'
      
        '        LEFT JOIN Stammdaten.Personen p ON k.erfasser_pskey = p.' +
        'pskey'
      
        '        LEFT JOIN Stammdaten.Personen pk ON k.kontrollorgan_pske' +
        'y = pk.pskey'
      '        LEFT JOIN Stammdaten.Betriebe b ON k.ID_Betrieb = b.Id'
      
        '        LEFT JOIN Stammdaten.Rechtsgrundlagen r ON k.id_rechtsgr' +
        'undlage = r.id'
      'WHERE  k.id = :id;')
    Left = 856
    Top = 8
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object quKontrollDetailsid: TFDAutoIncField
      FieldName = 'id'
      Origin = 'id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quKontrollDetailsBkb: TStringField
      FieldName = 'Bkb'
      Origin = 'Bkb'
      Required = True
      Size = 26
    end
    object quKontrollDetailsBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Origin = 'Bkbtyp'
      Size = 50
    end
    object quKontrollDetailsKontrolltyp: TStringField
      FieldName = 'Kontrolltyp'
      Origin = 'Kontrolltyp'
      Size = 80
    end
    object quKontrollDetailsDatum: TDateField
      FieldName = 'Datum'
      Origin = 'Datum'
    end
    object quKontrollDetailsKurzbemerkung: TWideMemoField
      FieldName = 'Kurzbemerkung'
      Origin = 'Kurzbemerkung'
      BlobType = ftWideMemo
      Size = **********
    end
    object quKontrollDetailsTstamp_Insert: TSQLTimeStampField
      FieldName = 'Tstamp_Insert'
      Origin = 'Tstamp_Insert'
    end
    object quKontrollDetailsErfasser_Vorname: TStringField
      FieldName = 'Erfasser_Vorname'
      Origin = 'Erfasser_Vorname'
      Size = 60
    end
    object quKontrollDetailsErfasser_Nachname: TStringField
      FieldName = 'Erfasser_Nachname'
      Origin = 'Erfasser_Nachname'
      Size = 60
    end
    object quKontrollDetailsErfasser: TStringField
      FieldName = 'Erfasser'
      Origin = 'Erfasser'
      ReadOnly = True
      Required = True
      Size = 121
    end
    object quKontrollDetailsKontrollorgan_Vorname: TStringField
      FieldName = 'Kontrollorgan_Vorname'
      Origin = 'Kontrollorgan_Vorname'
      Size = 60
    end
    object quKontrollDetailsKontrollorgan_Nachname: TStringField
      FieldName = 'Kontrollorgan_Nachname'
      Origin = 'Kontrollorgan_Nachname'
      Size = 60
    end
    object quKontrollDetailsKontrollorgan: TStringField
      FieldName = 'Kontrollorgan'
      Origin = 'Kontrollorgan'
      ReadOnly = True
      Required = True
      Size = 121
    end
    object quKontrollDetailsProbenziehung: TBooleanField
      FieldName = 'Probenziehung'
      Origin = 'Probenziehung'
      Required = True
    end
    object quKontrollDetailsBetriebsname: TStringField
      FieldName = 'Betriebsname'
      Origin = 'Betriebsname'
      Size = 255
    end
    object quKontrollDetailsStartzeit: TSQLTimeStampField
      FieldName = 'Startzeit'
      Origin = 'Startzeit'
    end
    object quKontrollDetailsEndezeit: TSQLTimeStampField
      FieldName = 'Endezeit'
      Origin = 'Endezeit'
    end
    object quKontrollDetailsVerweigerunggrund: TStringField
      FieldName = 'Verweigerunggrund'
      Origin = 'Verweigerunggrund'
      Size = 150
    end
    object quKontrollDetailsAngemeldet_Um: TSQLTimeStampField
      FieldName = 'Angemeldet_Um'
      Origin = 'Angemeldet_Um'
    end
    object quKontrollDetailsRechtsgrundlage: TStringField
      FieldName = 'Rechtsgrundlage'
      Origin = 'Rechtsgrundlage'
      Size = 50
    end
    object quKontrollDetailsStatus: TStringField
      FieldName = 'Status'
      Origin = 'Status'
      FixedChar = True
      Size = 1
    end
    object quKontrollDetailsbetriebstyp: TStringField
      FieldName = 'betriebstyp'
      Origin = 'betriebstyp'
      Required = True
      Size = 2
    end
    object quKontrollDetailslastchange: TSQLTimeStampField
      FieldName = 'lastchange'
      Origin = 'lastchange'
    end
    object quKontrollDetailsRef_bkb: TStringField
      FieldName = 'Ref_bkb'
      Origin = 'Ref_bkb'
      Size = 26
    end
    object quKontrollDetailsBetriebID: TIntegerField
      FieldName = 'BetriebID'
      Origin = 'BetriebID'
      Required = True
    end
    object quKontrollDetailsKontrollorganID: TFDAutoIncField
      FieldName = 'KontrollorganID'
      Origin = 'KontrollorganID'
      ReadOnly = True
    end
    object quKontrollDetailsguid_dokument: TGuidField
      FieldName = 'guid_dokument'
      Origin = 'guid_dokument'
      Size = 38
    end
  end
  object jqdpKontrollDetails: TIWCGJQGridDataSetProvider
    DataSet = quKontrollDetails
    KeyFields = 'id'
    Left = 960
    Top = 8
  end
  object quProben: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT Id, Probenbkb, Probenart, Bemerkung, Datum, Vorg_Menge, B' +
        'eschaffenheit, Futtertyp, Verwendungszweck, Tier_ARt_Lisa, Tier_' +
        'Kategorie, Beimischrate,'
      
        '       Verpackung, Verschluss, Versiegelt, Herk_Zukauf, Untersuc' +
        'hungsauftrag, STatus, Verdacht, Gegenprobe_Belassen, Exportname,' +
        ' Exporttime,'
      
        '       Agesauftragsnummer, agesprobennummer, Ages_Auftragsstatus' +
        ', Ages_Probenstatus, Probenbezeichnung'
      'FROM   Bewegungsdaten.KB_Proben'
      'WHERE  ID_Kontrollbericht = :id;')
    Left = 1048
    Top = 8
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object quProbenId: TFDAutoIncField
      FieldName = 'Id'
      Origin = 'Id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quProbenProbenbkb: TStringField
      FieldName = 'Probenbkb'
      Origin = 'Probenbkb'
      Size = 26
    end
    object quProbenProbenart: TStringField
      FieldName = 'Probenart'
      Origin = 'Probenart'
      Size = 50
    end
    object quProbenBemerkung: TStringField
      FieldName = 'Bemerkung'
      Origin = 'Bemerkung'
      Size = 250
    end
    object quProbenDatum: TDateField
      FieldName = 'Datum'
      Origin = 'Datum'
    end
    object quProbenVorg_Menge: TStringField
      FieldName = 'Vorg_Menge'
      Origin = 'Vorg_Menge'
      Size = 50
    end
    object quProbenBeschaffenheit: TStringField
      FieldName = 'Beschaffenheit'
      Origin = 'Beschaffenheit'
      Size = 50
    end
    object quProbenFuttertyp: TStringField
      FieldName = 'Futtertyp'
      Origin = 'Futtertyp'
      Size = 50
    end
    object quProbenVerwendungszweck: TStringField
      FieldName = 'Verwendungszweck'
      Origin = 'Verwendungszweck'
      Size = 80
    end
    object quProbenTier_ARt_Lisa: TStringField
      FieldName = 'Tier_ARt_Lisa'
      Origin = 'Tier_ARt_Lisa'
      Size = 50
    end
    object quProbenTier_Kategorie: TStringField
      FieldName = 'Tier_Kategorie'
      Origin = 'Tier_Kategorie'
      Size = 50
    end
    object quProbenBeimischrate: TBCDField
      FieldName = 'Beimischrate'
      Origin = 'Beimischrate'
      Precision = 18
    end
    object quProbenVerpackung: TStringField
      FieldName = 'Verpackung'
      Origin = 'Verpackung'
      Size = 50
    end
    object quProbenVerschluss: TStringField
      FieldName = 'Verschluss'
      Origin = 'Verschluss'
      Size = 50
    end
    object quProbenVersiegelt: TStringField
      FieldName = 'Versiegelt'
      Origin = 'Versiegelt'
      Size = 5
    end
    object quProbenHerk_Zukauf: TStringField
      FieldName = 'Herk_Zukauf'
      Origin = 'Herk_Zukauf'
      Size = 250
    end
    object quProbenUntersuchungsauftrag: TStringField
      FieldName = 'Untersuchungsauftrag'
      Origin = 'Untersuchungsauftrag'
      Size = 250
    end
    object quProbenSTatus: TStringField
      FieldName = 'STatus'
      Origin = 'STatus'
      Required = True
      FixedChar = True
      Size = 1
    end
    object quProbenVerdacht: TStringField
      FieldName = 'Verdacht'
      Origin = 'Verdacht'
      Size = 250
    end
    object quProbenGegenprobe_Belassen: TBooleanField
      FieldName = 'Gegenprobe_Belassen'
      Origin = 'Gegenprobe_Belassen'
      Required = True
    end
    object quProbenExportname: TStringField
      FieldName = 'Exportname'
      Origin = 'Exportname'
      Size = 50
    end
    object quProbenExporttime: TSQLTimeStampField
      FieldName = 'Exporttime'
      Origin = 'Exporttime'
    end
    object quProbenAgesauftragsnummer: TStringField
      FieldName = 'Agesauftragsnummer'
      Origin = 'Agesauftragsnummer'
      Size = 50
    end
    object quProbenagesprobennummer: TStringField
      FieldName = 'agesprobennummer'
      Origin = 'agesprobennummer'
      Size = 50
    end
    object quProbenAges_Auftragsstatus: TStringField
      FieldName = 'Ages_Auftragsstatus'
      Origin = 'Ages_Auftragsstatus'
      Size = 50
    end
    object quProbenAges_Probenstatus: TStringField
      FieldName = 'Ages_Probenstatus'
      Origin = 'Ages_Probenstatus'
      Size = 50
    end
    object quProbenProbenbezeichnung: TStringField
      FieldName = 'Probenbezeichnung'
      Origin = 'Probenbezeichnung'
      Required = True
      Size = 255
    end
  end
  object jqdpProben: TIWCGJQGridDataSetProvider
    DataSet = quProben
    KeyFields = 'id'
    Left = 1128
    Top = 8
  end
  object quProbenPdf: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Ergebnis_PDF, Probenbkb'
      'FROM   Bewegungsdaten.KB_Proben'
      'WHERE  ID = :id;')
    Left = 1192
    Top = 8
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object jqdDownload: TIWCGJQFileDownload
    Version = '1.0'
    Left = 776
    Top = 12
  end
  object jqsAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 696
    Top = 8
  end
  object jqdpMaengel: TIWCGJQGridDataSetProvider
    DataSet = quMaengel
    KeyFields = 'ID'
    Left = 1327
    Top = 11
  end
  object quMaengel: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT DISTINCT m.ID, m.GUID, m.ID_Mangeltyp, m.ID_Massnahme, m.' +
        'Frist, m.Text, m.Beseitigt_am, m.Status, mt.Bezeichnung AS "Mang' +
        'eltypBez",'
      '       ma.Langtext AS "MassnahmeBez", ma.iD, m.Frist'
      'FROM   Bewegungsdaten.Maengel m'
      
        '        INNER JOIN Bewegungsdaten.Bewertete_Fragen bf ON m.ID = ' +
        'bf.ID_Mangel'
      
        '        LEFT OUTER JOIN Stammdaten.Mangeltypen mt ON m.ID_Mangel' +
        'typ = mt.ID'
      
        '        LEFT OUTER JOIN Stammdaten.Massnahmen ma ON m.ID_Massnah' +
        'me = ma.ID'
      'WHERE  bf.ID_Bericht = :kontrollbericht'
      'ORDER BY ma.ID ASC, m.FRIST ASC;')
    Left = 1264
    Top = 8
    ParamData = <
      item
        Name = 'KONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = 574
      end>
    object quMaengelID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quMaengelGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object quMaengelID_Mangeltyp: TIntegerField
      FieldName = 'ID_Mangeltyp'
      Origin = 'ID_Mangeltyp'
      Required = True
    end
    object quMaengelID_Massnahme: TIntegerField
      FieldName = 'ID_Massnahme'
      Origin = 'ID_Massnahme'
      Required = True
    end
    object quMaengelFrist: TDateField
      FieldName = 'Frist'
      Origin = 'Frist'
    end
    object quMaengelText: TMemoField
      FieldName = 'Text'
      Origin = 'Text'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object quMaengelBeseitigt_am: TDateField
      FieldName = 'Beseitigt_am'
      Origin = 'Beseitigt_am'
    end
    object quMaengelStatus: TStringField
      FieldName = 'Status'
      Origin = 'Status'
      ReadOnly = True
      Size = 1
    end
    object quMaengelMangeltypBez: TStringField
      FieldName = 'MangeltypBez'
      Origin = 'MangeltypBez'
      Size = 200
    end
    object quMaengelMassnahmeBez: TMemoField
      FieldName = 'MassnahmeBez'
      Origin = 'MassnahmeBez'
      BlobType = ftMemo
      Size = **********
    end
    object quMaengeliD_1: TFDAutoIncField
      FieldName = 'iD_1'
      Origin = 'iD'
      ReadOnly = True
    end
    object quMaengelFrist_1: TDateField
      FieldName = 'Frist_1'
      Origin = 'Frist'
    end
  end
  object quMangelAbschliessen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Maengel'
      'SET    Beseitigt_Am = :beseitigt_am'
      'WHERE  Id = :id;')
    Left = 1310
    Top = 746
    ParamData = <
      item
        Name = 'BESEITIGT_AM'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quExistierenBewFragen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT count(Id) AS "Anzahl"'
      'FROM   Bewegungsdaten.Bewertete_Fragen'
      'WHERE  ID_Bericht = :id;')
    Left = 46
    Top = 514
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFristVerlaengern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Maengel'
      'SET    Frist = :frist'
      'WHERE  ID = :id;')
    Left = 1206
    Top = 746
    ParamData = <
      item
        Name = 'FRIST'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
