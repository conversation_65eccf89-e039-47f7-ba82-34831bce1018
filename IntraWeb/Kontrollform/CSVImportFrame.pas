﻿unit CSVImportFrame;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQFileUpload, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQComboBox,
  ELKE.Classes.Generated, System.Generics.Collections, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQEdit, IWCGJQRegion, IWCGJQLabel, IWCompCheckbox, IWCGJQCheckBox;

type
  TKontrollzeile = class(TObject)
  private
    Gruppe: TGruppe;
    Gemeindenummer: string;
    LFBIS: string;
    IDBetrieb: integer;
    CC: boolean;
    Kontrolltyp: TKontrolltyp;
    Rechtsgrundlage: TRechtsgrundlage;
    Faellig: TDateTime;
    Probenbezeichnung: string;
    Probenbeschreibung: string;
    Zeilennummer: integer;
  end;

type
  ECSVInputError = class(Exception);
  EKontrolleErstellenError = class(Exception);

type
  TCSVImport = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    jquNoeKontrollplan: TIWCGJQFileUpload;
    quExistiertBetrieb: TFDQuery;
    quBetriebErstellen: TFDQuery;
    quNeueAdresse: TFDQuery;
    quFindeGemeinde: TFDQuery;
    Alert: TIWCGJQSweetAlert;
    QImportMapping: TFDQuery;
    QImportMappingID: TGuidField;
    QImportMappingPROBENBEZEICHNUNG: TStringField;
    QImportMappingBKBTYP: TStringField;
    QImportMappingKONTROLLTYP: TStringField;
    QImportMappingBLDCODE: TSmallintField;
    QImportMappingID_RECHTSGRUNDLAGEN: TIntegerField;
    CheckCCImport: TIWCGJQCheckBoxEx;
    procedure jquNoeKontrollplanGetFileName(Sender: TObject; var APath, AFileName:
      string);
    procedure JquNoeKontrollplanOnUploadComplete(Sender: TObject;
      AParams: TStringList);
  private
    { Private declarations }
    FGruppen: TList<TGruppe>;
    FKontrolltypen: TList<TKontrolltyp>;
    function ExistiertBetrieb(regnr: string): integer;
    function FindeGruppe(Okz: string): TGruppe;
    procedure BezeichnungsMapping(Kontrollzeile: TKontrollzeile; Probenbezeichnung: string);
    procedure KontrollenErstellen(Zeilen: TObjectList<TKontrollzeile>);
    procedure ImportMappingAbfragen;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation

uses ServerController, System.IOUtils, System.Types, System.StrUtils, dmmain,
  Utility, ELKE.Services.Me.Intf, DX.Classes.ObjectFactory, ELKE.Classes.RESTError;

{$R *.dfm}


constructor TCSVImport.Create(AOwner: TComponent);
begin
  inherited;
  jquNoeKontrollplan.JQFileUploadOptions.UploadPath := 'uploads\csv';

  FGruppen := Usersession.ELKERest.GetUser.Bundesland.gruppen;
  FKontrolltypen := Usersession.ELKERest.GetKontrolltypen;
end;

destructor TCSVImport.Destroy;
begin
  //FreeAndNil(FGruppen);
 // FreeAndNil(FKontrolltypen);
  inherited;
end;

procedure TCSVImport.JquNoeKontrollplanOnUploadComplete(Sender: TObject;
  AParams: TStringList);
var
  filename: string;
  lines: System.TArray<System.string>;
  fields: TStringList;
  i, monat: integer;
  Zeilen: TObjectList<TKontrollzeile>;
  Kontrollzeile: TKontrollzeile;

  success: boolean;
begin
  Zeilen := TObjectList<TKontrollzeile>.Create;
  filename := AParams.Values['fileName'];
  filename := TPath.Combine(TPath.GetFullPath(TIWCGJQFileUploadOptions(Sender).UploadPath) , filename);
  lines := TFile.ReadAllLines(filename);
  ImportMappingAbfragen;

  fields := TStringList.Create;
  success := true;
  try
    try
      fields.Delimiter := ';';
      fields.StrictDelimiter := true;
      for i := 1 to length(lines) - 1 do
      begin
        if lines[i].length < 1 then
        begin
          Continue;
        end;
        fields.DelimitedText := lines[i];
        //Aktuell haben wir 14 Felder (0-13)
        if fields.Count <14  then
          raise ECSVInputError.CreateFmt('CSV-Format fehlerhaft: Zeile %d hat nur %d Spalten. Es müssen min. 14 Spalten vorhanden sein!', [i, fields.Count]);

        Kontrollzeile := TKontrollzeile.Create;
        Kontrollzeile.CC := fields[9].Equals('1');
        if (not CheckCCImport.Checked) and (Kontrollzeile.CC) then
        begin
          Kontrollzeile.Free;
          Continue;
        end;
        Kontrollzeile.Zeilennummer := i;
        Kontrollzeile.Gemeindenummer := fields[1].Substring(0, 3);
        Kontrollzeile.LFBIS := fields[2];
        // Existiert der Betrieb?
        Kontrollzeile.IDBetrieb := ExistiertBetrieb(fields[2]);
        if Kontrollzeile.IDBetrieb = -1 then
        begin
          raise ECSVInputError.Create('Die LFBIS-Nummer ' + Kontrollzeile.LFBIS + ' in der Zeile ' +
            IntToStr(Kontrollzeile.Zeilennummer) + ' hat keinen Betrieb zugeordnet.');
        end;
        BezeichnungsMapping(Kontrollzeile, fields[11]);
        if Kontrollzeile.Kontrolltyp = Nil then
        begin
          raise ECSVInputError.Create('Die Probenbezeichnung ' + fields[11] + ' in der Zeile ' +
            IntToStr(Kontrollzeile.Zeilennummer) + ' hat keine passende Verbindung in der CSV-Mapping Tabelle.');
        end;
        Kontrollzeile.Gruppe := FindeGruppe(Kontrollzeile.Gemeindenummer);
        if Kontrollzeile.Gruppe = Nil then
        begin
          raise ECSVInputError.Create('Die Gemeindenummer ' + Kontrollzeile.Gemeindenummer + ' in der Zeile ' +
            IntToStr(Kontrollzeile.Zeilennummer) + ' hat keine Gruppe mit der passenden OKZ zugeordnet.');
        end;
        monat := StrToInt(fields[12]);
        Kontrollzeile.Faellig := EncodeDate(CurrentYear, monat, 1);
        Kontrollzeile.Probenbezeichnung := fields[11].Trim; //"RST-M-A4-KU5"
        Kontrollzeile.Probenbeschreibung := fields[13].Trim; //"30 ml Harn auf Zeranol"

        Zeilen.Add(Kontrollzeile);
      end;
      KontrollenErstellen(Zeilen);
    except
      on E: ECSVInputError do
      begin
        Alert.Error(E.Message);
        success := false;
      end;
      on E: EKontrolleErstellenError do
      begin
        Alert.Error(E.Message);
        success := false;
      end;
      on E: Exception do
      begin
        Alert.Error(E.Message);
        success := false;
      end;
    end;
  if success then
  begin
    Alert.success(Format('Es wurden %d Kontrollen aus %d CSV-Eingabedatensätzen erstellt.', [Zeilen.Count, Length(lines) - 1]));
  end;
  finally
    fields.Free;
    Zeilen.Free;
  end;
end;

function TCSVImport.FindeGruppe(Okz: string): TGruppe;
var
  Gruppe: TGruppe;
begin
  Result := Nil;
  for Gruppe in FGruppen do
  begin
    if Gruppe.Okz.HasValue then
    begin
      if Gruppe.Okz.Value.Equals(Okz) then
      begin
        Result := Gruppe;
        Exit;
      end;
    end;
  end;
end;

procedure TCSVImport.BezeichnungsMapping(Kontrollzeile: TKontrollzeile; Probenbezeichnung: string);
var
  Kontrolltyp: TKontrolltyp;
begin
  QImportMapping.First;
  while not QImportMapping.Eof do
  begin
    if Probenbezeichnung.Equals(QImportMappingPROBENBEZEICHNUNG.AsString) then
    begin
      for Kontrolltyp in FKontrolltypen do
      begin
        if (Kontrolltyp.Bkbtyp.Typ.Equals(QImportMappingBKBTYP.AsString)) and
          (Kontrolltyp.Kontrolltyp.Equals(QImportMappingKONTROLLTYP.AsString)) then
        begin
          Kontrollzeile.Kontrolltyp := Kontrolltyp;
          Kontrollzeile.Rechtsgrundlage := TRechtsgrundlage.Create;
          Kontrollzeile.Rechtsgrundlage.Id := QImportMappingID_RECHTSGRUNDLAGEN.Value;
          Exit;
        end;
      end;
    end;
    QImportMapping.Next;
  end;
end;

procedure TCSVImport.KontrollenErstellen(Zeilen: TObjectList<TKontrollzeile>);
var
  Kontrollzeile: TKontrollzeile;
  Service: IMe;
  Kb: TKontrollbericht;
  Factory: IObjectFactory;
  Todo: TTodo;
  formatedDate: string;
begin
  Kontrollzeile := nil;
  Service := Usersession.ELKERest.GetMeService;
  Factory := TObjectFactory.Create;
  try
    for Kontrollzeile in Zeilen do
    begin
      Todo := Factory.Instance.Create<TTodo>;
      Todo.Faellig := Kontrollzeile.Faellig;
      DateTimeToString(formatedDate, 'dd.mm.yyyy', Todo.Faellig);
      Todo.Titel := 'Kontrollplan ' + Kontrollzeile.Kontrolltyp.Bkbtyp.Typ + ' ' + Kontrollzeile.LFBIS + ' ' +
        formatedDate;
      Todo.Gruppe := Kontrollzeile.Gruppe;

      Kb := Factory.Instance.Create<TKontrollbericht>;
      Kb.Kontrolltyp := Kontrollzeile.Kontrolltyp;
      Kb.Rechtsgrundlage := Kontrollzeile.Rechtsgrundlage;
      Kb.Betrieb := Factory.Instance.Create<TBetrieb>;
      Kb.Betrieb.Id := Kontrollzeile.IDBetrieb;
      Kb.Kurzbemerkung := Kontrollzeile.Probenbezeichnung;
      Kb.KontrollInformationen := Kontrollzeile.Probenbeschreibung;
      Kb.GruppeQuelle := Kontrollzeile.Gruppe;

      Service.NeuerUngeplanterKontrollBericht(Kb, Todo);
      Kontrollzeile.Rechtsgrundlage.Free;
    end;
  except
    on E: Exception do
    begin
      raise EKontrolleErstellenError.Create('In der Zeile ' + IntToStr(Kontrollzeile.Zeilennummer) +
        ' gab es ein Problem beim Importieren. Bis zu dieser Zeile wurden die Kontrollen erfolgreich erstellt.' +
        sLineBreak + E.Message);
    end;
  end;
end;

procedure TCSVImport.ImportMappingAbfragen;
begin
  QImportMapping.Close;
  QImportMapping.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  QImportMapping.Open;
end;

{

  service := Usersession.ELKERest.GetMeService;
  try
  raise Exception.Create('Es gibt noch keinen Kontrolltypen.');
  // kontrolltyp := kontrolltypen[jqcbKontrolltyp.SelectedIndex - 1];
  // rechtsgrundlage := rechtsgrundlagen[jqcbKontrollgrund.SelectedIndex - 1];

  factory := TObjectFactory.Create;

  todo := factory.Instance.Create<TTodo>;
  todo.Faellig := StrToDate('01/' + fields[12] + '/' + jqeJahr.Text);
  DateTimeToString(formatedDate, 'dd.mm.yyyy', todo.Faellig);
  todo.Titel := Kontrolltyp.Bkbtyp.Typ + ' ' + fields[3] + ' ' + formatedDate;

  //gruppe := FindeGruppe();
  todo.gruppe := factory.Instance.Create<TGruppe>;
  todo.gruppe.Id := gruppe.Id;

  // Spezielle Art des Constructor Aufrufs beachten!
  kb := factory.Instance.Create<TKontrollbericht>;
  // Kontrollbericht ausfüllen
  kb.Kontrolltyp := Kontrolltyp;
  kb.rechtsgrundlage := rechtsgrundlage;
  kb.Betrieb := factory.Instance.Create<TBetrieb>;
  kb.Betrieb.Id := idBetrieb;
  if fields.Count > 13 then
  begin
  kb.Kurzbemerkung := fields[13];
  end;

  // Kontrollbericht abspeichern
  service.NeuerUngeplanterKontrollBericht(kb, todo);

  success := true;
  except
  on E: Exception do
  begin
  jqsAlert.Error('Es gab einen Fehler beim Erstellen des Kontrollberichts.' +
  sLineBreak + E.Message);
  success := false;
  end;
  end;
}

function TCSVImport.ExistiertBetrieb(regnr: string): integer;
begin
  quExistiertBetrieb.Close;
  quExistiertBetrieb.ParamByName('lfbis').AsString := regnr;
  quExistiertBetrieb.Open;
  if quExistiertBetrieb.RecordCount > 0 then
  begin
    Result := quExistiertBetrieb.FieldByName('id').AsInteger;
  end
  else
  begin
    Result := -1;
  end;
end;

procedure TCSVImport.jquNoeKontrollplanGetFileName(Sender: TObject; var APath, AFileName: string);
begin
  //Sicherstellen, dass NUR in den UploadPath hochgeladen werden kann. Keine ../../ tricks ...
  APath := TPath.GetFullPath(TIWCGJQFileUpload(Sender).JQFileUploadOptions.UploadPath) +'\';
  AFileName := TPath.GetFileName(AFileName);
end;

end.
