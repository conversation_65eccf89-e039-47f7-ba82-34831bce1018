object AgesXML: TAgesXML
  Left = 0
  Top = 0
  Width = 546
  Height = 394
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 546
    Height = 394
    RenderInvisibleControls = True
    TabOrder = 0
    Align = alClient
    BorderOptions.NumericWidth = 1
    BorderOptions.BorderWidth = cbwNumeric
    BorderOptions.Style = cbsSolid
    BorderOptions.Color = clNone
    object iwplProben: TIWCGPanelList
      Left = 1
      Top = 1
      Width = 544
      Height = 392
      TabOrder = 0
      Version = '1.0'
      Align = alClient
      ZIndex = 5001
      Items = <>
    end
  end
  object quGetKontrollId: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT kb.id'
      'FROM   Bewegungsdaten.Kontrollbericht kb'
      
        '        INNER JOIN Bewegungsdaten.KB_Proben p ON p.ID_Kontrollbe' +
        'richt = kb.id'
      'WHERE  p.Probenbkb = :probenbkb')
    Left = 817
    Top = 169
    ParamData = <
      item
        Name = 'PROBENBKB'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
end
