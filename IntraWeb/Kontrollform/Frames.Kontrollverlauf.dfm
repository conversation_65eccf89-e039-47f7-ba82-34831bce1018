inherited FrameKontrollverlauf: TFrameKontrollverlauf
  Width = 1473
  Height = 653
  Align = alClient
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1473
    Height = 653
    RenderInvisibleControls = True
    TabOrder = 9
    object jqdProbeErstellen: TIWCGJQDialog
      Left = 239
      Top = 19
      Width = 814
      Height = 590
      Visible = False
      TabOrder = 4
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 590
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 814
      JQDialogOptions.zIndex = 5000
    end
    object jqdAgesXml: TIWCGJQDialog
      Left = 248
      Top = 19
      Width = 814
      Height = 590
      Visible = False
      TabOrder = 7
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 590
      JQDialogOptions.Modal = True
      JQDialogOptions.Title = 'Hochgeladene Proben'
      JQDialogOptions.Width = 814
      JQDialogOptions.zIndex = 5000
    end
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1473
      Height = 80
      RenderInvisibleControls = True
      TabOrder = 11
      Version = '1.0'
      Align = alTop
      object IWLabel4: TIWCGJQLabel
        Left = 17
        Top = 11
        Width = 29
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Von:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 173
        Top = 11
        Width = 23
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bis:'
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 329
        Top = 33
        Width = 100
        Height = 21
        TabOrder = 12
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbAbfragenOnClick
      end
      object jqdVon: TIWCGJQDatePicker
        Left = 17
        Top = 33
        Width = 150
        Height = 21
        TabOrder = 1
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object jqdBis: TIWCGJQDatePicker
        Left = 173
        Top = 33
        Width = 150
        Height = 21
        Version = '1.0'
        ZIndex = 5000
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chstes'
        JQDatePickerOptions.PrevText = 'Vorheriges'
        JQDatePickerOptions.Regional = dporGerman
      end
      object jqbProbenExportieren: TIWCGJQButton
        Left = 435
        Top = 33
        Width = 150
        Height = 21
        Visible = False
        TabOrder = 2
        Version = '1.0'
        JQButtonOptions.Label_ = 'Proben exportieren'
        JQButtonOptions.OnClick.OnEvent = JqbProbenExportierenOnClick
      end
      object jqbProbeErstellen: TIWCGJQButton
        Left = 591
        Top = 33
        Width = 150
        Height = 21
        Visible = False
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Probe erstellen'
        JQButtonOptions.OnClick.OnEvent = JqdProbeErstellenOnClick
      end
      object jqbDetailansicht: TIWCGJQButton
        Left = 747
        Top = 33
        Width = 100
        Height = 21
        Visible = False
        TabOrder = 5
        Version = '1.0'
        JQButtonOptions.Label_ = 'Detailansicht'
        JQButtonOptions.OnClick.OnEvent = JqbDetailansichtOnClick
      end
      object jquAges: TIWCGJQFileUpload
        Left = 853
        Top = 33
        Width = 200
        Height = 21
        Visible = False
        TabOrder = 6
        Version = '1.0'
        JQFileUploadOptions.Multiple = False
        JQFileUploadOptions.AllowedExtensions.Strings = (
          'xml')
        JQFileUploadOptions.OnComplete.OnEvent = JquAgesOnComplete
        JQFileUploadOptions.OnComplete.SendAllArguments = True
        JQFileUploadOptions.Classes.Button = 'ui-button'
        JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
        JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
        JQFileUploadOptions.Classes.Fail = 'ui-state-error'
        JQFileUploadOptions.Tip = 'Dateien zum Hochladen hier ablegen'
        JQFileUploadOptions.CancelCaption = 'Abbrechen'
        JQFileUploadOptions.FailedCaption = 'Fehlgeschlagen'
        JQFileUploadOptions.CanOverrideFile = True
        JQFileUploadOptions.DragText = 'Dateien zum Hochladen hier ablegen'
        JQFileUploadOptions.UploadButtonText = 'AGES XML hochladen'
        JQFileUploadOptions.CancelButtonText = 'Abbrechen'
        JQFileUploadOptions.FailUploadText = 'Fehlgeschlagen'
        JQFileUploadOptions.ShowUploadedFileList = False
        CanOverrideFile = True
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 80
      Width = 1473
      Height = 523
      RenderInvisibleControls = True
      TabOrder = 13
      Version = '1.0'
      Align = alClient
      object jqgKontrolluebersicht: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1473
        Height = 523
        TabOrder = 10
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Status'
            Name = 'Status'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Status'
            ProviderName = 'Status'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y H:i'
            Idx = 'Startzeit'
            Name = 'Startzeit'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Startzeit'
            ProviderName = 'Startzeit'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y H:i'
            Idx = 'Endezeit'
            Name = 'Endezeit'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Endezeit'
            ProviderName = 'Endezeit'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkb'
            Name = 'Bkb'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BKB'
            ProviderName = 'Bkb'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollbericht Typ'
            ProviderName = 'Bkbtyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrolltypTyp'
            Name = 'KontrolltypTyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll Typ'
            ProviderName = 'KontrolltypTyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BetriebName'
            Name = 'BetriebName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betrieb'
            ProviderName = 'BetriebName'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GemeindeNummer'
            Name = 'GemeindeNummer'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gemeinde'
            Position = 13
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LFBIS'
            Name = 'LFBIS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'LFBIS'
            Position = 7
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebstyp'
            Name = 'Betriebstyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'Typ'
            Position = 8
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'RechtsgrundlageBez'
            Name = 'RechtsgrundlageBez'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollgrund'
            ProviderName = 'RechtsgrundlageBez'
            Position = 9
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrollorganName'
            Name = 'KontrollorganName'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrollorgan'
            ProviderName = 'KontrollorganName'
            Position = 10
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KontrollInformationen'
            Name = 'KontrollInformationen'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontroll-Informationen'
            ProviderName = 'KontrollInformationen'
            Position = 11
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GruppeQuelle'
            Name = 'GruppeQuelle'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
            Position = 12
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ'
            Name = 'PLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'PLZ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Ort'
            Name = 'Ort'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Ort'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Strasse'
            Name = 'Strasse'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Strasse'
          end>
        JQGridOptions.Height = 475
        JQGridOptions.IgnoreCase = True
        JQGridOptions.LoadOnce = True
        JQGridOptions.RowNum = 200
        JQGridOptions.Sortable = True
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1471
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = jqgKontrolluebersichtJQGridOptionsLoadComplete
        JQGridOptions.OnSelectRow.AjaxAppend = False
        JQGridOptions.OnSelectRow.OnEvent = jqgKontrolluebersichtOnSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridOptions.PagerVisible = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderKontrollverlauf
        JQGridToolbarSearch.Active = True
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object iwrBot: TIWCGJQRegion
      Left = 0
      Top = 603
      Width = 1473
      Height = 50
      RenderInvisibleControls = True
      TabOrder = 14
      Version = '1.0'
      Align = alBottom
      object LabelCount: TIWLabel
        Left = 176
        Top = 17
        Width = 0
        Height = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        NoWrap = True
        HasTabOrder = False
        FriendlyName = 'LabelCount'
      end
      object ButtonCSVExport: TIWCGJQButton
        Left = 17
        Top = 16
        Width = 150
        Height = 21
        TabOrder = 8
        Version = '1.0'
        JQButtonOptions.Label_ = 'Ergebnis exportieren'
        JQButtonOptions.OnClick.OnEvent = ButtonCSVExportOnClick
      end
    end
  end
  object quProben: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT g.Pol_Bez_Kennzif AS "BH", g.Pol_Bezirk AS "BH_Name", b.R' +
        'egnr AS "LFBIS", b.name AS "Betr_Name", a.Strasse AS "Betr_Stras' +
        'se", a.Ort AS "Betr_Ort", a.PLZ AS "Betr_Plz", kb.Bkb AS "EXT_KE' +
        'NN_AUFTR",'
      
        '       pr.Probenart, pr.Bemerkung, pr.Probenbkb AS "EXT_KENN_PRO' +
        'BE", pr.Untersuchungsauftrag, pr.Datum AS "DAT_Probenahme", pe.E' +
        'mail AS "Einsender_mail", pe.Telefon AS "Einsender_Tel", pr.Vorg' +
        '_Menge,'
      
        '       pr.Beschaffenheit, pr.Futtertyp, pr.Verwendungszweck, pr.' +
        'Tier_art_lisa AS "Tier_Art", pr.Tier_kategorie, pr.Beimischrate,' +
        ' pr.Verpackung, pr.Verschluss, pr.Versiegelt, pr.Herk_Zukauf, '
      
        '       pr.Untersuchungsauftrag, pr.Verdacht, pr.Gegenprobe_Belas' +
        'sen, pe.Nachname, pe.Vorname, pe.Titel, pr.Status, '#39#39' AS "Proben' +
        'bez"'
      'FROM   Bewegungsdaten.KB_Proben pr'
      
        '        LEFT OUTER JOIN Stammdaten.Personen pe ON pr.ID_Einsende' +
        'r = pe.ID'
      
        '        LEFT OUTER JOIN Bewegungsdaten.Kontrollbericht kb ON pr.' +
        'ID_Kontrollbericht = kb.ID'
      
        '        LEFT OUTER JOIN Stammdaten.Betriebe b ON kb.ID_Betrieb =' +
        ' b.ID'
      
        '        LEFT OUTER JOIN Stammdaten.Adressen a ON b.ID_Adresse = ' +
        'a.ID'
      
        '        LEFT OUTER JOIN Stammdaten.Gemeinden g ON a.ID_Gemeinde ' +
        '= g.ID'
      'WHERE pr.ID_Kontrollbericht = :id_kontrollbericht;')
    Left = 1184
    Top = 16
    ParamData = <
      item
        Name = 'ID_KONTROLLBERICHT'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object DownloadVerlauf: TIWCGJQFileDownload
    Version = '1.0'
    Left = 1185
    Top = 65
  end
  object jqsAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 1185
    Top = 113
  end
  object quAgesXmlHochladen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Bewegungsdaten.Kb_Proben'
      
        'SET    Ergebnis_XML = :xml, Exportname = :exportname, Exporttime' +
        ' = :Exporttime, Agesauftragsnummer = :Agesauftragsnummer, Agespr' +
        'obennummer = :Agesprobennummer, '
      
        '       ages_auftragsstatus = :ages_auftragsstatus, ages_probenst' +
        'atus = :ages_probenstatus, GUID_DOKUMENT_AGES_ERGEBNIS = :GUID_D' +
        'OKUMENT_AGES_ERGEBNIS, '
      
        '       grenzwertueberschreitung = :grenzwertueberschreitung, auf' +
        'tragszusatztext = :auftragszusatztext'
      'WHERE  Probenbkb = :probenbkb;')
    Left = 1417
    Top = 113
    ParamData = <
      item
        Name = 'XML'
        DataType = ftWideMemo
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'EXPORTNAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'EXPORTTIME'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AGESAUFTRAGSNUMMER'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AGESPROBENNUMMER'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AGES_AUFTRAGSSTATUS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AGES_PROBENSTATUS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GUID_DOKUMENT_AGES_ERGEBNIS'
        DataType = ftGuid
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRENZWERTUEBERSCHREITUNG'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUFTRAGSZUSATZTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PROBENBKB'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object ProviderKontrollverlauf: TIWCGJQGridDataSetProvider
    DataSet = DMKontrolle.MTKontrollverlauf
    KeyFields = 'ID'
    Left = 1352
    Top = 16
  end
  object quProbenDokumentHochladen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.Dokumente (GUID, BLDCODE, Bezeichnung' +
        ', Typ, Dateiname, Erstellt_Am, Dokument)'
      
        'VALUES      (:guid, :bldcode, :bezeichnung, :typ, :dateiname, :e' +
        'rstellt_am, :dokument);')
    Left = 1417
    Top = 169
    ParamData = <
      item
        Name = 'GUID'
        DataType = ftGuid
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DATEINAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ERSTELLT_AM'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DOKUMENT'
        DataType = ftBlob
        ParamType = ptInput
        Value = Null
      end>
  end
end
