object CSVImport: TCSVImport
  Left = 0
  Top = 0
  Width = 852
  Height = 572
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 852
    Height = 572
    RenderInvisibleControls = True
    Version = '1.0'
    Align = alClient
    BorderOptions.NumericWidth = 1
    BorderOptions.Style = cbsSolid
    object jquNoeKontrollplan: TIWCGJQFileUpload
      Left = 11
      Top = 56
      Width = 230
      Height = 21
      TabOrder = 1
      Version = '1.0'
      JQFileUploadOptions.Multiple = False
      JQFileUploadOptions.AllowedExtensions.Strings = (
        'csv'
        'txt')
      JQFileUploadOptions.OnComplete.OnEvent = JquNoeKontrollplanOnUploadComplete
      JQFileUploadOptions.OnComplete.SendAllArguments = True
      JQFileUploadOptions.Classes.Button = 'ui-button'
      JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
      JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
      JQFileUploadOptions.Classes.Fail = 'ui-state-error'
      JQFileUploadOptions.OnUpload.SendAllArguments = True
      JQFileUploadOptions.Tip = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.CancelCaption = 'Abbrechen'
      JQFileUploadOptions.FailedCaption = 'Fehlgeschlagen'
      JQFileUploadOptions.CanOverrideFile = True
      JQFileUploadOptions.DragText = 'Dateien zum Hochladen hier ablegen'
      JQFileUploadOptions.UploadButtonText = 'CSV-Kontrollplan hochladen'
      JQFileUploadOptions.CancelButtonText = 'Abbrechen'
      JQFileUploadOptions.FailUploadText = 'Fehlgeschlagen'
      JQFileUploadOptions.MultipleFileDropNotAllowedMessage = 'Sie d'#252'rfen nur ein File hochladen'
      JQFileUploadOptions.ShowUploadedFileList = False
      CanOverrideFile = True
      OnGetFileName = jquNoeKontrollplanGetFileName
    end
    object CheckCCImport: TIWCGJQCheckBoxEx
      Left = 11
      Top = 21
      Width = 366
      Height = 21
      TabOrder = 2
      Version = '1.0'
      Caption = 'AMA-CC mitimportieren'
    end
  end
  object quExistiertBetrieb: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Betriebe'
      'WHERE  REGNR = :lfbis;')
    Left = 40
    Top = 504
    ParamData = <
      item
        Name = 'LFBIS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quBetriebErstellen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Betriebe (REGNR, Name, Telefon, ID_Adress' +
        'e, GEMNR, Bldcode)'
      
        'VALUES      (:lfbis, :name, :telefon, :id_adresse, :gemnr, :bldc' +
        'ode);')
    Left = 136
    Top = 504
    ParamData = <
      item
        Name = 'LFBIS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TELEFON'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ADRESSE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GEMNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeueAdresse: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Adressen (Ort, Plz, Strasse, Bldcode, ID_' +
        'Gemeinde)'
      'VALUES      (:Ort, :Plz, :Strasse, :Bldcode, :id_gemeinde);')
    Left = 224
    Top = 504
    ParamData = <
      item
        Name = 'ORT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STRASSE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_GEMEINDE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFindeGemeinde: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Gemeinden'
      'WHERE  Gemeindekennziffer = :gemeindekennziffer;')
    Left = 312
    Top = 504
    ParamData = <
      item
        Name = 'GEMEINDEKENNZIFFER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 784
    Top = 16
  end
  object QImportMapping: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.CSVImportMapping'
      'WHERE  BLDCODE = :bldcode;')
    Left = 392
    Top = 504
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
    object QImportMappingID: TGuidField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QImportMappingPROBENBEZEICHNUNG: TStringField
      FieldName = 'PROBENBEZEICHNUNG'
      Origin = 'PROBENBEZEICHNUNG'
      Required = True
      Size = 255
    end
    object QImportMappingBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Required = True
      Size = 10
    end
    object QImportMappingKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      Required = True
      FixedChar = True
      Size = 3
    end
    object QImportMappingBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QImportMappingID_RECHTSGRUNDLAGEN: TIntegerField
      FieldName = 'ID_RECHTSGRUNDLAGEN'
      Origin = 'ID_RECHTSGRUNDLAGEN'
      Required = True
    end
  end
end
