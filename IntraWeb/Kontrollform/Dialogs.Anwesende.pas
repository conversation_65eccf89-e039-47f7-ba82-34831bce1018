unit Dialogs.Anwesende;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, Modules.Kontrolle,
  IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQGrid, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert;

type
  //TDialogAnwesende = class(TDialogBase)
  TDialogAnwesende = class(TDialogBase<TDMKontrolle>)
    GridAnwesende: TIWCGJQGrid;
    ProviderAnwesende: TIWCGJQGridDataSetProvider;
    IWCGJQRegion1: TIWCGJQRegion;
  private
    IDKontrollbericht: integer;
  public
    constructor Create(AOwner: TComponent; LIDKontrollbericht: integer);
      reintroduce;
    procedure DoButtonCancel(ASender: TObject; AParams: TStringList); override;
    procedure DoButtonOK(ASender: TObject; AParams: TStringList); override;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}


constructor TDialogAnwesende.Create(AOwner: TComponent;
  LIDKontrollbericht: integer);
begin
  self := inherited Create(AOwner);
  IDKontrollbericht := LIDKontrollbericht;
  if LIDKontrollbericht > -1 then
  begin
    DM.OpenAnwesende(LIDKontrollbericht);
    ProviderAnwesende.DataSet := DM.QAnwesende;
    IWFrameRegion.JQDialogOptions.Title := 'Anwesende';
  end;
end;

procedure TDialogAnwesende.DoButtonCancel(ASender: TObject;
  AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogAnwesende.DoButtonOK(ASender: TObject; AParams: TStringList);
begin
  inherited;
  Close;
end;

procedure TDialogAnwesende.InitializeControls;
begin
  inherited;
   //todo 1 -oCH -cImplementieren: Felder initialisieren
end;

end.
