unit Forms.UngeplanteKontrolle;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes,
  Forms.Base, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCompExtCtrls, IWCGJQButton, IWCompLabel,
  IWCGJQLabel, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, Vcl.Controls, Vcl.Forms,
  IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion,
  Frames.UngeplanteKontrolle,
  ELKE.Classes.Generated, IWCGJQComp, IWCGJQSweetAlert;

type
  TFormUngeplanteKontrolle = class(TFormBase)
    procedure IWAppFormDestroy(Sender: TObject);
    procedure IWAppFormCreate(Sender: TObject);
  private
    FKontrolle: TKont<PERSON>bericht;
    FFrame: TFrameUngeplanteKontrolle;
    FSaveProc: TPRoc;
    procedure SetKontrolle(const Value: TKontrollbericht);
    procedure DoSaveProc;
  public
    constructor Create(AOwner: TComponent; ASaveProc:TProc);reintroduce;
    property Kontrolle: TKontrollbericht write SetKontrolle;
  end;

implementation


{$R *.dfm}


procedure TFormUngeplanteKontrolle.IWAppFormDestroy(Sender: TObject);
begin
  FreeAndNil(FFrame);
  inherited;
end;

constructor TFormUngeplanteKontrolle.Create(AOwner: TComponent; ASaveProc:TProc);
begin
  inherited Create(AOwner);
  FSaveProc := ASaveProc;
end;

procedure TFormUngeplanteKontrolle.DoSaveProc;
begin
  if Assigned(FSaveProc) then
  begin
   FSaveProc;
  end;
end;

procedure TFormUngeplanteKontrolle.IWAppFormCreate(Sender: TObject);
begin
  FFrame := TFrameUngeplanteKontrolle.Create(self,
    procedure
    begin
      DoSaveProc;
      Release;
    end);
  FFrame.Name := 'FrameUngeplateKontrolle';
  FFrame.IWFrameRegion.Parent := self;
  FFrame.Align := alClient;
  Kontrolle := nil;
end;

procedure TFormUngeplanteKontrolle.SetKontrolle(const Value: TKontrollbericht);
begin
  FKontrolle := Value;
  if FKontrolle <> nil then
  begin
    FFrame.InitialisiereAusKontrolle(FKontrolle);
    Title := 'Ungeplante Kontrolle bearbeiten'
  end
  else
  begin
    Title := 'Ungeplante Kontrolle erstellen';
  end;
end;

end.
