inherited DialogStornierungsgrund: TDialogStornierungsgrund
  Width = 468
  Height = 196
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 468
    Height = 196
    TabOrder = 0
    JQDialogOptions.Height = 196
    JQDialogOptions.Width = 468
    inherited RegionContent: TIWCGJQRegion
      Width = 468
      Height = 146
      TabOrder = 1
      object IWCGJQRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 468
        Height = 146
        TabOrder = 5
        Version = '1.0'
        Align = alClient
        object IWLabel1: TIWCGJQLabel
          Left = 17
          Top = 23
          Width = 122
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Stornierungsgrund:'
        end
        object EditStornierungsgrund: TIWCGJQEdit
          Left = 17
          Top = 45
          Width = 420
          Height = 21
          TabOrder = 6
          Version = '1.0'
          ScriptEvents = <>
          Text = ''
          JQEvents.OnKeyUp.OnEvent = EditStornierungsgrundJQEventsKeyUp
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 146
      Width = 468
      TabOrder = 4
      inherited ButtonCancel: TIWCGJQButton
        Left = 360
        TabOrder = 3
      end
      inherited ButtonOK: TIWCGJQButton
        Left = 244
        TabOrder = 2
      end
    end
  end
end
