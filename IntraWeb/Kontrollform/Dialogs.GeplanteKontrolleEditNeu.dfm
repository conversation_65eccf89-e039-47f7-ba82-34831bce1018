inherited DialogGeplanteKontrolleEditNeu: TDialogGeplanteKontrolleEditNeu
  Width = 761
  Height = 675
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 761
    Height = 675
    TabOrder = 4
    JQDialogOptions.Height = 675
    JQDialogOptions.Width = 761
    inherited RegionContent: TIWCGJQRegion
      Width = 761
      Height = 615
      TabOrder = 1
      object LabelBetrieb: TIWCGJQLabel
        Left = 207
        Top = 114
        Width = 55
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelBetrieb'
        Caption = 'Betrieb:'
      end
      object LabelKontrollberichttyp: TIWCGJQLabel
        Left = 124
        Top = 44
        Width = 138
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrollberichttyp'
        Caption = 'Kontrollberichtstyp:'
      end
      object LabelKontrollgrund: TIWCGJQLabel
        Left = 159
        Top = 220
        Width = 103
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrollgrund'
        Caption = 'Kontrollgrund:'
      end
      object LabelKontrolltyp: TIWCGJQLabel
        Left = 179
        Top = 80
        Width = 83
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelKontrolltyp'
        Caption = 'Kontrolltyp:'
      end
      object LabelAngemeldet: TIWCGJQLabel
        Left = 171
        Top = 299
        Width = 91
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelAngemeldet'
        Caption = 'Angemeldet:'
      end
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 94
        Top = 339
        Width = 168
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelAngemeldet'
        Caption = 'Kontroll-Informationen:'
      end
      object LabelFaelligkeitsdatum: TIWCGJQLabel
        Left = 142
        Top = 261
        Width = 120
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelFaelligkeitsdatum'
        Caption = 'F'#228'lligkeitsdatum:'
      end
      object IWCGJQLabel2: TIWCGJQLabel
        Left = 164
        Top = 459
        Width = 98
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelNotiz'
        Caption = 'Interne Notiz:'
      end
      object ButtonBetrieb: TIWCGJQButton
        Left = 661
        Top = 114
        Width = 33
        Height = 21
        TabOrder = 8
        Version = '1.0'
        JQButtonOptions.Icons.Primary = 'ui-icon-pencil'
        JQButtonOptions.OnClick.OnEvent = ButtonBetriebJQButtonOptionsClick
      end
      object DatePickFaelligkeit: TIWCGJQDatePicker
        Left = 282
        Top = 261
        Width = 370
        Height = 21
        TabOrder = 9
        Version = '1.0'
        Caption = ''
        JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
      end
      object DropBerichtstyp: TIWCGJQDropDown
        Left = 282
        Top = 38
        Width = 370
        Height = 25
        TabOrder = 5
        Version = '1.0'
        DataLink.FieldName = 'BKBTYP'
        JQDropDownOptions.OnChange.OnEvent = DropBerichtstypJQDropDownOptionsChange
        Groups = <>
        Items = <>
      end
      object DropKontrollgrund: TIWCGJQDropDown
        Left = 282
        Top = 221
        Width = 370
        Height = 25
        TabOrder = 7
        Version = '1.0'
        DataLink.FieldName = 'ID_RECHTSGRUNDLAGE'
        Groups = <>
        Items = <>
      end
      object DropKontrolltyp: TIWCGJQDropDown
        Left = 282
        Top = 82
        Width = 370
        Height = 25
        TabOrder = 6
        Version = '1.0'
        DataLink.FieldName = 'KONTROLLTYP'
        Groups = <>
        Items = <>
      end
      object MemoBetrieb: TIWCGJQMemoEx
        Left = 282
        Top = 117
        Width = 370
        Height = 90
        TabOrder = 10
        Css = ''
        Version = '1.0'
        ZIndex = 0
        Style.Strings = (
          'width:370px !important')
        BGColor = clNone
        Editable = False
        Required = False
        SubmitOnAsyncEvent = True
        ReadOnly = True
      end
      object DatePickAngemeldet: TIWCGJQDatePicker
        Left = 303
        Top = 299
        Width = 349
        Height = 21
        TabOrder = 11
        Version = '1.0'
        Caption = ''
        JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
      end
      object MemoKontrollInformationen: TIWCGJQMemoEx
        Left = 282
        Top = 339
        Width = 370
        Height = 105
        TabOrder = 13
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:370px !important')
      end
      object CheckAngemeldet: TIWCGJQCheckBoxEx
        Left = 282
        Top = 299
        Width = 23
        Height = 21
        TabOrder = 14
        Version = '1.0'
        Caption = ''
        JQCheckExOptions.OnClick.OnEvent = CheckAngemeldetJQCheckExOptionsClick
        JQCheckExOptions.OnChange.OnEvent = CheckAngemeldetJQCheckExOptionsClick
      end
      object MemoInterneNotiz: TIWCGJQMemoEx
        Left = 282
        Top = 459
        Width = 370
        Height = 105
        TabOrder = 15
        Css = ''
        Version = '1.0'
        ZIndex = 0
        BGColor = clNone
        Editable = True
        Required = False
        SubmitOnAsyncEvent = True
        MemoStyle.Strings = (
          'width:370px !important')
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 625
      Width = 759
      TabOrder = 3
      inherited ButtonCancel: TIWCGJQButton
        Left = 651
        TabOrder = 2
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 643
        TabOrder = 12
        inherited ButtonOK: TIWCGJQButton
          Left = 535
          TabOrder = 0
        end
      end
    end
  end
end
