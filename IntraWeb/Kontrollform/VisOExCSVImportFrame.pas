﻿unit VisOExCSVImportFrame;

interface

uses
  System.SysUtils, System.Classes, System.DateUtils,
  VCL.Controls, VCL.Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQFileUpload, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQComboBox,
  ELKE.Classes.Generated, System.Generics.Collections, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQEdit, IWCGJQRegion, IWCGJQLabel, IWCompCheckbox, IWCGJQCheckBox;

type
  TKontrollzeile = class(TObject)
  private
    Gruppe: TGruppe;
    Gemeindenummer: string;
    OKZ: string;
    LFBIS: string;
    Bundesland: integer;
    IDBetrieb: integer;
    CC: boolean;
    Kontrolltyp: TKontrolltyp;
    Faellig: TDateTime;
    Probenbezeichnung: string;
    Probenbeschreibung: string;
    Zeilennummer: integer;

    BKBNr: string;
    Prioritaet: string;
    Seuchen_id: string;
  end;

type
  ECSVInputError = class(Exception);
  EKontrolleErstellenError = class(Exception);

type
  TVisOExCSVImport = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    FileBkbImport: TIWCGJQFileUpload;
    quExistiertBetrieb: TFDQuery;
    quBetriebErstellen: TFDQuery;
    quNeueAdresse: TFDQuery;
    quFindeGemeinde: TFDQuery;
    Alert: TIWCGJQSweetAlert;
    QBKBInsert: TFDQuery;
    QBKBInsertNUMMER: TStringField;
    QBKBInsertSYSTEMKZ: TStringField;
    QBKBInsertBKBTYP: TStringField;
    QBKBInsertJAHR: TBCDField;
    QBKBInsertBUNDESLAND: TSmallintField;
    QBKBInsertLFD_NR: TIntegerField;
    QBKBInsertLFD_NR_HEX: TStringField;
    QGruppe: TFDQuery;
    QGruppeID: TFDAutoIncField;
    QGruppeBEZEICHNUNG: TStringField;
    QGruppeBLDCODE: TSmallintField;
    QGruppeMUTTERGRUPPE: TIntegerField;
    QGruppeID_USER_HAUPTVER: TIntegerField;
    QGruppeID_USER_STELLVER: TIntegerField;
    QGruppeOKZ: TStringField;
    QGruppePERSOENLICH: TBooleanField;
    QGruppeEMAIL: TStringField;
    FileKKImport: TIWCGJQFileUpload;
    procedure UploadGetFileName(Sender: TObject; var APath, AFileName: string);
    procedure FileBkbImportOnUploadComplete(Sender: TObject; AParams: TStringList);
    procedure FileKKImportJQFileUploadOptionsComplete(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FGruppen: TList<TGruppe>;
    FKontrolltypen: TList<TKontrolltyp>;
    FRechtsgrundlage: TRechtsgrundlage;
    FRechtsgrundlagen: TList<TRechtsgrundlage>;

    function ExistiertBetrieb(regnr: string): integer;
    function FindeGruppe(OKZ: string): TGruppe;
    procedure KontrollenErstellen(Zeilen: TObjectList<TKontrollzeile>);
    function GetKontrolltypFuerTierart(const ATierart: string): TKontrolltyp;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation

uses
  ServerController, System.IOUtils, System.Types, System.StrUtils, dmmain,
  Utility, ELKE.Services.Me.Intf, DX.Classes.ObjectFactory, ELKE.Classes.RESTError, ELKE.Server.Logger;

{$R *.dfm}

const
  BKB_TYP = 'TSKO';
  ID_RECHTSGRUNDLAGE = 50;

constructor TVisOExCSVImport.Create(AOwner: TComponent);
begin
  inherited;
  FileBkbImport.JQFileUploadOptions.UploadPath := 'uploads\csv';
  FileKKImport.JQFileUploadOptions.UploadPath := 'uploads\csv';

  FGruppen := Usersession.ELKERest.GetUser.Bundesland.gruppen;
  FKontrolltypen := Usersession.ELKERest.GetKontrolltypen;

  FRechtsgrundlagen := Usersession.ELKERest.Rechtsgrundlagen(BKB_TYP);
  for var LRechtsgrundlage in FRechtsgrundlagen do
  begin
    if LRechtsgrundlage.Id = ID_RECHTSGRUNDLAGE then
    begin
      FRechtsgrundlage := LRechtsgrundlage;
      break;
    end;
  end;

end;

destructor TVisOExCSVImport.Destroy;
begin
  inherited;
end;

procedure TVisOExCSVImport.FileBkbImportOnUploadComplete(Sender: TObject; AParams: TStringList);
begin
  var LKontrollZeilen := TObjectList<TKontrollzeile>.Create;
  var LFilename := AParams.Values['fileName'];
  LFilename := TPath.Combine(TPath.GetFullPath(TIWCGJQFileUploadOptions(Sender).UploadPath), LFilename);
  var LCSVLines := TFile.ReadAllLines(LFilename);

  var LFields := TStringList.Create;
  try
    try
      LFields.Delimiter := ';';
      LFields.StrictDelimiter := true;
      for var i := 0 to length(LCSVLines) - 1 do
      begin
        if LCSVLines[i].length < 1 then
        begin
          Continue;
        end;
        LFields.DelimitedText := LCSVLines[i];

        //Aktuell haben wir 24 Felder (0-23)
        if LFields.Count < 24 then
          raise
            ECSVInputError.CreateFmt('CSV-Format fehlerhaft: Zeile %d hat nur %d Spalten. Es müssen min. 24 Spalten vorhanden sein!', [i, LFields.Count]);

{$REGION 'IMPORT FORMAT'}
        (*
          CSV-Datei Feldbeschreibung:
          ===========================

          Field  0: stichtag        (DATE)          Stichtag der Datenermittlung
          Field  1: bkbNr           (CHAR[26])      Eindeutige BKB Nummer (Format: BBB.TTTTTTTTTT.JJJJ.NNNNNN)
          Field  2: parentBkbNr     (CHAR[26])      BKB Nummer des übergeordneten BKBs (sofern vorhanden)
          Field  3: bkbName         (CHAR[100])     Name des BKB
          Field  4: datum           (DATE)          Datum des BKB (Verwendung als Fälligkeitsdatum/Monat)
          Field  5: art             (CHAR[1])       Untersuchungsart gemäß Domain 9.8 domArtUntersuchung
          Field  6: tierart         (CHAR[2])       Werte gemäß Werteliste 8.6
          Field  7: regNrOrt        (CHAR[7])       Registrierungsnummer des Untersuchungsorts (entspricht LFBIS)
          Field  8: gemeindeOrt     (CHAR[5])       Gemeindenummer des Untersuchungsorts
          Field  9: xKoord          (DECIMAL(31,8)) X-Koordinate, sofern vorhanden (WGS84)
          Field 10: yKoord          (DECIMAL(31,8)) Y-Koordinate, sofern vorhanden (WGS84)
          Field 11: regNrBest       (CHAR[7])       Registrierungsnummer des Bestandsbetriebs
          Field 12: landBestB       (CHAR[2])       Land des Bestandsbetriebs
          Field 13: anzTiereBestand (INTEGER)       Anzahl der Tiere im Bestand
          Field 14: anzTiereKontr   (INTEGER)       Anzahl der kontrollierten Tiere
          Field 15: ergebnis        (CHAR[1])       BKB Gesamtergebnis gem. Werteliste 8.11
          Field 16: ktr_tierarztnr  (CHAR[5])       Tierarztnummer des kontrollierenden Tierarztes
          Field 17: ktr_sonstige    (CHAR[100])     Name eines sonstigen Kontrollorgans oder Probennehmers
          Field 18: bkbKommentar    (VARCHAR[16000]) Anamnese / Bemerkungen / nicht amtliche Untersuchungsaufträge
          Field 19: bkbStatus       (CHAR[1])       BKB Status gemäß 9.20 BKB Status
          Field 20: labor           (SMALLINT)      Labor ID gem. Werteliste 8.4
          Field 21: geloescht       (CHAR[1])       BKB ist gelöscht ([J]a / [N]ein)
          Field 22: prioritaet      (CHAR[1])       Priorität bei BKBs vom Typ TKH-S
          Field 23: seuchen_id      (CHAR[20])      Seuchen-ID (Klammer-ID für BKBs)

          "2025-01-01";"VIS.TKH-S.2024.000031";"";"Tierkrankheit - Seuchenkontrolle";"2024-10-28";"E";"GE";"2211360";"40441";;;"2211360";"";0;1;"";"04721";"";"";"V";;"N";"2";"HPAI-2024-01";

          ------------------------------------------------------------------------
          Import-Regelwerk (aktuell relevant):
          ====================================

          Felder für den Import:
            - BKBNr         (Field 1: bkbNr)
            - prioritaet    (Field 22: prioritaet)
            - seuchen_id    (Field 23: seuchen_id)

          Regeln für den Import (Filterkriterien):
            - Filter auf BKB Typ: 'VIS.TKH.S'
            - BKB Status nur 'G' oder 'B', eventuell zusätzlich 'O'
            - Gemeindenummer = Field 8 (gemeindeOrt)
            - LFBIS          = Field 7 (regNrOrt)
            - Probenbezeichnung fix gesetzt als: 'TKH-S' + Field 6 (tierart)
            - Monat (Fälligkeitsdatum) entspricht Field 4 (datum)
        *)
{$ENDREGION}

        //Prüfung ob Zeile valide ist, oder mgl. den HEader enthält
        var LDatumString := LFields[4];
        var LFormatSettings := TFormatSettings.Create('en-us');
        LFormatSettings.ShortDateFormat := 'YYYY-MM-DD';
        LFormatSettings.LongDateFormat := 'YYYY-MM-DD';
        LFormatSettings.DateSeparator := '-';

        var LDatum: TDate := StrToDateDef(LDatumString, 0, LFormatSettings);
        if LDatum = 0 then
        begin
          ELKELog('Ungültiges Datum, Datensatz wird nicht importiert.');
          Continue;
        end;

        //Filter auf VIS.TKH.S
        var LBKB := LFields[1].ToUpper.Trim;
        if not LBKB.StartsWith('VIS.TKH-S') then
          Continue;

        var LKontrollzeile := TKontrollzeile.Create;

        LKontrollzeile.CC := False; //Hier immer false

        LKontrollzeile.Zeilennummer := i;

        LKontrollzeile.Gemeindenummer := LFields[8];
        LKontrollzeile.OKZ := Copy(LFields[8], 1, 3);

        LKontrollzeile.LFBIS := LFields[7];

        var LBundesland: string := LFields[8][1]; // 40441  - erstes Zeichen ist das Bundeslandkürzel
        LKontrollzeile.Bundesland := StrToInt(LBundesland + '00'); // 400

        if LKontrollzeile.Bundesland <> 300 then //Vorläufig nur NOE
          Continue;

        // Existiert der Betrieb?
        LKontrollzeile.IDBetrieb := ExistiertBetrieb(LKontrollzeile.LFBIS);
        if LKontrollzeile.IDBetrieb = -1 then
          raise ECSVInputError.Create('Die LFBIS-Nummer ' + LKontrollzeile.LFBIS + ' in der Zeile ' +
            IntToStr(LKontrollzeile.Zeilennummer) + ' hat keinen Betrieb zugeordnet.');

        LKontrollzeile.Gruppe := FindeGruppe(LKontrollzeile.OKZ);
        if LKontrollzeile.Gruppe = nil then
          raise ECSVInputError.Create('Die Gemeindenummer ' + LKontrollzeile.Gemeindenummer + ' in der Zeile ' +
            IntToStr(LKontrollzeile.Zeilennummer) + ' hat keine Gruppe mit der passenden OKZ zugeordnet.');

        LKontrollzeile.Prioritaet := LFields[22];

        // Fälligkeit je nach Priorität setzen
        case StrToIntDef(LKontrollzeile.Prioritaet, 0) of
          1: LKontrollzeile.Faellig := Today; // heute
          2: LKontrollzeile.Faellig := IncDay(Today, 14); // heute + 14 Tage
          3: LKontrollzeile.Faellig := IncMonth(Today, 1); // heute + 1 Monat
        else
          LKontrollzeile.Faellig := Today; // Default heute
        end;

        LKontrollzeile.Probenbezeichnung := 'TKH-S ' + LFields[6];
        LKontrollzeile.Probenbeschreibung := ''; //Todo: KK Import

        LKontrollzeile.BKBNr := LFields[1];
        LKontrollzeile.Kontrolltyp := GetKontrolltypFuerTierart(LFields[6]);
        if LKontrollzeile.Kontrolltyp = nil then
        begin
          ELKELog('Kontrolltyp für Tierart %s existiert nicht. Datensatz wird nicht importiert.', [LFields[6]]);
          Continue;
        end;

        LKontrollzeile.Seuchen_id := LFields[23];

        LKontrollZeilen.Add(LKontrollzeile);
      end;
      KontrollenErstellen(LKontrollZeilen);
      Alert.Success(Format('Es wurden %d Kontrollen aus %d CSV-Eingabedatensätzen erstellt.', [LKontrollZeilen.Count,
          length(LCSVLines) - 1]));
    except
      on E: Exception do
      begin
        Alert.Error('Fehler beim CSV Import' + E.Message);
      end;
    end;
  finally
    FreeAndNil(LFields);
    FreeAndNil(LKontrollZeilen);
  end;
end;

function TVisOExCSVImport.FindeGruppe(OKZ: string): TGruppe;
var
  Gruppe: TGruppe;
begin
  Result := Usersession.ELKERest.GetGruppeFuerOKZ(OKZ);
end;

function TVisOExCSVImport.GetKontrolltypFuerTierart(const ATierart: string): TKontrolltyp;
begin
  var LKontrolltypKuerzel := 'M' + ATierart;
  Result := nil;
  for var LKontrolltyp in FKontrolltypen do
  begin
    if (LKontrolltyp.Bkbtyp.Typ = BKB_TYP) and (LKontrolltyp.Kontrolltyp = LKontrolltypKuerzel) then
    begin
      Result := LKontrolltyp;
      break;
    end;
  end;

end;

procedure TVisOExCSVImport.KontrollenErstellen(Zeilen: TObjectList<TKontrollzeile>);
var
  Kontrollzeile: TKontrollzeile;
  Service: IMe;
  Kb: TKontrollbericht;
  Factory: IObjectFactory;
  Todo: TTodo;
  formatedDate: string;
begin
  Kontrollzeile := nil;
  Factory := TObjectFactory.Create;
  try
    for Kontrollzeile in Zeilen do
    begin
      //TODO: BKB im REST Service prüfen
      QBKBInsert.Close;
      QBKBInsert.ParamByName('BKB').AsString := Kontrollzeile.BKBNr;
      QBKBInsert.Open;
      if QBKBInsert.RecordCount >= 1 then
      begin
        ELKELog('BKB %s existiert bereits. Wird nicht importiert.', [Kontrollzeile.BKBNr]);
        Continue;
      end
      else
        //BKB eintragen (sonst akzeptiert der REST Server das nicht)
        QBKBInsert.Insert;
      try

        //VIS.TKH-S.2024.000031

        var LBKBParts := Kontrollzeile.BKBNr.Split(['.']);

        QBKBInsertNUMMER.AsString := Kontrollzeile.BKBNr;
        QBKBInsertSYSTEMKZ.AsString := 'VIS';
        QBKBInsertBKBTYP.AsString := BKB_TYP;
        QBKBInsertJAHR.AsString := LBKBParts[2];
        QBKBInsertLFD_NR.AsString := LBKBParts[3];
        QBKBInsertBUNDESLAND.AsInteger := Kontrollzeile.Bundesland;
        QBKBInsert.Post;
      except
        QBKBInsert.Cancel;
        raise;
      end;

      Kb := nil;
      try
        Todo := Factory.Instance.Create<TTodo>;
        Todo.Faellig := Kontrollzeile.Faellig;
        DateTimeToString(formatedDate, 'dd.mm.yyyy', Todo.Faellig);
        Todo.Titel := 'Kontrollplan ' + Kontrollzeile.Kontrolltyp.Bkbtyp.Typ + ' ' + Kontrollzeile.LFBIS + ' ' +
          formatedDate;
        Todo.Gruppe := Kontrollzeile.Gruppe;

        Kb := Factory.Instance.Create<TKontrollbericht>;
        Kb.Bkb := Kontrollzeile.BKBNr;
        Kb.Kontrolltyp := Kontrollzeile.Kontrolltyp;
        Kb.Rechtsgrundlage := FRechtsgrundlage;
        Kb.Betrieb := Factory.Instance.Create<TBetrieb>;
        Kb.Betrieb.Id := Kontrollzeile.IDBetrieb;
        Kb.Kurzbemerkung := Kontrollzeile.Probenbezeichnung;
        Kb.KontrollInformationen := Kontrollzeile.Probenbeschreibung;
        Kb.GruppeQuelle := Kontrollzeile.Gruppe;
        Kb.Status := 'U';

        Kb.Prioritaet := Kontrollzeile.Prioritaet;
        Kb.SeuchenId := Kontrollzeile.Seuchen_id;
        ELKELog('BKB %s wird importiert.', [Kontrollzeile.BKBNr]);
        Usersession.ELKERest.UngeplantenKontrollberichtErstellen(Kb, Todo);
      finally
        //Todo: Memleaks prüfen. Factory prüfen!
          //FreeAndNil(Kb);
      end;
    end;
  except
    on E: Exception do
    begin
      raise EKontrolleErstellenError.Create('In der Zeile ' + IntToStr(Kontrollzeile.Zeilennummer) +
        ' gab es ein Problem beim Importieren. Bis zu dieser Zeile wurden die Kontrollen erfolgreich erstellt.' +
        sLineBreak + E.Message);
    end;
  end;
end;

{

  service := Usersession.ELKERest.GetMeService;
  try
  raise Exception.Create('Es gibt noch keinen Kontrolltypen.');
  // kontrolltyp := kontrolltypen[jqcbKontrolltyp.SelectedIndex - 1];
  // rechtsgrundlage := rechtsgrundlagen[jqcbKontrollgrund.SelectedIndex - 1];

  factory := TObjectFactory.Create;

  todo := factory.Instance.Create<TTodo>;
  todo.Faellig := StrToDate('01/' + fields[12] + '/' + jqeJahr.Text);
  DateTimeToString(formatedDate, 'dd.mm.yyyy', todo.Faellig);
  todo.Titel := Kontrolltyp.Bkbtyp.Typ + ' ' + fields[3] + ' ' + formatedDate;

  //gruppe := FindeGruppe();
  todo.gruppe := factory.Instance.Create<TGruppe>;
  todo.gruppe.Id := gruppe.Id;

  // Spezielle Art des Constructor Aufrufs beachten!
  kb := factory.Instance.Create<TKontrollbericht>;
  // Kontrollbericht ausfüllen
  kb.Kontrolltyp := Kontrolltyp;
  kb.rechtsgrundlage := rechtsgrundlage;
  kb.Betrieb := factory.Instance.Create<TBetrieb>;
  kb.Betrieb.Id := idBetrieb;
  if fields.Count > 13 then
  begin
  kb.Kurzbemerkung := fields[13];
  end;

  // Kontrollbericht abspeichern
  service.NeuerUngeplanterKontrollBericht(kb, todo);

  success := true;
  except
  on E: Exception do
  begin
  jqsAlert.Error('Es gab einen Fehler beim Erstellen des Kontrollberichts.' +
  sLineBreak + E.Message);
  success := false;
  end;
  end;
}

function TVisOExCSVImport.ExistiertBetrieb(regnr: string): integer;
begin
  quExistiertBetrieb.Close;
  quExistiertBetrieb.ParamByName('lfbis').AsString := regnr;
  quExistiertBetrieb.Open;
  if quExistiertBetrieb.RecordCount > 0 then
  begin
    Result := quExistiertBetrieb.FieldByName('id').AsInteger;
  end
  else
  begin
    Result := -1;
  end;
end;

procedure TVisOExCSVImport.FileKKImportJQFileUploadOptionsComplete(Sender: TObject; AParams: TStringList);
begin
  //KK-Datei auf MKS Einträge scannen und die entsprechenden Kontrollen updaten
  var LZeilen := TStringList.Create;
  var LFelder := TStringList.Create;
  LFelder.Delimiter := ';';
  LFelder.StrictDelimiter := true;
  var LQuery := TFDQuery.Create(nil);
  var LImportCount := 0;
  var LProcessedCount := 0;
  try
    var LFilename := AParams.Values['fileName'];
    LFilename := TPath.Combine(TPath.GetFullPath(TIWCGJQFileUploadOptions(Sender).UploadPath), LFilename);
    LZeilen.LoadFromFile(LFilename);

    LQuery.Connection := dm_main.FBC_MAIN;
    LQuery.SQL.Text := 'UPDATE Bewegungsdaten.Kontrollbericht SET Kontroll_Informationen = :Info WHERE BKB = :BKB';

    for var i := 0 to LZeilen.Count - 1 do // Keine Kopfzeile, also ab 0
    begin
      var LZeile := LZeilen[i];
      LFelder.DelimitedText := LZeile;
      if LFelder.Count >= 4 then
      begin
        var LBKB := Trim(LFelder[2]).ToUpper; // Spalte 3 (Index 2)
        var LKontrolltyp := Trim(LFelder[3]).ToUpper; // Spalte 4 (Index 3)

        if LKontrolltyp = 'MKS' then
        begin
          ELKELog('MKS Update : ' + LBKB);
          LQuery.ParamByName('Info').AsString := 'Probenname MKS';
          LQuery.ParamByName('BKB').AsString := LBKB;
          LQuery.ExecSQL;
          LImportCount := LImportCount + LQuery.RowsAffected;
          inc(LProcessedCount);
        end;
      end;
    end;
  finally
    LZeilen.Free;
    LFelder.Free;
    LQuery.Free;
  end;
  Alert.Success(Format('Es wurden %d MKS BKBs verarbeitet, davon wurden %d importiert!',
    [LProcessedCount, LImportCount]));
end;

procedure TVisOExCSVImport.UploadGetFileName(Sender: TObject; var APath, AFileName: string);
begin
  //Sicherstellen, dass NUR in den UploadPath hochgeladen werden kann. Keine ../../ tricks ...
  APath := TPath.GetFullPath(TIWCGJQFileUpload(Sender).JQFileUploadOptions.UploadPath) + '\';
  AFileName := TPath.GetFileName(AFileName);
end;

end.

