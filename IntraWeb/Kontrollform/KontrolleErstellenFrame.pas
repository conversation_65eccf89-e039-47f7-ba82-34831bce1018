﻿unit KontrolleErstellenFrame;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40<PERSON>omponent, IWCGJQComp, IWCGJQSweetAlert,
  IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQDateTimePicker, IWCGJQComboBox, IWCGJQDatePicker,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQControl, IWCGJQButton, IWCGJQDialog,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  ELKE.Classes.Generated, IWCompCheckbox, IWCGJQCheckBox, IWCGJQGrid, IWCGJQEdit, IWCGJQLabel, Vcl.Controls, Vcl.Forms,
  IWCGJQRegion;

type
  TKontrolleErstellen = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    iwrKontrolleneingabe: TIWCGJQRegion;
    ButtonSpeichern: TIWCGJQButton;
    DatePickerAngemeldetUm: TIWCGJQDatePicker;
    ComboKontrollberichtTyp: TIWCGJQComboBoxEx;
    ComboKontrolltyp: TIWCGJQComboBoxEx;
    ComboKontrollgrund: TIWCGJQComboBoxEx;
    LabelBetrieb: TIWCGJQLabel;
    jqbBetrieb: TIWCGJQButton;
    jqdBetriebe: TIWCGJQDialog;
    iwrBetriebSuchen: TIWCGJQRegion;
    iwrBetriebe: TIWCGJQRegion;
    jqgBetriebe: TIWCGJQGrid;
    ProviderBetriebe: TIWCGJQGridDataSetProvider;
    IWLabel7: TIWCGJQLabel;
    jqeRegnr: TIWCGJQEdit;
    jqeName: TIWCGJQEdit;
    IWLabel8: TIWCGJQLabel;
    jqbBetriebSuchen: TIWCGJQButton;
    IWLabel9: TIWCGJQLabel;
    jqeStrasse: TIWCGJQEdit;
    IWLabel10: TIWCGJQLabel;
    jqePlz: TIWCGJQEdit;
    jqdtAngemeldetUm: TIWCGJQDateTimePicker;
    CheckboxAngemeldetUm: TIWCGJQCheckBox;
    jqaAlert: TIWCGJQSweetAlert;
    procedure jqcbKontrollberichtOnChange(Sender: TObject; AParams: TStringList);
    procedure jqbBetriebOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbBetriebSuchenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqcAngemeldetUmOnChange(Sender: TObject; EventParams: TStringList);
    procedure ButtonSpeichernOnClick(Sender: TObject; AParams: TStringList);
    procedure JqgBetriebeOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure JqcbKontrolltypOnChange(Sender: TObject; AParams: TStringList);
    procedure JqcbKontrollgrundOnChange(Sender: TObject; AParams: TStringList);
    procedure jqcbKontrollgrundJQEventsChange(Sender: TObject; AParams: TStringList);
    procedure DatePickerAngemeldetUmJQEventsBlur(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    idBetrieb: Integer;
    berichtstypen: TList<TBkbTyp>;
    FKontrolltypen: TList<TKontrolltyp>;
    FRechtsgrundlagen: TList<TRechtsgrundlage>;
    FSaveProc: TProc;
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure betriebAusgewaehlt(Sender: TObject; AURLParams: TStringList);
    procedure BKBTypAuswählen;
    procedure CheckObSpeichernErlaubt;
    function GetKontrollTyp(ATyp: string): TKontrolltyp;
    function GetKontrollGrund(AId: string): TRechtsgrundlage;
    procedure DoSave;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; ASaveProc: TProc); reintroduce;
    destructor Destroy; override;
    procedure ResetFields(resetBkbtyp: boolean = true; resetBetrieb: boolean = true);
  end;

implementation

uses ServerController, dmmain, Utility, System.DateUtils, XData.Client,
  ELKE.Services.Me.Intf, DX.Classes.ObjectFactory, ELKE.Classes.PVP.Token,
  IW.HTTP.Request, IWApplication, JQ.Helpers.Button, ELKE.Classes.RESTError;

{$R *.dfm}


constructor TKontrolleErstellen.Create(AOwner: TComponent; ASaveProc: TProc);
var
  bkbtyp: TBkbTyp;
  item: TIWCGJQComboBoxExItem;
  i: Integer;
begin
  inherited Create(AOwner);
  FSaveProc := ASaveProc;
  berichtstypen := Nil;
  FKontrolltypen := Nil;
  FRechtsgrundlagen := Nil;

  berichtstypen := Usersession.ELKERest.berichtstypen;

  ResetFields;
  // "LeerEintrag" hinzufügen und auswählen
  ComboKontrollberichtTyp.Items.AddOption('Bitte auswählen ...', '');
  ComboKontrollberichtTyp.SelectedIndex := 0;

  // Alle erlaubten Bkbtypen zur auswahl stellen
  for i := 0 to berichtstypen.Count - 1 do
  begin
    bkbtyp := berichtstypen[i];
    if bkbtyp.Probe = false then
    begin
      item := ComboKontrollberichtTyp.Items.Add;
      item.Caption := bkbtyp.Bezeichnung;
      item.Value := bkbtyp.Typ;
    end;
  end;

  ButtonSpeichern.Enabled := false;
  // Heutiges Datum setzen
  DatePickerAngemeldetUm.Date := Now;

  ButtonSpeichern.AsyncShowWaitWheel;
end;

procedure TKontrolleErstellen.DatePickerAngemeldetUmJQEventsBlur(Sender: TObject; AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

destructor TKontrolleErstellen.Destroy;
begin
  berichtstypen.Free;
  FKontrolltypen.Free;
  FRechtsgrundlagen.Free;
  inherited;
end;

procedure TKontrolleErstellen.DoSave;
begin
  if Assigned(FSaveProc) then
  begin
    FSaveProc;
  end;
end;

function TKontrolleErstellen.GetKontrollGrund(AId: string): TRechtsgrundlage;
begin
  result := nil;
  for var LKontrollgrund in FRechtsgrundlagen do
  begin
    if LKontrollgrund.Id.ToString = AId then
    begin
      result := LKontrollgrund;
      break;
    end;
  end;

end;

function TKontrolleErstellen.GetKontrollTyp(ATyp: string): TKontrolltyp;
begin
  result := nil;
  for var LKontrollTyp in FKontrolltypen do
  begin
    if LKontrollTyp.Kontrolltyp = ATyp then
    begin
      result := LKontrollTyp;
      break;
    end;
  end;
end;

procedure TKontrolleErstellen.BKBTypAuswählen;
var
  LKontrollTyp: TKontrolltyp;
  LRechtsgrundlage: TRechtsgrundlage;
  i: Integer;
  LBkbtyp: String;
begin
  if ComboKontrollberichtTyp.SelectedIndex = 0 then
  begin
    ResetFields(false, true);
    abort;
  end;

  LBkbtyp := ComboKontrollberichtTyp.SelectedItem.Value;

  // Alle erlaubten Bkbtypen zur Auswahl stellen
  FreeAndNil(FKontrolltypen);
  FKontrolltypen := Usersession.ELKERest.kontrolltypen(LBkbtyp);
  ComboKontrolltyp.Items.Clear;
  for i := 0 to FKontrolltypen.Count - 1 do
  begin
    LKontrollTyp := FKontrolltypen[i];
    if LKontrollTyp.Bezeichnung.HasValue then
    begin
      ComboKontrolltyp.Items.AddOption(LKontrollTyp.Bezeichnung, LKontrollTyp.Kontrolltyp);
    end;
  end;
  ComboKontrolltyp.JQComboBoxExOptions.RefreshItems;

  // Rechtsgrundlagen zur Auswahl stellen
  FreeAndNil(FRechtsgrundlagen);
  FRechtsgrundlagen := Usersession.ELKERest.rechtsgrundlagen(LBkbtyp);
  ComboKontrollgrund.Items.Clear;
  for i := 0 to FRechtsgrundlagen.Count - 1 do
  begin
    LRechtsgrundlage := FRechtsgrundlagen[i];
    ComboKontrollgrund.Items.AddOption(LRechtsgrundlage.Kurzbezeichnung, LRechtsgrundlage.Id.ToString);
  end;
  ComboKontrollgrund.JQComboBoxExOptions.RefreshItems;

  // Ausgeschalteten Felder einschalten
  CheckboxAngemeldetUm.Enabled := true;
  ComboKontrolltyp.Enabled := true;
  ComboKontrollgrund.Enabled := true;
  LabelBetrieb.Enabled := true;
  DatePickerAngemeldetUm.Enabled := true;
end;

procedure TKontrolleErstellen.CheckObSpeichernErlaubt;
begin
  ButtonSpeichern.Enabled := (idBetrieb <> -1) and (DatePickerAngemeldetUm.Date > 0) and
    (ComboKontrollberichtTyp.SelectedItem.Value > '');
end;

{ Kontrollbericht }
procedure TKontrolleErstellen.jqcbKontrollberichtOnChange(Sender: TObject; AParams: TStringList);
begin
  BKBTypAuswählen;
end;

procedure TKontrolleErstellen.jqcbKontrollgrundJQEventsChange(Sender: TObject; AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

procedure TKontrolleErstellen.JqcbKontrollgrundOnChange(Sender: TObject;
  AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

procedure TKontrolleErstellen.JqcbKontrolltypOnChange(Sender: TObject;
  AParams: TStringList);
begin
  CheckObSpeichernErlaubt;
end;

{ Öffnet den Dialog mit der Betriebssuche }
procedure TKontrolleErstellen.jqbBetriebOnClick(Sender: TObject;
  AParams: TStringList);
begin
  dm_main.quBetriebssuche.Close;
  jqeRegnr.Text := '';
  jqeName.Text := '';
  jqePlz.Text := '';
  jqeStrasse.Text := '';
  jqgBetriebe.JQGridOptions.ResetSelection;
  DialogErstellen(jqdBetriebe, betriebAusgewaehlt, jqdAbbrechenEvent,
    'Betrieb suchen');
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := true;
end;

// Wenn ein Betrieb ausgewählt wird, kann man wieder den auswählen Knopf drücken
procedure TKontrolleErstellen.JqgBetriebeOnSelectRow(Sender: TObject;
  AParams: TStringList);
begin
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := false;
end;

{ Sucht im Dialog nach einem Betrieb }
procedure TKontrolleErstellen.jqbBetriebSuchenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if (jqeStrasse.Text.Length < 2) and (jqeRegnr.Text.Length < 2) and (jqeName.Text.Length < 2) and
    (jqePlz.Text.Length < 2) then
  begin
    jqaAlert.Info('Sie müssen zuerst Suchparameter eingeben');
    Exit;
  end;
  jqgBetriebe.JQGridOptions.ResetSelection;
  jqdBetriebe.JQDialogOptions.Buttons.Items[0].Disabled := true;
  dm_main.quBetriebssuche.Close;
  dm_main.quBetriebssuche.Prepare;
  dm_main.quBetriebssuche.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  dm_main.quBetriebssuche.ParamByName('strasse').AsString := jqeStrasse.Text;
  dm_main.quBetriebssuche.ParamByName('regnr').AsString := jqeRegnr.Text;
  dm_main.quBetriebssuche.ParamByName('name').AsString := jqeName.Text;
  dm_main.quBetriebssuche.ParamByName('plz').AsString := jqePlz.Text;
  dm_main.quBetriebssuche.Active := true;
end;

{ Wenn der User den Betriebssuchen Dialog bestätigt. }
procedure TKontrolleErstellen.betriebAusgewaehlt(Sender: TObject; AURLParams: TStringList);
begin
  jqdBetriebe.Visible := false;
  if not moveQueryToRow(dm_main.quBetriebssuche, jqgBetriebe) then
    Exit;
  LabelBetrieb.Caption := dm_main.quBetriebssuche.FieldByName('name').AsString;
  idBetrieb := dm_main.quBetriebssuche.FieldByName('id').AsInteger;
  CheckObSpeichernErlaubt
end;

procedure TKontrolleErstellen.jqcAngemeldetUmOnChange(Sender: TObject;
  EventParams: TStringList);
begin
  if CheckboxAngemeldetUm.Checked then
  begin
    jqdtAngemeldetUm.Visible := true;
    jqdtAngemeldetUm.DateTime := Now;
  end
  else
  begin
    jqdtAngemeldetUm.Visible := false;
  end;
  CheckObSpeichernErlaubt;
end;

{ Speichert die eingegebene Kontrolle auf dem Server }
procedure TKontrolleErstellen.ButtonSpeichernOnClick(Sender: TObject;
  AParams: TStringList);
var
  LRechtsgrundlage: TRechtsgrundlage;
  LKontrollTyp: TKontrolltyp;

  service: IMe;
  kb: TKontrollbericht;
  factory: IObjectFactory;

begin
  if idBetrieb = -1 then
  begin
    jqaAlert.Warning('Es muss ein Betrieb ausgewählt werden.');
    Exit;
  end;
  if (ComboKontrolltyp.SelectedItem.Value = '') or
    (ComboKontrollgrund.SelectedItem.Value = '') then
  begin
    jqaAlert.Warning('Es muss ein Kontrolltyp und eine Rechtsgrundlage ' +
      'ausgewählt werden.');
    Exit;
  end;

  service := Usersession.ELKERest.GetMeService;
  try
    LKontrollTyp := GetKontrollTyp(ComboKontrolltyp.SelectedItem.Value);
    LRechtsgrundlage := GetKontrollGrund(ComboKontrollgrund.SelectedItem.Value);

    factory := TObjectFactory.Create;

    // Spezielle Art des Constructor Aufrufs beachten!
    kb := factory.Instance.Create<TKontrollbericht>;
    // Kontrollbericht ausfüllen
    kb.Datum := DatePickerAngemeldetUm.Date;
    kb.Kontrolltyp := LKontrollTyp;
    kb.rechtsgrundlage := LRechtsgrundlage;
    kb.Betrieb := factory.Instance.Create<TBetrieb>;
    kb.Betrieb.Id := idBetrieb;
    if CheckboxAngemeldetUm.Checked then
    begin
      kb.AngemeldetUm := jqdtAngemeldetUm.DateTime;
    end;

    // Kontrollbericht abspeichern
    service.NeuerKontrollBericht(kb);

  except
    on E: Exception do
    begin
      jqaAlert.Error('Es gab einen Fehler beim Erstellen des Kontrollberichts.' +
        sLineBreak + E.Message);
      abort;
    end;
  end;

  jqaAlert.Info('Kontrollbericht wurde erfolgreich erstellt!');
  DoSave;
end;

procedure TKontrolleErstellen.ResetFields(resetBkbtyp: boolean = true;
  resetBetrieb: boolean = true);
begin
  if resetBkbtyp then
    ComboKontrollberichtTyp.JQComboBoxExOptions.SetSelIndex(0);
  if resetBetrieb then
  begin
    LabelBetrieb.Caption := '';
    LabelBetrieb.Enabled := false;
    idBetrieb := -1;
  end;

  ComboKontrolltyp.JQComboBoxExOptions.RemoveALLOptions;
  ComboKontrollgrund.JQComboBoxExOptions.RemoveALLOptions;
  DatePickerAngemeldetUm.Date := Now;
  CheckboxAngemeldetUm.Checked := false;

  CheckboxAngemeldetUm.Enabled := false;
  ComboKontrolltyp.Enabled := false;
  ComboKontrollgrund.Enabled := false;
  DatePickerAngemeldetUm.Enabled := false;
  jqdtAngemeldetUm.Visible := false;

  ButtonSpeichern.Enabled := false;
end;

procedure TKontrolleErstellen.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdBetriebe.Visible := false;
end;

end.
