﻿unit Dialogs.GeplanteKontrolleBearbeiten;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQDatePicker, IWCGJQDateTimePicker,
  IWCompCheckbox, IWCGJQCheckBox, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQComboBox, Modules.Kontrolle, System.Generics.Collections, ELKE.Classes.Generated, IWCGJQLabel, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHT<PERSON><PERSON><PERSON>omponent, IWCGJQ<PERSON>om<PERSON>, IWCGJ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IWCGJQ<PERSON>emo, IWCompMemo,
  Aurelius.Types.Nullable;

type
  //TDialogGeplanteKontrolleBearbeiten = class(TDialogBase)
  TDialogGeplanteKontrolleBearbeiten = class(TDialogBase<TDMKontrolle>)
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    jqcAngemeldetUm: TIWCGJQCheckBox;
    DateAngemeldetUm: TIWCGJQDateTimePicker;
    LabelBkbtyp: TIWCGJQLabel;
    ComboBoxKontrollgrund: TIWCGJQComboBoxEx;
    DateGeplantesDatum: TIWCGJQDatePicker;
    ComboKontrolltyp: TIWCGJQComboBoxEx;
    MemoBetrieb: TIWCGJQMemoEx;
    IWCGJQLabel1: TIWCGJQLabel;
    MemoKontrollInformationen: TIWCGJQMemoEx;
    IWCGJQLabel2: TIWCGJQLabel;
    MemoInterneNotizen: TIWCGJQMemoEx;
    procedure AngemeldetOnAsyncChange(Sender: TObject; EventParams: TStringList);
  private
    Kontrollbericht: TKontrollbericht;
    rechtsgrundlagen: TList<TRechtsgrundlage>;
    FKontrolltypen: TList<TKontrolltyp>;
  public
    constructor Create(AOwner: TComponent; IDKontrollbericht: integer); reintroduce;
    destructor Destroy; override;
    procedure InitializeControls; override;
    procedure Save;
  end;

implementation

{$R *.dfm}


uses Utility, ServerController;

constructor TDialogGeplanteKontrolleBearbeiten.Create(AOwner: TComponent; IDKontrollbericht: integer);
var
  i, current: integer;
  rechtsgrundlage: TRechtsgrundlage;
  s: string;
begin
  self := inherited Create(AOwner);
  if IDKontrollbericht > -1 then
  begin
    Kontrollbericht := DM.GetKontrollbericht(IDKontrollbericht);
    LabelBkbtyp.Caption := Kontrollbericht.Kontrolltyp.Bkbtyp.Typ;

    FKontrolltypen := UserSession.ELKERest.GetKontrolltypenForBkbtyp(LabelBkbtyp.Caption);
    ComboKontrolltyp.Items.Clear;
    for var LKontrolltyp in FKontrolltypen do
    begin
      var
      LItem := ComboKontrolltyp.Items.Add;
      LItem.Value := LKontrolltyp.Kontrolltyp;
      LItem.Caption := LKontrolltyp.Bezeichnung;
      LItem.UserData := LKontrolltyp;
    end;
    ComboKontrolltyp.SelectByValue(Kontrollbericht.Kontrolltyp.Kontrolltyp);
    Assert(ComboKontrolltyp.SelectedIndex > -1);
    ComboKontrolltyp.AjaxReRender;
    MemoBetrieb.ClearLines;
    try
      s := Kontrollbericht.Betrieb.Adresse.Plz + ', ' + Kontrollbericht.Betrieb.Adresse.Ort;
      MemoBetrieb.Lines.Add(Kontrollbericht.Betrieb.Name);
      MemoBetrieb.Lines.Add(Kontrollbericht.Betrieb.Adresse.Strasse);
      if s <> ', ' then
      begin
        MemoBetrieb.Lines.Add(s);
      end;
      if Kontrollbericht.Betrieb.Registrierung <> nil then
      begin
        MemoBetrieb.Lines.Add(Kontrollbericht.Betrieb.Registrierung.Regnr);
      end;
    except
      on E: Exception do Alert.Warning('Beim Bearbeiten ist ein Fehler aufgetreten' + sLineBreak + E.Message);
    end;

    if Kontrollbericht.Datum.HasValue then
      DateGeplantesDatum.Date := Kontrollbericht.Datum.Value;

    MemoKontrollInformationen.Text := Kontrollbericht.KontrollInformationen.ValueOrDefault;
    MemoInterneNotizen.Text := Kontrollbericht.InterneNotiz.ValueOrDefault;

    if Kontrollbericht.AngemeldetUm.HasValue then
    begin
      DateAngemeldetUm.DateTime := Kontrollbericht.AngemeldetUm.Value;
      jqcAngemeldetUm.Checked := true;
    end
    else
    begin
      jqcAngemeldetUm.Checked := false;
    end;
    DateAngemeldetUm.Enabled := jqcAngemeldetUm.Checked;
    ComboBoxKontrollgrund.Items.Clear;
    current := -1;
    rechtsgrundlagen := UserSession.ELKERest.rechtsgrundlagen(Kontrollbericht.Kontrolltyp.Bkbtyp.Typ);
    for i := 0 to rechtsgrundlagen.Count - 1 do
    begin
      rechtsgrundlage := rechtsgrundlagen[i];
      ComboBoxKontrollgrund.Items.AddOption(rechtsgrundlage.Kurzbezeichnung, IntToStr(i));
      if rechtsgrundlage.Id = Kontrollbericht.rechtsgrundlage.Id then
      begin
        current := i;
      end;
    end;
    if current <> -1 then
    begin
      ComboBoxKontrollgrund.SelectedIndex := current;
    end;
    ComboBoxKontrollgrund.AjaxReRender;
  end;
end;

destructor TDialogGeplanteKontrolleBearbeiten.Destroy;
begin
  rechtsgrundlagen.Free;
  FKontrolltypen.Free;
  inherited;
end;

procedure TDialogGeplanteKontrolleBearbeiten.AngemeldetOnAsyncChange(Sender: TObject; EventParams: TStringList);
begin
  inherited;
  DateAngemeldetUm.Enabled := jqcAngemeldetUm.Checked;
end;

procedure TDialogGeplanteKontrolleBearbeiten.InitializeControls;
begin
  inherited;
   //todo 1 -oCH -cImplementieren: Felder initialisieren
end;

procedure TDialogGeplanteKontrolleBearbeiten.Save;
begin
  if jqcAngemeldetUm.Checked then
  begin
    Kontrollbericht.AngemeldetUm := DateAngemeldetUm.DateTime;
  end
  else
  begin
    Kontrollbericht.AngemeldetUm := sNull;
  end;
  Kontrollbericht.Datum := DateGeplantesDatum.Date;
  Kontrollbericht.rechtsgrundlage := rechtsgrundlagen[ComboBoxKontrollgrund.SelectedIndex];
  Kontrollbericht.Kontrolltyp := TKontrolltyp(ComboKontrolltyp.SelectedItem.UserData);
  Kontrollbericht.KontrollInformationen := MemoKontrollInformationen.Lines.Text.Trim;
  Kontrollbericht.InterneNotiz := MemoInterneNotizen.Lines.Text.Trim;

  UserSession.ELKERest.KontrollberichtBearbeiten(Kontrollbericht);
end;

end.
