﻿unit Forms.Kontrollen;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Forms.Base, IWCGJQButton,
  Vcl.Imaging.jpeg, IWCompExtCtrls, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, IWCompTabControl,
  Frames.Kontrollverlauf, KontrolleErstellenFrame,
  Frames.UngeplanteKontrolle, System.Generics.Collections,
  Frames.GeplanteKontrollen, Frames.UngeplanteKontrollen, UserSessionUnit, Mo<PERSON>les.<PERSON><PERSON><PERSON><PERSON>, IWCGJ<PERSON>abel, IWCGJQTabs,
  IWCGJQComp, IWCGJQSweetAlert,

  CSVImportFrame,
  VisOExCSVImportFrame;

type
  TFormKontrollen = class(TFormBase)
    iwrMid: TIWCGJQRegion;
    TabsKontrollen: TIWCGJQTabs;
    TabKontrollverlauf: TIWCGJQTab;
    TabGeplanteKontrollen: TIWCGJQTab;
    TabUngeplanteKontrollen: TIWCGJQTab;
    TabCSVImport: TIWCGJQTab;
    TabVisCSVImport: TIWCGJQTab;
    procedure IWAppFormCreate(Sender: TObject);
    procedure jqbZurueckOnClick(Sender: TObject; AParams: TStringList);
    procedure TabsKontrollenJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FDM: TDMKontrolle;
    aktiveTabs: TList<String>;
    kontrolluebersicht: TFrameKontrollverlauf;
    geplanteKontrollen: TFrameGeplanteKontrollen;
    UngeplanteKontrollen: TUngeplanteKontrollen;
    CsvImport: TCSVImport;
    VISCsvImport: TVisOExCSVImport;
  public
    { Public declarations }
    destructor Destroy; override;
    function DM: TDMKontrolle;
  end;

implementation

uses Funktionen, ServerController, StrUtils;

{$R *.dfm}


procedure TFormKontrollen.IWAppFormCreate(Sender: TObject);
var
  Funktionen: TFunktionenManager;
begin
  inherited;
  FDM := TDMKontrolle.Create(self);
  Funktionen := Usersession.FunktionenManager;
  if Funktionen.HatFunktion(Kontrollen_anzeigen) then
  begin
    TabKontrollverlauf.Visible := true;
    kontrolluebersicht := TFrameKontrollVerlauf.Create(self, FDM);
    kontrolluebersicht.Parent := TabKontrollverlauf;

    TabGeplanteKontrollen.Visible := true;
    geplanteKontrollen := TFrameGeplanteKontrollen.Create(self, FDM);
    geplanteKontrollen.Parent := TabGeplanteKontrollen;

    TabUngeplanteKontrollen.Visible := true;
    UngeplanteKontrollen := TUngeplanteKontrollen.Create(self, FDM);
    UngeplanteKontrollen.Parent := TabUngeplanteKontrollen;
  end;
  if Funktionen.HatFunktion(Kontrolle_anlegen) then
  begin
    // TabUngeplanteKontrolle.Visible := true;
    // ungeplanteKontrolle := TUngeplanteKontrolleErstellen.Create(Self);
    // ungeplanteKontrolle.Parent := TabUngeplanteKontrolle;
    // aktiveTabs.Add('UErstellen');
  end;

  TabCSVImport.Visible := true;
  CsvImport := TCSVImport.Create(self);
  CsvImport.Parent := TabCSVImport;

  TabVisCSVImport.Visible := true;
  VISCsvImport := TVisOExCSVImport.Create(self);
  VISCsvImport.Parent := TabVisCSVImport;

  for var i := 0 to TabsKontrollen.TabCount - 1 do
  begin
    if TabsKontrollen.Tabs[i].Visible then
    begin
      TabsKontrollen.ActiveTab := TabsKontrollen.Tabs[i];
      break;
    end;
  end;
end;

destructor TFormKontrollen.Destroy;
begin
  aktiveTabs.Free;
  inherited;
end;

procedure TFormKontrollen.TabsKontrollenJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
begin

  if TabsKontrollen.ActiveTab = TabKontrollverlauf then
  begin
    // kontrolluebersicht.Search;
  end
  else if TabsKontrollen.ActiveTab = TabGeplanteKontrollen then
  begin
    // geplanteKontrollen.Search;
  end
  else if TabsKontrollen.ActiveTab = TabUngeplanteKontrollen then
  begin
    // UngeplanteKontrollen.Search;
  end
  else if TabsKontrollen.ActiveTab = TabCSVImport then
  begin
    //
  end;
end;

function TFormKontrollen.DM: TDMKontrolle;
begin
  result := FDM;
end;

procedure TFormKontrollen.jqbZurueckOnClick(Sender: TObject;
  AParams: TStringList);
begin
  Release;
end;

end.
