object KontrolleErstellen: TKontrolleErstellen
  Left = 0
  Top = 0
  Width = 1137
  Height = 714
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 1137
    Height = 714
    RenderInvisibleControls = True
    TabOrder = 11
    Version = '1.0'
    Align = alClient
    DesignSize = (
      1137
      714)
    object jqdBetriebe: TIWCGJQDialog
      Left = 40
      Top = 16
      Width = 800
      Height = 400
      Visible = False
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 400
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 800
      JQDialogOptions.zIndex = 5000
      object iwrBetriebSuchen: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 800
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 12
        Version = '1.0'
        Align = alTop
        object IWLabel7: TIWCGJQLabel
          Left = 3
          Top = 3
          Width = 42
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Regnr:'
        end
        object IWLabel8: TIWCGJQLabel
          Left = 167
          Top = 3
          Width = 41
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Name:'
        end
        object IWLabel10: TIWCGJQLabel
          Left = 493
          Top = 3
          Width = 23
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Plz:'
        end
        object IWLabel9: TIWCGJQLabel
          Left = 329
          Top = 3
          Width = 47
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel7'
          Caption = 'Stra'#223'e:'
        end
        object jqeRegnr: TIWCGJQEdit
          Left = 3
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 2
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqeName: TIWCGJQEdit
          Left = 167
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 3
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqbBetriebSuchen: TIWCGJQButton
          Left = 659
          Top = 25
          Width = 75
          Height = 21
          TabOrder = 4
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.OnEvent = jqbBetriebSuchenOnClick
        end
        object jqeStrasse: TIWCGJQEdit
          Left = 329
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 5
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
        object jqePlz: TIWCGJQEdit
          Left = 493
          Top = 25
          Width = 150
          Height = 21
          TabOrder = 6
          Version = '1.0'
          ZIndex = 5001
          ScriptEvents = <>
          Text = ''
        end
      end
      object iwrBetriebe: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 800
        Height = 340
        RenderInvisibleControls = True
        TabOrder = 13
        Version = '1.0'
        Align = alClient
        object jqgBetriebe: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 800
          Height = 340
          TabOrder = 1
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Regnr'
              Name = 'Regnr'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Regnr'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NAME'
              Name = 'NAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'STRASSE'
              Name = 'STRASSE'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Stra'#223'e'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'PLZ'
              Name = 'PLZ'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'PLZ'
            end>
          JQGridOptions.Height = 313
          JQGridOptions.RowNum = 200
          JQGridOptions.Sortable = True
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 798
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridOptions.OnSelectRow.OnEvent = JqgBetriebeOnSelectRow
          JQGridOptions.PagerVisible = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = ProviderBetriebe
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object iwrKontrolleneingabe: TIWCGJQRegion
      Left = 235
      Top = 157
      Width = 680
      Height = 400
      RenderInvisibleControls = True
      TabOrder = 14
      Version = '1.0'
      Anchors = []
      object IWLabel1: TIWCGJQLabel
        Left = 11
        Top = 24
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollbericht Typ:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 11
        Top = 64
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontroll Typ:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 11
        Top = 104
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Betrieb:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 11
        Top = 176
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollgrund:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 11
        Top = 216
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Geplantes Datum:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 11
        Top = 256
        Width = 170
        Height = 19
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Angemeldet:'
      end
      object LabelBetrieb: TIWCGJQLabel
        Left = 201
        Top = 104
        Width = 420
        Height = 66
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelBetrieb'
      end
      object CheckboxAngemeldetUm: TIWCGJQCheckBox
        Left = 201
        Top = 256
        Width = 24
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 8
        OnAsyncChange = jqcAngemeldetUmOnChange
        Checked = False
        FriendlyName = 'CheckboxAngemeldetUm'
      end
      object ButtonSpeichern: TIWCGJQButton
        Left = 521
        Top = 304
        Width = 104
        Height = 33
        TabOrder = 9
        Version = '1.0'
        JQButtonOptions.Label_ = 'Speichern'
        JQButtonOptions.OnClick.OnEvent = ButtonSpeichernOnClick
      end
      object DatePickerAngemeldetUm: TIWCGJQDatePicker
        Left = 201
        Top = 216
        Width = 420
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQEvents.OnBlur.OnEvent = DatePickerAngemeldetUmJQEventsBlur
      end
      object ComboKontrollberichtTyp: TIWCGJQComboBoxEx
        Left = 201
        Top = 24
        Width = 420
        Height = 21
        TabOrder = 15
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = jqcbKontrollberichtOnChange
        JQComboBoxExOptions.PositionOptions.Enable = True
        JQComboBoxExOptions.PositionOptions.Collision = 'none none'
        Caption = ''
      end
      object ComboKontrolltyp: TIWCGJQComboBoxEx
        Left = 201
        Top = 64
        Width = 420
        Height = 21
        TabOrder = 16
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = JqcbKontrolltypOnChange
        Caption = ''
      end
      object ComboKontrollgrund: TIWCGJQComboBoxEx
        Left = 201
        Top = 176
        Width = 420
        Height = 21
        TabOrder = 17
        Version = '1.0'
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 418
        JQComboBoxExOptions.OnChange.OnEvent = JqcbKontrollgrundOnChange
        Caption = ''
        JQEvents.OnChange.OnEvent = jqcbKontrollgrundJQEventsChange
      end
      object jqbBetrieb: TIWCGJQButton
        Left = 640
        Top = 102
        Width = 30
        Height = 21
        TabOrder = 18
        Version = '1.0'
        JQButtonOptions.Icons.Primary = 'ui-icon-pencil'
        JQButtonOptions.OnClick.OnEvent = jqbBetriebOnClick
      end
      object jqdtAngemeldetUm: TIWCGJQDateTimePicker
        Left = 231
        Top = 256
        Width = 389
        Height = 21
        TabOrder = 8
        Css = 'ui-widget ui-widget-content ui-corner-all'
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorheriger'
        JQDatePickerOptions.Regional = dporGerman
      end
    end
  end
  object ProviderBetriebe: TIWCGJQGridDataSetProvider
    DataSet = dm_main.quBetriebssuche
    KeyFields = 'ID'
    Left = 888
    Top = 56
  end
  object jqaAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 768
    Top = 16
  end
end
