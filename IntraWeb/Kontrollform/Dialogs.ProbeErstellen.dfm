﻿inherited DialogProbeErstellen: TDialogProbeErstellen
  Width = 864
  Height = 741
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 864
    Height = 741
    TabOrder = 20
    TabStop = False
    JQDialogOptions.Height = 741
    JQDialogOptions.Width = 864
    inherited RegionContent: TIWCGJQRegion
      Width = 864
      Height = 681
      TabOrder = 19
      TabStop = False
      object RegionMid: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 864
        Height = 681
        TabOrder = 18
        Version = '1.0'
        Align = alClient
        TabStop = False
        object RegionLeft: TIWCGJQRegion
          Left = 19
          Top = 14
          Width = 306
          Height = 694
          TabOrder = 21
          Font.Size = 11
          Version = '1.0'
          TabStop = False
          object LabelBeschaffenheit: TIWCGJQLabel
            Left = 20
            Top = 378
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelBeschaffenheit'
            Caption = 'Beschaffenheit:'
          end
          object LabelProbenverpackung: TIWCGJQLabel
            Left = 20
            Top = 409
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelProbenverpackung'
            Caption = 'Verpackung der Probe:'
          end
          object LabelProbenverschluss: TIWCGJQLabel
            Left = 20
            Top = 440
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelProbenverschluss'
            Caption = 'Verschluss der Probe:'
          end
          object LabelVersiegelt: TIWCGJQLabel
            Left = 20
            Top = 471
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelVersiegelt'
            Caption = 'Versiegelt:'
          end
          object LabelHerkunft: TIWCGJQLabel
            Left = 20
            Top = 495
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'Herkung:'
            Caption = 'Herkunft:'
          end
          object LabelSackanhänger: TIWCGJQLabel
            Left = 20
            Top = 526
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Sackanh'#228'nger liegt bei:'
          end
          object LabelGegenprobe: TIWCGJQLabel
            Left = 20
            Top = 581
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Gegenprobe am Betrieb belassen:'
          end
          object LabelZukauf: TIWCGJQLabel
            Left = 20
            Top = 550
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Herkunft - Zukauf:'
          end
          object LabelBemerkung: TIWCGJQLabel
            Left = 20
            Top = 634
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Bemerkung:'
          end
          object LabelProbenbeschreibung: TIWCGJQLabel
            Left = 20
            Top = 68
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Probenbeschreibung:'
          end
          object LabelVerdacht: TIWCGJQLabel
            Left = 20
            Top = 130
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelVerdacht'
            Caption = 'Verdacht:'
          end
          object LabelProbenart: TIWCGJQLabel
            Left = 20
            Top = 99
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel2'
            Caption = 'Probenart:'
          end
          object LabelUParameter: TIWCGJQLabel
            Left = 20
            Top = 161
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelUParameter'
            Caption = 'Gew'#252'nschte Untersuchungsparameter:'
          end
          object LabelFuttertyp: TIWCGJQLabel
            Left = 20
            Top = 192
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelFuttertyp'
            Caption = 'Futtermitteltyp:'
          end
          object LabelBeimischrate: TIWCGJQLabel
            Left = 20
            Top = 223
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelBeimischrate'
            Caption = 'Beimischrate in %:'
          end
          object LabelEinzelfutterart: TIWCGJQLabel
            Left = 20
            Top = 254
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelEinzelfutterart'
            Caption = 'Einzelfutterart:'
          end
          object LabelTierart: TIWCGJQLabel
            Left = 20
            Top = 285
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelTierart'
            Caption = 'Tierart:'
          end
          object LabelTierkategorie: TIWCGJQLabel
            Left = 20
            Top = 316
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelTierkategorie'
            Caption = 'Tierkategorie:'
          end
          object LabelMenge: TIWCGJQLabel
            Left = 20
            Top = 347
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelMenge'
            Caption = 'vorgefundene Menge:'
          end
          object LabelProbendatum: TIWCGJQLabel
            Left = 20
            Top = 37
            Width = 270
            Height = 25
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel14'
            Caption = 'Probendatum:'
          end
          object LabelProbenkennung: TIWCGJQLabel
            Left = 185
            Top = 9
            Width = 105
            Height = 18
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'LabelProbenkennung'
            Caption = 'Probenkennung:'
          end
          object LabelGegenprobeKennzeichnung: TIWCGJQLabel
            Left = 104
            Top = 608
            Width = 186
            Height = 18
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 11
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'LabelGegenprobe'
            Caption = 'Kennzeichnung Gegenprobe:'
          end
        end
        object RegionRight: TIWCGJQRegion
          Left = 331
          Top = 9
          Width = 382
          Height = 702
          TabOrder = 17
          Version = '1.0'
          object ComboBeschaffenheit: TIWCGJQComboBoxEx
            Left = 3
            Top = 383
            Width = 367
            Height = 25
            TabOrder = 9
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            Caption = ''
          end
          object ComboEinzelfutterart: TIWCGJQComboBoxEx
            Left = 3
            Top = 259
            Width = 367
            Height = 25
            TabOrder = 5
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            Caption = ''
          end
          object ComboFuttermitteltyp: TIWCGJQComboBoxEx
            Left = 3
            Top = 197
            Width = 367
            Height = 25
            TabOrder = 2
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            JQComboBoxExOptions.OnSelect.OnEvent = ComboFuttermitteltypSelect
            Caption = ''
          end
          object ComboHerkunft: TIWCGJQComboBoxEx
            Left = 3
            Top = 500
            Width = 367
            Height = 25
            TabOrder = 12
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            JQComboBoxExOptions.OnSelect.OnEvent = ComboHerkunftSelect
            Caption = ''
          end
          object ComboTierart: TIWCGJQComboBoxEx
            Left = 3
            Top = 290
            Width = 367
            Height = 25
            TabOrder = 6
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            JQComboBoxExOptions.OnSelect.OnEvent = ComboTierartSelect
            Caption = ''
          end
          object ComboTierkategorie: TIWCGJQComboBoxEx
            Left = 3
            Top = 321
            Width = 367
            Height = 25
            TabOrder = 7
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            Caption = ''
          end
          object ComboVerpackung: TIWCGJQComboBoxEx
            Left = 3
            Top = 414
            Width = 367
            Height = 25
            TabOrder = 10
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            Caption = ''
          end
          object ComboVerschluss: TIWCGJQComboBoxEx
            Left = 3
            Top = 445
            Width = 367
            Height = 25
            TabOrder = 11
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            Caption = ''
          end
          object ComboProbenart: TIWCGJQComboBoxEx
            Left = 3
            Top = 104
            Width = 367
            Height = 25
            TabOrder = 24
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            Items = <>
            Groups = <>
            SelectedIndex = 0
            JQComboBoxExOptions.Width = 365
            JQComboBoxExOptions.OnSelect.OnEvent = ComboProbenartSelect
            Caption = ''
          end
          object jqeBeimischrate: TIWCGJQEdit
            Left = 3
            Top = 228
            Width = 369
            Height = 25
            TabOrder = 4
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeBemerkung: TIWCGJQEdit
            Left = 3
            Top = 639
            Width = 369
            Height = 25
            TabOrder = 14
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeGewUnt: TIWCGJQEdit
            Left = 3
            Top = 166
            Width = 369
            Height = 25
            TabOrder = 3
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeHerkZukauf: TIWCGJQEdit
            Left = 3
            Top = 555
            Width = 369
            Height = 25
            TabOrder = 13
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeProbenbezeichnung: TIWCGJQEdit
            Left = 3
            Top = 73
            Width = 369
            Height = 25
            TabOrder = 25
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeVerdacht: TIWCGJQEdit
            Left = 3
            Top = 135
            Width = 369
            Height = 25
            TabOrder = 1
            Version = '1.0'
            TabIndex = True
            ScriptEvents = <>
            Text = ''
          end
          object jqeVorgMenge: TIWCGJQEdit
            Left = 3
            Top = 352
            Width = 369
            Height = 25
            TabOrder = 8
            Version = '1.0'
            ScriptEvents = <>
            Text = ''
          end
          object DatepickerProbendatum: TIWCGJQDatePicker
            Left = 3
            Top = 42
            Width = 369
            Height = 25
            TabOrder = 23
            Version = '1.0'
            Caption = ''
            JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
          end
          object jqeProbenkennung: TIWCGJQEdit
            Left = 3
            Top = 11
            Width = 369
            Height = 25
            TabOrder = 26
            Version = '1.0'
            MaxLength = 24
            ScriptEvents = <>
            Text = ''
          end
          object jqeGegenprobe: TIWCGJQEdit
            Left = 3
            Top = 608
            Width = 369
            Height = 25
            TabOrder = 27
            Version = '1.0'
            MaxLength = 24
            ScriptEvents = <>
            Text = ''
          end
          object iwcGegenprobe: TIWCGJQCheckBoxEx
            Left = 3
            Top = 586
            Width = 100
            Height = 21
            TabOrder = 28
            Version = '1.0'
            Caption = ''
            JQCheckExOptions.OnChange.OnEvent = iwcGegenprobeJQCheckExOptionsChange
          end
          object iwcSackanhaenger: TIWCGJQCheckBoxEx
            Left = 3
            Top = 531
            Width = 100
            Height = 21
            TabOrder = 29
            Version = '1.0'
            Caption = ''
          end
          object iwcVersiegelt: TIWCGJQCheckBoxEx
            Left = 3
            Top = 476
            Width = 100
            Height = 21
            TabOrder = 30
            Version = '1.0'
            Caption = ''
          end
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 691
      Width = 862
      TabOrder = 22
      TabStop = False
      inherited ButtonCancel: TIWCGJQButton
        Left = 754
        TabOrder = 16
        TabIndex = True
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 746
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          Left = 638
          TabOrder = 15
          TabIndex = True
        end
      end
    end
  end
end
