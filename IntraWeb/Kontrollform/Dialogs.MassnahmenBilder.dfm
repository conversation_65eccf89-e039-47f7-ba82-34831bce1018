inherited DialogMassnahmenBilder: TDialogMassnahmenBilder
  Width = 650
  Height = 495
  ExplicitWidth = 650
  ExplicitHeight = 495
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 650
    Height = 495
    TabOrder = 4
    JQDialogOptions.Height = 495
    JQDialogOptions.Title = 'Massnahmen Bilder'
    JQDialogOptions.Width = 650
    ExplicitWidth = 650
    ExplicitHeight = 495
    inherited RegionContent: TIWCGJQRegion
      Width = 650
      Height = 445
      TabOrder = 0
      ExplicitWidth = 650
      ExplicitHeight = 445
      object ImageMangelBilder: TIWCGJQImage
        Left = 0
        Top = 0
        Width = 650
        Height = 399
        TabOrder = 5
        Version = '1.0'
        Align = alClient
        Enabled = False
        DataLink.DataSource = DSMangelBilder
        DataLink.FieldName = 'Bild'
        Proportional = True
        Center = True
        ExplicitHeight = 405
      end
      object RegionBottomContent: TIWCGJQRegion
        Left = 0
        Top = 399
        Width = 650
        Height = 46
        TabOrder = 6
        Version = '1.0'
        Align = alBottom
        object LabelBildnummer: TIWCGJQLabel
          Left = 312
          Top = 12
          Width = 26
          Height = 16
          Alignment = taCenter
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'LabelBildnummer'
          Caption = '1/10'
        end
        object ButtonPrevious: TIWCGJQButton
          Left = 11
          Top = 6
          Width = 100
          Height = 34
          TabOrder = 7
          Version = '1.0'
          JQButtonOptions.Label_ = 'Zur'#252'ck'
          JQButtonOptions.OnClick.OnEvent = ButtonPreviousJQButtonOptionsClick
        end
        object ButtonNext: TIWCGJQButton
          Left = 542
          Top = 6
          Width = 100
          Height = 34
          TabOrder = 8
          Version = '1.0'
          JQButtonOptions.Label_ = 'Weiter'
          JQButtonOptions.OnClick.OnEvent = ButtonNextJQButtonOptionsClick
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 445
      Width = 650
      TabOrder = 3
      ExplicitTop = 445
      ExplicitWidth = 650
      inherited ButtonCancel: TIWCGJQButton
        Left = 542
        Visible = False
        TabOrder = 2
        ExplicitLeft = 542
      end
      inherited ButtonOK: TIWCGJQButton
        Left = 426
        TabOrder = 1
        ExplicitLeft = 426
      end
    end
  end
  object DSMangelBilder: TDataSource
    AutoEdit = False
    DataSet = DMKontrolle.QMangelBilder
    Left = 568
    Top = 280
  end
end
