﻿unit Frames.Kontrollverlauf;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider,
  IWCGJQControl, IWCGJQGrid, IWCGJQButton, IWCGJQCheckBoxList,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, ELKE.Classes.Generated, System.Generics.Collections,
  IWCGJQGridCollectionProvider, Datasnap.DBClient, Datasnap.Provider,
  Aurelius.Types.Nullable, IWCGJQImage, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompExtCtrls,
  Vcl.Graphics, pngimage, IWCompLabel, IWCGJQEdit, IWCGJQDatePicker,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp,
  IWCGJQDownload, IWCGJQFileDownload, IWCGJQSweetAlert,
  IWCGJQDialog, IWCGJQFileUpload, AgesXMLFrame, Modules.Kontrolle, System.Net.HttpClient, Frames.Base, IWCGJQRegion,
  IWCGJQLabel;

type

  // ACHTUNG: Wegen einer Schwäche im DFM Designer ist hier ein kleiner Trick notwendig!
  // Um den den Designer aufrufen zu können, diese Zeile aktivieren:
  //TFrameKontrollverlauf = class(TFrameBase)
  // Diese Zeile zum Kompilieren aktivieren:
  TFrameKontrollverlauf = class(TFrameBase<TDMKontrolle>)
    iwrTop: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    jqgKontrolluebersicht: TIWCGJQGrid;
    jqbAbfragen: TIWCGJQButton;
    IWLabel4: TIWCGJQLabel;
    jqdVon: TIWCGJQDatePicker;
    IWLabel5: TIWCGJQLabel;
    jqdBis: TIWCGJQDatePicker;
    jqbProbenExportieren: TIWCGJQButton;
    quProben: TFDQuery;
    DownloadVerlauf: TIWCGJQFileDownload;
    jqsAlert: TIWCGJQSweetAlert;
    jqbProbeErstellen: TIWCGJQButton;
    jqdProbeErstellen: TIWCGJQDialog;
    jqbDetailansicht: TIWCGJQButton;
    jquAges: TIWCGJQFileUpload;
    quAgesXmlHochladen: TFDQuery;
    jqdAgesXml: TIWCGJQDialog;
    iwrBot: TIWCGJQRegion;
    ButtonCSVExport: TIWCGJQButton;
    ProviderKontrollverlauf: TIWCGJQGridDataSetProvider;
    quProbenDokumentHochladen: TFDQuery;
    LabelCount: TIWLabel;
    procedure jqgKontrolluebersichtOnSelectRow(Sender: TObject;
      AParams: TStringList);
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure kontrolluebersichtOnGetImage(Sender: TObject;
      ACol: TIWCGJQGridCol; const ARowId, ACellValue: string;
      var APicture: TPicture; var AImgUrl, ATitle, AAltTitle: string);
    procedure jqbSucheResetOnClick(Sender: TObject; AParams: TStringList);
    procedure JqbProbenExportierenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure JqdProbeErstellenOnClick(Sender: TObject; AParams: TStringList);
    procedure JqbDetailansichtOnClick(Sender: TObject; AParams: TStringList);
    procedure JquAgesOnComplete(Sender: TObject; AParams: TStringList);
    procedure ButtonCSVExportOnClick(Sender: TObject; AParams: TStringList);
    procedure iwiRedClick(Sender: TObject);
    procedure jqgKontrolluebersichtJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    collection: TKontrollberichte;
    agesXML: TAgesXML;
    procedure ResetSearch;
    procedure ProbeErstellenCb(Sender: TObject; AURLParams: TStringList);
    procedure DialogAbbrechen(Sender: TObject; AURLParams: TStringList);
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; ADatamodule: TDMKontrolle); override;
    destructor Destroy; override;
    procedure ResetGrid;
    procedure Search;
  end;

implementation

uses dmmain, DetailFenster, ServerController, DateUtils, System.IOUtils,
  Utility,
  ComObj, MSXML, System.NetEncoding, ELKE.Services.Me.Intf,
  DX.Classes.ObjectFactory, Dialogs.Kontrolldetails, JQ.Helpers.Grid, JQ.Helpers.Button, ELKE.Classes.RESTError;

{$R *.dfm}


constructor TFrameKontrollverlauf.Create(AOwner: TComponent; ADatamodule: TDMKontrolle);
begin
  inherited;
  ResetSearch;
  agesXML := TAgesXML.Create(Self);
  agesXML.Parent := jqdAgesXml;
  jqgKontrolluebersicht.SetUpDefaults(ProviderKontrollverlauf);
  jqbAbfragen.AsyncShowWaitWheel;

  // Dialoge vorab erzeugen
  TDialogKontrollDetails.Create(Self, -1);
end;

destructor TFrameKontrollverlauf.Destroy;
begin
  collection.Free;
  inherited;
end;

procedure TFrameKontrollverlauf.ResetGrid;
begin
  jqgKontrolluebersicht.JQGridOptions.ClearGridData(true);
  if Assigned(collection) then
  begin
    while collection.Count > 0 do
    begin
      collection.item[0].Free;
    end;
  end;
  jqgKontrolluebersicht.JQGridOptions.ClearGridData(true);
end;

procedure TFrameKontrollverlauf.iwiRedClick(Sender: TObject);
begin

end;

{ Fragt die Kontrollberichte für die ausgewählten Jahre ab }
procedure TFrameKontrollverlauf.jqbAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  Search;
end;

procedure TFrameKontrollverlauf.Search;
begin
  DM.GetKontrollverlauf(jqdVon.Date, jqdBis.Date);
end;

procedure TFrameKontrollverlauf.jqgKontrolluebersichtJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
  inherited;
  LabelCount.Caption := jqgKontrolluebersicht.RowCount.ToString + ' Ergebnisse gefunden.';
end;

procedure TFrameKontrollverlauf.jqgKontrolluebersichtOnSelectRow(Sender: TObject;
  AParams: TStringList);
var
  id: Integer;
begin
  inherited;
  if not DM.MoveTableToSelected(jqgKontrolluebersicht, DM.MTKontrollverlauf) then
  begin
    jqsAlert.Info('Es muss eine Kontrolle ausgewählt sein');
    Exit;
  end;

  id := DM.MTKontrollverlaufID.Value;
  TDialogKontrollDetails.Create(Self, id).Show(true);
end;

procedure TFrameKontrollverlauf.kontrolluebersichtOnGetImage(Sender: TObject;
  ACol: TIWCGJQGridCol; const ARowId, ACellValue: string;
  var APicture: TPicture; var AImgUrl, ATitle, AAltTitle: string);
begin
  if ACellValue = 'red' then
  begin
    APicture := TPicture.Create;
    APicture.LoadFromFile('wwwroot/Bilder/red.png');
  end
  else if ACellValue = 'yellow' then
  begin
    APicture := TPicture.Create;
    APicture.LoadFromFile('wwwroot/Bilder/yellow.png');
  end;
end;

procedure TFrameKontrollverlauf.ResetSearch;
begin
  jqdVon.Date := IncMonth(Now, -1);
  jqdBis.Date := Now + 1;
end;

// Exportiert das gerade angezeigte Ergebnis als CSV.
// Es werden genau die selben Spalten/Daten verwendet wie im Grid
procedure TFrameKontrollverlauf.ButtonCSVExportOnClick(Sender: TObject;
  AParams: TStringList);
var
  filename: string;
begin
  try
    filename := DM.ExportAsCSV(DM.MTKontrollverlauf, jqsAlert);
    DownloadVerlauf.DownloadFileName(filename);
  except
    on e: EAbort do
      raise;
    on e: Exception do
    begin
      jqsAlert.Error('Die Kontrollberichte konnten nicht exportiert werden.' + sLineBreak + e.Message);
    end;
  end;
end;

// Exportiert, wenn vorhanden, die Proben des ausgewählten Kontrollberichts als CSV
procedure TFrameKontrollverlauf.JqbProbenExportierenOnClick(Sender: TObject;
  AParams: TStringList);
var
  text, filename, bkb: string;
  index, kbId: Integer;
begin
  text := 'BH;BH_NAME;LFBIS;BETR_NAME;BETR_STRASSE;BETR_ORT;BETR_PLZ;EXT_KENN_AUFTR;PROBENBEZ;'
    + 'PROBENART;BEMERKUNG;EXT_KENN_PROBE;UNTERSUCHUNGSAUFTRAG;DAT_PROBENAHME;EINSENDER;EINSENDER_MAIL;'
    + 'EINSENDER_TEL;VORG_MENGE;BESCHAFFENHEIT;FUTTERTYP;VERWENDUNGSZWECK;TIER_ART;TIER_KATEGORIE;'
    + 'BEIMISCHRATE;VERPACKUNG;VERSCHLUSS;VERSIEGELT;HERK_ZUKAUF;VERDACHT;GEGENPROBE_BELASSEN';
  index := StrToInt(jqgKontrolluebersicht.JQGridOptions.SelRow);
  kbId := (collection.Items[index]
    as TKontrollberichtItem).id;
  bkb := (collection.Items[index]
    as TKontrollberichtItem).bkb;
  quProben.Close;
  quProben.ParamByName('id_kontrollbericht').AsInteger := kbId;
  quProben.Open;

  if quProben.RecordCount = 0 then
  begin
    jqsAlert.Info('Dieser Kontrollbericht hat keine Proben');
    quProben.Close;
    Exit;
  end;

  while not quProben.Eof do
  begin
    text := text + sLineBreak;
    text := text + quProben.FieldByName('bh').AsString + ';';
    text := text + '"' + quProben.FieldByName('bh_name').AsString + '";';
    text := text + '"' + quProben.FieldByName('lfbis').AsString + '";';
    text := text + '"' + quProben.FieldByName('betr_name').AsString + '";';
    text := text + '"' + quProben.FieldByName('betr_strasse').AsString + '";';
    text := text + '"' + quProben.FieldByName('betr_ort').AsString + '";';
    text := text + quProben.FieldByName('betr_plz').AsString + ';';
    text := text + '"' + quProben.FieldByName('ext_kenn_auftr').AsString + '";';
    text := text + '"' + quProben.FieldByName('probenbez').AsString + '";';
    text := text + '"' + quProben.FieldByName('probenart').AsString + '";';
    text := text + '"' + quProben.FieldByName('bemerkung').AsString + '";';
    text := text + '"' + quProben.FieldByName('EXT_KENN_PROBE').AsString + '";';
    text := text + '"' + quProben.FieldByName('untersuchungsauftrag')
      .AsString + '";';
    text := text + quProben.FieldByName('dat_probenahme').AsString + ';';
    text := text + '"' + quProben.FieldByName('titel').AsString + ' ' +
      quProben.FieldByName('vorname').AsString + ' ' +
      quProben.FieldByName('nachname').AsString + '";';
    text := text + '"' + quProben.FieldByName('einsender_mail').AsString + '";';
    text := text + '"' + quProben.FieldByName('einsender_tel').AsString + '";';
    text := text + '"' + quProben.FieldByName('vorg_menge').AsString + '";';
    text := text + '"' + quProben.FieldByName('beschaffenheit').AsString + '";';
    text := text + '"' + quProben.FieldByName('futtertyp').AsString + '";';
    text := text + '"' + quProben.FieldByName('verwendungszweck')
      .AsString + '";';
    text := text + '"' + quProben.FieldByName('tier_art').AsString + '";';
    text := text + '"' + quProben.FieldByName('tier_kategorie').AsString + '";';
    text := text + quProben.FieldByName('beimischrate').AsString + ';';
    text := text + '"' + quProben.FieldByName('verpackung').AsString + '";';
    text := text + '"' + quProben.FieldByName('verschluss').AsString + '";';
    text := text + '"' + quProben.FieldByName('versiegelt').AsString + '";';
    text := text + '"' + quProben.FieldByName('herk_zukauf').AsString + '";';
    text := text + '"' + quProben.FieldByName('verdacht').AsString + '";';
    text := text + '"' + quProben.FieldByName('gegenprobe_belassen')
      .AsString + '"';

    quProben.Next;
  end;
  quProben.Close;

  try
    if not DirectoryExists('exports') then
      if not CreateDir('exports') then
        Exit;
    filename := 'exports\' + bkb + ' ' + FormatDateTime('dd-mm-yyyy hh-nn',
      Now) + '.csv';
    TFile.WriteAllText(filename, text);
    DownloadVerlauf.DownloadFileName(filename);
  except
    jqsAlert.Error('Es gab einen Fehler beim Erstellen des Files');
  end;
end;

// ProbeErstellen Dialog öffnen
procedure TFrameKontrollverlauf.JqdProbeErstellenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if jqgKontrolluebersicht.JQGridOptions.SelRow = '' then
  begin
    jqsAlert.Info('Es muss eine Reihe ausgewählt sein');
    Exit;
  end;
  DialogErstellen(jqdProbeErstellen, ProbeErstellenCb, DialogAbbrechen,
    'Probe erstellen', 'Erstellen');
end;

procedure TFrameKontrollverlauf.ProbeErstellenCb(Sender: TObject;
  AURLParams: TStringList);
begin
  //index := StrToInt(jqgKontrolluebersicht.JQGridOptions.SelRow);
  // kbId := (collection.Items[index] as TKontrollberichtItem).id;
  // jqdProbeErstellen.Visible := not probeErstellen.ProbeSpeichern(kbId);
  // todo 1 -oCH -cTesten:Hier ist der Code, der tatsächlich was tut, aus unbekannten Gründen auskommentiert. Prüfen, ob das Erstellen von Proben überhaupt funktioniert
end;

// Öffnet die Kontroll-Detailansicht von der ausgewählten Kontrolle
procedure TFrameKontrollverlauf.JqbDetailansichtOnClick(Sender: TObject;
  AParams: TStringList);
var
  index, kbId: Integer;
begin
  if jqgKontrolluebersicht.JQGridOptions.SelRow = '' then
  begin
    Exit;
  end;
  index := StrToInt(jqgKontrolluebersicht.JQGridOptions.SelRow);
  kbId := (collection.Items[index]
    as TKontrollberichtItem).id;
  TDetailFrm.Create(Self, kbId).Show;
end;

// Liest ein AGES-XML ein und speichert die Ergebnisse in der Bewegungsdaten.KB_Proben Tabelle.
procedure TFrameKontrollverlauf.JquAgesOnComplete(Sender: TObject;
  AParams: TStringList);
var
  filename, text: string;
  xml: IXMLDOMDocument;
  auftrag: IXMLDOMNode;
  nAuftraege: IXMLDOMNodeList;
  i, j: Integer;
  bkb, probenBkb, pdf, filenamePdf: string;
  exportname, agesauftragsnummer, agesprobennummer, ages_auftragsstatus,
    ages_probenstatus, auftragszusatz, auftragzusatztext: string;
  grenzwertueberschreitung: boolean;
  exporttime: TDateTime;
  proben, probenBkbs: TStringList;
  decodedPdf: TArray<byte>;
  pdfStream: TMemoryStream;
  dokumentGuid: TGUID;
begin
  var LFilename := AParams.Values['fileName'];
  LFilename := TPath.Combine(TPath.GetFullPath(TIWCGJQFileUploadOptions(Sender).UploadPath) , LFilename);
  text := TFile.ReadAllText(LFilename, TEncoding.UTF8);

  xml := CreateOleObject('Microsoft.XMLDOM') as IXMLDOMDocument;
  xml.loadXML(text);
  if xml.parseError.errorCode <> 0 then
  begin
    raise Exception.Create('Fehler beim Parsen des XML-Dokuments');
  end;

  proben := Nil;
  probenBkbs := Nil;
  try
    try
      // Proben und Probenbkbs ist nur dafür da, um dem User feedback zu geben
      proben := TStringList.Create;
      probenBkbs := TStringList.Create;

      nAuftraege := xml.selectNodes('DATEN/AUFTRAG');
      // Der gesamte Prozess ist in einer Datenbank Transaktion die rollbacked wird wenn ein Fehler passiert.
      dm_main.FBC_MAIN.StartTransaction;
      // Es wird durch alle Aufträge im XML-File geloopt.
      for i := 0 to nAuftraege.length - 1 do
      begin
        // XML wird geparst.
        auftrag := nAuftraege.item[i];
        bkb := auftrag.selectSingleNode('EXTERNE-KENNUNG').text;
        probenBkb := auftrag.selectSingleNode('PROBE/EXTERNE-KENNUNG').text;
        filenamePdf := auftrag.selectSingleNode('PRUEFBERICHT/DATEINAME').text;
        pdf := auftrag.selectSingleNode('PRUEFBERICHT/DATEIINHALT').text;
        decodedPdf := TNetEncoding.Base64.DecodeStringToBytes(pdf);

        exportname := filename;
        exporttime := Now;
        agesauftragsnummer := auftrag.selectSingleNode('AUFTRAG-NR').text;
        agesprobennummer := auftrag.selectSingleNode('PROBE/PROBENR').text;
        ages_auftragsstatus := auftrag.selectSingleNode('STATUS').text;
        ages_probenstatus := auftrag.selectSingleNode('PROBE/STATUS').text;

        auftragszusatz := auftrag.selectSingleNode('AUFTRAG-ZUSATZ/FELD/PROMPT').text;
        auftragzusatztext := auftrag.selectSingleNode('AUFTRAG-ZUSATZ/FELD/TEXT').text;
        grenzwertueberschreitung := auftragzusatztext.Equals('Ja');

        // Zuerst wird die PDF in die DB gespeichert.
        dokumentGuid := TGUID.NewGuid;
        quProbenDokumentHochladen.Close;
        quProbenDokumentHochladen.ParamByName('Guid').AsGUID := dokumentGuid;
        quProbenDokumentHochladen.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
        quProbenDokumentHochladen.ParamByName('Bezeichnung').AsString := 'AGES-Pruefbericht';
        quProbenDokumentHochladen.ParamByName('Typ').AsString := 'PDF';
        quProbenDokumentHochladen.ParamByName('Erstellt_am').AsDateTime := Now;
        quProbenDokumentHochladen.ParamByName('Dateiname').AsString := filenamePdf;
        pdfStream := TMemoryStream.Create;
        try
          pdfStream.WriteData(decodedPdf, length(decodedPdf));
          quProbenDokumentHochladen.ParamByName('dokument').LoadFromStream(pdfStream, ftBlob);
        finally
          pdfStream.Free;
        end;
        quProbenDokumentHochladen.Execute;

        // Dann wird die Probe aktualisiert
        quAgesXmlHochladen.Close;
        quAgesXmlHochladen.ParamByName('probenbkb').AsString := probenBkb;
        quAgesXmlHochladen.ParamByName('exportname').AsString := exportname;
        quAgesXmlHochladen.ParamByName('exporttime').AsDateTime := exporttime;
        quAgesXmlHochladen.ParamByName('agesauftragsnummer').AsString :=
          agesauftragsnummer;
        quAgesXmlHochladen.ParamByName('agesprobennummer').AsString :=
          agesprobennummer;
        quAgesXmlHochladen.ParamByName('ages_auftragsstatus').AsString :=
          ages_auftragsstatus;
        quAgesXmlHochladen.ParamByName('ages_probenstatus').AsString :=
          ages_probenstatus;
        quAgesXmlHochladen.ParamByName('grenzwertueberschreitung').AsBoolean :=
          grenzwertueberschreitung;
        quAgesXmlHochladen.ParamByName('auftragszusatztext').AsString := auftragzusatztext;
        quAgesXmlHochladen.ParamByName('xml').AsWideMemo := text;
        quAgesXmlHochladen.ParamByName('GUID_DOKUMENT_AGES_ERGEBNIS').AsGUID := dokumentGuid;
        quAgesXmlHochladen.Execute;

        proben.Add(probenBkb);
        probenBkbs.Add(probenBkb);
      end;
      dm_main.FBC_MAIN.Commit;
      agesXML.ShowProben(proben, probenBkbs);
      probenBkbs := Nil;
    except
      // Wenn ein Fehler passiert die Transaktion rollbacken
      dm_main.FBC_MAIN.Rollback;
    end;
  finally
    proben.Free;
    probenBkbs.Free;
  end;
  jqdAgesXml.Visible := true;
end;

procedure TFrameKontrollverlauf.DialogAbbrechen(Sender: TObject;
  AURLParams: TStringList);
begin
  jqdProbeErstellen.Visible := false;
  jqdAgesXml.Visible := false;
end;

procedure TFrameKontrollverlauf.jqbSucheResetOnClick(Sender: TObject;
  AParams: TStringList);
begin
  ResetSearch;
end;

end.
