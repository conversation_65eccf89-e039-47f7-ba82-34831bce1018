﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <SanitizedProjectName>ELKE</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)</DCC_DcuOutput>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UnitSearchPath>.\rest\Classes;.\TMSAurelius\source;.\TMSAurelius\source\core;.\TMSAurelius\source\drivers;.\TMSBCL\source;.\TMSBCL\source\core;.\TMSBCL\source\core\common;.\TMSBCL\source\extra;.\TMSXData\source;.\TMSXData\source\core;.\TMSXData\source\core\common;.\TMSSparkle\source;.\TMSSparkle\source\core;.\TMSSparkle\source\extra;.\rest\dx-library;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <Icns_MainIcns>$(BDS)\bin\delphi_PROJECTICNS.icns</Icns_MainIcns>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <VerInfo_AutoIncVersion>true</VerInfo_AutoIncVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=;FileVersion=********;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductVersion=********;Comments=;ProgramID=at.esculenta.$(MSBuildProjectName);FileDescription=$(MSBuildProjectName);ProductName=$(MSBuildProjectName)</VerInfo_Keys>
        <VerInfo_Locale>3079</VerInfo_Locale>
        <VerInfo_MinorVer>13</VerInfo_MinorVer>
        <VerInfo_Release>1</VerInfo_Release>
    </PropertyGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>OptionSet</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality/>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
</Project>
