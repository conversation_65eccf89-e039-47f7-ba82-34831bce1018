﻿unit Forms.Logs;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes,Forms.Base,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  Vcl.Controls, Vcl.Forms, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, IWCompGrids,
  IWDBGrids, IWCGJQGrid, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider,
  IWCGJQButton, IWCGJQCheckBoxList, IWCGJQDatePicker,
  IWCGJQDateTimePicker, FireDAC.Comp.Client, Vcl.Imaging.jpeg, IWCompExtCtrls,
  IWCompButton, Data.DB, Datasnap.DBClient, IWCompListbox, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCompText, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  FireDAC.Comp.DataSet, IWCGJQDialog , IWCGJQCombobox, IWCGJQLabel, IWCGJQComp, IWCGJQSweetAlert;

type
  TFormLogs = class(TFormBase)
    iwrMain: TIWCGJQRegion;
    IWRegion1: TIWCGJQRegion;
    jqgLogs: TIWCGJQGrid;
    jqdpLogs: TIWCGJQGridDataSetProvider;
    jqbSuchen: TIWCGJQButton;
    jqcbLogLevel: TIWCGJQCheckBoxList;
    jqdpBis: TIWCGJQDateTimePicker;
    jqdpVon: TIWCGJQDateTimePicker;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqcbApplikation: TIWCGJQCheckBoxList;
    iwcbSortierung: TIWCGJQCombobox;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    IWLabel11: TIWCGJQLabel;
    IWLabel12: TIWCGJQLabel;
    IWLabel13: TIWCGJQLabel;
    IWLabel14: TIWCGJQLabel;
    iwlApplikation: TIWCGJQLabel;
    iwlLogLevel: TIWCGJQLabel;
    iwlURI: TIWCGJQLabel;
    iwlTimestamp: TIWCGJQLabel;
    iwlIP: TIWCGJQLabel;
    iwtMessage: TIWText;
    iwtBody: TIWText;
    iwlMethod: TIWCGJQLabel;
    IWCGJQGrid1: TIWCGJQGrid;
    jqdpRequestHeaders: TIWCGJQGridDataSetProvider;
    query: TFDQuery;
    jqdLog: TIWCGJQDialog;
    procedure jqbSuchenOnClick(Sender: TObject; AParams: TStringList);
    procedure logfensterOnCreate(Sender: TObject);
    procedure jqbZurueckOnClick(Sender: TObject; AParams: TStringList);
    procedure jqgLogsOnSelectRow(Sender: TObject; AParams: TStringList);
  public
  end;

implementation

uses dmmain;

{$R *.dfm}

procedure TFormLogs.jqbZurueckOnClick(Sender: TObject; AParams: TStringList);
begin
  Release;
end;

procedure TFormLogs.logfensterOnCreate(Sender: TObject);
var
  //query: TFDQuery;
  sql: String;
	cbItem: TIWcgJQCheckboxitem;
begin
  //query := TFDQuery.Create(self);
  //query.Connection := dm_main.FBC_MAIN;

  // Die Werte für die Checkboxlisten werden aus der Datenbank geholt
  // und eingetragen.
  sql := 'SELECT DISTINCT [Application] FROM Logging.Log;';
  query.Open(sql);
  query.First;
  while not query.Eof do
  begin
    cbItem := jqcbApplikation.Items.Add;
    cbItem.Value := query.FieldByName('Application').AsString;
    cbItem.Caption := query.FieldByName('Application').AsString;
    query.Next;
  end;
  query.Close;

  sql := 'SELECT DISTINCT Log_Level FROM Logging.Log;';
  query.Open(sql);
  query.First;
  while not query.Eof do
  begin
    cbItem := jqcbLogLevel.Items.Add;
    cbItem.Value := query.FieldByName('Log_Level').AsString;
    cbItem.Caption := query.FieldByName('Log_Level').AsString;
    query.Next;
  end;
  query.Close;

  // Ein Datum wird als Default gesetzt.
  jqdpVon.DateTime := Now - 7;
  jqdpBis.DateTime := Now;
  // Sortierung
  iwcbSortierung.NoSelectionText := 'Absteigend';
  iwcbSortierung.Items.Add('Aufsteigend');
end;

procedure TFormLogs.jqbSuchenOnClick(Sender: TObject; AParams: TStringList);
var
  query: TFDQuery;
  sqlText: String;
  i: Integer;
  count: Integer;
begin
  // Query initialisieren
  query := dm_main.qu_logs;
  query.Close;
  query.SQL.Clear;
  // Query Text schreiben
  sqlText :=           'SELECT * ';
  sqlText := sqlText + 'FROM   [Logging].[Log] ';
  sqlText := sqlText + 'WHERE  Timestamp BETWEEN :startdatum AND :enddatum ';

  // Für jede ausgewählte Applikation wird eine neue Abfrage zur query hinzugefügt
  // die testet, ob der DB-Eintrag gleich heißt wie die ausgewählte
  if not (jqcbApplikation.SelectedCount = 0) then begin
    sqlText := sqlText + ' AND (';
    for i := 0 to jqcbApplikation.SelectedCount - 1 do begin
      sqlText := sqlText + 'Application LIKE :application' + IntToStr(i) + ' OR ';
    end;
    sqlText := sqlText.Remove(sqlText.Length - 3);
    sqlText := sqlText + ') ';
  end;
  // Das gleiche wird für die Log_Levels gemacht
  if not (jqcbLogLevel.SelectedCount = 0) then begin
    sqlText := sqlText + ' AND (';
    for i := 0 to jqcbLogLevel.SelectedCount - 1 do begin
      sqlText := sqlText + 'Log_Level LIKE :loglevel' + IntToStr(i) + ' OR ';
    end;
    sqlText := sqlText.Remove(sqlText.Length - 3);
    sqlText := sqlText + ') ';
  end;
  // Der Timestamp muss zwischen dem von und bis Datum sein.
  sqlText := sqlText + '  ';
  if iwcbSortierung.SelectedText.Equals('Aufsteigend') then begin
    sqlText := sqlText + ' ORDER BY Timestamp ASC;';
  end else begin
    sqlText := sqlText + ' ORDER BY Timestamp DESC;';
  end;
  query.SQL.Text := sqlText;

  // Für jeden Parameter muss der Datentyp eingetragen werden.
  for i := 0 to jqcbApplikation.SelectedCount - 1 do begin
    query.ParamByName('application' + IntToStr(i)).DataType := TFieldType.ftString;
  end;
  for i := 0 to jqcbLogLevel.SelectedCount - 1 do begin
    query.ParamByName('loglevel' + IntToStr(i)).DataType := TFieldType.ftString;
  end;
  query.ParamByName('startdatum').DataType := TFieldType.ftDateTime;
  query.ParamByName('enddatum').DataType := TFieldType.ftDateTime;

  // Die Parameter werden mit den ausgewählten Elementen befüllt.
  if not (jqcbApplikation.SelectedCount = 0) then begin
    count := 0;
    for i := 0 to jqcbApplikation.Items.Count - 1 do begin
      if jqcbApplikation.Items[i].Selected then begin
        query.ParamByName('application' + IntToStr(count)).AsString := jqcbApplikation.Items[i].Value;
        count := count + 1;
      end;
    end;
  end;
  if not (jqcbLogLevel.SelectedCount = 0) then begin
    count := 0;
    for i := 0 to jqcbLogLevel.Items.Count - 1 do begin
      if jqcbLogLevel.Items[i].Selected then begin
        query.ParamByName('loglevel' + IntToStr(count)).AsString := jqcbLogLevel.Items[i].Value;
        count := count + 1;
      end;
    end;
  end;
  // Von und Bis Datum wird gesetzt
  query.ParamByName('startdatum').AsDateTime := jqdpVon.DateTime;
  query.ParamByName('enddatum').AsDateTime := jqdpBis.DateTime;

  query.Active := true;
end;

{Wenn der User auf einen Eintrag im Grid klickt wird eine Detailansicht
 des Logs angezeigt.}
procedure TFormLogs.jqgLogsOnSelectRow(Sender: TObject; AParams: TStringList);
var
  index: Integer;
  query: TFDQuery;
  id: String;
begin
  // Die ID vom Logeintrag wird von der query geholt
  index := StrToInt(AParams.Values['rowid']) - 1;
  query := dm_main.qu_logs;
  query.First;
  query.MoveBy(index);
  id := query.FieldByName('ID').AsString;

  // Query befüllt das Grid
  dm_main.qu_requestheaders.Close;
  dm_main.qu_requestheaders.Prepare;
  dm_main.qu_requestheaders.ParamByName('ID').AsString := id;
  dm_main.qu_requestheaders.active := true;

  // Die Query Logdetail joined alle relevanten Tabellen zusammen
  query := dm_main.qu_logdetail;
  query.Close;
  query.ParamByName('ID').AsString := id;
  query.Open;
  query.First;

  // Setze alle Felder des Modals
  iwtMessage.Text := ' ';
  iwtBody.Text := ' ';
  iwlApplikation.Caption := query.FieldByName('Application').AsString;
  iwlMethod.Caption := query.FieldByName('Method').AsString;
  iwlIP.Caption := query.FieldByName('Remote_IP').AsString;
  iwlURI.Caption := query.FieldByName('URL').AsString;
  iwlLogLevel.Caption := query.FieldByName('Log_Level').AsString;
  iwlTimestamp.Caption := DateTimeToStr(query.FieldByName('Timestamp').AsDateTime);
  iwtMessage.Text := query.FieldByName('Message').AsString;
  iwtBody.Text := query.FieldByName('Body').AsString;

  // Das Modal anzeigen
  jqdLog.Visible := true;
  query.Close;
end;

end.
