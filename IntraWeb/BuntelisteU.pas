unit BuntelisteU;

interface

uses
  <PERSON><PERSON>, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes,
  IWCGJQButton, Vcl.Imaging.jpeg, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls,
  Vcl.Forms, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, IWCGPanelList;

type
  TFrmBunteliste = class(TIWAppForm)
    plTest: TIWCGPanelList;
    IWCGJQButton1: TIWCGJQButton;
    procedure iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure IWCGJQButton1JQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
  public
  end;

implementation

{$R *.dfm}


procedure TFrm<PERSON>unteliste.iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
	release;
end;

procedure TFrmBunteliste.IWCGJQButton1JQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
	release;
end;

end.
