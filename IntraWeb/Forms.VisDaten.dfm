inherited FormVisDaten: TFormVisDaten
  Width = 1080
  Height = 697
  Title = 'VIS-Daten'
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1080
    TabOrder = 48
    inherited ImageLogo: TIWImageFile
      Left = 797
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 588
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 47
    end
  end
  object IWRegion5: TIWCGJQRegion [1]
    Left = 1060
    Top = 50
    Width = 20
    Height = 622
    RenderInvisibleControls = True
    TabOrder = 49
    Version = '1.0'
    Align = alRight
    Color = clWebSILVER
  end
  object iwtVisdaten: TIWTabControl [2]
    Left = 20
    Top = 50
    Width = 1040
    Height = 622
    Margins.Left = 10
    Margins.Right = 10
    RenderInvisibleControls = False
    OnChange = iwtVisdatenChange
    ActiveTabFont.Color = clWebWHITE
    ActiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    ActiveTabFont.Size = 10
    ActiveTabFont.Style = [fsBold]
    InactiveTabFont.Color = clWebBLACK
    InactiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    InactiveTabFont.Size = 10
    InactiveTabFont.Style = []
    ActiveTabColor = clWebCORNFLOWERBLUE
    InactiveTabColor = clWebLIGHTGRAY
    ActivePage = 0
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    Color = clWebSILVER
    ClipRegion = False
    TabMargin = 3
    TabPadding = 0
    TabBorderRadius = 3
    ActiveTabBorder.Color = clWebBLACK
    ActiveTabBorder.Width = 1
    InactiveTabBorder.Color = clWebBLACK
    InactiveTabBorder.Width = 1
    DesignSize = (
      1040
      622)
    object TabBetriebstypen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 3
      Title = 'Betriebstypen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Betriebstypen: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Betriebstypen'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ID'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ID'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BEZEICHNUNG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BEZEICHNUNG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BEGDAT'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BEGDAT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ENDDAT'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ENDDAT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_VisBetriebstypen
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion7: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 50
        Version = '1.0'
        Align = alTop
        object iwl_betriebstyp_bezeichnung: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 78
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betriebstyp_bezeichnung'
          Caption = 'Bezeichnung'
        end
        object iwi_loesche_Betriebstypensuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_BetriebstypensucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_loesche_Betriebstypensuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_betriebstyp_id: TIWCGJQLabel
          Left = 168
          Top = 4
          Width = 13
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betriebstyp_id'
          Caption = 'ID'
        end
        object iwl_betriebstypen_count: TIWCGJQLabel
          Left = 688
          Top = 51
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betriebstypen_count'
          Caption = '-'
        end
        object Edit_betriebstypen_sucheBezeichnung: TIWCGJQEdit
          Left = 30
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 6
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_Betriebstypen_IDsuche: TIWCGJQEdit
          Left = 168
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 8
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheBetriebstyp: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 16
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheBetriebstypJQButtonOptionsClick
        end
      end
      object iwcggrid_Betriebstypen: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 28
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID'
            Name = 'ID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstypen-ID'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstypenbezeichnung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'BEGDAT'
            Name = 'BEGDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beginndatum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'ENDDAT'
            Name = 'ENDDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Enddatum'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Betriebstypen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabZulassungslistencodes: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 5
      Title = 'Zulassungslistencodes'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Zulassungslistencodes: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Zulassungslistencodes'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BETRIEBSTYP'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BETRIEBSTYP'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ZULASSUNGSTYP'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ZULASSUNGSTYP'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'SEKTION'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'SEKTION'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'GRUPPE'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'GRUPPE'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'CODE'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'CODE'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_VisCodes
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion9: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 51
        Version = '1.0'
        Align = alTop
        object iwl_listencodes_betriebstyp: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 69
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          ConvertSpaces = True
          HasTabOrder = False
          FriendlyName = 'iwl_listencodes_betriebstyp'
          Caption = 'Betriebstyp'
        end
        object iwi_loesche_Listencodesuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_ListencodesucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_loesche_Listencodesuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_listencodes_count: TIWCGJQLabel
          Left = 688
          Top = 51
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_listencodes_count'
          Caption = '-'
        end
        object iwl_Listencodes_codesuche: TIWCGJQLabel
          Left = 157
          Top = 2
          Width = 31
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_Listencodes_codesuche'
          Caption = 'Code'
        end
        object Edit_Listencodes_Betriebstypsuche: TIWCGJQEdit
          Left = 30
          Top = 26
          Width = 121
          Height = 21
          TabOrder = 12
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_Listencodes_Codesuche: TIWCGJQEdit
          Left = 157
          Top = 26
          Width = 121
          Height = 21
          TabOrder = 13
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheListencodes: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 17
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheListencodesJQButtonOptionsClick
        end
      end
      object iwcggrid_Listencodes: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 31
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSTYP'
            Name = 'BETRIEBSTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstypen-ID'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ZULASSUNGSTYP'
            Name = 'ZULASSUNGSTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungstyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SEKTION'
            Name = 'SEKTION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Sektion'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GRUPPE'
            Name = 'GRUPPE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gruppe'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'CODE'
            Name = 'CODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Code'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Listencodes
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabTierarten: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 6
      Title = 'Tierarten'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Tierarten: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Tierarten'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'WERT'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'WERT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'TIERART'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'TIERART'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_Tierarten
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion10: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 52
        Version = '1.0'
        Align = alTop
        object iwl_suche_Tierart: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 27
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          ConvertSpaces = True
          HasTabOrder = False
          FriendlyName = 'iwl_suche_Tierart'
          Caption = 'Text'
        end
        object iwi_Loesche_Tierartsuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_Loesche_TierartsucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_Loesche_Tierartsuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_tierart_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_tierart_count'
          Caption = '-'
        end
        object Edit_tierartsuche: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 18
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheTierart: TIWCGJQButton
          Left = 688
          Top = 24
          Width = 75
          Height = 21
          TabOrder = 21
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheTierartJQButtonOptionsClick
        end
      end
      object iwcggrid_Tierarten: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 32
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TIERART'
            Name = 'TIERART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tierart'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'WERT'
            Name = 'WERT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kurzzeichen'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Tierarten
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabZulassungstypen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 8
      Title = 'Zulassungstypen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Zulassungstypen: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Zulassungstypen'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ID'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ID'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BEZEICHNUNG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BEZEICHNUNG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'TAETIGKEITSCODE'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'TAETIGKEITSCODE'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_Zulassungstypen
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion13: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 53
        Version = '1.0'
        Align = alTop
        object iwl_Zulassungstypen_Bezeichnung: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 78
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          ConvertSpaces = True
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Bezeichnung'
        end
        object iwi_Loesche_Zulassungstypensuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_Loesche_ZulassungstypensucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_Loesche_Zulassungstypensuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_Zulassungstypen_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_Zulassungstypen_count'
          Caption = '-'
        end
        object Edit_Zulassungstypen_sucheBezeichnung: TIWCGJQEdit
          Left = 30
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 19
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheZulassungstypen: TIWCGJQButton
          Left = 688
          Top = 24
          Width = 75
          Height = 21
          TabOrder = 22
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheZulassungstypenJQButtonOptionsClick
        end
      end
      object iwcggrid_Zulassungstypen: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 33
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID'
            Name = 'ID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungstypen-ID'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungsbezeichnung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TAETIGKEITSCODE'
            Name = 'TAETIGKEITSCODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'T'#228'tigkeitscode'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Zulassungstypen
        JQSubGridProvider = iwcgprov_Zulassungstypen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabSchluesseltypen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 10
      Title = 'Schl'#252'sseltypen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Schluesseltypen: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Zulassungstypen'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ID'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ID'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BEZEICHNUNG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BEZEICHNUNG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_Schluesseltypen
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion12: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 54
        Version = '1.0'
        Align = alTop
        object iwl_Schluesseltypen_bezeichnung: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 78
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          ConvertSpaces = True
          HasTabOrder = False
          FriendlyName = 'iwl_Schluesseltypen_bezeichnung'
          Caption = 'Bezeichnung'
        end
        object iwi_schluesseltypensuche_loeschen: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_schluesseltypensuche_loeschenClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_schluesseltypensuche_loeschen'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_Schluesseltypen_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_Schluesseltypen_count'
          Caption = '-'
        end
        object Edit_Schluesseltypen_sucheBezeichnung: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 23
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheSchluesseltypen: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 24
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheSchluesseltypenJQButtonOptionsClick
          Default.Enabled = True
        end
      end
      object iwcggrid_Schluesseltypen: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 34
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID'
            Name = 'ID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Schl'#252'ssel-ID'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Schl'#252'sselbezeichnung'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Schluesseltypen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabPrbkat: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 11
      Title = 'PRBKAT'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_prbkat: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 37
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TIERART'
            Name = 'TIERART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tierart'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KATEG'
            Name = 'KATEG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kategorie'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MINMON'
            Name = 'MINMON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'min. Monate'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MAXMON'
            Name = 'MAXMON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'max. Monate'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GESCHLM'
            Name = 'GESCHLM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'GESCHLM'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GESCHLW'
            Name = 'GESCHLW'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'GESCHLW'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GESCHLU'
            Name = 'GESCHLU'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'GESCHLU'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MUTTIER'
            Name = 'MUTTIER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Muttertier'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TEXT'
            Name = 'TEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Text'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ORD'
            Name = 'ORD'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'ORD'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_prbkat
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion11: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 55
        Version = '1.0'
        Align = alTop
        object iwi_loesche_prbkatsuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_prbkatsucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_schluesseltypensuche_loeschen'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwcgla_Tierart: TIWCGJQLabel
          Left = 30
          Top = 4
          Width = 42
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwcgla_Tierart'
          Caption = 'Tierart'
        end
        object Kategorie: TIWCGJQLabel
          Left = 168
          Top = 4
          Width = 59
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'Kategorie'
          Caption = 'Kategorie'
        end
        object iwcgl_prbkat_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwcgl_prbkat_count'
          Caption = '-'
        end
        object Edit_prbkat_Tierart: TIWCGJQEdit
          Left = 30
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 26
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_prbkat_kateg: TIWCGJQEdit
          Left = 168
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 29
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_suche_prbkat: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 39
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_suche_prbkatJQButtonOptionsClick
          JQEvents.OnClick.Ajax = False
          Default.Enabled = True
        end
      end
    end
    object TabZulassungsnummern: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 12
      Title = 'Zulassungsnummern'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_ZulassungsNr: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 44
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'STICHTAG'
            Name = 'STICHTAG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Stichtag'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'REGNR'
            Name = 'REGNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Reg. Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ZULASSUNGSNUMMER'
            Name = 'ZULASSUNGSNUMMER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungsnummer'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'BEGINNDATUM'
            Name = 'BEGINNDATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beginndatum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'ENDDATUM'
            Name = 'ENDDATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Enddatum'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_ZulassungsNr
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 56
        Version = '1.0'
        Align = alTop
        object iwi_loesche_zulnrsuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_zulnrsucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_schluesseltypensuche_loeschen'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwcgl_zulnr_regnr: TIWCGJQLabel
          Left = 30
          Top = 4
          Width = 37
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwcgla_Tierart'
          Caption = 'Regnr'
        end
        object iwcgl_zulnr_zulnr: TIWCGJQLabel
          Left = 168
          Top = 4
          Width = 122
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'Kategorie'
          Caption = 'Zulassungsnummer'
        end
        object iwl_zulassungsnr_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwcgl_prbkat_count'
          Caption = '-'
        end
        object Edit_zulnr_regnr: TIWCGJQEdit
          Left = 30
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 35
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_zulnr_zulnr: TIWCGJQEdit
          Left = 168
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 36
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_suche_zulassungsnr: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 46
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_suche_zulassungsnrJQButtonOptionsClick
          JQEvents.OnClick.Ajax = False
          Default.Enabled = True
        end
      end
    end
    object TabZulassungen: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 1
      Title = 'Zulassungen'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Zulassungen: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Zulassungen'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'STICHTAG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'STICHTAG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'REGNR'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'REGNR'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BETRIEBSTYP'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BETRIEBSTYP'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ZULASSUNG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ZULASSUNG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'BEGINNDATUM'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'BEGINNDATUM'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ENDDATUM'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ENDDATUM'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        FooterRowCount = 0
        FriendlyName = 'iwdbg_Zulassungen'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion2: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 57
        Version = '1.0'
        Align = alTop
        object iwl_zulassungen_regnr: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 143
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_zulassungen_regnr'
          Caption = 'Registrierungsnummer'
        end
        object iwi_loesche_Zulassungssuche: TIWImage
          Left = 769
          Top = 25
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_ZulassungssucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_loesche_Zulassungssuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_zulassungen_count: TIWCGJQLabel
          Left = 688
          Top = 51
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_zulassungen_count'
          Caption = '-'
        end
        object Edit_zulassung_sucheRegnr: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 38
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheZulassung: TIWCGJQButton
          Left = 688
          Top = 24
          Width = 75
          Height = 21
          TabOrder = 15
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheZulassungJQButtonOptionsClick
        end
      end
      object iwcggrid_Zulassungen: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 25
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'STICHTAG'
            Name = 'STICHTAG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Stichtag'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'REGNR'
            Name = 'REGNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Reg. Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ZULASSUNG'
            Name = 'ZULASSUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSTYP'
            Name = 'BETRIEBSTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'BEGINNDATUM'
            Name = 'BEGINNDATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beginndatum'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'ENDDATUM'
            Name = 'ENDDATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Enddatum'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Zulassungen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabDOMWerte: TIWTabPage
      Left = 0
      Top = 20
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 4
      Title = 'DOM Werte'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Domwerte: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'DOM Werte'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ORD'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ORD'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'CODE'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'TEXT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'TEXT'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'TEXT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'DOM'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'DOM'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_VisDOM
        FooterRowCount = 0
        FriendlyName = 'IWDBGrid1'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion8: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 58
        Version = '1.0'
        Align = alTop
        object iwl_domwert_text: TIWCGJQLabel
          Left = 30
          Top = 2
          Width = 27
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          ConvertSpaces = True
          HasTabOrder = False
          FriendlyName = 'iwl_domwert_text'
          Caption = 'Text'
        end
        object iwi_loesche_DOMwertsuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loesche_DOMwertsucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_loesche_DOMwertsuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_domwert_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_domwert_count'
          Caption = '-'
        end
        object Edit_Domwert_Textsuche: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 40
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheDOMwert: TIWCGJQButton
          Left = 688
          Top = 24
          Width = 75
          Height = 21
          TabOrder = 20
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheDOMwertJQButtonOptionsClick
        end
      end
      object iwcggrid_DOMwert: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 30
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ORD'
            Name = 'ORD'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'ORD'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'CODE'
            Name = 'CODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Code'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TEXT'
            Name = 'TEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Text'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'DOM'
            Name = 'DOM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'DOM'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_DOMwerte
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabBetriebsstammdaten: TIWTabPage
      Left = 0
      Top = 14
      Width = 1040
      Height = 652
      Visible = False
      RenderInvisibleControls = False
      Title = 'Betriebsstammdaten'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwdbg_Betriebsstammdaten: TIWDBGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        Visible = False
        Align = alClient
        BorderColors.Color = clNone
        BorderColors.Light = clNone
        BorderColors.Dark = clNone
        BGColor = clNone
        BorderSize = 1
        BorderStyle = tfDefault
        Caption = 'Betriebsstammdaten'
        CellPadding = 0
        CellSpacing = 0
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        FrameBuffer = 40
        Lines = tlAll
        UseFrame = True
        UseSize = True
        ScrollToCurrentRow = False
        Columns = <
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'STICHTAG'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'STICHTAG'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'REGNR'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'REGNR'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'NAME'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'NAME'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'GEMNR'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'STRASSE'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'KATASTRALGMNR'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'HNR'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'KATASTRALGEMNAME'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'KATASTRALGEMNAME'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'STRASSE'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'STRASSE'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'HNR'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'HNR'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'PLZ'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'PLZ'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'ORT'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'ORT'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'TELEFON'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'TELEFON'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'FAX'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'FAX'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end
          item
            Alignment = taLeftJustify
            BGColor = clNone
            DoSubmitValidation = True
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            Header = False
            Height = '0'
            ShowHint = True
            VAlign = vaMiddle
            Visible = True
            Width = '0'
            Wrap = False
            RawText = False
            Css = ''
            BlobCharLimit = 0
            CompareHighlight = hcNone
            DataField = 'EMAIL'
            Title.Alignment = taCenter
            Title.BGColor = clNone
            Title.DoSubmitValidation = True
            Title.Font.Color = clNone
            Title.Font.Size = 10
            Title.Font.Style = []
            Title.Header = False
            Title.Height = '0'
            Title.ShowHint = True
            Title.Text = 'EMAIL'
            Title.VAlign = vaMiddle
            Title.Visible = True
            Title.Width = '0'
            Title.Wrap = False
            Title.RawText = True
          end>
        DataSource = ds_VisBStammdaten
        FooterRowCount = 0
        FriendlyName = 'iwdbg_Betriebsstammdaten'
        FromStart = True
        HighlightColor = clNone
        HighlightRows = False
        Options = [dgShowTitles]
        RefreshMode = rmAutomatic
        RowLimit = 0
        RollOver = False
        RowClick = False
        RollOverColor = clNone
        RowHeaderColor = clNone
        RowAlternateColor = clNone
        RowCurrentColor = clNone
      end
      object IWRegion3: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1040
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 59
        Version = '1.0'
        Align = alTop
        object iwl_bstammdaten_name: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 36
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_name'
          Caption = 'Name'
        end
        object iwi_loeschen_Stammdatensuche: TIWImage
          Left = 769
          Top = 24
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = iwi_loeschen_StammdatensucheClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'iwi_loeschen_Stammdatensuche'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object iwl_bstammdaten_regnr: TIWCGJQLabel
          Left = 157
          Top = 3
          Width = 47
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_regnr'
          Caption = 'Reg.Nr.'
        end
        object IWL_Betriebsst_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWL_Betriebsst_count'
          Caption = '-'
        end
        object iwl_bstammdaten_Strasse: TIWCGJQLabel
          Left = 284
          Top = 3
          Width = 41
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_Strasse'
          Caption = 'Stra'#223'e'
        end
        object iwl_bstammdaten_Hnr: TIWCGJQLabel
          Left = 411
          Top = 3
          Width = 83
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_Hnr'
          Caption = 'Hausnummer'
        end
        object Edit_su_Name1_betrest: TIWCGJQEdit
          Left = 30
          Top = 23
          Width = 121
          Height = 21
          TabOrder = 41
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_su_regnr_betrest: TIWCGJQEdit
          Left = 157
          Top = 23
          Width = 121
          Height = 21
          TabOrder = 42
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_BStammdaten_sucheStrasse: TIWCGJQEdit
          Left = 284
          Top = 23
          Width = 121
          Height = 21
          TabOrder = 43
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_BStammdaten_sucheHnr: TIWCGJQEdit
          Left = 411
          Top = 23
          Width = 121
          Height = 21
          TabOrder = 45
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheStammdaten: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 14
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheStammdatenJQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
      end
      object jqgStammdaten: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1040
        Height = 580
        TabOrder = 27
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'STICHTAG'
            Name = 'STICHTAG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Stichtag'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'REGNR'
            Name = 'REGNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'Reg. Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'NAME'
            Name = 'NAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Name'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEMNR'
            Name = 'GEMNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'Gem. Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KATASTRALGMNR'
            Name = 'KATASTRALGMNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'Katastralgem. Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KATASTRALGEMNAME'
            Name = 'KATASTRALGEMNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Katastralgemeinde'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STRASSE'
            Name = 'STRASSE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Stra'#223'e'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'HNR'
            Name = 'HNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Haus Nr.'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ'
            Name = 'PLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'PLZ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ORT'
            Name = 'ORT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Ort'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TELEFON'
            Name = 'TELEFON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Telefon'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FAX'
            Name = 'FAX'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Fax'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'EMAIL'
            Name = 'EMAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'E-Mail'
          end>
        JQGridOptions.Height = 526
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1038
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Stammdaten
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
  end
  object IWRegion6: TIWCGJQRegion [3]
    Left = 0
    Top = 50
    Width = 20
    Height = 622
    RenderInvisibleControls = True
    TabOrder = 60
    Version = '1.0'
    Align = alLeft
    Color = clWebSILVER
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 672
    Width = 1080
  end
  object ds_VisBStammdaten: TDataSource
    DataSet = dm_main.qu_VisBStammdaten
    Left = 232
  end
  object ds_VisZulassungen: TDataSource
    DataSet = dm_main.qu_VisZulassungen
    Left = 288
  end
  object ds_VisBetriebstypen: TDataSource
    DataSet = dm_main.qu_VisBetriebstypen
    Left = 360
  end
  object ds_VisDOM: TDataSource
    DataSet = dm_main.qu_VisDom
    Left = 432
  end
  object ds_VisCodes: TDataSource
    DataSet = dm_main.qu_VisListencodes
    Left = 504
  end
  object ds_Tierarten: TDataSource
    DataSet = dm_main.qu_VISTierarten
    Left = 576
  end
  object ds_Zulassungstypen: TDataSource
    DataSet = dm_main.qu_VISZulassungstypen
    Left = 648
  end
  object ds_Schluesseltypen: TDataSource
    DataSet = dm_main.qu_VISSchluesseltypen
    Left = 720
  end
  object iwcgprov_Zulassungen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VisZulassungen
    DataSource = ds_VisZulassungen
    AutoPost = False
    Left = 40
    Top = 640
  end
  object iwcgprov_Stammdaten: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VisBStammdaten
    DataSource = ds_VisBStammdaten
    Left = 40
    Top = 591
  end
  object iwcgprov_Betriebstypen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VisBetriebstypen
    DataSource = ds_VisBetriebstypen
    Left = 152
    Top = 589
  end
  object iwcgprov_DOMwerte: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VisDom
    DataSource = ds_VisDOM
    Left = 160
    Top = 645
  end
  object iwcgprov_Listencodes: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VisListencodes
    DataSource = ds_VisCodes
    Left = 272
    Top = 597
  end
  object iwcgprov_Tierarten: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VISTierarten
    DataSource = ds_Tierarten
    Left = 272
    Top = 645
  end
  object iwcgprov_Zulassungstypen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VISZulassungstypen
    DataSource = ds_Zulassungstypen
    Left = 384
    Top = 597
  end
  object iwcgprov_Schluesseltypen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VISSchluesseltypen
    DataSource = ds_Schluesseltypen
    Left = 376
    Top = 653
  end
  object iwcgprov_prbkat: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VISprbkat
    Left = 512
    Top = 589
  end
  object ds_Prbkat: TDataSource
    DataSet = dm_main.qu_VISprbkat
    Left = 760
    Top = 1
  end
  object ds_ZulassungsNr: TDataSource
    DataSet = dm_main.qu_VISZulassungsNr
    Left = 816
  end
  object iwcgprov_ZulassungsNr: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_VISZulassungsNr
    DataSource = ds_ZulassungsNr
    Left = 496
    Top = 638
  end
  object iwcgsa_Visdaten: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 956
    Top = 80
  end
end
