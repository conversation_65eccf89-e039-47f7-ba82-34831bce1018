unit Config;

interface

uses
  System.SyncObjs, dx.Classes.Singleton;

type
  TConfig = class(T<PERSON><PERSON>leton<TConfig>)

  public
    RestURI: string;

    ServerURIBase: string;
    ServerPort: Integer;
    SSLPort: Integer;

    DatabaseServer: string;
    Database: string;
    DatabaseUserName: string;
    DatabasePassword: string;
    DatabaseDriverID: string;

  public
    constructor Create;
    procedure ReadConfig;

  end;

implementation

uses
  Inifiles, System.SysUtils, System.IOUtils;

constructor TConfig.Create;
begin
  inherited;
  ReadConfig;
end;

procedure TConfig.ReadConfig;
var
  LConfigFile: TCustomIniFile;
begin
  LConfigFile := TIniFile.Create(TPath.Combine(TPath.GetLibraryPath, TPath.ChangeExtension(ParamStr(0), '.ini')));
  try
    // Die Defaults
    ServerURIBase := '/at.esculenta.elkeiw-e/';
    ServerPort := 80;
    SSLPort := 0;
    // Ini ggfs. initialisieren
    if not LConfigFile.SectionExists('Server') then
    begin
      LConfigFile.WriteString('Server', 'URLBase', ServerURIBase);
      LConfigFile.WriteInteger('Server', 'Port', ServerPort);
      LConfigFile.WriteInteger('Server', 'SSLPort', SSLPort);
    end
    else
    begin
      ServerURIBase := LConfigFile.ReadString('Server', 'URLBase', ServerURIBase);
      ServerPort := LConfigFile.ReadInteger('Server', 'Port', ServerPort);
      SSLPort := LConfigFile.ReadInteger('Server', 'SSLPort', SSLPort);
    end;

    // Die Defaults
    RestURI := 'https://stp.esculenta.at/at.esculenta.elkerest-e/v1';
    // Ini ggfs. initialisieren
    if not LConfigFile.SectionExists('ELKERest') then
    begin
      LConfigFile.WriteString('ELKERest', 'RESTUri', RestURI);
    end
      // Ini lesen
    else
    begin
      RestURI := LConfigFile.ReadString('ELKERest', 'RESTUri', RestURI);
    end;

    // Die Defaults
    DatabaseServer := '10.10.0.102';
    Database := 'ELKEDB';
    DatabaseUserName := 'bbo';
    DatabasePassword := 'Esc1234!';
    DatabaseDriverID := 'MSSQL';
    // Ini ggfs. initialisieren
    if not LConfigFile.SectionExists('Database') then
    begin
      LConfigFile.WriteString('Database', 'Server', DatabaseServer);
      LConfigFile.WriteString('Database', 'Database', Database);
      LConfigFile.WriteString('Database', 'User_Name', DatabaseUserName);
      LConfigFile.WriteString('Database', 'Password', DatabasePassword);
      LConfigFile.WriteString('Database', 'DriverID', DatabaseDriverID);
    end
    else
      // Ini lesen
    begin
      DatabaseServer := LConfigFile.ReadString('Database', 'Server', DatabaseServer);
      Database := LConfigFile.ReadString('Database', 'Database', Database);
      DatabaseUserName := LConfigFile.ReadString('Database', 'User_Name', DatabaseUserName);
      DatabasePassword := LConfigFile.ReadString('Database', 'Password', DatabasePassword);
      DatabaseDriverID := LConfigFile.ReadString('Database', 'DriverID', DatabaseDriverID);
    end;
  finally
    LConfigFile.Free;
  end;

end;

end.

