unit Frames.Auswahldaten.Tiere;

interface

uses
  SysUtils, Classes, Controls, IWCGFrame, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container,
  IWCGJQRegion,
  Vcl.Forms, IWRegion, IWCGJQControl, Frames.Base, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl,
  IWCompLabel, IWCGJQLabel, Modules.AMA, Data.DB;

type
  //TFrameAuswahldatenTiere = class(TFrameBase)
    TFrameAuswahldatenTiere = class(TFrameBase<TDMAma>)
    IWCGJQLabel1: TIWCGJQLabel;
    IWCGJQLabel2: TIWCGJQLabel;
    LabelAnzahl: TIWCGJQLabel;
    LabelTierKategorie: TIWCGJQLabel;
    procedure IWCGJQFrameCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

implementation

{$R *.dfm}

procedure TFrameAuswahldatenTiere.IWCGJQFrameCreate(Sender: TObject);
begin
  inherited;
  LabelTierKategorie.Caption := DM.QTierdatenTIERKATEGORIE.AsString;
  LabelAnzahl.Caption := DM.QTierdatenANZAHL.AsString;
end;


end.
