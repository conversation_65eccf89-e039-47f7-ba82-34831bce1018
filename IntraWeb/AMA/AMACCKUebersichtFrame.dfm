inherited AMACCKUebersicht: TAMACCKUebersicht
  Width = 1163
  Height = 808
  Align = alClient
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1163
    Height = 808
    TabOrder = 2
    object RegionContent: TIWCGJQRegion
      Left = 0
      Top = 48
      Width = 1163
      Height = 760
      RenderInvisibleControls = True
      TabOrder = 3
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object GridAuftraege: TIWCGJQGrid
        Left = 1
        Top = 1
        Width = 1161
        Height = 758
        TabOrder = 1
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AUFTRAGSJAHR'
            Name = 'AUFTRAGSJAHR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Width = 100
            Caption = 'Jahr'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AUFTRAGSART'
            Name = 'AUFTRAGSART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Art'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LFBIS_HAUPTBETRIEB'
            Name = 'LFBIS_HAUPTBETRIEB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'LFBIS HB'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKB'
            Name = 'BKB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'BKB'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BBKNr'
            Name = 'BBKNr'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BBKNr'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VORNAME'
            Name = 'VORNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Vorname'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'NACHNAME'
            Name = 'NACHNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Nachname'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ADRESSE_BEW'
            Name = 'ADRESSE_BEW'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Adresse'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ORT_BEW'
            Name = 'ORT_BEW'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Ort'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ_BEW'
            Name = 'PLZ_BEW'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'PLZ'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Tierhalter'
            Name = 'Tierhalter'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Tierhalter'
          end
          item
            Align = gaCenter
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'HatTeilbetriebe'
            Name = 'HatTeilbetriebe'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Hat Teilbetriebe'
          end
          item
            Align = gaCenter
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrollberichtErzeugt'
            Name = 'KontrollberichtErzeugt'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = ' Erzeugt'
          end
          item
            Align = gaCenter
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrolleDurchgefuehrt'
            Name = 'KontrolleDurchgefuehrt'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Durchgef'#252'hrt'
          end
          item
            Align = gaCenter
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrolleBewertet'
            Name = 'KontrolleBewertet'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Bewertet'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrolleVerweigert'
            Name = 'KontrolleVerweigert'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Verweigert'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrolleStorniert'
            Name = 'KontrolleStorniert'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Storniert'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'KontrolleAbgeschlossen'
            Name = 'KontrolleAbgeschlossen'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Abgeschlossen'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STATUS'
            Name = 'STATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Status'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bewertungs_Version'
            Name = 'Bewertungs_Version'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bew. Version'
          end>
        JQGridOptions.GridView = True
        JQGridOptions.Grouping = True
        JQGridOptions.Height = 704
        JQGridOptions.MultiSelect = True
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1159
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnDblClickRow.OnEvent = GridAuftraegeJQGridOptionsDblClickRow
        JQGridOptions.OnSelectRow.OnEvent = GridAuftraegeOnSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridOptions.RowAttrFn.Script = 
          'function (ts, rd, cur, id){'#13#10'  if (ts.KontrollberichtErzeugt == ' +
          '"1") {'#13#10'    return { "style" : "font-style: normal; color: gray"' +
          '} '#13#10'  } '#13#10'  else if (ts.Betriebstyp == "TB") {'#13#10'    return { "st' +
          'yle" : "font-style: italic; color: black"} '#13#10'  }'#13#10'  else {'#13#10'    ' +
          'return { "style" : "font-style: normal; color: black"}'#13#10'  } '#13#10'}'
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderAuftraege
        JQSubGridProvider = ProviderAuftraege
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQGridGroupHeader.UseColSpanStyle = True
        JQGridGroupHeader.GroupHeaders = <
          item
            StartColumnName = 'KontrollberichtErzeugt'
            NumberOfColumns = 6
            TitleText = 'Kontrolle'
          end>
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object RegionToolbar: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1163
      Height = 48
      RenderInvisibleControls = True
      TabOrder = 5
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      BorderOptions.Color = clWebGRAY
      CGScrollStyle = cgsbsNone
      object IWCGJQLabel1: TIWCGJQLabel
        AlignWithMargins = True
        Left = 430
        Top = 17
        Width = 203
        Height = 22
        Margins.Left = 32
        Margins.Top = 16
        Margins.Right = 8
        Margins.Bottom = 8
        Align = alLeft
        Css = 'ui-widget h2 vertCenter'
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'F'#228'lligkeit f'#252'r neue Kontrollen:'
      end
      object ButtonKontrollberichte: TIWCGJQButton
        AlignWithMargins = True
        Left = 177
        Top = 9
        Width = 213
        Height = 30
        Margins.Left = 32
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 6
        Version = '1.0'
        Align = alLeft
        JQButtonOptions.Label_ = 'Kontrollberichte generieren'
        JQButtonOptions.OnClick.OnEvent = ButtonKontrollberichteJQButtonOptionsClick
      end
      object ButtonAuftraege: TIWCGJQButton
        AlignWithMargins = True
        Left = 9
        Top = 9
        Width = 128
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Version = '1.0'
        Align = alLeft
        JQButtonOptions.Label_ = 'Auftr'#228'ge laden'
        JQButtonOptions.OnClick.OnEvent = jqbSuchenJQButtonOptionsClick
      end
      object DatepickerFrist: TIWCGJQDatePicker
        AlignWithMargins = True
        Left = 649
        Top = 9
        Width = 121
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 4
        Css = 'ui-widget ui-widget-content ui-corner-all h3'
        Version = '1.0'
        Align = alLeft
        Caption = ''
        JQDatePickerOptions.AutoSize = True
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.DayNames.Strings = (
          'Sonntag'
          'Montag'
          'Dienstag'
          'Mittwoch'
          'Donnerstag'
          'Freitag'
          'Samstag')
        JQDatePickerOptions.DayNamesMin.Strings = (
          'So'
          'Mo'
          'Di'
          'Mi'
          'Do'
          'Fr'
          'Sa')
        JQDatePickerOptions.DayNamesShort.Strings = (
          'Son'
          'Mon'
          'Die'
          'Mit'
          'Don'
          'Fre'
          'Sam')
        JQDatePickerOptions.MonthNames.Strings = (
          'Januar'
          'Februar'
          'M'#228'rz'
          'April'
          'Mai'
          'Juni'
          'Juli'
          'August'
          'September'
          'Oktober'
          'November'
          'Dezember')
        JQDatePickerOptions.MonthNamesShort.Strings = (
          'Jan'
          'Feb'
          'M'#228'r'
          'Apr'
          'Mai'
          'Jun'
          'Jul'
          'Aug'
          'Sep'
          'Okt'
          'Nov'
          'Dez')
      end
    end
  end
  object ProviderAuftraege: TIWCGJQGridDataSetProvider
    DataSet = DMAma.quAuftraege
    Left = 777
    Top = 9
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 944
    Top = 16
  end
  object Wait: TIWCGJQSweetAlert
    Version = '1.0'
    JQSweetAlertOptions.Title = 'Bitte warten!'
    JQSweetAlertOptions.Text = 'Daten werden verarbeitet ...'
    JQSweetAlertOptions.AlertType = jqsatInfo
    JQSweetAlertOptions.AllowEscapeKey = False
    JQSweetAlertOptions.ShowConfirmButton = False
    Left = 1032
    Top = 16
  end
end
