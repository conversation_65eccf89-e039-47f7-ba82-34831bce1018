unit Dialogs.Pdf;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWBaseComponent, IWBaseHT<PERSON>Component, IWBaseHTML40Component,
  IWCGJQComp, IWCGJQSweetAlert, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQPDFViewer;

type
  TDialogPdf = class(TDialogBase)
    PdfViewer: TIWCGJQPDFViewer;
  private
    { Private declarations }
  public
    procedure ShowPdf(APdfFileName:string);
    procedure InitializeControls; override;
  end;


implementation

uses
  IWApplication, System.IOUtils, IWGlobal, IWAppCache, IWMimeTypes;

{$R *.dfm}

{ TDialogPdf }

procedure TDialogPdf.InitializeControls;
begin
  inherited;

end;

procedure TDialogPdf.ShowPdf(APdfFileName: string);
begin
  var LPdfFile :=  TIWAppCache.AddFileToCache(WebApplication, APdfFileName, 'APPLICATION/PDF');
  PdfViewer.JQPDFViewerOptions.PDFFileName := LPdfFile;
  Show(false);
end;

end.
