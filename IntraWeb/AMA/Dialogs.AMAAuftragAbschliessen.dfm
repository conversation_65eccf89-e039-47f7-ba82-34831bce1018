inherited DialogAMAAuftragAbschliessen: TDialogAMAAuftragAbschliessen
  Width = 1200
  Height = 518
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 1200
    Height = 518
    TabOrder = 3
    JQDialogOptions.Height = 518
    JQDialogOptions.Title = 'Auftrag bewerten'
    JQDialogOptions.Width = 1200
    inherited RegionContent: TIWCGJQRegion
      Width = 1200
      Height = 458
      TabOrder = 4
      object RegionModul: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1200
        Height = 65
        TabOrder = 7
        Version = '1.0'
        Align = alTop
        object IWCGJQLabel1: TIWCGJQLabel
          Left = 3
          Top = 10
          Width = 42
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel1'
          Caption = 'Modul:'
        end
        object LabelStorno: TIWCGJQLabel
          Left = 312
          Top = 37
          Width = 449
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'LabelStorno'
          Caption = 'LabelStorno'
        end
        object ComboModule: TIWCGJQComboBoxEx
          Left = 3
          Top = 32
          Width = 254
          Height = 21
          TabOrder = 11
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Items = <>
          Groups = <>
          SelectedIndex = 0
          JQComboBoxExOptions.Width = 252
          Caption = ''
        end
        object ButtonModulHinzufuegen: TIWCGJQButton
          AlignWithMargins = True
          Left = 1092
          Top = 23
          Width = 100
          Height = 34
          Margins.Left = 8
          Margins.Top = 23
          Margins.Right = 8
          Margins.Bottom = 8
          TabOrder = 9
          Version = '1.0'
          Align = alRight
          JQButtonOptions.Label_ = 'Hinzuf'#252'gen'
          JQButtonOptions.OnClick.OnEvent = ButtonModulHinzufuegenClick
          JQButtonOptions.OnClick.SendAllArguments = True
        end
        object ButtonModulStornieren: TIWCGJQButton
          AlignWithMargins = True
          Left = 976
          Top = 23
          Width = 100
          Height = 34
          Margins.Left = 8
          Margins.Top = 23
          Margins.Right = 8
          Margins.Bottom = 8
          TabOrder = 12
          Version = '1.0'
          Align = alRight
          JQButtonOptions.Label_ = 'Stornieren'
          JQButtonOptions.OnClick.OnEvent = ButtonModulStornierenClick
          JQButtonOptions.OnClick.SendAllArguments = True
        end
      end
      object GridModule: TIWCGJQGrid
        Left = 0
        Top = 65
        Width = 1200
        Height = 393
        TabOrder = 10
        Version = '1.0'
        Align = alClient
        CGScrollStyle = cgsbsVerticalAuto
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Modul'
            Name = 'Modul'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Modul'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Anforderung'
            Name = 'Anforderung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Anforderung'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'Kontrolliert'
            Name = 'Kontrolliert'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Kontrolliert'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditRules.OnCustom_Func.Script = 'function (grid,value,name,valref){'#13#10#13#10'}'
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'Ok'
            Name = 'Ok'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Ok'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'Auffaellig'
            Name = 'Auffaellig'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Auffaellig'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'GerVerstossOk'
            Name = 'GerVerstossOk'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'GerVerstossOk'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'Vorsatz'
            Name = 'Vorsatz'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Vorsatz'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.Value = '0:0;1:1;3:3;5:5'
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetSelect
            Formatter = gcfInteger
            Idx = 'Ausmass'
            Name = 'Ausmass'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Ausmass'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.Value = '0:0;1:1;3:3;5:5;10:10'
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetSelect
            Formatter = gcfInteger
            Idx = 'Schwere'
            Name = 'Schwere'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Schwere'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.Value = '0:0;1:1;5:5'
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetSelect
            Formatter = gcfInteger
            Idx = 'Dauer'
            Name = 'Dauer'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Dauer'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetTextArea
            Idx = 'Bemerkung'
            Name = 'Bemerkung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bemerkung'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'KZ_HV'
            Name = 'KZ_HV'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Hist. Verstoss'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetButton
            Formatter = gcfActions
            FormatOptions.DelButton = False
            FormatOptions.EditOptions.SaveKeys.Enable = True
            Idx = 'EDIT'
            Name = 'EDIT'
            Search = False
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 60
          end>
        JQGridOptions.DeselectAfterSort = False
        JQGridOptions.ForceFit = True
        JQGridOptions.Height = 339
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1198
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = GridModuleJQGridOptionsLoadComplete
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderModule
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 468
      Width = 1198
      TabOrder = 2
      inherited ButtonCancel: TIWCGJQButton
        Left = 1090
        TabOrder = 1
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 1082
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          Left = 974
          TabOrder = 5
          JQButtonOptions.Label_ = 'Speichern'
        end
        object IWCGJQRegion1: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 966
          Height = 50
          TabOrder = 8
          Version = '1.0'
          Align = alClient
          object ButtonAbschliessen: TIWCGJQButton
            AlignWithMargins = True
            Left = 752
            Top = 8
            Width = 206
            Height = 34
            Margins.Left = 8
            Margins.Top = 8
            Margins.Right = 8
            Margins.Bottom = 8
            TabOrder = 6
            Version = '1.0'
            Align = alRight
            JQButtonOptions.Label_ = 'Bewertung abschlie'#223'en'
            JQButtonOptions.OnClick.OnEvent = ButtonAbschliessenJQButtonOptionsClick
            JQButtonOptions.OnClick.SendAllArguments = True
          end
        end
      end
    end
  end
  inherited Alert: TIWCGJQSweetAlert
    Left = 264
  end
  object ProviderModule: TIWCGJQGridDataSetProvider
    DataSet = DMAma.MTAuswahldatenBewertungen
    KeyFields = 'ID'
    Left = 560
    Top = 124
  end
end
