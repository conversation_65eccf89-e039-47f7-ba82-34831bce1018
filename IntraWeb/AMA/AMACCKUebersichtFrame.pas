﻿unit AMACCKUebersichtFrame;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQGrid, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQButton, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompGrids, IWCompListbox,
  IWCGJQComboBox, IWCGJQRegion,
  ServerController,
  Frames.Base,
  Modules.AMA, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWCompLabel,
  IWCGJQLabel, IWCGJQDatePicker;

type
  TAMACCKUebersicht = class(TFrameBase<TDMAma>)
  //   TAMACCKUebersicht = class(TFrameBase)
    RegionContent: TIWCGJQRegion;
    RegionToolbar: TIWCGJQRegion;
    ProviderAuftraege: TIWCGJQGridDataSetProvider;
    GridAuftraege: TIWCGJQGrid;
    ButtonKontrollberichte: TIWCGJQButton;
    ButtonAuftraege: TIWCGJQButton;
    Alert: TIWCGJQSweetAlert;
    Wait: TIWCGJQSweetAlert;
    IWCGJQLabel1: TIWCGJQLabel;
    DatepickerFrist: TIWCGJQDatePicker;
    procedure GridAuftraegeOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure jqbSuchenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure GridAuftraegeJQGridOptionsDblClickRow(Sender: TObject; AParams: TStringList);
    procedure ButtonKontrollberichteJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure IWCGJQButton1JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
  public
    { Public declarations }
    constructor Create(AOwner: TComponent); override;
    procedure AuftraegeLaden;
  end;

implementation

uses
  System.DateUtils,
  JQ.Helpers.Grid, JQ.Helpers.Button,
  Utility, ELKE.Classes.RESTError,
  Dialogs.AMAAuftragDetail;

{$R *.dfm}


procedure TAMACCKUebersicht.AuftraegeLaden;
begin
  DM.AuftraegeLaden;
end;

procedure TAMACCKUebersicht.ButtonKontrollberichteJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  var
  LAusgewählteAufträge := GridAuftraege.JQGridOptions.SelRows;

  if Length(LAusgewählteAufträge) = 0 then
  begin
    Alert.Error('Kein Auftrag ausgewählt!');
    Abort;
  end;

  var
  i := 0;
  var
  LErrCount := 0;
  var
  LError := '';
  for var LAuftrag in LAusgewählteAufträge do
  begin
    DM.SelectAuftrag(LAuftrag);
    if not DM.AuftragHatKontrollen then
    begin
      try
        DM.KontrolleFuerAuftragErzeugen(DatepickerFrist.Date);
        inc(i);
      except
        inc(LErrCount);
        // Bei einem einzelnen Auftrag zeigen wir die Fehlermeldung an.
        if Length(LAusgewählteAufträge) = 1 then
          raise;
      end;
    end
    else
    begin
      LError := 'Es wurden keine Kontrollen generiert. Der gewählte Auftrag hat bereits Kontrollen!';
    end;
  end;
  DM.AuftraegeLaden;
  if LError <> '' then
  begin
    Alert.Info(LError);
  end
  else
  begin
    Alert.Info
      (Format('Es wurde(n) %d Kontrolle(n) generiert!'#13#10' %d Aufträge konnten wegen Fehlern nicht verarbeitet werden!',
      [i, LErrCount]));
  end;
end;

constructor TAMACCKUebersicht.Create(AOwner: TComponent);
begin
  inherited;
  ProviderAuftraege.DataSet := DM.quAuftraege;
  GridAuftraege.SetupDefaults(ProviderAuftraege);

  AuftraegeLaden;

  ButtonKontrollberichte.AsyncShowWaitWheel;
  DatepickerFrist.Date := Today;

  TDialogAMAAuftragsDetails.Create(self, DM);
end;

// Wenn eine Reihe im Grid ausgewählt wird, öffnet sich eine Detailansicht von
// diesem Auftrag.
procedure TAMACCKUebersicht.GridAuftraegeJQGridOptionsDblClickRow(Sender: TObject; AParams: TStringList);
begin
  Utility.moveQueryToRow(GridAuftraege);
  var
  LDialog := TDialogAMAAuftragsDetails.Create(self, DM);
  LDialog.Show(true, true,
    procedure
    begin
      DM.quAuftraege.Refresh;
      GridAuftraege.JQGridOptions.ReloadGrid;
    end);
end;

procedure TAMACCKUebersicht.GridAuftraegeOnSelectRow(Sender: TObject; AParams: TStringList);
begin
  GridAuftraege.SelectRecordFromCurrentRow;
end;

procedure TAMACCKUebersicht.IWCGJQButton1JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  var
  LDialog := TDialogAMAAuftragsDetails.Create(self, DM);
  LDialog.Show(true);
  // GridAuftraegeJQGridOptionsDblClickRow(Sender, AParams);
end;

procedure TAMACCKUebersicht.jqbSuchenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  AuftraegeLaden;
end;

end.
