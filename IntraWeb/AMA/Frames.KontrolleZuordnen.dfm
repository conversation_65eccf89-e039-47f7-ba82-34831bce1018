inherited DialogKontrolleZuordnen: TDialogKontrolleZuordnen
  OnCreate = IWCGJQFrameCreate
  inherited IWFrameRegion: TIWCGJQDialog
    TabOrder = 5
    JQDialogOptions.Title = 'Kontrolle ausw'#228'hlen und zuordnen'
    inherited RegionContent: TIWCGJQRegion
      TabOrder = 2
      object GridKontrollen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 600
        Height = 340
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKB'
            Name = 'BKB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BKB'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'DATUM'
            Name = 'DATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'DATUM'
          end>
        JQGridOptions.Height = 286
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 598
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderKontrollen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      TabOrder = 4
      inherited ButtonCancel: TIWCGJQButton
        TabOrder = 3
      end
      inherited RegionOK: TIWCGJQRegion
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          TabOrder = 1
        end
      end
    end
  end
  inherited Alert: TIWCGJQSweetAlert
    JQSweetAlertOptions.OnBtnClick.OnEvent = AlertJQSweetAlertOptionsBtnClick
    JQSweetAlertOptions.OnBtnClick.SendAllArguments = True
  end
  object ProviderKontrollen: TIWCGJQGridDataSetProvider
    DataSet = DMAma.QKontrollenFuerBetrieb
    AutoPost = False
    Left = 344
    Top = 140
  end
  object Alert2: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 8
    Top = 72
  end
end
