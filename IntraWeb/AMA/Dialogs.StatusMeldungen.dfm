inherited DialogStatusmeldungen: TDialogStatusmeldungen
  inherited IWFrameRegion: TIWCGJQDialog
    TabOrder = 1
    inherited RegionContent: TIWCGJQRegion
      TabOrder = 4
      object GridStatusMeldungen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 600
        Height = 250
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKB'
            Name = 'BKB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 250
            Caption = 'Kontrolle'
            CaptionAlign = gaLeft
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            Idx = 'MELDEDATUM'
            Name = 'MELDEDATUM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Datum'
            CaptionAlign = gaLeft
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEWERTUNGS_VERSION'
            Name = 'BEWERTUNGS_VERSION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Version'
            CaptionAlign = gaLeft
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STATUS'
            Name = 'STATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Status'
            CaptionAlign = gaLeft
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KONTAKT'
            Name = 'KONTAKT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontakt'
            CaptionAlign = gaLeft
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetTextArea
            Idx = 'KOMMENTAR'
            Name = 'KOMMENTAR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 500
            Caption = 'Kommentar'
            CaptionAlign = gaLeft
          end>
        JQGridOptions.Height = 196
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 598
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = GridStatusMeldungenJQGridOptionsSelectRow
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderStatusMeldungen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object MemoKommentar: TIWCGJQMemoEx
        Left = 0
        Top = 250
        Width = 600
        Height = 90
        TabOrder = 7
        Css = ''
        Version = '1.0'
        Align = alBottom
        ZIndex = 0
        BGColor = clNone
        Editable = False
        Required = False
        SubmitOnAsyncEvent = True
        ReadOnly = True
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      TabOrder = 0
      inherited ButtonCancel: TIWCGJQButton
        Visible = False
        TabOrder = 5
      end
      inherited RegionOK: TIWCGJQRegion
        TabOrder = 2
        inherited ButtonOK: TIWCGJQButton
          TabOrder = 3
        end
      end
    end
  end
  object ProviderStatusMeldungen: TIWCGJQGridDataSetProvider
    DataSet = DMAma.QStatusMeldungen
    DataSource = DSStatusmeldungen
    Left = 76
    Top = 77
  end
  object DSStatusmeldungen: TDataSource
    DataSet = DMAma.QStatusMeldungen
    Left = 208
    Top = 84
  end
end
