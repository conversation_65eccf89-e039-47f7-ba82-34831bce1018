inherited VisOExImport: TVisOExImport
  Width = 1497
  Height = 910
  Align = alClient
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1497
    Height = 910
    RenderInvisibleControls = True
    TabOrder = 13
    BorderOptions.Style = cbsSolid
    object jqdAuftragsart: TIWCGJQDialog
      Left = 482
      Top = 271
      Width = 353
      Height = 166
      Visible = False
      TabOrder = 11
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 166
      JQDialogOptions.Modal = True
      JQDialogOptions.Title = 'Auftragsart erstellen'
      JQDialogOptions.Width = 353
      JQDialogOptions.zIndex = 5000
      object IWLabel30: TIWCGJQLabel
        Left = 24
        Top = 24
        Width = 83
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel30'
        Caption = 'Bezeichnung:'
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 113
        Top = 22
        Width = 220
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
    object progress: TIWCGJQRegion
      Left = 0
      Top = 48
      Width = 1497
      Height = 50
      RenderInvisibleControls = True
      TabOrder = 14
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object ButtonUpload: TIWCGJQFileUpload
        AlignWithMargins = True
        Left = 9
        Top = 9
        Width = 250
        Height = 32
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 16
        Version = '1.0'
        Align = alLeft
        JQFileUploadOptions.Multiple = False
        JQFileUploadOptions.AllowedExtensions.Strings = (
          'json')
        JQFileUploadOptions.OnComplete.OnEvent = ButtonUploadJQFileUploadOptionsComplete
        JQFileUploadOptions.OnComplete.SendAllArguments = True
        JQFileUploadOptions.Classes.Button = 'ui-button'
        JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
        JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
        JQFileUploadOptions.Classes.Fail = 'ui-state-error'
        JQFileUploadOptions.OnUpload.OnEvent = jquFileUploadJQFileUploadOptionsUpload
        JQFileUploadOptions.Tip = 'Datei zum Hochladen hier ablegen'
        JQFileUploadOptions.CancelCaption = 'Abbrechen'
        JQFileUploadOptions.FailedCaption = 'Fehlgeschlagen'
        JQFileUploadOptions.CanOverrideFile = True
        JQFileUploadOptions.OnError.OnEvent = jquFileUploadOnError
        JQFileUploadOptions.DragText = 'Files zum Hochladen hier ablegen'
        JQFileUploadOptions.UploadButtonText = 'VIS-OEx-Datei hochladen'
        JQFileUploadOptions.CancelButtonText = 'Abbrechen'
        JQFileUploadOptions.FailUploadText = 'Fehlgeschlagen'
        JQFileUploadOptions.MultipleFileDropNotAllowedMessage = 'Es darf nur eine Datei hochgeladen werden'
        JQFileUploadOptions.ShowUploadedFileList = False
        CanOverrideFile = True
        Enabled = False
      end
      object ButtonSpeichern: TIWCGJQButton
        AlignWithMargins = True
        Left = 275
        Top = 9
        Width = 151
        Height = 32
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 15
        Version = '1.0'
        Align = alLeft
        Enabled = False
        JQButtonOptions.Disabled = True
        JQButtonOptions.Label_ = 'Speichern'
        JQButtonOptions.OnClick.OnEvent = jqbSpeichernOnClick
      end
    end
    object iwrAllgemein: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1497
      Height = 48
      RenderInvisibleControls = True
      TabOrder = 17
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object IWLabel1: TIWCGJQLabel
        AlignWithMargins = True
        Left = 153
        Top = 9
        Width = 88
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Align = alLeft
        Css = 'ui-widget h2 vertCenter'
        RenderSize = False
        StyleRenderOptions.RenderSize = False
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Auftragsart:'
      end
      object IWLabel2: TIWCGJQLabel
        AlignWithMargins = True
        Left = 9
        Top = 9
        Width = 48
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Align = alLeft
        Css = 'ui-widget h2 vertCenter'
        RenderSize = False
        StyleRenderOptions.RenderSize = False
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel2'
        Caption = 'Jahr:'
      end
      object LabelJahr: TIWCGJQLabel
        AlignWithMargins = True
        Left = 73
        Top = 9
        Width = 64
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        Align = alLeft
        Css = 'ui-widget h2 vertCenter'
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelJahr'
      end
      object ComboAuftragsarten: TIWCGJQComboBoxEx
        AlignWithMargins = True
        Left = 257
        Top = 9
        Width = 200
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 6
        Version = '1.0'
        Align = alLeft
        StyleRenderOptions.RenderBorder = False
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 198
        JQComboBoxExOptions.OnChange.OnEvent = ComboAuftragsartenJQComboBoxExOptionsChange
        Caption = ''
      end
      object jqbAuftragsartErstellen: TIWCGJQButton
        AlignWithMargins = True
        Left = 473
        Top = 9
        Width = 220
        Height = 30
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 10
        Version = '1.0'
        Align = alLeft
        JQButtonOptions.Label_ = 'Neue Auftragsart erstellen'
        JQButtonOptions.OnClick.OnEvent = jqbAuftragsartErstellenOnClick
      end
    end
    object iwrAuftraege: TIWCGJQRegion
      Left = 0
      Top = 98
      Width = 1497
      Height = 265
      RenderInvisibleControls = True
      TabOrder = 18
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object iwrAuftraegePanel: TIWCGJQRegion
        Left = 1
        Top = 1
        Width = 436
        Height = 263
        RenderInvisibleControls = True
        TabOrder = 19
        Version = '1.0'
        Align = alLeft
        BorderOptions.Style = cbsSolid
        object iwplAuftraege: TIWCGPanelList
          Left = 0
          Top = 0
          Width = 436
          Height = 263
          TabOrder = 4
          Version = '1.0'
          Align = alClient
          Items = <>
        end
      end
      object iwrAuftrag: TIWCGJQRegion
        Left = 437
        Top = 1
        Width = 1059
        Height = 263
        Visible = False
        RenderInvisibleControls = True
        TabOrder = 20
        Version = '1.0'
        Align = alClient
        BorderOptions.Style = cbsSolid
        object IWLabel3: TIWCGJQLabel
          Left = 20
          Top = 18
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Vorname:'
        end
        object IWLabel4: TIWCGJQLabel
          Left = 20
          Top = 40
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Nachname:'
        end
        object IWLabel5: TIWCGJQLabel
          Left = 20
          Top = 62
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Plz:'
        end
        object IWLabel6: TIWCGJQLabel
          Left = 20
          Top = 84
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Ort:'
        end
        object IWLabel7: TIWCGJQLabel
          Left = 20
          Top = 106
          Width = 90
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Adresse:'
        end
        object IWLabel8: TIWCGJQLabel
          Left = 404
          Top = 18
          Width = 125
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'BbkNr:'
        end
        object IWLabel9: TIWCGJQLabel
          Left = 404
          Top = 40
          Width = 125
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Bundesland:'
        end
        object IWLabel10: TIWCGJQLabel
          Left = 404
          Top = 62
          Width = 125
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Gemeindekennzahl:'
        end
        object IWLabel11: TIWCGJQLabel
          Left = 404
          Top = 84
          Width = 125
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Tel. Festnetz:'
        end
        object IWLabel12: TIWCGJQLabel
          Left = 806
          Top = 128
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Info 3:'
        end
        object IWLabel13: TIWCGJQLabel
          Left = 806
          Top = 18
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Flag 1:'
        end
        object IWLabel14: TIWCGJQLabel
          Left = 806
          Top = 40
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Flag 2:'
        end
        object IWLabel15: TIWCGJQLabel
          Left = 806
          Top = 62
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Flag 3:'
        end
        object IWLabel16: TIWCGJQLabel
          Left = 806
          Top = 84
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Info 1:'
        end
        object IWLabel17: TIWCGJQLabel
          Left = 806
          Top = 106
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'Info 2:'
        end
        object iwlVorname: TIWCGJQLabel
          Left = 124
          Top = 18
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlNachname: TIWCGJQLabel
          Left = 124
          Top = 40
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlPlz: TIWCGJQLabel
          Left = 124
          Top = 62
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlOrt: TIWCGJQLabel
          Left = 124
          Top = 84
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlAdresse: TIWCGJQLabel
          Left = 124
          Top = 106
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlBbk: TIWCGJQLabel
          Left = 540
          Top = 18
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlBundesland: TIWCGJQLabel
          Left = 540
          Top = 40
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlGemeindekennzahl: TIWCGJQLabel
          Left = 540
          Top = 62
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlTelFestnetz: TIWCGJQLabel
          Left = 540
          Top = 84
          Width = 250
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlInfo3: TIWCGJQLabel
          Left = 852
          Top = 128
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlFlag1: TIWCGJQLabel
          Left = 852
          Top = 18
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlFlag2: TIWCGJQLabel
          Left = 852
          Top = 40
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlFlag3: TIWCGJQLabel
          Left = 852
          Top = 62
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlInfo1: TIWCGJQLabel
          Left = 852
          Top = 84
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
        object iwlInfo2: TIWCGJQLabel
          Left = 852
          Top = 106
          Width = 80
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'IWLabel3'
          Caption = 'IWLabel3'
        end
      end
    end
    object iwrBetriebsdatenAlle: TIWCGJQRegion
      Left = 0
      Top = 363
      Width = 1497
      Height = 547
      RenderInvisibleControls = True
      TabOrder = 21
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object iwrBetriebsdatenPanel: TIWCGJQRegion
        Left = 1
        Top = 1
        Width = 430
        Height = 545
        RenderInvisibleControls = True
        TabOrder = 22
        Version = '1.0'
        Align = alLeft
        BorderOptions.Style = cbsSolid
        object iwplBetriebsdaten: TIWCGPanelList
          Left = 0
          Top = 0
          Width = 430
          Height = 545
          TabOrder = 5
          Version = '1.0'
          Align = alClient
          Items = <>
        end
      end
      object iwrBetriebsdaten: TIWCGJQRegion
        Left = 431
        Top = 1
        Width = 1065
        Height = 545
        Visible = False
        RenderInvisibleControls = True
        TabOrder = 23
        Version = '1.0'
        Align = alClient
        BorderOptions.Style = cbsSolid
        object iwrBetrDaten: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 1065
          Height = 177
          RenderInvisibleControls = True
          Version = '1.0'
          Align = alTop
          BorderOptions.Style = cbsSolid
          object IWLabel20: TIWCGJQLabel
            Left = 410
            Top = 106
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Tierhalter:'
          end
          object IWLabel21: TIWCGJQLabel
            Left = 26
            Top = 18
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Betriebstyp:'
          end
          object IWLabel22: TIWCGJQLabel
            Left = 26
            Top = 40
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Betriebsart:'
          end
          object IWLabel23: TIWCGJQLabel
            Left = 26
            Top = 62
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Betrnr:'
          end
          object IWLabel24: TIWCGJQLabel
            Left = 26
            Top = 84
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Plz:'
          end
          object IWLabel25: TIWCGJQLabel
            Left = 26
            Top = 106
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Ort:'
          end
          object IWLabel31: TIWCGJQLabel
            Left = 410
            Top = 18
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Adresse:'
          end
          object IWLabel32: TIWCGJQLabel
            Left = 410
            Top = 40
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Gemeindekennzahl:'
          end
          object IWLabel33: TIWCGJQLabel
            Left = 410
            Top = 62
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Ln Fl'#228'che:'
          end
          object IWLabel34: TIWCGJQLabel
            Left = 410
            Top = 84
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'TGD:'
          end
          object iwlAdresseBet: TIWCGJQLabel
            Left = 546
            Top = 18
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlBetriebsart: TIWCGJQLabel
            Left = 130
            Top = 40
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlBetriebstyp: TIWCGJQLabel
            Left = 130
            Top = 18
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlBetrnr: TIWCGJQLabel
            Left = 130
            Top = 62
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlGemeindekennzahlBet: TIWCGJQLabel
            Left = 546
            Top = 40
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlLnFlaeche: TIWCGJQLabel
            Left = 546
            Top = 62
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlOrtBet: TIWCGJQLabel
            Left = 130
            Top = 106
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlPlzBet: TIWCGJQLabel
            Left = 130
            Top = 84
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlTGD: TIWCGJQLabel
            Left = 546
            Top = 84
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object iwlTierhalter: TIWCGJQLabel
            Left = 546
            Top = 106
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
          object IWLabel35: TIWCGJQLabel
            Left = 75
            Top = 128
            Width = 41
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel35'
            Caption = 'Name:'
          end
          object iwlBetriebname: TIWCGJQLabel
            Left = 130
            Top = 128
            Width = 250
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'IWLabel3'
          end
        end
        object iwrBetriebPanels: TIWCGJQRegion
          Left = 0
          Top = 177
          Width = 1065
          Height = 368
          RenderInvisibleControls = True
          TabOrder = 1
          Version = '1.0'
          Align = alClient
          BorderOptions.Style = cbsSolid
          object iwplTierdaten: TIWCGPanelList
            Left = 710
            Top = 0
            Width = 350
            Height = 368
            TabOrder = 7
            Version = '1.0'
            Align = alLeft
            Items = <>
          end
          object iwplVokSanktionen: TIWCGPanelList
            Left = 355
            Top = 0
            Width = 350
            Height = 368
            TabOrder = 8
            Version = '1.0'
            Align = alLeft
            Items = <>
          end
          object iwplAuswahldaten: TIWCGPanelList
            Left = 0
            Top = 0
            Width = 350
            Height = 368
            TabOrder = 9
            Version = '1.0'
            Align = alLeft
            Items = <>
          end
          object IWRegion2: TIWCGJQRegion
            Left = 350
            Top = 0
            Width = 5
            Height = 368
            RenderInvisibleControls = True
            TabOrder = 2
            Version = '1.0'
            Align = alLeft
            BorderOptions.Style = cbsSolid
          end
          object IWRegion3: TIWCGJQRegion
            Left = 705
            Top = 0
            Width = 5
            Height = 368
            RenderInvisibleControls = True
            TabOrder = 3
            Version = '1.0'
            Align = alLeft
            BorderOptions.Style = cbsSolid
          end
        end
      end
    end
  end
  object quNeuerAuftrag: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.CCK_Auftrag (AuftragsId, Auftragsjahr' +
        ', Auftragsart, Lfbis_Hauptbetrieb, Bbknr, bldcode, vorname, nach' +
        'name, plz_bew,'
      
        '            ort_bew, adresse_bew, gemeindekz_bew, tel_festnetz, ' +
        'flag_1, flag_2, flag_3, info_1, info_2, info_3 ,Id_bearbeiter)'
      
        'VALUES      (:AuftragsId, :Auftragsjahr, :Auftragsart, :lfbis_hb' +
        ', :Bbknr, :bldcode, :vorname, :nachname, :plz_bew,'
      
        '            :ort_bew, :adresse_bew, :gemeindekz_bew, :tel_festne' +
        'tz, :flag_1, :flag_2, :flag_3, :info_1, :info_2, :info_3, :id_be' +
        'arbeiter);')
    Left = 864
    Top = 848
    ParamData = <
      item
        Name = 'AUFTRAGSID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUFTRAGSJAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUFTRAGSART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LFBIS_HB'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BBKNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VORNAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NACHNAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ_BEW'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ORT_BEW'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ADRESSE_BEW'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GEMEINDEKZ_BEW'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TEL_FESTNETZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FLAG_1'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FLAG_2'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FLAG_3'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'INFO_1'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'INFO_2'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'INFO_3'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_BEARBEITER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object jqsaAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 648
    Top = 8
  end
  object quNeueBetriebsdaten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.CCK_Betriebsdaten (ID_CCK_Auftrag, Be' +
        'triebstyp, Betriebsart, Lfbis, Plz_betr, Ort_betr, Adresse_Betr,'
      
        '                                              Gemeindekz_Betr, L' +
        'N_Flaeche, Tgd, Tierhalter, ID_Betrieb)'
      
        'VALUES      (:ID_CCK_Auftrag, :Betriebstyp, :Betriebsart, :Lfbis' +
        ', :Plz_betr, :Ort_betr, :Adresse_Betr, :Gemeindekz_Betr,'
      '             :LN_Flaeche, :Tgd, :Tierhalter, :ID_Betrieb);')
    Left = 960
    Top = 848
    ParamData = <
      item
        Name = 'ID_CCK_AUFTRAG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BETRIEBSART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LFBIS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ_BETR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ORT_BETR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ADRESSE_BETR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GEMEINDEKZ_BETR'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LN_FLAECHE'
        DataType = ftFloat
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TGD'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TIERHALTER'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_BETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeueAuswahldaten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.CCK_Auswahldaten (ID_CCK_Betriebsdate' +
        'n, Auswahl_ID, [Modul], Auswahldatum, Auswahlgrund, '
      '            Status)'
      
        'VALUES      (:ID_CCK_Betriebsdaten, :Auswahl_ID, :Modul, :Auswah' +
        'ldatum, :Auswahlgrund, '
      '             :Status);')
    Left = 1152
    Top = 848
    ParamData = <
      item
        Name = 'ID_CCK_BETRIEBSDATEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUSWAHL_ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MODUL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUSWAHLDATUM'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUSWAHLGRUND'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STATUS'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeueVokSanktion: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.CCK_Vok_Sanktionen (ID_CCK_Betriebsda' +
        'ten, Vok_Sanktionen_Id, Vok_Sank, Jahr)'
      
        'VALUES      (:ID_CCK_Betriebsdaten, :Vok_Sanktionen_Id, :Vok_San' +
        'k, :Jahr);')
    Left = 1256
    Top = 848
    ParamData = <
      item
        Name = 'ID_CCK_BETRIEBSDATEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VOK_SANKTIONEN_ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VOK_SANK'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'JAHR'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeueTierdaten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.CCK_Tierdaten (ID_CCK_Betriebsdaten, ' +
        'Tierdaten_Id, Tierkategorie, Anzahl, VIS_Tierart, VIS_PRBKat)'
      
        'VALUES      (:ID_CCK_Betriebsdaten, :Tierdaten_Id, :Tierkategori' +
        'e, :Anzahl, :VIS_Tierart, :VIS_PRBKat);')
    Left = 1056
    Top = 848
    ParamData = <
      item
        Name = 'ID_CCK_BETRIEBSDATEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TIERDATEN_ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TIERKATEGORIE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ANZAHL'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VIS_TIERART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VIS_PRBKAT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAuftragsarten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.CCK_Auftragsarten;')
    Left = 320
    Top = 56
  end
  object quNeueAuftragsart: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Stammdaten.CCK_Auftragsarten (Bezeichnung, Gesperrt)'
      'VALUES      (:bez, 0);')
    Left = 392
    Top = 57
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quBetriebFinden: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Betriebe'
      'WHERE  REGNR = :betrnr;')
    Left = 776
    Top = 844
    ParamData = <
      item
        Name = 'BETRNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quVisTierarten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.AMA_VIS_Tierarten;')
    Left = 688
    Top = 844
  end
  object AlertWaitImport: TIWCGJQSweetAlert
    Version = '1.0'
    JQSweetAlertOptions.Title = 'Bitte warten'
    JQSweetAlertOptions.Text = 'Der Import wird verarbeitet ...'
    JQSweetAlertOptions.AlertType = jqsatInfo
    JQSweetAlertOptions.ShowConfirmButton = False
    Left = 672
    Top = 160
  end
end
