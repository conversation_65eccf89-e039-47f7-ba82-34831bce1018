﻿unit VisOeOxImportFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, System.Json,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQFileUpload, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQEdit,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQSelectableList, IWCGJQButton, IWCGPanelList,
  IWCGJQCheckBoxList, Graphics, IWCGJQDialog, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQGrid, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWCompListbox,
  IWCGJQComboBox, IWCGJQRegion, IWCGJQLabel, IWCGJQProgressBar,JQ.Helpers.ComboboxEx,

  Frames.Base,
  Modules.AMA,
  ELKE.Classes.Generated,
  AMAClasses;

type
  // TVisOExImport = class(TFrameBase)
  TVisOExImport = class(TFrameBase<TDMAma>)
    progress: TIWCGJQRegion;
    ButtonUpload: TIWCGJQFileUpload;
    quNeuerAuftrag: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    LabelJahr: TIWCGJQLabel;
    iwrAllgemein: TIWCGJQRegion;
    iwrAuftraege: TIWCGJQRegion;
    ButtonSpeichern: TIWCGJQButton;
    iwplAuftraege: TIWCGPanelList;
    iwrAuftrag: TIWCGJQRegion;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    IWLabel11: TIWCGJQLabel;
    IWLabel12: TIWCGJQLabel;
    IWLabel13: TIWCGJQLabel;
    IWLabel14: TIWCGJQLabel;
    IWLabel15: TIWCGJQLabel;
    IWLabel16: TIWCGJQLabel;
    IWLabel17: TIWCGJQLabel;
    iwlVorname: TIWCGJQLabel;
    iwlNachname: TIWCGJQLabel;
    iwlPlz: TIWCGJQLabel;
    iwlOrt: TIWCGJQLabel;
    iwlAdresse: TIWCGJQLabel;
    iwlBbk: TIWCGJQLabel;
    iwlBundesland: TIWCGJQLabel;
    iwlGemeindekennzahl: TIWCGJQLabel;
    iwlTelFestnetz: TIWCGJQLabel;
    iwlInfo3: TIWCGJQLabel;
    iwlFlag1: TIWCGJQLabel;
    iwlFlag2: TIWCGJQLabel;
    iwlFlag3: TIWCGJQLabel;
    iwlInfo1: TIWCGJQLabel;
    iwlInfo2: TIWCGJQLabel;
    iwrAuftraegePanel: TIWCGJQRegion;
    iwrBetriebsdatenAlle: TIWCGJQRegion;
    iwrBetriebsdatenPanel: TIWCGJQRegion;
    iwrBetriebsdaten: TIWCGJQRegion;
    iwplBetriebsdaten: TIWCGPanelList;
    IWLabel21: TIWCGJQLabel;
    IWLabel22: TIWCGJQLabel;
    IWLabel23: TIWCGJQLabel;
    IWLabel24: TIWCGJQLabel;
    IWLabel25: TIWCGJQLabel;
    iwlBetriebstyp: TIWCGJQLabel;
    iwlBetriebsart: TIWCGJQLabel;
    iwlBetrnr: TIWCGJQLabel;
    iwlPlzBet: TIWCGJQLabel;
    iwlOrtBet: TIWCGJQLabel;
    IWLabel31: TIWCGJQLabel;
    IWLabel32: TIWCGJQLabel;
    IWLabel33: TIWCGJQLabel;
    IWLabel34: TIWCGJQLabel;
    iwlAdresseBet: TIWCGJQLabel;
    iwlGemeindekennzahlBet: TIWCGJQLabel;
    iwlLnFlaeche: TIWCGJQLabel;
    iwlTGD: TIWCGJQLabel;
    IWLabel20: TIWCGJQLabel;
    iwlTierhalter: TIWCGJQLabel;
    iwrBetrDaten: TIWCGJQRegion;
    iwrBetriebPanels: TIWCGJQRegion;
    iwplTierdaten: TIWCGPanelList;
    iwplVokSanktionen: TIWCGPanelList;
    iwplAuswahldaten: TIWCGPanelList;
    IWRegion2: TIWCGJQRegion;
    IWRegion3: TIWCGJQRegion;
    jqsaAlert: TIWCGJQSweetAlert;
    quNeueBetriebsdaten: TFDQuery;
    quNeueAuswahldaten: TFDQuery;
    quNeueVokSanktion: TFDQuery;
    quNeueTierdaten: TFDQuery;
    ComboAuftragsarten: TIWCGJQComboBoxEx;
    quAuftragsarten: TFDQuery;
    jqbAuftragsartErstellen: TIWCGJQButton;
    jqdAuftragsart: TIWCGJQDialog;
    IWLabel30: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    quNeueAuftragsart: TFDQuery;
    quBetriebFinden: TFDQuery;
    IWLabel35: TIWCGJQLabel;
    iwlBetriebname: TIWCGJQLabel;
    quVisTierarten: TFDQuery;
    AlertWaitImport: TIWCGJQSweetAlert;
    procedure jquFileUploadOnError(Sender: TObject; AParams: TStringList);
    procedure AuftragItemOnClick(Sender: TObject; AParams: TStringList);
    procedure BetriebsdatenItemOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbSpeichernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAuftragsartErstellenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqcAuftragsartenOnAsyncChange(Sender: TObject;
      EventParams: TStringList);
    procedure jquFileUploadJQFileUploadOptionsUpload(Sender: TObject; AParams: TStringList);
    procedure ComboAuftragsartenJQComboBoxExOptionsChange(Sender: TObject; AParams: TStringList);
    procedure ButtonUploadJQFileUploadOptionsComplete(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FCurrentImportFile: TCCKFile;
    FCurrentImportAuftrag: TCCKAuftrag;
    FCurrentImportBetriebsdaten: TCCKBetriebsdaten;
    indexOfAuftrag, indexOfBetriebsdaten: integer;
    procedure AuftragInitialisieren;
    procedure AuftragPanelAktualisieren;
    procedure BetriebsdatenInitialisieren;
    procedure BetriebsdatenPanelAktualisieren;
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure AuftragsartErstellen(Sender: TObject; AURLParams: TStringList);
    function AuftragSpeichern(auftrag: TCCKAuftrag): integer;
    function BetriebsdatenSpeichern(betriebsdaten: TCCKBetriebsdaten): integer;
    procedure AuswahldatenSpeichern(auswahldaten: TCCKAuswahldaten);
    procedure VokSanktionSpeichern(sanktion: TCCKVok_Sanktionen);
    procedure TierdatenSpeichern(tierdaten: TCCKTierdaten);
    procedure ResetFormular;
    procedure BetriebeZuordnen;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation

uses
  Generics.Collections, REST.Json, System.IOUtils,
  JQ.Helpers.Button,
  ServerController,
  dmmain, Utility,
  ELKE.Server.Logger;

{$R *.dfm}

procedure TVisOExImport.ComboAuftragsartenJQComboBoxExOptionsChange(Sender: TObject; AParams: TStringList);
begin
  inherited;
  ButtonUpload.Enabled := ComboAuftragsarten.SelectedText > '';
end;

constructor TVisOExImport.Create(AOwner: TComponent);
begin
  inherited;
  FCurrentImportFile := nil;
  FCurrentImportAuftrag := nil;
  iwplAuftraege.ItemHeight := 35;
  iwplBetriebsdaten.ItemHeight := 35;
  iwplAuswahldaten.ItemHeight := 45;
  iwplVokSanktionen.ItemHeight := 45;
  iwplTierdaten.ItemHeight := 45;

  ButtonSpeichern.AsyncShowWaitWheel;

  // Auftragsarten ausfüllen
  ComboAuftragsarten.Items.Add(''); // Leereintrag als erstes
  RefreshQuery(quAuftragsarten);
  quAuftragsarten.First;
  while not quAuftragsarten.Eof do
  begin
    if quAuftragsarten.FieldByName('Gesperrt').AsInteger = 0 then
      ComboAuftragsarten.Items.Add(quAuftragsarten.FieldByName('Bezeichnung').AsString);
    quAuftragsarten.Next;
  end;
end;

destructor TVisOExImport.Destroy;
begin
  FreeAndNil(FCurrentImportFile);
  inherited;
end;

// ****************************************************************************//
// ******************************Auftragsart***********************************//
// ****************************************************************************//

/// Öffnet einen Dialog in dem der User eine neue Auftragsart erstellen kann
procedure TVisOExImport.jqbAuftragsartErstellenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  DialogErstellen(jqdAuftragsart, AuftragsartErstellen, jqdAbbrechenEvent,
    'Auftragsart erstellen', 'Erstellen');
end;

procedure TVisOExImport.jqcAuftragsartenOnAsyncChange(Sender: TObject;
  EventParams: TStringList);
begin

end;

/// Eine neue Auftragsart in die Datenbank einfügen und zur ComboBox hinzufügen
procedure TVisOExImport.AuftragsartErstellen(Sender: TObject; AURLParams: TStringList);
var
  bez: string;
begin
  jqdAuftragsart.Visible := false;
  bez := jqeBezeichnung.Text;
  if bez = '' then
    Exit;
  quNeueAuftragsart.Close;
  quNeueAuftragsart.Prepare;
  quNeueAuftragsart.ParamByName('bez').AsString := bez;
  quNeueAuftragsart.Execute;
  ComboAuftragsarten.Items.Add(bez);
end;

// ****************************************************************************//
// ********************************Upload**************************************//
// ****************************************************************************//

/// Wenn der Upload des Files abgeschlossen ist, wird der Text des Files in
// einen String eingelesen welcher dann in ein JSonObjekt vom Typ TCCKFile
/// umgewandelt wird. TCCKFile und die anderen Klassen in AMAClasses sind genau
/// nach dem Json-File modeliert, damit die Übersetzung automatisch funktioneren
/// kann.
procedure TVisOExImport.jquFileUploadJQFileUploadOptionsUpload(Sender: TObject; AParams: TStringList);
begin
  AlertWaitImport.Show;
end;

// Sucht in der DB ob es schon einen Betrieb mit der Regnr gibt und weißt ihn
// dann den Betriebsdaten zu. Der LFBis_Hauptbetrieb des Auftrages wird auf die
// Betrnr der Betriebsdaten gesetzt, wo der Typ HB ist.
procedure TVisOExImport.BetriebeZuordnen;
begin
  for var auftrag in FCurrentImportFile.bewirtschafter do
  begin
    for var betriebsdaten in auftrag.betriebsdaten do
    begin
      quBetriebFinden.ParamByName('betrnr').AsString := betriebsdaten.betrnr;
      quBetriebFinden.Open;
      if quBetriebFinden.RecordCount > 0 then
      begin
        quBetriebFinden.First;
        betriebsdaten.id_betrieb := quBetriebFinden.FieldByName('id').AsInteger;
        betriebsdaten.betrieb_name := quBetriebFinden.FieldByName('name').AsString;
      end
      else
      begin
        betriebsdaten.id_betrieb := -1;
        betriebsdaten.betrieb_name := '-';
      end;
      quBetriebFinden.Close;

      if betriebsdaten.betriebstyp = 'HB' then
        auftrag.lfbis_hauptbetrieb := betriebsdaten.betrnr;
    end;
  end;
end;

// ****************************************************************************//
// *******************************Auftrag**************************************//
// ****************************************************************************//

/// Initialisiert das UI. Das Jahr wird gesetzt und für jeden Auftrag wird ein
/// ein Item zum AuftragsPanel hinzugefügt in dem mittels einer Ampel angezeigt
/// wird, ob alle Felder richtig ausgefüllt wurden.
procedure TVisOExImport.AuftragInitialisieren;
var
  item: TIWCGPanelListItem;
begin
  LabelJahr.Caption := '';
  iwplAuftraege.Items.Clear;

  if not Assigned(Self.FCurrentImportFile) then
    Abort;

  LabelJahr.Caption := IntToStr(FCurrentImportFile.jahr);

  for var auftrag in FCurrentImportFile.bewirtschafter do
  begin
    auftrag.auftragsjahr := FCurrentImportFile.jahr;
    // Der BLDCode wird einstellig übermittelt: 3 -> 300  (3 ist die REGION)
    Assert(auftrag.bldcode < 100);
    auftrag.bldcode := DM.GetBldCodeFromRegion(auftrag.bldcode);
    auftrag.bkb := DM.GetNeueBKBNummer;
    item := iwplAuftraege.Items.Add;
    item.Caption := auftrag.vorname + ' ' + auftrag.nachname;
    item.OnClick.OnEvent := AuftragItemOnClick;
    item.OnClick.Ajax := true;
  end;
  iwplAuftraege.AjaxReRender(false, true);
end;

/// Drückt der User auf ein Panelitem, wird zuerst bestimmt, auf welches er
/// gedrückt hat. Danach werden alle Felder im Dialog auf die Werte im Objekt
/// gesetzt.
procedure TVisOExImport.AuftragItemOnClick(Sender: TObject; AParams: TStringList);
var
  event: string;
  charArray: array[0..0] of Char;
  splits: TArray<string>;
  index: integer;
begin
  // Der Index vom gedrückten Item kann nur so bekommen werden
  event := AParams.ValueFromIndex[0];
  charArray[0] := '[';
  splits := event.Split(charArray);
  charArray[0] := ']';
  splits := splits[1].Split(charArray);
  index := StrToInt(splits[0]);

  FCurrentImportAuftrag := FCurrentImportFile.bewirtschafter[index];
  indexOfAuftrag := index;

  AuftragPanelAktualisieren;
  BetriebsdatenInitialisieren;
end;

/// Setzt die Werte im Auftrag Panel
procedure TVisOExImport.AuftragPanelAktualisieren;
begin
  iwlVorname.Caption := FCurrentImportAuftrag.vorname;
  iwlNachname.Caption := FCurrentImportAuftrag.nachname;
  iwlPlz.Caption := IntToStr(FCurrentImportAuftrag.plz_bew);
  iwlOrt.Caption := FCurrentImportAuftrag.ort_bew;
  iwlAdresse.Caption := FCurrentImportAuftrag.adresse_bew;
  iwlBbk.Caption := FCurrentImportAuftrag.bbknr;
  iwlBundesland.Caption := IntToStr(FCurrentImportAuftrag.bldcode);
  iwlGemeindekennzahl.Caption := IntToStr(FCurrentImportAuftrag.gemeindekz_bew);
  iwlTelFestnetz.Caption := FCurrentImportAuftrag.tel_festnetz;
  iwlFlag1.Caption := FCurrentImportAuftrag.flag_1;
  iwlFlag2.Caption := FCurrentImportAuftrag.flag_2;
  iwlFlag3.Caption := FCurrentImportAuftrag.flag_3;
  iwlInfo1.Caption := FCurrentImportAuftrag.info_1;
  iwlInfo2.Caption := FCurrentImportAuftrag.info_2;
  iwlInfo3.Caption := FCurrentImportAuftrag.info_3;

  iwrAuftrag.Visible := true;
  iwrBetriebsdaten.Visible := false;
  indexOfBetriebsdaten := -1;
end;

// ****************************************************************************//
// ***************************Betriebsdaten************************************//
// ****************************************************************************//

/// Erstellt für jeden Betriebsdaten-Eintrag einen Listeneintrag den der
/// Benutzer auswählen kann um die Details zu sehen
procedure TVisOExImport.BetriebsdatenInitialisieren;
var
  betriebsdaten: TCCKBetriebsdaten;
  item: TIWCGPanelListItem;
begin
  if not Assigned(Self.FCurrentImportAuftrag) then
    Exit;

  iwplBetriebsdaten.Items.Clear;
  for betriebsdaten in FCurrentImportAuftrag.betriebsdaten do
  begin
    item := iwplBetriebsdaten.Items.Add;
    item.Caption := betriebsdaten.betrnr;
    item.OnClick.Ajax := true;
    item.OnClick.OnEvent := BetriebsdatenItemOnClick;
  end;
  iwplBetriebsdaten.AjaxReRender(false, true);
end;

/// Wählt der User einen Eintrag im Betriebsdaten-Panel aus, werden alle Details
/// der Betriebsdaten angezeigt.
procedure TVisOExImport.BetriebsdatenItemOnClick(Sender: TObject; AParams: TStringList);
var
  event: string;
  charArray: array[0..0] of Char;
  splits: TArray<string>;
  index: integer;
begin
  // Der Index vom gedrückten Item kann nur so bekommen werden
  event := AParams.ValueFromIndex[0];
  charArray[0] := '[';
  splits := event.Split(charArray);
  charArray[0] := ']';
  splits := splits[1].Split(charArray);
  index := StrToInt(splits[0]);

  FCurrentImportBetriebsdaten := FCurrentImportAuftrag.betriebsdaten[index];
  indexOfBetriebsdaten := index;
  BetriebsdatenPanelAktualisieren;
end;

/// Setzt die Werte im Betriebsdaten Panel auf die der momementan ausgewählten
/// Betriebsdaten.
procedure TVisOExImport.BetriebsdatenPanelAktualisieren;
var
  auswahldaten: TCCKAuswahldaten;
  voksanktion: TCCKVok_Sanktionen;
  tierdaten: TCCKTierdaten;
  item: TIWCGPanelListItem;
begin
  iwlBetriebstyp.Caption := FCurrentImportBetriebsdaten.betriebstyp;
  iwlBetriebsart.Caption := FCurrentImportBetriebsdaten.betriebsart;
  iwlBetrnr.Caption := FCurrentImportBetriebsdaten.betrnr;
  iwlPlzBet.Caption := FCurrentImportBetriebsdaten.plz_betr;
  iwlOrtBet.Caption := FCurrentImportBetriebsdaten.ort_betr;
  iwlAdresseBet.Caption := FCurrentImportBetriebsdaten.adresse_betr;
  iwlGemeindekennzahlBet.Caption := IntToStr(FCurrentImportBetriebsdaten.gemeindekz_betr);
  iwlLnFlaeche.Caption := FCurrentImportBetriebsdaten.ln_flaeche.ToString;
  iwlTGD.Caption := FCurrentImportBetriebsdaten.tgd;
  iwlTierhalter.Caption := FCurrentImportBetriebsdaten.tierhalter;
  iwlBetriebname.Caption := FCurrentImportBetriebsdaten.betrieb_name;

  iwplAuswahldaten.Items.Clear;
  for auswahldaten in FCurrentImportBetriebsdaten.auswahldaten do
  begin
    item := iwplAuswahldaten.Items.Add;
    item.Caption := 'Modul: ' + auswahldaten.modul + '<br>' +
      'Auswahldatum: ' + DateToStr(auswahldaten.auswahldatum) + '<br>' +
      'Auswahlgrund: ' + auswahldaten.auswahlgrund;
  end;
  iwplAuswahldaten.AjaxReRender;

  iwplVokSanktionen.Items.Clear;
  for voksanktion in FCurrentImportBetriebsdaten.vok_sasnktionen do
  begin
    item := iwplVokSanktionen.Items.Add;
    item.Caption := 'Vok Sanktion: ' + voksanktion.vok_sank + '<br>' +
      'Jahr: ' + IntToStr(voksanktion.jahr) + '<br>'
  end;
  iwplVokSanktionen.AjaxReRender;

  iwplTierdaten.Items.Clear;
  for tierdaten in FCurrentImportBetriebsdaten.tierdaten do
  begin
    item := iwplTierdaten.Items.Add;
    item.Caption := 'Tierkategorie: ' + tierdaten.tierkategorie + '<br>' +
      'Anzahl: ' + IntToStr(tierdaten.anzahl) + '<br>'
  end;
  iwplTierdaten.AjaxReRender;

  iwrBetriebsdaten.Visible := true;
end;

// ****************************************************************************//
// ***************************In die DB speichern******************************//
// ****************************************************************************//

procedure TVisOExImport.jqbSpeichernOnClick(Sender: TObject;
  AParams: TStringList);
var
  auftrag: TCCKAuftrag;
  betriebsdaten: TCCKBetriebsdaten;
  auswahldaten: TCCKAuswahldaten;
  voksanktion: TCCKVok_Sanktionen;
  tierdaten: TCCKTierdaten;
  i, j, k: integer;
begin
  ButtonSpeichern.Enabled := false;

  // Überprüfen, ob alles soweit ausgefüllt ist, dass es in die DB geladen
  // werden kann.
  if not Assigned(FCurrentImportFile) then
    Exit;

  // Holt die Zuordnung von AMA zu VIS Tierarten
  RefreshQuery(quVisTierarten);

  dm_main.FBC_MAIN.StartTransaction;
  try
    // In die DB Speichern
    for i := 0 to Length(FCurrentImportFile.bewirtschafter) - 1 do
    begin
      auftrag := FCurrentImportFile.bewirtschafter[i];
      // AuftragsId ist die Reihenfolge vom Json (und vom array)
      auftrag.auftragsid := i;
      // In die Db speichern und Id setzen
      auftrag.id := AuftragSpeichern(auftrag);

      for j := 0 to Length(auftrag.betriebsdaten) - 1 do
      begin
        betriebsdaten := auftrag.betriebsdaten[j];
        betriebsdaten.id_cck_auftrag := auftrag.id;
        // Betriebsdaten speichern und Id setzen
        betriebsdaten.id := BetriebsdatenSpeichern(betriebsdaten);

        // Auswahldaten speichern
        for k := 0 to Length(betriebsdaten.auswahldaten) - 1 do
        begin
          auswahldaten := betriebsdaten.auswahldaten[k];
          auswahldaten.id_cck_betriebsdaten := betriebsdaten.id;
          auswahldaten.auswahl_id := k;
          AuswahldatenSpeichern(auswahldaten);
        end;
        // Vok_sanktionen speichern
        for k := 0 to Length(betriebsdaten.vok_sasnktionen) - 1 do
        begin
          voksanktion := betriebsdaten.vok_sasnktionen[k];
          voksanktion.id_cck_betriebsdaten := betriebsdaten.id;
          voksanktion.vok_sanktionen_id := k;
          VokSanktionSpeichern(voksanktion);
        end;
        // Tierdaten speichern
        for k := 0 to Length(betriebsdaten.tierdaten) - 1 do
        begin
          tierdaten := betriebsdaten.tierdaten[k];
          tierdaten.id_cck_betriebsdaten := betriebsdaten.id;
          tierdaten.tierdaten_id := k;
          TierdatenSpeichern(tierdaten);
        end;
      end;
    end;
    dm_main.FBC_MAIN.Commit;
    jqsaAlert.Info('Erfolgreich gespeichert!');
  except
    dm_main.FBC_MAIN.Rollback;
    jqsaAlert.Info('Es gab einen Fehler beim Speichern der Daten.');
  end;
  ResetFormular;
end;

function TVisOExImport.AuftragSpeichern(auftrag: TCCKAuftrag): integer;
begin
  quNeuerAuftrag.Close;
  quNeuerAuftrag.Prepare;
  quNeuerAuftrag.ParamByName('AuftragsId').AsInteger := auftrag.auftragsid;
  quNeuerAuftrag.ParamByName('Auftragsjahr').AsSmallInt := auftrag.auftragsjahr;
  quNeuerAuftrag.ParamByName('auftragsart').AsString := ComboAuftragsarten.SelectedText;
  quNeuerAuftrag.ParamByName('lfbis_hb').AsString := auftrag.lfbis_hauptbetrieb;
  quNeuerAuftrag.ParamByName('bbknr').AsString := auftrag.bbknr;
  quNeuerAuftrag.ParamByName('bldcode').AsInteger := auftrag.bldcode;
  quNeuerAuftrag.ParamByName('vorname').AsString := auftrag.vorname;
  quNeuerAuftrag.ParamByName('nachname').AsString := auftrag.nachname;
  quNeuerAuftrag.ParamByName('plz_bew').AsString := IntToStr(auftrag.plz_bew);
  quNeuerAuftrag.ParamByName('ort_bew').AsString := auftrag.ort_bew;
  quNeuerAuftrag.ParamByName('adresse_bew').AsString := auftrag.adresse_bew;
  quNeuerAuftrag.ParamByName('gemeindekz_bew').AsInteger := auftrag.gemeindekz_bew;
  quNeuerAuftrag.ParamByName('tel_festnetz').AsString := auftrag.tel_festnetz;
  quNeuerAuftrag.ParamByName('flag_1').AsString := auftrag.flag_1;
  quNeuerAuftrag.ParamByName('flag_2').AsString := auftrag.flag_2;
  quNeuerAuftrag.ParamByName('flag_3').AsString := auftrag.flag_3;
  quNeuerAuftrag.ParamByName('info_1').AsString := auftrag.info_1;
  quNeuerAuftrag.ParamByName('info_2').AsString := auftrag.info_2;
  quNeuerAuftrag.ParamByName('info_3').AsString := auftrag.info_3;
  quNeuerAuftrag.ParamByName('id_bearbeiter').AsInteger := dm_main.UserId;

  quNeuerAuftrag.Execute;
  Result := dm_main.FBC_MAIN.GetLastAutoGenValue('');
end;

function TVisOExImport.BetriebsdatenSpeichern(betriebsdaten: TCCKBetriebsdaten): integer;
begin
  quNeueBetriebsdaten.Close;
  quNeueBetriebsdaten.Prepare;
  quNeueBetriebsdaten.ParamByName('Id_cck_auftrag').AsInteger := betriebsdaten.id_cck_auftrag;
  quNeueBetriebsdaten.ParamByName('betriebstyp').AsString := betriebsdaten.betriebstyp;
  quNeueBetriebsdaten.ParamByName('betriebsart').AsString := betriebsdaten.betriebsart;
  quNeueBetriebsdaten.ParamByName('lfbis').AsString := betriebsdaten.betrnr;
  quNeueBetriebsdaten.ParamByName('plz_betr').AsString := betriebsdaten.plz_betr;
  quNeueBetriebsdaten.ParamByName('ort_betr').AsString := betriebsdaten.ort_betr;
  quNeueBetriebsdaten.ParamByName('adresse_betr').AsString := betriebsdaten.adresse_betr;
  quNeueBetriebsdaten.ParamByName('gemeindekz_betr').AsInteger := betriebsdaten.gemeindekz_betr;
  quNeueBetriebsdaten.ParamByName('ln_flaeche').AsFloat := betriebsdaten.ln_flaeche;
  quNeueBetriebsdaten.ParamByName('tgd').AsString := betriebsdaten.tgd;
  quNeueBetriebsdaten.ParamByName('tierhalter').AsString := betriebsdaten.tierhalter;
  if betriebsdaten.id_betrieb <> -1 then
    quNeueBetriebsdaten.ParamByName('id_betrieb').AsInteger := betriebsdaten.id_betrieb;
  quNeueBetriebsdaten.Execute;
  Result := dm_main.FBC_MAIN.GetLastAutoGenValue('');
end;

procedure TVisOExImport.ButtonUploadJQFileUploadOptionsComplete(Sender: TObject; AParams: TStringList);
var
  LImportContent: string;
  i: integer;
begin
  ELKELog('VIS OEx Import startet ...');
  ResetFormular;
  AlertWaitImport.Close;
  var LFilename := AParams.Values['fileName'];
  LFilename := TPath.Combine(TPath.GetFullPath(ButtonUpload.GetUploadPath), LFilename);
  ELKELog('VIS OEx Import-Datei: ' + LFilename);
  LImportContent := TFile.ReadAllText(LFilename, TEncoding.UTF8);
  FCurrentImportFile := nil;
  try
    FCurrentImportFile := TJson.JsonToObject<TCCKFile>(LImportContent);
    i := Length(FCurrentImportFile.bewirtschafter);
    BetriebeZuordnen;
    AuftragInitialisieren;
    ButtonSpeichern.Enabled := true;
    jqsaAlert.Info('Es wurden ' + i.ToString + ' Aufträge geladen.');
    ELKELog('VIS OEx Import: ' + i.ToString + ' Aufträge geladen.');
  except
    on e: Exception do
    begin
      FreeAndNil(FCurrentImportFile);
      jqsaAlert.Error('Beim Import ist ein Fehler aufgetreten: ' + e.Message);
      ELKELog('VIS OEx Import fehlgeschalgen: ' + e.Message);
    end;
  end;

end;

procedure TVisOExImport.AuswahldatenSpeichern(auswahldaten: TCCKAuswahldaten);
begin
  quNeueAuswahldaten.Close;
  quNeueAuswahldaten.Prepare;
  quNeueAuswahldaten.ParamByName('id_cck_betriebsdaten').AsInteger := auswahldaten.id_cck_betriebsdaten;
  quNeueAuswahldaten.ParamByName('auswahl_id').AsInteger := auswahldaten.auswahl_id;
  quNeueAuswahldaten.ParamByName('auswahldatum').AsDate := auswahldaten.auswahldatum;
  quNeueAuswahldaten.ParamByName('auswahlgrund').AsString := auswahldaten.auswahlgrund;
  quNeueAuswahldaten.ParamByName('modul').AsString := auswahldaten.modul;
  quNeueAuswahldaten.ParamByName('status').AsString := 'O';
  quNeueAuswahldaten.Execute;
end;

procedure TVisOExImport.VokSanktionSpeichern(sanktion: TCCKVok_Sanktionen);
begin
  quNeueVokSanktion.Close;
  quNeueVokSanktion.Prepare;
  quNeueVokSanktion.ParamByName('id_cck_betriebsdaten').AsInteger := sanktion.id_cck_betriebsdaten;
  quNeueVokSanktion.ParamByName('vok_sanktionen_id').AsInteger := sanktion.vok_sanktionen_id;
  quNeueVokSanktion.ParamByName('vok_sank').AsString := sanktion.vok_sank;
  quNeueVokSanktion.ParamByName('jahr').AsSmallInt := sanktion.jahr;
  quNeueVokSanktion.Execute;
end;

procedure TVisOExImport.TierdatenSpeichern(tierdaten: TCCKTierdaten);
var
  gefunden: boolean;
begin
  quNeueTierdaten.Close;
  quNeueTierdaten.Prepare;
  quNeueTierdaten.ParamByName('id_cck_betriebsdaten').AsInteger := tierdaten.id_cck_betriebsdaten;
  quNeueTierdaten.ParamByName('tierdaten_id').AsInteger := tierdaten.tierdaten_id;
  quNeueTierdaten.ParamByName('tierkategorie').AsString := tierdaten.tierkategorie;
  quNeueTierdaten.ParamByName('anzahl').AsInteger := tierdaten.anzahl;

  gefunden := false;
  quVisTierarten.First;
  while not quVisTierarten.Eof do
  begin
    if quVisTierarten.FieldByName('Tierart_Ama').AsString = tierdaten.tierkategorie then
    begin
      quNeueTierdaten.ParamByName('vis_tierart').AsString :=
        quVisTierarten.FieldByName('Tierart_vis').AsString;
      quNeueTierdaten.ParamByName('vis_prbkat').AsString :=
        quVisTierarten.FieldByName('Kateg_vis').AsString;
      gefunden := true;
      Break;
    end;

    quVisTierarten.Next;
  end;
  if not gefunden then
  begin
    quNeueTierdaten.ParamByName('vis_tierart').Clear;
    quNeueTierdaten.ParamByName('vis_prbkat').Clear;
  end;

  quNeueTierdaten.Execute;
end;

// ****************************************************************************//
// ********************************Allgemein***********************************//
// ****************************************************************************//

procedure TVisOExImport.jquFileUploadOnError(Sender: TObject;
  AParams: TStringList);
begin
  // OnError
end;

procedure TVisOExImport.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdAuftragsart.Visible := false;
end;

procedure TVisOExImport.ResetFormular;
begin
  FreeAndNil(Self.FCurrentImportFile);
  FreeAndNil(Self.FCurrentImportBetriebsdaten);
  iwrAuftrag.Visible := false;
  iwrBetriebsdaten.Visible := false;
  indexOfAuftrag := -1;
  indexOfBetriebsdaten := -1;

  iwplAuftraege.Items.Clear;
  iwplBetriebsdaten.Items.Clear;
  iwplAuftraege.AjaxReRender;
  iwplBetriebsdaten.AjaxReRender;

  ButtonSpeichern.Enabled := false;
end;

end.

