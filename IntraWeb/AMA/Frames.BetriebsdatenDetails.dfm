inherited FrameBetriebsdatenDetails: TFrameBetriebsdatenDetails
  Width = 205
  Height = 95
  OnCreate = IWCGJQFrameCreate
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 205
    Height = 95
    TabOrder = 1
    object IWCGJQLabel1: TIWCGJQLabel
      Left = 5
      Top = 5
      Width = 74
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWCGJQLabel1'
      Caption = 'Betriebstyp:'
    end
    object IWCGJQLabel2: TIWCGJQLabel
      Left = 5
      Top = 27
      Width = 70
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWCGJQLabel2'
      Caption = 'BetriebsNr:'
    end
    object LabelBetriebsTyp: TIWCGJQLabel
      Left = 81
      Top = 5
      Width = 106
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'LabelBetriebsTyp'
      Caption = 'LabelBetriebsTyp'
    end
    object LabelBetriebsNr: TIWCGJQLabel
      Left = 81
      Top = 27
      Width = 113
      Height = 16
      Anchors = [akLeft, akTop, akRight]
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'LabelBetriebsNr'
      Caption = 'IWCGJQLabel3'
    end
    object IWCGJQLabel3: TIWCGJQLabel
      Left = 5
      Top = 49
      Width = 56
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWCGJQLabel3'
      Caption = 'Adresse:'
    end
    object LabelAdresse: TIWCGJQLabel
      Left = 81
      Top = 49
      Width = 113
      Height = 16
      Anchors = [akLeft, akTop, akRight]
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'LabelAdresse'
      Caption = 'IWCGJQLabel3'
    end
    object IWCGJQLabel4: TIWCGJQLabel
      Left = 5
      Top = 71
      Width = 86
      Height = 16
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWCGJQLabel4'
      Caption = 'Durchgef'#252'hrt:'
    end
    object CheckboxDurchgefuehrt: TIWCGJQCheckBox
      Left = 93
      Top = 71
      Width = 28
      Height = 21
      Editable = False
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      SubmitOnAsyncEvent = True
      Style = stNormal
      Checked = False
      FriendlyName = 'CheckboxDurchgefuehrt'
    end
    object ButtonAktualisieren: TIWCGJQButton
      Left = 172
      Top = 66
      Width = 21
      Height = 21
      Hint = 'Details aktualisieren'
      TabOrder = 2
      Version = '1.0'
      Anchors = [akRight, akBottom]
      JQButtonOptions.Text = False
      JQButtonOptions.Icons.Primary = 'ui-icon-arrow-1-e'
      JQButtonOptions.OnClick.OnEvent = IWFrameRegionJQEventsClick
    end
    object ButtonKontrolleZuweisen: TIWCGJQButton
      Left = 145
      Top = 66
      Width = 21
      Height = 21
      Hint = 'Kontrolle zuweisen'
      Version = '1.0'
      Anchors = [akRight, akBottom]
      JQButtonOptions.Text = False
      JQButtonOptions.Icons.Primary = 'ui-icon-plus'
      JQButtonOptions.OnClick.OnEvent = ButtonKontrolleZuweisenJQButtonOptionsClick
    end
  end
end
