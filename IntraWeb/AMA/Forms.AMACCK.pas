﻿unit Forms.AMACCK;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Forms.Base, IWBaseComponent,
  IWBaseHT<PERSON>Component, IWBaseHTML40Component, IWCompExtCtrls, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQRegion, IWCGJQButton, Vcl.Imaging.jpeg, IWCompTabControl,
  IWCGJQLabel, IWCGJQTabs, IWCGJQComp, IWCGJQSweetAlert,
  AMACCKUebersichtFrame,
  AMACCKImportFrame,
  VisOeOxImportFrame;

type
  TFormAMACCK = class(TFormBase)
    RegionContent: TIWCGJQRegion;
    TabsAMA_CCK: TIWCGJQTabs;
    TabImport: TIWCGJQTab;
    TabUebersicht: TIWCGJQTab;
    TabImportVis: TIWCGJQTab;
    procedure AMACCKOnCreate(Sender: TObject);
    procedure TabsAMA_CCKJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FFrameImport: TAMACCKImport;
    FFrameImportVis: TVisOExImport;
    FFrameUebersicht: TAMACCKUebersicht;
  public
    { Public declarations }
  end;

implementation

{$R *.dfm}


procedure TFormAMACCK.AMACCKOnCreate(Sender: TObject);
begin
  FFrameImport := TAMACCKImport.Create(Self);
  FFrameImport.Parent := TabImport;

  FFrameImportVis := TVisOExImport.Create(Self);
  FFrameImportVis.Parent := TabImportVis;

  FFrameUebersicht := TAMACCKUebersicht.Create(Self);
  FFrameUebersicht.Parent := TabUebersicht;

  TabsAMA_CCK.ActiveTab := TabUebersicht;
end;

procedure TFormAMACCK.TabsAMA_CCKJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
begin
  if TabsAMA_CCK.ActiveTab = TabUebersicht then
  begin
    FFrameUebersicht.AuftraegeLaden;
  end;
end;

end.
