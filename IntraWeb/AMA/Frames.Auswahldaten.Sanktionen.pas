unit Frames.Auswahldaten.Sanktionen;

interface

uses
  SysUtils, Classes, Controls, IWCGFrame, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container,
  IWCGJQRegion,
  Vcl.Forms, IWRegion, IWCGJQControl, Frames.Base, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl,
  IWCompLabel, IWCGJQLabel, Modules.AMA, Data.DB;

type
  // TFrameAuswahldatenSanktionen = class(TFrameBase)
  TFrameAuswahldatenSanktionen = class(TFrameBase<TDMAma>)
    IWCGJQLabel1: TIWCGJQLabel;
    IWCGJQLabel2: TIWCGJQLabel;
    LabelJahr: TIWCGJQLabel;
    LabelSanktion: TIWCGJQLabel;
    procedure IWFrameRegionCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

implementation

{$R *.dfm}


procedure TFrameAuswahldatenSanktionen.IWFrameRegionCreate(Sender: TObject);
begin
  inherited;
  LabelSanktion.Caption := DM.QSanktionenVOK_SANK.AsString;
  LabelJahr.Caption := DM.QSanktionenJAHR.AsString;
end;

end.
