inherited DialogAMAAuftragsDetails: TDialogAMAAuftragsDetails
  Width = 1072
  Height = 700
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 1072
    Height = 700
    TabOrder = 4
    JQDialogOptions.Height = 700
    JQDialogOptions.Width = 1072
    inherited RegionContent: TIWCGJQRegion
      Width = 1072
      Height = 630
      Margins.Bottom = 16
      RenderInvisibleControls = True
      TabOrder = 10
      object RegionContentTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1072
        Height = 220
        TabOrder = 5
        Version = '1.0'
        Align = alTop
        object RegionBewirtschafterdaten: TIWCGJQRegion
          AlignWithMargins = True
          Left = 0
          Top = 42
          Width = 1070
          Height = 176
          Margins.Left = 0
          Margins.Top = 2
          Margins.Right = 2
          Margins.Bottom = 2
          TabOrder = 9
          Version = '1.0'
          Align = alClient
          BorderOptions.NumericWidth = 1
          BorderOptions.Style = cbsSolid
          object IWLabel10: TIWCGJQLabel
            Left = 330
            Top = 94
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Gemeindekennzahl:'
          end
          object IWLabel11: TIWCGJQLabel
            Left = 330
            Top = 116
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Tel. Festnetz:'
          end
          object IWLabel12: TIWCGJQLabel
            Left = 862
            Top = 116
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Info 3:'
          end
          object IWLabel13: TIWCGJQLabel
            Left = 718
            Top = 72
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Flag 1:'
          end
          object IWLabel14: TIWCGJQLabel
            Left = 718
            Top = 94
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Flag 2:'
          end
          object IWLabel15: TIWCGJQLabel
            Left = 718
            Top = 116
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Flag 3:'
          end
          object IWLabel16: TIWCGJQLabel
            Left = 862
            Top = 72
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Info 1:'
          end
          object IWLabel17: TIWCGJQLabel
            Left = 862
            Top = 94
            Width = 40
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Info 2:'
          end
          object IWLabel3: TIWCGJQLabel
            Left = 20
            Top = 50
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Vorname:'
          end
          object IWLabel4: TIWCGJQLabel
            Left = 20
            Top = 72
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Nachname:'
          end
          object IWLabel5: TIWCGJQLabel
            Left = 20
            Top = 94
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Plz:'
          end
          object IWLabel6: TIWCGJQLabel
            Left = 20
            Top = 116
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Ort:'
          end
          object IWLabel7: TIWCGJQLabel
            Left = 20
            Top = 138
            Width = 90
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Adresse:'
          end
          object IWLabel8: TIWCGJQLabel
            Left = 330
            Top = 50
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'BbkNr:'
          end
          object IWLabel9: TIWCGJQLabel
            Left = 330
            Top = 72
            Width = 125
            Height = 16
            Alignment = taRightJustify
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Bundesland:'
          end
          object iwlAdresse: TIWCGJQLabel
            Left = 124
            Top = 138
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'ADRESSE_BEW'
          end
          object iwlBbk: TIWCGJQLabel
            Left = 466
            Top = 50
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'BBKNr'
          end
          object iwlBundesland: TIWCGJQLabel
            Left = 466
            Top = 72
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'BLDCODE'
          end
          object iwlFlag1: TIWCGJQLabel
            Left = 764
            Top = 72
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'FLAG_1'
          end
          object iwlFlag2: TIWCGJQLabel
            Left = 764
            Top = 94
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'FLAG_2'
          end
          object iwlFlag3: TIWCGJQLabel
            Left = 764
            Top = 116
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'FLAG_3'
          end
          object iwlGemeindekennzahl: TIWCGJQLabel
            Left = 466
            Top = 94
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'GEMEINDEKZ_BEW'
          end
          object iwlInfo1: TIWCGJQLabel
            Left = 908
            Top = 72
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'INFO_1'
          end
          object iwlInfo2: TIWCGJQLabel
            Left = 908
            Top = 94
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'INFO_2'
          end
          object iwlInfo3: TIWCGJQLabel
            Left = 908
            Top = 116
            Width = 80
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'INFO_3'
          end
          object iwlNachname: TIWCGJQLabel
            Left = 124
            Top = 72
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'NACHNAME'
          end
          object iwlOrt: TIWCGJQLabel
            Left = 124
            Top = 116
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'ORT_BEW'
          end
          object iwlPlz: TIWCGJQLabel
            Left = 124
            Top = 94
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'PLZ_BEW'
          end
          object iwlTelFestnetz: TIWCGJQLabel
            Left = 466
            Top = 116
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'TEL_FESTNETZ'
          end
          object iwlVorname: TIWCGJQLabel
            Left = 124
            Top = 50
            Width = 200
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            DataLink.DataSource = DSAuftragDetails
            DataLink.FieldName = 'VORNAME'
          end
          object IWCGJQLabel4: TIWCGJQLabel
            Left = 718
            Top = 50
            Width = 171
            Height = 16
            Css = 'ui-widget h3'
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'IWLabel3'
            Caption = 'Zusatzinformationen:'
          end
          object RegionTitleBewirtschafter: TIWCGJQRegion
            Left = 1
            Top = 1
            Width = 1068
            Height = 24
            TabOrder = 22
            Version = '1.0'
            Align = alTop
            CGAppearanceSettings.Corners = False
            object LabelTitle: TIWCGJQLabel
              AlignWithMargins = True
              Left = 4
              Top = 4
              Width = 788
              Height = 16
              Margins.Left = 4
              Margins.Top = 4
              Margins.Right = 4
              Margins.Bottom = 4
              Align = alClient
              Css = 'ui-widget h2'
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              FriendlyName = 'LabelTitle'
              Caption = 'Bewirtschafterdaten:'
            end
            object LabelAuftragVersion: TIWCGJQLabel
              AlignWithMargins = True
              Left = 800
              Top = 4
              Width = 264
              Height = 16
              Margins.Left = 4
              Margins.Top = 4
              Margins.Right = 4
              Margins.Bottom = 4
              Align = alRight
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              NoWrap = True
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'LabelAuftragVersion'
              Caption = 'Version 0'
            end
          end
        end
        object IWCGJQRegion1: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 1072
          Height = 40
          TabOrder = 26
          Version = '1.0'
          Align = alTop
          object LabelAuftrag: TIWCGJQLabel
            AlignWithMargins = True
            Left = 4
            Top = 8
            Width = 333
            Height = 28
            Margins.Left = 4
            Margins.Top = 8
            Margins.Right = 4
            Margins.Bottom = 4
            Align = alLeft
            Css = 'ui-widget h2'
            Font.Color = clNone
            Font.Size = 12
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelAuftrag'
            Caption = 'LabelAuftrag'
          end
          object LabelStatus: TIWCGJQLabel
            AlignWithMargins = True
            Left = 345
            Top = 8
            Width = 483
            Height = 28
            Margins.Left = 4
            Margins.Top = 8
            Margins.Right = 4
            Margins.Bottom = 4
            Align = alClient
            Css = 'ui-widget h2'
            Font.Color = clNone
            Font.Size = 12
            Font.Style = []
            HasTabOrder = False
            AutoSize = False
            FriendlyName = 'LabelAuftrag'
            Caption = 'Status:'
          end
          object ButtonStatusmeldungen: TIWCGJQButton
            AlignWithMargins = True
            Left = 836
            Top = 8
            Width = 232
            Height = 28
            Margins.Left = 4
            Margins.Top = 8
            Margins.Right = 4
            Margins.Bottom = 4
            TabOrder = 27
            Version = '1.0'
            Align = alRight
            JQButtonOptions.Label_ = 'Alle Statusmeldungen ...'
            JQButtonOptions.OnClick.OnEvent = ButtonStatusmeldungenJQButtonOptionsClick
          end
        end
      end
      object RegionContentMain: TIWCGJQRegion
        Left = 0
        Top = 220
        Width = 1072
        Height = 410
        RenderInvisibleControls = True
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        object RegionBetriebsdaten: TIWCGJQRegion
          AlignWithMargins = True
          Left = 0
          Top = 2
          Width = 297
          Height = 408
          Margins.Left = 0
          Margins.Top = 2
          Margins.Right = 2
          Margins.Bottom = 0
          TabOrder = 8
          Version = '1.0'
          Align = alLeft
          BorderOptions.NumericWidth = 1
          BorderOptions.Style = cbsSolid
          object IWCGJQLabel2: TIWCGJQLabel
            AlignWithMargins = True
            Left = 5
            Top = 5
            Width = 287
            Height = 16
            Margins.Left = 4
            Margins.Top = 4
            Margins.Right = 4
            Margins.Bottom = 16
            Align = alTop
            Css = 'ui-widget h2'
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWCGJQLabel1'
            Caption = 'Betriebsdaten:'
          end
          object ListBetriebsdaten: TIWCGJQResponsiveList
            AlignWithMargins = True
            Left = 5
            Top = 37
            Width = 287
            Height = 366
            Margins.Left = 4
            Margins.Top = 0
            Margins.Right = 4
            Margins.Bottom = 4
            TabOrder = 11
            Version = '1.0'
            Align = alClient
            CGScrollStyle = cgsbsVerticalAuto
            JQResponsiveListOptions.View = rlvList
            JQResponsiveListOptions.Padding = 2
          end
        end
        object RegionAuswahldaten: TIWCGJQRegion
          AlignWithMargins = True
          Left = 301
          Top = 2
          Width = 769
          Height = 408
          Margins.Left = 2
          Margins.Top = 2
          Margins.Right = 2
          Margins.Bottom = 0
          RenderInvisibleControls = True
          TabOrder = 7
          Version = '1.0'
          Align = alClient
          BorderOptions.NumericWidth = 1
          BorderOptions.Style = cbsSolid
          object IWCGJQLabel3: TIWCGJQLabel
            AlignWithMargins = True
            Left = 5
            Top = 5
            Width = 759
            Height = 16
            Margins.Left = 4
            Margins.Top = 4
            Margins.Right = 4
            Margins.Bottom = 16
            Align = alTop
            Css = 'ui-widget h2'
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWCGJQLabel1'
            Caption = 'Auswahldaten:'
          end
          object RegionAuswahldatenDetails: TIWCGJQRegion
            AlignWithMargins = True
            Left = 1
            Top = 236
            Width = 767
            Height = 163
            Margins.Left = 0
            Margins.Right = 0
            Margins.Bottom = 8
            RenderInvisibleControls = True
            TabOrder = 12
            Version = '1.0'
            Align = alClient
            object RegionModule: TIWCGJQRegion
              Left = 0
              Top = 0
              Width = 240
              Height = 163
              TabOrder = 16
              Version = '1.0'
              Align = alLeft
              object ListAuswahldatenModule: TIWCGJQResponsiveList
                AlignWithMargins = True
                Left = 8
                Top = 0
                Width = 224
                Height = 159
                Margins.Left = 8
                Margins.Top = 0
                Margins.Right = 8
                Margins.Bottom = 4
                RenderInvisibleControls = True
                TabOrder = 19
                Version = '1.0'
                Align = alClient
                CGScrollStyle = cgsbsVerticalAuto
                JQResponsiveListOptions.View = rlvList
                JQResponsiveListOptions.Padding = 2
              end
            end
            object RegionSanktionen: TIWCGJQRegion
              Left = 240
              Top = 0
              Width = 287
              Height = 163
              TabOrder = 17
              Version = '1.0'
              Align = alClient
              object ListAuswahldatenSanktionen: TIWCGJQResponsiveList
                AlignWithMargins = True
                Left = 8
                Top = 0
                Width = 271
                Height = 159
                Margins.Left = 8
                Margins.Top = 0
                Margins.Right = 8
                Margins.Bottom = 4
                RenderInvisibleControls = True
                TabOrder = 20
                Version = '1.0'
                Align = alClient
                CGScrollStyle = cgsbsVerticalAuto
                JQResponsiveListOptions.View = rlvList
                JQResponsiveListOptions.Padding = 2
              end
            end
            object RegionTierdaten: TIWCGJQRegion
              Left = 527
              Top = 0
              Width = 240
              Height = 163
              TabOrder = 18
              Version = '1.0'
              Align = alRight
              object ListAuswahldatenTierdaten: TIWCGJQResponsiveList
                AlignWithMargins = True
                Left = 8
                Top = 0
                Width = 224
                Height = 159
                Margins.Left = 8
                Margins.Top = 0
                Margins.Right = 8
                Margins.Bottom = 4
                RenderInvisibleControls = True
                TabOrder = 21
                Version = '1.0'
                Align = alClient
                CGScrollStyle = cgsbsVerticalAuto
                JQResponsiveListOptions.View = rlvList
                JQResponsiveListOptions.Padding = 2
              end
            end
          end
          object RegionAuswahldatenBetrieb: TIWCGJQRegion
            Left = 1
            Top = 37
            Width = 767
            Height = 196
            TabOrder = 13
            Version = '1.0'
            Align = alTop
            object IWLabel20: TIWCGJQLabel
              Left = 334
              Top = 98
              Width = 125
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel20'
              Caption = 'Tierhalter:'
            end
            object IWLabel21: TIWCGJQLabel
              Left = 28
              Top = 10
              Width = 90
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel21'
              Caption = 'Betriebstyp:'
            end
            object IWLabel22: TIWCGJQLabel
              Left = 28
              Top = 32
              Width = 90
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel22'
              Caption = 'Betriebsart:'
            end
            object IWLabel23: TIWCGJQLabel
              Left = 28
              Top = 54
              Width = 90
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel23'
              Caption = 'Betrnr:'
            end
            object IWLabel24: TIWCGJQLabel
              Left = 28
              Top = 76
              Width = 90
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel24'
              Caption = 'Plz:'
            end
            object IWLabel25: TIWCGJQLabel
              Left = 28
              Top = 98
              Width = 90
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel25'
              Caption = 'Ort:'
            end
            object IWLabel31: TIWCGJQLabel
              Left = 334
              Top = 10
              Width = 125
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel31'
              Caption = 'Adresse:'
            end
            object IWLabel32: TIWCGJQLabel
              Left = 334
              Top = 32
              Width = 125
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel32'
              Caption = 'Gemeindekennzahl:'
            end
            object IWLabel33: TIWCGJQLabel
              Left = 334
              Top = 54
              Width = 125
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel33'
              Caption = 'Ln Fl'#228'che:'
            end
            object IWLabel34: TIWCGJQLabel
              Left = 334
              Top = 76
              Width = 125
              Height = 16
              Alignment = taRightJustify
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'IWLabel34'
              Caption = 'TGD:'
            end
            object IWLabel35: TIWCGJQLabel
              Left = 77
              Top = 120
              Width = 41
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              FriendlyName = 'IWLabel35'
              Caption = 'Name:'
            end
            object iwlAdresseBet: TIWCGJQLabel
              Left = 465
              Top = 10
              Width = 250
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlAdresseBet'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'ADRESSE_BETR'
            end
            object iwlBetriebname: TIWCGJQLabel
              Left = 132
              Top = 120
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlBetriebname'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'NAME'
            end
            object iwlBetriebsart: TIWCGJQLabel
              Left = 132
              Top = 32
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlBetriebsart'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'BETRIEBSART'
            end
            object iwlBetriebstyp: TIWCGJQLabel
              Left = 132
              Top = 10
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlBetriebstyp'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'BETRIEBSTYP'
            end
            object iwlBetrnr: TIWCGJQLabel
              Left = 132
              Top = 54
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlBetrnr'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'LFBIS'
            end
            object iwlGemeindekennzahlBet: TIWCGJQLabel
              Left = 465
              Top = 32
              Width = 250
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlGemeindekennzahlBet'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'GEMEINDEKZ_BETR'
            end
            object iwlLnFlaeche: TIWCGJQLabel
              Left = 465
              Top = 54
              Width = 250
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlLnFlaeche'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'LN_FLAECHE'
            end
            object iwlOrtBet: TIWCGJQLabel
              Left = 132
              Top = 98
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlOrtBet'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'ORT_BETR'
            end
            object iwlPlzBet: TIWCGJQLabel
              Left = 132
              Top = 76
              Width = 200
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlPlzBet'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'PLZ_BETR'
            end
            object iwlTGD: TIWCGJQLabel
              Left = 465
              Top = 76
              Width = 250
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlTGD'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'TGD'
            end
            object iwlTierhalter: TIWCGJQLabel
              Left = 465
              Top = 98
              Width = 250
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              AutoSize = False
              FriendlyName = 'iwlTierhalter'
              Caption = 'IWLabel3'
              DataLink.DataSource = DSBetriebsDatenDetailsEinzeln
              DataLink.FieldName = 'TIERHALTER'
            end
            object ButtonKontrolbericht: TIWCGJQButton
              Left = 143
              Top = 141
              Width = 129
              Height = 50
              TabOrder = 14
              Version = '1.0'
              JQButtonOptions.Label_ = 'Kontrollbericht anzeigen'
              JQButtonOptions.OnClick.OnEvent = ButtonKontrolberichtJQButtonOptionsClick
              JQButtonOptions.Wrap = True
            end
            object ButtonKontrolldetails: TIWCGJQButton
              Left = 8
              Top = 141
              Width = 129
              Height = 50
              TabOrder = 15
              Version = '1.0'
              JQButtonOptions.Label_ = 'Kontrolldetails anzeigen'
              JQButtonOptions.OnClick.OnEvent = ButtonKontrolldetailsJQButtonOptionsClick
              JQButtonOptions.Wrap = True
            end
            object ButtonBewertungsblattAnzeigen: TIWCGJQButton
              Left = 278
              Top = 141
              Width = 129
              Height = 50
              TabOrder = 23
              Version = '1.0'
              JQButtonOptions.Label_ = 'Bewertungsblatt anzeigen'
              JQButtonOptions.OnClick.OnEvent = ButtonBewertungsblattAnzeigenJQButtonOptionsClick
              JQButtonOptions.Wrap = True
            end
            object ButtonBewertungsblattErzeugen: TIWCGJQButton
              Left = 413
              Top = 141
              Width = 129
              Height = 50
              TabOrder = 24
              Version = '1.0'
              JQButtonOptions.Label_ = 'Bewertungsblatt neu erzeugen'
              JQButtonOptions.OnClick.OnEvent = ButtonBewertungsblattErzeugenJQButtonOptionsClick
              JQButtonOptions.Wrap = True
            end
            object ButtonCCDokument: TIWCGJQButton
              Left = 548
              Top = 141
              Width = 129
              Height = 50
              TabOrder = 25
              Version = '1.0'
              JQButtonOptions.Label_ = 'CC-Dokument anzeigen'
              JQButtonOptions.OnClick.OnEvent = ButtonCCDokumentJQButtonOptionsClick
              JQButtonOptions.Wrap = True
            end
          end
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 650
      Width = 1070
      TabOrder = 3
      inherited ButtonCancel: TIWCGJQButton
        Left = 962
        TabOrder = 1
        JQButtonOptions.Label_ = 'Schlie'#223'en'
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 954
        TabOrder = 2
        inherited ButtonOK: TIWCGJQButton
          Left = 846
          TabOrder = 0
          JQButtonOptions.Label_ = 'Bewerten'
        end
      end
    end
  end
  inherited Alert: TIWCGJQSweetAlert
    Left = 16
    Top = 80
  end
  object DSAuftragDetails: TDataSource
    DataSet = DMAma.QAuftragDetails
    Left = 280
    Top = 40
  end
  object DSBetriebsdatenDetails: TDataSource
    DataSet = DMAma.QBetriebsdatenDetails
    Left = 104
    Top = 376
  end
  object DSBetriebsDatenDetailsEinzeln: TDataSource
    DataSet = DMAma.QBetriebsdatenDetailsEinzeln
    Left = 904
    Top = 264
  end
  object DSModule: TDataSource
    DataSet = DMAma.QAuswahldatenModule
    Left = 408
    Top = 496
  end
  object DSTierdaten: TDataSource
    DataSet = DMAma.QTierdaten
    Left = 936
    Top = 512
  end
  object DSSanktionen: TDataSource
    DataSet = DMAma.QSanktionen
    Left = 664
    Top = 504
  end
end
