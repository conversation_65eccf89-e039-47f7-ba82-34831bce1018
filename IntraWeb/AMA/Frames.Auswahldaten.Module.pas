unit Frames.Auswahldaten.Module;

interface

uses
  SysUtils, Classes, Controls, IWCGFrame, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container,
  IWCGJQRegion,
  Vcl.Forms, IWRegion, IWCGJQControl, Frames.Base, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl,
  IWCompLabel, IWCGJQLabel, Modules.AMA, Data.DB;

type
  TFrameAuswahldatenModule = class(TFrameBase<TDMAma>)
    IWCGJQLabel1: TIWCGJQLabel;
    LabelModul: TIWCGJQLabel;
    IWCGJQLabel2: TIWCGJQLabel;
    LabelAuswahldatum: TIWCGJQLabel;
    IWCGJQLabel3: TIWCGJQLabel;
    LabelAuswahlgrund: TIWCGJQLabel;
    procedure IWCGJQFrameCreate(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

implementation

{$R *.dfm}


procedure TFrameAuswahldatenModule.IWCGJQFrameCreate(Sender: TObject);
begin
  inherited;
  LabelModul.Caption := DM.QAuswahldatenModuleModul.AsString;
  LabelAuswahldatum.Caption := DM.QAuswahldatenModuleAuswahldatum.AsString;
  LabelAuswahlgrund.Caption := DM.QAuswahldatenModuleAuswahlgrund.AsString;
end;

end.
