inherited GruppenF: TG<PERSON>penF
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 1
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 13
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID'
            Name = 'ID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'ID'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'BEZEICHNUNG'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BLDCODE'
            Name = 'BLDCODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BLDCODE'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'MUTTERGRUPPE'
            Name = 'MUTTERGRUPPE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'MUTTERGRUPPE'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'OKZ'
            Name = 'OKZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'OKZ'
            Position = 6
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID_USER_HAUPTVER'
            Name = 'ID_USER_HAUPTVER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'ID_USER_HAUPTVER'
            Position = 4
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ID_USER_STELLVER'
            Name = 'ID_USER_STELLVER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'ID_USER_STELLVER'
            Position = 5
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 5
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 6
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 15
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 9
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 310
      Top = 112
      Width = 347
      Height = 249
      TabOrder = 11
      JQDialogOptions.Height = 249
      JQDialogOptions.Width = 347
      object IWLabel1: TIWCGJQLabel
        Left = 32
        Top = 16
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 32
        Top = 43
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Muttergruppe:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 3
        Top = 70
        Width = 114
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'ID User Hauptver:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 16
        Top = 97
        Width = 101
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'ID User Stellver:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 32
        Top = 124
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Okz:'
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 123
        Top = 16
        Width = 200
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeMuttergruppe: TIWCGJQEdit
        Left = 123
        Top = 43
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeIDHauptver: TIWCGJQEdit
        Left = 123
        Top = 70
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeIdStellver: TIWCGJQEdit
        Left = 123
        Top = 97
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeOkz: TIWCGJQEdit
        Left = 123
        Top = 124
        Width = 200
        Height = 21
        TabOrder = 14
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quGruppen
    Left = 944
    Top = 16
  end
  object quGruppen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM systemstammdaten.[gruppen]'
      
        'WHERE UPPER(bezeichnung) LIKE '#39'%'#39' + :BEZ + '#39'%'#39' AND persoenlich =' +
        ' 0;')
    Left = 705
    Top = 18
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.[Gruppen] (bezeichnung, bldcode, mu' +
        'ttergruppe, id_user_hauptver, id_user_stellver, okz)'
      
        'VALUES      (:bezeichnung, :bldcode, :muttergruppe, :id_user_hau' +
        'ptver, :id_user_stellver, :okz);')
    Left = 761
    Top = 17
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MUTTERGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_HAUPTVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_STELLVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Gruppen'
      'SET    Bezeichnung = :bezeichnung, Muttergruppe = :muttergruppe,'
      
        '       ID_USER_HAUPTVER = :id_user_hauptver, ID_USER_STELLVER = ' +
        ':id_user_stellver, Okz = :okz'
      'WHERE  ID = :id;')
    Left = 817
    Top = 17
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MUTTERGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_HAUPTVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_STELLVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Gruppen'
      'WHERE       ID = :id;')
    Left = 880
    Top = 16
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
