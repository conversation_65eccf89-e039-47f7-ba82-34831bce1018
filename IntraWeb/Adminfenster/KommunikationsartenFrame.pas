﻿unit KommunikationsartenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWApplication,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCGJQEdit, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TKommunikationsarten = class(TCRUDGrid)
    quKommunikationsarten: TFDQuery;
    quNeu: TFDQuery;
    quLoeschen: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    jqeArt: TIWCGJQEdit;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
    procedure LoeschenBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure KommunikationsartenAbfragen;
  end;

var
  Kommunikationsarten: TKommunikationsarten;

implementation

uses Utility, dmmain;

{$R *.dfm}


constructor TKommunikationsarten.Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, alert);

  enableAbfragen(quKommunikationsarten, KommunikationsartenAbfragen);
  if bearbeitbar then
  begin
    enableNeu('Neue Kommunikationsart', nil, NeuBestaetigt);
    enableAendern('Kommunikationsart ändern', InitAendern, AendernBestaetigt);
    enableLoeschen('Wollen Sie die Kommunikationsart wirklich löschen?',
      LoeschenBestaetigt);
  end;
end;

procedure TKommunikationsarten.KommunikationsartenAbfragen;
begin
  RefreshQuery(quKommunikationsarten);
end;

procedure TKommunikationsarten.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('art').AsString := jqeArt.Text;
  quNeu.Execute;
end;

procedure TKommunikationsarten.InitAendern;
var
  art: String;
begin
  art := quKommunikationsarten.FieldByName('Art').AsString;
  jqeArt.Text := art;
end;

// Da die Tabelle nur ein Feld hat - Art ist das der Primary-Key der nicht
// änderbar ist. Deswegen wird die "geänderte" Art neu eingefügt und die
// ursprüngliche gelöscht.
procedure TKommunikationsarten.AendernBestaetigt;
begin
  NeuBestaetigt;
  LoeschenBestaetigt;
end;

procedure TKommunikationsarten.LoeschenBestaetigt;
var
  art: String;
begin
  art := quKommunikationsarten.FieldByName('Art').AsString;
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('art').AsString := art;
  quLoeschen.Execute;
end;

procedure TKommunikationsarten.ResetModal;
begin
  jqeArt.Text := '';
end;

end.
