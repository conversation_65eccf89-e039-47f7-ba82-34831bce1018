inherited FormAdministrator: TFormAdministrator
  Width = 1385
  Height = 600
  Title = 'Administratorfenster'
  BGColor = cl3DLight
  DesignLeft = 2
  DesignTop = 2
  object iwrModal: TIWCGJQRegion [0]
    Left = 50
    Top = 50
    Width = 800
    Height = 400
    Visible = False
    RenderInvisibleControls = True
    TabOrder = 31
    Version = '1.0'
    object iwrFunktionsrollenModal: TIWCGJQRegion
      Left = 1
      Top = 16
      Width = 1300
      Height = 502
      RenderInvisibleControls = True
      TabOrder = 32
      Version = '1.0'
      object iwrFunktionsrolleFunktion: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        TabOrder = 33
        Version = '1.0'
        Align = alLeft
        object jqgFunktionsrolleFunktion: TIWCGJQGrid
          Left = 0
          Top = 60
          Width = 649
          Height = 442
          TabOrder = 1
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Progmodul'
              Name = 'Progmodul'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Progmodul'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 388
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.CloseOnEscape = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpFunktionsrollenFunktion
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
        object IWRegion4: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 649
          Height = 60
          RenderInvisibleControls = True
          TabOrder = 34
          Version = '1.0'
          Align = alTop
          object IWLabel15: TIWCGJQLabel
            Left = 12
            Top = 16
            Width = 83
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel15'
            Caption = 'Bezeichnung:'
          end
          object EditFunktionsrollenFunktionsBezeichnung: TIWCGJQEdit
            Left = 98
            Top = 16
            Width = 200
            Height = 21
            TabOrder = 54
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            ScriptEvents = <>
            Text = ''
          end
          object jqbFunktionsrollenFunktion: TIWCGJQButton
            Left = 320
            Top = 16
            Width = 75
            Height = 21
            TabOrder = 5
            Version = '1.0'
            JQButtonOptions.Label_ = 'Suchen'
            JQButtonOptions.OnClick.OnEvent = jqbFunktionsrollenFunktionOnClick
          end
        end
      end
      object iwrFunktionsrolleRolle: TIWCGJQRegion
        Left = 651
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        TabOrder = 35
        Version = '1.0'
        Align = alRight
        object IWRegion5: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 649
          Height = 60
          RenderInvisibleControls = True
          TabOrder = 36
          Version = '1.0'
          Align = alTop
          object IWLabel16: TIWCGJQLabel
            Left = 12
            Top = 16
            Width = 83
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel15'
            Caption = 'Bezeichnung:'
          end
          object EditFunktionsrollenRollenBezeichnung: TIWCGJQEdit
            Left = 98
            Top = 16
            Width = 200
            Height = 21
            TabOrder = 56
            Version = '1.0'
            StyleRenderOptions.RenderBorder = False
            ScriptEvents = <>
            Text = ''
          end
          object jqbFunktionsrollenRolle: TIWCGJQButton
            Left = 320
            Top = 16
            Width = 75
            Height = 21
            TabOrder = 6
            Version = '1.0'
            JQButtonOptions.Label_ = 'Suchen'
            JQButtonOptions.OnClick.OnEvent = jqbFunktionsrollenRolleOnClick
          end
        end
        object jqgFunktionsrolleRolle: TIWCGJQGrid
          Left = 0
          Top = 60
          Width = 649
          Height = 442
          TabOrder = 3
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 388
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpFunktionsrollenRolle
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object iwrRechtsgrundlagenModal: TIWCGJQRegion
      Left = 192
      Top = 271
      Width = 360
      Height = 106
      RenderInvisibleControls = True
      TabOrder = 37
      Version = '1.0'
      object IWLabel19: TIWCGJQLabel
        Left = 24
        Top = 24
        Width = 112
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel19'
        Caption = 'Bezeichnung:'
      end
      object IWLabel20: TIWCGJQLabel
        Left = 24
        Top = 56
        Width = 112
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel19'
        Caption = 'Kurzbezeichnung:'
      end
      object EditRechtsgrundlageBezeichnung: TIWCGJQEdit
        Left = 145
        Top = 24
        Width = 200
        Height = 21
        TabOrder = 57
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangeData
      end
      object EditRechtsgrundlageKurzbezeichnung: TIWCGJQEdit
        Left = 145
        Top = 56
        Width = 200
        Height = 21
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangeData
      end
    end
    object iwrBkbRechtsgrundlagenModal: TIWCGJQRegion
      Left = 1
      Top = 16
      Width = 1300
      Height = 502
      RenderInvisibleControls = True
      TabOrder = 38
      Version = '1.0'
      object iwrBkbRechtsgrundlageBkb: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        TabOrder = 39
        Version = '1.0'
        Align = alLeft
        object jqgBkbRechtsgrundlageBkb: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 649
          Height = 502
          TabOrder = 7
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bkbtyp'
              Name = 'Bkbtyp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bkbtyp'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 448
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpBkbRechtsgrundlageBkb
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrBkbRechtsgrundlageRechtsgrundlage: TIWCGJQRegion
        Left = 651
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        TabOrder = 40
        Version = '1.0'
        Align = alRight
        object jqgBkbRechtsgrundlageRechtsgrundlage: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 649
          Height = 502
          TabOrder = 10
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bezeichnung'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Kurzbezeichnung'
              Name = 'Kurzbezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kurzbezeichnung'
            end>
          JQGridOptions.Height = 448
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpBkbRechtsgrundlageRechtsgrundlage
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
  end
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1385
    TabOrder = 12
    inherited ImageLogo: TIWImageFile
      Left = 1102
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 893
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 55
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 575
    Width = 1385
    TabOrder = 30
  end
  object Tabs: TIWCGJQTabs [3]
    Left = 0
    Top = 50
    Width = 1385
    Height = 525
    TabOrder = 14
    Version = '1.0'
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    ActiveTab = UserTab
    JQTabOptions.OnSelect.OnEvent = TabsOnSelect
    object FunktionenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Funktionen'
      TabIndex = 0
      Tabs = Tabs
      object jqgFunktionen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 0
        Height = 0
        TabOrder = 19
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PROGMODUL'
            Name = 'PROGMODUL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Progmodul'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 120
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'HINTTEXT'
            Name = 'HINTTEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Hinttext'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PROGCALLID'
            Name = 'PROGCALLID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Progcall ID'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SICHTBAR'
            Name = 'SICHTBAR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Sichtbar'
          end>
        JQGridOptions.Height = -27
        JQGridOptions.LoadOnce = True
        JQGridOptions.RowNum = 200
        JQGridOptions.Sortable = True
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = -2
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.PagerVisible = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpFunktionen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object UserTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'User'
      TabIndex = 1
      Tabs = Tabs
      object GridUser: TIWCGJQGrid
        Left = 0
        Top = 57
        Width = 1381
        Height = 163
        TabOrder = 29
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'USERGUID'
            Name = 'USERGUID'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Width = 50
            Caption = 'User GUID'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'USERNAME'
            Name = 'USERNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Username'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'EMAIL'
            Name = 'EMAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Email'
          end>
        JQGridOptions.Height = 109
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1379
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = UserGridOnSelectRow
        JQGridNav.Active = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Edit = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderUser
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion2: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1381
        Height = 57
        RenderInvisibleControls = True
        TabOrder = 41
        Version = '1.0'
        Align = alTop
        object IWLabel1: TIWCGJQLabel
          Left = 8
          Top = 2
          Width = 63
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel1'
          Caption = 'Username'
        end
        object iwusersuchen: TIWButton
          Left = 688
          Top = 20
          Width = 75
          Height = 25
          Caption = 'Suchen'
          Color = clBtnFace
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          FriendlyName = 'iwusersuchen'
          TabOrder = 9
          OnClick = userSuchenOnClick
        end
        object IWImage2: TIWImage
          Left = 769
          Top = 22
          Width = 20
          Height = 20
          RenderSize = False
          StyleRenderOptions.RenderSize = False
          BorderOptions.Width = 0
          UseSize = False
          OnClick = userSuchenResetOnClick
          Picture.Data = {
            07544269746D617066010000424D660100000000000076000000280000001400
            0000140000000100040000000000F00000000000000000000000100000001000
            0000000000000000BF0000BF000000BFBF00BF000000BF00BF00BFBF0000C0C0
            C000808080000000FF0000FF000000FFFF00FF000000FF00FF00FFFF0000FFFF
            FF00777888877777887777770000777088877777888777770000791108887791
            0888777700007911008887910088877700007911100889111008777700007991
            1108911111007777000077911110111111077777000077791111111107777777
            0000777991111111877777770000777799111110887777770000777779111110
            8887777700007777791111108887777700007777791111110888777700007777
            9111991100888777000077791110899110088877000077791118779911008887
            0000777111187779111088870000777111077777911108770000777999777777
            799177770000777777777777779977770000}
          FriendlyName = 'IWImage2'
          TransparentColor = clNone
          JpegOptions.CompressionQuality = 90
          JpegOptions.Performance = jpBestSpeed
          JpegOptions.ProgressiveEncoding = False
          JpegOptions.Smoothing = True
        end
        object EditUsername: TIWCGJQEdit
          Left = 4
          Top = 24
          Width = 121
          Height = 21
          TabOrder = 49
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
      end
      object IWRegion3: TIWCGJQRegion
        Left = 0
        Top = 220
        Width = 1381
        Height = 267
        RenderInvisibleControls = True
        TabOrder = 42
        Version = '1.0'
        Align = alBottom
        object IWDBEdit1: TIWDBEdit
          Left = 32
          Top = 62
          Width = 350
          Height = 21
          StyleRenderOptions.RenderBorder = False
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          FriendlyName = 'IWDBEdit1'
          MaxLength = 40
          SubmitOnAsyncEvent = True
          TabOrder = 10
          AutoEditable = False
          DataField = 'USERNAME'
          DataSource = ds_sysuser
          PasswordPrompt = False
        end
        object IWLabel3: TIWCGJQLabel
          Left = 32
          Top = 40
          Width = 63
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel3'
          Caption = 'Username'
        end
        object IWDBCheckBox1: TIWDBCheckBox
          Left = 32
          Top = 168
          Width = 121
          Height = 21
          Visible = False
          Caption = 'Aktiv'
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          SubmitOnAsyncEvent = True
          Style = stNormal
          TabOrder = 12
          AutoEditable = False
          DataField = 'AKTIV'
          DataSource = ds_sysuser
          FriendlyName = 'IWDBCheckBox1'
          ValueChecked = 'true'
          ValueUnchecked = 'false'
        end
        object IWLabel4: TIWCGJQLabel
          Left = 32
          Top = 104
          Width = 83
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel3'
          Caption = 'Emailadresse'
        end
        object IWDBEdit2: TIWDBEdit
          Left = 32
          Top = 126
          Width = 350
          Height = 21
          StyleRenderOptions.RenderBorder = False
          Editable = False
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          FriendlyName = 'IWDBEdit1'
          MaxLength = 100
          SubmitOnAsyncEvent = True
          TabOrder = 14
          AutoEditable = False
          DataField = 'EMAIL'
          DataSource = ds_sysuser
          PasswordPrompt = False
        end
        object RegionGruppen: TIWCGJQRegion
          Left = 428
          Top = 0
          Width = 800
          Height = 267
          TabOrder = 50
          Version = '1.0'
          object IWCGJQRegion1: TIWCGJQRegion
            Left = 0
            Top = 0
            Width = 800
            Height = 40
            TabOrder = 52
            Version = '1.0'
            Align = alTop
            object IWCGJQLabel1: TIWCGJQLabel
              Left = 9
              Top = 12
              Width = 58
              Height = 16
              Font.Color = clNone
              Font.Size = 10
              Font.Style = []
              HasTabOrder = False
              FriendlyName = 'IWCGJQLabel1'
              Caption = 'Gruppen:'
            end
          end
          object IWCGJQRegion2: TIWCGJQRegion
            Left = 0
            Top = 40
            Width = 800
            Height = 227
            TabOrder = 53
            Version = '1.0'
            Align = alClient
            object GridGruppenVonUser: TIWCGJQGrid
              Left = 0
              Top = 0
              Width = 800
              Height = 227
              TabOrder = 51
              Version = '1.0'
              Align = alClient
              JQGridOptions.ColModel = <
                item
                  Editable = True
                  EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                  EditOptions.CustomElements = <>
                  Idx = 'Bezeichnung'
                  Name = 'Bezeichnung'
                  SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                  Caption = 'Bezeichnung'
                end>
              JQGridOptions.Height = 200
              JQGridOptions.RowNum = 100
              JQGridOptions.SubGridModel = <>
              JQGridOptions.Width = 798
              JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
              JQGridOptions.PagerVisible = False
              JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
              JQGridCustomButtons = <>
              JQGridProvider = GridGruppenVonUserProvider
              JQGridToolbarSearch.DefaultSearch = gsoContains
              JQDragAndDropOptions.ConnectWith = <>
            end
          end
        end
      end
    end
    object GruppenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Gruppen'
      TabIndex = 2
      Tabs = Tabs
    end
    object UsergruppenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Usergruppen'
      TabIndex = 3
      Tabs = Tabs
    end
    object RollenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Rollen'
      TabIndex = 4
      Tabs = Tabs
    end
    object Funktionsrollentab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Funktionsrollen'
      TabIndex = 5
      Tabs = Tabs
      object iwrFunktionsrollenMid: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 0
        Height = 0
        RenderInvisibleControls = True
        TabOrder = 43
        Version = '1.0'
        Align = alClient
        object jqgFunktionsrollen: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 0
          Height = 0
          TabOrder = 20
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BEZEICHNUNG_1'
              Name = 'BEZEICHNUNG_1'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Funktionsbezeichnung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'HINTTEXT'
              Name = 'HINTTEXT'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Funktions Hinttext'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'PROGMODUL'
              Name = 'PROGMODUL'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Progmodul'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BEZEICHNUNG'
              Name = 'BEZEICHNUNG'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Rollenbezeichnung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BEGDAT'
              Name = 'BEGDAT'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Beginndatum'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'ENDDAT'
              Name = 'ENDDAT'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Enddatum'
            end>
          JQGridOptions.Height = -54
          JQGridOptions.RowNum = 40
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = -2
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Active = False
          JQGridNav.Add = False
          JQGridNav.CloseOnEscape = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpFunktionsrollen
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrFunktionsrollenTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 0
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 44
        Version = '1.0'
        Align = alTop
        object IWLabel17: TIWCGJQLabel
          Left = 8
          Top = 2
          Width = 143
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel17'
          Caption = 'Funktionsbezeichnung:'
        end
        object IWLabel18: TIWCGJQLabel
          Left = 256
          Top = 2
          Width = 122
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel18'
          Caption = 'Rollenbezeichnung:'
        end
        object EditFunktionsrollenFunktionsSuche: TIWCGJQEdit
          Left = 6
          Top = 19
          Width = 200
          Height = 21
          TabOrder = 2
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object EditFunktionsrollenRollenSuche: TIWCGJQEdit
          Left = 256
          Top = 19
          Width = 200
          Height = 21
          TabOrder = 4
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object jqbZuweisungLoeschen: TIWCGJQButton
          Left = 651
          Top = 19
          Width = 154
          Height = 21
          TabOrder = 21
          Version = '1.0'
          JQButtonOptions.Label_ = 'Zuweisung l'#246'schen'
          JQButtonOptions.OnClick.OnEvent = jqbZuweisungLoeschenOnClick
        end
        object jqbNeueZuweisung: TIWCGJQButton
          Left = 824
          Top = 19
          Width = 154
          Height = 21
          TabOrder = 22
          Version = '1.0'
          JQButtonOptions.Label_ = 'Neue Zuweisung'
          JQButtonOptions.OnClick.OnEvent = jqbNeueZuweisungOnClick
        end
        object jqbFunktionsrollenSuchen: TIWCGJQButton
          Left = 480
          Top = 19
          Width = 75
          Height = 21
          TabOrder = 23
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.OnEvent = jqbFunktionsrollenSuchenOnClick
        end
      end
    end
    object RechtsgrundlagenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Rechtsgrundlagen'
      TabIndex = 6
      Tabs = Tabs
      object iwrRechtsgrundlagenMid: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 0
        Height = 0
        RenderInvisibleControls = True
        TabOrder = 45
        Version = '1.0'
        Align = alClient
        object jqgRechtsgrundlagen: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 0
          Height = 0
          TabOrder = 24
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bezeichnung'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Kurzbezeichnung'
              Name = 'Kurzbezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Kurzbezeichnung'
            end>
          JQGridOptions.Height = -54
          JQGridOptions.RowNum = 40
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = -2
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpRechtsgrundlagen
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrRechtsgrundlagenTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 0
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 46
        Version = '1.0'
        Align = alTop
        object jqbRechtsgrundlagenAbfragen: TIWCGJQButton
          Left = 32
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 25
          Version = '1.0'
          JQButtonOptions.Label_ = 'Abfragen'
          JQButtonOptions.OnClick.OnEvent = jqbRechtsgrundlagenAbfragenOnClick
        end
        object jqbRechtsgrundlageNeu: TIWCGJQButton
          Left = 160
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 26
          Version = '1.0'
          JQButtonOptions.Label_ = 'Neu'
          JQButtonOptions.OnClick.OnEvent = jqbRechtsgrundlagenNeuOnClick
        end
        object jqbRechtsgrundlageAendern: TIWCGJQButton
          Left = 290
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 27
          Version = '1.0'
          JQButtonOptions.Label_ = #196'ndern'
          JQButtonOptions.OnClick.OnEvent = jqbRechtsgrundlagenAendernOnClick
        end
        object jqbRechtsgrundlageLoeschen: TIWCGJQButton
          Left = 430
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 28
          Version = '1.0'
          JQButtonOptions.Label_ = 'L'#246'schen'
          JQButtonOptions.OnClick.OnEvent = jqbRechtsgrundlagenLoeschenOnClick
        end
      end
    end
    object BkbtypenRechtsgrundlagenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Bkbtypen-Rechtsgrundlagen'
      TabIndex = 7
      Tabs = Tabs
      object iwrBkbRechtsgrundlageMid: TIWCGJQRegion
        Left = 0
        Top = 60
        Width = 0
        Height = 0
        RenderInvisibleControls = True
        TabOrder = 47
        Version = '1.0'
        Align = alClient
        object jqgBkbRechtsgrundlage: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 0
          Height = 0
          TabOrder = 15
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bkbtyp'
              Name = 'Bkbtyp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bkbtyp'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BkbBezeichnung'
              Name = 'BkbBezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bkbtyp Bezeichnung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Rechtsgrundlage Bezeichnung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Kurzbezeichnung'
              Name = 'Kurzbezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Rechtsgrundlage Kurzbezeichnung'
            end>
          JQGridOptions.Height = -54
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = -2
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpBkbRechtsgrundlage
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrBkbRechtsgrundlageTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 0
        Height = 60
        RenderInvisibleControls = True
        TabOrder = 48
        Version = '1.0'
        Align = alTop
        object jqbBkbRechtsgrundlageNeu: TIWCGJQButton
          Left = 150
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 16
          Version = '1.0'
          JQButtonOptions.Label_ = 'Neu'
          JQButtonOptions.OnClick.OnEvent = jqbBkbRechtsgrundlageNeuOnClick
        end
        object jqbBkbRechtsgrundlageLoeschen: TIWCGJQButton
          Left = 277
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 17
          Version = '1.0'
          JQButtonOptions.Label_ = 'L'#246'schen'
          JQButtonOptions.OnClick.OnEvent = jqbBkbRechtsgrundlageLoeschenOnClick
        end
        object jqbBkbRechtsgrundlageAbfragen: TIWCGJQButton
          Left = 23
          Top = 16
          Width = 100
          Height = 21
          TabOrder = 18
          Version = '1.0'
          JQButtonOptions.Label_ = 'Abfragen'
          JQButtonOptions.OnClick.OnEvent = jqbBkbRechtsgrundlageAbfragenOnClick
        end
      end
    end
    object BkbtypenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Bkbtypen'
      TabIndex = 8
      Tabs = Tabs
    end
    object RollenBkbtypenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Rollen-Bkbtypen'
      TabIndex = 9
      Tabs = Tabs
    end
    object PersonenpraefixTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Personenpr'#228'fix'
      TabIndex = 10
      Tabs = Tabs
    end
    object BundeslaenderTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Bundeslaender'
      TabIndex = 11
      Tabs = Tabs
    end
    object GemeindenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Gemeinden'
      TabIndex = 12
      Tabs = Tabs
    end
    object KontrolltypenTab: TIWCGJQTab
      Visible = False
      Css = ''
      Version = '1.0'
      Caption = 'Kontrolltypen'
      TabIndex = 13
      Tabs = Tabs
    end
  end
  object ds_funktionen: TDataSource
    DataSet = dm_main.qu_funktionen
    Left = 200
    Top = 5
  end
  object ds_sysuser: TDataSource
    DataSet = dm_main.qu_user_suche
    Left = 240
    Top = 5
  end
  object jqdpFunktionen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_funktionen
    DataSource = ds_funktionen
    Left = 800
  end
  object ProviderUser: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_user_suche
    DataSource = ds_sysuser
    KeyFields = 'ID'
    Left = 760
  end
  object ds_gruppen: TDataSource
    DataSet = dm_main.qu_gruppen
    Left = 288
  end
  object jqdpGruppen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_gruppen
    DataSource = ds_gruppen
    Left = 848
  end
  object jqdpUsergruppen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_usergruppen
    Left = 880
    Top = 8
  end
  object ds_usergruppen: TDataSource
    DataSet = dm_main.qu_usergruppen
    Left = 336
  end
  object jqdpUsergruppenUsername: TIWCGJQGridDataSetProvider
    DataSet = quUsergruppeUsername
    Left = 928
  end
  object quUsergruppeUsername: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   systemstammdaten.[user]'
      'WHERE  username LIKE '#39'%'#39' + :username + '#39'%'#39
      '        AND bldcode = :bldcode;')
    Left = 1296
    Top = 16
    ParamData = <
      item
        Name = 'USERNAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quUsergruppeAddUser: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.usergruppen (ID_User, ID_Gruppe, Be' +
        'gdat, Enddat)'
      'VALUES      (:userid, :gruppenid, :begdat, :enddat);')
    Left = 1241
    Top = 432
    ParamData = <
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
  end
  object quUsergruppeRemoveUser: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Usergruppen'
      'SET    Enddat = :enddat'
      'WHERE  ID_USER = :userid AND ID_GRUPPE = :gruppenid;')
    Left = 1240
    Top = 480
    ParamData = <
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quExistiertUserGruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT count(ID_USER) AS Anzahl'
      'FROM   Systemstammdaten.Usergruppen'
      'WHERE  ID_USER = :userid AND ID_GRUPPE = :gruppenid;')
    Left = 1241
    Top = 529
    ParamData = <
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quUpdateGruppenUser: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Usergruppen'
      'SET    Begdat = :begdat, Enddat = :enddat'
      'WHERE  ID_USER = :userid AND ID_GRUPPE = :gruppenid;')
    Left = 1241
    Top = 577
    ParamData = <
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object jqdpRollen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_rollen
    Left = 976
  end
  object iwmAdmin: TIWModalWindow
    CloseOnEscKey = False
    Left = 1136
  end
  object jqsaAdmin: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 1064
    Top = 16
  end
  object jqdpFunktionsrollen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_funktionsrollen
    KeyFields = 'id_rolle,id_funktion'
    Left = 1024
    Top = 8
  end
  object jqdpFunktionsrollenFunktion: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_funktionsrollen_funktion
    Left = 720
    Top = 8
  end
  object jqdpFunktionsrollenRolle: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_funktionsrollen_rollen
    Left = 688
  end
  object jqdpRechtsgrundlagen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_rechtsgrundlagen
    KeyFields = 'ID'
    Left = 624
    Top = 8
  end
  object jqdpBkbRechtsgrundlage: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_bkb_rechtsgrundlage
    Left = 568
  end
  object jqdpBkbRechtsgrundlageBkb: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_bkb_rechtsgrundlage_bkb
    Left = 520
    Top = 8
  end
  object jqdpBkbRechtsgrundlageRechtsgrundlage: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage
    Left = 480
  end
  object jqdpBkbtypen: TIWCGJQGridDataSetProvider
    Left = 440
  end
  object GridGruppenVonUserProvider: TIWCGJQGridDataSetProvider
    DataSet = QGruppenVonUser
    Left = 424
    Top = 50
  end
  object QGruppenVonUser: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT g.Bezeichnung'
      
        'FROM   Systemstammdaten.Usergruppen ug INNER JOIN Systemstammdat' +
        'en.Gruppen g ON ug.ID_Gruppe = g.ID'
      'WHERE  ug.ID_User = :userid;')
    Left = 1158
    Top = 546
    ParamData = <
      item
        Name = 'USERID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
