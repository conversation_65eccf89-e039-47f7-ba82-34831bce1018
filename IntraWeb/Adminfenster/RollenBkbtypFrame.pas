﻿unit RollenBkbtypFrame;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQControl, IWCGJQButton, IWCGJQGrid,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCompExtCtrls,
  IWCGJQComp, IWCGJQSweetAlert, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWApplication, IWCGJQRegion;

type
  TRollenBkbtyp = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqbAbfragen: TIWCGJQButton;
    jqgRollenBkb: TIWCGJQGrid;
    jqdpRollenBkb: TIWCGJQGridDataSetProvider;
    quRollenBkb: TFDQuery;
    iwrMid: TIWCGJQRegion;
    jqbNeu: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    quRollenBkbLoeschen: TFDQuery;
    quRollenBkbExistiert: TFDQuery;
    iwrRollenBkbModal: TIWCGJQRegion;
    iwrRollen: TIWCGJQRegion;
    iwrBkb: TIWCGJQRegion;
    jqgRollen: TIWCGJQGrid;
    jqgBkb: TIWCGJQGrid;
    jqdpRollen: TIWCGJQGridDataSetProvider;
    jqdpBkb: TIWCGJQGridDataSetProvider;
    quRollen: TFDQuery;
    quBkb: TFDQuery;
    quRollenBkbNeu: TFDQuery;
    quRollenBkbBkbtyp: TStringField;
    quRollenBkbId_Rollen: TIntegerField;
    quRollenBkbBezeichnung: TStringField;
    quRollenID: TFDAutoIncField;
    quRollenBezeichnung: TStringField;
    quBkbBkbtyp: TStringField;
    quBkbBezeichnung: TStringField;
    procedure jqbRollenBkbAfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FModal: TIWModalWindow;
    FAlert: TIWCGJQSweetAlert;
    property Alert: TIWCGJQSweetAlert read FAlert;
    procedure rollenBkbLoeschen(Sender: TObject; AParams: TStringList);
    procedure rollenBkbNeu(Sender: TObject; EventParams: TStringList);
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AModal: TIWModalWindow; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean); reintroduce;
    procedure rollenBkbAbfragen;
  end;

implementation

uses dmmain, Utility, JQ.Helpers.Grid;

{$R *.dfm}


constructor TRollenBkbtyp.Create(AOwner: TComponent; AModal: TIWModalWindow; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FModal := AModal;
  FAlert := AAlert;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbLoeschen.Visible := false;
  end;

  jqgRollenBkb.SetupDefaults(jqdpRollenBkb);
  jqgRollen.SetupDefaults(jqdpRollen);
  jqgBkb.SetupDefaults(jqdpBkb);
end;

procedure TRollenBkbtyp.jqbRollenBkbAfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  rollenBkbAbfragen;
end;

procedure TRollenBkbtyp.rollenBkbAbfragen;
begin
  RefreshQuery(quRollenBkb);
end;

procedure TRollenBkbtyp.jqbLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(quRollenBkb, jqgRollenBkb) then
    Exit;
  Alert.JQSweetAlertOptions.Title := 'Wollen Sie den Rollen-Bkbtyp löschen?';
  Alert.JQSweetAlertOptions.AlertType := jqsatWarning;
  Alert.JQSweetAlertOptions.ShowCancelButton := True;
  Alert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  Alert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  Alert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  Alert.JQSweetAlertOptions.OnBtnClick.OnEvent := rollenBkbLoeschen;
  Alert.Show;
end;

procedure TRollenBkbtyp.rollenBkbLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: boolean;
  id_rollen: Integer;
  bkbtyp: string;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  id_rollen := quRollenBkb.FieldByName('Id_Rollen').AsInteger;
  bkbtyp := quRollenBkb.FieldByName('bkbtyp').AsString;
  quRollenBkbLoeschen.Close;
  quRollenBkbLoeschen.Prepare;
  quRollenBkbLoeschen.ParamByName('Id_Rollen').AsInteger := id_rollen;
  quRollenBkbLoeschen.ParamByName('bkbtyp').AsString := bkbtyp;
  quRollenBkbLoeschen.Execute;
  rollenBkbAbfragen;
end;

procedure TRollenBkbtyp.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
begin
  RefreshQuery(quBkb);
  RefreshQuery(quRollen);

  with FModal do
  begin
    Title := 'Neuer Rollen-Bkbtyp';
    ContentElement := iwrRollenBkbModal;
    Buttons.CommaText := '&Erstellen,&Abbrechen';
    OnAsyncClick := rollenBkbNeu;
    Show;
  end;
end;

procedure TRollenBkbtyp.rollenBkbNeu(Sender: TObject; EventParams: TStringList);
var
  id: Integer;
  bkbtyp: String;
begin
  if FModal.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  // Exit wenn nicht in beiden Grids eine Reihe ausgewählt wurde
  if not(moveQueryToRow(quRollen, jqgRollen))
    or not(moveQueryToRow(quBkb, jqgBkb)) then
  begin
    Alert.Error('Es muss ein Bkbtyp und eine Rolle ausgewählt sein.');
    Abort;
  end;
  // Bekomme die IDs der ausgewählten Reihen
  id := quRollen.FieldByName('Id').AsInteger;
  bkbtyp := quBkb.FieldByName('Bkbtyp').AsString;

  quRollenBkbExistiert.Close;
  quRollenBkbExistiert.Prepare;
  quRollenBkbExistiert.ParamByName('id_rollen').AsInteger := id;
  quRollenBkbExistiert.ParamByName('bkbtyp').AsString := bkbtyp;
  quRollenBkbExistiert.Open;

  if quRollenBkbExistiert.FieldByName('Anzahl').AsInteger > 0 then
  begin
    // Es existiert schon Eintrag
    Alert.Error('Diese Kombination existiert schon.');
    Abort;
  end;

  quRollenBkbNeu.Close;
  quRollenBkbNeu.Prepare;
  quRollenBkbNeu.ParamByName('id_rollen').AsInteger := id;
  quRollenBkbNeu.ParamByName('bkbtyp').AsString := bkbtyp;
  quRollenBkbNeu.Execute;

  rollenBkbAbfragen;
end;

end.
