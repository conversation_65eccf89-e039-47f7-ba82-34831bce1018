inherited Kommunikationsarten: TKommunikationsarten
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 1
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 10
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ART'
            Name = 'ART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Art'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 5
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 7
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 11
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 6
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 360
      Top = 128
      Width = 216
      Height = 140
      TabOrder = 8
      JQDialogOptions.Height = 140
      JQDialogOptions.Width = 216
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 24
        Width = 24
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Art:'
      end
      object jqeArt: TIWCGJQEdit
        Left = 46
        Top = 24
        Width = 150
        Height = 21
        TabOrder = 9
        Version = '1.0'
        ZIndex = 5001
        MaxLength = 10
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quKommunikationsarten
    Left = 952
  end
  object quKommunikationsarten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Kommunikationsarten;')
    Left = 865
    Top = 25
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Systemstammdaten.Kommunikationsarten (Art)'
      'VALUES      (:art);')
    Left = 785
    Top = 25
    ParamData = <
      item
        Name = 'ART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Kommunikationsarten'
      'WHERE       Art = :art;')
    Left = 737
    Top = 25
    ParamData = <
      item
        Name = 'ART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
end
