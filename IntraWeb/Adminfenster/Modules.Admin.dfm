object DMAdmin: TDMAdmin
  OldCreateOrder = False
  Height = 527
  Width = 985
  object QGruppen: TFDQuery
    FilterOptions = [foCaseInsensitive]
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE  BLDCODE = :bldcode AND persoenlich = 0'
      'ORDER BY Muttergruppe ASC')
    Left = 32
    Top = 24
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QGruppenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGruppenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QGruppenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QGruppenMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QGruppenID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QGruppenID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QGruppenOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QGruppenPERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
    object QGruppenEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 250
    end
  end
  object QGruppensuche: TFDQuery
    FilterOptions = [foCaseInsensitive]
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE   Persoenlich = 0'
      '        AND BLDCODE = :bldcode'
      'order by Bezeichnung')
    Left = 48
    Top = 264
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object QGruppensucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGruppensucheBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QGruppensucheBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QGruppensucheMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QGruppensucheID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QGruppensucheID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QGruppensucheOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QGruppensuchePERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
  end
  object QUserSuche: TFDQuery
    FilterOptions = [foCaseInsensitive]
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.[User]'
      'WHERE   Bldcode = :bldcode'
      '        AND ID IN (SELECT ID_USER'
      '                   FROM   Systemstammdaten.Usergruppen'
      '                   WHERE  ID_Gruppe = :idgruppe)'
      '        OR ID = :IDUser'
      'order by UserName')
    Left = 48
    Top = 312
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 7
      end
      item
        Name = 'IDUSER'
        DataType = ftInteger
        ParamType = ptInput
        Value = 1
      end>
    object QUserSucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QUserSucheID_PERSON: TIntegerField
      FieldName = 'ID_PERSON'
      Origin = 'ID_PERSON'
    end
    object QUserSucheUSERNAME: TStringField
      FieldName = 'USERNAME'
      Origin = 'USERNAME'
      Size = 40
    end
    object QUserSuchePASSWORT: TStringField
      FieldName = 'PASSWORT'
      Origin = 'PASSWORT'
      Size = 40
    end
    object QUserSucheUSERGUID: TStringField
      FieldName = 'USERGUID'
      Origin = 'USERGUID'
      Size = 40
    end
    object QUserSucheUSERTYPE: TSmallintField
      FieldName = 'USERTYPE'
      Origin = 'USERTYPE'
    end
    object QUserSucheGESPERRT: TSmallintField
      FieldName = 'GESPERRT'
      Origin = 'GESPERRT'
    end
    object QUserSucheBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QUserSucheUSERPWC: TSmallintField
      FieldName = 'USERPWC'
      Origin = 'USERPWC'
    end
    object QUserSucheAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object QUserSucheTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object QUserSucheINS_DBUSER: TStringField
      FieldName = 'INS_DBUSER'
      Origin = 'INS_DBUSER'
      Size = 30
    end
    object QUserSucheLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object QUserSucheCHANGE_DBUSER: TStringField
      FieldName = 'CHANGE_DBUSER'
      Origin = 'CHANGE_DBUSER'
      Size = 30
    end
    object QUserSucheEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 100
    end
    object QUserSucheLAST_LOGIN: TSQLTimeStampField
      FieldName = 'LAST_LOGIN'
      Origin = 'LAST_LOGIN'
    end
  end
  object QUserDetails: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.[User]'
      'WHERE  ID = :id;')
    Left = 112
    Top = 24
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QUserDetailsID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QUserDetailsID_PERSON: TIntegerField
      FieldName = 'ID_PERSON'
      Origin = 'ID_PERSON'
    end
    object QUserDetailsUSERNAME: TStringField
      FieldName = 'USERNAME'
      Origin = 'USERNAME'
      Size = 40
    end
    object QUserDetailsPASSWORT: TStringField
      FieldName = 'PASSWORT'
      Origin = 'PASSWORT'
      Size = 40
    end
    object QUserDetailsUSERGUID: TStringField
      FieldName = 'USERGUID'
      Origin = 'USERGUID'
      Size = 40
    end
    object QUserDetailsUSERTYPE: TSmallintField
      FieldName = 'USERTYPE'
      Origin = 'USERTYPE'
    end
    object QUserDetailsGESPERRT: TSmallintField
      FieldName = 'GESPERRT'
      Origin = 'GESPERRT'
    end
    object QUserDetailsBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QUserDetailsUSERPWC: TSmallintField
      FieldName = 'USERPWC'
      Origin = 'USERPWC'
    end
    object QUserDetailsAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object QUserDetailsTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object QUserDetailsINS_DBUSER: TStringField
      FieldName = 'INS_DBUSER'
      Origin = 'INS_DBUSER'
      Size = 30
    end
    object QUserDetailsLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object QUserDetailsCHANGE_DBUSER: TStringField
      FieldName = 'CHANGE_DBUSER'
      Origin = 'CHANGE_DBUSER'
      Size = 30
    end
    object QUserDetailsEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 100
    end
    object QUserDetailsLAST_LOGIN: TSQLTimeStampField
      FieldName = 'LAST_LOGIN'
      Origin = 'LAST_LOGIN'
    end
  end
  object QNeueGruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.Gruppen (Bezeichnung, Bldcode, Mutt' +
        'ergruppe, ID_User_Hauptver, OKZ, Persoenlich, Email)'
      
        'VALUES      (:Bezeichnung, :Bldcode, :Muttergruppe, :ID_User_Hau' +
        'ptver, :OKZ, 0, :Email);')
    Left = 32
    Top = 80
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'MUTTERGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_USER_HAUPTVER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'EMAIL'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QHatGruppeKontrollen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT COUNT(T.ID) AS "Anzahl"'
      'FROM   Bewegungsdaten.Todo T'
      
        'JOIN   Bewegungsdaten.Kontrollbericht K on K.ID = T.ID_Kontrollb' +
        'ericht'
      'JOIN   BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS KS on KS.ID = K.ID'
      'WHERE  K.Datum IS NULL'
      '        AND T.ID_Gruppe = :idgruppe'
      '        AND KS.STATUS = '#39'U'#39';')
    Left = 232
    Top = 24
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QHatGruppeKontrollenAnzahl: TIntegerField
      FieldName = 'Anzahl'
      Origin = 'Anzahl'
      ReadOnly = True
    end
  end
  object QAnzahlUntergruppen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Count(ID) AS "Anzahl"'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE  Muttergruppe = :idgruppe;')
    Left = 232
    Top = 72
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QAnzahlUntergruppenAnzahl: TIntegerField
      FieldName = 'Anzahl'
      Origin = 'Anzahl'
      ReadOnly = True
    end
  end
  object QAnzahlMitglieder: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Count(ID_Gruppe) AS "Anzahl"'
      'FROM   Systemstammdaten.Usergruppen'
      'WHERE  ID_Gruppe = :idgruppe;')
    Left = 232
    Top = 120
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QAnzahlMitgliederAnzahl: TIntegerField
      FieldName = 'Anzahl'
      Origin = 'Anzahl'
      ReadOnly = True
    end
  end
  object QGruppeLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Usergruppen'
      'WHERE  ID_Gruppe = :idgruppe;'
      ''
      'DELETE FROM Systemstammdaten.Gruppen'
      'WHERE       ID = :idgruppe;')
    Left = 336
    Top = 24
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object QGruppenDetails: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Gruppen'
      'WHERE  ID = :gruppenid')
    Left = 32
    Top = 128
    ParamData = <
      item
        Name = 'GRUPPENID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QGruppenDetailsID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGruppenDetailsBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object QGruppenDetailsBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QGruppenDetailsMUTTERGRUPPE: TIntegerField
      FieldName = 'MUTTERGRUPPE'
      Origin = 'MUTTERGRUPPE'
    end
    object QGruppenDetailsID_USER_HAUPTVER: TIntegerField
      FieldName = 'ID_USER_HAUPTVER'
      Origin = 'ID_USER_HAUPTVER'
      Required = True
    end
    object QGruppenDetailsID_USER_STELLVER: TIntegerField
      FieldName = 'ID_USER_STELLVER'
      Origin = 'ID_USER_STELLVER'
    end
    object QGruppenDetailsOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Size = 100
    end
    object QGruppenDetailsPERSOENLICH: TBooleanField
      FieldName = 'PERSOENLICH'
      Origin = 'PERSOENLICH'
      Required = True
    end
    object QGruppenDetailsEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 250
    end
  end
  object QAddUserToGruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'BEGIN'
      '  IF NOT EXISTS (SELECT 1'
      '                 FROM   Systemstammdaten.Usergruppen'
      
        '                 WHERE  ID_User = :iduser AND ID_Gruppe = :idgru' +
        'ppe)'
      '  BEGIN'
      
        '    INSERT INTO Systemstammdaten.Usergruppen (ID_User, ID_Gruppe' +
        ', Begdat, Enddat)'
      '    VALUES      (:iduser, :idgruppe, :begdat, '#39'2999-01-01'#39');'
      '  END'
      'END')
    Left = 200
    Top = 264
    ParamData = <
      item
        Name = 'IDUSER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
  end
  object QUsers: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT P.Nachname, P.Vorname, U.ID'
      'FROM Systemstammdaten.[User] U'
      'JOIN Stammdaten.[Personen] P on P.ID = U.ID_PERSON'
      'WHERE  Bldcode = :bldcode'
      'ORDER BY P.Nachname, P.Vorname')
    Left = 464
    Top = 24
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object QUsersNachname: TStringField
      FieldName = 'Nachname'
      Origin = 'Nachname'
      Required = True
      Size = 60
    end
    object QUsersVorname: TStringField
      FieldName = 'Vorname'
      Origin = 'Vorname'
      Required = True
      Size = 60
    end
    object QUsersID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
  end
  object QUsergruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Usergruppen ug'
      
        '        INNER JOIN Systemstammdaten.[User] u ON UG.ID_User = u.I' +
        'D'
      'WHERE  ID_Gruppe = :idgruppe;')
    Left = 200
    Top = 312
    ParamData = <
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QUsergruppeID_USER: TIntegerField
      FieldName = 'ID_USER'
      Origin = 'ID_USER'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QUsergruppeID_GRUPPE: TIntegerField
      FieldName = 'ID_GRUPPE'
      Origin = 'ID_GRUPPE'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QUsergruppeBEGDAT: TDateField
      FieldName = 'BEGDAT'
      Origin = 'BEGDAT'
      Required = True
    end
    object QUsergruppeENDDAT: TDateField
      FieldName = 'ENDDAT'
      Origin = 'ENDDAT'
      Required = True
    end
    object QUsergruppeID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
    end
    object QUsergruppeID_PERSON: TIntegerField
      FieldName = 'ID_PERSON'
      Origin = 'ID_PERSON'
    end
    object QUsergruppeUSERNAME: TStringField
      FieldName = 'USERNAME'
      Origin = 'USERNAME'
      Size = 40
    end
    object QUsergruppePASSWORT: TStringField
      FieldName = 'PASSWORT'
      Origin = 'PASSWORT'
      Size = 40
    end
    object QUsergruppeUSERGUID: TStringField
      FieldName = 'USERGUID'
      Origin = 'USERGUID'
      Size = 40
    end
    object QUsergruppeUSERTYPE: TSmallintField
      FieldName = 'USERTYPE'
      Origin = 'USERTYPE'
    end
    object QUsergruppeGESPERRT: TSmallintField
      FieldName = 'GESPERRT'
      Origin = 'GESPERRT'
    end
    object QUsergruppeBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QUsergruppeUSERPWC: TSmallintField
      FieldName = 'USERPWC'
      Origin = 'USERPWC'
    end
    object QUsergruppeAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object QUsergruppeTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
    end
    object QUsergruppeINS_DBUSER: TStringField
      FieldName = 'INS_DBUSER'
      Origin = 'INS_DBUSER'
      Size = 30
    end
    object QUsergruppeLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object QUsergruppeCHANGE_DBUSER: TStringField
      FieldName = 'CHANGE_DBUSER'
      Origin = 'CHANGE_DBUSER'
      Size = 30
    end
    object QUsergruppeEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 100
    end
    object QUsergruppeLAST_LOGIN: TSQLTimeStampField
      FieldName = 'LAST_LOGIN'
      Origin = 'LAST_LOGIN'
    end
  end
  object QRemoveFromGruppe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Usergruppen'
      'WHERE       ID_User = :iduser AND ID_Gruppe = :idgruppe;')
    Left = 312
    Top = 264
    ParamData = <
      item
        Name = 'IDUSER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'IDGRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
