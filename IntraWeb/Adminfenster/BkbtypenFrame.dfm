inherited Bkbtypen: TBkbtypen
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 1
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 13
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKBTYP'
            Name = 'BKBTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'BKBTYP'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BEZEICHNUNG'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'SICHTBAR'
            Name = 'SICHTBAR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'SICHTBAR'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'AKTIV'
            Name = 'AKTIV'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'AKTIV'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEGDAT'
            Name = 'BEGDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BEGDAT'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ENDDAT'
            Name = 'ENDDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'ENDDAT'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SECCLASS_MIN'
            Name = 'SECCLASS_MIN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'SECCLASS_MIN'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 4
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 5
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 17
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 8
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 9
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Top = 149
      Width = 353
      TabOrder = 11
      JQDialogOptions.Width = 353
      object iwcBkbAktiv: TIWCheckBox
        Left = 120
        Top = 101
        Width = 121
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 7
        Checked = False
        FriendlyName = 'IWCheckBox1'
      end
      object iwcBkbSichtbar: TIWCheckBox
        Left = 120
        Top = 74
        Width = 121
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 8
        Checked = False
        FriendlyName = 'iwcBkbSichtbar'
      end
      object IWLabel21: TIWCGJQLabel
        Left = 20
        Top = 20
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Bkbtyp:'
      end
      object IWLabel22: TIWCGJQLabel
        Left = 20
        Top = 47
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Bezeichnung:'
      end
      object IWLabel23: TIWCGJQLabel
        Left = 20
        Top = 74
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Sichtbar:'
      end
      object IWLabel24: TIWCGJQLabel
        Left = 20
        Top = 101
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Aktiv:'
      end
      object IWLabel25: TIWCGJQLabel
        Left = 20
        Top = 128
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Begdat:'
      end
      object IWLabel26: TIWCGJQLabel
        Left = 20
        Top = 155
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Enddat:'
      end
      object IWLabel27: TIWCGJQLabel
        Left = 20
        Top = 182
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel21'
        Caption = 'Secclass Min:'
      end
      object jqdBkbBegdat: TIWCGJQDatePicker
        Left = 120
        Top = 128
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd/mm/yyyy'
      end
      object jqdBkbEnddat: TIWCGJQDatePicker
        Left = 120
        Top = 155
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd/mm/yyyy'
      end
      object jqeBkbBezeichnung: TIWCGJQEdit
        Left = 120
        Top = 47
        Width = 200
        Height = 21
        TabOrder = 14
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeBkbBkbtyp: TIWCGJQEdit
        Left = 120
        Top = 20
        Width = 200
        Height = 21
        TabOrder = 15
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeBkbSecclass: TIWCGJQEdit
        Left = 120
        Top = 182
        Width = 200
        Height = 21
        TabOrder = 16
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quBkbtypen
    KeyFields = 'BKBTYP'
    Left = 952
    Top = 8
  end
  object quBkbtypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bkbtypen;')
    Left = 881
    Top = 58
    object quBkbtypenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object quBkbtypenMODUL: TStringField
      FieldName = 'MODUL'
      Origin = 'MODUL'
      Required = True
      Size = 10
    end
    object quBkbtypenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 50
    end
    object quBkbtypenSICHTBAR: TBooleanField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
      Required = True
    end
    object quBkbtypenAKTIV: TBooleanField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
      Required = True
    end
    object quBkbtypenBEGDAT: TDateField
      FieldName = 'BEGDAT'
      Origin = 'BEGDAT'
      Required = True
    end
    object quBkbtypenENDDAT: TDateField
      FieldName = 'ENDDAT'
      Origin = 'ENDDAT'
      Required = True
    end
    object quBkbtypenLASTCHANGE: TDateField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
    end
    object quBkbtypenTSTAMP_INSERT: TSQLTimeStampField
      FieldName = 'TSTAMP_INSERT'
      Origin = 'TSTAMP_INSERT'
      Required = True
    end
    object quBkbtypenSECCLASS_MIN: TSmallintField
      FieldName = 'SECCLASS_MIN'
      Origin = 'SECCLASS_MIN'
    end
    object quBkbtypenPROBE: TBooleanField
      FieldName = 'PROBE'
      Origin = 'PROBE'
      Required = True
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Bkbtypen (bkbtyp, bezeichnung, sichtbar, ' +
        'aktiv, begdat, enddat, secclass_min, tstamp_insert)'
      
        'VALUES      (:bkbtyp, :bez, :sichtbar, :aktiv, :begdat, :enddat,' +
        ' :secclass_min, :tstamp_insert);')
    Left = 793
    Top = 58
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AKTIV'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SECCLASS_MIN'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TSTAMP_INSERT'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Bkbtypen'
      'WHERE       Bkbtyp = :bkbtyp;')
    Left = 801
    Top = 10
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = '-1'
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Bkbtypen'
      
        'SET    bezeichnung = :bez, Sichtbar = :sichtbar, Aktiv = :aktiv,' +
        ' Begdat = :begdat, Enddat = :enddat, Secclass_min = :secclass_mi' +
        'n'
      'WHERE  Bkbtyp = :bkbtyp;')
    Left = 881
    Top = 10
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AKTIV'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SECCLASS_MIN'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = '-1'
      end>
  end
end
