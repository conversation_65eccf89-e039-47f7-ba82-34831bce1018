﻿unit KommunikationswegeFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQComboBox, IWCGJQEdit, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWApplication, IWCGJQRegion, IWCGJQLabel;

type
  TKommunikationswege = class(TCRUDGrid)
    quKommunikationswege: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    quLoeschen: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    jqeValue: TIWCGJQEdit;
    jqcbArt: TIWCGJQComboBoxEx;
    quKommunikationsarten: TFDQuery;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
    procedure LoeschenBestaetigt;
    function KorrektAusgefuellt: boolean;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure KommunikationswegeAbfragen;
  end;

var
  Kommunikationswege: TKommunikationswege;

implementation

uses Utility, dmmain;

{$R *.dfm}


constructor TKommunikationswege.Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
var
  item: TIWCGJQComboBoxExItem;
begin
  inherited Create(AOwner, alert);

  enableAbfragen(quKommunikationswege, KommunikationswegeAbfragen);
  if bearbeitbar then
  begin
    enableNeu('Neuer Kommunikationsweg', nil, NeuBestaetigt);
    enableAendern('Kommunikationsweg ändern', InitAendern, AendernBestaetigt);
    enableLoeschen('Wollen Sie den Kommunikationsweg wirklich löschen?',
      LoeschenBestaetigt);

    // Die Combobox mit den vorhandenen Arten füllen.
    RefreshQuery(quKommunikationsarten);
    quKommunikationsarten.First;
    while not quKommunikationsarten.Eof do
    begin
      item := jqcbArt.Items.Add;
      item.Caption := quKommunikationsarten.FieldByName('Art').AsString;
      item.Value := quKommunikationsarten.FieldByName('Art').AsString;
      quKommunikationsarten.Next;
    end;
  end;
end;

procedure TKommunikationswege.KommunikationswegeAbfragen;
begin
  RefreshQuery(quKommunikationswege);
end;

procedure TKommunikationswege.NeuBestaetigt;
begin
  if not KorrektAusgefuellt then
    Exit;
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('art').AsString := jqcbArt.SelectedItem.Value;
  quNeu.ParamByName('bez').AsString := jqeBezeichnung.Text;
  quNeu.ParamByName('value').AsString := jqeValue.Text;
  quNeu.Execute;
  quNeu.Close;
end;

procedure TKommunikationswege.InitAendern;
begin
  jqcbArt.SelectByValue(quKommunikationswege.FieldByName('art').AsString);
  jqeBezeichnung.Text := quKommunikationswege.FieldByName('bezeichnung').AsString;
  jqeValue.Text := quKommunikationswege.FieldByName('value').AsString;
end;

procedure TKommunikationswege.AendernBestaetigt;
begin
  if not KorrektAusgefuellt then
    Exit;
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('id').AsInteger :=
    quKommunikationswege.FieldByName('id').AsInteger;
  quAendern.ParamByName('art').AsString := jqcbArt.SelectedItem.Value;
  quAendern.ParamByName('bez').AsString := jqeBezeichnung.Text;
  quAendern.ParamByName('value').AsString := jqeValue.Text;
  quAendern.Execute;
  quAendern.Close;
end;

procedure TKommunikationswege.LoeschenBestaetigt;
begin
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('id').AsInteger :=
    quKommunikationswege.FieldByName('id').AsInteger;
  quLoeschen.Execute;
  quLoeschen.Close;
end;

function TKommunikationswege.KorrektAusgefuellt: boolean;
begin
  Result := (jqeBezeichnung.Text <> '') and (jqeValue.Text <> '');
end;

procedure TKommunikationswege.ResetModal;
begin
  jqeBezeichnung.Text := '';
  jqeValue.Text := '';
end;

end.
