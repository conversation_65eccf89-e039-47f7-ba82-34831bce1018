﻿unit BkbtypenFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQGrid, IWCGJQButton, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHT<PERSON>40Component, IWCGJQComp, IWCGJQSweetAlert,
  FireDAC.Comp.Client, IWCGJQDialog, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, CRUDGridFrame, IWCGJQEdit, IWCGJQDatePicker,
  IWCompLabel, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl,
  IWCompCheckbox, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet, IWCGJQRegion, IWCGJQLabel;

type
  TBkbtypen = class(TCRUDGrid)
    iwcBkbAktiv: TIWCheckBox;
    iwcBkbSichtbar: TIWCheckBox;
    IWLabel21: TIWCGJQLabel;
    IWLabel22: TIWCGJQLabel;
    IWLabel23: TIWCGJQLabel;
    IWLabel24: TIWCGJQLabel;
    IWLabel25: TIWCGJQLabel;
    IWLabel26: TIWCGJQLabel;
    IWLabel27: TIWCGJQLabel;
    jqdBkbBegdat: TIWCGJQDatePicker;
    jqdBkbEnddat: TIWCGJQDatePicker;
    jqeBkbBezeichnung: TIWCGJQEdit;
    jqeBkbBkbtyp: TIWCGJQEdit;
    jqeBkbSecclass: TIWCGJQEdit;
    quBkbtypen: TFDQuery;
    quNeu: TFDQuery;
    quLoeschen: TFDQuery;
    quAendern: TFDQuery;
    quBkbtypenBKBTYP: TStringField;
    quBkbtypenMODUL: TStringField;
    quBkbtypenBEZEICHNUNG: TStringField;
    quBkbtypenSICHTBAR: TBooleanField;
    quBkbtypenAKTIV: TBooleanField;
    quBkbtypenBEGDAT: TDateField;
    quBkbtypenENDDAT: TDateField;
    quBkbtypenLASTCHANGE: TDateField;
    quBkbtypenTSTAMP_INSERT: TSQLTimeStampField;
    quBkbtypenSECCLASS_MIN: TSmallintField;
    quBkbtypenPROBE: TBooleanField;
  private
    { Private declarations }
    procedure neuBestaetigt;
    procedure initAendern;
    procedure aendernBestaetigt;
    procedure loeschenBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent;  alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure bkbtypenAbfragen;
  end;

var
  Bkbtypen: TBkbtypen;

implementation

uses Utility, dmmain;

{$R *.dfm}

constructor TBkbtypen.Create(AOwner: TComponent;  alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, alert);

  EnableAbfragen(quBkbtypen, bkbtypenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neuer Bkbtyp', nil, neuBestaetigt);
    EnableAendern('Bkbtyp ändern', initAendern, aendernBestaetigt);
    EnableLoeschen('Wollen Sie den Bkbtypen wirklich löschen?', loeschenBestaetigt);
  end;
end;

procedure TBkbtypen.bkbtypenAbfragen;
begin
  RefreshQuery(quBkbtypen);
end;

/// Füllt die Query mit den Daten aus den Edits und speichert einen neuen
/// Bkbtypen in die Datenbank ab.
procedure TBkbtypen.neuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bkbtyp').AsString := jqeBkbBkbtyp.Text;
  quNeu.ParamByName('bez').AsString := jqeBkbBezeichnung.Text;
  quNeu.ParamByName('sichtbar').AsBoolean := iwcBkbSichtbar.Checked;
  quNeu.ParamByName('aktiv').AsBoolean := iwcBkbAktiv.Checked;
  quNeu.ParamByName('begdat').AsDate := jqdBkbBegdat.Date;
  quNeu.ParamByName('enddat').AsDate := jqdBkbEnddat.Date;
  quNeu.ParamByName('secclass_min').AsSmallInt := StrToInt(jqeBkbSecclass.Text);
  quNeu.ParamByName('tstamp_insert').AsDateTime := Now;
  quNeu.Execute;
end;

/// Füllt den Dialog mit den Werten der momentan ausgewählten Reihe.
procedure TBkbtypen.initAendern;
begin
  jqeBkbBkbtyp.Enabled := false;
  jqeBkbBkbtyp.Text := quBkbtypen.FieldByName('bkbtyp').AsString;
  jqeBkbBezeichnung.Text := quBkbtypen.FieldByName('Bezeichnung').AsString;
  jqeBkbSecclass.Text := IntToStr(quBkbtypen.FieldByName('secclass_min').AsInteger);
  iwcBkbSichtbar.Checked := quBkbtypen.FieldByName('sichtbar').AsBoolean;
  iwcBkbAktiv.Checked := quBkbtypen.FieldByName('aktiv').AsBoolean;
  jqdBkbBegdat.Date := quBkbtypen.FieldByName('begdat').AsDateTime;
  jqdBkbEnddat.Date := quBkbtypen.FieldByName('enddat').AsDateTime;
end;

/// Ändert die ausgewählte Reihe in der DB mit den neuen.
procedure TBkbtypen.aendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('bkbtyp').AsString := jqeBkbBkbtyp.Text;
  quAendern.ParamByName('bez').AsString := jqeBkbBezeichnung.Text;
  quAendern.ParamByName('sichtbar').AsBoolean := iwcBkbSichtbar.Checked;
  quAendern.ParamByName('aktiv').AsBoolean := iwcBkbAktiv.Checked;
  quAendern.ParamByName('begdat').AsDate := jqdBkbBegdat.Date;
  quAendern.ParamByName('enddat').AsDate := jqdBkbEnddat.Date;
  quAendern.ParamByName('secclass_min').AsSmallInt := StrToInt(jqeBkbSecclass.Text);
  quAendern.Execute;
end;

/// Löscht die ausgewählte Reihe aus der DB.
procedure TBkbtypen.loeschenBestaetigt;
var
  bkbtyp: String;
begin
  bkbtyp := quBkbtypen.FieldByName('Bkbtyp').AsString;
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('Bkbtyp').AsString := bkbtyp;
  quLoeschen.Execute;
end;

/// Setzt alle Felder des Modals zurück und enabled die Disabled Felder.
procedure TBkbtypen.ResetModal;
var
  begdat: TDateTime;
  enddat: TDateTime;
begin
  // Setzte die Daten
  begdat := Now();
  enddat := Now();
  enddat := IncMonth(enddat, 12 * 500);
  jqdBkbBegdat.Date := begdat;
  jqdBkbEnddat.Date := enddat;
  jqeBkbBkbtyp.Text := '';
  jqeBkbBezeichnung.Text := '';
  jqeBkbSecclass.Text := '1';
  jqeBkbBkbtyp.Enabled := true;
end;

end.
