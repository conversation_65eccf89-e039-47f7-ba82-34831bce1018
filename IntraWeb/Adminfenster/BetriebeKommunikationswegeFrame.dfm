inherited BetriebeKommunikationswege: TBetriebeKommunikationswege
  Width = 1030
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1030
    TabOrder = 1
    inherited jqdDialog: TIWCGJQDialog [0]
      Left = 6
      Top = 3
      Width = 1000
      Height = 518
      TabOrder = 11
      JQDialogOptions.Height = 518
      JQDialogOptions.Width = 1000
      object iwrLeft: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 500
        Height = 518
        RenderInvisibleControls = True
        TabOrder = 16
        Version = '1.0'
        Align = alLeft
        object iwrBetriebSuchen: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 500
          Height = 60
          RenderInvisibleControls = True
          TabOrder = 17
          Version = '1.0'
          Align = alTop
          object IWLabel1: TIWCGJQLabel
            Left = 0
            Top = 3
            Width = 41
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel1'
            Caption = 'Name:'
          end
          object IWLabel2: TIWCGJQLabel
            Left = 136
            Top = 3
            Width = 42
            Height = 16
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWLabel2'
            Caption = 'Regnr:'
          end
          object jqeBetriebSuchen: TIWCGJQButton
            Left = 275
            Top = 25
            Width = 75
            Height = 21
            TabOrder = 10
            Version = '1.0'
            JQButtonOptions.Label_ = 'Suchen'
            JQButtonOptions.OnClick.OnEvent = jqbBetriebSuchenOnClick
          end
          object jqeName: TIWCGJQEdit
            Left = 0
            Top = 25
            Width = 120
            Height = 21
            TabOrder = 12
            Version = '1.0'
            ZIndex = 5001
            ScriptEvents = <>
            Text = ''
          end
          object jqeRegnr: TIWCGJQEdit
            Left = 136
            Top = 25
            Width = 120
            Height = 21
            TabOrder = 14
            Version = '1.0'
            ZIndex = 5001
            ScriptEvents = <>
            Text = ''
          end
        end
        object jqgBetriebe: TIWCGJQGrid
          Left = 0
          Top = 60
          Width = 500
          Height = 458
          TabOrder = 7
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'REGNR'
              Name = 'REGNR'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Regnr'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'NAME'
              Name = 'NAME'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Name'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Ort'
              Name = 'Ort'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Ort'
            end>
          JQGridOptions.Height = 404
          JQGridOptions.RowNum = 40
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 498
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpBetriebe
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrRight: TIWCGJQRegion
        Left = 500
        Top = 0
        Width = 500
        Height = 518
        RenderInvisibleControls = True
        TabOrder = 18
        Version = '1.0'
        Align = alRight
        object jqgKommunikationswege: TIWCGJQGrid
          Left = 0
          Top = 60
          Width = 500
          Height = 458
          TabOrder = 8
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'BEZEICHNUNG'
              Name = 'BEZEICHNUNG'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bezeichnung'
            end
            item
              Editable = True
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'VALUE'
              Name = 'VALUE'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Value'
            end>
          JQGridOptions.Height = 404
          JQGridOptions.RowNum = 40
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 498
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpKommunikationswege
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
        object iwrKommunikationswege: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 500
          Height = 60
          RenderInvisibleControls = True
          TabOrder = 19
          Version = '1.0'
          Align = alTop
        end
      end
    end
    inherited iwrMid: TIWCGJQRegion [1]
      Width = 1030
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        Width = 1030
        TabOrder = 13
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Regnr'
            Name = 'Regnr'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Regnr'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Name'
            Name = 'Name'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Name'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'bezeichnung'
            Name = 'bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'value'
            Name = 'value'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Value'
          end>
        JQGridOptions.Width = 1028
      end
    end
    inherited iwrTop: TIWCGJQRegion [2]
      Width = 1030
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 5
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 6
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 15
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          Left = 257
          TabOrder = 9
        end
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quBeKw
    Left = 976
  end
  object quBeKw: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT b.Regnr, b.Name, k.art, k.bezeichnung, k.value, bk.Id_Bet' +
        'rieb, bk.Id_Kommunikationsweg'
      'FROM   Bewegungsdaten.Betriebe_Kommunikationswege bk'
      '        INNER JOIN Stammdaten.Betriebe b ON bk.Id_Betrieb = b.Id'
      
        '        INNER JOIN Stammdaten.Kommunikationswege k ON bk.Id_Komm' +
        'unikationsweg = k.Id'
      'WHERE  b.Bldcode = :bldcode;')
    Left = 920
    Top = 24
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Bewegungsdaten.Betriebe_Kommunikationswege (Id_Betri' +
        'eb, Id_Kommunikationsweg)'
      'VALUES      (:id_betrieb, :id_kommunikationsweg);')
    Left = 872
    Top = 24
    ParamData = <
      item
        Name = 'ID_BETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_KOMMUNIKATIONSWEG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Bewegungsdaten.Betriebe_Kommunikationswege'
      'WHERE       Id_Betrieb = :id_betrieb '
      '             AND Id_Kommunikationsweg = :id_kommunikationsweg;')
    Left = 808
    Top = 24
    ParamData = <
      item
        Name = 'ID_BETRIEB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_KOMMUNIKATIONSWEG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    Left = 736
    Top = 24
  end
  object jqdpBetriebe: TIWCGJQGridDataSetProvider
    DataSet = quBetriebe
    KeyFields = 'ID'
    Left = 978
    Top = 75
  end
  object jqdpKommunikationswege: TIWCGJQGridDataSetProvider
    DataSet = quKommunikationswege
    KeyFields = 'ID'
    Left = 978
    Top = 123
  end
  object quBetriebe: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT b.Id, b.Regnr, b.Name, a.Ort'
      'FROM   Stammdaten.Betriebe b'
      '        INNER JOIN Stammdaten.Adressen a ON b.Id_Adresse = a.Id'
      'WHERE  b.Bldcode = :bldcode'
      '        AND b.Name LIKE '#39'%'#39' + :name + '#39'%'#39
      '        AND b.Regnr LIKE '#39'%'#39' + :regnr + '#39'%'#39';')
    Left = 914
    Top = 79
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end>
    object quBetriebeId: TFDAutoIncField
      FieldName = 'Id'
      Origin = 'Id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quBetriebeRegnr: TStringField
      FieldName = 'Regnr'
      Origin = 'Regnr'
      FixedChar = True
      Size = 7
    end
    object quBetriebeName: TStringField
      FieldName = 'Name'
      Origin = 'Name'
      Required = True
      Size = 255
    end
    object quBetriebeOrt: TWideStringField
      FieldName = 'Ort'
      Origin = 'Ort'
      Required = True
      Size = 150
    end
  end
  object quKommunikationswege: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Kommunikationswege;')
    Left = 914
    Top = 127
  end
end
