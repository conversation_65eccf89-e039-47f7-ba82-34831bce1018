﻿unit GemeindenFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQDatePicker, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompEdit, IWCGJQControl, IWCGJQDialog,
  IWBaseComponent, IWBase<PERSON>MLComponent, IWBaseHTML40Component, IWCGJQComp,
  IWCGJQSweetAlert, IWCGJQDateTimePicker, IWCGJQTimePicker, IWCompTimeEdit,
  IWCGJQButton, IWCGJQEdit, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQGrid, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TGemeinden = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqbNeu: TIWCGJQButton;
    jqbAbfragen: TIWCGJQButton;
    jqbAendern: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    iwrMid: TIWCGJQRegion;
    jqgGemeinden: TIWCGJQGrid;
    quGemeinden: TFDQuery;
    jqdpGemeinden: TIWCGJQGridDataSetProvider;
    IWLabel17: TIWCGJQLabel;
    EditGemeindenname: TIWCGJQEdit;
    IWLabel18: TIWCGJQLabel;
    EditAmtsplz: TIWCGJQEdit;
    IWLabel1: TIWCGJQLabel;
    EditGemeindencode: TIWCGJQEdit;
    IWLabel2: TIWCGJQLabel;
    EditGemeindenkennziffer: TIWCGJQEdit;
    jqdGemeinden: TIWCGJQDialog;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    jqeKennziffer: TIWCGJQEdit;
    jqeName: TIWCGJQEdit;
    jqeCode: TIWCGJQEdit;
    jqePlz: TIWCGJQEdit;
    jqePolBezKenn: TIWCGJQEdit;
    jqePolBez: TIWCGJQEdit;
    jqePolBezCode: TIWCGJQEdit;
    jqeLandIso: TIWCGJQEdit;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    quLoeschen: TFDQuery;
    quGemeindenID: TFDAutoIncField;
    quGemeindenGEMEINDEKENNZIFFER: TIntegerField;
    quGemeindenGEMEINDENAME: TStringField;
    quGemeindenGEMEINDECODE: TIntegerField;
    quGemeindenAMTSPLZ: TStringField;
    quGemeindenPOL_BEZ_KENNZIF: TIntegerField;
    quGemeindenPOL_BEZIRK: TStringField;
    quGemeindenPOL_BEZ_CODE: TIntegerField;
    quGemeindenLAND_ISO2: TStringField;
    quGemeindenBUNDESLANDCODE: TSmallintField;
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlert: TIWCGJQSweetAlert;

    property Alert: TIWCGJQSweetAlert read FAlert;
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure jqdNeuEvent(Sender: TObject; AURLParams: TStringList);
    procedure gemeindeLoeschen(Sender: TObject; AParams: TStringList);
    procedure jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
    procedure clearModal;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert;
      AWebApplication: TIWApplication; bearbeitbar: boolean); reintroduce;
    procedure gemeindenAbfragen(kennziffer: Integer = -1;
      name: String = ''; code: Integer = -1; plz: String = '');
  end;

implementation

uses dmmain, Utility, JQ.Helpers.Grid;

{$R *.dfm}


constructor TGemeinden.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert;
  AWebApplication: TIWApplication; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbAendern.Visible := false;
    jqbLoeschen.Visible := false;
  end;

  jqgGemeinden.SetupDefaults(jqdpGemeinden);
end;

procedure TGemeinden.gemeindenAbfragen(kennziffer: Integer = -1;
  name: String = ''; code: Integer = -1; plz: String = '');
begin
  quGemeinden.Close;
  quGemeinden.Prepare;
  quGemeinden.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quGemeinden.ParamByName('name').AsString := name;
  quGemeinden.ParamByName('plz').AsString := plz;
  quGemeinden.ParamByName('code').AsInteger := code;
  quGemeinden.ParamByName('kennziffer').AsInteger := kennziffer;
  quGemeinden.Active := true;
end;

procedure TGemeinden.jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
var
  name, plz: String;
  kennziffer, code: Integer;
begin
  name := EditGemeindenname.Text;
  plz := EditAmtsplz.Text;
  if EditGemeindenkennziffer.Text = '' then
    kennziffer := -1
  else
    kennziffer := StrToInt(EditGemeindenkennziffer.Text);
  if EditGemeindencode.Text = '' then
    code := -1
  else
    code := StrToInt(EditGemeindencode.Text);
  gemeindenAbfragen(kennziffer, name, code, plz);
end;

procedure TGemeinden.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  clearModal;
  jqdGemeinden.JQDialogOptions.Title := 'Neue Gemeinde';
  jqb := jqdGemeinden.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.OnClick.OnEvent := jqdNeuEvent;
  jqb := jqdGemeinden.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdGemeinden.Visible := true;
end;

procedure TGemeinden.jqdNeuEvent(Sender: TObject; AURLParams: TStringList);
var
  name, plz, pol_bez, land: String;
  kennziffer, code, pol_bez_kenn, pol_bez_code: Integer;
begin
  jqdGemeinden.Visible := false;

  name := jqeName.Text;
  plz := jqePlz.Text;
  pol_bez := jqePolBez.Text;
  land := jqeLandIso.Text;
  kennziffer := StrToInt(jqeKennziffer.Text);
  code := StrToInt(jqeCode.Text);
  pol_bez_kenn := StrToInt(jqePolBezKenn.Text);
  pol_bez_code := StrToInt(jqePolBezCode.Text);

  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('name').AsString := name;
  quNeu.ParamByName('plz').AsString := plz;
  quNeu.ParamByName('pol_bez').AsString := pol_bez;
  quNeu.ParamByName('land').AsString := land;
  quNeu.ParamByName('kennziffer').AsInteger := kennziffer;
  quNeu.ParamByName('code').AsInteger := code;
  quNeu.ParamByName('pol_bez_kenn').AsInteger := pol_bez_kenn;
  quNeu.ParamByName('pol_bez_code').AsInteger := pol_bez_code;
  quNeu.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quNeu.Execute;

  gemeindenAbfragen;
end;

procedure TGemeinden.jqbAendernOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  clearModal;
  if not moveQueryToRow(quGemeinden, jqgGemeinden) then
  begin
    Alert.Error('Es muss eine Gemeinde ausgewählt sein.');
    Abort;
  end;

  jqeName.Text := quGemeinden.FieldByName('gemeindename').AsString;
  jqePlz.Text := quGemeinden.FieldByName('amtsplz').AsString;
  jqePolBez.Text := quGemeinden.FieldByName('pol_bezirk').AsString;
  jqeLandIso.Text := quGemeinden.FieldByName('land_iso2').AsString;
  jqeKennziffer.Text := IntToStr(quGemeinden.FieldByName('gemeindekennziffer').AsInteger);
  jqeCode.Text := IntToStr(quGemeinden.FieldByName('gemeindecode').AsInteger);
  jqePolBezKenn.Text := IntToStr(quGemeinden.FieldByName('pol_bez_kennzif').AsInteger);
  jqePolBezCode.Text := IntToStr(quGemeinden.FieldByName('pol_bez_code').AsInteger);

  jqdGemeinden.JQDialogOptions.Title := 'Gemeinde ändern';
  jqb := jqdGemeinden.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Ändern';
  jqb.OnClick.OnEvent := jqdAendernEvent;
  jqb := jqdGemeinden.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdGemeinden.Visible := true;
end;

procedure TGemeinden.jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
var
  name, plz, pol_bez, land: String;
  kennziffer, code, pol_bez_kenn, pol_bez_code: Integer;
begin
  jqdGemeinden.Visible := false;

  name := jqeName.Text;
  plz := jqePlz.Text;
  pol_bez := jqePolBez.Text;
  land := jqeLandIso.Text;
  kennziffer := StrToInt(jqeKennziffer.Text);
  code := StrToInt(jqeCode.Text);
  pol_bez_kenn := StrToInt(jqePolBezKenn.Text);
  pol_bez_code := StrToInt(jqePolBezCode.Text);

  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('name').AsString := name;
  quAendern.ParamByName('plz').AsString := plz;
  quAendern.ParamByName('pol_bez').AsString := pol_bez;
  quAendern.ParamByName('land').AsString := land;
  quAendern.ParamByName('kennziffer').AsInteger := kennziffer;
  quAendern.ParamByName('code').AsInteger := code;
  quAendern.ParamByName('pol_bez_kenn').AsInteger := pol_bez_kenn;
  quAendern.ParamByName('pol_bez_code').AsInteger := pol_bez_code;
  quAendern.ParamByName('id').AsInteger := quGemeinden.FieldByName('Id').AsInteger;
  quAendern.Execute;

  gemeindenAbfragen;
end;

procedure TGemeinden.jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
begin
  if not moveQueryToRow(quGemeinden, jqgGemeinden) then
  begin
    Alert.Error('Es muss eine Gemeinde ausgewählt sein.');
    Abort;
  end;
  Alert.JQSweetAlertOptions.Title := 'Wollen Sie die Gemeinde wirklich löschen?';
  Alert.JQSweetAlertOptions.AlertType := jqsatWarning;
  Alert.JQSweetAlertOptions.ShowCancelButton := true;
  Alert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  Alert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  Alert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  Alert.JQSweetAlertOptions.OnBtnClick.OnEvent := gemeindeLoeschen;
  Alert.Show;
end;

procedure TGemeinden.gemeindeLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: boolean;
  id: Integer;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  id := quGemeinden.FieldByName('Id').AsInteger;
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('id').AsInteger := id;
  quLoeschen.Execute;
  gemeindenAbfragen;
end;

procedure TGemeinden.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdGemeinden.Visible := false;
end;

procedure TGemeinden.clearModal;
begin
  jqeKennziffer.Text := '';
  jqeName.Text := '';
  jqeCode.Text := '';
  jqePlz.Text := '';
  jqePolBezKenn.Text := '';
  jqePolBez.Text := '';
  jqePolBezCode.Text := '';
  jqeLandIso.Text := '';
  jqdGemeinden.JQDialogOptions.Buttons.Clear;
end;

end.
