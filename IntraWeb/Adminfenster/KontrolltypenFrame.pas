﻿unit KontrolltypenFrame;

interface

uses
  System.SysUtils, System.Classes, Vcl.Controls, Vcl.Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWCGJQGrid, IWCGJQControl,
  IWCGJQButton, IWHTMLContainer, IWHTML40Container, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQ<PERSON><PERSON><PERSON><PERSON>t, IWCGJQ<PERSON>ialog, IWCGJQEdit,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQFileUpload, IWCGJQDatePicker, IWCompMemo, IWCompEdit, IWCompCheckbox,
  IWCGJQRegion, IWCGJQLabel, IWCGJQCheckBox, IWCGJQDropDown, IWCGJQComboBox,
  IW.HTTP.Reply, IW.HTTP.Request;

type
  TKontrolltypen = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrRollenTop: TIWCGJQRegion;
    jqbAbfragen: TIWCGJQButton;
    jqbNeu: TIWCGJQButton;
    jqbAendern: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    iwrRollenMid: TIWCGJQRegion;
    jqgKontrolltypen: TIWCGJQGrid;
    jqdpKontrolltypen: TIWCGJQGridDataSetProvider;
    jqdKontrolltypen: TIWCGJQDialog;
    quKontrolltypen: TFDQuery;
    LabelKontrolltyp: TIWCGJQLabel;
    LabelBezeichnung: TIWCGJQLabel;
    LabelRefbkb: TIWCGJQLabel;
    LabelFreieAdresse: TIWCGJQLabel;
    LabelBkb: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    quNeu: TFDQuery;
    quLoeschen: TFDQuery;
    quAendern: TFDQuery;
    quKontrolltypenBKBTYP: TStringField;
    quKontrolltypenKONTROLLTYP: TStringField;
    quKontrolltypenBEZEICHNUNG: TStringField;
    quKontrolltypenREFBKB: TSmallintField;
    quKontrolltypenFREIE_ADRESSE: TBooleanField;
    quKontrolltypenSICHTBAR: TBooleanField;
    quKontrolltypenPROBEN: TBooleanField;
    quKontrolltypenOERTLICHKEITEN: TBooleanField;
    quKontrolltypenAUTO_KONTROLLIERT: TBooleanField;
    CheckFreieAdresse: TIWCGJQCheckBoxEx;
    EditKontrolltyp: TIWCGJQEdit;
    DropBkb: TIWCGJQDropDown;
    QBkb: TFDQuery;
    QBkbBKBTYP: TStringField;
    CheckRefbkb: TIWCGJQCheckBoxEx;
    LabelSichtbar: TIWCGJQLabel;
    LabelProben: TIWCGJQLabel;
    LabelOertlichkeiten: TIWCGJQLabel;
    LabelAutoKontrolliert: TIWCGJQLabel;
    CheckProben: TIWCGJQCheckBoxEx;
    CheckOertlichkeit: TIWCGJQCheckBoxEx;
    CheckAutoK: TIWCGJQCheckBoxEx;
    CheckSichtbar: TIWCGJQCheckBoxEx;
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure OnChangedData(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlert: TIWCGJQSweetAlert;
    property Alert:TIWCGJQSweetAlert read FAlert;

    procedure resetModal;
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure kontrolltypAendern(Sender: TObject; AURLParams: TStringList);
    procedure kontrolltypNeu(Sender: TObject; AURLParams: TStringList);
    procedure jqsaKontrolltypLoeschen(Sender: TObject; AParams: TStringList);
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean); reintroduce;
    procedure kontrolltypenAbfragen;
  end;

implementation

uses dmmain, Utility, JQ.Helpers.Grid;

{$R *.dfm}

constructor TKontrolltypen.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbAendern.Visible := false;
    jqbLoeschen.Visible := false;
  end;

  jqgKontrolltypen.SetupDefaults(jqdpKontrolltypen);

  QBkb.Close;
  QBkb.Open;
  QBkb.First;
  DropBkb.Items.Clear;
  while not QBkb.eof do
  begin
    var LItem := DropBkb.Items.Add;
    LItem.Caption := QBkbBKBTYP.AsString;
    LItem.Value := QBkbBKBTYP.AsString;
    QBkb.Next;
  end;
end;

procedure TKontrolltypen.jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
begin
  kontrolltypenAbfragen;
end;

procedure TKontrolltypen.kontrolltypenAbfragen;
begin
  RefreshQuery(quKontrolltypen);
end;

{Öffnet ein leeres Modalfenster}
procedure TKontrolltypen.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  resetModal;
  jqdKontrolltypen.JQDialogOptions.Title := 'Neuer Kontrolltyp';
  jqb := jqdKontrolltypen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.OnClick.OnEvent := kontrolltypNeu;
  jqb := jqdKontrolltypen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdKontrolltypen.Visible := true;
end;

{Fügt einen neuen Kontrolltyp mit den ausgefüllten Werten in die DB ein.}
procedure TKontrolltypen.kontrolltypNeu(Sender: TObject; AURLParams: TStringList);
begin
  jqdKontrolltypen.Visible := false;
  try
    quNeu.Close;
    quNeu.Prepare;
    quNeu.ParamByName('Bkbtyp').AsString := DropBkb.Text;
    quNeu.ParamByName('Kontrolltyp').AsString := EditKontrolltyp.Text;
    quNeu.ParamByName('Freie_Adresse').AsBoolean := CheckFreieAdresse.Checked;
    quNeu.ParamByName('Bez').AsString := jqeBezeichnung.Text;
    if CheckRefbkb.Checked then
    begin
      quNeu.ParamByName('Refbkb').AsInteger := 1;
    end
    else
    begin
      quNeu.ParamByName('Refbkb').AsInteger := 0;
    end;
    quNeu.ParamByName('Sichtbar').AsBoolean := CheckSichtbar.Checked;
    quNeu.ParamByName('Proben').AsBoolean := CheckProben.Checked;
    quNeu.ParamByName('Oertlichkeiten').AsBoolean := CheckOertlichkeit.Checked;
    quNeu.ParamByName('Auto_Kontrolliert').AsBoolean := CheckAutoK.Checked;
    quNeu.Execute;

    kontrolltypenAbfragen;
  except
    on E: Exception do Alert.Warning('Beim Anlegen ist ein Fehler aufgetreten' + sLineBreak + E.Message);
  end;
end;

{Füllt die Textfelder im Modalfenster mit den Daten des ausgewählten
 Kontrolltyps und öffnet das Modalfenster}
procedure TKontrolltypen.jqbAendernOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  if not moveQueryToRow(quKontrolltypen, jqgKontrolltypen) then begin
    Alert.Error('Es muss ein Kontrolltyp ausgewählt sein.');
    Abort;
  end;
  resetModal;
  DropBkb.Enabled := false;
  EditKontrolltyp.Enabled := false;
  DropBkb.JQDropDownOptions.SelectByValue(quKontrolltypen.FieldByName('Bkbtyp').AsString);
  EditKontrolltyp.Text := quKontrolltypen.FieldByName('Kontrolltyp').AsString;
  jqeBezeichnung.Text := quKontrolltypen.FieldByName('Bezeichnung').AsString;
  CheckFreieAdresse.Checked := quKontrolltypen.FieldByName('Freie_Adresse').AsBoolean;
  if quKontrolltypen.FieldByName('Refbkb').AsInteger = 0 then
  begin
    CheckRefbkb.Checked := false;
  end
  else
  begin
    CheckRefbkb.Checked := true;
  end;
  CheckSichtbar.Checked := quKontrolltypen.FieldByName('Sichtbar').AsBoolean;
  CheckProben.Checked := quKontrolltypen.FieldByName('Proben').AsBoolean;
  CheckOertlichkeit.Checked := quKontrolltypen.FieldByName('Oertlichkeiten').AsBoolean;
  CheckAutoK.Checked := quKontrolltypen.FieldByName('Auto_Kontrolliert').AsBoolean;

  jqdKontrolltypen.JQDialogOptions.Title := 'Kontrolltyp ändern';
  jqb := jqdKontrolltypen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Ändern';
  jqb.OnClick.OnEvent := kontrolltypAendern;
  jqb := jqdKontrolltypen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdKontrolltypen.Visible := true;
end;

procedure TKontrolltypen.kontrolltypAendern(Sender: TObject; AURLParams: TStringList);
begin
  try
    jqdKontrolltypen.Visible := false;
    quAendern.Close;
    quAendern.Prepare;
    quAendern.ParamByName('Bkbtyp').AsString := DropBkb.Text;
    quAendern.ParamByName('Kontrolltyp').AsString := EditKontrolltyp.Text;
    quAendern.ParamByName('Freie_Adresse').AsBoolean := CheckFreieAdresse.Checked;
    quAendern.ParamByName('Bez').AsString := jqeBezeichnung.Text;
    if CheckRefbkb.Checked then
    begin
      quAendern.ParamByName('Refbkb').AsInteger := 1;
    end
    else
    begin
      quAendern.ParamByName('Refbkb').AsInteger := 0;
    end;
    quAendern.ParamByName('Sichtbar').AsBoolean := CheckSichtbar.Checked;
    quAendern.ParamByName('Proben').AsBoolean := CheckProben.Checked;
    quAendern.ParamByName('Oertlichkeiten').AsBoolean := CheckOertlichkeit.Checked;
    quAendern.ParamByName('Auto_Kontrolliert').AsBoolean := CheckAutoK.Checked;
    quAendern.Execute;
    kontrolltypenAbfragen;
  except
    on E: Exception do Alert.Warning('Beim Bearbeiten ist ein Fehler aufgetreten' + sLineBreak + E.Message);
  end;
end;

{Setzt die Query auf die ausgewählte Rolle und fordert danach den User auf das
 Löschen mittels eines SweetAlerts zu bestätigen.}
procedure TKontrolltypen.jqbLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(quKontrolltypen, jqgKontrolltypen) then begin
    Alert.Error('Es muss ein Kontrolltyp ausgewählt sein.');
    Abort;
  end;
  alert.JQSweetAlertOptions.Title := 'Wollen Sie den Kontrolltyp löschen?';
  alert.JQSweetAlertOptions.AlertType := jqsatWarning;
  alert.JQSweetAlertOptions.ShowCancelButton := True;
  alert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  alert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  alert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  alert.JQSweetAlertOptions.OnBtnClick.OnEvent:= jqsaKontrolltypLoeschen;
  alert.Show;
end;

procedure TKontrolltypen.jqsaKontrolltypLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  bkbtyp, kontrolltyp: String;
begin
  isConfirmButton:= StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then begin
    Exit;
  end;
  bkbtyp := quKontrolltypen.FieldByName('bkbtyp').AsString;
  kontrolltyp := quKontrolltypen.FieldByName('kontrolltyp').AsString;
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('bkbtyp').AsString := bkbtyp;
  quLoeschen.ParamByName('kontrolltyp').AsString := kontrolltyp;
  quLoeschen.Execute;
  kontrolltypenAbfragen;
end;

procedure TKontrolltypen.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdKontrolltypen.Visible := false;
end;

procedure TKontrolltypen.OnChangedData(Sender: TObject; AParams: TStringList);
begin
  //
end;

procedure TKontrolltypen.resetModal;
begin
  DropBkb.Enabled := true;
  EditKontrolltyp.Enabled := true;
  DropBkb.SelectedIndex := -1;
  EditKontrolltyp.Text := '';
  jqeBezeichnung.Text := '';
  CheckFreieAdresse.Checked := false;
  CheckRefbkb.Checked := false;
  CheckSichtbar.Checked := false;
  CheckProben.Checked := false;
  CheckOertlichkeit.Checked := false;
  CheckAutoK.Checked := false;
  jqdKontrolltypen.JQDialogOptions.Buttons.Clear;
end;

end.
