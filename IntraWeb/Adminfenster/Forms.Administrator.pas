﻿unit Forms.Administrator;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, Vcl.Controls,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompButton,
  IWCompMemo, Vcl.Imaging.jpeg, IWCompExtCtrls, Vcl.Forms, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCompGrids,
  IWDBStdCtrls, IWCompTabControl, IWDBGrids, Data.DB, IWCGJQControl,
  IWCGJQButtons, IWCGJQNavigator, Vcl.Graphics, IWCompLabel, IWCompEdit,
  IWCompCheckbox, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider,
  IWCGJQGrid, IWCGJQButton, IWCGJQRegion,
  IWCGJQGridCollectionProvider, FireDAC.Comp.Client, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  FireDAC.Comp.DataSet, IWCGJQAutoComplete, IWCGJQTabs, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQShowMessage,
  Forms.Base, IWCompText, IWCompTimeEdit, IWCGJQDatePicker, IWCGJQSweetAlert,
  RollenBkbtypFrame, PersonenprefixFrame, BundeslaenderFrame, GemeindenFrame,
  RollenFrame, KontrolltypenFrame, BkbtypenFrame, System.Generics.Collections,
  KommunikationsartenFrame, KommunikationswegeFrame,
  BetriebeKommunikationswegeFrame, GruppenFrame, IWCGJQLabel, Frames.GruppenAdmin,
  Frames.UserGruppenAdmin, Modules.Admin, IWCGJQEdit;

type
  TFormAdministrator = class(TFormBase)
    ds_funktionen: TDataSource;
    ds_sysuser: TDataSource;
    jqdpFunktionen: TIWCGJQGridDataSetProvider;
    ProviderUser: TIWCGJQGridDataSetProvider;
    ds_gruppen: TDataSource;
    jqdpGruppen: TIWCGJQGridDataSetProvider;
    jqdpUsergruppen: TIWCGJQGridDataSetProvider;
    ds_usergruppen: TDataSource;
    jqdpUsergruppenUsername: TIWCGJQGridDataSetProvider;
    quUsergruppeUsername: TFDQuery;
    quUsergruppeAddUser: TFDQuery;
    quUsergruppeRemoveUser: TFDQuery;
    quExistiertUserGruppe: TFDQuery;
    quUpdateGruppenUser: TFDQuery;
    jqdpRollen: TIWCGJQGridDataSetProvider;
    iwmAdmin: TIWModalWindow;
    iwrModal: TIWCGJQRegion;
    jqsaAdmin: TIWCGJQSweetAlert;
    jqdpFunktionsrollen: TIWCGJQGridDataSetProvider;
    iwrFunktionsrollenModal: TIWCGJQRegion;
    jqgFunktionsrolleFunktion: TIWCGJQGrid;
    iwrFunktionsrolleFunktion: TIWCGJQRegion;
    iwrFunktionsrolleRolle: TIWCGJQRegion;
    IWRegion4: TIWCGJQRegion;
    IWRegion5: TIWCGJQRegion;
    jqgFunktionsrolleRolle: TIWCGJQGrid;
    jqdpFunktionsrollenFunktion: TIWCGJQGridDataSetProvider;
    jqdpFunktionsrollenRolle: TIWCGJQGridDataSetProvider;
    EditFunktionsrollenFunktionsBezeichnung: TIWCGJQEdit;
    EditFunktionsrollenRollenBezeichnung: TIWCGJQEdit;
    IWLabel15: TIWCGJQLabel;
    IWLabel16: TIWCGJQLabel;
    jqbFunktionsrollenFunktion: TIWCGJQButton;
    jqbFunktionsrollenRolle: TIWCGJQButton;
    jqdpRechtsgrundlagen: TIWCGJQGridDataSetProvider;
    iwrRechtsgrundlagenModal: TIWCGJQRegion;
    IWLabel19: TIWCGJQLabel;
    IWLabel20: TIWCGJQLabel;
    EditRechtsgrundlageBezeichnung: TIWCGJQEdit;
    EditRechtsgrundlageKurzbezeichnung: TIWCGJQEdit;
    jqdpBkbRechtsgrundlage: TIWCGJQGridDataSetProvider;
    iwrBkbRechtsgrundlagenModal: TIWCGJQRegion;
    iwrBkbRechtsgrundlageBkb: TIWCGJQRegion;
    iwrBkbRechtsgrundlageRechtsgrundlage: TIWCGJQRegion;
    jqgBkbRechtsgrundlageBkb: TIWCGJQGrid;
    jqgBkbRechtsgrundlageRechtsgrundlage: TIWCGJQGrid;
    jqdpBkbRechtsgrundlageBkb: TIWCGJQGridDataSetProvider;
    jqdpBkbRechtsgrundlageRechtsgrundlage: TIWCGJQGridDataSetProvider;
    jqdpBkbtypen: TIWCGJQGridDataSetProvider;
    Tabs: TIWCGJQTabs;
    FunktionenTab: TIWCGJQTab;
    UserTab: TIWCGJQTab;
    GruppenTab: TIWCGJQTab;
    UsergruppenTab: TIWCGJQTab;
    RollenTab: TIWCGJQTab;
    Funktionsrollentab: TIWCGJQTab;
    RechtsgrundlagenTab: TIWCGJQTab;
    BkbtypenRechtsgrundlagenTab: TIWCGJQTab;
    BkbtypenTab: TIWCGJQTab;
    RollenBkbtypenTab: TIWCGJQTab;
    PersonenpraefixTab: TIWCGJQTab;
    BundeslaenderTab: TIWCGJQTab;
    GemeindenTab: TIWCGJQTab;
    KontrolltypenTab: TIWCGJQTab;
    iwrBkbRechtsgrundlageMid: TIWCGJQRegion;
    jqgBkbRechtsgrundlage: TIWCGJQGrid;
    iwrBkbRechtsgrundlageTop: TIWCGJQRegion;
    jqbBkbRechtsgrundlageNeu: TIWCGJQButton;
    jqbBkbRechtsgrundlageLoeschen: TIWCGJQButton;
    jqbBkbRechtsgrundlageAbfragen: TIWCGJQButton;
    jqgFunktionen: TIWCGJQGrid;
    iwrFunktionsrollenMid: TIWCGJQRegion;
    jqgFunktionsrollen: TIWCGJQGrid;
    iwrFunktionsrollenTop: TIWCGJQRegion;
    IWLabel17: TIWCGJQLabel;
    IWLabel18: TIWCGJQLabel;
    EditFunktionsrollenFunktionsSuche: TIWCGJQEdit;
    EditFunktionsrollenRollenSuche: TIWCGJQEdit;
    jqbZuweisungLoeschen: TIWCGJQButton;
    jqbNeueZuweisung: TIWCGJQButton;
    jqbFunktionsrollenSuchen: TIWCGJQButton;
    iwrRechtsgrundlagenMid: TIWCGJQRegion;
    jqgRechtsgrundlagen: TIWCGJQGrid;
    iwrRechtsgrundlagenTop: TIWCGJQRegion;
    jqbRechtsgrundlagenAbfragen: TIWCGJQButton;
    jqbRechtsgrundlageNeu: TIWCGJQButton;
    jqbRechtsgrundlageAendern: TIWCGJQButton;
    jqbRechtsgrundlageLoeschen: TIWCGJQButton;
    GridUser: TIWCGJQGrid;
    IWRegion2: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    iwusersuchen: TIWButton;
    IWImage2: TIWImage;
    IWRegion3: TIWCGJQRegion;
    IWDBEdit1: TIWDBEdit;
    IWLabel3: TIWCGJQLabel;
    IWDBCheckBox1: TIWDBCheckBox;
    IWLabel4: TIWCGJQLabel;
    IWDBEdit2: TIWDBEdit;
    EditUsername: TIWCGJQEdit;
    RegionGruppen: TIWCGJQRegion;
    IWCGJQRegion1: TIWCGJQRegion;
    IWCGJQRegion2: TIWCGJQRegion;
    GridGruppenVonUser: TIWCGJQGrid;
    IWCGJQLabel1: TIWCGJQLabel;
    GridGruppenVonUserProvider: TIWCGJQGridDataSetProvider;
    QGruppenVonUser: TFDQuery;
    procedure IWAppFormCreate(Sender: TObject);
    procedure userSuchenResetOnClick(Sender: TObject);
    procedure userSuchenOnClick(Sender: TObject);
    procedure iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbZuweisungLoeschenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbNeueZuweisungOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbFunktionsrollenFunktionOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbFunktionsrollenRolleOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbFunktionsrollenSuchenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbRechtsgrundlagenAbfragenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbRechtsgrundlagenNeuOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbRechtsgrundlagenAendernOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbRechtsgrundlagenLoeschenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbBkbRechtsgrundlageAbfragenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbBkbRechtsgrundlageNeuOnClick(Sender: TObject;
      AParams: TStringList);
    procedure jqbBkbRechtsgrundlageLoeschenOnClick(Sender: TObject;
      AParams: TStringList);
    procedure TabsOnSelect(Sender: TObject; AParams: TStringList);
    procedure UserGridOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure OnChangeData(Sender: TObject; AParams: TStringList);
  private
    RollenBkbtypFrame: TRollenBkbtyp;
    personenpraefixFrame: TPersonenpraefix;
    BundeslaenderFrame: TBundeslaender;
    GemeindenFrame: TGemeinden;
    RollenFrame: TRollen;
    KontrolltypenFrame: TKontrolltypen;
    BkbtypenFrame: TBkbtypen;
    KommunikationsartenFrame: TKommunikationsarten;
    KommunikationswegeFrame: TKommunikationswege;
    BetriebeKommunikationswegeFrame: TBetriebeKommunikationswege;

    DMAdmin: TDMAdmin;
    GruppenAdmin: TGruppenAdmin;
    UserGruppenAdmin: TUsergruppenAdmin;

    aktiveTabs: TList<String>;

    procedure FunktionenKonfigurieren;

    procedure userSuchenQuery(username: string);
    procedure funktionsrollenAbfragen(funktionsBezeichnung: String = ''; rollenBezeichnung: String = '');
    procedure funktionsrollenZuweisungLoeschen(Sender: TObject; AParams: TStringList);
    procedure funktionsrolleNeu(Sender: TObject; EventParams: TStringList);
    function funktionsrolleExistiert(idRolle: Integer; idFunktion: Integer): Boolean;

    procedure rechtsgrundlagenAbfragen;
    procedure rechtsgrundlageNeu(Sender: TObject; EventParams: TStringList);
    procedure rechtsgrundlageAendern(Sender: TObject; EventParams: TStringList);
    procedure rechsgrundlageLoeschen(Sender: TObject; AParams: TStringList);

    procedure bkbRechtsgrundlagenAbfragen;
    procedure bkbRechsgrundlageLoeschen(Sender: TObject; AParams: TStringList);
    procedure bkbRechtsgrundlageNeu(Sender: TObject; EventParams: TStringList);

    procedure bkbtypenShow;
    procedure rollenShow;
    procedure rollenBkbtypShow;
    procedure personenPrefixShow;
    procedure bundeslaenderShow;
    procedure gemeindenShow;
    procedure kontrolltypenShow;
    procedure KommunikationsartenShow;
    procedure KommunikationswegeShow;
    procedure BetriebeKommunikationswegeShow;
  public
    GruppenFrame: TGruppenF;
    destructor Destroy; override;
  end;

implementation

{$R *.dfm}


uses dmmain, UserSessionUnit, Utility, Funktionen, ServerController, StrUtils, JQ.Helpers.Grid;

procedure TFormAdministrator.IWAppFormCreate(Sender: TObject);
begin
  Inherited;
  DMAdmin := TDMAdmin.Create(Self);
  FunktionenKonfigurieren;
  TabsOnSelect(Nil, Nil);
  GridUser.SetupDefaults(ProviderUser);
end;

destructor TFormAdministrator.Destroy;
begin
  aktiveTabs.Free;
  inherited;
end;

/// Schaltet die für den Benutzer (nicht) erlaubten Funktionen aus/ein.
procedure TFormAdministrator.FunktionenKonfigurieren;
var
  Funktionen: TFunktionenManager;
  bearbeitbar: Boolean;
  startTab: Integer;
begin
  Funktionen := Usersession.FunktionenManager;
  aktiveTabs := TList<String>.Create;
  startTab := -1;

  if Funktionen.HatFunktion(Funktionen_anzeigen) then
  begin
    FunktionenTab.Visible := true;
    aktiveTabs.Add('Funktionen');
    startTab := 0;
  end;
  if Funktionen.HatFunktion(User_anzeigen) then
  begin
    UserTab.Visible := true;
    aktiveTabs.Add('User');
    if startTab = -1 then
      startTab := 1;
  end;
  if Funktionen.HatFunktion(Gruppen_anzeigen) then
  begin
    GruppenTab.Visible := true;
    GruppenAdmin := TGruppenAdmin.Create(GruppenTab, DMAdmin);
    GruppenAdmin.Parent := GruppenTab;
    aktiveTabs.Add('Gruppen');
    if startTab = -1 then
      startTab := 2;
  end;
  if Funktionen.HatFunktion(Usergruppen_anzeigen) then
  begin
    UsergruppenTab.Visible := true;
    bearbeitbar := Funktionen.HatFunktion(Usergruppen_bearbeiten);
    UserGruppenAdmin := TUsergruppenAdmin.Create(UsergruppenTab, DMAdmin, bearbeitbar);
    UserGruppenAdmin.Parent := UsergruppenTab;
    aktiveTabs.Add('Usergruppen');
    if startTab = -1 then
      startTab := 3;
  end;
  if Funktionen.HatFunktion(Rollen_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Rollen_bearbeiten);
    RollenFrame := TRollen.Create(Self, jqsaAdmin, bearbeitbar);
    RollenFrame.Parent := RollenTab;
    RollenTab.Visible := true;
    aktiveTabs.Add('Rollen');
    if startTab = -1 then
      startTab := 4;
  end;
  if Funktionen.HatFunktion(Funktionsrollen_anzeigen) then
  begin
    Funktionsrollentab.Visible := true;
    aktiveTabs.Add('Funktionsrollen');
    if startTab = -1 then
      startTab := 5;
  end;
  if Funktionen.HatFunktion(Rechtsgrundlagen_anzeigen) then
  begin
    RechtsgrundlagenTab.Visible := true;
    aktiveTabs.Add('Rechtsgrundlagen');
    if startTab = -1 then
      startTab := 6;
  end;
  if Funktionen.HatFunktion(Bkbtypen_Rechtsgrundlage_anzeigen) then
  begin
    BkbtypenRechtsgrundlagenTab.Visible := true;
    aktiveTabs.Add('Bkbtypen-Rechtsgrundlage');
    if startTab = -1 then
      startTab := 7;
  end;
  if Funktionen.HatFunktion(Bkbtypen_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Bkbtypen_bearbeiten);
    BkbtypenFrame := TBkbtypen.Create(Self, jqsaAdmin, bearbeitbar);
    BkbtypenFrame.Parent := BkbtypenTab;
    BkbtypenTab.Visible := true;
    aktiveTabs.Add('Bkbtypen');
    if startTab = -1 then
      startTab := 8;
  end;
  if Funktionen.HatFunktion(Rollen_Bkbtyp_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Rollen_Bkbtyp_bearbeiten);
    RollenBkbtypFrame := TRollenBkbtyp.Create(Self, iwmAdmin, jqsaAdmin, bearbeitbar);
    RollenBkbtypFrame.Parent := RollenBkbtypenTab;
    RollenBkbtypenTab.Visible := true;
    aktiveTabs.Add('Rollen-Bkbtyp');
    if startTab = -1 then
      startTab := 9;
  end;
  if Funktionen.HatFunktion(Personenpraefix_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Personenpraefix_bearbeiten);
    personenpraefixFrame := TPersonenpraefix.Create(Self, jqsaAdmin, bearbeitbar);
    personenpraefixFrame.Parent := PersonenpraefixTab;
    PersonenpraefixTab.Visible := true;
    aktiveTabs.Add('Personenpräfix');
    if startTab = -1 then
      startTab := 10;
  end;
  if Funktionen.HatFunktion(Bundeslaender_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Bundeslaender_bearbeiten);
    BundeslaenderFrame := TBundeslaender.Create(Self, jqsaAdmin, bearbeitbar);
    BundeslaenderFrame.Parent := BundeslaenderTab;
    BundeslaenderTab.Visible := true;
    aktiveTabs.Add('Bundesländer');
    if startTab = -1 then
      startTab := 11;
  end;
  if Funktionen.HatFunktion(Gemeinden_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Gemeinden_bearbeiten);
    GemeindenFrame := TGemeinden.Create(Self, jqsaAdmin,
      webApplication, bearbeitbar);
    GemeindenFrame.Parent := GemeindenTab;
    GemeindenTab.Visible := true;
    aktiveTabs.Add('Gemeinden');
    if startTab = -1 then
      startTab := 12;
  end;
  if Funktionen.HatFunktion(Kontrolltypen_anzeigen) then
  begin
    bearbeitbar := Funktionen.HatFunktion(Kontrolltypen_bearbeiten);
    KontrolltypenFrame := TKontrolltypen.Create(Self, jqsaAdmin, bearbeitbar);
    KontrolltypenFrame.Parent := KontrolltypenTab;
    KontrolltypenTab.Visible := true;
    aktiveTabs.Add('Kontrolltypen');
    if startTab = -1 then
      startTab := 13;
  end;
  { kommunikationsartenFrame := TKommunikationsarten.Create(Self, jqsaAdmin,
    WebApplication, true);
    kommunikationsartenFrame.Parent := TabKommunikationsarten;
    TabKommunikationsarten.Visible := true;
    aktiveTabs.Add('Kommunikationsarten');

    kommunikationswegeFrame := TKommunikationswege.Create(Self, jqsaAdmin,
    WebApplication, true);
    kommunikationswegeFrame.Parent := TabKommunikationswege;
    TabKommunikationswege.Visible := true;
    aktiveTabs.Add('Kommunikationswege');

    betriebeKommunikationswegeFrame := TBetriebeKommunikationswege.Create(Self,
    jqsaAdmin, WebApplication, true);
    betriebeKommunikationswegeFrame.Parent := TabBetriebeKommunikationswege;
    TabBetriebeKommunikationswege.Visible := true;
    aktiveTabs.Add('BetriebeKommunikationswege'); }

  // Bearbeiten
  if not Funktionen.HatFunktion(Funktionsrollen_bearbeiten) then
  begin
    jqbNeueZuweisung.Visible := false;
    jqbZuweisungLoeschen.Visible := false;
  end;
  if not Funktionen.HatFunktion(Bkbtypen_Rechtsgrundlage_bearbeiten) then
  begin
    jqbBkbRechtsgrundlageNeu.Visible := false;
    jqbBkbRechtsgrundlageLoeschen.Visible := false;
  end;
  if not Funktionen.HatFunktion(Rechtsgrundlagen_bearbeiten) then
  begin
    jqbRechtsgrundlageNeu.Visible := false;
    jqbRechtsgrundlageAendern.Visible := false;
    jqbRechtsgrundlageLoeschen.Visible := false;
  end;
  Tabs.ActiveTabIndex := startTab;
end;

procedure TFormAdministrator.iwcgb_ZurueckJQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
  Release;
end;

{ Die Prozedur wird jedes mal aufgerufen, wenn ein neuer Tab geöffnet wird.
  Abhängig von der Tabnummer wird dann eine query abgeschickt um die Seite mit
  Daten zu füllen. }
procedure TFormAdministrator.TabsOnSelect(Sender: TObject; AParams: TStringList);
begin
  { case IndexText(aktiveTabs[Tabs.ActiveTabIndex],
    ['Funktionen', 'User', 'Gruppen',
    'Usergruppen', 'Rollen', 'Funktionsrollen', 'Rechtsgrundlagen',
    'Bkbtypen-Rechtsgrundlage', 'Bkbtypen', 'Rollen-Bkbtyp',
    'Personenpräfix', 'Bundesländer', 'Gemeinden',
    'Kontrolltypen', 'Kommunikationsarten', 'Kommunikationswege',
    'BetriebeKommunikationswege']) of }
  case Tabs.ActiveTabIndex of
    0:
      RefreshQuery(dm_main.qu_funktionen);
    1:
      userSuchenQuery('');
    2:
      GruppenAdmin.FillTreeGruppen;
    3:
      UserGruppenAdmin.FillTreeGruppen;
    4:
      rollenShow;
    5:
      funktionsrollenAbfragen;
    6:
      rechtsgrundlagenAbfragen;
    7:
      bkbRechtsgrundlagenAbfragen;
    8:
      bkbtypenShow;
    9:
      rollenBkbtypShow;
    10:
      personenPrefixShow;
    11:
      bundeslaenderShow;
    12:
      gemeindenShow;
    13:
      kontrolltypenShow;
    14:
      KommunikationsartenShow;
    15:
      KommunikationswegeShow;
    16:
      BetriebeKommunikationswegeShow;
  end;

end;

{ ***************************************************************************
  ****************************User-Tab*************************************
  **************************************************************************** }

{ Die Prozedur wird vom User-Tab beim Klicken auf den Suchen-Knopf aufgerufen.
  Die Textfelder Username und Pskey werden ausgelesen und damit eine SQL-Query
  gebaut. }
procedure TFormAdministrator.userSuchenOnClick(Sender: TObject);
var
  // sql : string;
  username: string;
begin
  username := UpperCase(trim(EditUsername.Text));
  userSuchenQuery(username);
end;

{ Leert die Textfelder für die Suche von Usern und startet eine leere Query }
procedure TFormAdministrator.userSuchenResetOnClick(Sender: TObject);
begin
  EditUsername.Text := '';
  userSuchenQuery('');
end;

{ Nimmt den Username und den pskey aus den Labeln und baut damit eine Query }
procedure TFormAdministrator.userSuchenQuery(username: string);
begin
  dm_main.qu_user_suche.close;
  dm_main.qu_user_suche.Prepare;
  dm_main.qu_user_suche.ParamByName('USERNAME').AsString := username;
  dm_main.qu_user_suche.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  dm_main.qu_user_suche.Active := true;
end;

procedure TFormAdministrator.UserGridOnSelectRow(Sender: TObject; AParams: TStringList);
var
  userid: Integer;
begin
  inherited;
  moveQueryToRow(dm_main.qu_user_suche, GridUser);
  userid := dm_main.qu_user_suche.FieldByName('id').AsInteger;
  QGruppenVonUser.close;
  QGruppenVonUser.ParamByName('userid').AsInteger := userid;
  QGruppenVonUser.Open;
end;

{ *******************************************************************************
  ***************************Funktionsrollen-Tab**********************************
  ******************************************************************************* }
{ Nimmt die Bezeichnungen aus den Textfeldern und filtert dann die
  Funktionsrollen danach. }
procedure TFormAdministrator.jqbFunktionsrollenSuchenOnClick(Sender: TObject;
  AParams: TStringList);
var
  funktionsBezeichnung: String;
  rollenBezeichnung: String;
begin
  funktionsBezeichnung := EditFunktionsrollenFunktionsSuche.Text;
  rollenBezeichnung := EditFunktionsrollenRollenSuche.Text;
  funktionsrollenAbfragen(funktionsBezeichnung, rollenBezeichnung);
end;

{ Fragt die Funktionsrollen ab und filtert dabei nach den übergebenen
  Bezeichnungen. Wenn nichts übergeben wird, wird alles ausgeben }
procedure TFormAdministrator.funktionsrollenAbfragen
  (funktionsBezeichnung: String = ''; rollenBezeichnung: String = '');
var
  query: TFDQuery;
begin
  query := dm_main.qu_funktionsrollen;
  query.close;
  query.ParamByName('fbez').AsString := funktionsBezeichnung;
  query.ParamByName('rbez').AsString := rollenBezeichnung;
  query.Active := true;
end;

{ Öffnet ein Modalfenster in dem der User Funktion und Rolle auswählen muss }
procedure TFormAdministrator.jqbNeueZuweisungOnClick(Sender: TObject;
  AParams: TStringList);
begin
  dm_main.qu_funktionsrollen_funktion.close;
  dm_main.qu_funktionsrollen_funktion.Prepare;
  dm_main.qu_funktionsrollen_funktion.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  dm_main.qu_funktionsrollen_funktion.Active := true;
  dm_main.qu_funktionsrollen_rollen.close;
  dm_main.qu_funktionsrollen_rollen.Prepare;
  dm_main.qu_funktionsrollen_rollen.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  dm_main.qu_funktionsrollen_rollen.Active := true;

  with iwmAdmin do
  begin
    Title := 'Neue Funktionsrolle';
    ContentElement := iwrFunktionsrollenModal;
    Buttons.CommaText := '&Erstellen,&Abbrechen';
    OnAsyncClick := funktionsrolleNeu;
    Show;
  end;
end;

{ Wird das Modalfenster geschlossen, wird als erstes geschaut, ob eine positive
  Bestätigung erfolt ist. Danch wird die funktionsrolle gesetzt }
procedure TFormAdministrator.funktionsrolleNeu(Sender: TObject; EventParams: TStringList);
var
  begdat: TDateTime;
  enddat: TDateTime;
  query: TFDQuery;
  idFunktion, idRolle: Integer;
begin
  if iwmAdmin.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  // Exit wenn nicht in beiden Grids eine Reihe ausgewählt wurde
  if not(moveQueryToRow
    (dm_main.qu_funktionsrollen_funktion, jqgFunktionsrolleFunktion))
    or not(moveQueryToRow
    (dm_main.qu_funktionsrollen_rollen, jqgFunktionsrolleRolle)) then
  begin
    Alert.Error('Es muss eine Funktion und eine Rolle ausgewählt sein.');
    Abort
  end;
  // Bekomme die ID der ausgewählten Funktion/Rolle
  idFunktion := dm_main.qu_funktionsrollen_funktion.FieldByName('Id').AsInteger;
  idRolle := dm_main.qu_funktionsrollen_rollen.FieldByName('Id').AsInteger;
  // Setzte die Daten
  begdat := Now();
  enddat := Now();
  enddat := IncMonth(enddat, 12 * 500);

  // Schaue ob die Funktionsrolle schon existiert. Wenn ja, wird die query
  // genommen, welche lediglich das beginn- enddatum neu setzt
  if funktionsrolleExistiert(idRolle, idFunktion) then
  begin
    query := dm_main.qu_funktionsrollen_update;
  end
  else
  begin
    query := dm_main.qu_funktionsrollen_neu;
  end;

  // Beide queries brauchen genau die gleichen parameter, also werden sie
  // gesetzt und die query gestartet
  query.close;
  query.ParamByName('id_funktion').AsInteger := idFunktion;
  query.ParamByName('id_rolle').AsInteger := idRolle;
  query.ParamByName('begdat').AsDate := begdat;
  query.ParamByName('enddat').AsDate := enddat;
  query.Execute;

  funktionsrollenAbfragen;
end;

{ Nimmt die ausgewählte Funktion und Rolle und schaut, ob es in der DB schon eine
  Kombination von ihnen gibt. }
function TFormAdministrator.funktionsrolleExistiert(idRolle: Integer; idFunktion: Integer): Boolean;
var
  query: TFDQuery;
begin
  query := dm_main.qu_existiert_funktionsrolle;
  query.close;
  query.ParamByName('id_funktion').AsInteger := idFunktion;
  query.ParamByName('id_rolle').AsInteger := idRolle;
  query.Open;
  query.First;
  Result := query.FieldByName('Anzahl').AsInteger > 0;
end;

{ Sucht im Modalfenster nach den Funktionen mit der Bezeichnung }
procedure TFormAdministrator.jqbFunktionsrollenFunktionOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_funktionsrollen_funktion;
  query.close;
  query.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  query.ParamByName('Bez').AsString := EditFunktionsrollenFunktionsBezeichnung.Text;
  query.Active := true;
end;

{ Sucht im Modalfenster nach den Rollen mit der Bezeichnung }
procedure TFormAdministrator.jqbFunktionsrollenRolleOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_funktionsrollen_rollen;
  query.close;
  query.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  query.ParamByName('Bez').AsString := EditFunktionsrollenRollenBezeichnung.Text;
  query.Active := true;
end;

{ Wird auf den Löschen Button gedrückt, wird geprüft ob eine Zeile ausgewählt
  ist und danach ein JQSweetAlert geöffnet der den User nach Bestätigung fragt. }
procedure TFormAdministrator.jqbZuweisungLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
  index: String;
  row: Integer;
begin
  index := jqgFunktionsrollen.JQGridOptions.SelRow;
  if index.Equals('') then
  begin
    Alert.Error('Es muss eine Zeile ausgewählt sein.');
    Abort;
  end;
  row := StrToInt(index) - 1;
  query := dm_main.qu_funktionsrollen;
  query.First;
  query.MoveBy(row);

  jqsaAdmin.JQSweetAlertOptions.Title := 'Wollen Sie die Zuweisung löschen?';
  jqsaAdmin.JQSweetAlertOptions.AlertType := jqsatWarning;
  jqsaAdmin.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  jqsaAdmin.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaAdmin.JQSweetAlertOptions.OnBtnClick.OnEvent := funktionsrollenZuweisungLoeschen;
  jqsaAdmin.Show;
end;

{ Wird der JQSweetAlert geschlossen, wird geschaut, ob die Rückmeldung positiv
  ist und danach wird die Zuweisung gelöscht }
procedure TFormAdministrator.funktionsrollenZuweisungLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  idFunktion, idRolle: Integer;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  idFunktion := dm_main.qu_funktionsrollen.FieldByName('Id_Funktion').AsInteger;
  idRolle := dm_main.qu_funktionsrollen.FieldByName('Id_Rolle').AsInteger;

  query := dm_main.qu_funktionsrollen_loeschen;
  query.close;
  query.ParamByName('enddat').AsDate := Now;
  query.ParamByName('Id_funktion').AsInteger := idFunktion;
  query.ParamByName('Id_rolle').AsInteger := idRolle;
  query.Execute;
  funktionsrollenAbfragen;
end;

{ *******************************************************************************
  ***************************Rechtsgrundlagen-Tab*********************************
  ******************************************************************************* }

procedure TFormAdministrator.jqbRechtsgrundlagenAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  rechtsgrundlagenAbfragen;
end;

procedure TFormAdministrator.rechtsgrundlagenAbfragen;
var
  query: TFDQuery;
begin
  query := dm_main.qu_rechtsgrundlagen;
  query.close;
  query.Active := true;
end;

procedure TFormAdministrator.jqbRechtsgrundlagenNeuOnClick(Sender: TObject;
  AParams: TStringList);
begin
  with iwmAdmin do
  begin
    Title := 'Neue Rechtsgrundlage';
    ContentElement := iwrRechtsgrundlagenModal;
    Buttons.CommaText := '&Erstellen,&Abbrechen';
    OnAsyncClick := rechtsgrundlageNeu;
    Show;
  end;
end;

procedure TFormAdministrator.rechtsgrundlageNeu(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
  bezeichnung: String;
  kurzbezeichnung: String;
begin
  if iwmAdmin.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  bezeichnung := EditRechtsgrundlageBezeichnung.Text;
  kurzbezeichnung := EditRechtsgrundlageKurzbezeichnung.Text;

  query := dm_main.qu_rechtsgrundlagen_neu;
  query.close;
  if bezeichnung <> '' then
    query.ParamByName('bez').AsString := bezeichnung;
  if kurzbezeichnung <> '' then
    query.ParamByName('kurzbez').AsString := kurzbezeichnung;
  query.Execute;

  rechtsgrundlagenAbfragen;
end;

procedure TFormAdministrator.jqbRechtsgrundlagenAendernOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_rechtsgrundlagen;
  if not moveQueryToRow(query, jqgRechtsgrundlagen) then
    Exit;
  EditRechtsgrundlageBezeichnung.Text := query.FieldByName('Bezeichnung').AsString;
  EditRechtsgrundlageKurzbezeichnung.Text := query.FieldByName('Kurzbezeichnung').AsString;

  with iwmAdmin do
  begin
    Title := 'Rechtsgrundlage ändern';
    ContentElement := iwrRechtsgrundlagenModal;
    Buttons.CommaText := '&Ändern,&Abbrechen';
    OnAsyncClick := rechtsgrundlageAendern;
    Show;
  end;
end;

procedure TFormAdministrator.rechtsgrundlageAendern(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
  bezeichnung: String;
  kurzbezeichnung: String;
  id: Integer;
begin
  if iwmAdmin.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  bezeichnung := EditRechtsgrundlageBezeichnung.Text;
  kurzbezeichnung := EditRechtsgrundlageKurzbezeichnung.Text;
  id := dm_main.qu_rechtsgrundlagen.FieldByName('Id').AsInteger;

  query := dm_main.qu_rechtsgrundlagen_update;
  query.close;
  query.ParamByName('Id').AsInteger := id;
  if bezeichnung <> '' then
    query.ParamByName('bez').AsString := bezeichnung;
  if kurzbezeichnung <> '' then
    query.ParamByName('kurzbez').AsString := kurzbezeichnung;
  query.Execute;

  rechtsgrundlagenAbfragen;
end;

procedure TFormAdministrator.jqbRechtsgrundlagenLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(dm_main.qu_rechtsgrundlagen, jqgRechtsgrundlagen) then
    Exit;

  jqsaAdmin.JQSweetAlertOptions.Title := 'Wollen Sie die Rechtsgrundlage löschen?';
  jqsaAdmin.JQSweetAlertOptions.AlertType := jqsatWarning;
  jqsaAdmin.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  jqsaAdmin.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaAdmin.JQSweetAlertOptions.OnBtnClick.OnEvent := rechsgrundlageLoeschen;
  jqsaAdmin.Show;
end;

procedure TFormAdministrator.rechsgrundlageLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  id: Integer;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  id := dm_main.qu_rechtsgrundlagen.FieldByName('Id').AsInteger;

  query := dm_main.qu_rechtsgrundlagen_loeschen;
  query.close;
  query.ParamByName('Id').AsInteger := id;
  query.Execute;

  rechtsgrundlagenAbfragen;
end;

{ *******************************************************************************
  ************************Bkbtypen-Rechtsgrundlagen-Tab***************************
  ******************************************************************************* }

procedure TFormAdministrator.jqbBkbRechtsgrundlageAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  bkbRechtsgrundlagenAbfragen;
end;

procedure TFormAdministrator.bkbRechtsgrundlagenAbfragen;
var
  query: TFDQuery;
begin
  query := dm_main.qu_bkb_rechtsgrundlage;
  query.close;
  query.Active := true;
end;

procedure TFormAdministrator.jqbBkbRechtsgrundlageNeuOnClick(Sender: TObject;
  AParams: TStringList);
begin
  dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage.close;
  dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage.Prepare;
  dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage.Active := true;
  dm_main.qu_bkb_rechtsgrundlage_bkb.close;
  dm_main.qu_bkb_rechtsgrundlage_bkb.Prepare;
  dm_main.qu_bkb_rechtsgrundlage_bkb.Active := true;

  with iwmAdmin do
  begin
    Title := 'Neue Bkbtyp-Rechtsgrundlage';
    ContentElement := iwrBkbRechtsgrundlagenModal;
    Buttons.CommaText := '&Erstellen,&Abbrechen';
    OnAsyncClick := bkbRechtsgrundlageNeu;
    Show;
  end;
end;

procedure TFormAdministrator.bkbRechtsgrundlageNeu(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
  id: Integer;
  bkbtyp: String;
begin
  if iwmAdmin.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  // Exit wenn nicht in beiden Grids eine Reihe ausgewählt wurde
  if not(moveQueryToRow(dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage,
    jqgBkbRechtsgrundlageRechtsgrundlage))
    or not(moveQueryToRow(dm_main.qu_bkb_rechtsgrundlage_bkb,
    jqgBkbRechtsgrundlageBkb)) then
  begin
    Alert.Error('Es muss ein Bkbtyp und eine Rechtsgrundlage ausgewählt sein.');
    Abort;
  end;
  // Bekomme die IDs der ausgewählten Reihen
  id := dm_main.qu_bkb_rechtsgrundlage_rechtsgrundlage.FieldByName('Id').AsInteger;
  bkbtyp := dm_main.qu_bkb_rechtsgrundlage_bkb.FieldByName('Bkbtyp').AsString;

  query := dm_main.qu_bkb_rechtsgrundlage_neu;
  query.close;
  query.ParamByName('id_rechtsgrundlage').AsInteger := id;
  query.ParamByName('bkbtyp').AsString := bkbtyp;
  query.Execute;

  bkbRechtsgrundlagenAbfragen;
end;

procedure TFormAdministrator.jqbBkbRechtsgrundlageLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(dm_main.qu_bkb_rechtsgrundlage, jqgBkbRechtsgrundlage) then
    Exit;

  jqsaAdmin.JQSweetAlertOptions.Title := 'Wollen Sie die Verbindung zwischen ' +
    'Bkbtyp und Rechtsgrundlage wirklich löschen?';
  jqsaAdmin.JQSweetAlertOptions.AlertType := jqsatWarning;
  jqsaAdmin.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  jqsaAdmin.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  jqsaAdmin.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaAdmin.JQSweetAlertOptions.OnBtnClick.OnEvent := bkbRechsgrundlageLoeschen;
  jqsaAdmin.Show;
end;

procedure TFormAdministrator.bkbRechsgrundlageLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  id: Integer;
  bkbtyp: String;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  id := dm_main.qu_bkb_rechtsgrundlage.FieldByName('Id').AsInteger;
  bkbtyp := dm_main.qu_bkb_rechtsgrundlage.FieldByName('Bkbtyp').AsString;

  query := dm_main.qu_bkb_rechtsgrundlage_loeschen;
  query.close;
  query.ParamByName('Id').AsInteger := id;
  query.ParamByName('Bkbtyp').AsString := bkbtyp;
  query.Execute;

  bkbRechtsgrundlagenAbfragen;
end;

{ *******************************************************************************
  ***********************************Frames***************************************
  ******************************************************************************* }

procedure TFormAdministrator.rollenShow;
begin
  RollenFrame.rollenSuchen;
end;

procedure TFormAdministrator.rollenBkbtypShow;
begin
  RollenBkbtypFrame.rollenBkbAbfragen;
end;

procedure TFormAdministrator.personenPrefixShow;
begin
  personenpraefixFrame.personenpraefixAbfragen;
end;

procedure TFormAdministrator.bundeslaenderShow;
begin
  BundeslaenderFrame.bundeslaenderAbfragen;
end;

procedure TFormAdministrator.gemeindenShow;
begin
  GemeindenFrame.gemeindenAbfragen;
end;

procedure TFormAdministrator.kontrolltypenShow;
begin
  KontrolltypenFrame.kontrolltypenAbfragen;
end;

procedure TFormAdministrator.OnChangeData(Sender: TObject; AParams: TStringList);
begin
  //
end;

procedure TFormAdministrator.bkbtypenShow;
begin
  BkbtypenFrame.bkbtypenAbfragen;
end;

procedure TFormAdministrator.KommunikationsartenShow;
begin
  KommunikationsartenFrame.KommunikationsartenAbfragen;
end;

procedure TFormAdministrator.KommunikationswegeShow;
begin
  KommunikationswegeFrame.KommunikationswegeAbfragen;
end;

procedure TFormAdministrator.BetriebeKommunikationswegeShow;
begin
  BetriebeKommunikationswegeFrame.BetriebeKommunikationswegeAbfragen;
end;

end.
