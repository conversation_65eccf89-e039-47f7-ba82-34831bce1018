﻿unit PersonenprefixFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQDatePicker, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompEdit, IWCGJQControl, IWCGJQDialog,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp,
  IWCGJQSweetAlert, IWCGJQDateTimePicker, IWCGJQTimePicker, IWCompTimeEdit,
  IWCGJQButton, IWCGJQEdit, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQGrid, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TPersonenpraefix = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqdPersonenpraefix: TIWCGJQDialog;
    jqbNeu: TIWCGJQButton;
    jqePraefix: TIWCGJQEdit;
    jqgPersonenpraefix: TIWCGJQGrid;
    jqdpPersonenpraefix: TIWCGJQGridDataSetProvider;
    quPersonenpraefix: TFDQuery;
    jqbAbfragen: TIWCGJQButton;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    quNeu: TFDQuery;
    quLoeschen: TFDQuery;
    jqbAendern: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    quAendern: TFDQuery;
    quPersonenpraefixPRAEFIX: TStringField;
    quPersonenpraefixBEZEICHNUNG: TStringField;
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlert: TIWCGJQSweetAlert;

    property Alert: TIWCGJQSweetAlert read FAlert;
    procedure jqdNeuErstellenEvent(Sender: TObject; AURLParams: TStringList);
    procedure jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure personenpraefixLoeschen(Sender: TObject; AParams: TStringList);
    procedure clearModal;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean); reintroduce;
    procedure personenpraefixAbfragen;
  end;

implementation

uses dmmain, Utility;

{$R *.dfm}

constructor TPersonenpraefix.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbAendern.Visible := false;
    jqbLoeschen.Visible := false;
  end;
end;

procedure TPersonenpraefix.jqbAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  personenpraefixAbfragen;
end;

procedure TPersonenpraefix.personenpraefixAbfragen;
begin
  RefreshQuery(quPersonenpraefix);
end;

procedure TPersonenpraefix.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  clearModal;
  jqdPersonenpraefix.JQDialogOptions.Title := 'Neuer Personenprefix';
  jqb := jqdPersonenpraefix.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.OnClick.OnEvent := jqdNeuErstellenEvent;
  jqb := jqdPersonenpraefix.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdPersonenpraefix.Visible := true;
end;

procedure TPersonenpraefix.jqdNeuErstellenEvent(Sender: TObject; AURLParams: TStringList);
var
  praefix, bez: String;
begin
  jqdPersonenpraefix.Visible := false;
  praefix := jqePraefix.Text;
  bez := jqeBezeichnung.Text;

  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('praefix').AsString := praefix;
  quNeu.ParamByName('bez').AsString := bez;
  quNeu.Execute;

  personenpraefixAbfragen;
end;

procedure TPersonenpraefix.jqbAendernOnClick(Sender: TObject;
  AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  clearModal;
  jqePraefix.Enabled := false;

  if not moveQueryToRow(quPersonenpraefix, jqgPersonenpraefix) then
    Exit;

  jqePraefix.Text := quPersonenpraefix.FieldByName('Praefix').AsString;
  jqeBezeichnung.Text := quPersonenpraefix.FieldByName('Bezeichnung').AsString;

  jqdPersonenpraefix.JQDialogOptions.Title := 'Personenprefix ändern';
  jqb := jqdPersonenpraefix.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Ändern';
  jqb.OnClick.OnEvent := jqdAendernEvent;
  jqb := jqdPersonenpraefix.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdPersonenpraefix.Visible := true;
end;

procedure TPersonenpraefix.jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
var
  praefix, bez: String;
begin
  jqdPersonenpraefix.Visible := false;
  praefix := jqePraefix.Text;
  bez := jqeBezeichnung.Text;

  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('praefix').AsString := praefix;
  quAendern.ParamByName('bez').AsString := bez;
  quAendern.Execute;

  personenpraefixAbfragen;
end;

procedure TPersonenpraefix.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdPersonenpraefix.Visible := false;
end;

procedure TPersonenpraefix.clearModal;
begin
  jqePraefix.Enabled := true;
  jqePraefix.Text := '';
  jqeBezeichnung.Text := '';
  jqdPersonenpraefix.JQDialogOptions.Buttons.Clear;
end;

procedure TPersonenpraefix.jqbLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(quPersonenpraefix, jqgPersonenpraefix) then
    Exit;
  alert.JQSweetAlertOptions.Title := 'Wollen Sie den Personenpraefix löschen?';
  alert.JQSweetAlertOptions.AlertType := jqsatWarning;
  alert.JQSweetAlertOptions.ShowCancelButton := True;
  alert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  alert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  alert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  alert.JQSweetAlertOptions.OnBtnClick.OnEvent:= personenpraefixLoeschen;
  alert.Show;
end;

procedure TPersonenpraefix.personenpraefixLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  praefix: String;
begin
  isConfirmButton:= StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then begin
    Exit;
  end;
  praefix := quPersonenpraefix.FieldByName('Praefix').AsString;
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('Praefix').AsString := praefix;
  quLoeschen.Execute;
  personenpraefixAbfragen;
end;

end.
