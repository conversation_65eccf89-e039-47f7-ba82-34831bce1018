object RollenBkbtyp: TRollenBkbtyp
  Left = 0
  Top = 0
  Width = 1322
  Height = 628
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 1322
    Height = 628
    RenderInvisibleControls = True
    TabOrder = 5
    Version = '1.0'
    Align = alClient
    object iwrRollenBkbModal: TIWCGJQRegion
      Left = 3
      Top = 67
      Width = 1300
      Height = 502
      Visible = False
      RenderInvisibleControls = True
      TabOrder = 6
      Version = '1.0'
      object iwrRollen: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        TabOrder = 7
        Version = '1.0'
        Align = alLeft
        object jqgRollen: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 649
          Height = 502
          TabOrder = 11
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 448
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpRollen
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
      object iwrBkb: TIWCGJQRegion
        Left = 651
        Top = 0
        Width = 649
        Height = 502
        RenderInvisibleControls = True
        Version = '1.0'
        Align = alRight
        object jqgBkb: TIWCGJQGrid
          Left = 0
          Top = 0
          Width = 649
          Height = 502
          TabOrder = 1
          Version = '1.0'
          Align = alClient
          JQGridOptions.ColModel = <
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bkbtyp'
              Name = 'Bkbtyp'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Sortable = False
              Caption = 'Bkbtyp'
            end
            item
              EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
              EditOptions.CustomElements = <>
              Idx = 'Bezeichnung'
              Name = 'Bezeichnung'
              SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
              Caption = 'Bezeichnung'
            end>
          JQGridOptions.Height = 448
          JQGridOptions.SubGridModel = <>
          JQGridOptions.Width = 647
          JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
          JQGridNav.Add = False
          JQGridNav.Del = False
          JQGridNav.Edit = False
          JQGridNav.Refresh = False
          JQGridNav.Search = False
          JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
          JQGridCustomButtons = <>
          JQGridProvider = jqdpBkb
          JQGridToolbarSearch.DefaultSearch = gsoContains
          JQDragAndDropOptions.ConnectWith = <>
        end
      end
    end
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1322
      Height = 60
      RenderInvisibleControls = True
      TabOrder = 8
      Version = '1.0'
      Align = alTop
      object jqbAbfragen: TIWCGJQButton
        Left = 32
        Top = 18
        Width = 100
        Height = 21
        TabOrder = 9
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbRollenBkbAfragenOnClick
      end
      object jqbNeu: TIWCGJQButton
        Left = 192
        Top = 18
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbNeuOnClick
      end
      object jqbLoeschen: TIWCGJQButton
        Left = 344
        Top = 18
        Width = 100
        Height = 21
        TabOrder = 4
        Version = '1.0'
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = jqbLoeschenOnClick
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 60
      Width = 1322
      Height = 568
      RenderInvisibleControls = True
      TabOrder = 10
      Version = '1.0'
      Align = alClient
      object jqgRollenBkb: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 1322
        Height = 568
        TabOrder = 2
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bkbtyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bezeichnung'
            Name = 'Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Rollenbezeichnung'
          end>
        JQGridOptions.Height = 514
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1320
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpRollenBkb
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
  end
  object jqdpRollenBkb: TIWCGJQGridDataSetProvider
    DataSet = quRollenBkb
    KeyFields = 'Bkbtyp,ID_Rollen'
    Left = 944
    Top = 8
  end
  object quRollenBkb: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT rb.Bkbtyp, rb.Id_Rollen, r.Bezeichnung'
      'FROM   Stammdaten.Rollen_Bkbtypen rb'
      
        '        INNER JOIN Systemstammdaten.Rollen r ON rb.Id_Rollen = r' +
        '.Id;')
    Left = 872
    Top = 8
    object quRollenBkbBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Origin = 'Bkbtyp'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object quRollenBkbId_Rollen: TIntegerField
      FieldName = 'Id_Rollen'
      Origin = 'Id_Rollen'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object quRollenBkbBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Required = True
      Size = 50
    end
  end
  object quRollenBkbLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Rollen_Bkbtypen'
      'WHERE       ID_Rollen = :id_rollen AND Bkbtyp = :bkbtyp;')
    Left = 785
    Top = 9
    ParamData = <
      item
        Name = 'ID_ROLLEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quRollenBkbExistiert: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Count(Id) AS "Anzahl"'
      'FROM   Stammdaten.Rollen_Bkbtypen'
      'WHERE  Bkbtyp = :bkbtyp AND Id_Rollen = :id_rollen;')
    Left = 681
    Top = 9
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_ROLLEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object jqdpRollen: TIWCGJQGridDataSetProvider
    DataSet = quRollen
    KeyFields = 'ID'
    Left = 1009
    Top = 9
  end
  object jqdpBkb: TIWCGJQGridDataSetProvider
    DataSet = quBkb
    KeyFields = 'Bkbtyp'
    Left = 1065
    Top = 9
  end
  object quRollen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT ID, Bezeichnung'
      'FROM   Systemstammdaten.Rollen;')
    Left = 553
    Top = 9
    object quRollenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quRollenBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Required = True
      Size = 50
    end
  end
  object quBkb: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT Bkbtyp, Bezeichnung'
      'FROM   Stammdaten.Bkbtypen;')
    Left = 609
    Top = 9
    object quBkbBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Origin = 'Bkbtyp'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object quBkbBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Required = True
      Size = 50
    end
  end
  object quRollenBkbNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Stammdaten.Rollen_Bkbtypen (id_rollen, bkbtyp)'
      'VALUES      (:id_rollen, :bkbtyp);')
    Left = 489
    Top = 9
    ParamData = <
      item
        Name = 'ID_ROLLEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
end
