object Personenpraefix: TPersonenpraefix
  Left = 0
  Top = 0
  Width = 1044
  Height = 654
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 1044
    Height = 654
    RenderInvisibleControls = True
    TabOrder = 10
    Version = '1.0'
    Align = alClient
    BorderOptions.Style = cbsSolid
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 60
      Width = 1044
      Height = 594
      RenderInvisibleControls = True
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqgPersonenpraefix: TIWCGJQGrid
        Left = 1
        Top = 1
        Width = 1042
        Height = 592
        TabOrder = 5
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PRAEFIX'
            Name = 'PRAEFIX'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Praefix'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bezeichnung'
          end>
        JQGridOptions.Height = 538
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1040
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpPersonenpraefix
        JQSubGridProvider = jqdpPersonenpraefix
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1044
      Height = 60
      RenderInvisibleControls = True
      TabOrder = 1
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqbNeu: TIWCGJQButton
        Left = 152
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbNeuOnClick
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 32
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 6
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbAbfragenOnClick
      end
      object jqbAendern: TIWCGJQButton
        Left = 272
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 8
        Version = '1.0'
        JQButtonOptions.Label_ = #196'ndern'
        JQButtonOptions.OnClick.OnEvent = jqbAendernOnClick
      end
      object jqbLoeschen: TIWCGJQButton
        Left = 392
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 9
        Version = '1.0'
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = jqbLoeschenOnClick
      end
    end
    object jqdPersonenpraefix: TIWCGJQDialog
      Left = 488
      Top = 176
      Width = 350
      Height = 185
      Visible = False
      TabOrder = 2
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.CloseOnEscape = False
      JQDialogOptions.Height = 185
      JQDialogOptions.Modal = True
      JQDialogOptions.Resizable = False
      JQDialogOptions.Width = 350
      JQDialogOptions.zIndex = 5000
      JQDialogOptions.ShowCloseIcon = False
      object IWLabel1: TIWCGJQLabel
        Left = 20
        Top = 24
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Pr'#228'fix:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 20
        Top = 64
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel2'
        Caption = 'Bezeichnung:'
      end
      object jqePraefix: TIWCGJQEdit
        Left = 120
        Top = 24
        Width = 200
        Height = 21
        TabOrder = 4
        Version = '1.0'
        ZIndex = 5001
        MaxLength = 2
        ScriptEvents = <>
        Text = ''
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 120
        Top = 64
        Width = 200
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  object jqdpPersonenpraefix: TIWCGJQGridDataSetProvider
    DataSet = quPersonenpraefix
    KeyFields = 'PRAEFIX'
    Left = 976
    Top = 8
  end
  object quPersonenpraefix: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Personenpraefix;')
    Left = 864
    Top = 8
    object quPersonenpraefixPRAEFIX: TStringField
      FieldName = 'PRAEFIX'
      Origin = 'PRAEFIX'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 2
    end
    object quPersonenpraefixBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Size = 50
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.Personenpraefix (Praefix, Bezeichnu' +
        'ng)'
      'VALUES      (:praefix, :bez);')
    Left = 792
    Top = 8
    ParamData = <
      item
        Name = 'PRAEFIX'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Systemstammdaten.Personenpraefix'
      'WHERE       Praefix = :praefix;')
    Left = 736
    Top = 8
    ParamData = <
      item
        Name = 'PRAEFIX'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Personenpraefix'
      'SET    Bezeichnung = :bez'
      'WHERE  Praefix = :praefix;')
    Left = 672
    Top = 8
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PRAEFIX'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
end
