﻿unit GruppenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWApplication, IWCGJQEdit, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TGruppenF = class(TCRUDGrid)
    quGruppen: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    quLoeschen: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    IWLabel3: TIWCGJQLabel;
    jqeMuttergruppe: TIWCGJQEdit;
    IWLabel4: TIWCGJQLabel;
    jqeIDHauptver: TIWCGJQEdit;
    IWLabel5: TIWCGJQLabel;
    jqeIdStellver: TIWCGJQEdit;
    IWLabel6: TIWCGJQLabel;
    jqeOkz: TIWCGJQEdit;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
    procedure LoeschenBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure GruppenAbfragen;
    procedure BearbeitenErlauben;
  end;

var
  GruppenF: TGruppenF;

implementation

uses dmmain, Utility;

{$R *.dfm}

constructor TGruppenF.Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, alert);

  EnableAbfragen(quGruppen, GruppenAbfragen);
  if bearbeitbar then
  begin
    BearbeitenErlauben;
  end;
end;

procedure TGruppenF.GruppenAbfragen;
begin
  RefreshQuery(quGruppen);
end;

procedure TGruppenF.BearbeitenErlauben;
begin
  EnableNeu('Neuen Gruppe erstellen', Nil, NeuBestaetigt);
  EnableAendern('Gruppe ändern', InitAendern, AendernBestaetigt);
  EnableLoeschen('Gruppe löschen', LoeschenBestaetigt);
end;

procedure TGruppenF.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  if jqeMuttergruppe.Text.IsEmpty then
    quNeu.ParamByName('muttergruppe').Clear
  else
    quNeu.ParamByName('muttergruppe').AsInteger := StrToInt(jqeMuttergruppe.Text);

  quNeu.ParamByName('id_user_hauptver').AsInteger := StrToInt(jqeIDHauptver.Text);

  if jqeIdStellver.Text.IsEmpty then
    quNeu.ParamByName('id_user_stellver').Clear
  else
    quNeu.ParamByName('id_user_stellver').AsInteger := StrToInt(jqeIdStellver.Text);
  quNeu.ParamByName('okz').AsString := jqeOkz.Text;
  quNeu.Execute;
end;

procedure TGruppenF.InitAendern;
begin
  jqeBezeichnung.Text := quGruppen.FieldByName('bezeichnung').AsString;
  jqeMuttergruppe.Text := quGruppen.FieldByName('muttergruppe').AsString;
  jqeIDHauptver.Text := quGruppen.FieldByName('id_user_hauptver').AsString;
  jqeIdStellver.Text := quGruppen.FieldByName('id_user_stellver').AsString;
  jqeOkz.Text := quGruppen.FieldByName('okz').AsString;
end;

procedure TGruppenF.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.ParamByName('id').AsInteger := quGruppen.FieldByName('id').AsInteger;
  quAendern.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  if jqeMuttergruppe.Text.IsEmpty then
    quAendern.ParamByName('muttergruppe').Clear
  else
    quAendern.ParamByName('muttergruppe').AsInteger := StrToInt(jqeMuttergruppe.Text);

  quAendern.ParamByName('id_user_hauptver').AsInteger := StrToInt(jqeIDHauptver.Text);

  if jqeIdStellver.Text.IsEmpty then
    quAendern.ParamByName('id_user_stellver').Clear
  else
    quAendern.ParamByName('id_user_stellver').AsInteger := StrToInt(jqeIdStellver.Text);
  quAendern.ParamByName('okz').AsString := jqeOkz.Text;
  quAendern.Execute;
end;

procedure TGruppenF.LoeschenBestaetigt;
begin
  quLoeschen.Close;
  quLoeschen.ParamByName('id').AsInteger := quGruppen.FieldByName('id').AsInteger;
  quLoeschen.Execute;
end;

procedure TGruppenF.ResetModal;
begin
  jqeBezeichnung.Text := '';
  jqeMuttergruppe.Text := '';
  jqeIDHauptver.Text := '';
  jqeIdStellver.Text := '';
  jqeOkz.Text := '';
end;

end.
