object Bundeslaender: TBundeslaender
  Left = 0
  Top = 0
  Width = 930
  Height = 521
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 930
    Height = 521
    RenderInvisibleControls = True
    TabOrder = 9
    Version = '1.0'
    Align = alClient
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 930
      Height = 60
      RenderInvisibleControls = True
      TabOrder = 10
      Version = '1.0'
      Align = alTop
      object jqbNeu: TIWCGJQButton
        Left = 152
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 11
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbNeuOnClick
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 32
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 13
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbAbfragenOnClick
      end
      object jqbAendern: TIWCGJQButton
        Left = 272
        Top = 23
        Width = 100
        Height = 21
        TabOrder = 14
        Version = '1.0'
        JQButtonOptions.Label_ = #196'ndern'
        JQButtonOptions.OnClick.OnEvent = jqbAendernOnClick
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 60
      Width = 930
      Height = 461
      RenderInvisibleControls = True
      TabOrder = 12
      Version = '1.0'
      Align = alClient
      object jqgBundeslaender: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 930
        Height = 461
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BLDCODE'
            Name = 'BLDCODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bldcode'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KURZTEXT'
            Name = 'KURZTEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kurztext'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'REGION'
            Name = 'REGION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Region'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LANDKZ'
            Name = 'LANDKZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Landkz'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'OKZ'
            Name = 'OKZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'OKZ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gfcImage
            Idx = 'BLDLOGO'
            Name = 'BLDLOGO'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bldlogo'
          end>
        JQGridOptions.Height = 407
        JQGridOptions.RowNum = 40
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 928
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpBundeslaender
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object jqdBundeslaender: TIWCGJQDialog
      Left = 273
      Top = 96
      Width = 368
      Height = 337
      Visible = False
      TabOrder = 1
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 337
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 368
      JQDialogOptions.zIndex = 5000
      JQDialogOptions.ShowCloseIcon = False
      object IWLabel1: TIWCGJQLabel
        Left = 20
        Top = 15
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bldcode:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 20
        Top = 42
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 20
        Top = 69
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kurztext:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 20
        Top = 96
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Region:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 20
        Top = 123
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Landkz:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 20
        Top = 150
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Okz:'
      end
      object IWLabel7: TIWCGJQLabel
        Left = 20
        Top = 177
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bldlogo:'
      end
      object jqeBldcode: TIWCGJQEdit
        Left = 120
        Top = 15
        Width = 200
        Height = 21
        TabOrder = 2
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 120
        Top = 42
        Width = 200
        Height = 21
        TabOrder = 3
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeKurztext: TIWCGJQEdit
        Left = 120
        Top = 69
        Width = 200
        Height = 21
        TabOrder = 4
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeRegion: TIWCGJQEdit
        Left = 120
        Top = 96
        Width = 200
        Height = 21
        TabOrder = 5
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeLandkz: TIWCGJQEdit
        Left = 120
        Top = 123
        Width = 200
        Height = 21
        TabOrder = 6
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeOkz: TIWCGJQEdit
        Left = 120
        Top = 150
        Width = 200
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jquBldlogo: TIWCGJQFileUpload
        Left = 120
        Top = 177
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        JQFileUploadOptions.AllowedExtensions.Strings = (
          'jpg'
          'png')
        JQFileUploadOptions.Classes.Button = 'ui-button'
        JQFileUploadOptions.Classes.List = 'cg-qq-upload-list'
        JQFileUploadOptions.Classes.Success = 'ui-state-highlight'
        JQFileUploadOptions.Classes.Fail = 'ui-state-error'
        JQFileUploadOptions.UploadButtonText = 'Bild hochladen'
      end
    end
  end
  object jqdpBundeslaender: TIWCGJQGridDataSetProvider
    DataSet = quBundeslaender
    KeyFields = 'BLDCODE'
    Left = 832
    Top = 8
  end
  object quBundeslaender: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Bundeslaender;')
    Left = 737
    Top = 9
    object quBundeslaenderBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object quBundeslaenderBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 30
    end
    object quBundeslaenderKURZTEXT: TStringField
      FieldName = 'KURZTEXT'
      Origin = 'KURZTEXT'
      Required = True
      Size = 2
    end
    object quBundeslaenderREGION: TSmallintField
      FieldName = 'REGION'
      Origin = 'REGION'
      Required = True
    end
    object quBundeslaenderLANDKZ: TStringField
      FieldName = 'LANDKZ'
      Origin = 'LANDKZ'
      Required = True
      FixedChar = True
      Size = 2
    end
    object quBundeslaenderBLDLOGO: TBlobField
      FieldName = 'BLDLOGO'
      Origin = 'BLDLOGO'
    end
    object quBundeslaenderOKZ: TStringField
      FieldName = 'OKZ'
      Origin = 'OKZ'
      Required = True
      Size = 50
    end
    object quBundeslaenderBKBKZ: TStringField
      FieldName = 'BKBKZ'
      Origin = 'BKBKZ'
      Required = True
      FixedChar = True
      Size = 1
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Systemstammdaten.Bundeslaender (Bldcode, Bezeichnung' +
        ', Kurztext, Region, Landkz, Okz, Bldlogo)'
      
        'VALUES      (:Bldcode, :Bezeichnung, :Kurztext, :Region, :Landkz' +
        ', :Okz, :Bldlogo)')
    Left = 673
    Top = 9
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KURZTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'REGION'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LANDKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDLOGO'
        DataType = ftGraphic
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Bundeslaender'
      'SET    Okz = :okz, Kurztext = :kurztext'
      'WHERE  Bldcode = :bldcode;')
    Left = 625
    Top = 9
    ParamData = <
      item
        Name = 'OKZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KURZTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendereLogo: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Systemstammdaten.Bundeslaender'
      'SET    Bldlogo = :bldlogo'
      'WHERE  Bldcode = :bldcode;')
    Left = 553
    Top = 9
    ParamData = <
      item
        Name = 'BLDLOGO'
        DataType = ftGraphic
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
end
