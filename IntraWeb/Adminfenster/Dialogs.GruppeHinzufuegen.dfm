inherited DialogGruppeHinzufuegen: TDialogGruppeHinzufuegen
  Width = 557
  Height = 287
  OnCreate = IWCGJQFrameCreate
  ExplicitWidth = 557
  ExplicitHeight = 287
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 557
    Height = 287
    TabOrder = 3
    JQDialogOptions.Height = 287
    JQDialogOptions.Width = 557
    ExplicitWidth = 557
    ExplicitHeight = 287
    inherited RegionContent: TIWCGJQRegion
      Width = 557
      Height = 227
      TabOrder = 4
      ExplicitWidth = 557
      ExplicitHeight = 227
      object Email: TIWCGJQLabel
        Left = 16
        Top = 182
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'Email'
        Caption = 'Email:'
      end
      object LabelBezeichnung: TIWCGJQLabel
        Left = 16
        Top = 16
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelBezeichnung'
        Caption = 'Bezeichnung:'
      end
      object LabelHauptverantwortlicher: TIWCGJQLabel
        Left = 16
        Top = 80
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Hauptverantwortlicher:'
      end
      object LabelMuttergruppe: TIWCGJQLabel
        Left = 16
        Top = 48
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Muttergruppe:'
      end
      object LabelOrgKennzeichen: TIWCGJQLabel
        Left = 16
        Top = 148
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Org Kennzeichen:'
      end
      object LabelStellvertreter: TIWCGJQLabel
        Left = 16
        Top = 114
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWCGJQLabel1'
        Caption = 'Stellvertreter:'
      end
      object EditBezeichnung: TIWCGJQEdit
        Left = 184
        Top = 16
        Width = 350
        Height = 21
        TabOrder = 6
        Version = '1.0'
        ZIndex = 2222
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'BEZEICHNUNG'
        Enabled = False
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangeData
      end
      object EditEmail: TIWCGJQEdit
        Left = 184
        Top = 182
        Width = 350
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 2222
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'EMAIL'
        Enabled = False
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangeData
      end
      object EditOKZ: TIWCGJQEdit
        Left = 184
        Top = 148
        Width = 350
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 2222
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'OKZ'
        Enabled = False
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangeData
      end
      object DropdownMuttergruppe: TIWCGJQDropDown
        Left = 184
        Top = 48
        Width = 350
        Height = 25
        TabOrder = 9
        Version = '1.0'
        Enabled = False
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'MUTTERGRUPPE'
        DataLink.ListDataSource = DSGruppenSuche
        DataLink.ListFieldNames = 'Bezeichnung'
        DataLink.ListSelectFieldName = 'ID'
        DataLink.ListLookupResultFieldName = 'Bezeichnung'
        JQDropDownOptions.Ajax.QuietMillis = 100
        JQDropDownOptions.Ajax.Use = True
        JQDropDownOptions.OnChange.OnEvent = OnChangeData
        JQDropDownOptions.AttachTo = jqddatInput
        JQDropDownOptions.InfiniteScroll = True
        JQDropDownOptions.NoMatchesMsg = 'Keine Treffer'
        JQDropDownOptions.InputTooShortMsg = 'Bitte noch %d Zeichen mehr eingeben ...'
        Groups = <>
        Items = <>
      end
      object DropdownHauptverantwortlicher: TIWCGJQDropDown
        Left = 184
        Top = 79
        Width = 350
        Height = 25
        TabOrder = 10
        Version = '1.0'
        Enabled = False
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'ID_USER_HAUPTVER'
        DataLink.ListDataSource = DSUserSuche
        DataLink.ListFieldNames = 'USERNAME'
        DataLink.ListSelectFieldName = 'ID'
        DataLink.ListLookupResultFieldName = 'USERNAME'
        JQDropDownOptions.Ajax.QuietMillis = 100
        JQDropDownOptions.Ajax.Use = True
        JQDropDownOptions.OnChange.OnEvent = OnChangeData
        JQDropDownOptions.AttachTo = jqddatInput
        JQDropDownOptions.InfiniteScroll = True
        JQDropDownOptions.NoMatchesMsg = 'Keine Treffer'
        JQDropDownOptions.InputTooShortMsg = 'Bitte noch %d Zeichen mehr eingeben ...'
        Groups = <>
        Items = <>
      end
      object DropdownStellvertreter: TIWCGJQDropDown
        Left = 184
        Top = 110
        Width = 350
        Height = 25
        TabOrder = 11
        Version = '1.0'
        Enabled = False
        DataLink.DataSource = DSGruppen
        DataLink.FieldName = 'ID_USER_STELLVER'
        DataLink.ListDataSource = DSUserSuche
        DataLink.ListFieldNames = 'USERNAME'
        DataLink.ListSelectFieldName = 'ID'
        DataLink.ListLookupResultFieldName = 'USERNAME'
        JQDropDownOptions.Ajax.QuietMillis = 100
        JQDropDownOptions.Ajax.Use = True
        JQDropDownOptions.OnChange.OnEvent = OnChangeData
        JQDropDownOptions.AttachTo = jqddatInput
        JQDropDownOptions.InfiniteScroll = True
        JQDropDownOptions.NoMatchesMsg = 'Keine Treffer'
        JQDropDownOptions.InputTooShortMsg = 'Bitte noch %d Zeichen mehr eingeben ...'
        Groups = <>
        Items = <>
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 237
      Width = 555
      TabOrder = 2
      ExplicitTop = 237
      ExplicitWidth = 555
      inherited ButtonCancel: TIWCGJQButton
        Left = 447
        TabOrder = 0
        ExplicitLeft = 447
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 439
        ExplicitWidth = 439
        inherited ButtonOK: TIWCGJQButton
          Left = 331
          TabOrder = 5
          ExplicitLeft = 331
        end
      end
    end
  end
  object DSGruppenSuche: TDataSource
    DataSet = DMAdmin.QGruppensuche
    Left = 32
    Top = 240
  end
  object DSUserSuche: TDataSource
    DataSet = DMAdmin.QUserSuche
    Left = 112
    Top = 240
  end
  object DSGruppen: TDataSource
    DataSet = DMAdmin.QGruppen
    Left = 32
    Top = 176
  end
end
