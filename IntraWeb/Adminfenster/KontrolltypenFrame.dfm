object Kontrolltypen: TKontrolltypen
  Left = 0
  Top = 0
  Width = 1176
  Height = 709
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 1176
    Height = 709
    RenderInvisibleControls = True
    TabOrder = 1
    Version = '1.0'
    Align = alClient
    BorderOptions.Style = cbsSolid
    object iwrRollenTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1176
      Height = 75
      RenderInvisibleControls = True
      TabOrder = 2
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqbAbfragen: TIWCGJQButton
        Left = 32
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 4
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbAbfragenOnClick
      end
      object jqbNeu: TIWCGJQButton
        Left = 150
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 9
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbNeuOnClick
      end
      object jqbAendern: TIWCGJQButton
        Left = 269
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 13
        Version = '1.0'
        JQButtonOptions.Label_ = #196'ndern'
        JQButtonOptions.OnClick.OnEvent = jqbAendernOnClick
      end
      object jqbLoeschen: TIWCGJQButton
        Left = 392
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 15
        Version = '1.0'
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = jqbLoeschenOnClick
      end
    end
    object iwrRollenMid: TIWCGJQRegion
      Left = 0
      Top = 75
      Width = 1176
      Height = 634
      RenderInvisibleControls = True
      TabOrder = 5
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqgKontrolltypen: TIWCGJQGrid
        Left = 1
        Top = 1
        Width = 1174
        Height = 632
        TabOrder = 16
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKBTYP'
            Name = 'BKBTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bkbtyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KONTROLLTYP'
            Name = 'KONTROLLTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kontrolltyp'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'REFBKB'
            Name = 'REFBKB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Refbkb'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'FREIE_ADRESSE'
            Name = 'FREIE_ADRESSE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Freie Adresse'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'SICHTBAR'
            Name = 'SICHTBAR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'SICHTBAR'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'PROBEN'
            Name = 'PROBEN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'PROBEN'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'OERTLICHKEITEN'
            Name = 'OERTLICHKEITEN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'OERTLICHKEITEN'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'AUTO_KONTROLLIERT'
            Name = 'AUTO_KONTROLLIERT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'AUTO_KONTROLLIERT'
          end>
        JQGridOptions.Height = 578
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1172
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpKontrolltypen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object jqdKontrolltypen: TIWCGJQDialog
      Left = 320
      Top = 120
      Width = 481
      Height = 385
      Visible = False
      TabOrder = 17
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.CloseOnEscape = False
      JQDialogOptions.Height = 385
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 481
      JQDialogOptions.zIndex = 5000
      object LabelKontrolltyp: TIWCGJQLabel
        Left = 24
        Top = 51
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelKontrolltyp'
        Caption = 'Kontrolltyp:'
      end
      object LabelBezeichnung: TIWCGJQLabel
        Left = 24
        Top = 78
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Bezeichnung:'
      end
      object LabelRefbkb: TIWCGJQLabel
        Left = 24
        Top = 105
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Refbkb:'
      end
      object LabelFreieAdresse: TIWCGJQLabel
        Left = 24
        Top = 132
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Freie Adresse:'
      end
      object LabelBkb: TIWCGJQLabel
        Left = 24
        Top = 24
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelBkb'
        Caption = 'Bkbtyp:'
      end
      object LabelSichtbar: TIWCGJQLabel
        Left = 24
        Top = 159
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelSichtbar'
        Caption = 'Sichtbar:'
      end
      object LabelProben: TIWCGJQLabel
        Left = 24
        Top = 186
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelProben'
        Caption = 'Proben:'
      end
      object LabelOertlichkeiten: TIWCGJQLabel
        Left = 24
        Top = 213
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelOertlichkeiten'
        Caption = #214'rtlichkeiten:'
      end
      object LabelAutoKontrolliert: TIWCGJQLabel
        Left = 3
        Top = 240
        Width = 106
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'LabelAutoKontrolliert'
        Caption = 'Auto Kontrolliert:'
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 115
        Top = 78
        Width = 198
        Height = 21
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangedData
        JQEvents.OnChange.OnEvent = OnChangedData
      end
      object CheckFreieAdresse: TIWCGJQCheckBoxEx
        Left = 115
        Top = 132
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        Caption = ''
      end
      object EditKontrolltyp: TIWCGJQEdit
        Left = 115
        Top = 51
        Width = 198
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        MaxLength = 3
        ScriptEvents = <>
        Text = ''
        JQEvents.OnKeyUp.OnEvent = OnChangedData
        JQEvents.OnChange.OnEvent = OnChangedData
      end
      object DropBkb: TIWCGJQDropDown
        Left = 115
        Top = 20
        Width = 198
        Height = 25
        TabOrder = 6
        Version = '1.0'
        Groups = <>
        Items = <>
      end
      object CheckRefbkb: TIWCGJQCheckBoxEx
        Left = 115
        Top = 105
        Width = 100
        Height = 21
        TabOrder = 8
        Version = '1.0'
        Caption = ''
      end
      object CheckProben: TIWCGJQCheckBoxEx
        Left = 115
        Top = 186
        Width = 100
        Height = 21
        TabOrder = 10
        Version = '1.0'
        Caption = ''
      end
      object CheckOertlichkeit: TIWCGJQCheckBoxEx
        Left = 115
        Top = 213
        Width = 100
        Height = 21
        TabOrder = 11
        Version = '1.0'
        Caption = ''
      end
      object CheckAutoK: TIWCGJQCheckBoxEx
        Left = 115
        Top = 240
        Width = 100
        Height = 21
        TabOrder = 12
        Version = '1.0'
        Caption = ''
      end
      object CheckSichtbar: TIWCGJQCheckBoxEx
        Left = 115
        Top = 159
        Width = 100
        Height = 21
        TabOrder = 14
        Version = '1.0'
        Caption = ''
      end
    end
  end
  object jqdpKontrolltypen: TIWCGJQGridDataSetProvider
    DataSet = quKontrolltypen
    KeyFields = 'BKBTYP,Kontrolltyp'
    Left = 536
    Top = 16
  end
  object quKontrolltypen: TFDQuery
    FilterOptions = [foCaseInsensitive]
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Kontrolltypen;')
    Left = 624
    Top = 16
    object quKontrolltypenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object quKontrolltypenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      FixedChar = True
      Size = 3
    end
    object quKontrolltypenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Size = 80
    end
    object quKontrolltypenREFBKB: TSmallintField
      FieldName = 'REFBKB'
      Origin = 'REFBKB'
    end
    object quKontrolltypenFREIE_ADRESSE: TBooleanField
      FieldName = 'FREIE_ADRESSE'
      Origin = 'FREIE_ADRESSE'
      Required = True
    end
    object quKontrolltypenSICHTBAR: TBooleanField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
      Required = True
    end
    object quKontrolltypenPROBEN: TBooleanField
      FieldName = 'PROBEN'
      Origin = 'PROBEN'
      Required = True
    end
    object quKontrolltypenOERTLICHKEITEN: TBooleanField
      FieldName = 'OERTLICHKEITEN'
      Origin = 'OERTLICHKEITEN'
      Required = True
    end
    object quKontrolltypenAUTO_KONTROLLIERT: TBooleanField
      FieldName = 'AUTO_KONTROLLIERT'
      Origin = 'AUTO_KONTROLLIERT'
      Required = True
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Kontrolltypen (Bkbtyp, Kontrolltyp, Bezei' +
        'chnung, Refbkb, Freie_Adresse, Sichtbar, Proben, Oertlichkeiten,' +
        ' Auto_Kontrolliert)'
      
        'VALUES      (:bkbtyp, :kontrolltyp, :bez, :refbkb, :freie_adress' +
        'e, :sichtbar, :proben, :oertlichkeiten, :auto_kontrolliert);')
    Left = 688
    Top = 16
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'REFBKB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FREIE_ADRESSE'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PROBEN'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OERTLICHKEITEN'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUTO_KONTROLLIERT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Kontrolltypen'
      'WHERE       Bkbtyp = :bkbtyp AND Kontrolltyp = :kontrolltyp;')
    Left = 744
    Top = 16
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Kontrolltypen'
      
        'SET    Bezeichnung = :bez, Freie_Adresse = :freie_adresse, Refbk' +
        'b = :refbkb, Sichtbar = :sichtbar, '
      
        '       Proben = :proben, Oertlichkeiten = :oertlichkeiten, Auto_' +
        'Kontrolliert = :auto_kontrolliert '
      'WHERE  Bkbtyp = :bkbtyp AND Kontrolltyp = :kontrolltyp;')
    Left = 808
    Top = 16
    ParamData = <
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FREIE_ADRESSE'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'REFBKB'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PROBEN'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'OERTLICHKEITEN'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUTO_KONTROLLIERT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QBkb: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT [BKBTYP]'
      '  FROM [STAMMDATEN].[BKBTYPEN] '
      '  order by BKBTYP')
    Left = 864
    Top = 16
    object QBkbBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
  end
end
