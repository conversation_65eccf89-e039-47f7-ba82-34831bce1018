﻿unit BetriebeKommunikationswegeFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWApplication,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param,
  FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.Async, FireDAC.DApt, Data.DB, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCGJQEdit, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQRegion, IWCGJQLabel, IW.HTTP.Reply, System.Net.HttpClient;

type
  TBetriebeKommunikationswege = class(TCRUDGrid)
    quBeKw: TFDQuery;
    quNeu: TFDQuery;
    quLoeschen: TFDQuery;
    quAendern: TFDQuery;
    iwrLeft: TIWCGJQRegion;
    iwrRight: TIWCGJQRegion;
    jqgBetriebe: TIWCGJQGrid;
    jqgKommunikationswege: TIWCGJQGrid;
    jqdpBetriebe: TIWCGJQGridDataSetProvider;
    jqdpKommunikationswege: TIWCGJQGridDataSetProvider;
    iwrBetriebSuchen: TIWCGJQRegion;
    iwrKommunikationswege: TIWCGJQRegion;
    quBetriebe: TFDQuery;
    jqeBetriebSuchen: TIWCGJQButton;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqeName: TIWCGJQEdit;
    jqeRegnr: TIWCGJQEdit;
    quKommunikationswege: TFDQuery;
    quBetriebeId: TFDAutoIncField;
    quBetriebeRegnr: TStringField;
    quBetriebeName: TStringField;
    quBetriebeOrt: TWideStringField;
    procedure jqbBetriebSuchenOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure Init;
    procedure LoeschenBestaetigt;
    procedure BetriebeSuchen;
    function KorrektAusgefuellt: boolean;
  protected
    procedure ResetModal; override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent;
      alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure BetriebeKommunikationswegeAbfragen;
  end;

var
  BetriebeKommunikationswege: TBetriebeKommunikationswege;

implementation

uses dmmain, Utility;

{$R *.dfm}


constructor TBetriebeKommunikationswege.Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, alert);

  enableAbfragen(quBeKw, BetriebeKommunikationswegeAbfragen);
  if bearbeitbar then
  begin
    enableNeu('Neue Zuweisung', Init, NeuBestaetigt);
    enableLoeschen('Wollen Sie die Zuweisung wirklich löschen?',
      LoeschenBestaetigt);
  end;
end;

procedure TBetriebeKommunikationswege.BetriebeKommunikationswegeAbfragen;
begin
  RefreshQuery(quBeKw);
end;

procedure TBetriebeKommunikationswege.Init;
begin
  BetriebeSuchen;
  RefreshQuery(quKommunikationswege);
end;

procedure TBetriebeKommunikationswege.BetriebeSuchen;
begin
  quBetriebe.Close;
  quBetriebe.Prepare;
  quBetriebe.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quBetriebe.ParamByName('name').AsString := jqeName.Text;
  quBetriebe.ParamByName('regnr').AsString := jqeRegnr.Text;
  quBetriebe.Active := true;
end;

procedure TBetriebeKommunikationswege.jqbBetriebSuchenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  BetriebeSuchen;
end;

procedure TBetriebeKommunikationswege.NeuBestaetigt;
begin
  if not KorrektAusgefuellt then
    Exit;
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('id_betrieb').AsInteger :=
    quBetriebe.FieldByName('id').AsInteger;
  quNeu.ParamByName('id_kommunikationsweg').AsInteger :=
    quKommunikationswege.FieldByName('id').AsInteger;
  quNeu.Execute;
  quNeu.Close;
end;

procedure TBetriebeKommunikationswege.LoeschenBestaetigt;
begin
  quLoeschen.Close;
  quLoeschen.Prepare;
  quLoeschen.ParamByName('id_betrieb').AsInteger :=
    quBeKw.FieldByName('id_betrieb').AsInteger;
  quLoeschen.ParamByName('id_kommunikationsweg').AsInteger :=
    quBeKw.FieldByName('id_kommunikationsweg').AsInteger;
  quLoeschen.Execute;
  quLoeschen.Close;
end;

function TBetriebeKommunikationswege.KorrektAusgefuellt: boolean;
begin
  Result := moveQueryToRow(quBetriebe, jqgBetriebe) and
    moveQueryToRow(quKommunikationswege, jqgKommunikationswege);
end;

procedure TBetriebeKommunikationswege.ResetModal;
begin
  jqeName.Text := '';
  jqeRegnr.Text := '';
  quBetriebe.Close;
  quKommunikationswege.Close;
end;

end.
