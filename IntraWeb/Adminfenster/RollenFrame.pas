﻿unit RollenFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWCGJQGrid, IWCGJQControl,
  IWCGJQButton, IWHTMLContainer, IWHTML40Container, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweet<PERSON>lert, IWCGJQDialog, IWCGJQEdit,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQFileUpload, IWCGJQDatePicker, IWCompMemo, IWCompEdit, IWCompCheckbox, IWCGJQRegion, IWCGJQLabel;

type
  TRollen = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrRollenMid: TIWCGJQRegion;
    jqgRollen: TIWCGJQGrid;
    iwrRollenTop: TIWCGJQRegion;
    jqbAbfragen: TIWCGJQButton;
    jqbNeu: TIWCGJQButton;
    jqbAendern: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    jqdRollen: TIWCGJQDialog;
    iwcRollenDefaultrolle: TIWCheckBox;
    iwcRollenSichtbar: TIWCheckBox;
    IWLabel11: TIWCGJQLabel;
    IWLabel12: TIWCGJQLabel;
    IWLabel13: TIWCGJQLabel;
    IWLabel14: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    iwmRollenParameter: TIWMemo;
    jqdRollenGueltigAb: TIWCGJQDatePicker;
    jqeRollenBezeichnung: TIWCGJQEdit;
    jqdpRollen: TIWCGJQGridDataSetProvider;
    procedure jqbRollenAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRollenLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRollenAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRollenNeuOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlert: TIWCGJQSweetAlert;
    property Alert: TIWCGJQSweetAlert read FAlert;

    procedure jqsarolleLoeschen(Sender: TObject; AParams: TStringList);
    procedure resetRollenModal;
    procedure rolleAendern(Sender: TObject; AURLParams: TStringList);
    procedure rolleNeu(Sender: TObject; AURLParams: TStringList);
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean); reintroduce;
    procedure rollenSuchen;
  end;

implementation

uses dmmain, Utility;

{$R *.dfm}


constructor TRollen.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbAendern.Visible := false;
    jqbLoeschen.Visible := false;
  end;
end;

procedure TRollen.rollenSuchen;
begin
  RefreshQuery(dm_main.qu_rollen);
end;

procedure TRollen.jqbRollenAbfragenOnClick(Sender: TObject; AParams: TStringList);
begin
  rollenSuchen;
end;

{ Öffnet ein leeres Modalfenster }
procedure TRollen.jqbRollenNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  resetRollenModal;
  jqdRollen.JQDialogOptions.Title := 'Neue Rolle';
  jqb := jqdRollen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.OnClick.OnEvent := rolleNeu;
  jqb := jqdRollen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdRollen.Visible := true;
end;

{ Mit den eingegebenen Daten wird eine neue Rolle in der Db erstellt. }
procedure TRollen.rolleNeu(Sender: TObject; AURLParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_rollen_neu;
  query.Close;
  query.ParamByName('Bez').AsString := jqeRollenBezeichnung.Text;
  query.ParamByName('Default').AsBoolean := iwcRollenDefaultrolle.Checked;
  query.ParamByName('Sichtbar').AsBoolean := iwcRollenSichtbar.Checked;
  query.ParamByName('ab').AsDate := jqdRollenGueltigAb.Date;
  if iwmRollenParameter.Text <> '' then
  begin
    query.ParamByName('Parameter').AsString := iwmRollenParameter.Text;
  end
  else
    query.ParamByName('Parameter').Clear();
  query.Execute;
  jqdRollen.Visible := false;
  rollenSuchen;
end;

{ Füllt die Textfelder im Modalfenster mit den Daten der ausgewählten Rolle und
  öffnet das Modalfenster }
procedure TRollen.jqbRollenAendernOnClick(Sender: TObject; AParams: TStringList);
var
  query: TFDQuery;
  jqb: TIWCGJQCustomDialogButton;
begin
  if not moveQueryToRow(dm_main.qu_rollen, jqgRollen) then
  begin
    Alert.Error('Es muss eine Rolle ausgewählt sein.');
    Abort;
  end;

  resetRollenModal;
  query := dm_main.qu_rollen;
  jqeRollenBezeichnung.Text := query.FieldByName('Bezeichnung').AsString;
  iwcRollenDefaultrolle.Checked := query.FieldByName('Defaultrolle').AsBoolean;
  iwcRollenSichtbar.Checked := query.FieldByName('Sichtbar').AsBoolean;
  jqdRollenGueltigAb.Date := query.FieldByName('Gueltig_ab').AsDateTime;
  iwmRollenParameter.Text := query.FieldByName('Parameter').AsString;

  jqdRollen.JQDialogOptions.Title := 'Rolle ändern';
  jqb := jqdRollen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Ändern';
  jqb.OnClick.OnEvent := rolleAendern;
  jqb := jqdRollen.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdRollen.Visible := true;
end;

{ Die ausgewählte Rolle wird in der DB geändert. Dann wird das Grid neugeladen. }
procedure TRollen.rolleAendern(Sender: TObject; AURLParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_rollen_update;
  query.Close;
  query.ParamByName('Id').AsInteger := dm_main.qu_rollen.FieldByName('ID').AsInteger;
  query.ParamByName('Bez').AsString := jqeRollenBezeichnung.Text;
  query.ParamByName('Default').AsBoolean := iwcRollenDefaultrolle.Checked;
  query.ParamByName('Sichtbar').AsBoolean := iwcRollenSichtbar.Checked;
  query.ParamByName('ab').AsDate := jqdRollenGueltigAb.Date;
  if iwmRollenParameter.Text <> '' then
  begin
    query.ParamByName('Parameter').AsString := iwmRollenParameter.Text;
  end
  else
    query.ParamByName('Parameter').Clear();
  query.Execute;
  jqdRollen.Visible := false;
  rollenSuchen;
end;

{ Setzt die Query auf die ausgewählte Rolle und fordert danach den User auf das
  Löschen mittels eines SweetAlerts zu bestätigen. }
procedure TRollen.jqbRollenLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  if not moveQueryToRow(dm_main.qu_rollen, jqgRollen) then
  begin
    Alert.Error('Es muss eine Rolle ausgewählt sein.');
    Abort;
  end;
  Alert.JQSweetAlertOptions.Title := 'Wollen Sie die Rolle löschen?';
  Alert.JQSweetAlertOptions.AlertType := jqsatWarning;
  Alert.JQSweetAlertOptions.ShowCancelButton := true;
  Alert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  Alert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  Alert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  Alert.JQSweetAlertOptions.OnBtnClick.OnEvent := jqsarolleLoeschen;
  Alert.Show;
end;

{ Nachdem der SweetAlert geschlossen ist, wird nach Abschluss überprüft und
  danach wird die ausgewählte Rolle aus der DB gelöscht. }
procedure TRollen.jqsarolleLoeschen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: boolean;
  id: Integer;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  id := dm_main.qu_rollen.FieldByName('ID').AsInteger;
  query := dm_main.qu_rollen_loeschen;
  query.Close;
  query.ParamByName('Id').AsInteger := id;
  query.Execute;
  rollenSuchen;
end;

procedure TRollen.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdRollen.Visible := false;
end;

procedure TRollen.resetRollenModal;
begin
  jqeRollenBezeichnung.Text;
  iwcRollenDefaultrolle.Checked := false;
  iwcRollenSichtbar.Checked := true;
  iwmRollenParameter.Clear;
  jqdRollenGueltigAb.Date := Now;
  jqdRollen.JQDialogOptions.Buttons.Clear;
end;

end.
