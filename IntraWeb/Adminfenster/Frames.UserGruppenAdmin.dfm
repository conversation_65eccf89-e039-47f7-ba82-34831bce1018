inherited UsergruppenAdmin: TUsergruppenAdmin
  Width = 1209
  Height = 897
  Align = alClient
  ExplicitWidth = 1209
  ExplicitHeight = 897
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1209
    Height = 897
    TabOrder = 2
    ExplicitWidth = 1209
    ExplicitHeight = 897
    object RegionTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 1209
      Height = 400
      TabOrder = 6
      Version = '1.0'
      Align = alTop
      object TreeGruppen: TIWCGJQTreeView
        Left = 0
        Top = 40
        Width = 1209
        Height = 360
        TabOrder = 8
        Version = '1.0'
        Align = alClient
        JQTreeViewOptions.HTMLPlugin.PluginActive = True
        JQTreeViewOptions.ThemesPlugin.PluginActive = True
        TreeNodes = <>
        JQEvents.OnSelect.OnEvent = GruppenOnSelect
      end
      object IWCGJQRegion2: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1209
        Height = 40
        TabOrder = 3
        Version = '1.0'
        Align = alTop
        object IWCGJQLabel1: TIWCGJQLabel
          Left = 16
          Top = 12
          Width = 52
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel1'
          Caption = 'Gruppen'
        end
      end
    end
    object RegionBottom: TIWCGJQRegion
      Left = 0
      Top = 400
      Width = 1209
      Height = 497
      TabOrder = 7
      Version = '1.0'
      Align = alClient
      object RegionButtons: TIWCGJQRegion
        Left = 0
        Top = 424
        Width = 1209
        Height = 73
        TabOrder = 9
        Version = '1.0'
        Align = alBottom
        object IWCGJQRegion1: TIWCGJQRegion
          Left = 792
          Top = 0
          Width = 417
          Height = 73
          TabOrder = 1
          Version = '1.0'
          Align = alRight
          object ButtonSpeichern: TIWCGJQButton
            Left = 290
            Top = 35
            Width = 100
            Height = 21
            TabOrder = 10
            Version = '1.0'
            JQButtonOptions.Label_ = 'Speichern'
            JQButtonOptions.OnClick.Ajax = False
            JQButtonOptions.OnClick.OnEvent = ButtonSpeichernOnClick
          end
          object ButtonAbbrechen: TIWCGJQButton
            Left = 184
            Top = 35
            Width = 100
            Height = 21
            Version = '1.0'
            JQButtonOptions.Label_ = 'Abbrechen'
            JQButtonOptions.OnClick.Ajax = False
            JQButtonOptions.OnClick.OnEvent = ButtonAbbrechenOnClick
          end
        end
      end
      object IWCGJQRegion3: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1209
        Height = 424
        Margins.Left = 5
        Margins.Top = 5
        Margins.Right = 5
        Margins.Bottom = 5
        TabOrder = 4
        Version = '1.0'
        Align = alClient
        object SelectUser: TIWCGJQMultiSelect
          Left = 0
          Top = 0
          Width = 1209
          Height = 424
          TabOrder = 5
          Version = '1.0'
          Align = alClient
          JQMultiSelectOptions.AvailableListPosition = jqmsolpLeft
          JQMultiSelectOptions.Locale = jqmsolDE
          JQMultiSelectOptions.HeaderText = 'Benutzer'
          JQMultiSelectOptions.SearchDelay = 300
          Items = <>
          Groups = <>
          Caption = 'Benutzer'
          JQCustomLocal.Strings.ItemsSelectedNil = 'Kein Benutzer ausgew'#228'hlt'
          JQCustomLocal.Strings.ItemsSelected = '{count} Benutzer ausgew'#228'hlt'
          JQCustomLocal.Strings.ItemsSelectedPlural = '{count} Benutzer ausgew'#228'hlt'
          JQCustomLocal.Strings.ItemsAvailableNil = 'Kein Benutzer verf'#252'gbar'
          JQCustomLocal.Strings.ItemsAvailable = '{count} Benutzer verf'#252'gbar'
          JQCustomLocal.Strings.ItemsAvailablePlural = '{count} Benutzer verf'#252'gbar'
          JQCustomLocal.Strings.ItemsFilteredNil = 'Kein Benutzer gefunden'
          JQCustomLocal.Strings.ItemsFiltered = '{count} Benutzer gefunden'
          JQCustomLocal.Strings.ItemsFilteredPlural = '{count} Benutzer gefunden'
          JQCustomLocal.Strings.SelectAll = 'Alle ausw'#228'hlen'
          JQCustomLocal.Strings.DeselectAll = 'Nichts ausw'#228'hlen'
          JQCustomLocal.Strings.Search = 'Suchoptionen'
        end
      end
    end
  end
  object Alert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 1056
    Top = 40
  end
end
