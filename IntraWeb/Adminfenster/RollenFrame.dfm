object Rollen: TRollen
  Left = 0
  Top = 0
  Width = 841
  Height = 569
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 841
    Height = 569
    RenderInvisibleControls = True
    TabOrder = 13
    Version = '1.0'
    Align = alClient
    BorderOptions.Style = cbsSolid
    object iwrRollenMid: TIWCGJQRegion
      Left = 0
      Top = 75
      Width = 841
      Height = 494
      RenderInvisibleControls = True
      Version = '1.0'
      Align = alClient
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqgRollen: TIWCGJQGrid
        Left = 1
        Top = 1
        Width = 839
        Height = 492
        TabOrder = 1
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bezeichnung'
            Name = 'Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bezeichnung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Defaultrolle'
            Name = 'Defaultrolle'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Defaultrolle'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Sichtbar'
            Name = 'Sichtbar'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Sichtbar'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Gueltig_Ab'
            Name = 'Gueltig_Ab'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'G'#252'ltig ab'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Parameter'
            Name = 'Parameter'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Parameter'
          end>
        JQGridOptions.Height = 438
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 837
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpRollen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object iwrRollenTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 841
      Height = 75
      RenderInvisibleControls = True
      TabOrder = 2
      Version = '1.0'
      Align = alTop
      BorderOptions.NumericWidth = 1
      BorderOptions.Style = cbsSolid
      object jqbAbfragen: TIWCGJQButton
        Left = 32
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Abfragen'
        JQButtonOptions.OnClick.OnEvent = jqbRollenAbfragenOnClick
      end
      object jqbNeu: TIWCGJQButton
        Left = 150
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 4
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbRollenNeuOnClick
      end
      object jqbAendern: TIWCGJQButton
        Left = 269
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 5
        Version = '1.0'
        JQButtonOptions.Label_ = #196'ndern'
        JQButtonOptions.OnClick.OnEvent = jqbRollenAendernOnClick
      end
      object jqbLoeschen: TIWCGJQButton
        Left = 392
        Top = 25
        Width = 100
        Height = 21
        TabOrder = 6
        Version = '1.0'
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = jqbRollenLoeschenOnClick
      end
    end
    object jqdRollen: TIWCGJQDialog
      Left = 205
      Top = 104
      Width = 364
      Height = 321
      Visible = False
      TabOrder = 7
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.CloseOnEscape = False
      JQDialogOptions.Height = 321
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 364
      JQDialogOptions.zIndex = 5000
      JQDialogOptions.ShowCloseIcon = False
      object iwcRollenDefaultrolle: TIWCheckBox
        Left = 115
        Top = 51
        Width = 121
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 9
        Checked = False
        FriendlyName = 'iwcRollenDefaultrolle'
      end
      object iwcRollenSichtbar: TIWCheckBox
        Left = 115
        Top = 78
        Width = 121
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 10
        Checked = True
        FriendlyName = 'iwcRollenSichtbar'
      end
      object IWLabel11: TIWCGJQLabel
        Left = 24
        Top = 51
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Defaultrolle:'
      end
      object IWLabel12: TIWCGJQLabel
        Left = 24
        Top = 78
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Sichtbar:'
      end
      object IWLabel13: TIWCGJQLabel
        Left = 24
        Top = 105
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'G'#252'ltig ab:'
      end
      object IWLabel14: TIWCGJQLabel
        Left = 24
        Top = 132
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Parameter:'
      end
      object IWLabel7: TIWCGJQLabel
        Left = 24
        Top = 24
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel7'
        Caption = 'Bezeichnung:'
      end
      object iwmRollenParameter: TIWMemo
        Left = 115
        Top = 132
        Width = 200
        Height = 90
        ZIndex = 5001
        StyleRenderOptions.RenderBorder = False
        BGColor = clNone
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        InvisibleBorder = False
        HorizScrollBar = False
        VertScrollBar = True
        Required = False
        TabOrder = 11
        SubmitOnAsyncEvent = True
        FriendlyName = 'iwmRollenParameter'
      end
      object jqdRollenGueltigAb: TIWCGJQDatePicker
        Left = 115
        Top = 105
        Width = 200
        Height = 21
        TabOrder = 11
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.DateFormat = 'dd/mm/yyyy'
      end
      object jqeRollenBezeichnung: TIWCGJQEdit
        Left = 115
        Top = 24
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  object jqdpRollen: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_rollen
    KeyFields = 'ID'
    Left = 697
    Top = 17
  end
end
