inherited Kommunikationswege: TKommunikationswege
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 1
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 12
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ART'
            Name = 'ART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Art'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VALUE'
            Name = 'VALUE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Value'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 3
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 5
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 6
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 13
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 9
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 366
      Top = 117
      Height = 200
      TabOrder = 11
      JQDialogOptions.Height = 200
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 24
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Art:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 56
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 16
        Top = 88
        Width = 85
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Value:'
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 115
        Top = 56
        Width = 150
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeValue: TIWCGJQEdit
        Left = 115
        Top = 88
        Width = 150
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqcbArt: TIWCGJQComboBoxEx
        Left = 115
        Top = 24
        Width = 150
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 148
        Caption = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quKommunikationswege
    Left = 944
  end
  object quKommunikationswege: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Kommunikationswege;')
    Left = 864
    Top = 24
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Kommunikationswege (Art, Bezeichnung, Val' +
        'ue)'
      'VALUES      (:art, :bez, :value);')
    Left = 785
    Top = 25
    ParamData = <
      item
        Name = 'ART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VALUE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Kommunikationswege'
      'SET    Art = :art, Bezeichnung = :bez, Value = :value'
      'WHERE  Id = :id;')
    Left = 737
    Top = 25
    ParamData = <
      item
        Name = 'ART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VALUE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Kommunikationswege'
      'WHERE       Id = :id;')
    Left = 681
    Top = 25
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quKommunikationsarten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Systemstammdaten.Kommunikationsarten;')
    Left = 865
    Top = 73
  end
end
