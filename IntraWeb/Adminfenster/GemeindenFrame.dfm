object Gemeinden: TGemeinden
  Left = 0
  Top = 0
  Width = 970
  Height = 759
  Align = alClient
  TabOrder = 0
  object IWFrameRegion: TIWCGJQRegion
    Left = 0
    Top = 0
    Width = 970
    Height = 759
    RenderInvisibleControls = True
    TabOrder = 16
    Version = '1.0'
    Align = alClient
    object iwrTop: TIWCGJQRegion
      Left = 0
      Top = 0
      Width = 970
      Height = 100
      RenderInvisibleControls = True
      TabOrder = 17
      Version = '1.0'
      Align = alTop
      object IWLabel17: TIWCGJQLabel
        Left = 16
        Top = 10
        Width = 103
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel17'
        Caption = 'Gemeindename:'
      end
      object IWLabel18: TIWCGJQLabel
        Left = 220
        Top = 10
        Width = 55
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel18'
        Caption = 'Amtsplz:'
      end
      object IWLabel1: TIWCGJQLabel
        Left = 220
        Top = 54
        Width = 97
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel18'
        Caption = 'Gemeindecode:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 54
        Width = 129
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel17'
        Caption = 'Gemeindekennziffer:'
      end
      object EditGemeindenname: TIWCGJQEdit
        Left = 14
        Top = 27
        Width = 200
        Height = 21
        TabOrder = 18
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
      end
      object EditAmtsplz: TIWCGJQEdit
        Left = 220
        Top = 27
        Width = 200
        Height = 21
        TabOrder = 20
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
      end
      object EditGemeindencode: TIWCGJQEdit
        Left = 220
        Top = 71
        Width = 200
        Height = 21
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
      end
      object EditGemeindenkennziffer: TIWCGJQEdit
        Left = 14
        Top = 71
        Width = 200
        Height = 21
        TabOrder = 1
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
      end
      object jqbNeu: TIWCGJQButton
        Left = 568
        Top = 27
        Width = 100
        Height = 21
        TabOrder = 2
        Version = '1.0'
        JQButtonOptions.Label_ = 'Neu'
        JQButtonOptions.OnClick.OnEvent = jqbNeuOnClick
      end
      object jqbAbfragen: TIWCGJQButton
        Left = 448
        Top = 27
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        JQButtonOptions.Label_ = 'Suchen'
        JQButtonOptions.OnClick.OnEvent = jqbAbfragenOnClick
      end
      object jqbAendern: TIWCGJQButton
        Left = 688
        Top = 27
        Width = 100
        Height = 21
        TabOrder = 4
        Version = '1.0'
        JQButtonOptions.Label_ = #196'ndern'
        JQButtonOptions.OnClick.OnEvent = jqbAendernOnClick
      end
      object jqbLoeschen: TIWCGJQButton
        Left = 808
        Top = 27
        Width = 100
        Height = 21
        TabOrder = 5
        Version = '1.0'
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = jqbLoeschenOnClick
      end
    end
    object iwrMid: TIWCGJQRegion
      Left = 0
      Top = 100
      Width = 970
      Height = 659
      RenderInvisibleControls = True
      TabOrder = 19
      Version = '1.0'
      Align = alClient
      object jqgGemeinden: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 970
        Height = 659
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEMEINDEKENNZIFFER'
            Name = 'GEMEINDEKENNZIFFER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Gemeindenkennziffer'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEMEINDENAME'
            Name = 'GEMEINDENAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gemeindename'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEMEINDECODE'
            Name = 'GEMEINDECODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Gemeindecode'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AMTSPLZ'
            Name = 'AMTSPLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Amtsplz'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'POL_BEZ_KENNZIF'
            Name = 'POL_BEZ_KENNZIF'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Pol_Bzk_Kennzif'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'POL_BEZIRK'
            Name = 'POL_BEZIRK'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Pol_Bezirk'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'POL_BEZ_CODE'
            Name = 'POL_BEZ_CODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Pol_Bez_Code'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LAND_ISO2'
            Name = 'LAND_ISO2'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Land_ISO2'
          end>
        JQGridOptions.Height = 605
        JQGridOptions.RowNum = 50
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 968
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridNav.Add = False
        JQGridNav.CloseOnEscape = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = jqdpGemeinden
        JQSubGridProvider = jqdpGemeinden
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object jqdGemeinden: TIWCGJQDialog
      Left = 310
      Top = 176
      Width = 387
      Height = 329
      Visible = False
      TabOrder = 7
      Version = '1.0'
      Align = alNone
      ZIndex = 5000
      JQDialogOptions.AutoOpen = False
      JQDialogOptions.Height = 329
      JQDialogOptions.Modal = True
      JQDialogOptions.Width = 387
      JQDialogOptions.zIndex = 5000
      JQDialogOptions.ShowCloseIcon = False
      object IWLabel3: TIWCGJQLabel
        Left = 20
        Top = 15
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Gemeindenkenziffer:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 20
        Top = 42
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Gemeindename:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 20
        Top = 69
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Gemeindecode:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 20
        Top = 96
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Amtsplz:'
      end
      object IWLabel7: TIWCGJQLabel
        Left = 20
        Top = 123
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Pol Bez Kennziffer:'
      end
      object IWLabel8: TIWCGJQLabel
        Left = 20
        Top = 150
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Pol Bezirk:'
      end
      object IWLabel9: TIWCGJQLabel
        Left = 20
        Top = 177
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Pol Bezirk Code:'
      end
      object IWLabel10: TIWCGJQLabel
        Left = 20
        Top = 204
        Width = 132
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Land ISO2:'
      end
      object jqeKennziffer: TIWCGJQEdit
        Left = 158
        Top = 15
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeName: TIWCGJQEdit
        Left = 158
        Top = 42
        Width = 200
        Height = 21
        TabOrder = 9
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeCode: TIWCGJQEdit
        Left = 158
        Top = 69
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqePlz: TIWCGJQEdit
        Left = 158
        Top = 96
        Width = 200
        Height = 21
        TabOrder = 11
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqePolBezKenn: TIWCGJQEdit
        Left = 158
        Top = 123
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqePolBez: TIWCGJQEdit
        Left = 158
        Top = 150
        Width = 200
        Height = 21
        TabOrder = 13
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqePolBezCode: TIWCGJQEdit
        Left = 158
        Top = 177
        Width = 200
        Height = 21
        TabOrder = 14
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeLandIso: TIWCGJQEdit
        Left = 158
        Top = 204
        Width = 200
        Height = 21
        TabOrder = 15
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  object quGemeinden: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Gemeinden'
      'WHERE  Bundeslandcode = :bldcode'
      
        '        AND ((Gemeindekennziffer = :kennziffer) OR (:kennziffer ' +
        '= -1))'
      '        AND ((Gemeindecode = :code) OR (:code = -1))'
      '        AND Gemeindename LIKE '#39'%'#39' + :name + '#39'%'#39
      '        AND AmtsPlz LIKE '#39'%'#39' + :plz + '#39'%'#39';')
    Left = 520
    Top = 56
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end
      item
        Name = 'KENNZIFFER'
        DataType = ftInteger
        ParamType = ptInput
        Value = -1
      end
      item
        Name = 'CODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = -1
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
    object quGemeindenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quGemeindenGEMEINDEKENNZIFFER: TIntegerField
      FieldName = 'GEMEINDEKENNZIFFER'
      Origin = 'GEMEINDEKENNZIFFER'
      Required = True
    end
    object quGemeindenGEMEINDENAME: TStringField
      FieldName = 'GEMEINDENAME'
      Origin = 'GEMEINDENAME'
      Required = True
      Size = 80
    end
    object quGemeindenGEMEINDECODE: TIntegerField
      FieldName = 'GEMEINDECODE'
      Origin = 'GEMEINDECODE'
      Required = True
    end
    object quGemeindenAMTSPLZ: TStringField
      FieldName = 'AMTSPLZ'
      Origin = 'AMTSPLZ'
      Required = True
      Size = 10
    end
    object quGemeindenPOL_BEZ_KENNZIF: TIntegerField
      FieldName = 'POL_BEZ_KENNZIF'
      Origin = 'POL_BEZ_KENNZIF'
      Required = True
    end
    object quGemeindenPOL_BEZIRK: TStringField
      FieldName = 'POL_BEZIRK'
      Origin = 'POL_BEZIRK'
      Required = True
      Size = 80
    end
    object quGemeindenPOL_BEZ_CODE: TIntegerField
      FieldName = 'POL_BEZ_CODE'
      Origin = 'POL_BEZ_CODE'
      Required = True
    end
    object quGemeindenLAND_ISO2: TStringField
      FieldName = 'LAND_ISO2'
      Origin = 'LAND_ISO2'
      Required = True
      FixedChar = True
      Size = 2
    end
    object quGemeindenBUNDESLANDCODE: TSmallintField
      FieldName = 'BUNDESLANDCODE'
      Origin = 'BUNDESLANDCODE'
    end
  end
  object jqdpGemeinden: TIWCGJQGridDataSetProvider
    DataSet = quGemeinden
    KeyFields = 'ID'
    Left = 448
    Top = 56
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Gemeinden (Gemeindekennziffer, Gemeindena' +
        'me, Gemeindecode, Amtsplz, Pol_Bez_kennzif, Pol_Bezirk,'
      '              Pol_Bez_Code, Land_Iso2, Bundeslandcode)'
      
        'VALUES      (:kennziffer, :name, :code, :plz, :pol_bez_kenn, :po' +
        'l_bez, :pol_bez_code, :land, :bldcode);')
    Left = 584
    Top = 56
    ParamData = <
      item
        Name = 'KENNZIFFER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ_KENN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ_CODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LAND'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Gemeinden '
      
        'SET    Gemeindekennziffer = :kennziffer, Gemeindename = :name, G' +
        'emeindecode = :code, Amtsplz = :plz, '
      
        '        Pol_Bez_kennzif = :pol_bez_kenn, Pol_Bezirk = :pol_bez, ' +
        'Pol_Bez_Code =  :pol_bez_code, Land_Iso2 = :land'
      'WHERE  Id = :id;')
    Left = 640
    Top = 56
    ParamData = <
      item
        Name = 'KENNZIFFER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'NAME'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ_KENN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POL_BEZ_CODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LAND'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Gemeinden'
      'WHERE       Id = :id;')
    Left = 696
    Top = 56
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
