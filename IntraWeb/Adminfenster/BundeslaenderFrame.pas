﻿unit BundeslaenderFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWCGJQGrid, IWCGJQControl,
  IWCGJQButton, IWHTMLContainer, IWHTML40Container, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWCGJQDialog, IWCGJQEdit,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQFileUpload, IWCGJQRegion, IWCGJQLabel, IW.HTTP.Reply, System.Net.HttpClient;

type
  TBundeslaender = class(TFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqbNeu: TIWCGJQButton;
    jqbAbfragen: TIWCGJQButton;
    jqbAendern: TIWCGJQButton;
    iwrMid: TIWCGJQRegion;
    jqgBundeslaender: TIWCGJQGrid;
    jqdpBundeslaender: TIWCGJQGridDataSetProvider;
    quBundeslaender: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    jqdBundeslaender: TIWCGJQDialog;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    jqeBldcode: TIWCGJQEdit;
    jqeBezeichnung: TIWCGJQEdit;
    jqeKurztext: TIWCGJQEdit;
    jqeRegion: TIWCGJQEdit;
    jqeLandkz: TIWCGJQEdit;
    jqeOkz: TIWCGJQEdit;
    IWLabel7: TIWCGJQLabel;
    jquBldlogo: TIWCGJQFileUpload;
    quAendereLogo: TFDQuery;
    quBundeslaenderBLDCODE: TSmallintField;
    quBundeslaenderBEZEICHNUNG: TStringField;
    quBundeslaenderKURZTEXT: TStringField;
    quBundeslaenderREGION: TSmallintField;
    quBundeslaenderLANDKZ: TStringField;
    quBundeslaenderBLDLOGO: TBlobField;
    quBundeslaenderOKZ: TStringField;
    quBundeslaenderBKBKZ: TStringField;
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAendernOnClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlert: TIWCGJQSweetAlert;
    property Alert: TIWCGJQSweetAlert read FAlert;

    procedure jqdNeuEvent(Sender: TObject; AURLParams: TStringList);
    procedure jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure clearModal;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean); reintroduce;
    procedure bundeslaenderAbfragen;
  end;

implementation

uses dmmain, Utility, JQ.Helpers.Grid;

{$R *.dfm}


constructor TBundeslaender.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  jquBldlogo.JQFileUploadOptions.Multiple := false;

  if not bearbeitbar then
  begin
    jqbNeu.Visible := false;
    jqbAendern.Visible := false;
  end;

  jqgBundeslaender.SetupDefaults(jqdpBundeslaender);
end;

procedure TBundeslaender.jqbAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  bundeslaenderAbfragen;
end;

procedure TBundeslaender.bundeslaenderAbfragen;
begin
  RefreshQuery(quBundeslaender);
end;

procedure TBundeslaender.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  clearModal;
  jqdBundeslaender.JQDialogOptions.Title := 'Neues Bundesland';
  jqb := jqdBundeslaender.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.OnClick.OnEvent := jqdNeuEvent;
  jqb := jqdBundeslaender.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdBundeslaender.Visible := true;
end;

procedure TBundeslaender.jqdNeuEvent(Sender: TObject; AURLParams: TStringList);
var
  bldcode, region: SmallInt;
  bezeichnung, kurztext, landkz, okz: String;
begin
  jqdBundeslaender.Visible := false;

  bldcode := StrToInt(jqeBldcode.Text);
  region := StrToInt(jqeRegion.Text);
  bezeichnung := jqeBezeichnung.Text;
  kurztext := jqeKurztext.Text;
  landkz := jqeLandkz.Text;
  okz := jqeOkz.Text;

  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bldcode').AsSmallInt := bldcode;
  quNeu.ParamByName('bezeichnung').AsString := bezeichnung;
  quNeu.ParamByName('kurztext').AsString := kurztext;
  quNeu.ParamByName('region').AsSmallInt := region;
  quNeu.ParamByName('landkz').AsString := landkz;
  quNeu.ParamByName('okz').AsString := okz;
  quNeu.Execute;

  bundeslaenderAbfragen;
end;

procedure TBundeslaender.jqbAendernOnClick(Sender: TObject;
  AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  if not moveQueryToRow(quBundeslaender, jqgBundeslaender) then
    Exit;

  clearModal;
  jqeBldcode.Enabled := false;
  jqeBezeichnung.Enabled := false;
  jqeRegion.Enabled := false;
  jqeLandkz.Enabled := false;
  jqeBldcode.Text := IntToStr(quBundeslaender.FieldByName('bldcode').AsInteger);
  jqeBezeichnung.Text := quBundeslaender.FieldByName('bezeichnung').AsString;
  jqeKurztext.Text := quBundeslaender.FieldByName('kurztext').AsString;
  jqeRegion.Text := IntToStr(quBundeslaender.FieldByName('region').AsInteger);
  jqeLandkz.Text := quBundeslaender.FieldByName('landkz').AsString;
  jqeOkz.Text := quBundeslaender.FieldByName('okz').AsString;

  jqdBundeslaender.JQDialogOptions.Title := 'Bundesland ändern';
  jqb := jqdBundeslaender.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Ändern';
  jqb.OnClick.OnEvent := jqdAendernEvent;
  jqb := jqdBundeslaender.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.OnClick.OnEvent := jqdAbbrechenEvent;
  jqdBundeslaender.Visible := true;
end;

procedure TBundeslaender.jqdAendernEvent(Sender: TObject; AURLParams: TStringList);
var
  bldcode: SmallInt;
  kurztext, okz: String;
begin
  jqdBundeslaender.Visible := false;
  bldcode := -1;
  try
    bldcode := StrToInt(jqeBldcode.Text);
  except
    Alert.Error('Ungültiges Bundesland');
    abort;
  end;
  kurztext := jqeKurztext.Text;
  okz := jqeOkz.Text;

  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('bldcode').AsSmallInt := bldcode;
  quAendern.ParamByName('kurztext').AsString := kurztext;
  quAendern.ParamByName('okz').AsString := okz;
  quAendern.Execute;

  bundeslaenderAbfragen;
end;

procedure TBundeslaender.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdBundeslaender.Visible := false;
end;

procedure TBundeslaender.clearModal;
begin
  jqeBldcode.Text := '';
  jqeBezeichnung.Text := '';
  jqeKurztext.Text := '';
  jqeRegion.Text := '';
  jqeLandkz.Text := '';
  jqeOkz.Text := '';
  jqeBldcode.Enabled := true;
  jqeBezeichnung.Enabled := true;
  jqeRegion.Enabled := true;
  jqeLandkz.Enabled := true;
  jqdBundeslaender.JQDialogOptions.Buttons.Clear;
end;

end.
