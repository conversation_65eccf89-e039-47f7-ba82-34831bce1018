unit Frames.UserGruppenAdmin;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Frames.Base, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQRegion, IWCGJQTreeView, Modules.Admin, IWCGJQMultiSelect,
  IWCGJQButton, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel, System.Generics.Collections;

type
  // TUsergruppenAdmin = class(TFrameBase)
  TUsergruppenAdmin = class(TFrameBase<TDMAdmin>)
    RegionTop: TIWCGJQRegion;
    RegionBottom: TIWCGJQRegion;
    TreeGruppen: TIWCGJQTreeView;
    SelectUser: TIWCGJQMultiSelect;
    RegionButtons: TIWCGJQRegion;
    IWCGJQRegion1: TIWCGJQRegion;
    ButtonSpeichern: TIWCGJQButton;
    ButtonAbbrechen: TIWCGJQButton;
    Alert: TIWCGJQSweetAlert;
    IWCGJQRegion2: TIWCGJQRegion;
    IWCGJQLabel1: TIWCGJQLabel;
    IWCGJQRegion3: TIWCGJQRegion;
    procedure GruppenOnSelect(Sender: TObject; AParams: TStringList);
    procedure ButtonSpeichernOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonAbbrechenOnClick(Sender: TObject; AParams: TStringList);
    procedure UserSelectOnChange(Sender: TObject; AParams: TStringList);
  private
    Bearbeitbar: boolean;
    GruppenID: integer;
    Hauptver, Stellver: integer;
    VorhandeneUser: TList<integer>;
    FSelectedGruppenNode: TIWCGJQTreeViewItem;
    procedure SelectGruppe(LGruppenID: integer);
  public
    constructor Create(AOwner: TComponent; DMAdmin: TDMAdmin; LBearbeitbar: boolean); reintroduce;
    destructor Destroy;override;
    procedure FillTreeGruppen;
  end;

implementation

uses dmmain, JQ.Helpers.TreeView, ELKE.Classes.RESTError;

{$R *.dfm}

constructor TUsergruppenAdmin.Create(AOwner: TComponent; DMAdmin: TDMAdmin; LBearbeitbar: boolean);
begin
  inherited Create(AOwner, DMAdmin);
  TreeGruppen.SetupDefaults;
  FillTreeGruppen;
  Bearbeitbar := LBearbeitbar;
  SelectUser.Enabled := false;
  VorhandeneUser := TList<integer>.Create;
end;

destructor TUsergruppenAdmin.Destroy;
begin
  VorhandeneUser.Free;
  inherited;
end;

procedure TUsergruppenAdmin.FillTreeGruppen;
begin
  DM.AddGruppenToTreeView(TreeGruppen);
  TreeGruppen.Refresh;
end;

procedure TUsergruppenAdmin.GruppenOnSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;

  if not Bearbeitbar then
    abort;
  if TreeGruppen.SelectedNode = Nil then
    abort;

  if TreeGruppen.SelectedNode = FSelectedGruppenNode then
    abort;
  FSelectedGruppenNode := TreeGruppen.SelectedNode;
  TreeGruppen.SetBookmark;

  SelectUser.Enabled := true;
  VorhandeneUser.Clear;
  GruppenID := integer(TreeGruppen.SelectedNode.Data);
  // ID von Haupt- und Stellvertreter feststellen
  DM.GruppenDetails(GruppenID);
  Hauptver := DM.QGruppenDetailsID_USER_HAUPTVER.Value;
  if not DM.QGruppenDetailsID_USER_STELLVER.IsNull then
  begin
    Stellver := DM.QGruppenDetailsID_USER_STELLVER.Value;
  end
  else
  begin
    Stellver := -1;
  end;
  // User anzeigen
  SelectGruppe(GruppenID);
end;

procedure TUsergruppenAdmin.SelectGruppe(LGruppenID: integer);
var
  item: TIWCGJQMultiSelectItem;
  userid: integer;
begin
  DM.SearchForAllUsers;
  SelectUser.Items.Clear;
  DM.QUsers.First;
  // Alle User auf der linken Seite anzeigen
  while not DM.QUsers.Eof do
  begin
    userid := DM.QUsersID.Value;
    item := SelectUser.Items.Add;
    item.Caption := DM.QUsersNACHNAME.AsString + ' ' + DM.QUsersVORNAME.AsString;
    item.Value := IntToStr(userid);
    DM.QUsers.Next;
  end;
  // Die User in der Gruppe auf die rechte Seite verschieben
  DM.GetUsersInGruppe(LGruppenID);
  while not DM.QUsergruppe.Eof do
  begin
    userid := DM.QUsergruppeID_USER.Value;
    VorhandeneUser.Add(userid);
    item := SelectUser.Items.ItemsByValue[IntToStr(userid)];
    if item <> Nil then
    begin
      item.Selected := true;
      if userid = Hauptver then
      begin
        item.Caption := item.Caption + ' (Hauptverantwortlicher)';
      end
      else if userid = Stellver then
      begin
        item.Caption := item.Caption + ' (Stellvertreter)';
      end;
    end;
    DM.QUsergruppe.Next;
  end;
  if WebApplication.CallBackProcessing then
    SelectUser.AjaxReRender;
end;

procedure TUsergruppenAdmin.UserSelectOnChange(Sender: TObject; AParams: TStringList);
var
  s: string;
begin
  inherited;
  for s in AParams do
  begin
    if s.Length > 10 then
    begin

    end;
  end;
end;

procedure TUsergruppenAdmin.ButtonSpeichernOnClick(Sender: TObject; AParams: TStringList);
var
  item: TIWCGJQMultiSelectItem;
  i, userid: integer;
begin
  inherited;
  dm_main.FBC_MAIN.StartTransaction;
  try
    for i := 0 to SelectUser.Items.Count - 1 do
    begin
      item := SelectUser.Items.Items[i];
      if item.Selected then
      begin
        userid := StrToInt(item.Value);
        VorhandeneUser.Remove(userid);
        DM.AddUserToGruppe(GruppenID, userid);
      end;
    end;
    for userid in VorhandeneUser do
    begin
      if (userid <> Hauptver) and (userid <> Stellver) then
      begin
        DM.RemoveUserFromGruppe(GruppenID, userid);
      end;
    end;
    dm_main.FBC_MAIN.Commit;
  except
    on E: Exception do
    begin
      dm_main.FBC_MAIN.Rollback;
      Alert.Error('Es gab einen Fehler beim Speichern.' + sLineBreak + E.Message);
      Exit;
    end;
  end;
  SelectUser.Enabled := false;
  Alert.Success('Erfolgreich gespeichert');
  FillTreeGruppen;
end;

procedure TUsergruppenAdmin.ButtonAbbrechenOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  SelectUser.Items.Clear;
  SelectUser.Enabled := false;
end;

end.
