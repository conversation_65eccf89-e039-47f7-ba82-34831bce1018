inherited FrmWillkommen: TFrmWillkommen
  Width = 775
  Height = 787
  HorzScrollBar.Visible = False
  OnShow = IWAppFormShow
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 775
    TabOrder = 1
    inherited ImageLogo: TIWImageFile
      Left = 492
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 2
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 762
    Width = 775
    TabOrder = 3
  end
  object RegionCenter: TIWCGJQRegion [2]
    Left = 0
    Top = 50
    Width = 775
    Height = 712
    TabOrder = 4
    Version = '1.0'
    Align = alClient
    DesignSize = (
      775
      712)
    object laWillkommen: TIWCGJQLabel
      AlignWithMargins = True
      Left = 8
      Top = 8
      Width = 759
      Height = 41
      Margins.Left = 8
      Margins.Top = 8
      Margins.Right = 8
      Margins.Bottom = 8
      Align = alTop
      StyleRenderOptions.RenderFont = True
      Alignment = taCenter
      Font.Color = clNone
      Font.Size = 22
      Font.Style = [fsBold]
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'LabelWillkommen'
      Caption = 'Willkommen'
    end
    object IWImageFile1: TIWImageFile
      Left = 109
      Top = 80
      Width = 554
      Height = 554
      Anchors = [akTop]
      BorderOptions.Width = 0
      UseSize = True
      OnClick = IWImageFile1Click
      Cacheable = True
      FriendlyName = 'IWImageFile1'
      ImageFile.Filename = '\bilder\ELKE-Logo.png'
    end
    object ButtonWeiter: TIWCGJQButton
      Left = 109
      Top = 654
      Width = 554
      Height = 57
      Font.Size = 22
      Version = '1.0'
      Anchors = [akTop]
      JQButtonOptions.Label_ = 'Weiter'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = iwcgbtn_WeiterJQButtonOptionsClick
      JQButtonOptions.OnClick.SendAllArguments = True
    end
  end
  object PopupCookieHinweis: TIWCGJQShowMessage
    Version = '1.0'
    Left = 1016
    Top = 88
  end
  object jqsaAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 808
    Top = 8
  end
  object AlertError: TIWCGJQSweetAlert
    Version = '1.0'
    JQSweetAlertOptions.Title = 'Login fehlgeschlagen'
    JQSweetAlertOptions.Text = 
      'Sie konnten nicht automatisch am System angemeldet werde. Bitte ' +
      'wenden Sie sich an die Administration!'
    JQSweetAlertOptions.AlertType = jqsatError
    JQSweetAlertOptions.AllowEscapeKey = False
    Left = 88
    Top = 178
  end
end
