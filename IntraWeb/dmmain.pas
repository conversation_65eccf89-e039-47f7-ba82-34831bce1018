﻿unit dmmain;

interface

uses
  System.SysUtils, System.Classes, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Comp.Client,
  Data.DB, FireDAC.Phys.FB, FireDAC.Stan.Param, FireDAC.DatS, FireDAC.DApt.Intf,
  FireDAC.DApt, FireDAC.Comp.DataSet, Vcl.Graphics, FireDAC.Phys.FBDef,
  FireDAC.VCLUI.Wait, IWVCLComponent, IWBaseLayoutComponent,
  IWBaseContainerLayout, IWContainerLayout, IWLayoutMgrHTML, FireDAC.Phys.MSSQL,
  FireDAC.Phys.MSSQLDef, Vcl.Imaging.pngimage, IdBaseComponent,
  IdComponent, IdTCPConnection, IdTCPClient, IdHTTP, System.Variants, Config;

type
  HTTPOperation = (hpGet,
    hpPut,
    hpPost,
    hpDelete,
    hpPatch,
    hpUpdate);

  Tdm_main = class(TDataModule)
    FBC_MAIN: TFDConnection;
    TransactionMain: TFDTransaction;
    qu_user: TFDQuery;
    qu_checkland: TFDQuery;
    qu_funktionen: TFDQuery;
    qu_VisBStammdaten: TFDQuery;
    qu_VisZulassungen: TFDQuery;
    qu_VisBetriebstypen: TFDQuery;
    qu_VisDom: TFDQuery;
    qu_VisListencodes: TFDQuery;
    qu_VISTierarten: TFDQuery;
    qu_VISZulassungstypen: TFDQuery;
    qu_VISSchluesseltypen: TFDQuery;
    qu_VISprbkat: TFDQuery;
    qu_VISZulassungsNr: TFDQuery;
    qu_StDat_Tierarten: TFDQuery;
    qu_StDat_TAkat: TFDQuery;
    quBetriebssuche: TFDQuery;
    qu_einzelBetrieb: TFDQuery;
    qu_Zulassungen: TFDQuery;
    qu_Betriebstypen: TFDQuery;
    qu_Zulassungsnummern: TFDQuery;
    qu_FilterFragen: TFDQuery;
    qu_ZulassungsArten: TFDQuery;
    qu_Taetigkeiten: TFDQuery;
    qu_kontrollfragen: TFDQuery;
    qu_unterfragen: TFDQuery;
    qu_StDat_TaetBetrtyp: TFDQuery;
    qu_StDat_BetrtypTA: TFDQuery;
    qu_Fragengruppen: TFDQuery;
    qu_Fragenhauptgruppen: TFDQuery;
    qu_Checklistenpunkte: TFDQuery;
    qu_Checklisten: TFDQuery;
    qu_Checkp_Frage: TFDQuery;
    qu_stdat_revstamm_ins: TFDQuery;
    qu_filterCheckpunkte: TFDQuery;
    qu_Termine: TFDQuery;
    qu_Ins_Termin: TFDQuery;
    qu_Update_Termin: TFDQuery;
    qu_ZulassungenBetr: TFDQuery;
    qu_HTTP_Header: TFDQuery;
    qu_TaetigGrp: TFDQuery;
    qu_tierarten: TFDQuery;
    qu_LetzterLogin: TFDQuery;
    qu_Rolle: TFDQuery;
    ds_Betriebe: TDataSource;
    qu_btrtypenTA: TFDQuery;
    qu_btrtypenTat: TFDQuery;
    qu_UpdateAnmerkung: TFDQuery;
    qu_Ins_TodoListe: TFDQuery;
    qu_TodoListe: TFDQuery;
    qu_Bundesland: TFDQuery;
    ds_Bundesland: TDataSource;
    qu_StDat_RevisionsStamm: TFDQuery;
    qu_Kontr_bkbtypen: TFDQuery;
    qu_kontr_betrieb: TFDQuery;
    qu_kontr_benutzer: TFDQuery;
    qu_kontr_bkbUnterart: TFDQuery;
    qu_kontr_refbkb: TFDQuery;
    qu_kontr_Kontrollorgan: TFDQuery;
    qu_kontr_bkbtypKZ: TFDQuery;
    qu_sendNachricht: TFDQuery;
    qu_kontr_Berichte: TFDQuery;
    qu_autofillBetriebsname: TFDQuery;
    qu_KontrJahre: TFDQuery;
    qu_Kontrolljahre: TFDQuery;
    qu_kontrollListe: TFDQuery;
    qu_gruppen: TFDQuery;
    qu_user_suche: TFDQuery;
    qu_gruppe_speichern: TFDQuery;
    qu_gruppe_loesche: TFDQuery;
    qu_gruppe_aendern: TFDQuery;
    qu_usergruppen: TFDQuery;
    quAutocompleteUsername: TFDQuery;
    qu_KontrollenVonBetrieb: TFDQuery;
    qu_logs: TFDQuery;
    qu_logdetail: TFDQuery;
    qu_requestheaders: TFDQuery;
    qu_stdat_revisionsstamm_loeschen: TFDQuery;
    qu_stdat_revstamm_update: TFDQuery;
    qu_Ins_Stammdaten: TFDQuery;
    qu_stdat_revplan: TFDQuery;
    qu_stdat_revplan_erzeugen: TFDQuery;
    qu_stdat_revplan_update: TFDQuery;
    qu_stdat_revplan_loeschen: TFDQuery;
    qu_user_gid: TFDQuery;
    qu_rollen_neu: TFDQuery;
    qu_rollen: TFDQuery;
    qu_rollen_update: TFDQuery;
    qu_rollen_loeschen: TFDQuery;
    qu_funktionsrollen: TFDQuery;
    qu_funktionsrollen_loeschen: TFDQuery;
    qu_funktionsrollen_neu: TFDQuery;
    qu_funktionsrollen_funktion: TFDQuery;
    qu_funktionsrollen_rollen: TFDQuery;
    qu_existiert_funktionsrolle: TFDQuery;
    qu_funktionsrollen_update: TFDQuery;
    qu_rechtsgrundlagen: TFDQuery;
    qu_rechtsgrundlagen_neu: TFDQuery;
    qu_rechtsgrundlagen_update: TFDQuery;
    qu_rechtsgrundlagen_loeschen: TFDQuery;
    qu_bkb_rechtsgrundlage: TFDQuery;
    qu_bkb_rechtsgrundlage_neu: TFDQuery;
    qu_bkb_rechtsgrundlage_loeschen: TFDQuery;
    qu_bkb_rechtsgrundlage_bkb: TFDQuery;
    qu_bkb_rechtsgrundlage_rechtsgrundlage: TFDQuery;
    qu_user_sucheID: TFDAutoIncField;
    qu_user_sucheID_PERSON: TIntegerField;
    qu_user_sucheUSERNAME: TStringField;
    qu_user_suchePASSWORT: TStringField;
    qu_user_sucheUSERGUID: TStringField;
    qu_user_sucheUSERTYPE: TSmallintField;
    qu_user_sucheGESPERRT: TSmallintField;
    qu_user_sucheBLDCODE: TSmallintField;
    qu_user_sucheUSERPWC: TSmallintField;
    qu_user_sucheAKTIV: TSmallintField;
    qu_user_sucheTSTAMP_INSERT: TSQLTimeStampField;
    qu_user_sucheINS_DBUSER: TStringField;
    qu_user_sucheLASTCHANGE: TSQLTimeStampField;
    qu_user_sucheCHANGE_DBUSER: TStringField;
    qu_user_sucheEMAIL: TStringField;
    qu_user_sucheLAST_LOGIN: TSQLTimeStampField;
    QSys: TFDQuery;
    QSysSYSTEMKZ: TStringField;
    QSysSYSTEMNAME: TStringField;
    QSysVERSION_DB: TStringField;
    QSysVERSION_MOPED: TStringField;
    QSysVERSION_IW: TStringField;
    QSysVERSION_REST: TStringField;
    QSysVERSION_CCK: TStringField;
    qu_rollenID: TFDAutoIncField;
    qu_rollenBEZEICHNUNG: TStringField;
    qu_rollenDEFAULTROLLE: TBooleanField;
    qu_rollenSICHTBAR: TBooleanField;
    qu_rollenGUELTIG_AB: TDateField;
    qu_rollenPARAMETER: TStringField;
    qu_funktionsrollenID_ROLLE: TIntegerField;
    qu_funktionsrollenID_FUNKTION: TIntegerField;
    qu_funktionsrollenBEGDAT: TDateField;
    qu_funktionsrollenENDDAT: TDateField;
    qu_funktionsrollenSICHTBAR: TBooleanField;
    qu_funktionsrollenID: TFDAutoIncField;
    qu_funktionsrollenBEZEICHNUNG: TStringField;
    qu_funktionsrollenDEFAULTROLLE: TBooleanField;
    qu_funktionsrollenSICHTBAR_1: TBooleanField;
    qu_funktionsrollenGUELTIG_AB: TDateField;
    qu_funktionsrollenPARAMETER: TStringField;
    qu_funktionsrollenID_1: TFDAutoIncField;
    qu_funktionsrollenPROGMODUL: TStringField;
    qu_funktionsrollenBEZEICHNUNG_1: TStringField;
    qu_funktionsrollenHINTTEXT: TStringField;
    qu_funktionsrollenID_MUTTER: TIntegerField;
    qu_funktionsrollenFARBCODE: TIntegerField;
    qu_funktionsrollenBEGDAT_1: TDateField;
    qu_funktionsrollenENDDAT_1: TDateField;
    qu_funktionsrollenSICHTBAR_2: TBooleanField;
    qu_funktionsrollenPROGCALLID: TIntegerField;
    qu_funktionsrollenOBJEKT: TStringField;
    qu_funktionsrollenPRGTEIL: TStringField;
    qu_rechtsgrundlagenID: TFDAutoIncField;
    qu_rechtsgrundlagenBEZEICHNUNG: TStringField;
    qu_rechtsgrundlagenKURZBEZEICHNUNG: TStringField;
    quBetriebssucheZulnr: TStringField;
    quBetriebssucheID: TFDAutoIncField;
    quBetriebssucheREGNR: TStringField;
    quBetriebssucheNAME: TStringField;
    quBetriebssucheID_ADRESSE: TIntegerField;
    quBetriebssucheSTRASSE: TStringField;
    quBetriebssuchePLZ: TStringField;
    quBetriebssucheORT: TStringField;
    quBetriebssucheGEMNR: TStringField;
    quBetriebssucheKATASTRALGEMNAME: TStringField;
    quBetriebssucheAUFSICHTSORGAN: TStringField;
    quBetriebssucheTELEFON: TStringField;
    quBetriebssucheEMAIL: TStringField;
    quBetriebssucheVERGEBUEHRUNG: TStringField;
    quBetriebssucheBLDCODE: TSmallintField;
    quBetriebssucheVULGO: TStringField;
    quBetriebssucheANMERKUNG: TWideStringField;
    quBetriebssucheBVBKZ: TStringField;
    quBetriebssucheBBKNR: TStringField;
    quBetriebssucheMarkierungWert: TWideStringField;
    quBetriebssucheID_MARKIERUNG: TGuidField;
    quBetriebssucheFlagHTML: TStringField;
    quBetriebssucheMarkierungBeschreibung: TWideStringField;
    quBetriebssucheFlag: TIntegerField;
    qu_free: TFDQuery;
    qu_stdat_revplanID: TFDAutoIncField;
    qu_stdat_revplanID_REVISIONSSTAMM: TIntegerField;
    qu_stdat_revplanJAHR: TSmallintField;
    qu_stdat_revplanRISIKO_KATEGORIE: TSmallintField;
    qu_stdat_revplanJ_MINDEST_KONTROLL_FREQUENZ: TSingleField;
    qu_stdat_revplanANZ_BETRIEBE_IM_LAND: TSmallintField;
    qu_stdat_revplanANZ_GESAMT_KONTROLLEN: TSmallintField;
    qu_stdat_revplanGESPERRT: TBooleanField;
    qu_stdat_revplanID_1: TFDAutoIncField;
    qu_stdat_revplanBLDCODE: TSmallintField;
    qu_stdat_revplanSEKTION: TStringField;
    qu_stdat_revplanBETRIEBSGRUPPE_LM: TStringField;
    qu_stdat_revplanBETRIEBSGRUPPE_DETAIL: TStringField;
    qu_stdat_revplanBETRIEBSART: TStringField;
    qu_stdat_revplanBKBTYP: TStringField;
    qu_stdat_revplanKONTROLLTYP: TStringField;
    qu_stdat_revplan_abschliessen: TFDQuery;
    qu_stdat_revplan_abschliessenID: TIntegerField;
    qu_stdat_revplan_abschliessenKONTROLLTYP: TStringField;
    qu_stdat_revplan_abschliessenBKBTYP: TStringField;
    qu_stdat_revplan_abschliessenFREQUENZ: TSingleField;
    qu_stdat_revplan_sperren: TFDQuery;
    qu_stdat_revplan_kontrollen: TFDQuery;
    qu_stdat_revplan_kontrollenAnzahl: TIntegerField;
    qu_KontrollenVonBetriebID: TFDAutoIncField;
    qu_KontrollenVonBetriebGUID: TGuidField;
    qu_KontrollenVonBetriebBKB: TStringField;
    qu_KontrollenVonBetriebBKBTYP: TStringField;
    qu_KontrollenVonBetriebKONTROLLTYP: TStringField;
    qu_KontrollenVonBetriebDATUM: TDateField;
    qu_KontrollenVonBetriebERFASSER_PSKEY: TStringField;
    qu_KontrollenVonBetriebKONTROLLORGAN_PSKEY: TStringField;
    qu_KontrollenVonBetriebID_PERSON_ERFASSER: TIntegerField;
    qu_KontrollenVonBetriebID_PERSON_KONTROLLORGAN: TIntegerField;
    qu_KontrollenVonBetriebREF_BKB: TStringField;
    qu_KontrollenVonBetriebPROBENZIEHUNG: TBooleanField;
    qu_KontrollenVonBetriebID_BETRIEB: TIntegerField;
    qu_KontrollenVonBetriebREGNR_ORT: TStringField;
    qu_KontrollenVonBetriebSTARTZEIT: TSQLTimeStampField;
    qu_KontrollenVonBetriebENDEZEIT: TSQLTimeStampField;
    qu_KontrollenVonBetriebBESTAETIGT_UM: TSQLTimeStampField;
    qu_KontrollenVonBetriebID_RECHTSGRUNDLAGE: TIntegerField;
    qu_KontrollenVonBetriebSTATUS: TStringField;
    qu_KontrollenVonBetriebANGEMELDET_UM: TSQLTimeStampField;
    qu_KontrollenVonBetriebTSTAMP_INSERT: TSQLTimeStampField;
    qu_KontrollenVonBetriebLASTCHANGE: TSQLTimeStampField;
    qu_KontrollenVonBetriebBETRIEBSTYP: TStringField;
    qu_KontrollenVonBetriebGUID_UNTERSCHRIFT_ANWESENDER_BETRIEB: TGuidField;
    qu_KontrollenVonBetriebGUID_UNTERSCHRIFT_KONTROLLORGAN: TGuidField;
    qu_KontrollenVonBetriebVERWEIGERUNGSGRUND_UNTERSCHRIFT: TStringField;
    qu_KontrollenVonBetriebVerweigerunggrund: TStringField;
    qu_KontrollenVonBetriebGUID_DOKUMENT: TGuidField;
    qu_KontrollenVonBetriebFEHLERHAFT_GESETZT_AM: TSQLTimeStampField;
    qu_KontrollenVonBetriebSTORNIERT_AM: TSQLTimeStampField;
    qu_KontrollenVonBetriebSTORNOGRUND: TStringField;
    qu_KontrollenVonBetriebVERWEIGERT_AM: TSQLTimeStampField;
    qu_KontrollenVonBetriebGUID_DOKUMENT_CC: TGuidField;
    qu_KontrollenVonBetriebVISEXPORT_AM: TSQLTimeStampField;
    qu_KontrollenVonBetriebKONTROLL_INFORMATIONEN: TStringField;
    qu_tierartenTierart: TStringField;
    qu_Taetigkeitenbezeichnung: TStringField;
    qu_TaetigGrpBezeichnung: TStringField;
    qu_einzelBetriebName: TStringField;
    qu_einzelBetriebEmail: TStringField;
    qu_einzelBetriebTelefon: TStringField;
    qu_einzelBetriebAnmerkung: TWideStringField;
    qu_einzelBetriebStrasse: TWideStringField;
    qu_einzelBetriebPlz: TStringField;
    qu_einzelBetriebOrt: TWideStringField;
    qu_einzelBetriebGemeindename: TStringField;
    qu_KontrollenVonBetriebID_GRUPPE_QUELLE: TIntegerField;
    qu_KontrollenVonBetriebKURZBEMERKUNG: TWideMemoField;
    qu_KontrollenVonBetriebID_REVISIONSPLAN: TIntegerField;
    function giveLfbisAlphaZahl(zeichen: char): integer;
    procedure DataModuleCreate(Sender: TObject);
    procedure FBC_MAINBeforeConnect(Sender: TObject);
    procedure quBetriebssucheCalcFields(DataSet: TDataSet);
  private
    FConfig: TConfig;
    function GetSystemKZ: string;
  public
    { Public-Deklarationen }

    // Internal userid vom angemeldeten benutzer
    UserId: integer;
    username: string;
    BLDCODE: integer;
    erfasser: string;
    web_pfad_exe: string;
    web_pfad_listen: string;
    web_pfad_reports: string;

    Tierazt_titel_VN_NN: string;
    PCNAME: string;
    DBID: string;
    VERS_DB: string;
    VERS_META: string;
    VERS_KLD: string;
    login_nummer: string;
    PRT_LISTEN_NAME: string;
    //
    id_Ezg: integer;
    UserGrp: integer; // 1 Admin // 2 SH // 3 TX // 4 EZG // 5 LandVet
    Schlachtzulassungsnummer: string;
    Tierarztnummer: string;
    Lieferantennummer: string;
    LFBIS_NUMMER_LIEFERANT: string;
    Klass_id: string;
    Suche_zulassungsnummer: string;
    Suche_tierarzt_name_tanumm: string;
    probennummer: string;
    // Allgeine Verwendung
    PARA_ALLG_ANL_KOPF_ID: integer;
    PARA_ALLG_TIERART: string;
    //
    // Felder Eingabe -herkunftsbetrieb
    Herkunftsbetrieb_name1: string;
    Herkunftsbetrieb_name2: string;
    Herkunftsbetrieb_adresse: string;
    Herkunftsbetrieb_plz: string;
    Herkunftsbetrieb_ort: string;
    //
    FORMULAR_SENDER: string;
    //
    Landliste: TStringlist;
    // STU PARAMETER
    PARA_STU_ANL_KOPF_ID: integer;
    PARA_STU_EINZELTIER_ID: integer;
    PARA_STU_TIERART: string;
    PARA_STU_STU_ID: integer;
    // FUS Parameter
    PARA_FUS_ANL_KOPF_ID: integer;
    PARA_FUS_EINZELTIER_ID: integer;
    PARA_FUS_TIERART: string;
    PARA_FUS_STU_ID: integer;
    // Parameter / Dialogbox JN
    PARA_JN: boolean; // true , false
    PARA_QUELLE_FORMULAR: string;
    PARA_PROG_ABSCHNITT: string;
    PARA_RICHTUNG: string; // H = Hin , Z = Zurück
    PARA_MELDUNGSTEXT1: string;
    //
    // Para / inputbox1
    PARA_INP1_JN: boolean; // true , false
    PARA_INP1_QUELLE_FORMULAR: string;
    PARA_INP1_PROG_ABSCHNITT: string;
    PARA_INP1_RICHTUNG: string; // H = Hin , Z = Zurück
    PARA_INP1_MELDUNGSTEXT1: string;
    PARA_INP1_WERT: string;
    PARA_INP1_INTEGER_CHECK: boolean;
    PARA_INP1_DATUM_CHECK: boolean;
    PARA_INP1_MIN_CHECK: boolean;
    PARA_INP1_MAX_CHECK: boolean;
    PARA_INP1_ZERO_CHECK: boolean;
    PARA_INP1_MIN_CHECK_WERT: integer;
    PARA_INP1_MAX_CHECK_WERT: integer;
    PARA_INP1_AZ_CHECK: boolean;
    PARA_INP1_09_CHECK: boolean;
    PARA_INP1_09_AZ_az_CHECK: boolean;
    //
    Para_tkhid: string;
    Para_bkb_typ: string;
    Para_tierart: string;
    // Farben für die Darstellung
    Para_Color_Balken_unten: string;
    Para_Color_Balken_links: string;
    Para_Color_Balken_rechts: string;

    function check_land(land: string): boolean;
    function LFBISPruefung(lfbisstr: string): boolean;
    procedure Berechne_Datum(
      Datum_alt: String;
      Var Monate: string;
      Var Tage: string);
    function prepare_LFBIS(LFBIS: string): string;
    function AZ_Pruefung(TEXT: string): boolean;
    function Null_Neun_Zahlen_Pruefung(TEXT: string): boolean;
    function Null_Neun_Zahlen_AZ_Pruefung(TEXT: string): boolean;
    function CHECK_DATUM_FORMAT_TTMMJJJJ(tt, mm, yyyy: word): boolean;

    property SystemKZ: string read GetSystemKZ;

  end;

function dm_main: Tdm_main;

implementation

{%CLASSGROUP 'Vcl.Controls.TControl'}


uses ServerController;
{$R *.dfm}


function dm_main: Tdm_main;
begin
  Result := UserSession.dm_main;
end;

function Tdm_main.check_land(land: string): boolean;
begin
  dm_main.qu_checkland.active := false;
  dm_main.qu_checkland.SQL.Clear;
  // SQL-TEXT
  dm_main.qu_checkland.SQL.Add('Select count(*) as anz from landcode where KURZID =:KURZID');
  dm_main.qu_checkland.Prepared := true;
  // Parameterübergabe
  dm_main.qu_checkland.parambyname('KURZID').asstring := land;
  dm_main.qu_checkland.active := true;
  if dm_main.qu_checkland.fieldbyname('anz').asinteger > 0 then
    Result := true
  else
    Result := false;
  dm_main.qu_checkland.active := false;
end;

function Tdm_main.LFBISPruefung(lfbisstr: string): boolean;
var
  LFBIS: string;
  csum, xsum, tmp: Int64;
begin
  Result := false;
  LFBIS := lfbisstr;
  if check_land(copy(trim(LFBIS), 1, 2)) then
  begin
    Result := true;
    exit;
  end;
  Setlength(LFBIS, length(LFBIS));
  if length(LFBIS) <> 7 then
    exit;
  if (TryStrToInt64(copy(LFBIS, 2, 6), tmp) = false) then
    exit;
  xsum := giveLfbisAlphaZahl(LFBIS[1]) * 7;
  xsum := xsum + StrToInt(LFBIS[2]) * 6;
  xsum := xsum + StrToInt(LFBIS[3]) * 5;
  xsum := xsum + StrToInt(LFBIS[4]) * 4;
  xsum := xsum + StrToInt(LFBIS[5]) * 3;
  xsum := xsum + StrToInt(LFBIS[6]) * 2;
  csum := (11 - xsum Mod 11) Mod 10;
  if (csum <> StrToInt(LFBIS[7])) then
  begin
    Result := false;
    exit;
  end;
  Result := true;
end;

function Tdm_main.giveLfbisAlphaZahl(zeichen: char): integer;
begin
  Result := StrToIntDef(zeichen, -1);
  if Result = -1 then
  begin
    case zeichen of
      'A':
        Result := 10;
      'B':
        Result := 11;
      'C':
        Result := 12;
      'D':
        Result := 13;
      'E':
        Result := 14;
      'F':
        Result := 15;
      'G':
        Result := 16;
      'H':
        Result := 17;
      'I':
        Result := 18;
      'J':
        Result := 19;
      'K':
        Result := 20;
      'L':
        Result := 21;
      'M':
        Result := 22;
      'N':
        Result := 23;
      'O':
        Result := 24;
      'P':
        Result := 25;
      'Q':
        Result := 26;
      'R':
        Result := 27;
      'S':
        Result := 28;
      'T':
        Result := 29;
      'U':
        Result := 30;
      'V':
        Result := 31;
      'W':
        Result := 32;
      'X':
        Result := 33;
      'Y':
        Result := 34;
      'Z':
        Result := 35;
    end;
  end;
end;

procedure Tdm_main.Berechne_Datum(
  Datum_alt: String;
  Var Monate: string;
  Var Tage: string);
var
  Jahr, Mon, tag: word;
  Jahr_now, Mon_now, tag_now: word;
  dif_Jahr, dif_Mon, dif_tag: integer;
begin
  try
    if now < strtodate(Datum_alt) then { Prüfe ob Datum_alt größer aktuellem Datum ist }
    begin
      Monate := '-1';
      Tage := '-1';
      exit;
    end;
    DecodeDate(strtodate(Datum_alt), Jahr, Mon, tag);
    DecodeDate(now, Jahr_now, Mon_now, tag_now);
    dif_Jahr := Jahr_now - Jahr;
    dif_Mon := Mon_now - Mon;
    dif_tag := tag_now - tag;
    if dif_tag < 0 then
    begin
      dif_Mon := dif_Mon - 1;
      dif_tag := dif_tag + 30;
    end;
    if dif_Mon < 0 then
    begin
      dif_Jahr := dif_Jahr - 1;
      dif_Mon := dif_Mon + 12;
    end;
    Monate := IntToStr(dif_Mon + (dif_Jahr * 12));
    Tage := IntToStr(dif_tag);
    // tage:='1';
  except
    Monate := '-1'; // Im Fehlerfall gib -1 zurück
    Tage := '-1';
  end;
end;

function Tdm_main.prepare_LFBIS(LFBIS: string): string;
begin
  if trim(LFBIS) = '' then
  begin
    Result := '';
  end;
  LFBIS := trim(LFBIS);
  LFBIS := copy(LFBIS, 1, 7);
  if length(LFBIS) < 7 then
    LFBIS := '0' + LFBIS;
  if length(LFBIS) < 7 then
    LFBIS := '0' + LFBIS;
  if length(LFBIS) < 7 then
    LFBIS := '0' + LFBIS;
  if length(LFBIS) < 7 then
    LFBIS := '0' + LFBIS;
  if length(LFBIS) < 7 then
    LFBIS := '0' + LFBIS;
  if trim(LFBIS) = '00000' then
    LFBIS := '';
  Result := LFBIS;
end;

function Tdm_main.AZ_Pruefung(TEXT: string): boolean;
var
  i: integer;
begin
  i := length(TEXT);
  Result := false;
  repeat
    if CharInSet(TEXT[i], ['A' .. 'Z']) then
    begin
      i := i + 1
    end
    else
    begin
      Result := false;
      exit;
    end;
  until i = length(TEXT) + 1;
end;

function Tdm_main.Null_Neun_Zahlen_Pruefung(TEXT: string): boolean;
var
  i: integer;
begin
  i := 1;
  Result := true;
  repeat
    if CharInSet(TEXT[i], ['0' .. '9']) then
    begin
      i := i + 1
    end
    else
    begin
      Result := false;
      exit;
    end;
  until i = length(TEXT) + 1;
end;

function Tdm_main.Null_Neun_Zahlen_AZ_Pruefung(TEXT: string): boolean;
var
  i: integer;
begin
  i := 1;
  Result := true;
  repeat
    if (CharInSet(TEXT[i], ['0' .. '9'])) or (CharInSet(TEXT[i], ['A' .. 'Z', 'a' .. 'z'])) then
    begin
      i := i + 1
    end
    else
    begin
      Result := false;
      exit;
    end;
  until i = length(TEXT) + 1;
end;

function Tdm_main.CHECK_DATUM_FORMAT_TTMMJJJJ(tt, mm, yyyy: word): boolean;
var
  datum1: tdatetime;
begin
  Result := true;
  if tryencodedate(yyyy, mm, tt, datum1) = false then
  begin
    Result := false;
    exit;
  end;
end;

procedure Tdm_main.DataModuleCreate(Sender: TObject);
begin
  FConfig := TConfig.Default;
  FConfig.ReadConfig;

  Para_Color_Balken_unten := 'clActiveBorder';
  Para_Color_Balken_links := 'clActiveBorder';
  Para_Color_Balken_rechts := 'clActiveBorder';

  QSys.Open;
end;

procedure Tdm_main.FBC_MAINBeforeConnect(Sender: TObject);
begin
  FBC_MAIN.Params.Clear;
  FBC_MAIN.Params.Add('Server=' + FConfig.DatabaseServer);
  FBC_MAIN.Params.DriverID := FConfig.DatabaseDriverID;
  FBC_MAIN.Params.Database := FConfig.Database;
  FBC_MAIN.Params.username := FConfig.DatabaseUserName;
  FBC_MAIN.Params.Password := FConfig.DatabasePassword;
end;

function Tdm_main.GetSystemKZ: string;
begin
  Result := QSysSYSTEMKZ.asstring;
end;

procedure Tdm_main.quBetriebssucheCalcFields(DataSet: TDataSet);
begin
  if quBetriebssucheFlag.AsInteger <> 1 then
  begin
    quBetriebssucheFlagHTML.AsString := '';
  end else
  begin
    //MarkierungWert enthält den CSS class name der jeweiligen Markierung
    quBetriebssucheFlagHTML.AsString := Format( '<div class="%s" title="%s">&nbsp;</div>',
    [quBetriebssucheMarkierungWert.AsString, quBetriebssucheMarkierungBeschreibung.AsString]);
  end;
end;

end.
