﻿unit Teamfenster;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs,Forms.Base, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQRegion, IWCGJQButton, Vcl.Imaging.jpeg,
  IWCompExtCtrls, IWVCLComponent, IWBaseLayoutComponent, IWBaseContainerLayout,
  IWContainerLayout, IWLayoutMgrForm, IWCGJQImage, IWHTMLControls, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component , IWCGJQLabel;

type
  TTeamFensterForm = class(TFormBase)
    jqrMid: TIWCGJQRegion;
    IWCGJQImage1: TIWCGJQImage;
    iwlUeberschrift: TIWCGJQLabel;
    iwliKoepfe: TIWList;
    iwlPartnerfirmen: TIWCGJQLabel;
    iwliPartnerfirmen: TIWList;
    IWCGJQRegion1: TIWCGJQRegion;
    iwrLeft: TIWCGJQRegion;
    iwrRight: TIWCGJQRegion;
    procedure TeamFensterOnCreate(Sender: TObject);
  private
    { Private-Deklarationen }
  public
    { Public-Deklarationen }
  end;

implementation

{$R *.dfm}

procedure TTeamFensterForm.TeamFensterOnCreate(Sender: TObject);
begin
  iwlUeberschrift.Caption := 'Die wichtigsten Köpfe im ELKE-Universum';
  iwliKoepfe.Items.Add('Norbert Mitteregger (Owner / Projektleitung / DB)');
  iwliKoepfe.Items.Add('Olaf Monien (REST / DB / IW)');
  iwliKoepfe.Items.Add('Wolfgang Kremser (PVP / Entwicklungsleitung Moped / QS)');
  iwliKoepfe.Items.Add('Lukas Schuchlenz (IW)');
  iwliKoepfe.Items.Add('Andreas Wöhrer (Moped)');
  iwliKoepfe.Items.Add('Florian Fellner (Moped)');
  iwliKoepfe.Items.Add('Christoph Gruber (IW) ');
  iwliKoepfe.Items.Add('Killian Hohnhold (Technik)');

  iwlPartnerfirmen.Caption := 'Partnerfirmen';
  iwliPartnerfirmen.Items.Add('Wolfgang Kremser EDV Dienstleistungen');
  iwliPartnerfirmen.Items.Add('Unternehmensberatung Monien - Developer Experts');
  iwliPartnerfirmen.Items.Add('Systemrocket GmbH');

end;

end.
