﻿unit CRUDGridFrame;

interface

uses
  SysUtils, Classes, Controls, Forms, IWApplication,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWCGJQRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQGrid, IWCGJQButton, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  FireDAC.Comp.Client, IWCGJQDialog, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IW.HTTP.Reply, System.Net.HttpClient, Data.DB, IWCGFrame;

type
  TCallback = procedure of object;

  /// <summary>
  /// Diese Klasse kann als Vorlage für ein Frame verwendet werden, in dem
  /// Objekte angezeigt, hinzugefügt, geändert und gelöscht werden. <br />Dem
  /// jqdpGrid muss in der vererbten Klasse ein DataSet/DataSource(z.b.
  /// TFDQuery) hinzugefügt werden. Außerdem müssen Spalten zum Grid
  /// hinzugefügt werden. Die einzelnen Funktionen müssen einzeln enabled
  /// werden <br />Ohne enableAbfragen wird keine Funktion funktionieren, da
  /// die Query für sie benötigt wird. <br />
  /// </summary>
  TCRUDGrid = class(TIWCGJQFrame)
    IWFrameRegion: TIWCGJQRegion;
    iwrMid: TIWCGJQRegion;
    iwrTop: TIWCGJQRegion;
    jqgGrid: TIWCGJQGrid;
    jqbAbfragen: TIWCGJQButton;
    iwrSuchen: TIWCGJQRegion;
    iwrButtons: TIWCGJQRegion;
    jqbNeu: TIWCGJQButton;
    jqbAendern: TIWCGJQButton;
    jqbLoeschen: TIWCGJQButton;
    jqdDialog: TIWCGJQDialog;
    jqdpGrid: TIWCGJQGridDataSetProvider;
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqgGridJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
    procedure jqgGridJQGridOptionsGridComplete(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    query: TFDQuery;
    neuTitle: String;
    aendernTitle: String;
    loeschenTitle: String;

    FBookmark: TArray<Byte>;

    FAbfragenBestaetigt: TCallback;
    FInitNeu: TCallback;
    FNeuBestaetigt: TCallback;
    FInitAendern: TCallback;
    FAendernBestaetigt: TCallback;
    FLoeschenBestaetigt: TCallback;

    procedure loeschenOnEvent(Sender: TObject; AParams: TStringList);
    procedure jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
    procedure neuOnEvent(Sender: TObject; AURLParams: TStringList);
    procedure aendernOnEvent(Sender: TObject; AURLParams: TStringList);
  protected

    /// <summary>
    /// Wird in einer Message angezeigt, wenn der User versucht etwas zu
    /// ändern/ löschen, wenn keine Reihe ausgewählt ist. Hat einen
    /// Default-Wert.
    /// </summary>
    keineReiheAusgewaehlt: String;

    FAlert: TIWCGJQSweetAlert;

    Property Alert:TIWCGJQSweetAlert read FAlert;

    procedure SetBookmark;
    procedure GotoBookmark;
    procedure ClearBookmark;

    procedure SelectRowForCurrentRecord;
    procedure SelectRecordForCurrentRow;

    procedure CheckRowSelected;

    procedure DoAbfragenBestaetigt;
    procedure DoAendernBestaetigt;
    procedure DoInitAendern;
    procedure DoInitNeu;
    procedure DoLoeschenBestaetigt;
    procedure DoNeuBestaetigt;

    /// <summary>
    /// Soll die Felder im Dialog zurücksetzten und alle Felder die beim
    /// ündern disabled werden wieder enablen. Wird von dieser Klasse
    /// automatisch aufgerufen.
    /// </summary>
    procedure ResetModal; Virtual; Abstract;

    /// <summary>
    /// Schaltet den Abfragen Button ein. Die query ist diejenige, die im
    /// GridDataSetProvider eingesetzt ist. onClick wird aufgerufen, sobald
    /// der User den Button drückt.
    /// </summary>
    procedure EnableAbfragen(query: TFDQuery; onClick: TCallback);

    /// <summary>
    /// Schaltet den Neu Button ein. Der Title wird im Dialog als Überschrift
    /// angezeigt. initNeu wird aufgerufen, nachdem der User den Button
    /// gedrückt hat. Wird im Dialog ein Grid verwendet, sollten die Queries
    /// dort aktiviert werden. Kann auch auf nil gesetzt werden. onBestaetigt
    /// wird aufgerufen, wenn der Dialog mit dem Ok-Knopf geschlossen wird.
    /// </summary>
    procedure EnableNeu(title: String; initNeu: TCallback; onBestaetigt: TCallback);

    /// <summary>
    /// Schaltet den Ändern Button ein. Der Title wird im Dialog aus
    /// Überschrift angezeigt. Die Query wird auf die momentan ausgewählte
    /// Reihe gesetzt. initAendern wird aufgerufen, nachdem der User den
    /// Button gedrückt hat. Zu diesem Zeitpunkt ist die Query schon auf der
    /// richtigen Reihe und kann sofort abgefragt werden. onBestaetigt wird
    /// aufgerufen, wenn der Dialog mit dem Ok-Knopf geschlossen wird.
    /// </summary>
    procedure EnableAendern(title: String; initAendern: TCallback; onBestaetigt: TCallback);

    /// <summary>
    /// Schaltet den Löschen Button ein. Der Title wird im JQSweetDialog als
    /// Frage angezigt. Die Query wird auf die momentan ausgewöhlte Reihe
    /// gesetzt. onBestaetigt wird aufgerufen, wenn der Dialog mit dem
    /// Ok-Knopf geschlossen wird.
    /// </summary>
    procedure EnableLoeschen(title: String; onBestaetigt: TCallback);

    /// <summary>
    /// Schaltet die Visibility der Region Suchen auf true. Muss
    /// eingeschaltet werden, wenn sich dort Textfelder o.ä. für die Suche
    /// befinden.
    /// </summary>
    procedure EnableSearch;

  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert); reintroduce;

  end;

implementation

uses Utility, JQ.Helpers.Grid, ELKE.Classes.RESTError;

{$R *.dfm}


procedure TCRUDGrid.CheckRowSelected;
begin
  if jqgGrid.JQGridOptions.SelRow = '' then
  begin
    FAlert.Error('Kein Datensatz markiert!');
    abort;
  end;
end;

procedure TCRUDGrid.ClearBookmark;
begin
  SetLength(FBookmark, 0);
end;

constructor TCRUDGrid.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert);
begin
  inherited Create(AOwner);

  FAlert := AAlert;

  SetLength(FBookmark, 0);

  jqgGrid.SetupDefaults(jqdpGrid);

  keineReiheAusgewaehlt := 'Es muss eine Reihe ausgewählt sein.';
end;

procedure TCRUDGrid.DoAbfragenBestaetigt;
begin
  if Assigned(FAbfragenBestaetigt) then
    FAbfragenBestaetigt;
end;

procedure TCRUDGrid.DoAendernBestaetigt;
begin
  if Assigned(FAendernBestaetigt) then
    FAendernBestaetigt;
end;

procedure TCRUDGrid.DoInitAendern;
begin
  if Assigned(FInitAendern) then
    FInitAendern;
end;

procedure TCRUDGrid.DoInitNeu;
begin
  if Assigned(FInitNeu) then
    FInitNeu;
end;

procedure TCRUDGrid.DoLoeschenBestaetigt;
begin
  if Assigned(FLoeschenBestaetigt) then
    FLoeschenBestaetigt;
end;

procedure TCRUDGrid.DoNeuBestaetigt;
begin
  if Assigned(FNeuBestaetigt) then
    FNeuBestaetigt;
end;

procedure TCRUDGrid.EnableSearch;
begin
  iwrSuchen.Visible := true;
end;

procedure TCRUDGrid.GotoBookmark;
begin
  if Length(FBookmark) > 0 then
  begin
    try
      jqdpGrid.DataSet.GotoBookmark(FBookmark);
    except
      jqdpGrid.DataSet.first;
    end;
    SelectRowForCurrentRecord;
  end;
end;

procedure TCRUDGrid.EnableAbfragen(query: TFDQuery; onClick: TCallback);
begin
  jqbAbfragen.Visible := true;
  Self.query := query;
  FAbfragenBestaetigt := onClick;
end;

procedure TCRUDGrid.EnableNeu(title: String; initNeu: TCallback; onBestaetigt: TCallback);
begin
  jqbNeu.Visible := true;
  neuTitle := title;
  FInitNeu := initNeu;
  FNeuBestaetigt := onBestaetigt;
end;

procedure TCRUDGrid.EnableAendern(title: String; initAendern: TCallback; onBestaetigt: TCallback);
begin
  jqbAendern.Visible := true;
  aendernTitle := title;
  FInitAendern := initAendern;
  FAendernBestaetigt := onBestaetigt;
end;

procedure TCRUDGrid.EnableLoeschen(title: String; onBestaetigt: TCallback);
begin
  jqbLoeschen.Visible := true;
  loeschenTitle := title;
  FLoeschenBestaetigt := onBestaetigt;
end;

procedure TCRUDGrid.jqbAbfragenOnClick(Sender: TObject; AParams: TStringList);
begin
  var
  LSelected := jqgGrid.JQGridOptions.SelRow;
  DoAbfragenBestaetigt;
  jqgGrid.JQGridOptions.SelRow := LSelected;
end;

procedure TCRUDGrid.jqbNeuOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  ResetModal;
  jqdDialog.JQDialogOptions.Buttons.Clear;

  DoInitNeu;

  jqdDialog.JQDialogOptions.title := neuTitle;
  jqb := jqdDialog.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Erstellen';
  jqb.onClick.OnEvent := neuOnEvent;
  jqb := jqdDialog.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.onClick.OnEvent := jqdAbbrechenEvent;
  jqdDialog.Visible := true;
end;

procedure TCRUDGrid.neuOnEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdDialog.Visible := false;
  DoNeuBestaetigt;
  DoAbfragenBestaetigt;
end;

procedure TCRUDGrid.SelectRecordForCurrentRow;
begin
  // Wenn der User eine Zeile ausw�hlt (also "navigiert"), dann soll der Dataset auf den entsprechenden Record navigieren
  var
  LKeyValue := jqgGrid.JQGridOptions.SelRow;
  var
  LKeyField := jqdpGrid.KeyFields;
  // Keyfields sind zwingend erforderlich! Es muss das PK Feld der Datenmenge eingetragen werden.
  // Oder ein Unique Key Feld, sonst kann die Navigation nicht funktionieren!
  if (LKeyField = '') or (LKeyValue = '') then
    abort
  else
  begin
    jqdpGrid.DataSet.Locate(LKeyField, LKeyValue, [loCaseInsensitive]);
  end;
end;

procedure TCRUDGrid.SelectRowForCurrentRecord;
begin
  // Selectiert bzw. navigiert zur aktuellen Zeile, die zum Record im Dataset geh�rt
  var
  LKeyField := jqdpGrid.KeyFields;
  if (LKeyField > '') and (jqdpGrid.DataSet.FindField(LKeyField) <> nil) then
  begin
    var
    KeyValue := jqdpGrid.DataSet.FieldByName(LKeyField).AsString;
    jqgGrid.JQGridOptions.SelRow := KeyValue;
    jqgGrid.JQGridOptions.SetSelection(KeyValue, true);
  end;
end;

procedure TCRUDGrid.SetBookmark;
begin
  FBookmark := jqdpGrid.DataSet.GetBookmark;
end;

procedure TCRUDGrid.jqbAendernOnClick(Sender: TObject; AParams: TStringList);
var
  jqb: TIWCGJQCustomDialogButton;
begin
  // Aktuelle Row merken, und im OnselectRowEvent wieder setzen
  SetBookmark;
  ResetModal;
  FInitAendern;
  jqdDialog.JQDialogOptions.Buttons.Clear;
  jqdDialog.JQDialogOptions.title := aendernTitle;
  jqb := jqdDialog.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Speichern';
  jqb.onClick.OnEvent := aendernOnEvent;
  jqb := jqdDialog.JQDialogOptions.Buttons.Add;
  jqb.Text := 'Abbrechen';
  jqb.onClick.OnEvent := jqdAbbrechenEvent;
  jqdDialog.Visible := true;
end;

procedure TCRUDGrid.aendernOnEvent(Sender: TObject; AURLParams: TStringList);
begin
  DoAendernBestaetigt;
  DoAbfragenBestaetigt;
  jqdDialog.Hide;
end;

procedure TCRUDGrid.jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
begin
  CheckRowSelected;
  FAlert.JQSweetAlertOptions.title := loeschenTitle;
  FAlert.JQSweetAlertOptions.AlertType := jqsatWarning;
  FAlert.JQSweetAlertOptions.ShowCancelButton := true;
  FAlert.JQSweetAlertOptions.ConfirmButtonColor := '#DD6B55';
  FAlert.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  FAlert.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  FAlert.JQSweetAlertOptions.OnBtnClick.OnEvent := loeschenOnEvent;
  FAlert.Show;
end;

procedure TCRUDGrid.loeschenOnEvent(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if isConfirmButton then
  begin
    DoLoeschenBestaetigt;
    DoAbfragenBestaetigt;
  end;
end;

procedure TCRUDGrid.jqdAbbrechenEvent(Sender: TObject; AURLParams: TStringList);
begin
  jqdDialog.Hide;
end;

procedure TCRUDGrid.jqgGridJQGridOptionsGridComplete(Sender: TObject; AParams: TStringList);
begin
  SelectRowForCurrentRecord;
end;

procedure TCRUDGrid.jqgGridJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
begin
  SelectRecordForCurrentRow;
end;

end.
