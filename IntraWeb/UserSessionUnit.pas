unit UserSessionUnit;

{
  This is a DataModule where you can add components or declare fields that are specific to
  ONE user. Instead of creating global variables, it is better to use this datamodule. You can then
  access the it using UserSession.
}
interface

uses
  IWUserSessionBase, SysUtils, Classes, dmmain, IdBaseComponent, IdComponent,
  IdServerIOHandler, IdSSL, IdSSLOpenSSL, Vcl.ShareContract,
  Funktionen, ELKE.Classes.PVP.Token, ELKE.Classes.Generated, ELKE.Classes.Rest.Client,
  Config,
  Modules.Kontrolle;

type
  TIWUserSession = class(TIWUserSessionBase)
    procedure IWUserSessionBaseCreate(Sender: TObject);
    procedure IWUserSessionBaseDestroy(Sender: TObject);
  private
    FELKERest: TELKERestClient;
    FPVPToken: TPVPToken;
    FUser: TUser;
    FError: string;
    FFunktionenManager: TFunktionenManager;
  public
    { Public declarations }
    dm_main: Tdm_main;
    property ELKERest: TElkeRestClient read FELKERest;
    property FunktionenManager: TFunktionenManager read FFunktionenManager;
    property PVPToken: TPVPToken read FPVPToken write FPVPToken;
    property User: TUser read FUser write FUser;
    property Error: string read FError write FError;
  end;

implementation

uses inifiles, XData.Client, DX.Utils.Logger, IWExceptionLogger, ServerController, FireDAC.Stan.Param;

{$R *.dfm}


procedure TIWUserSession.IWUserSessionBaseCreate(Sender: TObject);
begin
  FError := '';
  dm_main := Tdm_main.Create(self);

  try
    FELKERest := TELKERestClient.Create(TConfig.Default.RESTUri);
    FFunktionenManager := TFunktionenManager.Create;

    FPVPToken := TPVPToken.Create(Webapplication.Request.RawHeaders);
    FUser := ELKERest.GetUser;
    Assert(Assigned(FUser), 'ELKERest.GetUser hat keinen User geliefert - FUser ist nil');
    // Setzt den BLDCode und die interne UserId des angemeldeten Users
    dm_main.qu_user_gid.Close;
    dm_main.qu_user_gid.ParamByName('gid').AsString := User.Userguid;
    dm_main.qu_user_gid.Open;
    dm_main.UserId := User.Id;
    dm_main.BLDCODE := User.Bundesland.BLDCODE;

  except
    on E: Exception do
    begin
      FreeAndNil(FUser);
      DXLog('Exception im Session Create: ' + E.Message);
      DXLog('Request Headers: ' + Webapplication.Request.RawHeaders.Text);
      IWServerController.ExceptionLogger.LogException(Sender, E, Webapplication.Request);
      if E is EXDataOperationRequestException then
      begin
        FError := EXDataOperationRequestException(E).ErrorMessage;
      end
      else
      begin
        FError := 'Fehler beim Zugriff auf das REST-Backend!';
      end;
    end;
  end;
end;

procedure TIWUserSession.IWUserSessionBaseDestroy(Sender: TObject);
begin
  FreeAndNil(FPVPToken);
  FreeAndNil(FELKERest);
  FreeAndNil(FFunktionenManager);
  FreeAndNil(dm_main);
end;

end.
