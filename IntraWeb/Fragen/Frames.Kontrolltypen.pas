unit Frames.Kontrolltypen;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBase<PERSON>omponent,
  IWBaseHT<PERSON><PERSON>omponent, IWBase<PERSON>ML40Component, <PERSON>WCGJQ<PERSON>omp, IWCGJQSweet<PERSON><PERSON>t,
  IWApplication, IWCGJQEdit, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCompListbox, IWCGJQRegion, IWCGJQCombobox, IWCGJQLabel, IWCGJQDropDown;

type
  TFrameKontrolltypen = class(TCRUDGrid)
    quKontrolltypen: TFDQuery;
    dropDown: TIWCGJQDropDown;
    quChecklisten: TFDQuery;
    DataSourceDropDown: TDataSource;
    quKontrolltypenID: TStringField;
    quKontrolltypenDISPLAY_NAME: TStringField;
    quChecklistenBEZEICHNUNG: TStringField;
    quChecklistenVERSIONSTEXT: TStringField;
    quChecklistenGUELTIG_AB: TDateField;
    quChecklistenGUELTIG_BIS: TDateField;
  private
    { Private declarations }

  protected
    procedure ResetModal; override;
  public
    { Public declarations }
    constructor Create(
      AOwner: TComponent;
      AAlert: TIWCGJQSweetAlert);
    procedure KontrolltypenAbfragen;
    procedure ChecklistenAbfragen;
  end;

implementation

uses dmmain, Utility, ServerController, ELKE.Classes.Generated, System.DateUtils, JQ.Helpers.Grid,
  ELKE.Classes.RESTError;

{$R *.dfm}


constructor TFrameKontrolltypen.Create(
  AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert);
begin
  inherited Create(AOwner, AAlert);

  EnableAbfragen(quKontrolltypen, ChecklistenAbfragen);

  FAlert := AAlert;
end;

procedure TFrameKontrolltypen.KontrolltypenAbfragen;
begin
  quKontrolltypen.Close;
  quKontrolltypen.ParamByName('BLDCODE').AsInteger := UserSession.User.Bundesland.Bldcode;
  quKontrolltypen.Open;
end;

procedure TFrameKontrolltypen.ResetModal;
begin
  inherited;
  //nothing
end;

procedure TFrameKontrolltypen.ChecklistenAbfragen;
begin
  quChecklisten.Close;
  quChecklisten.ParamByName('BKBTYP').AsString := dropDown.Val.Split([';'])[0];
  quChecklisten.ParamByName('KONTROLLTYP').AsString := dropDown.Val.Split([';'])[1];
  quChecklisten.Open;
end;

end.
