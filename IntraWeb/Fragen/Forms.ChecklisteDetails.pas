﻿unit Forms.ChecklisteDetails;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Forms.Base, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCompExtCtrls, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQRegion, IWCGJQButton, Vcl.Imaging.jpeg, IWCGJQEdit, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS,
  FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWHTMLControls,
  IWCompListbox, Classes.Fragen, Generics.Collections,
  IWCGJQDialog, IWCGJQMultiSelect, IWCompCheckbox,
  IWCGPanelList, IWCGJQComp, IWCGJQSweetAlert, Dialogs.FrageBearbeiten, IWAppForm,
  IWCGJQGrid, IWCGJQGridCustomProvider, IWCGJQGridCollectionProvider, IWCGJQRadio,
  System.Generics.Collections, IWCGJQLabel, IWCGJQSelectableList, IWCompButton, IWCGJQGridDataSetProvider,
  Modules.Checklisten;

type
  TChecklistenDetails = class(TFormBase)
    RegionChecklistendetails: TIWCGJQRegion;
    IWLabel5: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    iwlBezeichnung: TIWCGJQLabel;
    iwlGueltigAb: TIWCGJQLabel;
    iwlGueltigBis: TIWCGJQLabel;
    iwlVersion: TIWCGJQLabel;
    ButtonFrageHinzufuegen: TIWCGJQButton;
    IWLabel12: TIWCGJQLabel;
    EditQuelleExtern: TIWCGJQEdit;
    ButtonChecklisteSpeichern: TIWCGJQButton;
    iwlBearbeitenVerboten: TIWCGJQLabel;
    IWLabel1: TIWCGJQLabel;
    iwlVersionstext: TIWCGJQLabel;
    ButtonUnterfrageHinzufuegen: TIWCGJQButton;
    ButtonFrageVerschieben: TIWCGJQButton;
    ButtonFrageBearbeiten: TIWCGJQButton;
    ButtonFrageLoeschen: TIWCGJQButton;
    GridFragen: TIWCGJQGrid;
    ProviderFragen: TIWCGJQGridDataSetProvider;
    LabelAnzahlBewertungen: TIWCGJQLabel;
    DSKontrolltypen: TDataSource;
    AlertClose: TIWCGJQSweetAlert;
    ButtonFrageDuplizieren: TIWCGJQButton;
    procedure DialogVerschiebenJQDialogOptionsButtons0Click(Sender: TObject; AParams: TStringList);
    procedure AlertNochNichtGespeichertJQSweetAlertOptionsBtnClick(Sender: TObject; AParams: TStringList);
    procedure IWButton1Click(Sender: TObject);
    procedure GridFragenJQGridOptionsGridComplete(Sender: TObject; AParams: TStringList);

    procedure ButtonChecklisteSpeichernOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonFrageLoeschenOnClick(Sender: TObject; AParams: TStringList);

    procedure ButtonFrageHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonUnterfrageHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonFrageBearbeitenOnClick(Sender: TObject; AParams: TStringList);

    procedure ButtonFrageVerschiebenOnClick(Sender: TObject; AParams: TStringList);

    procedure ButtonBeendenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure GridFragenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
    procedure ButtonFrageDuplizierenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);

  private
    { Private declarations }
    DM: TDMChecklisten;
    // Die aktuell ausgewählte Frage
    FAktuelleFrageID: string;

    procedure DisableButtons;
    procedure UpdateButtons;
    procedure UpdateDetailsCheckliste;

    procedure SetBookmark;
    procedure GotoBookmark;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; ADM: TDMChecklisten); reintroduce;
    destructor Destroy; override;
  end;

implementation

uses dmmain, Utility, Dialogs.FrageVerschieben, BewertungenFrame, KontrollbereicheFrame, FormatierungenFrame,
  IWCGJSSuperObject, System.DateUtils, JQ.Helpers.Alert;

{$R *.dfm}


constructor TChecklistenDetails.Create(AOwner: TComponent; ADM: TDMChecklisten);
begin
  inherited Create(AOwner);
  DM := ADM;

  // Grid Setup
  GridFragen.JQGridOptions.TreeGrid := True;
  GridFragen.JQGridOptions.TreeGridModel := gtgmAdjacency;
  GridFragen.JQGridOptions.ExpandColumn := 'FragenNr';
  // Grid Provider Setup
  ProviderFragen.KeyFields := 'ID';
  ProviderFragen.TreeOptions.IsLeafFieldName := 'isleaf';
  ProviderFragen.TreeOptions.LevelFieldName := 'level';
  ProviderFragen.TreeOptions.ParentFieldName := 'ID_parent';
  ProviderFragen.DataSet := DM.MTFragen;

  GridFragen.JQGridProvider := ProviderFragen;

  DisableButtons;

  DM.ChecklisteLaden;
  UpdateDetailsCheckliste;

  // Dialoge vorab erzeugen
  TDialogFrageVerschieben.Create(Self, DM);
  TDialogFrageBearbeiten.Create(Self, DM);
end;

destructor TChecklistenDetails.Destroy;
begin
  inherited;
end;

procedure TChecklistenDetails.DisableButtons;
begin
  ButtonUnterfrageHinzufuegen.Enabled := false;
  ButtonFrageVerschieben.Enabled := false;
  ButtonFrageBearbeiten.Enabled := false;
  ButtonFrageLoeschen.Enabled := false;
  ButtonFrageHinzufuegen.Enabled := false;
  ButtonFrageDuplizieren.Enabled := false;
  ButtonChecklisteSpeichern.Enabled := false;
end;

procedure TChecklistenDetails.AlertNochNichtGespeichertJQSweetAlertOptionsBtnClick(
  Sender: TObject;
  AParams: TStringList);
begin
  if AParams.Values['isConfirm'] = 'true' then
  begin
    DM.AenderungenVerwerfen(false);
    ButtonBeendenJQButtonOptionsClick(Sender, nil);
  end;
end;

procedure TChecklistenDetails.ButtonBeendenJQButtonOptionsClick(
  Sender: TObject;
  AParams: TStringList);
begin
  if DM.HatBearbeitet then
  begin
    AlertClose.Confirm('Es wurde noch nicht gespeichert. Alle Änderungen verwerfen? ',
      procedure
      begin
        Release;
      end);
  end
  else
    Release;
end;

procedure TChecklistenDetails.ButtonChecklisteSpeichernOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  SetBookmark;
  if not DM.BearbeitenErlaubt then
  begin
    Alert.Error('Das Bearbeiten ist nicht erlaubt!');
    Abort;
  end;
  try
    DM.ChecklisteSpeichern(EditQuelleExtern.Text);
    Alert.info('Fragen erfolgreich gespeichert.');
  except
    Alert.Error('Die Fragen konnten nicht gespeichert werden!');
  end;
end;

procedure TChecklistenDetails.ButtonFrageBearbeitenOnClick(Sender: TObject; AParams: TStringList);
var
  LAktuelleFrage: TFrage;
  LDialogFrageBearbeiten: TDialogFrageBearbeiten;
begin
  SetBookmark;
  if not DM.BearbeitenErlaubt then
    Abort;

  LAktuelleFrage := DM.GetFrageForRowID(GridFragen.JQGridOptions.SelRow);
  if LAktuelleFrage = nil then
    Abort;

  LDialogFrageBearbeiten := TDialogFrageBearbeiten.Create(Self, DM);
  LDialogFrageBearbeiten.AktuelleFrage := LAktuelleFrage;
  LDialogFrageBearbeiten.Modus := TDialogFrageBearbeiten.TModus.Bearbeiten;
  LDialogFrageBearbeiten.Show(True, True,
    procedure
    begin
      GridFragen.JQGridOptions.ReloadGrid;
      LDialogFrageBearbeiten.Close;
    end);
end;

procedure TChecklistenDetails.ButtonFrageDuplizierenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  SetBookmark;
  try
    if not DM.BearbeitenErlaubt then
      Abort;

    var
    LSelectedId := GridFragen.JQGridOptions.SelRow;
    DM.FrageDuplizieren(LSelectedId);
  finally
    GridFragen.JQGridOptions.ReloadGrid;
    GotoBookmark;
  end;
end;

procedure TChecklistenDetails.ButtonFrageHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
var
  LAktuelleFrage: TFrage;
  LDialogFrageBearbeiten: TDialogFrageBearbeiten;
begin
  inherited;
  SetBookmark;
  if not DM.BearbeitenErlaubt then
    Abort;
  LAktuelleFrage := TFrage.Create;

  LDialogFrageBearbeiten := TDialogFrageBearbeiten.Create(Self, DM);
  LDialogFrageBearbeiten.AktuelleFrage := LAktuelleFrage;
  LDialogFrageBearbeiten.Modus := TDialogFrageBearbeiten.TModus.NeueFrage;
  LDialogFrageBearbeiten.Show(True, True,
    procedure
    begin
      GridFragen.JQGridOptions.ReloadGrid;
      LDialogFrageBearbeiten.Close;
    end
    );
end;

// ******************************Frage Loeschen**********************************

procedure TChecklistenDetails.ButtonFrageLoeschenOnClick(Sender: TObject; AParams: TStringList);
begin
  if not DM.BearbeitenErlaubt then
    Abort;

  var
  LSelectedId := GridFragen.JQGridOptions.SelRow;
  if LSelectedId = '' then
    Abort;
  var
  LAnzahl := DM.FrageLoeschen(LSelectedId);

  if LAnzahl = 0 then
    Alert.Error('Es wurde keine Frage ausgewählt/gelöscht!')
  else if LAnzahl = 1 then
    Alert.Success('Es wurde eine Frage gelöscht!')
  else if LAnzahl > 1 then
    Alert.Success('Es wurden ' + LAnzahl.ToString + ' Fragen gelöscht!');
end;

procedure TChecklistenDetails.ButtonUnterfrageHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
var
  LDialogFrageBearbeiten: TDialogFrageBearbeiten;
begin
  SetBookmark;

  if not DM.BearbeitenErlaubt then
    Abort;
  var
  LSelektierteFrage := DM.GetFrageForRowID(GridFragen.JQGridOptions.SelRow);
  if LSelektierteFrage = nil then
    Abort;

  var
  LNeueFrage := TFrage.Create;

  LDialogFrageBearbeiten := TDialogFrageBearbeiten.Create(Self, DM);
  LDialogFrageBearbeiten.SelektierteFrage := LSelektierteFrage;
  LDialogFrageBearbeiten.AktuelleFrage := LNeueFrage;
  LDialogFrageBearbeiten.Modus := TDialogFrageBearbeiten.TModus.NeueUnterfrage;
  LDialogFrageBearbeiten.Show(True, True,
    procedure
    begin
      GridFragen.JQGridOptions.ReloadGrid;
      LDialogFrageBearbeiten.Close;
    end
    );
end;

// ****************************Frage Verschieben*********************************
procedure TChecklistenDetails.ButtonFrageVerschiebenOnClick(Sender: TObject; AParams: TStringList);
begin
  SetBookmark;
  if not DM.BearbeitenErlaubt then
    Abort;
  var
  LAktuelleFrage := DM.GetFrageForRowID(GridFragen.JQGridOptions.SelRow);
  if LAktuelleFrage = nil then
    Abort;

  var
  LDialog := TDialogFrageVerschieben.Create(Self, DM);
  LDialog.MitZuVerschiebenderFrageInitialisieren(LAktuelleFrage);
  LDialog.Show(True);
end;

procedure TChecklistenDetails.GotoBookmark;
begin
  GridFragen.JQGridOptions.SelRow := FAktuelleFrageID;
  GridFragen.JQGridOptions.SetSelection(FAktuelleFrageID, True);
end;

procedure TChecklistenDetails.GridFragenJQGridOptionsGridComplete(Sender: TObject;
AParams:
  TStringList);
var
  LRowIds: TStrings;
  i: integer;
  Cls: ISuperObject;
  Props: ISuperObject;
begin
  inherited;
  GridFragen.JQGridOptions.TreeGridExpandAllNodes;

  // Zusatzinfos per Hint einblenden
  // Todo: Sinnvolle Infos
  LRowIds := TStringList.Create;
  try
    GridFragen.GetRowIds(LRowIds);
    for i := 0 to LRowIds.Count - 1 do
    begin
      Cls := SO();
      // Cls.S[''white-space']:= 'normal'; // To display wrappable, don't need for this case.

      Props := SO();
      Props.S['title'] := 'Zusatzinfos zur Frage ID' + LRowIds[i]; // HINT

      GridFragen.JQGridOptions.SetCell(LRowIds[i], 'Info', '', Cls, Props);
      // Only for the Field/Column 'Info', if you pass an empty string in AData, it doesn't change the content.
    end;
  finally
    LRowIds.Free;
  end;

end;

procedure TChecklistenDetails.GridFragenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
  inherited;
  GridFragen.JQGridOptions.TreeGridExpandAllNodes;
  GotoBookmark;
end;

procedure TChecklistenDetails.DialogVerschiebenJQDialogOptionsButtons0Click(
  Sender: TObject;
AParams:
  TStringList);
begin
  (*
    var
    LOberhalbVerschieben := RadioGroupVerschiebeModus.ItemIndex = 0;

    if ListboxFragenVerschieben.ItemIndex >= 0 then
    begin
    LSelectedValue := ListboxFragenVerschieben.Items.ValueFromIndex[ListboxFragenVerschieben.ItemIndex];
    end
    else
    Abort;

    LZielFrage := Nil;
    for var LFragenContainer in container do
    begin
    if LFragenContainer.frage.id.ToString = LSelectedValue then
    begin
    LZielFrage := LFragenContainer;
    Break;
    end;
    end;
    if LZielFrage = Nil then
    Abort;

    hatBearbeitet := true;


    container.Remove(AktuellSelektierteFrage);

    var
    LAlsUnterfrage := RadioGroupVerschiebeModus.ItemIndex = 2;
    if LAlsUnterfrage then
    begin
    LZielFrage.AddUnterfrage(AktuellSelektierteFrage, false);
    AktuellSelektierteFrage.frage.uebergeordneteFrageId := LZielFrage.frage.id;
    i := container.IndexOf(LZielFrage);
    container.Insert(i + 1, AktuellSelektierteFrage);
    // Ein Level tiefer wie die Zielfrage
    AktuellSelektierteFrage.UpdateInhalt(LZielFrage.fragenLevel + 1);
    end
    else
    begin
    // oberhalb verschieben => index der Zielfrage, die dann runter rutscht
    i := container.IndexOf(LZielFrage);
    if not LOberhalbVerschieben then
    begin
    // Unterhalb verschieben => Index nach der Zielfrage
    i := i + 1;
    end;
    container.Insert(i, AktuellSelektierteFrage);
    // Gleiches Fragenlevel wie die Zielfrage
    AktuellSelektierteFrage.UpdateInhalt(LZielFrage.fragenLevel);
    end;
  *)

end;

procedure TChecklistenDetails.UpdateButtons;
begin
  ButtonChecklisteSpeichern.Enabled := DM.BearbeitenErlaubt;
  ButtonFrageHinzufuegen.Enabled := DM.BearbeitenErlaubt;
  ButtonFrageDuplizieren.Enabled := DM.BearbeitenErlaubt;
  ButtonUnterfrageHinzufuegen.Enabled := DM.BearbeitenErlaubt;
  ButtonFrageLoeschen.Enabled := DM.BearbeitenErlaubt;
  ButtonFrageBearbeiten.Enabled := DM.BearbeitenErlaubt;
  ButtonFrageVerschieben.Enabled := DM.BearbeitenErlaubt;
  iwlBearbeitenVerboten.Visible := not DM.BearbeitenErlaubt;
end;

procedure TChecklistenDetails.UpdateDetailsCheckliste;
begin
  iwlBezeichnung.Caption := DM.quChecklistenBEZEICHNUNG.AsString;
  iwlVersion.Caption := DM.quChecklistenVERSION.AsString;
  iwlVersionstext.Caption := DM.quChecklistenVERSIONSTEXT.AsString;
  iwlGueltigAb.Caption := DM.quChecklistenGUELTIG_AB.AsString;
  iwlGueltigBis.Caption := DM.quChecklistenGUELTIG_BIS.AsString;

  var
  LAnzahlBewertungen := DM.AnzahlBewerteteFragen;
  if LAnzahlBewertungen > 0 then
  begin
    LabelAnzahlBewertungen.Caption := Format('Es liegen %d bewertete Fragen vor.', [LAnzahlBewertungen]);
  end
  else
  begin
    LabelAnzahlBewertungen.Caption := 'Es liegen noch keine bewerteten Fragen vor.';
  end;

  UpdateButtons;
end;

procedure TChecklistenDetails.IWButton1Click(Sender: TObject);
begin
  // Page refresh
end;

procedure TChecklistenDetails.SetBookmark;
begin
  FAktuelleFrageID := GridFragen.JQGridOptions.SelRow;
end;

end.
