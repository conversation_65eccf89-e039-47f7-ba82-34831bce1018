﻿unit FormatierungenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWApplication, IWCGJQEdit, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TFormatierungen = class(TCRUDGrid)
    quFormatierung: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqeCode: TIWCGJQEdit;
    jqeBeschreibung: TIWCGJQEdit;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure FormatierungenAbfragen;
  end;

var
  Formatierungen: TFormatierungen;

implementation

uses dmmain, Utility;

{$R *.dfm}

constructor TFormatierungen.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, AAlert);

  FAlert := AAlert;

  EnableAbfragen(quFormatierung, FormatierungenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neue Formatierung erstellen', Nil, NeuBestaetigt);
    EnableAendern('Formatierung ändern', InitAendern, AendernBestaetigt);
  end;
end;

procedure TFormatierungen.FormatierungenAbfragen;
begin
  RefreshQuery(quFormatierung);
end;

procedure TFormatierungen.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('code').AsString := jqeCode.Text;
  quNeu.ParamByName('beschreibung').AsString := jqeBeschreibung.Text;
  quNeu.Execute;
end;

procedure TFormatierungen.InitAendern;
begin
  jqeCode.Text := quFormatierung.FieldByName('code').AsString;
  jqeCode.Enabled := false;
  jqeBeschreibung.Text := quFormatierung.FieldByName('beschreibung').AsString;
end;

procedure TFormatierungen.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('code').AsString := jqeCode.Text;
  quAendern.ParamByName('beschreibung').AsString := jqeBeschreibung.Text;
  quAendern.Execute;
end;

procedure TFormatierungen.ResetModal;
begin
  jqeBeschreibung.Text := '';
  jqeCode.Text := '';
  jqeCode.Enabled := true;
end;

end.
