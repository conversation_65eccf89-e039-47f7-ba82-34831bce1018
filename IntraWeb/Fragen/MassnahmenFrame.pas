﻿unit MassnahmenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQ<PERSON>weet<PERSON><PERSON>t,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWApplication,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel,
  IWCGJQEdit, IWCGJQComboBox, IWCompListbox, Classes.Fragen, IWCGJQRegion, IWCGJQLabel, JQ.Helpers.ComboboxEx;

type
  TMassnahmen = class(TCRUDGrid)
    quMassnahmen: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    jqeLangtext: TIWCGJQEdit;
    jqeStandardfrist: TIWCGJQEdit;
    iwcbMassnahmenkatalog: TIWCGJQComboboxEx;
    quMassnahmenkatalog: TFDQuery;
    quMassnahmenid: TFDAutoIncField;
    quMassnahmenid_massnahmenkatalog: TIntegerField;
    quMassnahmenLangtext: TMemoField;
    quMassnahmenstandardfrist: TIntegerField;
    quMassnahmenbezeichnung: TStringField;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    idMassnahmenkatalog: integer;
    constructor Create(AOwner: TComponent;
      AAlert: TIWCGJQSweetAlert;
      bearbeitbar: Boolean);
    procedure MassnahmenAbfragen;
    function AusgewaehlteMassnahme: TMassnahme;
  end;

var
  Massnahmen: TMassnahmen;

implementation

uses dmmain, Utility;

{$R *.dfm}


constructor TMassnahmen.Create(AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert;
  bearbeitbar: Boolean);
begin
  inherited Create(AOwner, AAlert);

  FAlert := AAlert;
  Self.idMassnahmenkatalog := -1;

  EnableAbfragen(quMassnahmen, MassnahmenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neue Maßnahme erstellen', Nil, NeuBestaetigt);
    EnableAendern('Maßnahme ändern', InitAendern, AendernBestaetigt);
  end;

  RefreshQuery(quMassnahmenkatalog);
  while not quMassnahmenkatalog.Eof do
  begin
    iwcbMassnahmenkatalog.Items.Add(
      quMassnahmenkatalog.FieldByName('bezeichnung').AsString,
      quMassnahmenkatalog.FieldByName('id').AsString);
    quMassnahmenkatalog.Next;
  end;
  quMassnahmenkatalog.Close;
end;

procedure TMassnahmen.MassnahmenAbfragen;
begin
  quMassnahmen.Close;
  quMassnahmen.Prepare;
  if idMassnahmenkatalog = -1 then
  begin
    quMassnahmen.ParamByName('filter').AsBoolean := false;
  end
  else
  begin
    quMassnahmen.ParamByName('filter').AsBoolean := true;
    quMassnahmen.ParamByName('id_massnahmenkatalog').AsInteger := idMassnahmenkatalog;
  end;

  quMassnahmen.Active := true;
end;

procedure TMassnahmen.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('langtext').AsString := jqeLangtext.Text;
  quNeu.ParamByName('standardfrist').AsInteger :=
    StrToInt(jqeStandardfrist.Text);
  quNeu.ParamByName('id_massnahmenkatalog').AsInteger :=
    StrToInt(iwcbMassnahmenkatalog.SelectedItem.Value);
  quNeu.Execute;
end;

procedure TMassnahmen.InitAendern;
var
  index: integer;
begin
  jqeLangtext.Text := quMassnahmen.FieldByName('langtext').AsString;
  jqeStandardfrist.Text := quMassnahmen.FieldByName('standardfrist').AsString;
  index := iwcbMassnahmenkatalog.Items.IndexOf(quMassnahmen.FieldByName('bezeichnung').AsString);
  iwcbMassnahmenkatalog.ItemIndex := index;
  iwcbMassnahmenkatalog.Enabled := false;
end;

procedure TMassnahmen.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('id').AsInteger :=
    quMassnahmen.FieldByName('id').AsInteger;
  quAendern.ParamByName('langtext').AsString := jqeLangtext.Text;
  quAendern.ParamByName('standardfrist').AsInteger :=
    StrToInt(jqeStandardfrist.Text);
  quAendern.ParamByName('id_massnahmenkatalog').AsInteger := StrToInt(iwcbMassnahmenkatalog.SelectedItem.Value);
  quAendern.Execute;
end;

function TMassnahmen.AusgewaehlteMassnahme;
var
  massnahme: TMassnahme;
begin
  if not moveQueryToRow(quMassnahmen, jqgGrid) then
  begin
    Result := Nil;
    Exit;
  end;
  massnahme := TMassnahme.Create;
  try
    massnahme.id := quMassnahmen.FieldByName('id').AsInteger;
    massnahme.langtext := quMassnahmen.FieldByName('langtext').AsString;
    massnahme.standardfrist := quMassnahmen.FieldByName('standardfrist').AsInteger;
    massnahme.massnahmenKatalogId := quMassnahmen.FieldByName('id_massnahmenkatalog').AsInteger;
    Result := massnahme;
  except
    massnahme.Free;
    Result := Nil;
  end;
end;

procedure TMassnahmen.ResetModal;
begin
  jqeLangtext.Text := '';
  jqeStandardfrist.Text := '';
  iwcbMassnahmenkatalog.ItemIndex := -1;
  iwcbMassnahmenkatalog.Enabled := true;
end;

end.
