﻿unit Dialogs.FrageBearbeiten;

interface

uses
  System.SysUtils, System.Classes, System.Generics.Collections,
  Vcl.Controls, Vcl.Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer, IWHTML40Container, IWCGJQMultiSelect, IWCGJQEdit,
  IWCGJQButton, IWCGJQControl, IWCGPanelList, IWCompLabel, IWVCLBaseControl, Classes.Fragen, IWBaseHTMLControl,
  IWControl, IWCompListbox, IWCGJQDialog, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWApplication, IWCGJQCommon, IWCGJQRegion, IWCompMemo,
  IWCGJQMemo, IWBaseControl, IWCGJQHTMLEditor, IWCGJQComboBox, IWCGJQLabel,
  Dialogs.Base,
  Dialogs.FrageBewertungenBearbeiten,
  Modules.Checklisten, JQ.Helpers.ComboboxEx, IWCGJQCheckBox;

type
   //TDialogFrageBearbeiten = class(TDialogBase)
   TDialogFrageBearbeiten = class(TDialogBase<TDMChecklisten>)
    ComboFormatierung: TIWCGJQComboBoxEx;
    IWLabel1: TIWCGJQLabel;
    IWLabel11: TIWCGJQLabel;
    IWLabel13: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    ListBewertungen: TIWCGPanelList;
    ButtonBewertungHinzufuegen: TIWCGJQButton;
    EditFragennummer: TIWCGJQEdit;
    EditIdExtern: TIWCGJQEdit;
    EditTitelText: TIWCGJQEdit;
    SelectKontrollbereiche: TIWCGJQMultiSelect;
    MemoInfoText: TIWCGJQHTMLEditor;
    ButtonBewertungLoeschen: TIWCGJQButton;
    IWCGJQLabel1: TIWCGJQLabel;
    CheckboxCCrelevant: TIWCGJQCheckBoxEx;
    procedure IWFrameRegionCreate(Sender: TObject);
    procedure ButtonBewertungHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBewertungLoeschenOnClick(Sender: TObject; AParams: TStringList);

    procedure BewertungOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonOKClick(Sender: TObject; AParams: TStringList);
    procedure ButtonCancelClick(Sender: TObject; AParams: TStringList);

  public
    type
    TModus = (NeueFrage, NeueUnterfrage, Bearbeiten);

  private
    FAktuelleFrage: TFrage;
    FBearbeiten: boolean;
    FFragenBewertungSelected: integer;
    FModus: TModus;
    FSelektierteFrage: TFrage;

    procedure BewertungPruefen(ABewertung: TFragenBewertung);
    procedure BewertungLoeschen;
    procedure UpdateUI;
    procedure SetModus(const Value: TModus);
  public

    procedure InitializeControls; override;

    function GetFrage: TFrage;
    procedure DialogAbbrechen;
    property SelektierteFrage: TFrage read FSelektierteFrage write FSelektierteFrage;
    property AktuelleFrage: TFrage read FAktuelleFrage write FAktuelleFrage;
    property Modus: TModus read FModus write SetModus;
  end;

implementation

uses Utility, IWCGAsyncRender, BewertungenFrame, IWAppForm;

{$R *.dfm}


procedure TDialogFrageBearbeiten.IWFrameRegionCreate(Sender: TObject);
begin
  inherited;
  FAktuelleFrage := Nil;
  FSelektierteFrage := nil;
  TDialogFrBewEdit.Create(self, DM);
end;

procedure TDialogFrageBearbeiten.UpdateUI;
begin
  ButtonBewertungLoeschen.Enabled := FFragenBewertungSelected >= 0;

  // Fragenbewertungen setzen
  ListBewertungen.Items.Clear;
  if FAktuelleFrage <> nil then
  begin
    for var i := 0 to FAktuelleFrage.fragenbewertungen.Count - 1 do
    begin
      var
      LBewertung := FAktuelleFrage.fragenbewertungen.Items[i];
      // Panellistitem hinzufügen
      var
      LItem := ListBewertungen.Items.Add;
      // LItem.UniqueName := LItem.UniqueName + i.ToString;
      LItem.Caption := LBewertung.DisplayTextHTML;
      // Onclick
      LItem.OnClick.OnEvent := BewertungOnClick;
      LItem.OnClick.Ajax := true;
    end;
  end;
  // Die aktuell selektierte Bewertung farblich markieren
  for var i := 0 to ListBewertungen.Items.Count - 1 do
  begin
    if i = FFragenBewertungSelected then
    begin
      ListBewertungen.Items[i].ItemCSS := 'panellist-nav-item-li ui-state-highlight';
    end
    else
    begin
      ListBewertungen.Items[i].ItemCSS := 'panellist-nav-item-li';
    end;
  end;

  if WebApplication.IsCallBack then
  begin
    ListBewertungen.AjaxReRender;
    SelectKontrollbereiche.AjaxReRender;
    ComboFormatierung.AjaxReRender;
  end;
end;

function TDialogFrageBearbeiten.GetFrage;
begin
  if EditTitelText.Text = '' then
  begin
    if not FBearbeiten then
    begin
      FAktuelleFrage.Free;
    end;
    FAktuelleFrage := Nil;
    Result := Nil;
    Exit;
  end;

  FAktuelleFrage.Text := EditTitelText.Text;
  FAktuelleFrage.Info := MemoInfoText.Content;
  FAktuelleFrage.idExtern := EditIdExtern.Text;
  FAktuelleFrage.fragennr := EditFragennummer.Text;
  FAktuelleFrage.CCRelevant := CheckboxCCrelevant.Checked;

  // Kontrollbereiche
  FAktuelleFrage.Kontrollbereiche.Clear;
  for var LItem in SelectKontrollbereiche.Items do
  begin
    if (LItem as TIWCGJQMultiSelectItem).Selected then
    begin
      FAktuelleFrage.Kontrollbereiche.Add(FindeKontrollbereich(
        StrToInt((LItem as TIWCGJQMultiSelectItem).Value), DM.Kontrollbereiche));
    end;
  end;
  // Formatierung
  FAktuelleFrage.formatierung := ComboFormatierung.SelectedValue;

  // Return
  Result := FAktuelleFrage;
end;

procedure TDialogFrageBearbeiten.DialogAbbrechen;
begin
  // Wenn die Frage bearbeitet (und nicht neu erstellt) wird, dann gehört uns die FAktuelleFrage-Instanz hier nicht!
  if not FBearbeiten then
  begin
    FAktuelleFrage.Free;
  end;
  FAktuelleFrage := Nil;
  FBearbeiten := false;
end;

procedure TDialogFrageBearbeiten.BewertungLoeschen;
begin
  if (FFragenBewertungSelected >= 0) and (FFragenBewertungSelected < ListBewertungen.Items.Count) then
  begin
    ListBewertungen.Items.Delete(FFragenBewertungSelected);
    FAktuelleFrage.fragenbewertungen.Delete(FFragenBewertungSelected);
  end;
  FFragenBewertungSelected := -1;
  UpdateUI;
end;

// Knopf um eine neue Bewertung hinzuzufügen wurde geklickt
procedure TDialogFrageBearbeiten.ButtonBewertungHinzufuegenOnClick(Sender: TObject; AParams: TStringList);
begin
  var
  LDialogBewerten := TDialogFrBewEdit.Create(self, DM);
  LDialogBewerten.Neu;
  LDialogBewerten.Show(false, true,
    procedure
    begin
      var
      LBewertung := LDialogBewerten.GetFragenbewertung;
      if LBewertung.bewertung <> Nil then
      begin
        // Prüfen ob die Bewertung zulässig ist
        BewertungPruefen(LBewertung);

        // Panellistitem hinzufügen
        var
        LItem := ListBewertungen.Items.Add;
        // LItem-Text setzen
        LItem.Caption := LBewertung.DisplayTextHTML;
        // Onclick
        LItem.OnClick.OnEvent := BewertungOnClick;
        LItem.OnClick.Ajax := true;

        FAktuelleFrage.fragenbewertungen.Add(LBewertung);

        LDialogBewerten.Close;
        FFragenBewertungSelected := -1;
        UpdateUI;
      end;
    end);
end;

// Eine existierende Bewertung markieren
procedure TDialogFrageBearbeiten.BewertungOnClick(Sender: TObject; AParams: TStringList);
begin
  FFragenBewertungSelected := GetIndexFromPanelListOnClick(AParams);
  UpdateUI;
end;

procedure TDialogFrageBearbeiten.BewertungPruefen(ABewertung: TFragenBewertung);
begin
  // Es darf für eine Frage nur eine Bewertung vom Typ check-p bzw. datum-p existieren.
  // Todo: das "-p" sollte später in ein Feld/Attribut übeführt werden
  for var LExistierendeBewertung in FAktuelleFrage.fragenbewertungen do
  begin
    if (LExistierendeBewertung.bewertung.IsTypeP) and (ABewertung.bewertung.IsTypeP) then
    begin
      Alert.Error('Es darf nur eine Bewertung vom Typ "-p" geben!');
      Abort
    end;

    if LExistierendeBewertung.bewertung.id = ABewertung.bewertung.id then
    begin
      Alert.Error('Es darf nur eine Bewertung pro Typ geben!');
      Abort;
    end;
  end;
end;

procedure TDialogFrageBearbeiten.ButtonBewertungLoeschenOnClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  BewertungLoeschen;
end;

procedure TDialogFrageBearbeiten.ButtonCancelClick(Sender: TObject; AParams: TStringList);
begin
  DialogAbbrechen;
  Close;
end;

procedure TDialogFrageBearbeiten.ButtonOKClick(Sender: TObject; AParams: TStringList);
begin
  var LFrage := GetFrage;

  if LFrage <> nil then
  begin
    if Modus = TModus.NeueUnterfrage then
    begin
      DM.UnterfrageHinzufuegen(SelektierteFrage, LFrage);
    end
    else if Modus = TModus.NeueFrage then
    begin
      DM.FrageHinzufuegen(LFrage);
    end;
    DM.HatBearbeitet := true;
  end;
  DM.ChecklisteRefresh;

  inherited;
end;

procedure TDialogFrageBearbeiten.InitializeControls;
begin
  inherited;
  ListBewertungen.Items.Clear;
  EditTitelText.Text := '';
  MemoInfoText.Content := '';
  EditIdExtern.Text := '';
  EditFragennummer.Text := '';
  CheckboxCCrelevant.Checked := false;
  SelectKontrollbereiche.Items.Clear;
  SelectKontrollbereiche.SelectedItems.Clear;
  FFragenBewertungSelected := -1;
  ComboFormatierung.Items.Clear;

  FBearbeiten := true;

  EditTitelText.Text := AktuelleFrage.Text;
  MemoInfoText.Content := AktuelleFrage.Info;
  MemoInfoText.SendContentInAjax := true;
  EditIdExtern.Text := AktuelleFrage.idExtern;
  EditFragennummer.Text := AktuelleFrage.fragennr;
  CheckboxCCrelevant.Checked := AktuelleFrage.CCRelevant;

  // Kontrollbereiche befüllen
  if (DM.Kontrollbereiche <> nil) and (DM.Formatierungen <> nil) and (DM.Bewertungen <> nil) then
  begin
    ComboFormatierung.Items.Add('Standard', '-1');
    for var i := 0 to DM.Formatierungen.Count - 1 do
    begin
      ComboFormatierung.Items.Add(DM.Formatierungen[i].beschreibung, DM.Formatierungen[i].code);
    end;

    for var i := 0 to DM.Kontrollbereiche.Count - 1 do
    begin
      var
      item := SelectKontrollbereiche.Items.Add;
      item.Caption := DM.Kontrollbereiche[i].bezeichnung;
      item.Value := IntToStr(DM.Kontrollbereiche[i].id);
    end;
  end;

  ComboFormatierung.SelectedValue := AktuelleFrage.formatierung;

  // Kontrollbereiche setzen
  for var i := 0 to SelectKontrollbereiche.Items.Count - 1 do
  begin
    var
    LSelectedItem := SelectKontrollbereiche.Items.Items[i];
    LSelectedItem.Selected := false;
    for var j := 0 to AktuelleFrage.Kontrollbereiche.Count - 1 do
    begin
      if StrToInt(LSelectedItem.Value) = AktuelleFrage.Kontrollbereiche.Items[j].id then
      begin
        LSelectedItem.Selected := true;
        break;
      end;
    end;
  end;
  // die Bewertungen werden in UpdateUI gesetzt
  UpdateUI;
end;

procedure TDialogFrageBearbeiten.SetModus(const Value: TModus);
begin
  FModus := Value;

  case FModus of
    NeueFrage:
      Title := 'Frage hinzufügen';
    NeueUnterfrage:
      Title := 'Unterfrage hinzufügen';
    Bearbeiten:
      Title := 'Frage bearbeiten';
  end;
end;

end.
