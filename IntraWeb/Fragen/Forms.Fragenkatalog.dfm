inherited FormFragenkatalog: TFormFragenkatalog
  Width = 1481
  Height = 875
  Title = 'Fragenkatalog'
  OnCreate = FragenkatalogOnCreate
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1481
    TabOrder = 0
    inherited ImageLogo: TIWImageFile
      Left = 1198
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 989
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 1
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 850
    Width = 1481
    TabOrder = 3
  end
  object TabsFragenverwaltung: TIWCGJQTabs [2]
    Left = 0
    Top = 50
    Width = 1481
    Height = 800
    TabOrder = 2
    Version = '1.0'
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    ActiveTab = TabMangeltypen
    JQTabOptions.OnSelect.OnEvent = TabsFragenverwaltungJQTabOptionsSelect
    object TabChecklisten: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Checklisten'
      TabIndex = 0
      Tabs = TabsFragenverwaltung
    end
    object TabFormatierungen: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Formatierungen'
      TabIndex = 1
      Tabs = TabsFragenverwaltung
    end
    object TabBewertung: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Bewertungen'
      TabIndex = 2
      Tabs = TabsFragenverwaltung
    end
    object TabKontrollbereiche: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Kontrollbereiche'
      TabIndex = 3
      Tabs = TabsFragenverwaltung
    end
    object TabMassnahmen: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Ma'#223'nahmen'
      TabIndex = 5
      Tabs = TabsFragenverwaltung
    end
    object TabMangeltypen: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Mangeltypen'
      TabIndex = 6
      Tabs = TabsFragenverwaltung
    end
    object TabMassnahmenkatalog: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Ma'#223'nahmenkatalog'
      TabIndex = 4
      Tabs = TabsFragenverwaltung
    end
    object TabKontrolltypen: TIWCGJQTab
      Css = ''
      Version = '1.0'
      Caption = 'Kontrolltypen'
      TabIndex = 7
      Tabs = TabsFragenverwaltung
    end
  end
  object jqsaAlert: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 192
    Top = 8
  end
end
