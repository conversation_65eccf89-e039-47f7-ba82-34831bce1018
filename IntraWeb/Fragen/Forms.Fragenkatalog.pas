﻿unit Forms.Fragenkatalog;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Forms.Base, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCompExtCtrls, IWVCLBaseControl,
  IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQRegion, IWCGJQButton, Vcl.Imaging.jpeg,
  IWCompTabControl, BewertungenFrame, IWCGJQComp, IWCGJQSweetAlert,
  FormatierungenFrame, System.Generics.Collections, KontrollbereicheFrame,
  MassnahmenkatalogFrame, MassnahmenFrame, MangeltypenFrame, Frames.Checklisten,  IWCGJQLabel,
  Frames.Ko<PERSON>rolltypen, IWCGJQTabs;

type
  TFormFragenkatalog = class(TFormBase)
    jqsaAlert: TIWCGJQSweetAlert;
    TabsFragenverwaltung: TIWCGJQTabs;
    TabChecklisten: TIWCGJQTab;
    TabFormatierungen: TIWCGJQTab;
    TabBewertung: TIWCGJQTab;
    TabKontrollbereiche: TIWCGJQTab;
    TabMassnahmenkatalog: TIWCGJQTab;
    TabMassnahmen: TIWCGJQTab;
    TabMangeltypen: TIWCGJQTab;
    TabKontrolltypen: TIWCGJQTab;
    procedure FragenkatalogOnCreate(Sender: TObject);
    procedure TabsFragenverwaltungJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    aktiveTabs: TList<String>;
    BewertungenFrame: TBewertungen;
    FormatierungenFrame: TFormatierungen;
    KontrollbereicheFrame: TKontrollbereiche;
    MassnahmenkatalogFrame: TMassnahmenkatalog;
    MassnahmenFrame: TMassnahmen;
    MangeltypenFrame: TMangeltypen;
    checklistenFrame: TFrameChecklisten;
    kontrolltypenFrame: TFrameKontrolltypen;
  public
    { Public declarations }
    destructor Destroy; override;
  end;

var
  FormFragenkatalog: TFormFragenkatalog;

implementation

uses StrUtils, ServerController, Funktionen;

{$R *.dfm}


procedure TFormFragenkatalog.FragenkatalogOnCreate(Sender: TObject);
var
  bearbeiten: boolean;
  Funktionen: TFunktionenManager;
begin
  inherited;
  Funktionen := UserSession.FunktionenManager;
  aktiveTabs := TList<String>.Create;
  bearbeiten := Funktionen.HatFunktion(Fragen_bearbeiten);

  TabChecklisten.Visible := Funktionen.HatFunktion(Fragen_Checklisten_Bearbeiten);
  TabBewertung.Visible := Funktionen.HatFunktion(Fragen_Bewertungen_Bearbeiten);
  TabFormatierungen.Visible := Funktionen.HatFunktion(Fragen_Formatierungen_Bearbeiten);
  TabKontrollbereiche.Visible := Funktionen.HatFunktion(Fragen_Kontrollbereiche_Bearbeiten);
  TabMassnahmenkatalog.Visible := Funktionen.HatFunktion(Fragen_Maßnahmenkatalog_Bearbeiten);
  TabMassnahmen.Visible := Funktionen.HatFunktion(Fragen_Maßnahmen_Bearbeiten);
  TabMangeltypen.Visible := Funktionen.HatFunktion(Fragen_Mangeltypen_Bearbeiten);
  TabKontrolltypen.Visible := Funktionen.HatFunktion(Fragen_Checklisten_Bearbeiten);

  if Funktionen.HatFunktion(Fragen_Checklisten_Bearbeiten) then
  begin
    // Checklisten als erstes zeigen
    TabsFragenverwaltung.ActiveTab := TabChecklisten;
    checklistenFrame := TFrameChecklisten.Create(Self, jqsaAlert, bearbeiten);
    checklistenFrame.Parent := TabChecklisten;
    aktiveTabs.Add('checklisten');
  end;
  
  if Funktionen.HatFunktion(Fragen_Bewertungen_Bearbeiten) then
  begin
    BewertungenFrame := TBewertungen.Create(Self, jqsaAlert, bearbeiten);
    BewertungenFrame.Parent := TabBewertung;
    aktiveTabs.Add('bewertung');
  end;

  if Funktionen.HatFunktion(Fragen_Formatierungen_Bearbeiten) then
  begin
    FormatierungenFrame := TFormatierungen.Create(Self, jqsaAlert, bearbeiten);
    FormatierungenFrame.Parent := TabFormatierungen;
    aktiveTabs.Add('formatierung');
  end;

  if Funktionen.HatFunktion(Fragen_Kontrollbereiche_Bearbeiten) then
  begin
    KontrollbereicheFrame := TKontrollbereiche.Create(Self, jqsaAlert, bearbeiten);
    KontrollbereicheFrame.Parent := TabKontrollbereiche;
    aktiveTabs.Add('kontrollbereiche');
  end;

  if Funktionen.HatFunktion(Fragen_Maßnahmenkatalog_Bearbeiten) then
  begin
    MassnahmenkatalogFrame := TMassnahmenkatalog.Create(Self, jqsaAlert, bearbeiten);
    MassnahmenkatalogFrame.Parent := TabMassnahmenkatalog;
    aktiveTabs.Add('massnahmenkatalog');
  end;

  if Funktionen.HatFunktion(Fragen_Maßnahmen_Bearbeiten) then
  begin
    TabMassnahmen.Visible := true;
    MassnahmenFrame := TMassnahmen.Create(Self, jqsaAlert, bearbeiten);
    MassnahmenFrame.Parent := TabMassnahmen;
    aktiveTabs.Add('massnahmen');
  end;

  if Funktionen.HatFunktion(Fragen_Mangeltypen_Bearbeiten) then
  begin
    MangeltypenFrame := TMangeltypen.Create(Self, jqsaAlert, bearbeiten);
    MangeltypenFrame.Parent := TabMangeltypen;
    aktiveTabs.Add('mangeltypen');
  end;

  if Funktionen.HatFunktion(Fragen_Checklisten_Bearbeiten) then
  begin
    kontrolltypenFrame := TFrameKontrolltypen.Create(Self, jqsaAlert);
    kontrolltypenFrame.Parent := TabKontrolltypen;
    aktiveTabs.Add('kontrolltypen');
  end;
  
  TabsFragenverwaltungJQTabOptionsSelect(nil, nil);
end;

destructor TFormFragenkatalog.Destroy;
begin
  aktiveTabs.Free;
  inherited;
end;

procedure TFormFragenkatalog.TabsFragenverwaltungJQTabOptionsSelect(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if TabsFragenverwaltung.ActiveTab = TabBewertung then
    BewertungenFrame.BewertungenAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabFormatierungen then
    FormatierungenFrame.FormatierungenAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabKontrollbereiche then
    KontrollbereicheFrame.KontrollbereicheAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabMassnahmenkatalog then
    MassnahmenkatalogFrame.MassnahmenkatalogAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabMassnahmen then
    MassnahmenFrame.MassnahmenAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabMangeltypen then
    MangeltypenFrame.MangeltypenAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabChecklisten then
    checklistenFrame.ChecklistenAbfragen
  else if TabsFragenverwaltung.ActiveTab = TabKontrolltypen then
    kontrolltypenFrame.KontrolltypenAbfragen;
end;

end.
