inherited DialogChecklisteKontrolltypenZuordnen: TDialogChecklisteKontrolltypenZuordnen
  Width = 727
  Height = 597
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 727
    Height = 597
    TabOrder = 5
    JQDialogOptions.Height = 597
    JQDialogOptions.Width = 727
    inherited RegionContent: TIWCGJQRegion
      Width = 727
      Height = 537
      TabOrder = 2
      object GridKontrolltypen: TIWCGJQGrid
        Left = 0
        Top = 185
        Width = 727
        Height = 352
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.CellEdit = True
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BKB_Bezeichnung'
            Name = 'BKB_Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'BKB Typ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Kontrolltyp_Bezeichnung'
            Name = 'Kontrolltyp_Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Kontrolltyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            Idx = 'GUELTIG_AB'
            Name = 'GUELTIG_AB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'ab'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            Idx = 'GUELTIG_BIS'
            Name = 'GUELTIG_BIS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'bis'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'Gueltig'
            Name = 'Gueltig'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
          end>
        JQGridOptions.Height = 298
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 725
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = GridKontrolltypenJQGridOptionsLoadComplete
        JQGridOptions.OnSelectRow.OnEvent = GridKontrolltypenJQGridOptionsSelectRow
        JQGridOptions.OnAfterSubmitCell.OnEvent = GridKontrolltypenJQGridOptionsAfterSubmitCell
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderKontrolltypen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQGridGroupHeader.GroupHeaders = <
          item
            StartColumnName = 'GUELTIG_AB'
            NumberOfColumns = 3
            TitleText = 'G'#252'ltig'
          end>
        JQDragAndDropOptions.ConnectWith = <>
      end
      object RegionTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 727
        Height = 185
        TabOrder = 7
        Version = '1.0'
        Align = alTop
        object IWCGJQLabel1: TIWCGJQLabel
          Left = 3
          Top = 66
          Width = 143
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel1'
          Caption = 'Kontrolltyp ausw'#228'hlen:'
        end
        object IWCGJQLabel2: TIWCGJQLabel
          Left = 3
          Top = 163
          Width = 170
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel1'
          Caption = 'Zugeordnete Kontrolltypen:'
        end
        object LabelCheckliste: TIWCGJQLabel
          AlignWithMargins = True
          Left = 3
          Top = 3
          Width = 721
          Height = 40
          Align = alTop
          Font.Color = clNone
          Font.Size = 10
          Font.Style = [fsBold]
          HasTabOrder = False
          AutoSize = False
          FriendlyName = 'LabelCheckliste'
          DataLink.DataSource = DSCheckliste
          DataLink.FieldName = 'BEZEICHNUNG'
        end
        object IWCGJQLabel3: TIWCGJQLabel
          Left = 3
          Top = 119
          Width = 60
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel3'
          Caption = 'G'#252'ltig ab:'
        end
        object IWCGJQLabel4: TIWCGJQLabel
          Left = 201
          Top = 119
          Width = 23
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWCGJQLabel3'
          Caption = 'bis:'
        end
        object ButtonZuweisen: TIWCGJQButton
          Left = 474
          Top = 88
          Width = 95
          Height = 25
          TabOrder = 8
          Version = '1.0'
          JQButtonOptions.Label_ = 'zuweisen'
          JQButtonOptions.OnClick.OnEvent = ButtonZuweisenJQButtonOptionsClick
        end
        object ComboAktiveKontrollTypen: TIWCGJQDropDown
          Left = 3
          Top = 88
          Width = 465
          Height = 25
          TabOrder = 9
          Version = '1.0'
          DataLink.ListDataSource = DSAktiveKontrolltypen
          DataLink.ListFieldNames = 'DisplayName'
          DataLink.ListSelectFieldName = 'ID'
          JQDropDownOptions.Ajax.QuietMillis = 100
          JQDropDownOptions.Ajax.Use = True
          JQDropDownOptions.AttachTo = jqddatInput
          JQDropDownOptions.InfiniteScroll = True
          Groups = <>
          Items = <>
        end
        object EditDateAb: TIWCGJQDatePicker
          Left = 69
          Top = 119
          Width = 121
          Height = 21
          TabOrder = 10
          Version = '1.0'
          Caption = ''
          JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
        end
        object EditDateBis: TIWCGJQDatePicker
          Left = 230
          Top = 119
          Width = 121
          Height = 21
          TabOrder = 11
          Version = '1.0'
          Caption = ''
          JQDatePickerOptions.DateFormat = 'mm/dd/yyyy'
        end
        object CheckboxBesitzer: TIWCGJQCheckBoxEx
          Left = 3
          Top = 39
          Width = 100
          Height = 21
          TabOrder = 12
          Version = '1.0'
          Enabled = False
          Caption = 'Besitzer'
          JQCheckExOptions.Disabled = True
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 547
      Width = 725
      TabOrder = 4
      inherited ButtonCancel: TIWCGJQButton
        Left = 617
        Visible = False
        TabOrder = 3
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 609
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          Left = 501
          TabOrder = 1
          JQButtonOptions.Label_ = 'Schliessen'
        end
        object ButtonLoeschen: TIWCGJQButton
          Left = 3
          Top = 8
          Width = 151
          Height = 34
          TabOrder = 13
          Version = '1.0'
          JQButtonOptions.Label_ = 'Zuweisung l'#246'schen'
          JQButtonOptions.OnClick.OnEvent = ButtonLoeschenJQButtonOptionsClick
        end
      end
    end
  end
  inherited Alert: TIWCGJQSweetAlert
    Left = 48
    Top = 176
  end
  object DSCheckliste: TDataSource
    DataSet = DMChecklisten.quChecklisten
    Left = 480
    Top = 48
  end
  object ProviderKontrolltypen: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.QChecklisteKontrolltypen
    KeyFields = 'ID'
    Left = 288
    Top = 184
  end
  object DSAktiveKontrolltypen: TDataSource
    DataSet = DMChecklisten.QAktiveKontrolltypen
    Left = 480
    Top = 112
  end
end
