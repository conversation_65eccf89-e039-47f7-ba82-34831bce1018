inherited Massnahmen: TM<PERSON><PERSON><PERSON>
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 13
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 5
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 3
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'bezeichnung'
            Name = 'bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Ma'#223'nahmenkatalog'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Langtext'
            Name = 'Langtext'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Langtext'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'standardfrist'
            Name = 'standardfrist'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Standardfrist'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 6
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 9
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 11
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 4
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 12
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 1
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Width = 401
      Height = 212
      TabOrder = 2
      JQDialogOptions.Height = 212
      JQDialogOptions.Width = 401
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 16
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Ma'#223'nahmenkatalog:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 46
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Langtext:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 16
        Top = 80
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Standardfrist:'
      end
      object iwcbMassnahmenkatalog: TIWCGJQComboBoxEx
        Left = 152
        Top = 16
        Width = 200
        Height = 21
        TabOrder = 7
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 198
        Caption = ''
      end
      object jqeLangtext: TIWCGJQEdit
        Left = 152
        Top = 46
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeStandardfrist: TIWCGJQEdit
        Left = 152
        Top = 80
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quMassnahmen
    KeyFields = 'ID'
  end
  object quMassnahmen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT m.id, m.id_massnahmenkatalog, m.Langtext, m.standardfrist' +
        ', mk.bezeichnung'
      'FROM   Stammdaten.Massnahmen m'
      
        '        INNER JOIN Stammdaten.Massnahmenkatalog mk ON mk.ID = m.' +
        'ID_Massnahmenkatalog'
      
        'WHERE  m.id_massnahmenkatalog = :id_massnahmenkatalog OR :filter' +
        ' = 0;')
    Left = 689
    Top = 25
    ParamData = <
      item
        Name = 'ID_MASSNAHMENKATALOG'
        DataType = ftInteger
        ParamType = ptInput
        Value = 10
      end
      item
        Name = 'FILTER'
        DataType = ftBoolean
        ParamType = ptInput
        Value = False
      end>
    object quMassnahmenid: TFDAutoIncField
      FieldName = 'id'
      Origin = 'id'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quMassnahmenid_massnahmenkatalog: TIntegerField
      FieldName = 'id_massnahmenkatalog'
      Origin = 'id_massnahmenkatalog'
      Required = True
    end
    object quMassnahmenLangtext: TMemoField
      FieldName = 'Langtext'
      Origin = 'Langtext'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object quMassnahmenstandardfrist: TIntegerField
      FieldName = 'standardfrist'
      Origin = 'standardfrist'
    end
    object quMassnahmenbezeichnung: TStringField
      FieldName = 'bezeichnung'
      Origin = 'bezeichnung'
      Required = True
      Size = 200
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Massnahmen (ID_Massnahmenkatalog, Langtex' +
        't, Standardfrist)'
      'VALUES      (:id_massnahmenkatalog, :langtext, :standardfrist);')
    Left = 761
    Top = 25
    ParamData = <
      item
        Name = 'ID_MASSNAHMENKATALOG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LANGTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STANDARDFRIST'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Massnahmen '
      
        'SET    ID_Massnahmenkatalog = :id_massnahmenkatalog, Langtext = ' +
        ':langtext, Standardfrist = :standardfrist'
      'WHERE  ID = :id;')
    Left = 825
    Top = 25
    ParamData = <
      item
        Name = 'ID_MASSNAHMENKATALOG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LANGTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'STANDARDFRIST'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quMassnahmenkatalog: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Massnahmenkatalog;')
    Left = 944
    Top = 24
  end
end
