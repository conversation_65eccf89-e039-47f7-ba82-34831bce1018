inherited DialogFrageVerschieben: TDialogFrageVerschieben
  Width = 694
  Height = 671
  OnCreate = IWCGJQFrameCreate
  ExplicitWidth = 694
  ExplicitHeight = 671
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 694
    Height = 671
    TabOrder = 7
    JQDialogOptions.Height = 671
    JQDialogOptions.Title = 'Frage verschieben'
    JQDialogOptions.Width = 694
    ExplicitWidth = 694
    ExplicitHeight = 671
    inherited RegionContent: TIWCGJQRegion
      Width = 694
      Height = 611
      TabOrder = 2
      ExplicitWidth = 694
      ExplicitHeight = 611
      object RadioGroupVerschiebeModus: TIWCGJQRadioGroupEx
        AlignWithMargins = True
        Left = 8
        Top = 538
        Width = 678
        Height = 65
        Margins.Left = 8
        Margins.Top = 8
        Margins.Right = 8
        Margins.Bottom = 8
        TabOrder = 5
        Version = '1.0'
        Align = alBottom
        Items = <
          item
            Caption = 'Aktuelle Frage '#252'ber die oben selektierte Frage verschieben'
            Value = 'autoid1612178842-0'
          end
          item
            Caption = 'Aktuelle Frage unter die oben selektierte Frage verschieben'
            Value = 'autoid1612178437-1'
          end
          item
            Caption = 
              'Aktuelle Frage als Unterfrage an die oben selektierte Frage anh'#228 +
              'ngen'
            Value = 'autoid1612179442-2'
          end>
        PicturePositionOptions.My = jqpoLeftCenter
        PicturePositionOptions.At = jqpoRightCenter
      end
      object GridFragen: TIWCGJQGrid
        Left = 0
        Top = 0
        Width = 694
        Height = 530
        TabOrder = 6
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Classes.Strings = (
              'fragenNr')
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'FragenNr'
            Name = 'FragenNr'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Width = 50
            Caption = 'FragenNr'
          end
          item
            Classes.Strings = (
              'fragenText')
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Text'
            Name = 'Text'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Text'
          end>
        JQGridOptions.DeselectAfterSort = False
        JQGridOptions.ExpandColumn = 'FragenNr'
        JQGridOptions.Height = 503
        JQGridOptions.LoadOnce = True
        JQGridOptions.LoadText = 'Lade Daten...'
        JQGridOptions.RowNum = 100000
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 692
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.PagerVisible = False
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderFragen
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <
          item
          end>
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 621
      Width = 692
      TabOrder = 4
      ExplicitTop = 621
      ExplicitWidth = 692
      inherited ButtonCancel: TIWCGJQButton
        Left = 584
        TabOrder = 3
        JQButtonOptions.OnClick.Ajax = False
        JQButtonOptions.OnClick.AjaxAppend = False
        ExplicitLeft = 584
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 576
        TabOrder = 0
        inherited ButtonOK: TIWCGJQButton
          TabOrder = 1
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          ExplicitLeft = 468
        end
      end
    end
  end
  object ProviderFragen: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.MTFragen
    KeyFields = 'ID'
    Left = 480
    Top = 120
  end
end
