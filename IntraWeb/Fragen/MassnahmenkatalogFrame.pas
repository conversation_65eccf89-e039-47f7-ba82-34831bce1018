﻿unit MassnahmenkatalogFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, <PERSON>WCGJQ<PERSON>omp, IWCGJQSweet<PERSON>lert,
  Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWApplication, IWCGJQEdit,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQRegion, IWCGJQLabel;

type
  TMassnahmenkatalog = class(TCRUDGrid)
    quMassnahmenkatalog: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent;
    AAlert: TIWCGJQSweetAlert;
    bearbeitbar: Boolean);
    procedure MassnahmenkatalogAbfragen;
  end;

var
  Massnahmenkatalog: TMassnahmenkatalog;

implementation

uses dmmain, Utility;

{$R *.dfm}

constructor TMassnahmenkatalog.Create(AOwner: TComponent;
    AAlert: TIWCGJQSweetAlert;
    bearbeitbar: Boolean);
begin
  inherited Create(AOwner, Aalert);

  FAlert := Aalert;

  EnableAbfragen(quMassnahmenkatalog, MassnahmenkatalogAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neuen Maßnahmenktalog erstellen', Nil, NeuBestaetigt);
    EnableAendern('Maßnahmenktalog ändern', InitAendern, AendernBestaetigt);
  end;
end;

procedure TMassnahmenkatalog.MassnahmenkatalogAbfragen;
begin
  RefreshQuery(quMassnahmenkatalog);
end;

procedure TMassnahmenkatalog.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.Execute;
end;

procedure TMassnahmenkatalog.InitAendern;
begin
  jqeBezeichnung.Text := quMassnahmenkatalog.FieldByName('bezeichnung').AsString;
end;

procedure TMassnahmenkatalog.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('id').AsInteger :=
      quMassnahmenkatalog.FieldByName('id').AsInteger;
  quAendern.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quAendern.Execute;
end;

procedure TMassnahmenkatalog.ResetModal;
begin
  jqeBezeichnung.Text := '';
end;

end.
