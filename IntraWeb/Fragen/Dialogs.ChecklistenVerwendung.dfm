inherited DialogChecklistenVerwendung: TDialogChecklistenVerwendung
  Width = 699
  Height = 530
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 699
    Height = 530
    TabOrder = 1
    JQDialogOptions.Height = 530
    JQDialogOptions.Width = 699
    inherited RegionContent: TIWCGJQRegion
      Width = 699
      Height = 470
      TabOrder = 4
      object LabelBesitzer: TIWCGJQLabel
        AlignWithMargins = True
        Left = 0
        Top = 4
        Width = 696
        Height = 21
        Margins.Left = 0
        Margins.Top = 4
        Margins.Bottom = 8
        Align = alTop
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelBesitzer'
        Caption = 'LabelBesitzer'
      end
      object RegionGrids: TIWRegion
        Left = 0
        Top = 33
        Width = 699
        Height = 437
        RenderInvisibleControls = True
        Align = alClient
        BorderOptions.Style = cbsNone
        object RegionKontrolltypen: TIWRegion
          Left = 0
          Top = 0
          Width = 699
          Height = 193
          RenderInvisibleControls = True
          Align = alTop
          BorderOptions.Style = cbsNone
          object IWCGJQLabel2: TIWCGJQLabel
            AlignWithMargins = True
            Left = 0
            Top = 4
            Width = 699
            Height = 21
            Margins.Left = 0
            Margins.Top = 4
            Margins.Right = 0
            Margins.Bottom = 8
            Align = alTop
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWCGJQLabel1'
            Caption = 'Zugeordnete Kontrolltypen:'
          end
          object GridChecklistenVerwendungKontrolltypen: TIWCGJQGrid
            Left = 0
            Top = 33
            Width = 699
            Height = 160
            TabOrder = 6
            Version = '1.0'
            Align = alClient
            JQGridOptions.ColModel = <
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'BKB_Bezeichnung'
                Name = 'BKB_Bezeichnung'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'BKB Typ'
              end
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'Kontrolltyp_Bezeichnung'
                Name = 'Kontrolltyp_Bezeichnung'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'Kontrolltyp'
              end>
            JQGridOptions.Height = 106
            JQGridOptions.SubGridModel = <>
            JQGridOptions.Width = 697
            JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
            JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
            JQGridCustomButtons = <>
            JQGridProvider = ProviderChecklistenVErwendungKontrolltypen
            JQGridToolbarSearch.DefaultSearch = gsoContains
            JQDragAndDropOptions.ConnectWith = <>
          end
        end
        object RegionKontrollen: TIWRegion
          Left = 0
          Top = 193
          Width = 699
          Height = 244
          RenderInvisibleControls = True
          Align = alClient
          BorderOptions.Style = cbsNone
          object IWCGJQLabel1: TIWCGJQLabel
            AlignWithMargins = True
            Left = 0
            Top = 4
            Width = 699
            Height = 21
            Margins.Left = 0
            Margins.Top = 4
            Margins.Right = 0
            Margins.Bottom = 8
            Align = alTop
            Font.Color = clNone
            Font.Size = 10
            Font.Style = []
            HasTabOrder = False
            FriendlyName = 'IWCGJQLabel1'
            Caption = 'Zugeordnete Kontrollen:'
          end
          object GridChecklistenVerwendungKontrollen: TIWCGJQGrid
            Left = 0
            Top = 33
            Width = 699
            Height = 211
            TabOrder = 7
            Version = '1.0'
            Align = alClient
            JQGridOptions.ColModel = <
              item
                Editable = True
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'Bundesland'
                Name = 'Bundesland'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Width = 80
                Caption = 'Bundesland'
              end
              item
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'BKB'
                Name = 'BKB'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'BKB'
              end
              item
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Formatter = gcfDate
                Idx = 'DATUM'
                Name = 'DATUM'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Width = 100
                Caption = 'Datum'
              end
              item
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'STATUS'
                Name = 'STATUS'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Width = 80
                Caption = 'Status'
              end
              item
                EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
                EditOptions.CustomElements = <>
                Idx = 'Erfasser'
                Name = 'Erfasser'
                SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
                Caption = 'Erfasser'
              end>
            JQGridOptions.Height = 157
            JQGridOptions.SubGridModel = <>
            JQGridOptions.Width = 697
            JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
            JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
            JQGridCustomButtons = <>
            JQGridProvider = ProviderChecklistenVErwendungKontrollen
            JQGridToolbarSearch.DefaultSearch = gsoContains
            JQDragAndDropOptions.ConnectWith = <>
          end
        end
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 480
      Width = 697
      TabOrder = 0
      inherited ButtonCancel: TIWCGJQButton
        Left = 589
        Visible = False
        TabOrder = 5
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 581
        TabOrder = 2
        inherited ButtonOK: TIWCGJQButton
          Left = 473
          TabOrder = 3
        end
      end
    end
  end
  object ProviderChecklistenVErwendungKontrollen: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.QChecklisteVerwendungKontrollen
    Left = 288
    Top = 412
  end
  object ProviderChecklistenVErwendungKontrolltypen: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.QChecklisteKontrolltypen
    Left = 512
    Top = 84
  end
end
