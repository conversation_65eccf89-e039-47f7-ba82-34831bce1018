﻿unit Frames.Checklisten;

interface

uses
  System.SysUtils, System.Variants, System.Classes,
  Winapi.Windows, Winapi.Messages,
  Data.DB,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs,
  IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWApplication, IWCompListbox,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQEdit, IWCGJQDatePicker,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.<PERSON>.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, FireDAC.Comp.DataSet, FireDAC.Comp.Client,
  CRUDGridFrame, Forms.ChecklisteDetails, IWCGJQRegion, IWCompCheckbox, IWCGJQCheckBox, IWCGJQCombobox, IWCGJQLabel,
  Modules.Checklisten,
  Dialogs.ChecklisteKontrolltypenZuordnen, IWCGJQDropDown, Dialogs.ChecklistenVerwendung;

type
  TFrameChecklisten = class(TCRUDGrid)
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    EditBezeichnung: TIWCGJQEdit;
    DateGueltigAb: TIWCGJQDatePicker;
    DateGueltigBis: TIWCGJQDatePicker;
    EditVersion: TIWCGJQEdit;
    ButtonFragenBearbeiten: TIWCGJQButton;
    IWLabel7: TIWCGJQLabel;
    EditVersionstext: TIWCGJQEdit;
    ButtonChecklisteKopieren: TIWCGJQButton;
    AlertConfirmChecklisteLoeschen: TIWCGJQSweetAlert;
    CheckboxAktiveAnzeigen: TIWCGJQCheckBox;
    IWCGJQLabel1: TIWCGJQLabel;
    CheckboxPrivat: TIWCGJQCheckBoxEx;
    LabelBesitzer: TIWCGJQLabel;
    ButtonChecklisteZuweisen: TIWCGJQButton;
    CheckboxCCRelevant: TIWCGJQCheckBoxEx;
    IWCGJQLabel2: TIWCGJQLabel;
    DSVisBkbTypen: TDataSource;
    ButtonChecklistenVerwendung: TIWCGJQButton;
    EditBKBTID: TIWCGJQEdit;
    procedure jqbFragenBearbeitenOnClick(Sender: TObject; AParams: TStringList);
    procedure JqbChecklisteKopierenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure AlertConfirmChecklisteLoeschenJQSweetAlertOptionsBtnClick(Sender: TObject; AParams: TStringList);
    procedure CheckboxAktiveAnzeigenClick(Sender: TObject);
    procedure jqbAendernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure ButtonChecklisteZuweisenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure ButtonChecklistenVerwendungJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
  private
    { Private declarations }
    FAlertChecklisteLoeschenConfirmed: TProc;
    FDialogChecklisteKontrolltypenZuordnen: TDialogChecklisteKontrolltypenZuordnen;
    FDialogChecklistenVerwendung: TDialogChecklistenVerwendung;
    DM: TDMChecklisten;
    procedure ChecklisteSpeichern(AQuery: TFDQuery; ANeu: boolean);
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure InitNeu;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(
      AOwner: TComponent;
      AAlert: TIWCGJQSweetAlert;
      bearbeitbar: boolean);
    procedure ChecklistenAbfragen;
  end;

implementation

uses dmmain, Utility, ServerController, ELKE.Classes.Generated, System.DateUtils, JQ.Helpers.Grid,
  ELKE.Classes.RESTError;

{$R *.dfm}


constructor TFrameChecklisten.Create(
  AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert;
  bearbeitbar: boolean);
begin
  DM := TDMChecklisten.Create(self);
  inherited Create(AOwner, AAlert);
  jqdpGrid.DataSet := DM.quChecklisten;
  jqdpGrid.KeyFields := 'ID';

  Falert := AAlert;

  jqgGrid.SetupDefaults(jqdpGrid);

  EnableAbfragen(DM.quChecklisten, ChecklistenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neue Checkliste erstellen', InitNeu, NeuBestaetigt);
    EnableAendern('Checkliste bearbeiten', InitAendern, AendernBestaetigt);
  end;

  FDialogChecklisteKontrolltypenZuordnen := TDialogChecklisteKontrolltypenZuordnen.Create(self, DM);
  FDialogChecklistenVerwendung := TDialogChecklistenVerwendung.Create(self, DM);

  DM.QVIS_BKBTyp.Close;
  DM.QVIS_BKBTyp.Open;

end;

procedure TFrameChecklisten.ChecklistenAbfragen;
begin
  DM.quChecklisten.Close;
  if CheckboxAktiveAnzeigen.Checked then
  begin
    DM.quChecklisten.ParamByName('Today').AsDate := 0;
  end
  else
  begin
    DM.quChecklisten.ParamByName('Today').AsDate := today;
  end;

  RefreshQuery(DM.quChecklisten);
  DM.QChecklisteKontrolltypen.Open;
  SelectRowForCurrentRecord;
end;

procedure TFrameChecklisten.ChecklisteSpeichern(AQuery: TFDQuery; ANeu: boolean);
begin
  AQuery.Close;
  if not ANeu then
  begin
    AQuery.ParamByName('id').AsInteger := DM.quChecklisten.FieldByName('id').AsInteger;
  end;

  AQuery.ParamByName('bezeichnung').AsString := EditBezeichnung.Text;
  AQuery.ParamByName('version').AsInteger := StrToInt(EditVersion.Text);
  AQuery.ParamByName('versionstext').AsString := EditVersionstext.Text;
  AQuery.ParamByName('gueltig_ab').AsDate := DateGueltigAb.Date;
  AQuery.ParamByName('gueltig_bis').AsDate := DateGueltigBis.Date;
  AQuery.ParamByName('user').AsInteger := dm_main.UserId;
  AQuery.ParamByName('Besitzer_BLD').AsInteger := UserSession.ELKERest.GetMeService.Me.Bundesland.Bldcode;
  AQuery.ParamByName('privat').AsBoolean := CheckboxPrivat.Checked;
  AQuery.ParamByName('CCrelevant').AsBoolean := CheckboxCCRelevant.Checked;
  AQuery.ParamByName('VisBkbid').AsString := EditBKBTID.Text; //Ist eigentlich read/only

  AQuery.Execute;

  DM.quChecklisten.Refresh;
  GotoBookmark;
end;

procedure TFrameChecklisten.NeuBestaetigt;
begin
  if EditBezeichnung.Text = '' then
    raise Exception.Create('Bezeichnung fehlt!');
  if EditVersion.Text = '' then
    raise Exception.Create('Version fehlt!');
  if EditVersionstext.Text = '' then
    raise Exception.Create('Versionstext fehlt!');

  ChecklisteSpeichern(DM.quNeu, true);
end;

procedure TFrameChecklisten.AlertConfirmChecklisteLoeschenJQSweetAlertOptionsBtnClick(Sender: TObject;
  AParams: TStringList);
begin
  if AParams.Values['isConfirm'].ToLower = 'true' then
  begin
    if Assigned(FAlertChecklisteLoeschenConfirmed) then
    begin
      FAlertChecklisteLoeschenConfirmed;
    end;
  end;
end;

procedure TFrameChecklisten.ButtonChecklistenVerwendungJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  if jqgGrid.JQGridOptions.SelRow = '' then
  begin
    Alert.Info('Bitte zunächst eine Checkliste auswählen!');
    abort;
  end;
  jqgGrid.SelectRecordFromCurrentRow;
  FDialogChecklistenVerwendung.Show(true);
end;

procedure TFrameChecklisten.ButtonChecklisteZuweisenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  FDialogChecklisteKontrolltypenZuordnen.Show(true);
end;

procedure TFrameChecklisten.InitNeu;
begin
  EditBezeichnung.Text := 'Neue Checkliste';
  EditVersion.Text := '1';
  EditVersionstext.Text := 'Version 1';

  DateGueltigAb.Date := today + 1;
  DateGueltigBis.Date := today + 365;
  LabelBesitzer.Caption := UserSession.ELKERest.GetMeService.Me.Bundesland.Bezeichnung;
  CheckboxPrivat.Checked := true;
  CheckboxCCRelevant.Checked := False;
  EditBKBTID.Text := '';
end;

procedure TFrameChecklisten.InitAendern;
begin
  EditBezeichnung.Text := DM.quChecklistenBEZEICHNUNG.AsString;
  EditVersion.Text := DM.quChecklistenVERSION.AsString;
  EditVersionstext.Text := DM.quChecklistenVERSIONSTEXT.AsString;
  DateGueltigAb.Date := DM.quChecklistenGUELTIG_AB.AsDateTime;
  DateGueltigBis.Date := DM.quChecklistenGUELTIG_BIS.AsDateTime;
  LabelBesitzer.Caption := DM.quChecklistenBESITZER.AsString;
  CheckboxPrivat.Checked := DM.quChecklistenPRIVAT.AsBoolean;
  CheckboxCCRelevant.Checked := DM.quChecklistenCCRelevant.AsBoolean;
  EditBKBTID.Text := DM.quChecklistenVIS_BKBTID.AsString;
end;

procedure TFrameChecklisten.jqbAendernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  if not DM.UserBesitztAusgewählteCheckliste then
  begin
    Falert.Error('Die Checkliste kann nicht bearbeitet werden, da Ihr Bundesland nicht Besitzer ist!'#13#10
      + 'Um die Checkliste zu bearbeiten, müssen Sie zunächst eine Kopie erstellen!');
    abort;
  end;
  inherited jqbAendernOnClick(Sender, AParams);
end;

//
procedure TFrameChecklisten.JqbChecklisteKopierenOnClick(Sender: TObject;
  AParams: TStringList);
var
  idCheckliste: integer;
begin
  SetBookmark;
  idCheckliste := DM.quChecklisten.FieldByName('id').AsInteger;
  try
    UserSession.ELKERest.ChecklisteKopieren(idCheckliste);
    Alert.Success('Checkliste ' + DM.quChecklisten.FieldByName('Bezeichnung').AsString + ' wurde kopiert.');
  except
    on E: Exception do
    begin
      Alert.Error('Es gab einen Fehler beim Kopieren der Checkliste.' +
        sLineBreak + E.Message);
    end;
  end;
  ChecklistenAbfragen;
end;

procedure TFrameChecklisten.jqbFragenBearbeitenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  TChecklistenDetails.Create(self, DM).Show;
end;

procedure TFrameChecklisten.jqbLoeschenOnClick(Sender: TObject; AParams: TStringList);
begin
  SetBookmark;
  if DM.quChecklistenBESITZER_BLDCODE.AsInteger <> UserSession.User.Bundesland.Bldcode then
  begin
    Alert.Error('Es können nur eigene Checklisten gelöscht werden!');
    abort;
  end;

  if DM.quChecklistenInVerwendung.AsInteger = 1 then
  begin
    Alert.Error('Die Checkliste ist in Verwendung und kann nicht gelöscht werden!');
    abort;
  end;

  AlertConfirmChecklisteLoeschen.Show;

  var
  LId := DM.quChecklisten.FieldByName('id').AsInteger;
  FAlertChecklisteLoeschenConfirmed := procedure
    begin
      try
        UserSession.ELKERest.ChecklisteLoeschen(LId);
      except
        on E: Exception do
        begin
          Alert.Error('Die Checkliste konnte nicht gelöscht werden.' + sLineBreak + E.Message);
        end;
      end;
      ChecklistenAbfragen;
    end;
end;

procedure TFrameChecklisten.AendernBestaetigt;
begin
  ChecklisteSpeichern(DM.quAendern, False);
end;

procedure TFrameChecklisten.CheckboxAktiveAnzeigenClick(Sender: TObject);
begin
  inherited;
  ChecklistenAbfragen;
end;

procedure TFrameChecklisten.ResetModal;
begin
  EditVersion.Text := '';
  EditBezeichnung.Text := '';
  DateGueltigAb.Date := today + 1;
  DateGueltigBis.Date := IncMonth(today, 12);
  EditVersion.Text := '';
  EditVersionstext.Text := '';
  CheckboxPrivat.Checked := False;
  CheckboxCCRelevant.Checked := False;
  LabelBesitzer.Caption := UserSession.User.Bundesland.Bezeichnung;
end;

end.
