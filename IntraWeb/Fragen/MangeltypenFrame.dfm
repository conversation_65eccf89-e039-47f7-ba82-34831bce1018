inherited Mangeltypen: TMangeltypen
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 13
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 5
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 3
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Massnahmenkatalog'
            Name = 'Massnahmenkatalog'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Ma'#223'nahmenkatalog'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bkbtyp'
            Name = 'Bkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bkbtyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BezBkbtyp'
            Name = 'BezBkbtyp'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung Bkbtyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bezeichnung'
            Name = 'Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 6
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 7
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 11
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 4
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 12
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 1
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 232
      Top = 117
      Width = 577
      Height = 220
      TabOrder = 2
      JQDialogOptions.Height = 220
      JQDialogOptions.Width = 577
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 16
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Ma'#223'nahmenkatalog:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 86
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 16
        Top = 48
        Width = 118
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bkbtyp:'
      end
      object iwcbBkbtypen: TIWCGJQComboBoxEx
        Left = 152
        Top = 48
        Width = 400
        Height = 21
        TabOrder = 9
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 398
        Caption = ''
      end
      object iwcbMassnahmenkatalog: TIWCGJQComboBoxEx
        Left = 152
        Top = 16
        Width = 400
        Height = 21
        TabOrder = 8
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 398
        Caption = ''
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 152
        Top = 86
        Width = 400
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quMangeltypen
    KeyFields = 'ID'
    Left = 976
    Top = 16
  end
  object quMangeltypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT m.ID AS "ID", m.ID_Massnahmenkatalog, m.Bkbtyp, m.Bezeich' +
        'nung, mk.Bezeichnung AS "Massnahmenkatalog", b.Bezeichnung AS "B' +
        'ezBkbtyp"'
      'FROM   Stammdaten.Mangeltypen m'
      
        '        INNER JOIN Stammdaten.Massnahmenkatalog mk ON mk.ID = m.' +
        'ID_Massnahmenkatalog'
      '        INNER JOIN Stammdaten.Bkbtypen b ON m.bkbtyp = b.bkbtyp'
      'WHERE  b.Aktiv <> 0;')
    Left = 681
    Top = 17
    object quMangeltypenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quMangeltypenID_Massnahmenkatalog: TIntegerField
      FieldName = 'ID_Massnahmenkatalog'
      Origin = 'ID_Massnahmenkatalog'
      Required = True
    end
    object quMangeltypenBkbtyp: TStringField
      FieldName = 'Bkbtyp'
      Origin = 'Bkbtyp'
      Required = True
      Size = 10
    end
    object quMangeltypenBezeichnung: TStringField
      FieldName = 'Bezeichnung'
      Origin = 'Bezeichnung'
      Size = 200
    end
    object quMangeltypenMassnahmenkatalog: TStringField
      FieldName = 'Massnahmenkatalog'
      Origin = 'Massnahmenkatalog'
      Required = True
      Size = 200
    end
    object quMangeltypenBezBkbtyp: TStringField
      FieldName = 'BezBkbtyp'
      Origin = 'BezBkbtyp'
      Required = True
      Size = 50
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Mangeltypen (ID_Massnahmenkatalog, Bezeic' +
        'hnung, Bkbtyp)'
      'VALUES      (:ID_Massnahmenkatalog, :Bezeichnung, :Bkbtyp);')
    Left = 737
    Top = 17
    ParamData = <
      item
        Name = 'ID_MASSNAHMENKATALOG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Mangeltypen'
      'SET    Bezeichnung = :bezeichnung'
      'WHERE  ID = :id;')
    Left = 793
    Top = 17
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quMassnahmenkatalog: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Massnahmenkatalog;')
    Left = 880
    Top = 16
  end
  object quBkbtypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bkbtypen'
      'WHERE  Sichtbar = 1 AND Aktiv = 1;')
    Left = 681
    Top = 65
  end
end
