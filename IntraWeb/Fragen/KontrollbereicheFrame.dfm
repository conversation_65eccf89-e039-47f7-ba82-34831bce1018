inherited Kontrollbereiche: TKontrollbereiche
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 5
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 7
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 0
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bezeichnung'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 9
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 4
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 1
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 3
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 6
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 8
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Top = 86
      Width = 329
      Height = 155
      TabOrder = 11
      JQDialogOptions.Height = 155
      JQDialogOptions.Width = 329
      object IWLabel1: TIWCGJQLabel
        Left = 24
        Top = 32
        Width = 83
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 113
        Top = 32
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quKontrollbereiche
    KeyFields = 'ID'
  end
  object quKontrollbereiche: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Kontrollbereiche;')
    Left = 681
    Top = 17
    object quKontrollbereicheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quKontrollbereicheBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 200
    end
    object quKontrollbereicheVIS_KKATTID: TStringField
      FieldName = 'VIS_KKATTID'
      Origin = 'VIS_KKATTID'
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Stammdaten.Kontrollbereiche (Bezeichnung)'
      'VALUES      (:bezeichnung);')
    Left = 753
    Top = 17
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Kontrollbereiche'
      'SET    Bezeichnung = :bezeichnung'
      'WHERE  ID = :id;')
    Left = 809
    Top = 17
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
