﻿unit KontrollbereicheFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent,
  IWBaseHTML<PERSON>omponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWApplication, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl,
  IWCompLabel, IWCGJQEdit, IWCGJQRegion, IWCGJQLabel;

type
  TKontrollbereiche = class(TCRUDGrid)
    quKontrollbereiche: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    quKontrollbereicheID: TFDAutoIncField;
    quKontrollbereicheBEZEICHNUNG: TStringField;
    quKontrollbereicheVIS_KKATTID: TStringField;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert;
  bearbeitbar: Boolean);
    procedure KontrollbereicheAbfragen;
  end;

var
  Kontrollbereiche: TKontrollbereiche;

implementation

uses Utility, dmmain, JQ.Helpers.Grid;

{$R *.dfm}


constructor TKontrollbereiche.Create(AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert;
  bearbeitbar: Boolean);
begin
  inherited Create(AOwner, AAlert);

  FAlert := AAlert;
  
  EnableAbfragen(quKontrollbereiche, KontrollbereicheAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neuen Kontrollbereich erstellen', Nil, NeuBestaetigt);
    EnableAendern('Kontrollbereich ändern', InitAendern, AendernBestaetigt);
  end;
  jqgGrid.SetupDefaults(jqdpGrid);
end;

procedure TKontrollbereiche.KontrollbereicheAbfragen;
begin
  RefreshQuery(quKontrollbereiche);
end;

procedure TKontrollbereiche.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.Execute;
end;

procedure TKontrollbereiche.InitAendern;
begin
  jqgGrid.SelectRecordFromCurrentRow;
  jqeBezeichnung.Text := quKontrollbereiche.FieldByName('bezeichnung').AsString;
end;

procedure TKontrollbereiche.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('id').AsInteger :=
    quKontrollbereiche.FieldByName('id').AsInteger;
  quAendern.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quAendern.Execute;
end;

procedure TKontrollbereiche.ResetModal;
begin
  jqeBezeichnung.Text := '';
end;

end.
