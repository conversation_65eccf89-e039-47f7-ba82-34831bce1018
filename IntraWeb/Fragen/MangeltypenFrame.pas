﻿unit MangeltypenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWCGJQEdit, IWCompListbox, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWApplication, Classes.Fragen, IWCGJQRegion, IWCGJQCombobox, IWCGJQLabel,
  JQ.Helpers.ComboboxEx;

type
  TMangeltypen = class(TCRUDGrid)
    quMangeltypen: TFDQuery;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    quMassnahmenkatalog: TFDQuery;
    quBkbtypen: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    iwcbMassnahmenkatalog: TIWCGJQComboboxEx;
    IWLabel2: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    IWLabel4: TIWCGJQLabel;
    iwcbBkbtypen: TIWCGJQComboboxEx;
    quMangeltypenID: TFDAutoIncField;
    quMangeltypenID_Massnahmenkatalog: TIntegerField;
    quMangeltypenBkbtyp: TStringField;
    quMangeltypenBezeichnung: TStringField;
    quMangeltypenMassnahmenkatalog: TStringField;
    quMangeltypenBezBkbtyp: TStringField;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent;
      AAlert: TIWCGJQSweetAlert;
      bearbeitbar: Boolean);
    procedure MangeltypenAbfragen;
    function AusgewaehltenMangeltyp: TMangeltyp;
  end;

var
  Mangeltypen: TMangeltypen;

implementation

uses dmmain, Utility;

{$R *.dfm}


constructor TMangeltypen.Create(AOwner: TComponent;
  AAlert: TIWCGJQSweetAlert;
  bearbeitbar: Boolean);
begin
  inherited Create(AOwner, AAlert);

  falert := AAlert;

  EnableAbfragen(quMangeltypen, MangeltypenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neuen Mangeltyp erstellen', Nil, NeuBestaetigt);
    EnableAendern('Mangeltyp ändern', InitAendern, AendernBestaetigt);
  end;

  // Maßnahmenkatalog einträge in die Combobox geben
  RefreshQuery(quMassnahmenkatalog);
  while not quMassnahmenkatalog.Eof do
  begin
    iwcbMassnahmenkatalog.Items.Add(
      quMassnahmenkatalog.FieldByName('bezeichnung').AsString,
      quMassnahmenkatalog.FieldByName('id').AsString);
    quMassnahmenkatalog.Next;
  end;
  quMassnahmenkatalog.Close;

  RefreshQuery(quBkbtypen);
  while not quBkbtypen.Eof do
  begin
    iwcbBkbtypen.Items.Add(
      quBkbtypen.FieldByName('bezeichnung').AsString,
      quBkbtypen.FieldByName('bkbtyp').AsString);
    quBkbtypen.Next;
  end;
  quBkbtypen.Close;
end;

procedure TMangeltypen.MangeltypenAbfragen;
begin
  RefreshQuery(quMangeltypen);
end;

procedure TMangeltypen.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.ParamByName('bkbtyp').AsString := iwcbBkbtypen.SelectedItem.Value;
  quNeu.ParamByName('id_massnahmenkatalog').AsInteger :=
    StrToInt(iwcbMassnahmenkatalog.SelectedValue);
  quNeu.Execute;
end;

procedure TMangeltypen.InitAendern;
var
  index: Integer;
begin
  jqeBezeichnung.Text := quMangeltypen.FieldByName('bezeichnung').AsString;

  index := iwcbMassnahmenkatalog.Items.IndexOfName(quMangeltypen.FieldByName('massnahmenkatalog').AsString);
  iwcbMassnahmenkatalog.ItemIndex := index;

  index := iwcbBkbtypen.Items.IndexOfName(quMangeltypen.FieldByName('BezBkbtyp').AsString);
  iwcbBkbtypen.ItemIndex := index;

  iwcbBkbtypen.Enabled := false;
  iwcbMassnahmenkatalog.Enabled := false;
end;

procedure TMangeltypen.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.Prepare;
  quAendern.ParamByName('id').AsInteger :=
    quMangeltypen.FieldByName('id').AsInteger;
  quAendern.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quAendern.Execute;
end;

function TMangeltypen.AusgewaehltenMangeltyp;
var
  mangeltyp: TMangeltyp;
begin
  if not moveQueryToRow(quMangeltypen, jqgGrid) then
  begin
    Result := Nil;
    Exit;
  end;
  mangeltyp := TMangeltyp.Create;
  try
    mangeltyp.id := quMangeltypen.FieldByName('id').AsInteger;
    mangeltyp.bezeichnung := quMangeltypen.FieldByName('bezeichnung').AsString;
    mangeltyp.bkbtyp := quMangeltypen.FieldByName('bkbtyp').AsString;
    mangeltyp.massnahmenKatalogId := quMangeltypen.FieldByName('id_massnahmenkatalog').AsInteger;
    Result := mangeltyp;
  except
    mangeltyp.Free;
    Result := Nil;
  end;
end;

procedure TMangeltypen.ResetModal;
begin
  jqeBezeichnung.Text := '';
  iwcbMassnahmenkatalog.ItemIndex := -1;
  iwcbBkbtypen.ItemIndex := -1;
  iwcbMassnahmenkatalog.Enabled := true;
  iwcbBkbtypen.Enabled := true;
end;

end.
