object DMChecklisten: TDMChecklisten
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 786
  Width = 1176
  object quExistierenBewerteteFragen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT COUNT(bf.ID) AS "Anzahl"'
      'FROM   Bewegungsdaten.Bewertete_Fragen bf'
      '        INNER JOIN Stammdaten.Fragen f ON bf.ID_Frage = f.ID'
      'WHERE  f.ID_Checkliste = :id_checkliste;')
    Left = 186
    Top = 59
    ParamData = <
      item
        Name = 'ID_CHECKLISTE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object quExistierenBewerteteFragenAnzahl: TIntegerField
      FieldName = 'Anzahl'
      Origin = 'Anzahl'
      ReadOnly = True
    end
  end
  object quBewertungen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bewertungen'
      'ORDER BY Bezeichnung')
    Left = 192
    Top = 8
    object quBewertungenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quBewertungenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 200
    end
    object quBewertungenTYP: TStringField
      FieldName = 'TYP'
      Origin = 'TYP'
      Size = 10
    end
    object quBewertungenICON: TStringField
      FieldName = 'ICON'
      Origin = 'ICON'
      Size = 10
    end
  end
  object quMassnahmenkataloge: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Massnahmenkatalog;')
    Left = 296
    Top = 8
  end
  object quMangeltypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Mangeltypen;')
    Left = 400
    Top = 8
  end
  object quMassnahmen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Massnahmen;')
    Left = 480
    Top = 8
  end
  object quKontrollbereiche: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Kontrollbereiche;')
    Left = 568
    Top = 8
  end
  object quFormatierungen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Formatierungen;')
    Left = 664
    Top = 8
  end
  object quFrKontrollbereiche: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Fragen_Kontrollbereiche fk'
      
        '        INNER JOIN Stammdaten.Kontrollbereiche k ON fk.ID_Kontro' +
        'llbereich = k.ID'
      'WHERE  fk.ID_Frage = :id_frage;')
    Left = 746
    Top = 59
    ParamData = <
      item
        Name = 'ID_FRAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFrFragenbewertung: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Fragen_bewertungen fb'
      'WHERE  fb.ID_Frage = :id_frage;')
    Left = 746
    Top = 107
    ParamData = <
      item
        Name = 'ID_FRAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object QFragenLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Fragen'
      'WHERE  ID_Checkliste = :id_checkliste;')
    Left = 186
    Top = 227
    ParamData = <
      item
        Name = 'ID_CHECKLISTE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFrBewSpeichern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Fragen_Bewertungen (Id_Frage, Id_Bewertun' +
        'g, ausblenden, positiv, id_standard_massnahme, id_standard_mange' +
        'ltyp, sortierung)'
      
        'VALUES      (:Id_Frage, :Id_Bewertung, :ausblenden, :positiv, :i' +
        'd_standard_massnahme, :id_standard_mangeltyp, :position);')
    Left = 512
    Top = 232
    ParamData = <
      item
        Name = 'ID_FRAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_BEWERTUNG'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'AUSBLENDEN'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POSITIV'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_STANDARD_MASSNAHME'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_STANDARD_MANGELTYP'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'POSITION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFrKobSpeichern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Fragen_Kontrollbereiche(ID_Frage, ID_Kont' +
        'rollbereich)'
      'VALUES      (:id_frage, :id_kontrollbereich);')
    Left = 632
    Top = 232
    ParamData = <
      item
        Name = 'ID_FRAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_KONTROLLBEREICH'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quFrageSpeichern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Fragen (ID_Uebergeordnete_Frage,  ID_Exte' +
        'rn,  Extern_Quelle,  ID_Checkliste,  LFNR,  Fragennr, CCRelevant' +
        ',  Text,  Info,  Formatierung,  ID_Gruppe)'
      
        'VALUES                       (:ID_Uebergeordnete_Frage, :ID_Exte' +
        'rn, :Extern_Quelle, :ID_Checkliste, :LFNR, :Fragennr, :CCRelevan' +
        't, :Text, :Info, :Formatierung, :Id_Gruppe);')
    Left = 384
    Top = 232
    ParamData = <
      item
        Name = 'ID_UEBERGEORDNETE_FRAGE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_EXTERN'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'EXTERN_QUELLE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_CHECKLISTE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'LFNR'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FRAGENNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CCRELEVANT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = False
      end
      item
        Name = 'TEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'INFO'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'FORMATIERUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_GRUPPE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 1
      end>
  end
  object MTFragen: TFDMemTable
    FieldDefs = <>
    IndexDefs = <>
    IndexFieldNames = 'LFNR;FragenNr'
    FetchOptions.AssignedValues = [evMode]
    FetchOptions.Mode = fmAll
    ResourceOptions.AssignedValues = [rvPersistent, rvSilentMode]
    ResourceOptions.Persistent = True
    ResourceOptions.SilentMode = True
    UpdateOptions.AssignedValues = [uvCheckRequired, uvAutoCommitUpdates]
    UpdateOptions.CheckRequired = False
    UpdateOptions.AutoCommitUpdates = True
    StoreDefs = True
    Left = 184
    Top = 368
    object MTFragenID: TIntegerField
      FieldName = 'ID'
    end
    object MTFragenID_Parent: TIntegerField
      FieldName = 'ID_Parent'
    end
    object MTFragenLevel: TIntegerField
      FieldName = 'Level'
    end
    object MTFragenIsLeaf: TBooleanField
      FieldName = 'IsLeaf'
    end
    object MTFragenText: TStringField
      FieldName = 'Text'
      Size = 500
    end
    object MTFragenInfo: TWideMemoField
      FieldName = 'Info'
      Origin = 'Info'
      BlobType = ftWideMemo
      Size = 2147483647
    end
    object MTFragenFragenNr: TStringField
      DisplayWidth = 20
      FieldName = 'FragenNr'
      Origin = 'FragenNr'
    end
    object MTFragenExpanded: TBooleanField
      FieldName = 'expanded'
    end
    object MTFragenLfNr: TIntegerField
      FieldName = 'LfNr'
    end
    object MTFragenID_Extern: TStringField
      FieldName = 'ID_Extern'
      Size = 100
    end
    object MTFragenID_Gruppe: TIntegerField
      FieldName = 'ID_Gruppe'
    end
    object MTFragenFormatierung: TStringField
      FieldName = 'Formatierung'
      Size = 10
    end
    object MTFragenBewertungen: TStringField
      FieldName = 'Bewertungen'
      Size = 1000
    end
    object MTFragenCCRelevant: TBooleanField
      FieldName = 'CCRelevant'
    end
    object MTFragenKontrollbereiche: TStringField
      FieldName = 'Kontrollbereiche'
      Size = 8000
    end
  end
  object QFragen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select *'
      'from STAMMDATEN.FRAGEN F'
      
        'join STAMMDATEN.vFragenKontrollbereiche vFK on vFK.ID_Frage = F.' +
        'ID'
      'where ID_CHECKLISTE = :IDCheckliste'
      'order by LfNr')
    Left = 272
    Top = 368
    ParamData = <
      item
        Name = 'IDCHECKLISTE'
        DataType = ftString
        ParamType = ptInput
        Value = '66'
      end>
    object QFragenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QFragenID_UEBERGEORDNETE_FRAGE: TIntegerField
      FieldName = 'ID_UEBERGEORDNETE_FRAGE'
      Origin = 'ID_UEBERGEORDNETE_FRAGE'
    end
    object QFragenID_EXTERN: TStringField
      FieldName = 'ID_EXTERN'
      Origin = 'ID_EXTERN'
      Size = 100
    end
    object QFragenEXTERN_QUELLE: TStringField
      FieldName = 'EXTERN_QUELLE'
      Origin = 'EXTERN_QUELLE'
      Size = 200
    end
    object QFragenID_CHECKLISTE: TIntegerField
      FieldName = 'ID_CHECKLISTE'
      Origin = 'ID_CHECKLISTE'
    end
    object QFragenID_GRUPPE: TIntegerField
      FieldName = 'ID_GRUPPE'
      Origin = 'ID_GRUPPE'
      Required = True
    end
    object QFragenLFNR: TIntegerField
      FieldName = 'LFNR'
      Origin = 'LFNR'
    end
    object QFragenFRAGENNR: TStringField
      DisplayWidth = 20
      FieldName = 'FRAGENNR'
      Origin = 'FRAGENNR'
    end
    object QFragenTEXT: TWideStringField
      FieldName = 'TEXT'
      Origin = 'TEXT'
      Required = True
      Size = 500
    end
    object QFragenINFO: TWideMemoField
      FieldName = 'INFO'
      Origin = 'INFO'
      BlobType = ftWideMemo
      Size = 2147483647
    end
    object QFragenFORMATIERUNG: TStringField
      FieldName = 'FORMATIERUNG'
      Origin = 'FORMATIERUNG'
      Size = 10
    end
    object QFragenCCRelevant: TBooleanField
      FieldName = 'CCRelevant'
      Origin = 'CCRelevant'
    end
    object QFragenKontrollbereiche: TStringField
      FieldName = 'Kontrollbereiche'
      Origin = 'Kontrollbereiche'
      Size = 8000
    end
  end
  object QAktiveKontrolltypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select K.BKBTYP + '#39';'#39' + K.KONTROLLTYP        as ID,'
      '       B.BEZEICHNUNG + '#39' | '#39' + K.BEZEICHNUNG as DisplayName,'
      '       B.AKTIV,'
      '       B.BEGDAT,'
      '       B.ENDDAT,'
      '       B.BEZEICHNUNG                            BKB_Bezeichnung,'
      '       K.*'
      'from STAMMDATEN.KONTROLLTYPEN K'
      '         inner join STAMMDATEN.BKBTYPEN B on K.BKBTYP = B.BKBTYP'
      
        '         left join SYSTEMSTAMMDATEN.MODULE M on B.MODUL = M.MODU' +
        'L'
      
        '         inner join SYSTEMSTAMMDATEN.BUNDESLAENDER_MODULE BM on ' +
        'M.MODUL = BM.MODUL'
      
        '         left join SYSTEMSTAMMDATEN.BUNDESLAENDER BLD on BM.BLDC' +
        'ODE = BLD.BLDCODE'
      'where K.SICHTBAR = 1'
      '  and B.SICHTBAR = 1'
      '  and B.AKTIV = 1'
      '  and B.BEGDAT <= getdate()'
      '  and B.ENDDAT >= getdate()'
      'and BLD.BLDCODE = :BLDCODE'
      'order by K.BKBTYP, K.KONTROLLTYP')
    Left = 561
    Top = 377
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftString
        ParamType = ptInput
        Value = '300'
      end>
    object QAktiveKontrolltypenAKTIV: TBooleanField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
      Required = True
    end
    object QAktiveKontrolltypenBEGDAT: TDateField
      FieldName = 'BEGDAT'
      Origin = 'BEGDAT'
      Required = True
    end
    object QAktiveKontrolltypenENDDAT: TDateField
      FieldName = 'ENDDAT'
      Origin = 'ENDDAT'
      Required = True
    end
    object QAktiveKontrolltypenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object QAktiveKontrolltypenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      FixedChar = True
      Size = 3
    end
    object QAktiveKontrolltypenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Size = 80
    end
    object QAktiveKontrolltypenREFBKB: TSmallintField
      FieldName = 'REFBKB'
      Origin = 'REFBKB'
    end
    object QAktiveKontrolltypenFREIE_ADRESSE: TBooleanField
      FieldName = 'FREIE_ADRESSE'
      Origin = 'FREIE_ADRESSE'
      Required = True
    end
    object QAktiveKontrolltypenSICHTBAR: TBooleanField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
      Required = True
    end
    object QAktiveKontrolltypenPROBEN: TBooleanField
      FieldName = 'PROBEN'
      Origin = 'PROBEN'
      Required = True
    end
    object QAktiveKontrolltypenOERTLICHKEITEN: TBooleanField
      FieldName = 'OERTLICHKEITEN'
      Origin = 'OERTLICHKEITEN'
      Required = True
    end
    object QAktiveKontrolltypenAUTO_KONTROLLIERT: TBooleanField
      FieldName = 'AUTO_KONTROLLIERT'
      Origin = 'AUTO_KONTROLLIERT'
      Required = True
    end
    object QAktiveKontrolltypenBKB_Bezeichnung: TStringField
      FieldName = 'BKB_Bezeichnung'
      Origin = 'BKB_Bezeichnung'
      Required = True
      Size = 50
    end
    object QAktiveKontrolltypenID: TStringField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
      Required = True
      Size = 14
    end
    object QAktiveKontrolltypenDisplayName: TStringField
      FieldName = 'DisplayName'
      Origin = 'DisplayName'
      ReadOnly = True
      Size = 133
    end
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'insert into STAMMDATEN.CHECKLISTEN (BEZEICHNUNG,'
      '                                    GUELTIG_AB,'
      '                                    GUELTIG_BIS,'
      '                                    VERSION,'
      '                                    VERSIONSTEXT,'
      '                                    PRIVAT,'
      '                                    CCRelevant,'
      '                                    VIS_BKBTID,'
      '                                    CHANGE_DBUSER,'
      '                                    BESITZER_BLDCODE)'
      'values (:bezeichnung,'
      '        :gueltig_ab,'
      '        :gueltig_bis,'
      '        :version,'
      '        :versionstext,'
      '        :privat,'
      '        :CCrelevant,'
      '        :VisBkbId,'
      '        :User,'
      '        :Besitzer_BLD)')
    Left = 713
    Top = 385
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GUELTIG_AB'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GUELTIG_BIS'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VERSION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VERSIONSTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PRIVAT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CCRELEVANT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VISBKBID'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BESITZER_BLD'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'update STAMMDATEN.CHECKLISTEN'
      'set BEZEICHNUNG   = :bezeichnung,'
      '    GUELTIG_AB    = :gueltig_ab,'
      '    GUELTIG_BIS   = :gueltig_bis,'
      '    VERSION       = :version,'
      '    VERSIONSTEXT  = :versionstext,'
      '    CHANGE_DBUSER = :User,'
      '    CCRelevant =    :CCRelevant,'
      '    Vis_BKBTID =    :VisBkbId, '
      '    PRIVAT        = :Privat,'
      '    BESITZER_BLDCODE = :Besitzer_BLD'
      'where ID = :id')
    Left = 561
    Top = 521
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GUELTIG_AB'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'GUELTIG_BIS'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VERSION'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VERSIONSTEXT'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'USER'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CCRELEVANT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VISBKBID'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'PRIVAT'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BESITZER_BLD'
        ParamType = ptInput
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quChecklisten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select c.ID,'
      '       c.BEZEICHNUNG,'
      '       c.GUELTIG_AB,'
      '       c.GUELTIG_BIS,'
      '       c.VERSION,'
      '       c.VERSIONSTEXT,'
      '       c.CCRelevant,'
      '       c.BESITZER_BLDCODE,'
      '       B.BEZEICHNUNG as BESITZER,'
      '       c.PRIVAT,'
      '       c.VIS_BKBTID,'
      '       c.CHANGE_DBUSER as LASTCHANGE_USERID,'
      
        '       RTRIM(LTRIM(COALESCE(P.TITEL, '#39#39') + '#39' '#39' + COALESCE(P.VORN' +
        'AME, '#39#39') + '#39' '#39' + COALESCE(P.NACHNAME, '#39#39'))) as LASTCHANGE_PERSON' +
        ','
      '       c.LASTCHANGE,'
      '       t.NAME        as VIS_BKBTID_NAME,'
      ''
      '       case'
      '           when'
      
        '               -- Wir z'#228'hlen ob es Kontrolltypen gibt - wenn sel' +
        'bst Besitzer'
      '                       c.BESITZER_BLDCODE = :BLDCODE'
      '                   and ('
      '                           select count(ID_CHECKLISTEN)'
      
        '                           from STAMMDATEN.CHECKLISTEN_KONTROLLT' +
        'YPEN CK'
      '                           where ID_CHECKLISTEN = c.ID'
      '                       ) > 0'
      '               then 1'
      '           when'
      '                       c.BESITZER_BLDCODE = :BLDCODE'
      '                   and ('
      '                           select count(ID_CHECKLISTEN)'
      
        '                           from STAMMDATEN.CHECKLISTEN_KONTROLLT' +
        'YPEN CK'
      '                           where ID_CHECKLISTEN = c.ID'
      '                       ) = 0'
      '               then 0'
      ''
      '           -- Fremde Checklisten sind immer in Verwendung'
      '           when c.BESITZER_BLDCODE <> :BLDCODE'
      '               then 1'
      '           end       as InVerwendung'
      ''
      'from STAMMDATEN.CHECKLISTEN c'
      
        '         left join VISDATEN.TVMET_BKBTYP t on c.VIS_BKBTID = t.B' +
        'KBTID'
      
        '         left join SYSTEMSTAMMDATEN.BUNDESLAENDER B on c.BESITZE' +
        'R_BLDCODE = B.BLDCODE'
      
        '         left join SYSTEMSTAMMDATEN.[USER] U on c.CHANGE_DBUSER ' +
        '= U.ID'
      '         left join STAMMDATEN.PERSONEN P on P.ID = U.ID_PERSON'
      'where (c.BESITZER_BLDCODE = :BLDCODE'
      '    or c.PRIVAT = 0)'
      '  and GUELTIG_BIS >= :Today')
    Left = 713
    Top = 441
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftString
        ParamType = ptInput
        Value = '300'
      end
      item
        Name = 'TODAY'
        DataType = ftDateTime
        ParamType = ptInput
        Value = 44567d
      end>
    object quChecklistenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object quChecklistenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object quChecklistenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
    end
    object quChecklistenGUELTIG_BIS: TDateField
      FieldName = 'GUELTIG_BIS'
      Origin = 'GUELTIG_BIS'
    end
    object quChecklistenVERSION: TIntegerField
      FieldName = 'VERSION'
      Origin = 'VERSION'
      Required = True
    end
    object quChecklistenVERSIONSTEXT: TStringField
      FieldName = 'VERSIONSTEXT'
      Origin = 'VERSIONSTEXT'
      Required = True
      Size = 255
    end
    object quChecklistenCCRelevant: TBooleanField
      FieldName = 'CCRelevant'
      Origin = 'CCRelevant'
      Required = True
    end
    object quChecklistenBESITZER_BLDCODE: TSmallintField
      FieldName = 'BESITZER_BLDCODE'
      Origin = 'BESITZER_BLDCODE'
      Required = True
    end
    object quChecklistenBESITZER: TStringField
      FieldName = 'BESITZER'
      Origin = 'BESITZER'
      Size = 30
    end
    object quChecklistenPRIVAT: TBooleanField
      FieldName = 'PRIVAT'
      Origin = 'PRIVAT'
    end
    object quChecklistenVIS_BKBTID: TStringField
      FieldName = 'VIS_BKBTID'
      Origin = 'VIS_BKBTID'
      Size = 10
    end
    object quChecklistenVIS_BKBTID_NAME: TStringField
      FieldName = 'VIS_BKBTID_NAME'
      Origin = 'VIS_BKBTID_NAME'
      Size = 150
    end
    object quChecklistenInVerwendung: TIntegerField
      FieldName = 'InVerwendung'
      Origin = 'InVerwendung'
      ReadOnly = True
    end
    object quChecklistenLASTCHANGE_USERID: TIntegerField
      FieldName = 'LASTCHANGE_USERID'
      Origin = 'LASTCHANGE_USERID'
      Required = True
    end
    object quChecklistenLASTCHANGE_PERSON: TStringField
      FieldName = 'LASTCHANGE_PERSON'
      Origin = 'LASTCHANGE_PERSON'
      ReadOnly = True
      Size = 142
    end
    object quChecklistenLASTCHANGE: TSQLTimeStampField
      FieldName = 'LASTCHANGE'
      Origin = 'LASTCHANGE'
      Required = True
    end
  end
  object DSChecklisten: TDataSource
    DataSet = quChecklisten
    Left = 720
    Top = 504
  end
  object QChecklisteKontrolltypen: TFDQuery
    BeforePost = QChecklisteKontrolltypenBeforePost
    MasterSource = DSChecklisten
    MasterFields = 'ID'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'select cast(BCK.BLDCODE as varchar(10)) + cast(BCK.ID_CHECKLISTE' +
        'N as varchar(15)) + BCK.BKBTYP + BCK.KONTROLLTYP as ID,'
      '       BCK.*,'
      
        '       B.BEZEICHNUNG                                            ' +
        '                                                 as BKB_Bezeichn' +
        'ung,'
      
        '       K.BEZEICHNUNG                                            ' +
        '                                                 as Kontrolltyp_' +
        'Bezeichnung,'
      '       case'
      
        '           when (BCK.GUELTIG_AB > getdate()) or (BCK.GUELTIG_BIS' +
        ' < getdate())'
      '               then 0'
      
        '           else 1 end                                           ' +
        '                                                 as Gueltig'
      ''
      'from STAMMDATEN.BUNDESLAENDER_CHECKLISTEN_KONTROLLTYPEN BCK'
      
        '         left join STAMMDATEN.KONTROLLTYPEN K on BCK.KONTROLLTYP' +
        ' = K.KONTROLLTYP and BCK.BKBTYP = K.BKBTYP'
      
        '         left join STAMMDATEN.BKBTYPEN B on BCK.BKBTYP = B.BKBTY' +
        'P'
      'where BCK.ID_CHECKLISTEN = :ID'
      '  and BCK.BLDCODE = :BLDCODE'
      'order by '
      'Gueltig desc, BKBTYP, KONTROLLTYP')
    Left = 720
    Top = 576
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = 25
      end
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end>
    object QChecklisteKontrolltypenID: TStringField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
      Size = 38
    end
    object QChecklisteKontrolltypenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QChecklisteKontrolltypenID_CHECKLISTEN: TIntegerField
      FieldName = 'ID_CHECKLISTEN'
      Origin = 'ID_CHECKLISTEN'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QChecklisteKontrolltypenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object QChecklisteKontrolltypenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      FixedChar = True
      Size = 3
    end
    object QChecklisteKontrolltypenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
    end
    object QChecklisteKontrolltypenGUELTIG_BIS: TDateField
      FieldName = 'GUELTIG_BIS'
      Origin = 'GUELTIG_BIS'
    end
    object QChecklisteKontrolltypenBKB_Bezeichnung: TStringField
      FieldName = 'BKB_Bezeichnung'
      Origin = 'BKB_Bezeichnung'
      Size = 50
    end
    object QChecklisteKontrolltypenKontrolltyp_Bezeichnung: TStringField
      FieldName = 'Kontrolltyp_Bezeichnung'
      Origin = 'Kontrolltyp_Bezeichnung'
      Size = 80
    end
    object QChecklisteKontrolltypenGueltig: TIntegerField
      FieldName = 'Gueltig'
      Origin = 'Gueltig'
      ReadOnly = True
      Required = True
    end
  end
  object QKontrollTypZuweisenDirekt: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      '-- direkte Zuweisung'
      'insert into STAMMDATEN.CHECKLISTEN_KONTROLLTYPEN'
      '    (ID_CHECKLISTEN, BKBTYP, KONTROLLTYP)'
      'values (:ID_CHECKLISTEN, :BKBTYP, :KONTROLLTYP)')
    Left = 880
    Top = 376
    ParamData = <
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QKontrolltypZuweisenBundesland: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      ''
      ''
      '--Bundeslandzuweisung'
      'insert into STAMMDATEN.BUNDESLAENDER_CHECKLISTEN_KONTROLLTYPEN'
      
        '    (BLDCODE, ID_CHECKLISTEN, BKBTYP, KONTROLLTYP, GUELTIG_AB, G' +
        'UELTIG_BIS)'
      
        'values (:BLDCODE, :ID_CHECKLISTEN, :BKBTYP, :KONTROLLTYP, :Begin' +
        'Date, :EndDate);')
    Left = 896
    Top = 512
    ParamData = <
      item
        Name = 'BLDCODE'
        ParamType = ptInput
      end
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGINDATE'
        ParamType = ptInput
      end
      item
        Name = 'ENDDATE'
        ParamType = ptInput
      end>
  end
  object QKontrollTypZuweisungBLDLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'delete'
      'from STAMMDATEN.BUNDESLAENDER_CHECKLISTEN_KONTROLLTYPEN'
      'where BLDCODE = :BLDCODE'
      '  and ID_CHECKLISTEN = :ID_CHECKLISTEN'
      '  and BKBTYP = :BKBTYP'
      '  and KONTROLLTYP = :KONTROLLTYP')
    Left = 896
    Top = 608
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QKontrolltypVerwendng: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        '--Alle Kontrollen, die den Kontrolltyp verwenden und nicht gepla' +
        'nt bzw ungeplant sind'
      'select K.ID'
      'from BEWEGUNGSDATEN.KONTROLLBERICHT K'
      
        '         left join STAMMDATEN.PERSONEN P on K.ID_PERSON_ERFASSER' +
        ' = P.ID'
      
        '         left join SYSTEMSTAMMDATEN.[USER] U on P.ID = U.ID_PERS' +
        'ON'
      
        '         left join BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS vKS on' +
        ' K.ID = vKS.ID'
      'where U.BLDCODE = :BLDCODE'
      '  and K.BKBTYP = :BKBTYP'
      '  and K.KONTROLLTYP = :KONTROLLTYP'
      '  and vKS.STATUS not in ('#39'P'#39', '#39'U'#39')')
    Left = 1072
    Top = 608
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = 'AMA-CCK'
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = 'C01'
      end>
    object QKontrolltypVerwendngID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
  end
  object QKontrolltypZuweisungLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'delete'
      'from STAMMDATEN.CHECKLISTEN_KONTROLLTYPEN'
      'where '
      '  ID_CHECKLISTEN = :ID_CHECKLISTEN'
      '  and BKBTYP = :BKBTYP'
      '  and KONTROLLTYP = :KONTROLLTYP')
    Left = 896
    Top = 688
    ParamData = <
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QAktiveKontrolltypenCheckliste: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'select CK.BKBTYP + '#39';'#39' + CK.KONTROLLTYP as ID, B.BEZEICHNUNG + '#39 +
        ' | '#39' + K.BEZEICHNUNG as DisplayName'
      'from STAMMDATEN.CHECKLISTEN_KONTROLLTYPEN CK'
      
        '         left join STAMMDATEN.KONTROLLTYPEN K on CK.BKBTYP = K.B' +
        'KBTYP and CK.KONTROLLTYP = K.KONTROLLTYP'
      '         inner join STAMMDATEN.BKBTYPEN B on K.BKBTYP = B.BKBTYP'
      
        '         left join SYSTEMSTAMMDATEN.MODULE M on B.MODUL = M.MODU' +
        'L'
      
        '         inner join SYSTEMSTAMMDATEN.BUNDESLAENDER_MODULE BM on ' +
        'M.MODUL = BM.MODUL'
      
        '         left join SYSTEMSTAMMDATEN.BUNDESLAENDER BLD on BM.BLDC' +
        'ODE = BLD.BLDCODE'
      'where K.SICHTBAR = 1'
      '  and B.SICHTBAR = 1'
      '  and B.AKTIV = 1'
      '  and B.BEGDAT <= getdate()'
      '  and B.ENDDAT >= getdate()'
      '  and BLD.BLDCODE = :BLDCODE'
      '  and CK.ID_CHECKLISTEN = :ID'
      'order by K.BKBTYP, K.KONTROLLTYP')
    Left = 561
    Top = 449
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 400
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = 25
      end>
    object QAktiveKontrolltypenChecklisteID: TStringField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
      Required = True
      Size = 14
    end
    object QAktiveKontrolltypenChecklisteDisplayName: TStringField
      FieldName = 'DisplayName'
      Origin = 'DisplayName'
      ReadOnly = True
      Size = 133
    end
  end
  object QKontrollTypZuweisungDirektPruefen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      '-- direkte Zuweisung pr'#252'fen'
      'select *'
      'from STAMMDATEN.CHECKLISTEN_KONTROLLTYPEN'
      ''
      'where ID_CHECKLISTEN = :ID_CHECKLISTEN'
      '  and BKBTYP = :BKBTYP'
      '  and KONTROLLTYP = :KONTROLLTYP')
    Left = 872
    Top = 312
    ParamData = <
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object QVIS_BKBTyp: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'Select * from VISDATEN.TVMET_BKBTYP'
      'order by name')
    Left = 184
    Top = 504
    object QVIS_BKBTypGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QVIS_BKBTypORD: TIntegerField
      FieldName = 'ORD'
      Origin = 'ORD'
      Required = True
    end
    object QVIS_BKBTypBKBTID: TStringField
      FieldName = 'BKBTID'
      Origin = 'BKBTID'
      Required = True
      Size = 10
    end
    object QVIS_BKBTypNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Size = 150
    end
    object QVIS_BKBTypBKBART: TStringField
      FieldName = 'BKBART'
      Origin = 'BKBART'
      FixedChar = True
      Size = 3
    end
    object QVIS_BKBTypBKBERGEB: TStringField
      FieldName = 'BKBERGEB'
      Origin = 'BKBERGEB'
      FixedChar = True
      Size = 1
    end
    object QVIS_BKBTypUNTFORM: TStringField
      FieldName = 'UNTFORM'
      Origin = 'UNTFORM'
      FixedChar = True
      Size = 1
    end
    object QVIS_BKBTypBEGDAT: TDateField
      FieldName = 'BEGDAT'
      Origin = 'BEGDAT'
    end
    object QVIS_BKBTypENDDAT: TDateField
      FieldName = 'ENDDAT'
      Origin = 'ENDDAT'
    end
  end
  object QChecklisteVerwendungKontrollen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select K.ID,'
      '       K.BKB,'
      '       K.GUID,'
      '       K.DATUM,'
      '       concat(P.NACHNAME, '#39', '#39', P.VORNAME) as Erfasser,'
      '       KS.STATUS,'
      '       BL.KURZTEXT as Bundesland'
      'from BEWEGUNGSDATEN.KONTROLLBERICHT K,'
      '     BEWEGUNGSDATEN.vKONTROLLBERICHT_STATUS KS,'
      '     STAMMDATEN.BETRIEBE B,'
      '     SYSTEMSTAMMDATEN.BUNDESLAENDER BL,'
      '     STAMMDATEN.PERSONEN P'
      ''
      'where K.ID = KS.ID'
      '  and K.ID_BETRIEB = B.ID'
      '  and B.BLDCODE = BL.BLDCODE'
      '  and K.ID_PERSON_ERFASSER = P.ID'
      '  and K.ID in'
      '      ('
      '          select BW.ID_BERICHT'
      '          from BEWEGUNGSDATEN.BEWERTETE_FRAGEN BW'
      '          where BW.ID_FRAGE in ('
      '              select ID'
      '              from STAMMDATEN.FRAGEN F'
      '              where ID_CHECKLISTE = :ID_Checkliste'
      '          )'
      '      )')
    Left = 280
    Top = 632
    ParamData = <
      item
        Name = 'ID_CHECKLISTE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 29
      end>
    object QChecklisteVerwendungKontrollenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QChecklisteVerwendungKontrollenBKB: TStringField
      FieldName = 'BKB'
      Origin = 'BKB'
      Required = True
      Size = 26
    end
    object QChecklisteVerwendungKontrollenGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object QChecklisteVerwendungKontrollenDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
    end
    object QChecklisteVerwendungKontrollenSTATUS: TStringField
      FieldName = 'STATUS'
      Origin = 'STATUS'
      ReadOnly = True
      Size = 1
    end
    object QChecklisteVerwendungKontrollenBundesland: TStringField
      FieldName = 'Bundesland'
      Origin = 'Bundesland'
      Required = True
      Size = 2
    end
    object QChecklisteVerwendungKontrollenErfasser: TStringField
      FieldName = 'Erfasser'
      Origin = 'Erfasser'
      ReadOnly = True
      Required = True
      Size = 122
    end
  end
  object QKontrolltypZuweisungBundeslandPruefen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      '--Bundeslandzuweisung'
      ''
      'select *'
      'from STAMMDATEN.BUNDESLAENDER_CHECKLISTEN_KONTROLLTYPEN'
      'where BLDCODE = :BLDCODE'
      '  and KONTROLLTYP = :KONTROLLTYP'
      '  and BKBTYP = :BKBTYP'
      '  and ID_CHECKLISTEN = :ID_CHECKLISTEN')
    Left = 896
    Top = 464
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID_CHECKLISTEN'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
    object QKontrolltypZuweisungBundeslandPruefenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QKontrolltypZuweisungBundeslandPruefenID_CHECKLISTEN: TIntegerField
      FieldName = 'ID_CHECKLISTEN'
      Origin = 'ID_CHECKLISTEN'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QKontrolltypZuweisungBundeslandPruefenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 10
    end
    object QKontrolltypZuweisungBundeslandPruefenKONTROLLTYP: TStringField
      FieldName = 'KONTROLLTYP'
      Origin = 'KONTROLLTYP'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      FixedChar = True
      Size = 3
    end
    object QKontrolltypZuweisungBundeslandPruefenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
    end
    object QKontrolltypZuweisungBundeslandPruefenGUELTIG_BIS: TDateField
      FieldName = 'GUELTIG_BIS'
      Origin = 'GUELTIG_BIS'
    end
  end
end
