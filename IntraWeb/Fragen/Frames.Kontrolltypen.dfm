inherited FrameKontrolltypen: TFrameKontrolltypen
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 9
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 10
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 6
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 500
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VERSIONSTEXT'
            Name = 'VERSIONSTEXT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Version'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GUELTIG_AB'
            Name = 'GUELTIG_AB'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'g'#252'ltig ab'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GUELTIG_BIS'
            Name = 'GUELTIG_BIS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'g'#252'ltig bis'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 0
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 1
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 2
        inherited jqbAbfragen: TIWCGJQButton
          Left = 541
          Top = 30
          Height = 25
          Visible = True
          TabOrder = 7
        end
        inherited jqbNeu: TIWCGJQButton
          Left = 463
          Top = 3
          TabOrder = 8
        end
        inherited jqbAendern: TIWCGJQButton
          Left = 463
          Top = 3
          TabOrder = 3
        end
        inherited jqbLoeschen: TIWCGJQButton
          Left = 463
          Top = 3
          TabOrder = 4
        end
        object dropDown: TIWCGJQDropDown
          Left = 23
          Top = 30
          Width = 498
          Height = 25
          TabOrder = 11
          Version = '1.0'
          DataLink.ListDataSource = DataSourceDropDown
          DataLink.ListFieldNames = 'DISPLAY_NAME'
          DataLink.ListSelectFieldName = 'ID'
          JQDropDownOptions.Ajax.QuietMillis = 100
          JQDropDownOptions.Ajax.Use = True
          JQDropDownOptions.Data.Data = '""'
          JQDropDownOptions.AttachTo = jqddatInput
          JQDropDownOptions.InfiniteScroll = True
          Groups = <>
          Items = <>
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 344
      Top = 126
      TabOrder = 5
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quChecklisten
    Left = 864
    Top = 176
  end
  object quKontrolltypen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'select K.BKBTYP + '#39';'#39' + K.KONTROLLTYP as "ID", B.BEZEICHNUNG + '#39 +
        ' | '#39' + K.BEZEICHNUNG as "DISPLAY_NAME"'
      'from STAMMDATEN.KONTROLLTYPEN K'
      '         inner join STAMMDATEN.BKBTYPEN B on K.BKBTYP = B.BKBTYP'
      
        '         left join SYSTEMSTAMMDATEN.MODULE M on B.MODUL = M.MODU' +
        'L'
      
        '         inner join SYSTEMSTAMMDATEN.BUNDESLAENDER_MODULE BM on ' +
        'M.MODUL = BM.MODUL'
      
        '         left join SYSTEMSTAMMDATEN.BUNDESLAENDER BLD on BM.BLDC' +
        'ODE = BLD.BLDCODE'
      'where K.SICHTBAR = 1'
      '  and B.SICHTBAR = 1'
      '  and B.AKTIV = 1'
      '  and B.BEGDAT <= getdate()'
      '  and B.ENDDAT >= getdate()'
      '  and BLD.BLDCODE = :BLDCODE'
      'order by K.BKBTYP, K.KONTROLLTYP')
    Left = 952
    Top = 176
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object quKontrolltypenID: TStringField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
      Required = True
      Size = 14
    end
    object quKontrolltypenDISPLAY_NAME: TStringField
      FieldName = 'DISPLAY_NAME'
      Origin = 'DISPLAY_NAME'
      ReadOnly = True
      Size = 133
    end
  end
  object quChecklisten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'SELECT c.BEZEICHNUNG, c.VERSIONSTEXT, c.GUELTIG_AB, c.GUELTIG_BI' +
        'S'
      'FROM STAMMDATEN.CHECKLISTEN c'
      
        'INNER JOIN STAMMDATEN.CHECKLISTEN_KONTROLLTYPEN k ON k.ID_CHECKL' +
        'ISTEN = c.ID'
      
        'WHERE k.BKBTYP = :BKBTYP AND k.KONTROLLTYP = :KONTROLLTYP AND GE' +
        'TDATE() BETWEEN c.GUELTIG_AB AND c.GUELTIG_BIS;')
    Left = 952
    Top = 112
    ParamData = <
      item
        Name = 'BKBTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'KONTROLLTYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
    object quChecklistenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Required = True
      Size = 100
    end
    object quChecklistenVERSIONSTEXT: TStringField
      FieldName = 'VERSIONSTEXT'
      Origin = 'VERSIONSTEXT'
      Required = True
      Size = 255
    end
    object quChecklistenGUELTIG_AB: TDateField
      FieldName = 'GUELTIG_AB'
      Origin = 'GUELTIG_AB'
    end
    object quChecklistenGUELTIG_BIS: TDateField
      FieldName = 'GUELTIG_BIS'
      Origin = 'GUELTIG_BIS'
    end
  end
  object DataSourceDropDown: TDataSource
    DataSet = quKontrolltypen
    Left = 856
    Top = 112
  end
end
