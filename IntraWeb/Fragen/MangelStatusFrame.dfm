inherited MangelStatus: TMangelStatus
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 3
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 4
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 0
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BESCHREIBUNG'
            Name = 'BESCHREIBUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'BESCHREIBUNG'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STATUS'
            Name = 'STATUS'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'STATUS'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 5
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 6
      end
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 7
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 1
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 2
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 8
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 9
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 352
      Top = 109
      TabOrder = 10
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quMangelStatus
  end
  object quMangelStatus: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Mangel_Status;')
    Left = 673
    Top = 17
  end
end
