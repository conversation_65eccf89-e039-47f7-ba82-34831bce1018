inherited FrameChecklisten: TFrameChecklisten
  Width = 1443
  Height = 601
  inherited IWFrameRegion: TIWCGJQRegion
    Width = 1443
    Height = 601
    TabOrder = 13
    inherited iwrMid: TIWCGJQRegion
      Top = 60
      Width = 1443
      Height = 541
      TabOrder = 15
      object CheckboxAktiveAnzeigen: TIWCGJQCheckBox [0]
        AlignWithMargins = True
        Left = 3
        Top = 517
        Width = 1437
        Height = 21
        Align = alBottom
        Caption = 'Auch historische Checklisten anzeigen'
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        OnClick = CheckboxAktiveAnzeigenClick
        Checked = False
        FriendlyName = 'CheckboxAktiveAnzeigen'
      end
      inherited jqgGrid: TIWCGJQGrid
        Width = 1443
        Height = 514
        TabOrder = 12
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Bezeichnung'
            Name = 'Bezeichnung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Bezeichnung'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Version'
            Name = 'Version'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 75
            Caption = 'Nr'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Versionstext'
            Name = 'Versionstext'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Gueltig_Ab'
            Name = 'Gueltig_Ab'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'ab'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'Gueltig_Bis'
            Name = 'Gueltig_Bis'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'bis'
          end
          item
            Align = gaCenter
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'PRIVAT'
            Name = 'PRIVAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'Privat'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BESITZER'
            Name = 'BESITZER'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Besitzer'
          end
          item
            Align = gaCenter
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            FormatOptions.Disabled = False
            Idx = 'InVerwendung'
            Name = 'InVerwendung'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 75
            Caption = 'In Verwendung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'LASTCHANGE_PERSON'
            Name = 'LASTCHANGE_PERSON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'von'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDateTime
            FormatOptions.SrcFormat = 'Y-m-d h:i:s'
            FormatOptions.NewFormat = 'd.m.Y hh:mm:ss'
            Idx = 'LASTCHANGE'
            Name = 'LASTCHANGE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'am'
          end>
        JQGridOptions.Height = 487
        JQGridOptions.IgnoreCase = True
        JQGridOptions.SortName = 'Bezeichnung'
        JQGridOptions.Width = 1441
        JQGridToolbarSearch.SearchOnEnter = False
        JQGridGroupHeader.GroupHeaders = <
          item
            StartColumnName = 'Version'
            NumberOfColumns = 2
            TitleText = 'Version'
          end
          item
            StartColumnName = 'Gueltig_Ab'
            NumberOfColumns = 2
            TitleText = 'G'#252'ltig'
          end
          item
            StartColumnName = 'LASTCHANGE_PERSON'
            NumberOfColumns = 2
            TitleText = 'Letzte '#196'nderung'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      Width = 1443
      Height = 60
      TabOrder = 20
      inherited iwrSuchen: TIWCGJQRegion
        Width = 6
        Height = 60
      end
      inherited iwrButtons: TIWCGJQRegion
        Left = 6
        Width = 1437
        Height = 60
        TabOrder = 4
        Align = alClient
        inherited jqbAbfragen: TIWCGJQButton
          Left = 16
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 9
          JQButtonOptions.Label_ = 'Aktualisieren'
        end
        inherited jqbNeu: TIWCGJQButton
          Left = 192
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 10
          JQButtonOptions.Label_ = 'Checkliste hinzuf'#252'gen'
        end
        inherited jqbAendern: TIWCGJQButton
          Left = 368
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 6
          JQButtonOptions.Label_ = 'Checkliste bearbeiten'
          JQButtonOptions.OnClick.OnEvent = jqbAendernJQButtonOptionsClick
        end
        inherited jqbLoeschen: TIWCGJQButton
          Left = 896
          Top = 20
          Width = 170
          Height = 25
          Visible = True
          TabOrder = 11
          JQButtonOptions.Label_ = 'Checkliste l'#246'schen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
        end
        object ButtonFragenBearbeiten: TIWCGJQButton
          Left = 1112
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 5
          Version = '1.0'
          JQButtonOptions.Label_ = 'Fragen bearbeiten'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = jqbFragenBearbeitenOnClick
          JQButtonOptions.OnClick.SendAllArguments = True
        end
        object ButtonChecklisteKopieren: TIWCGJQButton
          Left = 720
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 8
          Version = '1.0'
          JQButtonOptions.Label_ = 'Checkliste kopieren'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = JqbChecklisteKopierenOnClick
        end
        object ButtonChecklisteZuweisen: TIWCGJQButton
          Left = 544
          Top = 20
          Width = 170
          Height = 25
          TabOrder = 17
          Version = '1.0'
          JQButtonOptions.Label_ = 'Checkliste zuweisen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = ButtonChecklisteZuweisenJQButtonOptionsClick
        end
        object ButtonChecklistenVerwendung: TIWCGJQButton
          Left = 1312
          Top = 20
          Width = 97
          Height = 25
          TabOrder = 19
          Version = '1.0'
          JQButtonOptions.Label_ = 'Verwendung'
          JQButtonOptions.OnClick.OnEvent = ButtonChecklistenVerwendungJQButtonOptionsClick
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Top = 117
      Width = 441
      Height = 444
      TabOrder = 14
      CGScrollStyle = cgsbsNone
      ShowHorzScrollBar = False
      ShowVertScrollBar = False
      JQDialogOptions.Height = 444
      JQDialogOptions.Width = 441
      object IWLabel2: TIWCGJQLabel
        Left = 3
        Top = 40
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 3
        Top = 80
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'G'#252'ltig ab:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 3
        Top = 120
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'G'#252'ltig bis:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 3
        Top = 160
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Version:'
      end
      object IWLabel7: TIWCGJQLabel
        Left = 3
        Top = 200
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Versionstext:'
      end
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 3
        Top = 240
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Besitzer:'
      end
      object LabelBesitzer: TIWCGJQLabel
        Left = 112
        Top = 240
        Width = 49
        Height = 16
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'LabelBesitzer'
        Caption = 'Besitzer'
      end
      object IWCGJQLabel2: TIWCGJQLabel
        Left = 3
        Top = 321
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'BKBTID:'
      end
      object EditBezeichnung: TIWCGJQEdit
        Left = 112
        Top = 40
        Width = 290
        Height = 21
        TabOrder = 22
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object DateGueltigAb: TIWCGJQDatePicker
        Left = 112
        Top = 80
        Width = 290
        Height = 21
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorheriger'
        JQDatePickerOptions.Regional = dporGerman
      end
      object DateGueltigBis: TIWCGJQDatePicker
        Left = 112
        Top = 120
        Width = 290
        Height = 21
        TabOrder = 2
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorheriger'
        JQDatePickerOptions.Regional = dporGerman
      end
      object EditVersion: TIWCGJQEdit
        Left = 112
        Top = 160
        Width = 290
        Height = 21
        TabOrder = 3
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object EditVersionstext: TIWCGJQEdit
        Left = 112
        Top = 200
        Width = 290
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object CheckboxPrivat: TIWCGJQCheckBoxEx
        Left = 112
        Top = 267
        Width = 100
        Height = 21
        TabOrder = 16
        Version = '1.0'
        Caption = 'Privat'
      end
      object CheckboxCCRelevant: TIWCGJQCheckBoxEx
        Left = 112
        Top = 294
        Width = 257
        Height = 21
        TabOrder = 18
        Version = '1.0'
        Caption = 'CC-Relevant'
      end
      object EditBKBTID: TIWCGJQEdit
        Left = 112
        Top = 321
        Width = 290
        Height = 21
        TabOrder = 21
        Version = '1.0'
        Enabled = False
        Editable = False
        ReadOnly = True
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.quChecklisten
    KeyFields = 'ID'
    Left = 952
  end
  object AlertConfirmChecklisteLoeschen: TIWCGJQSweetAlert
    Version = '1.0'
    JQSweetAlertOptions.Title = 'Best'#228'tigen'
    JQSweetAlertOptions.Text = 'Soll die Checkliste wirklich gel'#246'scht werden?'
    JQSweetAlertOptions.AlertType = jqsatWarning
    JQSweetAlertOptions.AllowEscapeKey = False
    JQSweetAlertOptions.ShowCancelButton = True
    JQSweetAlertOptions.CancelButtonText = 'Abbrechen'
    JQSweetAlertOptions.OnBtnClick.Ajax = False
    JQSweetAlertOptions.OnBtnClick.AjaxAppend = False
    JQSweetAlertOptions.OnBtnClick.OnEvent = AlertConfirmChecklisteLoeschenJQSweetAlertOptionsBtnClick
    Left = 776
    Top = 248
  end
  object DSVisBkbTypen: TDataSource
    DataSet = DMChecklisten.QVIS_BKBTyp
    Left = 776
    Top = 432
  end
end
