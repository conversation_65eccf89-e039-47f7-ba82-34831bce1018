inherited Formatierungen: TFormatierungen
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 4
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 5
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 0
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'CODE'
            Name = 'CODE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Code'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BESCHREIBUNG'
            Name = 'BESCHREIBUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Beschreibung'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 6
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 9
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 1
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 3
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 10
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 11
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Width = 329
      TabOrder = 12
      JQDialogOptions.Width = 329
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 24
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Code:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 56
        Width = 90
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel2'
        Caption = 'Beschreibung:'
      end
      object jqeCode: TIWCGJQEdit
        Left = 112
        Top = 24
        Width = 200
        Height = 21
        TabOrder = 7
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeBeschreibung: TIWCGJQEdit
        Left = 112
        Top = 51
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quFormatierung
  end
  object quFormatierung: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Formatierungen;')
    Left = 809
    Top = 25
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Stammdaten.Formatierungen (Code, Beschreibung)'
      'VALUES      (:code, :beschreibung);')
    Left = 745
    Top = 25
    ParamData = <
      item
        Name = 'CODE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BESCHREIBUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Formatierungen'
      'SET    beschreibung = :beschreibung'
      'WHERE  code = :code;')
    Left = 689
    Top = 25
    ParamData = <
      item
        Name = 'BESCHREIBUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'CODE'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
end
