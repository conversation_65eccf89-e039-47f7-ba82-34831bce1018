﻿unit BewertungenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweet<PERSON>lert,
  IWApplication, IWCGJQEdit, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompLabel, IWCompListbox, IWCGJQRegion, IWCGJQCombobox, IWCGJQLabel, JQ.Helpers.ComboboxEx;

type
  TBewertungen = class(TCRUDGrid)
    quBewertungen: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    jqeBezeichnung: TIWCGJQEdit;
    quNeu: TFDQuery;
    IWLabel3: TIWCGJQLabel;
    iwcbIcon: TIWCGJQComboboxEx;
    quIcons: TFDQuery;
    iwcbTyp: TIWCGJQComboboxEx;
    quTyp: TFDQuery;
  private
    { Private declarations }
    procedure NeuBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert;bearbeitbar: boolean);
    procedure BewertungenAbfragen;
  end;

var
  Bewertungen: TBewertungen;

implementation

uses dmmain, Utility;

{$R *.dfm}

constructor TBewertungen.Create(AOwner: TComponent; AAlert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, AAlert);

  FAlert := AAlert;

  EnableAbfragen(quBewertungen, BewertungenAbfragen);
  if bearbeitbar then
  begin
    EnableNeu('Neue Bewertung erstellen', Nil, NeuBestaetigt);
  end;

  // Icons aussuchen
  RefreshQuery(quIcons);
  while not quIcons.Eof do
  begin
    iwcbIcon.Items.Add(
        quIcons.FieldByName('Beschreibung').AsString,
        quIcons.FieldByName('icon').AsString);
    quIcons.Next;
  end;
  quIcons.Close;
  // Typen aussuchen
  RefreshQuery(quTyp);
  while not quTyp.Eof do
  begin
    iwcbTyp.Items.Add(
        quTyp.FieldByName('Beschreibung').AsString,
        quTyp.FieldByName('typ').AsString);
    quTyp.Next;
  end;
  quTyp.Close;
end;

procedure TBewertungen.BewertungenAbfragen;
begin
  RefreshQuery(quBewertungen);
end;

procedure TBewertungen.NeuBestaetigt;
begin
  if (iwcbIcon.ItemIndex = -1) OR (iwcbTyp.ItemIndex = -1) then begin
    Exit;
  end;
  quNeu.Close;
  quNeu.Prepare;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.ParamByName('typ').AsString := iwcbTyp.SelectedValue;
  quNeu.ParamByName('icon').AsString := iwcbIcon.SelectedValue;
  quNeu.Execute;
end;

procedure TBewertungen.ResetModal;
begin
  jqeBezeichnung.Text := '';
  iwcbIcon.ItemIndex := -1;
  iwcbTyp.ItemIndex := -1;
end;

end.
