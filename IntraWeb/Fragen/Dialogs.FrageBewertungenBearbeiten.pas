﻿unit Dialogs.FrageBewertungenBearbeiten;

interface

uses
  System.SysUtils, System.Classes, VCL.Controls, VCL.Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWCGJQControl, IWCGJQButton, IWCompLabel, IWCompListbox,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompCheckbox,
  MangeltypenFrame, MassnahmenFrame, IWApplication, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWCGJQDialog, Classes.Fragen, Generics.Collections, IWCGJQComboBox, IWCGJQCheckBox, IWCGJQLabel, IWCGJQRegion,
  Dialogs.Base, Modules.Checklisten, IWCGJQDropDown;

type
 // TDialogFrBewEdit = class(TDialogBase)
  TDialogFrBewEdit = class(TDialogBase<TDMChecklisten>)
    CheckboxAusblenden: TIWCGJQCheckBoxEx;
    CheckboxMangel: TIWCGJQCheckBoxEx;
    DropBewertungen: TIWCGJQDropDown;
  private
    { Private declarations }
    FFragenbewertung: TFragenbewertung;
    FBearbeiten: boolean;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; ADM: TDMChecklisten); override;
    destructor Destroy; override;
    procedure Neu;
    procedure Bearbeiten(ABewertung: TFragenbewertung);
    function GetFragenbewertung: TFragenbewertung;
    procedure InitializeControls; override;
  end;

implementation

{$R *.dfm}


constructor TDialogFrBewEdit.Create(AOwner: TComponent; ADM: TDMChecklisten);
begin
  self := inherited Create(AOwner, ADM);
  self.FFragenbewertung := Nil;

  InitializeControls;
end;

destructor TDialogFrBewEdit.Destroy;
begin
  inherited;
end;

procedure TDialogFrBewEdit.Neu;
begin
  Title := 'Bewertung hinzufügen';
  FBearbeiten := false;
  FFragenbewertung.Free;
  FFragenbewertung := TFragenbewertung.Create;
  DropBewertungen.Enabled := true;
  for var i := 0 to DM.Bewertungen.Count - 1 do
  begin
    var LItem := DropBewertungen.Items.Add;
    LItem.Caption := DM.Bewertungen[i].DisplayText;
    LItem.Value := IntToStr(DM.Bewertungen[i].id);
  end;
  DropBewertungen.AjaxReRender;
end;

procedure TDialogFrBewEdit.Bearbeiten(ABewertung: TFragenbewertung);
var
  index: integer;
begin
  Neu;
  Title := 'Bewertungen bearbeiten';
  FBearbeiten := true;
  FFragenbewertung.Assign(ABewertung);
  index := -1;
  for var i := 0 to DropBewertungen.Items.Count - 1 do
  begin
    if DropBewertungen.Items[i].Value = ABewertung.Bewertung.id.ToString then
    begin
      index := i;
      break
    end;
  end;
  DropBewertungen.SelectedIndex := index;
  DropBewertungen.Enabled := false;

  CheckboxAusblenden.Checked := FFragenbewertung.ausblenden;
  CheckboxMangel.Checked := not FFragenbewertung.positiv;
end;

// Liefert aktuelle Bewertungs-Objekt zurück, so dass der Zustand im Dialog entsprechend abgebildet wird
function TDialogFrBewEdit.GetFragenbewertung;
begin
  var
  LFragenbewertung := TFragenbewertung.Create;
  LFragenbewertung.Assign(FFragenbewertung);
  LFragenbewertung.positiv := not CheckboxMangel.Checked;
  LFragenbewertung.ausblenden := CheckboxAusblenden.Checked;

  // Standard Mangel und Maßnahme vorübergehend deaktiviert:
  // LFragenBewertung.standardMangel := mangeltyp;
  // LFragenBewertung.standardMassnahmne := massnahme;
  // LFragenbewertung.standardMangel := Nil;
  // LFragenbewertung.standardMassnahmne := Nil;

  if DropBewertungen.SelectedIndex >= 0 then
  begin
    LFragenbewertung.Bewertung := FindeBewertung(StrToInt(DropBewertungen.Items[DropBewertungen.SelectedIndex].Value), DM.Bewertungen);
  end
  else
  begin
    LFragenbewertung.Bewertung.Free;
    LFragenbewertung.Bewertung := nil;
  end;
  Result := LFragenbewertung;
end;

procedure TDialogFrBewEdit.InitializeControls;
begin
  inherited;
  CheckboxAusblenden.Checked := false;
  CheckboxMangel.Checked := false;
  DropBewertungen.Items.Clear;
  DropBewertungen.SelectedIndex := -1;
end;


end.
