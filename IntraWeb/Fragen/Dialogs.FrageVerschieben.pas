unit Dialogs.FrageVerschieben;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer,
  IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, IWCGJQRadio, IWCompListbox,
  IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel, Classes.Fragen,
  Modules.Checklisten, IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQGrid, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf,
  FireDAC.Stan.StorageBin, Data.DB, FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert;

type
  //TDialogFrageVerschieben = class(TDialogBase)
  TDialogFrageVerschieben = class(TDialogBase<TDMChecklisten>)
    RadioGroupVerschiebeModus: TIWCGJQRadioGroupEx;
    GridFragen: TIWCGJQGrid;
    ProviderFragen: TIWCGJQGridDataSetProvider;
    procedure IWCGJQFrameCreate(Sender: TObject);
    procedure ButtonCancelClick(Sender: TObject; AParams: TStringList);
    procedure ButtonOKClick(Sender: TObject; AParams: TStringList);
    procedure GridFragenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    FFrage: TFrage;
  public
    procedure InitializeControls; override;
    procedure MitZuVerschiebenderFrageInitialisieren(AFrage: TFrage);
  end;

implementation

{$R *.dfm}


procedure TDialogFrageVerschieben.ButtonCancelClick(Sender: TObject; AParams: TStringList);
begin
  inherited;
  DM.ChecklisteRefresh;
  Close;
end;

procedure TDialogFrageVerschieben.ButtonOKClick(Sender: TObject; AParams: TStringList);
var
  LModus: TVerschiebeModus;
begin
  var
  LZielFrage := DM.GetFrageForRowID(GridFragen.JQGridOptions.SelRow);

  if LZielFrage = nil then
    abort;

  case RadioGroupVerschiebeModus.ItemIndex of
    0:
      LModus := TVerschiebeModus.Oberhalb;
    1:
      LModus := TVerschiebeModus.Unterhalb;
    2:
      LModus := TVerschiebeModus.UnterFrage;
  else
    raise Exception.Create('Unbekannter Verschiebemodus!');
  end;

  try
    DM.FrageVerschieben(FFrage, LZielFrage, LModus);
  finally
    DM.ChecklisteRefresh;
  end;
  Close;
end;

procedure TDialogFrageVerschieben.GridFragenJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
  inherited;
  GridFragen.JQGridOptions.TreeGridExpandAllNodes;
end;

procedure TDialogFrageVerschieben.InitializeControls;
begin
  inherited;
  // todo 1 -oCH -cImplementieren: Felder initialisieren
end;

procedure TDialogFrageVerschieben.IWCGJQFrameCreate(Sender: TObject);
begin
  inherited;
  // Grid Setup
  GridFragen.JQGridOptions.TreeGrid := True;
  GridFragen.JQGridOptions.TreeGridModel := gtgmAdjacency;
  GridFragen.JQGridOptions.ExpandColumn := 'FragenNr';
  // Grid Provider Setup
  ProviderFragen.KeyFields := 'ID';
  ProviderFragen.TreeOptions.IsLeafFieldName := 'isleaf';
  ProviderFragen.TreeOptions.LevelFieldName := 'level';
  ProviderFragen.TreeOptions.ParentFieldName := 'ID_parent';
  ProviderFragen.DataSet := DM.MTFragen;

  GridFragen.JQGridProvider := ProviderFragen;
end;

procedure TDialogFrageVerschieben.MitZuVerschiebenderFrageInitialisieren(AFrage: TFrage);
begin
  if AFrage = nil then
    abort;
  FFrage := AFrage;
  DM.FrageAusblenden(AFrage);

  Title := Format('Frage %s verschieben', [AFrage.FragenNr]);
end;

end.
