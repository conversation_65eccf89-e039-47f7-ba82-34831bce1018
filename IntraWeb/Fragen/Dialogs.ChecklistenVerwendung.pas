unit Dialogs.ChecklistenVerwendung;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Dialogs.Base, IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component,
  IWCGJQComp, IWCGJQSweetAlert, IWCGJQButton, IWCGJQRegion, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, IWCGJQControl, IWCGJQDialog, Modules.Checklisten, IWCGJQGrid, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl, IWControl, IWCompLabel, IWCGJQLabel,
  dmmain;

type
  TDialogChecklistenVerwendung = class(TDialogBase)
    GridChecklistenVerwendungKontrollen: TIWCGJQGrid;
    ProviderChecklistenVErwendungKontrollen: TIWCGJQGridDataSetProvider;
    IWCGJQLabel1: TIWCGJQLabel;
    LabelBesitzer: TIWCGJQLabel;
    GridChecklistenVerwendungKontrolltypen: TIWCGJQGrid;
    IWCGJQLabel2: TIWCGJQLabel;
    ProviderChecklistenVErwendungKontrolltypen: TIWCGJQGridDataSetProvider;
    RegionKontrolltypen: TIWRegion;
    RegionKontrollen: TIWRegion;
    RegionGrids: TIWRegion;
  private
    DM: TDMChecklisten;
  public
    constructor Create(AOwner: TComponent; ADatamodule: TDMChecklisten); reintroduce;
    procedure InitializeControls; override;
  end;

implementation

uses
  JQ.Helpers.Grid;

{$R *.dfm}

{ TDialogChecklistenVerwendung }

constructor TDialogChecklistenVerwendung.Create(AOwner: TComponent; ADatamodule: TDMChecklisten);
begin
  inherited Create(AOwner);
  DM := ADatamodule;
  GridChecklistenVerwendungKontrollen.SetupDefaults(ProviderChecklistenVErwendungKontrollen);
  GridChecklistenVerwendungKontrolltypen.SetupDefaults(ProviderChecklistenVErwendungKontrolltypen);
end;

procedure TDialogChecklistenVerwendung.InitializeControls;
begin
  DM.RefreshChecklistenVerwendung;
  GridChecklistenVerwendungKontrolltypen.JQGridOptions.ReloadGrid;
  GridChecklistenVerwendungKontrollen.JQGridOptions.ReloadGrid;
  Title := 'Verwendung der Checkliste "' + DM.quChecklistenBEZEICHNUNG.AsString + '"';

  if DM.quChecklistenBESITZER_BLDCODE.Value <> dm_main.BLDCODE then
  begin
    LabelBesitzer.Caption := Format('Besitzer der Checkliste: %s. Die Checkliste ist daher in Verwendung.',
      [DM.quChecklistenBESITZER.AsString]);
  end
  else
  begin
    LabelBesitzer.Caption := Format('Besitzer der Checkliste: %s.',
      [DM.quChecklistenBESITZER.AsString]);
  end;

end;

end.
