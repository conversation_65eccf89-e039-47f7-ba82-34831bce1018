inherited ChecklistenDetails: TChecklistenDetails
  Width = 1584
  Height = 864
  Title = 'Checkliste: Fragen bearbeiten'
  AutoScrollPos = True
  DesignLeft = 2
  DesignTop = 2
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1584
    TabOrder = 4
    inherited ImageLogo: TIWImageFile
      Left = 1301
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 1092
      Css = 'ui-widget h2'
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 9
      JQButtonOptions.OnClick.Ajax = True
    end
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 839
    Width = 1584
    TabOrder = 7
  end
  object RegionChecklistendetails: TIWCGJQRegion [2]
    Left = 0
    Top = 50
    Width = 1584
    Height = 144
    RenderInvisibleControls = True
    TabOrder = 6
    Version = '1.0'
    Align = alTop
    object IWLabel5: TIWCGJQLabel
      Left = 32
      Top = 5
      Width = 103
      Height = 19
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel5'
      Caption = 'Bezeichnung:'
    end
    object IWLabel8: TIWCGJQLabel
      Left = 728
      Top = 6
      Width = 85
      Height = 19
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel5'
      Caption = 'Gueltig ab:'
    end
    object IWLabel9: TIWCGJQLabel
      Left = 832
      Top = 6
      Width = 89
      Height = 19
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel5'
      Caption = 'Gueltig bis:'
    end
    object IWLabel10: TIWCGJQLabel
      Left = 944
      Top = 6
      Width = 64
      Height = 19
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel5'
      Caption = 'Version:'
    end
    object iwlBezeichnung: TIWCGJQLabel
      Left = 149
      Top = 3
      Width = 548
      Height = 39
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel5'
      Caption = 'Bezeichnung'
    end
    object iwlGueltigAb: TIWCGJQLabel
      Left = 728
      Top = 31
      Width = 85
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel5'
      Caption = ' Datum'
    end
    object iwlGueltigBis: TIWCGJQLabel
      Left = 832
      Top = 31
      Width = 89
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel5'
      Caption = 'Datum'
    end
    object iwlVersion: TIWCGJQLabel
      Left = 944
      Top = 31
      Width = 64
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel5'
      Caption = 'Version'
    end
    object IWLabel12: TIWCGJQLabel
      Left = 32
      Top = 51
      Width = 111
      Height = 19
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel1'
      Caption = 'Extern Quelle:'
    end
    object iwlBearbeitenVerboten: TIWCGJQLabel
      Left = 728
      Top = 75
      Width = 350
      Height = 19
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'iwlBearbeitenVerboten'
      Caption = 'Bearbeiten verboten'
      AdditionStyle.Strings = (
        'color:red')
    end
    object IWLabel1: TIWCGJQLabel
      Left = 1022
      Top = 6
      Width = 101
      Height = 19
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'IWLabel5'
      Caption = 'Versionstext:'
    end
    object iwlVersionstext: TIWCGJQLabel
      Left = 1022
      Top = 31
      Width = 160
      Height = 59
      Font.Color = clNone
      Font.Size = 12
      Font.Style = []
      HasTabOrder = False
      AutoSize = False
      FriendlyName = 'IWLabel5'
      Caption = 'Versionstext'
    end
    object LabelAnzahlBewertungen: TIWCGJQLabel
      Left = 728
      Top = 55
      Width = 128
      Height = 16
      RenderSize = False
      StyleRenderOptions.RenderSize = False
      Font.Color = clNone
      Font.Size = 10
      Font.Style = []
      HasTabOrder = False
      FriendlyName = 'LabelAnzahlBewertungen'
      Caption = 'Anzahl Bewertungen'
    end
    object ButtonFrageHinzufuegen: TIWCGJQButton
      Left = 32
      Top = 100
      Width = 180
      Height = 36
      TabOrder = 11
      Version = '1.0'
      JQButtonOptions.Label_ = 'Frage hinzuf'#252'gen'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonFrageHinzufuegenOnClick
      JQButtonOptions.OnClick.SendAllArguments = True
    end
    object EditQuelleExtern: TIWCGJQEdit
      Left = 141
      Top = 51
      Width = 257
      Height = 21
      Hint = 
        'Beim Bearbeiten der Fragen kann hier eine e externe Quelle angeg' +
        'ebenn werden '
      TabOrder = 10
      Version = '1.0'
      ScriptEvents = <>
      Text = ''
    end
    object ButtonUnterfrageHinzufuegen: TIWCGJQButton
      Left = 218
      Top = 100
      Width = 180
      Height = 36
      Version = '1.0'
      JQButtonOptions.Label_ = 'Unterfrage hinzuf'#252'gen'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonUnterfrageHinzufuegenOnClick
    end
    object ButtonFrageVerschieben: TIWCGJQButton
      Left = 404
      Top = 100
      Width = 180
      Height = 36
      TabOrder = 2
      Version = '1.0'
      JQButtonOptions.Label_ = 'Frage verschieben'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonFrageVerschiebenOnClick
    end
    object ButtonFrageBearbeiten: TIWCGJQButton
      Left = 607
      Top = 100
      Width = 180
      Height = 36
      TabOrder = 12
      Version = '1.0'
      JQButtonOptions.Label_ = 'Frage bearbeiten'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonFrageBearbeitenOnClick
      JQButtonOptions.OnClick.SendAllArguments = True
    end
    object ButtonFrageLoeschen: TIWCGJQButton
      Left = 979
      Top = 100
      Width = 180
      Height = 36
      TabOrder = 1
      Version = '1.0'
      JQButtonOptions.Label_ = 'Frage l'#246'schen'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonFrageLoeschenOnClick
      JQEvents.OnClick.Ajax = False
    end
    object ButtonChecklisteSpeichern: TIWCGJQButton
      Left = 1186
      Top = 100
      Width = 180
      Height = 36
      TabOrder = 3
      Font.Style = [fsBold]
      Version = '1.0'
      JQButtonOptions.Label_ = 'Checkliste speichern'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonChecklisteSpeichernOnClick
    end
    object ButtonFrageDuplizieren: TIWCGJQButton
      Left = 793
      Top = 100
      Width = 180
      Height = 36
      Hint = 'Erzeugt eine Kopie der selektierten Frage'
      TabOrder = 8
      Version = '1.0'
      JQButtonOptions.Label_ = 'Frage duplizieren'
      JQButtonOptions.OnClick.Ajax = False
      JQButtonOptions.OnClick.AjaxAppend = False
      JQButtonOptions.OnClick.OnEvent = ButtonFrageDuplizierenJQButtonOptionsClick
      JQEvents.OnClick.Ajax = False
    end
  end
  object GridFragen: TIWCGJQGrid [3]
    Left = 0
    Top = 194
    Width = 1584
    Height = 645
    TabOrder = 5
    Version = '1.0'
    Align = alClient
    JQGridOptions.ColModel = <
      item
        Classes.Strings = (
          'fragenNr')
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Idx = 'FragenNr'
        Name = 'FragenNr'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Sortable = False
        Width = 50
        Caption = 'FragenNr'
      end
      item
        Align = gaCenter
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Formatter = gcfCheckBox
        Idx = 'CCRelevant'
        Name = 'CCRelevant'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Sortable = False
        Width = 50
        Caption = 'CC-relevant'
      end
      item
        Classes.Strings = (
          'fragenText')
        Editable = True
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Idx = 'Text'
        Name = 'Text'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Sortable = False
        Caption = 'Text'
      end
      item
        Classes.Strings = (
          'fragenInfo')
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Idx = 'Info'
        Name = 'Info'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Sortable = False
        Caption = 'Info'
      end
      item
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Idx = 'Bewertungen'
        Name = 'Bewertungen'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Caption = 'Bewertungen'
      end
      item
        Editable = True
        EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
        EditOptions.CustomElements = <>
        Idx = 'Kontrollbereiche'
        Name = 'Kontrollbereiche'
        SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
        Width = 200
        Caption = 'Kontrollbereiche'
      end>
    JQGridOptions.DeselectAfterSort = False
    JQGridOptions.ExpandColumn = 'FragenNr'
    JQGridOptions.Height = 618
    JQGridOptions.LoadOnce = True
    JQGridOptions.LoadText = 'Lade Daten...'
    JQGridOptions.SubGridModel = <>
    JQGridOptions.Width = 1582
    JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
    JQGridOptions.OnLoadComplete.OnEvent = GridFragenJQGridOptionsLoadComplete
    JQGridOptions.PagerVisible = False
    JQGridNav.Add = False
    JQGridNav.Del = False
    JQGridNav.Edit = False
    JQGridNav.Search = False
    JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
    JQGridCustomButtons = <>
    JQGridProvider = ProviderFragen
    JQGridToolbarSearch.DefaultSearch = gsoContains
    JQDragAndDropOptions.ConnectWith = <
      item
      end>
  end
  object ProviderFragen: TIWCGJQGridDataSetProvider
    DataSet = DMChecklisten.QFragen
    KeyFields = 'ID'
    Left = 64936
    Top = 65392
  end
  object DSKontrolltypen: TDataSource
    DataSet = DMChecklisten.QChecklisteKontrolltypen
    Left = 488
    Top = 74
  end
  object AlertClose: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 408
    Top = 16
  end
end
