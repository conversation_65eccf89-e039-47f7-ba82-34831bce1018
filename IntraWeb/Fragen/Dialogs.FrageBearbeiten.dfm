inherited DialogFrageBearbeiten: TDialogFrageBearbeiten
  Width = 806
  Height = 799
  inherited IWFrameRegion: TIWCGJQDialog
    Width = 806
    Height = 799
    TabOrder = 4
    JQDialogOptions.Height = 799
    JQDialogOptions.Width = 806
    inherited RegionContent: TIWCGJQRegion
      Width = 806
      Height = 739
      TabOrder = 0
      object IWLabel1: TIWCGJQLabel
        Left = 20
        Top = 447
        Width = 132
        Height = 19
        ZIndex = 5001
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Kontrollbereiche:'
      end
      object IWLabel11: TIWCGJQLabel
        Left = 20
        Top = 10
        Width = 74
        Height = 19
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'ID-Extern:'
      end
      object IWLabel13: TIWCGJQLabel
        Left = 479
        Top = 10
        Width = 101
        Height = 19
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Formatierung:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 232
        Top = 10
        Width = 114
        Height = 19
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel2'
        Caption = 'Fragennummer:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 57
        Top = 52
        Width = 37
        Height = 19
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel2'
        Caption = 'Text:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 59
        Top = 128
        Width = 35
        Height = 19
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel2'
        Caption = 'Info:'
      end
      object IWCGJQLabel1: TIWCGJQLabel
        Left = 448
        Top = 447
        Width = 107
        Height = 19
        ZIndex = 5001
        Font.Color = clNone
        Font.Size = 12
        Font.Style = []
        HasTabOrder = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bewertungen:'
      end
      object ComboFormatierung: TIWCGJQComboBoxEx
        Left = 600
        Top = 10
        Width = 169
        Height = 21
        TabOrder = 10
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 167
        Caption = ''
      end
      object MemoInfoText: TIWCGJQHTMLEditor
        Left = 100
        Top = 128
        Width = 669
        Height = 297
        TabOrder = 5
        Version = '1.0'
        ZIndex = 5001
        JQHTMLEditorOptions.Lang = 'de'
        JQHTMLEditorOptions.Height = 297
        JQHTMLEditorOptions.Width = 669
        JQHTMLEditorOptions.ToolBar = jqhetCustom
        JQHTMLEditorOptions.Resizable = False
        JQHTMLEditorOptions.CustomButtons.CopyPaste = [tbbcpPasteText, tbbcpPasteFormatText]
        JQHTMLEditorOptions.CustomButtons.UndoRedo = [tbburUndo]
        JQHTMLEditorOptions.CustomButtons.Style = [tbbstBold, tbbstItalic, tbbstUnderline, tbbstStrikeThrough, tbbstSubSscript, tbbstSuperScript]
        JQHTMLEditorOptions.CustomButtons.Colors = [tbbcForeColor, tbbcHiliteColor]
        JQHTMLEditorOptions.CustomButtons.Alignment = [tbbaJustifyLeft, tbbaJustifyCenter, tbbaJustifyRight, tbbaJustifyFull]
        JQHTMLEditorOptions.CustomButtons.Indent = [tbbiOutdent, tbbiIndent]
        JQHTMLEditorOptions.CustomButtons.Format = [tbbfFormatBlock, tbbfFontName]
        JQHTMLEditorOptions.CustomButtons.Lists = [tbblInsertOrderedList, tbblInsertUnorderedlist]
        JQHTMLEditorOptions.CustomButtons.Links = [tbbliLink, tbbliUnlink]
        JQHTMLEditorOptions.CustomButtons.Media = [tbbmImage]
        JQHTMLEditorOptions.CustomButtons.Tables = [tbbtTable, tbbtRowAfter, tbbtRowRemove, tbbtColAfter, tbbtColRemove]
      end
      object ListBewertungen: TIWCGPanelList
        Left = 448
        Top = 472
        Width = 321
        Height = 246
        TabOrder = 13
        Version = '1.0'
        Anchors = [akLeft, akTop, akBottom]
        Items = <
          item
            OnClick.Ajax = True
          end>
      end
      object ButtonBewertungHinzufuegen: TIWCGJQButton
        Left = 669
        Top = 445
        Width = 100
        Height = 21
        TabOrder = 1
        Version = '1.0'
        Anchors = [akTop, akRight]
        JQButtonOptions.Label_ = 'Hinzuf'#252'gen'
        JQButtonOptions.OnClick.OnEvent = ButtonBewertungHinzufuegenOnClick
      end
      object EditFragennummer: TIWCGJQEdit
        Left = 352
        Top = 10
        Width = 92
        Height = 21
        TabOrder = 9
        Version = '1.0'
        ZIndex = 5001
        MaxLength = 20
        ScriptEvents = <>
        Text = ''
      end
      object EditIdExtern: TIWCGJQEdit
        Left = 100
        Top = 10
        Width = 100
        Height = 21
        TabOrder = 3
        Version = '1.0'
        ZIndex = 5001
        MaxLength = 100
        ScriptEvents = <>
        Text = ''
      end
      object EditTitelText: TIWCGJQEdit
        Left = 100
        Top = 50
        Width = 669
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object SelectKontrollbereiche: TIWCGJQMultiSelect
        Left = 20
        Top = 472
        Width = 397
        Height = 246
        TabOrder = 11
        Version = '1.0'
        Anchors = [akLeft, akTop, akBottom]
        ZIndex = 5005
        JQMultiSelectOptions.AvailableListPosition = jqmsolpLeft
        JQMultiSelectOptions.SplitRatio = 0.500000000000000000
        Items = <>
        Groups = <>
        Caption = ''
        JQCustomLocal.Strings.ItemsSelectedNil = 'Nichts ausgew'#228'hlt'
        JQCustomLocal.Strings.ItemsSelected = '{count} ausgew'#228'hlt'
        JQCustomLocal.Strings.ItemsSelectedPlural = '{count} ausgew'#228'hlt'
        JQCustomLocal.Strings.ItemsAvailableNil = 'Nichts verf'#252'gbar'
        JQCustomLocal.Strings.ItemsAvailable = '{count} verf'#252'gbar'
        JQCustomLocal.Strings.ItemsAvailablePlural = '{count} verf'#252'gbar'
        JQCustomLocal.Strings.SelectAll = 'Alle ausw'#228'hlen'
      end
      object ButtonBewertungLoeschen: TIWCGJQButton
        Left = 563
        Top = 445
        Width = 100
        Height = 21
        TabOrder = 6
        Version = '1.0'
        Anchors = [akTop, akRight]
        JQButtonOptions.Label_ = 'L'#246'schen'
        JQButtonOptions.OnClick.OnEvent = ButtonBewertungLoeschenOnClick
      end
      object CheckboxCCrelevant: TIWCGJQCheckBoxEx
        Left = 100
        Top = 92
        Width = 257
        Height = 21
        TabOrder = 15
        Version = '1.0'
        Caption = 'CC-relevant:'
      end
    end
    inherited RegionBottom: TIWCGJQRegion
      Top = 749
      Width = 804
      TabOrder = 2
      inherited ButtonCancel: TIWCGJQButton
        Left = 696
        TabOrder = 14
      end
      inherited RegionOK: TIWCGJQRegion
        Width = 688
        TabOrder = 8
        inherited ButtonOK: TIWCGJQButton
          Left = 580
          TabOrder = 7
        end
      end
    end
  end
end
