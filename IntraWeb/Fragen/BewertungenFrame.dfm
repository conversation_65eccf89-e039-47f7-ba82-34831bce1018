inherited Bewertungen: TBewertungen
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 5
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 6
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 0
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TYP'
            Name = 'TYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Typ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ICON'
            Name = 'ICON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Icon'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 7
      inherited iwrButtons: TIWCGJQRegion
        TabOrder = 4
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 2
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 3
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 9
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 10
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Width = 329
      TabOrder = 11
      JQDialogOptions.Width = 329
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 24
        Width = 80
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 56
        Width = 80
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel2'
        Caption = 'Typ:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 16
        Top = 96
        Width = 80
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel2'
        Caption = 'Icon:'
      end
      object iwcbIcon: TIWCGJQComboBoxEx
        Left = 110
        Top = 96
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 198
        Caption = ''
      end
      object iwcbTyp: TIWCGJQComboBoxEx
        Left = 110
        Top = 56
        Width = 200
        Height = 21
        TabOrder = 13
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ZIndex = 5001
        Items = <>
        Groups = <>
        SelectedIndex = 0
        JQComboBoxExOptions.Width = 198
        Caption = ''
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 110
        Top = 24
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quBewertungen
  end
  object quBewertungen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bewertungen;')
    Left = 809
    Top = 25
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'INSERT INTO Stammdaten.Bewertungen (Bezeichnung, Typ, Icon)'
      'VALUES      (:bezeichnung, :typ, :icon);')
    Left = 737
    Top = 25
    ParamData = <
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'TYP'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ICON'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end>
  end
  object quIcons: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bewertungs_Icons;')
    Left = 944
    Top = 24
  end
  object quTyp: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Bewertungs_Typen;')
    Left = 944
    Top = 72
  end
end
