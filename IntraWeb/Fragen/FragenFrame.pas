unit FragenFrame;

interface

uses
  SysUtils, Classes, Controls, Forms,
  IWVCLBaseContainer, IWColor, IWContainer, IWRegion, IWHTMLContainer,
  IWHTML40Container, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWHTMLControls, FragenClasses, IWCompLabel;

type
  TFragen = class(TFrame)
    IWFrameRegion: TIWRegion;
    iwrBewertungen: TIWRegion;
    iwlBewertungen: TIWList;
    iwlText: TIWLabel;
    iwlInfo: TIWLabel;
    iwrText: TIWRegion;
    iwrFrage: TIWRegion;
    iwrUnterfragen: TIWRegion;
    procedure FragenOnClick(Sender: TObject);
  private
    { Private declarations }
    frage: TFrage;
    einrueckung: integer;
    unterfrageHinzufuegen: TUnterfrageHinzufuegenCallback;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; frage: TFrage;
        callback: TUnterfrageHinzufuegenCallback;
        einrueckung: integer = 0); reintroduce;
    destructor Destroy; override;
  end;

implementation

uses Graphics;

{$R *.dfm}

constructor TFragen.Create(AOwner: TComponent; frage: TFrage;
    callback: TUnterfrageHinzufuegenCallback; einrueckung: integer = 0);
begin
end;

destructor TFragen.Destroy;
begin
  frage.Free;

  inherited;
end;

procedure TFragen.FragenOnClick(Sender: TObject);
begin
  unterfrageHinzufuegen(Self);
end;

end.
