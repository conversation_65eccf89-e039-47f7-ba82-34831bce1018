inherited Tierarten: <PERSON><PERSON><PERSON><PERSON>
  inherited IWFrameRegion: TIWCGJQRegion
    TabOrder = 1
    inherited iwrMid: TIWCGJQRegion
      TabOrder = 2
      inherited jqgGrid: TIWCGJQGrid
        TabOrder = 13
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KZ'
            Name = 'KZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'KZ'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VIS_TIERART'
            Name = 'VIS_TIERART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'VIS Tierart'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfCheckBox
            Idx = 'SICHTBAR'
            Name = 'SICHTBAR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Sichtbar'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'BEGDAT'
            Name = 'BEGDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Begdat'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfDate
            FormatOptions.NewFormat = 'd.m.Y'
            Idx = 'ENDDAT'
            Name = 'ENDDAT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Enddat'
          end>
      end
    end
    inherited iwrTop: TIWCGJQRegion
      TabOrder = 4
      inherited iwrSuchen: TIWCGJQRegion
        TabOrder = 6
      end
      inherited iwrButtons: TIWCGJQRegion
        inherited jqbAbfragen: TIWCGJQButton
          TabOrder = 16
        end
        inherited jqbNeu: TIWCGJQButton
          TabOrder = 0
        end
        inherited jqbAendern: TIWCGJQButton
          TabOrder = 5
        end
        inherited jqbLoeschen: TIWCGJQButton
          TabOrder = 9
        end
      end
    end
    inherited jqdDialog: TIWCGJQDialog
      Left = 312
      Top = 117
      Width = 337
      Height = 308
      TabOrder = 11
      JQDialogOptions.Height = 308
      JQDialogOptions.Width = 337
      object IWLabel1: TIWCGJQLabel
        Left = 16
        Top = 16
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'KZ:'
      end
      object IWLabel2: TIWCGJQLabel
        Left = 16
        Top = 48
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Bezeichnung:'
      end
      object IWLabel3: TIWCGJQLabel
        Left = 16
        Top = 80
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'VIS Tierart:'
      end
      object IWLabel4: TIWCGJQLabel
        Left = 16
        Top = 112
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Sichtbar:'
      end
      object IWLabel5: TIWCGJQLabel
        Left = 16
        Top = 144
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Beginndatum:'
      end
      object IWLabel6: TIWCGJQLabel
        Left = 16
        Top = 176
        Width = 85
        Height = 16
        ZIndex = 5001
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel1'
        Caption = 'Enddatum:'
      end
      object iwcSichtbar: TIWCheckBox
        Left = 107
        Top = 112
        Width = 121
        Height = 21
        ZIndex = 5001
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        SubmitOnAsyncEvent = True
        Style = stNormal
        TabOrder = 8
        Checked = False
        FriendlyName = 'iwcSichtbar'
      end
      object jqeKZ: TIWCGJQEdit
        Left = 107
        Top = 16
        Width = 200
        Height = 21
        TabOrder = 8
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeBezeichnung: TIWCGJQEdit
        Left = 107
        Top = 48
        Width = 200
        Height = 21
        TabOrder = 10
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqeVisTierart: TIWCGJQEdit
        Left = 107
        Top = 80
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        ZIndex = 5001
        ScriptEvents = <>
        Text = ''
      end
      object jqdBegdat: TIWCGJQDatePicker
        Left = 107
        Top = 149
        Width = 200
        Height = 21
        TabOrder = 14
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorherhiger'
        JQDatePickerOptions.Regional = dporGerman
      end
      object jqdEnddat: TIWCGJQDatePicker
        Left = 107
        Top = 176
        Width = 200
        Height = 21
        TabOrder = 15
        Version = '1.0'
        ZIndex = 5001
        Caption = ''
        JQDatePickerOptions.CurrentText = 'Heute'
        JQDatePickerOptions.DateFormat = 'dd.mm.yyyy'
        JQDatePickerOptions.NextText = 'N'#228'chster'
        JQDatePickerOptions.PrevText = 'Vorherhiger'
        JQDatePickerOptions.Regional = dporGerman
      end
    end
  end
  inherited jqdpGrid: TIWCGJQGridDataSetProvider
    DataSet = quTierarten
    Left = 904
  end
  object quTierarten: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'SELECT *'
      'FROM   Stammdaten.Tierarten'
      'WHERE  Bldcode = :bldcode AND Aktiv = 1;')
    Left = 673
    Top = 25
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end>
  end
  object quNeu: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      
        'INSERT INTO Stammdaten.Tierarten (KZ, Bezeichnung, Vis_Tierart, ' +
        'Bldcode, Sichtbar, Aktiv, Begdat, Enddat)'
      
        'VALUES      (:kz, :bezeichnung, :vis_tierart, :bldcode, :sichtba' +
        'r, 1, :begdat, :enddat);')
    Left = 729
    Top = 25
    ParamData = <
      item
        Name = 'KZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VIS_TIERART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
  end
  object quAendern: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'UPDATE Stammdaten.Tierarten '
      
        'SET    KZ = :kz, Bezeichnung = :bezeichnung, Vis_Tierart = :vis_' +
        'tierart, Bldcode = :bldcode, Sichtbar = :sichtbar, Begdat = :beg' +
        'dat, Enddat = :enddat'
      'WHERE  ID = :id;')
    Left = 785
    Top = 25
    ParamData = <
      item
        Name = 'KZ'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEZEICHNUNG'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'VIS_TIERART'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'SICHTBAR'
        DataType = ftBoolean
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'BEGDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ENDDAT'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
  object quLoeschen: TFDQuery
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'DELETE FROM Stammdaten.Tierarten'
      'WHERE       ID = :id;')
    Left = 848
    Top = 24
    ParamData = <
      item
        Name = 'ID'
        DataType = ftInteger
        ParamType = ptInput
        Value = Null
      end>
  end
end
