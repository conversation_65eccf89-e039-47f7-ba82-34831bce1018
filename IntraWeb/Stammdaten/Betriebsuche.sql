declare @searchcount int;
set @searchcount = 2;

declare @search1 varchar(100);
set @search1 = 'mayer';
declare @search2 varchar(100);
set @search2 = '3493'; 


Select top 200 
       b.[ID],b.[R<PERSON><PERSON>],b.[<PERSON>AM<PERSON>],b.[ID_ADRESSE],a.[STRASSE],a.[PLZ],a.[ORT],
       g.[GEMEIN<PERSON>KENNZIFFER],g.[GEMEINDENAME],b.[AUFSICHTSORGAN],b.[TELEFON],b.[EMAIL],
	   b.[VERGEBUEHRUNG],b.[BLDCODE],b.[VULGO],b.[ANMERKUNG],b.[<PERSON><PERSON>K<PERSON>],b.[<PERSON><PERSON><PERSON><PERSON>],
	   z.<PERSON> as ZulassungsNr, z.Aktiv as ZulassungAktiv
from Stammdaten.betriebe b 
     LEFT OUTER JOIN STAMMDATEN.ADRESSEN a on b.ID_ADRESSE = a.ID
     LEFT OUTER JOIN STAMMDATEN.ZULASSUNGEN z ON b.REGNR = z.REGNR
	 LEFT outer join STAMMDATEN.GEMEINDEN g on a.ID_GEMEINDE = g.ID
where 
     b.BLDCODE = 300
--and  b.REGNR = z.REGNR and z.SICHTBAR = 1 and z.BEGINNDATUM <= GETDATE() and z.ENDDATUM >= GETDATE()
and
case
when (@searchcount=1) and 
         (charindex(@search1, b.Name +' '+ a.ORT +' '+ a.STRASSE +' '+ a.PLZ +' '+ b.REGNR + ' ' + COALESCE(z.ZULNR, '')) >0)
	 then 1
when (@searchcount=2) and 
         (charindex(@search1, b.Name +' '+ a.ORT +' '+ a.STRASSE +' '+ a.PLZ +' '+ b.REGNR + ' ' + COALESCE(z.ZULNR, '')) >0)
     and (charindex(@search2, b.Name +' '+ a.ORT +' '+ a.STRASSE +' '+ a.PLZ +' '+ b.REGNR + ' ' + COALESCE(z.ZULNR, '')) >0)
	 then 1
else 0
end = 1;
