inherited FormStammdaten: TFormStammdaten
  Width = 1250
  Height = 790
  Title = 'Stammdaten'
  DesignLeft = 2
  DesignTop = 2
  object iwrModal: TIWCGJQRegion [0]
    Left = 136
    Top = 76
    Width = 1000
    Height = 550
    Visible = False
    RenderInvisibleControls = True
    TabOrder = 75
    Version = '1.0'
    object iwrRevisionsstamm: TIWCGJQRegion
      Left = 243
      Top = 19
      Width = 473
      Height = 214
      Visible = False
      RenderInvisibleControls = True
      TabOrder = 76
      Version = '1.0'
      Color = clWindow
      object iwlSektion: TIWCGJQLabel
        Left = 33
        Top = 25
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'iwlSektion'
        Caption = 'Sektion:'
      end
      object iwlBetriebsgruppeLM: TIWCGJQLabel
        Left = 33
        Top = 53
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel5'
        Caption = 'Betriebsgruppe:'
      end
      object iwlBetriebsgruppeDetail: TIWCGJQLabel
        Left = 33
        Top = 80
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel5'
        Caption = 'Betriebsgruppe Detail:'
      end
      object iwlBetriebsart: TIWCGJQLabel
        Left = 33
        Top = 107
        Width = 150
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel5'
        Caption = 'Betriebsart:'
      end
      object iwmRevstammBetriebsart: TIWMemo
        Left = 200
        Top = 108
        Width = 201
        Height = 93
        StyleRenderOptions.RenderBorder = False
        BGColor = clNone
        Editable = True
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        InvisibleBorder = False
        HorizScrollBar = False
        VertScrollBar = True
        Required = False
        TabOrder = 12
        SubmitOnAsyncEvent = True
        FriendlyName = 'iwmRevstammBetriebsart'
      end
      object EditRevstammSektion: TIWCGJQEdit
        Left = 199
        Top = 25
        Width = 200
        Height = 22
        TabOrder = 5
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        MaxLength = 5
        ScriptEvents = <>
        Text = ''
      end
      object EditRevstammBG: TIWCGJQEdit
        Left = 199
        Top = 53
        Width = 200
        Height = 22
        TabOrder = 7
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        MaxLength = 20
        ScriptEvents = <>
        Text = ''
      end
      object EditRevstammBGDetail: TIWCGJQEdit
        Left = 199
        Top = 80
        Width = 200
        Height = 22
        TabOrder = 9
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        MaxLength = 10
        ScriptEvents = <>
        Text = ''
      end
    end
    object iwrRevplan: TIWCGJQRegion
      Left = 235
      Top = 38
      Width = 473
      Height = 110
      RenderInvisibleControls = True
      TabOrder = 77
      Version = '1.0'
      Color = clWindow
      object IWLabel9: TIWCGJQLabel
        Left = 10
        Top = 26
        Width = 215
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel9'
        Caption = 'Risiko Kategorie:'
      end
      object IWLabel10: TIWCGJQLabel
        Left = 10
        Top = 53
        Width = 215
        Height = 16
        Alignment = taRightJustify
        Font.Color = clNone
        Font.Size = 10
        Font.Style = []
        HasTabOrder = False
        AutoSize = False
        FriendlyName = 'IWLabel9'
        Caption = 'J'#228'hrliche Mindestkontrollfrequenz:'
      end
      object EditRevplanRisikoKategorie: TIWCGJQEdit
        Left = 232
        Top = 26
        Width = 200
        Height = 21
        TabOrder = 12
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
      end
      object EditRevplanMindestkontrollfrequenz: TIWCGJQEdit
        Left = 232
        Top = 53
        Width = 200
        Height = 21
        TabOrder = 13
        Version = '1.0'
        StyleRenderOptions.RenderBorder = False
        ScriptEvents = <>
        Text = ''
        FormatOptions.Precision = 3
        InputType = etNumber
      end
    end
  end
  inherited RegionToolbar: TIWCGJQRegion
    Width = 1250
    TabOrder = 74
    inherited ImageLogo: TIWImageFile
      Left = 967
    end
    inherited LabelTitle: TIWCGJQLabel
      Width = 758
    end
    inherited ButtonBeenden: TIWCGJQButton
      TabOrder = 72
    end
  end
  object iwtStammdaten: TIWTabControl [2]
    Left = 20
    Top = 50
    Width = 1210
    Height = 715
    Margins.Left = 10
    Margins.Right = 10
    RenderInvisibleControls = False
    OnChange = iwtStammdatenChange
    ActiveTabFont.Color = clWebWHITE
    ActiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    ActiveTabFont.Size = 10
    ActiveTabFont.Style = [fsBold]
    InactiveTabFont.Color = clWebBLACK
    InactiveTabFont.FontFamily = 'Arial, Sans-Serif, Verdana'
    InactiveTabFont.Size = 10
    InactiveTabFont.Style = []
    ActiveTabColor = clWebDARKGRAY
    InactiveTabColor = clWebLIGHTGRAY
    ActivePage = 7
    Align = alClient
    BorderOptions.NumericWidth = 0
    BorderOptions.Style = cbsNone
    Color = clWebSILVER
    ClipRegion = False
    TabMargin = 3
    TabPadding = 0
    TabBorderRadius = 3
    ActiveTabBorder.Color = clWebBLACK
    ActiveTabBorder.Width = 1
    InactiveTabBorder.Color = clWebBLACK
    InactiveTabBorder.Width = 1
    DesignSize = (
      1210
      715)
    object TabTaetigkeitenBetriebstyp: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 5
      Title = 'T'#228'tigkeiten-betriebstyp'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_TaetigkeitBetrtyp: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1210
        Height = 623
        TabOrder = 23
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSTYP'
            Name = 'BETRIEBSTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'BEZEICHNUNG'
          end>
        JQGridOptions.Height = 569
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_Taetigkeitenbetrtyp
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion6: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 78
        Version = '1.0'
        Align = alTop
        object IWLabel3: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 53
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'T'#228'tigkeit'
          Caption = 'T'#228'tigkeit'
        end
        object IWLabel4: TIWCGJQLabel
          Left = 157
          Top = 3
          Width = 69
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'Betriebstyp'
          Caption = 'Betriebstyp'
        end
        object iwl_tatbetrtyp_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWL_Betriebe_count'
          Caption = '-'
        end
        object Edit_Tatbetrtyp_taetigkeit: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 17
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_tatbetrtyp_betriebstyp: TIWCGJQEdit
          Left = 157
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 18
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_suche_taetigbetrtyp: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 24
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_suche_taetigbetrtypJQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
        object iwcgbtn_loesche_BetrtypTatsuche: TIWCGJQButton
          Left = 769
          Top = 23
          Width = 30
          Height = 21
          TabOrder = 53
          Version = '1.0'
          JQButtonOptions.Icons.Primary = 'ui-icon-close'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_loesche_BetrtypTatsucheJQButtonOptionsClick
        end
      end
    end
    object TabTaetigkeiten: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 4
      Title = 'T'#228'tigkeiten'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_Taetigkeiten: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1210
        Height = 518
        TabOrder = 21
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KURZ_BEZ'
            Name = 'KURZ_BEZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kurzbezeichnung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'AKTIV'
            Name = 'AKTIV'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Aktiv'
          end
          item
            Align = gaRight
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            EditType = gcetCheckBox
            Formatter = gcfCheckBox
            Idx = 'ZULASSUNGSPFL'
            Name = 'ZULASSUNGSPFL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungspflichtig'
          end>
        JQGridOptions.Height = 464
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = iwcggrid_TaetigkeitenJQGridOptionsSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderTaetigkeiten
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion2: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 81
        Version = '1.0'
        Align = alTop
        object IWLabel1: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 110
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_name'
          Caption = 'Kurz Bezeichnung'
        end
        object IWLabel2: TIWCGJQLabel
          Left = 157
          Top = 3
          Width = 78
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_bstammdaten_regnr'
          Caption = 'Bezeichnung'
        end
        object iwl_tat_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWL_Betriebe_count'
          Caption = '-'
        end
        object Edit_tat_kurzbez: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 27
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_tat_bezeichnung: TIWCGJQEdit
          Left = 157
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 28
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_suche_taetigkeit: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 22
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = IWCGJQButton3JQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
        object iwcgbtn_loesche_Taetigkeitssuche: TIWCGJQButton
          Left = 769
          Top = 23
          Width = 30
          Height = 21
          TabOrder = 52
          Version = '1.0'
          JQButtonOptions.Icons.Primary = 'ui-icon-close'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_loesche_TaetigkeitssucheJQButtonOptionsClick
        end
      end
      object iwrTaetigkeitenBearbeiten: TIWCGJQRegion
        Left = 0
        Top = 590
        Width = 1210
        Height = 105
        RenderInvisibleControls = True
        TabOrder = 82
        Version = '1.0'
        Align = alBottom
        object iwl_Taetigkeit_kurzbez: TIWCGJQLabel
          Left = 30
          Top = 10
          Width = 125
          Height = 19
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 12
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_Taetigkeit_kurzbez'
          Caption = 'Kurzbezeichnung:'
        end
        object iwl_taetigkeit_bezeichnung: TIWCGJQLabel
          Left = 67
          Top = 37
          Width = 88
          Height = 19
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 12
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel5'
          Caption = 'Bezeichnung'
        end
        object iwcb_Taetigkeit_aktiv: TIWCheckBox
          Left = 317
          Top = 8
          Width = 121
          Height = 21
          Caption = 'Aktiv'
          Editable = False
          Font.Color = clNone
          Font.Size = 12
          Font.Style = []
          SubmitOnAsyncEvent = True
          Style = stNormal
          TabOrder = 32
          Checked = False
          FriendlyName = 'iwcb_Taetigkeit_aktiv'
        end
        object iwcb_Taetigkeit_zulassungspfl: TIWCheckBox
          Left = 317
          Top = 35
          Width = 177
          Height = 24
          Caption = 'Zulassungspflichtig'
          Editable = False
          Font.Color = clNone
          Font.Size = 12
          Font.Style = []
          SubmitOnAsyncEvent = True
          Style = stNormal
          TabOrder = 33
          Checked = False
          FriendlyName = 'iwcb_Taetigkeit_zulassungspfl'
        end
        object Edit_Taetigkeit_Kurzbez: TIWCGJQEdit
          Left = 175
          Top = 8
          Width = 121
          Height = 21
          TabOrder = 29
          Font.Size = 12
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Editable = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_Taetigkeit_Bezeichnung: TIWCGJQEdit
          Left = 175
          Top = 35
          Width = 121
          Height = 21
          TabOrder = 30
          Font.Size = 12
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Editable = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_Taetigkeit_abbrechen: TIWCGJQButton
          Left = 610
          Top = 65
          Width = 108
          Height = 21
          Visible = False
          TabOrder = 33
          Version = '1.0'
          JQButtonOptions.Label_ = 'abbrechen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_Taetigkeit_abbrechenJQButtonOptionsClick
        end
        object iwcgb_Taetigkeit_aendern: TIWCGJQButton
          Left = 496
          Top = 35
          Width = 108
          Height = 21
          TabOrder = 34
          Version = '1.0'
          Enabled = False
          JQButtonOptions.Disabled = True
          JQButtonOptions.Label_ = #228'ndern'
          JQButtonOptions.OnClick.OnEvent = iwcgb_Taetigkeit_aendernJQButtonOptionsClick
        end
        object iwcgb_Taetigkeit_neu: TIWCGJQButton
          Left = 496
          Top = 8
          Width = 108
          Height = 21
          TabOrder = 35
          Version = '1.0'
          JQButtonOptions.Label_ = 'neu'
          JQButtonOptions.OnClick.OnEvent = iwcgb_Taetigkeit_neuJQButtonOptionsClick
        end
        object iwcgb_Taetigkeit_speichern: TIWCGJQButton
          Left = 496
          Top = 65
          Width = 108
          Height = 21
          Visible = False
          TabOrder = 36
          Version = '1.0'
          JQButtonOptions.Label_ = 'speichern'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_Taetigkeit_speichernJQButtonOptionsClick
        end
      end
    end
    object TabTierarten: TIWTabPage
      Left = 0
      Top = 14
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 2
      Title = 'Tierarten'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
    end
    object TabTierartenkategorie: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 3
      Title = 'Tierartenkategorie'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_Tierartkategorie: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1210
        Height = 623
        TabOrder = 19
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VIS_KATEG'
            Name = 'VIS_KATEG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kategorie im VIS'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KZ'
            Name = 'KZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Kurzzeichen'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BEZEICHNUNG'
            Name = 'BEZEICHNUNG'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Bezeichnung'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'VIS_TIERART'
            Name = 'VIS_TIERART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tierart im VIS'
          end>
        JQGridOptions.Height = 569
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.Ajax = False
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_TAkat
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion1: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 89
        Version = '1.0'
        Align = alTop
        object iwl_TAkat_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWL_Betriebe_count'
          Caption = '-'
        end
        object iwl_TAkateg_Kategorie: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 59
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_TAkateg_Kategorie'
          Caption = 'Kategorie'
        end
        object Edit_TAkat_kategorie: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 15
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_suche_TAKateg: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 20
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_suche_TAKategJQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
        object iwcgbtn_TAkat_suchebeenden: TIWCGJQButton
          Left = 769
          Top = 23
          Width = 30
          Height = 21
          TabOrder = 51
          Version = '1.0'
          JQButtonOptions.Icons.Primary = 'ui-icon-close'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_TAkat_suchebeendenJQButtonOptionsClick
        end
      end
    end
    object TabBetriebstypenTierart: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 7
      Title = 'Betriebstypen-Tierart'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwcggrid_BetrtypTA: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1210
        Height = 524
        TabOrder = 25
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSTYP'
            Name = 'BETRIEBSTYP'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebstyp'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TIERART'
            Name = 'TIERART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Tierart'
          end>
        JQGridOptions.Height = 470
        JQGridOptions.RowNum = 200
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = iwcggrid_BetrtypTAJQGridOptionsSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = iwcgprov_BetrtypTA
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion7: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 83
        Version = '1.0'
        Align = alTop
        object iwl_betrtypTA_Betrtyp: TIWCGJQLabel
          Left = 30
          Top = 3
          Width = 69
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'T'#228'tigkeit'
          Caption = 'Betriebstyp'
        end
        object iwl_betrtypTA_ta: TIWCGJQLabel
          Left = 157
          Top = 3
          Width = 96
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'Betriebstyp'
          Caption = 'Tierart (K'#252'rzel)'
        end
        object iwl_BetrtypTA_count: TIWCGJQLabel
          Left = 688
          Top = 50
          Width = 5
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWL_Betriebe_count'
          Caption = '-'
        end
        object Edit_betrtypta_betrtyp: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 37
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betrtypTA_TA: TIWCGJQEdit
          Left = 157
          Top = 25
          Width = 121
          Height = 21
          TabOrder = 38
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_betrtyp_suche: TIWCGJQButton
          Left = 688
          Top = 23
          Width = 75
          Height = 21
          TabOrder = 26
          Version = '1.0'
          JQButtonOptions.Label_ = 'suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_betrtyp_sucheJQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
        object iwcgbtn_loesche_BetrtypTAsuche: TIWCGJQButton
          Left = 769
          Top = 23
          Width = 30
          Height = 21
          TabOrder = 54
          Version = '1.0'
          JQButtonOptions.Icons.Primary = 'ui-icon-close'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_loesche_BetrtypTAsucheJQButtonOptionsClick
        end
      end
      object iwrBetriebstypenTierartBearbeiten: TIWCGJQRegion
        Left = 0
        Top = 596
        Width = 1210
        Height = 99
        RenderInvisibleControls = True
        TabOrder = 84
        Version = '1.0'
        Align = alBottom
        object IWLabel5: TIWCGJQLabel
          Left = 25
          Top = 21
          Width = 74
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel5'
          Caption = 'Betriebstyp:'
        end
        object IWLabel6: TIWCGJQLabel
          Left = 26
          Top = 51
          Width = 48
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel5'
          Caption = 'Tierart:'
        end
        object Edit_betrtTA_Betrtyp: TIWCGJQEdit
          Left = 111
          Top = 21
          Width = 200
          Height = 21
          TabOrder = 41
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Editable = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betrtTA_Tierart: TIWCGJQEdit
          Left = 111
          Top = 51
          Width = 200
          Height = 21
          TabOrder = 42
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Editable = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgbtn_betrtTA_neu: TIWCGJQButton
          Left = 371
          Top = 6
          Width = 75
          Height = 21
          TabOrder = 46
          Version = '1.0'
          JQButtonOptions.Label_ = 'neu'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_betrtTA_neuJQButtonOptionsClick
        end
        object iwcgbtn_betrtTA_aendern: TIWCGJQButton
          Left = 371
          Top = 33
          Width = 75
          Height = 21
          TabOrder = 47
          Version = '1.0'
          JQButtonOptions.Label_ = #228'ndern'
        end
        object iwcgbtn_betrtTA_speichern: TIWCGJQButton
          Left = 371
          Top = 60
          Width = 75
          Height = 21
          TabOrder = 48
          Version = '1.0'
          JQButtonOptions.Label_ = 'speichern'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_betrtTA_speichernJQButtonOptionsClick
        end
        object iwcgbtn_betrtTA_abbrechen: TIWCGJQButton
          Left = 452
          Top = 60
          Width = 75
          Height = 21
          TabOrder = 49
          Version = '1.0'
          JQButtonOptions.Label_ = 'abbrechen'
        end
      end
    end
    object TabBetriebe: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 1
      Title = 'Betriebe'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object GridBetriebe: TIWCGJQGrid
        Left = 0
        Top = 72
        Width = 1210
        Height = 395
        TabOrder = 14
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BVBKZ'
            Name = 'BVBKZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'BVB'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'REGNR'
            Name = 'REGNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'Reg. Nr.'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ZulassungsNr'
            Name = 'ZulassungsNr'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Zulassungs Nr'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'NAME'
            Name = 'NAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 240
            Caption = 'Name'
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GEMNR'
            Name = 'GEMNR'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'GEMEINDEKENNZIFFER'
            ProviderName = 'GEMEINDEKENNZIFFER'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'KATASTRALGEMNAME'
            Name = 'KATASTRALGEMNAME'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'GEMEINDENAME'
            ProviderName = 'GEMEINDENAME'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'STRASSE'
            Name = 'STRASSE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Stra'#223'e'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'PLZ'
            Name = 'PLZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 50
            Caption = 'PLZ'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ORT'
            Name = 'ORT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 200
            Caption = 'Ort'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'TELEFON'
            Name = 'TELEFON'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Telefon'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'EMAIL'
            Name = 'EMAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'E-Mail'
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'AUFSICHTSORGAN'
            Name = 'AUFSICHTSORGAN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 100
            Caption = 'Aufsichtsorgan'
          end>
        JQGridOptions.Height = 368
        JQGridOptions.LoadOnce = True
        JQGridOptions.RowNum = 200
        JQGridOptions.Sortable = True
        JQGridOptions.SubGridModel = <>
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnLoadComplete.OnEvent = GridBetriebeJQGridOptionsLoadComplete
        JQGridOptions.OnSelectRow.OnEvent = GridBetriebeOnSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridOptions.SelectTopRowOnLoad = True
        JQGridOptions.PagerVisible = False
        JQGridNav.Active = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderBetriebe
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
      object IWRegion3: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 72
        RenderInvisibleControls = True
        TabOrder = 90
        Version = '1.0'
        Align = alTop
        object LabelCount: TIWLabel
          Left = 677
          Top = 28
          Width = 147
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          NoWrap = True
          HasTabOrder = False
          FriendlyName = 'LabelCount'
          Caption = '0 Ergebnisse gefunden.'
        end
        object EditBetriebSuche: TIWCGJQEdit
          Left = 30
          Top = 25
          Width = 512
          Height = 30
          TabOrder = 57
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          ScriptEvents = <>
          Text = ''
        end
        object iwcgb_sucheBetrieb: TIWCGJQButton
          Left = 548
          Top = 25
          Width = 75
          Height = 30
          TabOrder = 16
          Version = '1.0'
          JQButtonOptions.Label_ = 'Suchen'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.OnEvent = iwcgb_sucheBetriebJQButtonOptionsClick
          JQButtonOptions.OnClick.SendAllArguments = True
          Default.Enabled = True
        end
        object iwcgbtn_loesche_Betriebssuche: TIWCGJQButton
          Left = 629
          Top = 25
          Width = 30
          Height = 30
          TabOrder = 50
          Version = '1.0'
          JQButtonOptions.Text = False
          JQButtonOptions.Icons.Primary = 'ui-icon-close'
          JQButtonOptions.OnClick.OnEvent = iwcgbtn_loesche_BetriebssucheJQButtonOptionsClick
        end
      end
      object iwrBetriebeBearbeiten: TIWCGJQRegion
        Left = 0
        Top = 467
        Width = 1210
        Height = 228
        RenderInvisibleControls = True
        TabOrder = 91
        Version = '1.0'
        Align = alBottom
        object iwl_betr_name: TIWCGJQLabel
          Left = 64
          Top = 37
          Width = 83
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'Betriebsname:'
          Caption = 'Betriebsname:'
        end
        object iwl_betr_GemNr: TIWCGJQLabel
          Left = 64
          Top = 72
          Width = 83
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betr_GemNr'
          Caption = 'Gemeinde Nr.:'
        end
        object iwl_betr_katastralgem: TIWCGJQLabel
          Left = 36
          Top = 100
          Width = 111
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Katastralgemeinde:'
        end
        object iwl_betr_Aufsicht: TIWCGJQLabel
          Left = 58
          Top = 134
          Width = 89
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Aufsichtsorgan:'
        end
        object iwl_Betr_Vegeb: TIWCGJQLabel
          Left = 677
          Top = 72
          Width = 86
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Vergeb'#252'hrung:'
        end
        object iwl_betr_Strass: TIWCGJQLabel
          Left = 411
          Top = 12
          Width = 43
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Stra'#223'e:'
        end
        object iwl_betr_PLZ: TIWCGJQLabel
          Left = 429
          Top = 43
          Width = 25
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'PLZ:'
        end
        object iwl_betr_Ort: TIWCGJQLabel
          Left = 431
          Top = 72
          Width = 23
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Ort:'
        end
        object iwl_betr_Telefon: TIWCGJQLabel
          Left = 406
          Top = 99
          Width = 48
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Telefon:'
        end
        object iwl_betr_EMail: TIWCGJQLabel
          Left = 414
          Top = 134
          Width = 40
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'E-Mail:'
        end
        object iwl_betrieb_vulgo: TIWCGJQLabel
          Left = 726
          Top = 12
          Width = 37
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betrieb_vulgo'
          Caption = 'Vulgo:'
        end
        object IWLabel7: TIWCGJQLabel
          Left = 697
          Top = 37
          Width = 66
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betr_bvbkz'
          Caption = 'BVB K'#252'rzel:'
        end
        object iwl_betr_regnr: TIWCGJQLabel
          Left = 103
          Top = 12
          Width = 44
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'iwl_betr_regnr'
          Caption = 'RegNr.:'
        end
        object IWCGJQMemo1: TIWCGJQMemo
          Left = 776
          Top = 99
          Width = 200
          Height = 62
          StyleRenderOptions.RenderFont = False
          StyleRenderOptions.RenderBorder = False
          BGColor = clNone
          Editable = True
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          InvisibleBorder = False
          HorizScrollBar = False
          VertScrollBar = True
          Required = False
          SubmitOnAsyncEvent = True
          ResizeDirection = rdHorizontal
          Enabled = False
          FriendlyName = 'IWCGJQMemo1'
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'ANMERKUNG'
        end
        object IWCGJQLabel1: TIWCGJQLabel
          Left = 679
          Top = 99
          Width = 84
          Height = 16
          Alignment = taRightJustify
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel6'
          Caption = 'Anmerkungen:'
        end
        object Edit_betr_vergeb: TIWCGJQEdit
          Left = 776
          Top = 68
          Width = 200
          Height = 25
          TabOrder = 59
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'VERGEBUEHRUNG'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_name: TIWCGJQEdit
          Left = 160
          Top = 37
          Width = 192
          Height = 25
          TabOrder = 60
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'NAME'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_gemnr: TIWCGJQEdit
          Left = 160
          Top = 68
          Width = 192
          Height = 25
          TabOrder = 62
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeGemeinden
          DataLink.FieldName = 'GEMEINDEKENNZIFFER'
          Alignment = taRightJustify
          Enabled = False
          MaxLength = 5
          ReadOnly = True
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_aufsicht: TIWCGJQEdit
          Left = 160
          Top = 130
          Width = 192
          Height = 25
          TabOrder = 63
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'AUFSICHTSORGAN'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_strasse: TIWCGJQEdit
          Left = 467
          Top = 6
          Width = 192
          Height = 25
          TabOrder = 64
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeAdressen
          DataLink.FieldName = 'STRASSE'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_email: TIWCGJQEdit
          Left = 467
          Top = 130
          Width = 192
          Height = 25
          TabOrder = 66
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'EMAIL'
          Enabled = False
          ScriptEvents = <>
          Text = ''
          JQValidateOptions.Enable = True
          JQValidateOptions.Rules = <
            item
              Rule = jqvorvEMail
              ErrorMessage = 'E-Mail ist ung'#252'ltig'
            end>
        end
        object Edit_betr_telefon: TIWCGJQEdit
          Left = 467
          Top = 99
          Width = 192
          Height = 25
          TabOrder = 71
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'TELEFON'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_vulgo: TIWCGJQEdit
          Left = 776
          Top = 6
          Width = 200
          Height = 25
          TabOrder = 73
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'VULGO'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object Edit_betr_bvbkz: TIWCGJQEdit
          Left = 776
          Top = 37
          Width = 200
          Height = 25
          TabOrder = 79
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'BVBKZ'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object EditBetriebRegNr: TIWCGJQEdit
          Left = 160
          Top = 6
          Width = 192
          Height = 25
          TabOrder = 80
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeEdit
          DataLink.FieldName = 'REGNR'
          Enabled = False
          ScriptEvents = <>
          Text = ''
        end
        object ButtonBetriebNeu: TIWCGJQButton
          Left = 1094
          Top = 6
          Width = 75
          Height = 21
          Visible = False
          TabOrder = 40
          Version = '1.0'
          Enabled = False
          JQButtonOptions.Disabled = True
          JQButtonOptions.Label_ = 'neu'
          JQButtonOptions.OnClick.OnEvent = ButtonBetriebNeuClick
        end
        object ButtonBetriebAendern: TIWCGJQButton
          Left = 1013
          Top = 6
          Width = 75
          Height = 21
          TabOrder = 43
          Version = '1.0'
          JQButtonOptions.Label_ = #228'ndern'
          JQButtonOptions.OnClick.OnEvent = ButtonBetriebAendernClick
        end
        object ButtonBetriebSpeichern: TIWCGJQButton
          Left = 1013
          Top = 33
          Width = 75
          Height = 21
          TabOrder = 44
          Version = '1.0'
          Enabled = False
          JQButtonOptions.Disabled = True
          JQButtonOptions.Label_ = 'speichern'
          JQButtonOptions.OnClick.Ajax = False
          JQButtonOptions.OnClick.AjaxAppend = False
          JQButtonOptions.OnClick.OnEvent = ButtonBetriebSpeichernClick
        end
        object ButtonBetriebAbbrechen: TIWCGJQButton
          Left = 1094
          Top = 33
          Width = 75
          Height = 21
          TabOrder = 45
          Version = '1.0'
          Enabled = False
          JQButtonOptions.Disabled = True
          JQButtonOptions.Label_ = 'abbrechen'
          JQButtonOptions.OnClick.OnEvent = ButtonBetriebAbbrechenClick
        end
        object EditBetriebOrt: TIWCGJQDropDown
          Left = 467
          Top = 68
          Width = 192
          Height = 25
          TabOrder = 95
          Version = '1.0'
          Enabled = False
          DataLink.DataSource = DSBetriebeAdressen
          DataLink.FieldName = 'ID_GEMEINDE'
          DataLink.ListDataSource = DSGemeindesuche
          DataLink.ListFieldNames = 'PlzOrt'
          DataLink.ListSelectFieldName = 'ID'
          DataLink.ListLookupResultFieldName = 'Gemeindename'
          JQDropDownOptions.MinimumInputLength = 2
          JQDropDownOptions.Ajax.QuietMillis = 100
          JQDropDownOptions.Ajax.Use = True
          JQDropDownOptions.AttachTo = jqddatInput
          JQDropDownOptions.InfiniteScroll = True
          JQDropDownOptions.NoMatchesMsg = 'Keine Treffer'
          JQDropDownOptions.SearchingMsg = 'Suche'
          JQDropDownOptions.InputTooShortMsg = 'Bitte geben Sie noch %d Zeichen mehr ein ...'
          Groups = <>
          Items = <>
        end
        object EditBetriebKastralGemeinde: TIWCGJQEdit
          Left = 160
          Top = 99
          Width = 192
          Height = 25
          TabOrder = 96
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeGemeinden
          DataLink.FieldName = 'GEMEINDENAME'
          Alignment = taRightJustify
          Enabled = False
          MaxLength = 5
          ReadOnly = True
          ScriptEvents = <>
          Text = ''
        end
        object EditBetriebPlz: TIWCGJQEdit
          Left = 467
          Top = 37
          Width = 192
          Height = 25
          TabOrder = 97
          Css = 'ui-widget ui-widget-content ui-corner-all ui-state-disabled'
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          DataLink.DataSource = DSBetriebeAdressen
          DataLink.FieldName = 'PLZ'
          Enabled = False
          ReadOnly = True
          ScriptEvents = <>
          Text = ''
        end
      end
    end
    object TabRevisionsstamm: TIWTabPage
      Left = 0
      Top = 16
      Width = 1216
      Height = 693
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 9
      Title = 'Revisionsstamm'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwre_sucheRevisionsStamm: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1216
        Height = 78
        RenderInvisibleControls = True
        TabOrder = 85
        Version = '1.0'
        Align = alTop
        object iwrRevisionsstammSuchen: TIWCGJQRegion
          Left = 0
          Top = 0
          Width = 156
          Height = 78
          RenderInvisibleControls = True
          TabOrder = 86
          Version = '1.0'
          Align = alLeft
          object jqbSucheRevisionsstamm: TIWCGJQButton
            Left = 29
            Top = 21
            Width = 94
            Height = 21
            TabOrder = 67
            Version = '1.0'
            JQButtonOptions.Label_ = 'Abfragen'
            JQButtonOptions.OnClick.OnEvent = jqbSucheRevisionsstammOnClick
          end
        end
        object iwrRevisionsstammBearbeiten: TIWCGJQRegion
          Left = 156
          Top = 0
          Width = 536
          Height = 78
          RenderInvisibleControls = True
          TabOrder = 87
          Version = '1.0'
          Align = alLeft
          object jqbRevAendern: TIWCGJQButton
            Left = 135
            Top = 21
            Width = 94
            Height = 21
            TabOrder = 68
            Version = '1.0'
            JQButtonOptions.Label_ = #196'ndern'
            JQButtonOptions.OnClick.OnEvent = jqbRevAendernOnClick
          end
          object jqbRevLoeschen: TIWCGJQButton
            Left = 255
            Top = 21
            Width = 94
            Height = 21
            TabOrder = 69
            Version = '1.0'
            JQButtonOptions.Label_ = 'L'#246'schen'
            JQButtonOptions.OnClick.OnEvent = jqbRevLoeschenOnClick
          end
          object jqbRevNeu: TIWCGJQButton
            Left = 18
            Top = 21
            Width = 94
            Height = 21
            TabOrder = 70
            Version = '1.0'
            JQButtonOptions.Label_ = 'Neu'
            JQButtonOptions.OnClick.OnEvent = jqbRevNeuOnClick
          end
        end
      end
      object jqgRevisionsstamm: TIWCGJQGrid
        Left = 0
        Top = 78
        Width = 1216
        Height = 615
        TabOrder = 39
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'SEKTION'
            Name = 'SEKTION'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Caption = 'Sektion'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            FirstSortOrder = gcfsoAsc
            Idx = 'BETRIEBSGRUPPE_LM'
            Name = 'BETRIEBSGRUPPE_LM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe LM'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSGRUPPE_DETAIL'
            Name = 'BETRIEBSGRUPPE_DETAIL'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe Detail'
          end
          item
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'BETRIEBSART'
            Name = 'BETRIEBSART'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsart'
          end>
        JQGridOptions.Height = 561
        JQGridOptions.RowNum = 100
        JQGridOptions.SubGridModel = <>
        JQGridOptions.ViewRecords = True
        JQGridOptions.Width = 1214
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProvRevisionsstamm
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
    object TabRevisionsplan: TIWTabPage
      Left = 0
      Top = 20
      Width = 1210
      Height = 695
      Visible = False
      RenderInvisibleControls = False
      TabOrder = 11
      Title = 'Revisionsplan'
      BorderOptions.NumericWidth = 0
      BorderOptions.Style = cbsNone
      Color = clWebWHITE
      object iwrRevplanTop: TIWCGJQRegion
        Left = 0
        Top = 0
        Width = 1210
        Height = 70
        RenderInvisibleControls = True
        TabOrder = 88
        Version = '1.0'
        Align = alTop
        object IWLabel8: TIWCGJQLabel
          Left = 16
          Top = 24
          Width = 103
          Height = 16
          Font.Color = clNone
          Font.Size = 10
          Font.Style = []
          HasTabOrder = False
          FriendlyName = 'IWLabel8'
          Caption = 'Jahr ausw'#228'hlen:'
        end
        object jqcbRevplanJahr: TIWCGJQComboBoxEx
          Left = 120
          Top = 24
          Width = 150
          Height = 21
          TabOrder = 55
          Version = '1.0'
          StyleRenderOptions.RenderBorder = False
          Items = <>
          Groups = <>
          SelectedIndex = 0
          JQComboBoxExOptions.Width = 148
          Caption = ''
        end
        object jqbRevplanAbfragen: TIWCGJQButton
          Left = 286
          Top = 24
          Width = 100
          Height = 21
          TabOrder = 58
          Version = '1.0'
          JQButtonOptions.Label_ = 'Abfragen'
          JQButtonOptions.OnClick.OnEvent = jqbRevplanAbfragenOnClick
        end
        object jqbRevplanAbschliesen: TIWCGJQButton
          Left = 411
          Top = 24
          Width = 375
          Height = 21
          TabOrder = 61
          Version = '1.0'
          JQButtonOptions.Label_ = 'Revisionsplan abschlie'#223'en und Kontrollen erstellen'
          JQButtonOptions.OnClick.OnEvent = jqbRevplanAbschliessenOnClick
        end
        object jqbRevplanLoeschen: TIWCGJQButton
          Left = 814
          Top = 24
          Width = 175
          Height = 21
          TabOrder = 65
          Version = '1.0'
          JQButtonOptions.Label_ = 'Revisionsplan l'#246'schen'
          JQButtonOptions.OnClick.OnEvent = jqbRevplanLoeschenOnClick
        end
      end
      object GridRevisionsplan: TIWCGJQGrid
        Left = 0
        Top = 70
        Width = 1210
        Height = 625
        TabOrder = 56
        Version = '1.0'
        Align = alClient
        JQGridOptions.ColModel = <
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'RISIKO_KATEGORIE'
            Name = 'RISIKO_KATEGORIE'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Sortable = False
            Width = 70
            Caption = 'Risiko Kategorie'
            Position = 4
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Formatter = gcfNumber
            Idx = 'J_MINDEST_KONTROLL_FREQUENZ'
            Name = 'J_MINDEST_KONTROLL_FREQUENZ'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 120
            Caption = 'J'#228'hrliche Mindestkontrollfrequenz'
            Position = 5
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ANZ_BETRIEBE_IM_LAND'
            Name = 'ANZ_BETRIEBE_IM_LAND'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Anzahl Betriebe im Land'
            Position = 6
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'ANZ_GESAMT_KONTROLLEN'
            Name = 'ANZ_GESAMT_KONTROLLEN'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 80
            Caption = 'Anzahl Gesamt Kontrollen'
            Position = 7
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Sektion'
            Name = 'Sektion'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 60
            Caption = 'Sektion'
            Position = 0
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebsgruppe_LM'
            Name = 'Betriebsgruppe_LM'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe LM'
            Position = 1
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebsgruppe_Detail'
            Name = 'Betriebsgruppe_Detail'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Caption = 'Betriebsgruppe Detail'
            Position = 2
          end
          item
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'Betriebsart'
            Name = 'Betriebsart'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 650
            Caption = 'Betriebsart'
            Position = 3
          end
          item
            Align = gaRight
            Editable = True
            EditOptions.DatePickerOptions.DateFormat = 'mm/dd/yyyy'
            EditOptions.CustomElements = <>
            Idx = 'GESPERRT'
            Name = 'GESPERRT'
            SearchOptions.SOpt = [gsoEqual, gsoNotEqual, gsoBeginWith, gsoDoesntBegin, gsoIsIn, gsoIsNotIn, gsoEndsWith, gsoDoesntEndsWith, gsoContains, gsoDoesntContain]
            Width = 70
            Caption = 'Gesperrt'
          end>
        JQGridOptions.Height = 571
        JQGridOptions.SubGridModel = <>
        JQGridOptions.ViewRecords = True
        JQGridOptions.Width = 1208
        JQGridOptions.GroupingView.DragAndDropOptions.SortOptions.Disabled = False
        JQGridOptions.OnSelectRow.OnEvent = GridRevisionsplanOnSelectRow
        JQGridOptions.OnSelectRow.SendAllArguments = True
        JQGridNav.Add = False
        JQGridNav.Del = False
        JQGridNav.Edit = False
        JQGridNav.Refresh = False
        JQGridNav.Search = False
        JQGridNav.FormsOptions.SearchOptions.SearchOperators = <>
        JQGridCustomButtons = <>
        JQGridProvider = ProviderRevisionsplan
        JQGridToolbarSearch.DefaultSearch = gsoContains
        JQDragAndDropOptions.ConnectWith = <>
      end
    end
  end
  object IWRegion5: TIWCGJQRegion [3]
    Left = 1230
    Top = 50
    Width = 20
    Height = 715
    RenderInvisibleControls = True
    TabOrder = 92
    Version = '1.0'
    Align = alRight
    Color = clWebSILVER
  end
  object IWRegion9: TIWCGJQRegion [4]
    Left = 0
    Top = 50
    Width = 20
    Height = 715
    RenderInvisibleControls = True
    TabOrder = 93
    Version = '1.0'
    Align = alLeft
    Color = clWebSILVER
  end
  inherited RegionStatusbar: TIWCGJQRegion
    Top = 765
    Width = 1250
    TabOrder = 94
  end
  object jqsaStammdaten: TIWCGJQSweetAlert
    Version = '1.0'
    Left = 832
    Top = 65534
  end
  object ProviderBetriebe: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QBetriebeSuche
    KeyFields = 'ID'
    Left = 744
    Top = 206
  end
  object DSTaetigkeiten: TDataSource
    DataSet = DMStammdaten.QTaetigkeiten
    Left = 592
    Top = 272
  end
  object ProviderTaetigkeiten: TIWCGJQGridDataSetProvider
    DataSet = DMStammdaten.QTaetigkeiten
    DataSource = DSTaetigkeiten
    Left = 592
    Top = 206
  end
  object ds_TierKat: TDataSource
    DataSet = dm_main.qu_StDat_TAkat
    Left = 272
  end
  object iwcgprov_TAkat: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_StDat_TAkat
    DataSource = ds_TierKat
    Left = 520
    Top = 65534
  end
  object iwcgprov_Taetigkeitenbetrtyp: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_StDat_TaetBetrtyp
    Left = 648
    Top = 65534
  end
  object ds_TatBetrtyp: TDataSource
    DataSet = dm_main.qu_StDat_TaetBetrtyp
    Left = 192
    Top = 65534
  end
  object ds_BetrtypTA: TDataSource
    DataSet = dm_main.qu_StDat_BetrtypTA
    Left = 240
    Top = 65534
  end
  object iwcgprov_BetrtypTA: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_StDat_BetrtypTA
    DataSource = ds_BetrtypTA
    Left = 560
    Top = 65534
  end
  object ds_Revisionsstamm: TDataSource
    DataSet = dm_main.qu_StDat_RevisionsStamm
    Left = 352
  end
  object ProvRevisionsstamm: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_StDat_RevisionsStamm
    DataSource = ds_Revisionsstamm
    KeyFields = 'ID'
    Left = 324
    Top = 208
  end
  object iwmStammdaten: TIWModalWindow
    Left = 144
    Top = 320
  end
  object ProviderRevisionsplan: TIWCGJQGridDataSetProvider
    DataSet = dm_main.qu_stdat_revplan
    KeyFields = 'ID'
    Left = 1024
  end
  object DSBetriebeSuche: TDataSource
    DataSet = DMStammdaten.QBetriebeSuche
    Left = 744
    Top = 272
  end
  object DSBetriebeEdit: TDataSource
    DataSet = DMStammdaten.QBetriebeEdit
    OnStateChange = DSBetriebeEditStateChange
    Left = 744
    Top = 336
  end
  object DSBetriebeAdressen: TDataSource
    DataSet = DMStammdaten.QBetriebeAdressen
    Left = 744
    Top = 408
  end
  object DSBetriebeGemeinden: TDataSource
    DataSet = DMStammdaten.QBetriebeGemeinden
    Left = 744
    Top = 472
  end
  object DSGemeindesuche: TDataSource
    DataSet = DMStammdaten.QGemeindesuche
    Left = 960
    Top = 328
  end
  object QGemeindesuche: TFDQuery
    ActiveStoredUsage = []
    MasterSource = DSBetriebeAdressen
    MasterFields = 'PLZ'
    Connection = dm_main.FBC_MAIN
    FetchOptions.AssignedValues = [evMode, evRecordCountMode]
    FetchOptions.RecordCountMode = cmTotal
    SQL.Strings = (
      
        'select ID, GemeindeKennziffer, GemeindeName from Stammdaten.Geme' +
        'inden'
      'where amtsplz = :plz'
      'order by gemeindename')
    Left = 296
    Top = 40
    ParamData = <
      item
        Name = 'PLZ'
        DataType = ftString
        ParamType = ptInput
        Size = 7
        Value = Null
      end>
    object QGemeindesucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGemeindesucheGemeindeKennziffer: TIntegerField
      FieldName = 'GemeindeKennziffer'
      Origin = 'GemeindeKennziffer'
      Required = True
    end
    object QGemeindesucheGemeindeName: TStringField
      FieldName = 'GemeindeName'
      Origin = 'GemeindeName'
      Required = True
      Size = 80
    end
  end
end
