﻿unit Forms.Stammdaten;

interface

uses
  System.Classes, System.SysUtils, IWAppForm, IWApplication, IWColor, IWTypes,
  Vcl.Imaging.jpeg, IWVCLBaseControl, IWBaseControl, IWBaseHTMLControl,
  IWControl, IWCompExtCtrls, Vcl.Controls, Vcl.Forms, IWVCLBaseContainer,
  IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion, IWCGJQControl,
  IWCGJQRegion, IWCGJQTabs, IWCGJQButton, IWCGJQHTMLEditor,
  IWCompTabControl, IWCompLabel, IWCGJQLabel, IWBaseComponent,
  IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert,
  IWCGJQEdit, IdGlobal, IdHash, IdHashMessageDigest, Vcl.Graphics, IWCompEdit,
  IWCompGrids, IWDBGrids, IWCGJQGrid, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, Data.DB, IWCompListbox, IWCompCheckbox,
  IWDBStdCtrls, IWCGJQDatePicker, IWCGJQMessageDlg, dialogs, IWCGJQCommon,
  Forms.Base, IWCompText, IWCompMemo, IWCGJQComboBox,
  System.Generics.Collections, TierartenFrame,
  Modules.Stammdaten, IWCGJQDropDown, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Param, FireDAC.Stan.Error,
  FireDAC.DatS, FireDAC.Phys.Intf, FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, FireDAC.Comp.DataSet,
  FireDAC.Comp.Client, IWCGJQMemo, ELKE.Classes.Generated,
  JQ.Helpers.ComboboxEx;

type
  // TFormStammdaten = class(TFormBase)
  TFormStammdaten = class(TFormBase<TDMStammdaten>)
    iwtStammdaten: TIWTabControl;
    IWRegion5: TIWCGJQRegion;
    jqsaStammdaten: TIWCGJQSweetAlert;
    TabBetriebe: TIWTabPage;
    GridBetriebe: TIWCGJQGrid;
    IWRegion3: TIWCGJQRegion;
    EditBetriebSuche: TIWCGJQEdit;
    iwcgb_sucheBetrieb: TIWCGJQButton;
    ProviderBetriebe: TIWCGJQGridDataSetProvider;
    TabTierarten: TIWTabPage;
    TabTierartenkategorie: TIWTabPage;
    iwcggrid_Tierartkategorie: TIWCGJQGrid;
    IWRegion1: TIWCGJQRegion;
    iwl_TAkat_count: TIWCGJQLabel;
    iwcgb_suche_TAKateg: TIWCGJQButton;
    TabTaetigkeiten: TIWTabPage;
    iwcggrid_Taetigkeiten: TIWCGJQGrid;
    IWRegion2: TIWCGJQRegion;
    Edit_tat_kurzbez: TIWCGJQEdit;
    IWLabel1: TIWCGJQLabel;
    Edit_tat_bezeichnung: TIWCGJQEdit;
    IWLabel2: TIWCGJQLabel;
    iwl_tat_count: TIWCGJQLabel;
    iwcgb_suche_taetigkeit: TIWCGJQButton;
    DSTaetigkeiten: TDataSource;
    ProviderTaetigkeiten: TIWCGJQGridDataSetProvider;
    ds_TierKat: TDataSource;
    iwcgprov_TAkat: TIWCGJQGridDataSetProvider;
    Edit_TAkat_kategorie: TIWCGJQEdit;
    iwl_TAkateg_Kategorie: TIWCGJQLabel;
    TabTaetigkeitenBetriebstyp: TIWTabPage;
    iwcggrid_TaetigkeitBetrtyp: TIWCGJQGrid;
    IWRegion6: TIWCGJQRegion;
    Edit_Tatbetrtyp_taetigkeit: TIWCGJQEdit;
    IWLabel3: TIWCGJQLabel;
    Edit_tatbetrtyp_betriebstyp: TIWCGJQEdit;
    IWLabel4: TIWCGJQLabel;
    iwl_tatbetrtyp_count: TIWCGJQLabel;
    iwcgb_suche_taetigbetrtyp: TIWCGJQButton;
    iwcgprov_Taetigkeitenbetrtyp: TIWCGJQGridDataSetProvider;
    ds_TatBetrtyp: TDataSource;
    TabBetriebstypenTierart: TIWTabPage;
    iwcggrid_BetrtypTA: TIWCGJQGrid;
    IWRegion7: TIWCGJQRegion;
    Edit_betrtypta_betrtyp: TIWCGJQEdit;
    iwl_betrtypTA_Betrtyp: TIWCGJQLabel;
    Edit_betrtypTA_TA: TIWCGJQEdit;
    iwl_betrtypTA_ta: TIWCGJQLabel;
    iwl_BetrtypTA_count: TIWCGJQLabel;
    iwcgb_betrtyp_suche: TIWCGJQButton;
    ds_BetrtypTA: TDataSource;
    iwcgprov_BetrtypTA: TIWCGJQGridDataSetProvider;
    IWRegion9: TIWCGJQRegion;
    iwrTaetigkeitenBearbeiten: TIWCGJQRegion;
    Edit_Taetigkeit_Kurzbez: TIWCGJQEdit;
    Edit_Taetigkeit_Bezeichnung: TIWCGJQEdit;
    iwl_Taetigkeit_kurzbez: TIWCGJQLabel;
    iwl_taetigkeit_bezeichnung: TIWCGJQLabel;
    iwcb_Taetigkeit_aktiv: TIWCheckBox;
    iwcb_Taetigkeit_zulassungspfl: TIWCheckBox;
    iwcgb_Taetigkeit_abbrechen: TIWCGJQButton;
    iwcgb_Taetigkeit_aendern: TIWCGJQButton;
    iwcgb_Taetigkeit_neu: TIWCGJQButton;
    iwcgb_Taetigkeit_speichern: TIWCGJQButton;
    ds_Revisionsstamm: TDataSource;
    TabRevisionsstamm: TIWTabPage;
    iwre_sucheRevisionsStamm: TIWCGJQRegion;
    jqgRevisionsstamm: TIWCGJQGrid;
    ProvRevisionsstamm: TIWCGJQGridDataSetProvider;
    jqbSucheRevisionsstamm: TIWCGJQButton;
    iwrBetriebeBearbeiten: TIWCGJQRegion;
    Edit_betr_vergeb: TIWCGJQEdit;
    Edit_betr_name: TIWCGJQEdit;
    Edit_betr_gemnr: TIWCGJQEdit;
    Edit_betr_aufsicht: TIWCGJQEdit;
    Edit_betr_strasse: TIWCGJQEdit;
    iwl_betr_GemNr: TIWCGJQLabel;
    Edit_betr_email: TIWCGJQEdit;
    Edit_betr_telefon: TIWCGJQEdit;
    iwl_betr_katastralgem: TIWCGJQLabel;
    iwl_betr_Aufsicht: TIWCGJQLabel;
    iwl_Betr_Vegeb: TIWCGJQLabel;
    iwl_betr_Strass: TIWCGJQLabel;
    iwl_betr_PLZ: TIWCGJQLabel;
    iwl_betr_Ort: TIWCGJQLabel;
    iwl_betr_Telefon: TIWCGJQLabel;
    iwl_betr_EMail: TIWCGJQLabel;
    iwl_betr_name: TIWCGJQLabel;
    ButtonBetriebNeu: TIWCGJQButton;
    ButtonBetriebAendern: TIWCGJQButton;
    ButtonBetriebSpeichern: TIWCGJQButton;
    ButtonBetriebAbbrechen: TIWCGJQButton;
    iwrBetriebstypenTierartBearbeiten: TIWCGJQRegion;
    Edit_betrtTA_Betrtyp: TIWCGJQEdit;
    Edit_betrtTA_Tierart: TIWCGJQEdit;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    iwcgbtn_betrtTA_neu: TIWCGJQButton;
    iwcgbtn_betrtTA_aendern: TIWCGJQButton;
    iwcgbtn_betrtTA_speichern: TIWCGJQButton;
    iwcgbtn_betrtTA_abbrechen: TIWCGJQButton;
    Edit_betr_vulgo: TIWCGJQEdit;
    Edit_betr_bvbkz: TIWCGJQEdit;
    EditBetriebRegNr: TIWCGJQEdit;
    iwcgbtn_loesche_Betriebssuche: TIWCGJQButton;
    iwcgbtn_TAkat_suchebeenden: TIWCGJQButton;
    iwcgbtn_loesche_Taetigkeitssuche: TIWCGJQButton;
    iwcgbtn_loesche_BetrtypTatsuche: TIWCGJQButton;
    iwcgbtn_loesche_BetrtypTAsuche: TIWCGJQButton;
    iwl_betrieb_vulgo: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    iwl_betr_regnr: TIWCGJQLabel;
    iwmStammdaten: TIWModalWindow;
    jqbRevNeu: TIWCGJQButton;
    jqbRevAendern: TIWCGJQButton;
    jqbRevLoeschen: TIWCGJQButton;
    iwrModal: TIWCGJQRegion;
    iwrRevisionsstamm: TIWCGJQRegion;
    iwlSektion: TIWCGJQLabel;
    EditRevstammSektion: TIWCGJQEdit;
    iwlBetriebsgruppeLM: TIWCGJQLabel;
    EditRevstammBG: TIWCGJQEdit;
    iwlBetriebsgruppeDetail: TIWCGJQLabel;
    EditRevstammBGDetail: TIWCGJQEdit;
    iwlBetriebsart: TIWCGJQLabel;
    iwmRevstammBetriebsart: TIWMemo;
    TabRevisionsplan: TIWTabPage;
    iwrRevplanTop: TIWCGJQRegion;
    GridRevisionsplan: TIWCGJQGrid;
    ProviderRevisionsplan: TIWCGJQGridDataSetProvider;
    jqcbRevplanJahr: TIWCGJQComboBoxEx;
    IWLabel8: TIWCGJQLabel;
    jqbRevplanAbfragen: TIWCGJQButton;
    iwrRevplan: TIWCGJQRegion;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    EditRevplanRisikoKategorie: TIWCGJQEdit;
    EditRevplanMindestkontrollfrequenz: TIWCGJQEdit;
    jqbRevplanAbschliesen: TIWCGJQButton;
    jqbRevplanLoeschen: TIWCGJQButton;
    iwrRevisionsstammSuchen: TIWCGJQRegion;
    iwrRevisionsstammBearbeiten: TIWCGJQRegion;
    DSBetriebeSuche: TDataSource;
    DSBetriebeEdit: TDataSource;
    DSBetriebeAdressen: TDataSource;
    DSBetriebeGemeinden: TDataSource;
    EditBetriebOrt: TIWCGJQDropDown;
    DSGemeindesuche: TDataSource;
    QGemeindesuche: TFDQuery;
    QGemeindesucheID: TFDAutoIncField;
    QGemeindesucheGemeindeKennziffer: TIntegerField;
    QGemeindesucheGemeindeName: TStringField;
    EditBetriebKastralGemeinde: TIWCGJQEdit;
    EditBetriebPlz: TIWCGJQEdit;
    IWCGJQMemo1: TIWCGJQMemo;
    IWCGJQLabel1: TIWCGJQLabel;
    LabelCount: TIWLabel;
    procedure DSBetriebeEditStateChange(Sender: TObject);
    function getMd5HashString(value: String): String;
    procedure iwcgb_sucheBetriebJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwi_loesche_BetriebssucheClick(Sender: TObject);
    procedure iwtStammdatenChange(Sender: TObject);
    procedure IWCGJQButton3JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwi_loesche_TaetigkeitssucheClick(Sender: TObject);
    procedure iwcgb_suche_TAKategJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwi_TAkat_suchebeendenClick(Sender: TObject);
    procedure iwi_Loesche_TierartsucheClick(Sender: TObject);
    procedure iwcgb_suche_taetigbetrtypJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgb_betrtyp_sucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwi_loesche_BetrtypTatsucheClick(Sender: TObject);
    procedure iwi_loesche_BetrtypTAsucheClick(Sender: TObject);
    procedure iwcggrid_TaetigkeitenJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
    procedure iwcgb_Taetigkeit_neuJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgb_Taetigkeit_speichernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgb_Taetigkeit_aendernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgb_Taetigkeit_abbrechenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure jqbSucheRevisionsstammOnClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBetriebNeuClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBetriebAbbrechenClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_betrtTA_neuJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_betrtTA_speichernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcggrid_BetrtypTAJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_Loesche_TierartsucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_loesche_BetriebssucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_TAkat_suchebeendenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_loesche_TaetigkeitssucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_loesche_BetrtypTatsucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure iwcgbtn_loesche_BetrtypTAsucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure GridBetriebeOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure ButtonBetriebSpeichernClick(Sender: TObject; AParams: TStringList);
    procedure ButtonBetriebAendernClick(Sender: TObject; AParams: TStringList);
    procedure IWAppFormCreate(Sender: TObject);
    procedure iwcgb_zurueckJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevNeuOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevAendernOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevplanAbfragenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevplanAbschliessenOnClick(Sender: TObject; AParams: TStringList);
    procedure jqbRevplanLoeschenOnClick(Sender: TObject; AParams: TStringList);
    procedure GridRevisionsplanOnSelectRow(Sender: TObject; AParams: TStringList);
    procedure GridBetriebeJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
  private
    aktiveTabs: TList<String>;

    aendereTaetigkeit, neueTaetigkeit: Boolean;
    neuBetrTypenTA: Boolean;
    idtaetigkeit: Integer;
    idbetrtypTA: String;

    // Revplan
    revPlanJahr: SmallInt;
    revplanBearbeitenErlaubt: Boolean;

    // Frames
    TierartenFrame: TTierarten;

    // Procedures
    procedure Betriebe;
    procedure Tierarten;
    procedure Tierartkategorie;
    procedure Taetigkeiten;
    procedure BetrtypTat;
    procedure BetrtypTA;
    procedure InitRevisionsplan;
    procedure TaetigkeitEinblenden;
    procedure TaetigkeitenAusblenden;
    procedure revstammAbfragen;
    procedure RevisionsstammNeu(Sender: TObject; EventParams: TStringList);
    procedure RevisionsstammAendern(Sender: TObject; EventParams: TStringList);
    procedure revstammLoeschenConfirmed(Sender: TObject; AParams: TStringList);
    procedure resetRevstammModal;
    procedure revplanErstellen(Sender: TObject; AParams: TStringList);
    procedure revplanAbfragen;
    procedure revplanAendern(Sender: TObject; EventParams: TStringList);
    procedure revplanAbschliessen(Sender: TObject; AParams: TStringList);
    procedure ungeplanteKontrolleAnlegen(ABerichtstypen: TList<TBkbTyp>; AGruppe: TGruppe; ABetriebID: Integer;
      AKontrolltyp, ABkbtyp: String; AFrequenz: Integer);
    procedure revplanLoeschenConfirmed(Sender: TObject; AParams: TStringList);
    procedure resetRevplanModal;

    procedure UpdateButtons;
    procedure FunktionenKonfigurieren;
  public
    destructor Destroy; override;
  end;

implementation

uses dmmain, Funktionen, ELKE.Services.Me.Intf, DX.Classes.ObjectFactory,
  ServerController, StrUtils, ELKE.Classes.RESTError, JQ.Helpers.Grid, ELKE.Classes, ELKE.Server.Logger, System.Math;

{$R *.dfm}

// ******************************************************************************
// ********************************Allgemein*************************************
// ******************************************************************************

{ Aufgerufen sobald das Fenster erzeugt wurde }
procedure TFormStammdaten.IWAppFormCreate(Sender: TObject);
var
  i: Word;
begin
  // Provider/Datasource setup
  DSBetriebeSuche.DataSet := DM.QBetriebeSuche;
  ProviderBetriebe.DataSource := DSBetriebeSuche;

  DSTaetigkeiten.DataSet := DM.QTaetigkeiten;
  ProviderTaetigkeiten.DataSource := DSTaetigkeiten;

  DSBetriebeEdit.DataSet := DM.QBetriebeEdit;
  DSBetriebeAdressen.DataSet := DM.QBetriebeAdressen;
  DSBetriebeGemeinden.DataSet := DM.QBetriebeGemeinden;

  DSGemeindesuche.DataSet := DM.QGemeindesuche;

  iwtStammdaten.ActivePage := 0;

  for i := 2019 to CurrentYear + 1 do
  begin
    jqcbRevplanJahr.Items.Add(IntToStr(i));
  end;

  TierartenFrame := TTierarten.Create(Self, jqsaStammdaten, false);
  TierartenFrame.Parent := TabTierarten;

  FunktionenKonfigurieren;

  // Initiale Tab-Konfiguration
  iwtStammdatenChange(Self);

  UpdateButtons;

  jqgRevisionsstamm.SetupDefaults(ProvRevisionsstamm);
  jqgRevisionsstamm.JQGridOptions.SelectTopRowOnLoad := true;

  GridRevisionsplan.SetupDefaults(ProviderRevisionsplan);
end;

destructor TFormStammdaten.Destroy;
begin
  aktiveTabs.Free;
  inherited;
end;

procedure TFormStammdaten.FunktionenKonfigurieren;
var
  Funktionen: TFunktionenManager;
begin
  Funktionen := Usersession.FunktionenManager;
  aktiveTabs := TList<String>.Create;

  // Anzeigen
  if Funktionen.HatFunktion(Betriebsübersicht_anzeigen) then
  begin
    TabBetriebe.Visible := true;
    aktiveTabs.Add('Betriebe');
  end;
  (* vorläufig hart deaktiviert
    if Funktionen.HatFunktion(Tierarten_anzeigen) then
    begin
    TabTierarten.Visible := true;
    aktiveTabs.Add('Tierarten');
    end;
    if Funktionen.HatFunktion(Tierartenkategorien_anzeigen) then
    begin
    TabTierartenkategorie.Visible := true;
    aktiveTabs.Add('Tierartenkategorie');
    end;
    if Funktionen.HatFunktion(Tätigkeiten_anzeigen) then
    begin
    TabTaetigkeiten.Visible := true;
    aktiveTabs.Add('Tätigkeiten');
    end;
    if Funktionen.HatFunktion(Tätigkeitenbetriebstypen_anzeigen) then
    begin
    TabTaetigkeitenBetriebstyp.Visible := true;
    aktiveTabs.Add('Tätigkeiten-betriebstyp');
    end;
    if Funktionen.HatFunktion(Betriebstypen_Tierart_anzeigen) then
    begin
    TabBetriebstypenTierart.Visible := true;
    aktiveTabs.Add('Betriebstyp-Tierart');
    end;
  *)
  if Funktionen.HatFunktion(Revisionsstamm_anzeigen) then
  begin
    TabRevisionsstamm.Visible := true;
    aktiveTabs.Add('');
  end;
  if Funktionen.HatFunktion(Revisionsplan_anzeigen) then
  begin
    TabRevisionsplan.Visible := true;
    aktiveTabs.Add('Revisionsplan');
  end;

  // Bearbeiten
  if not Funktionen.HatFunktion(Betriebe_bearbeiten) then
    iwrBetriebeBearbeiten.Visible := false;
  if Funktionen.HatFunktion(Tierarten_bearbeiten) then
    TierartenFrame.BearbeitenErlauben;
  if not Funktionen.HatFunktion(Tätigkeiten_bearbeiten) then
    iwrTaetigkeitenBearbeiten.Visible := false;
  if not Funktionen.HatFunktion(Betriebstypen_Tierart_bearbeiten) then
    iwrBetriebstypenTierartBearbeiten.Visible := false;
  if not Funktionen.HatFunktion(Revisionsstamm_bearbeiten) then
    iwrRevisionsstammBearbeiten.Visible := false;
  revplanBearbeitenErlaubt := Funktionen.HatFunktion(Revisionsplan_bearbeiten);
  if not revplanBearbeitenErlaubt then
  begin
    jqbRevplanAbschliesen.Visible := false;
    jqbRevplanLoeschen.Visible := false;
  end;
end;

{ Aufgerufen wenn der Benutzer den Tab wechselt. }
procedure TFormStammdaten.iwtStammdatenChange(Sender: TObject);
begin
  case IndexText(aktiveTabs[iwtStammdaten.ActivePage],
    ['Betriebe', 'Tierarten', 'Tierartenkategorie',
    'Tätigkeiten', 'Tätigkeiten-betriebstyp', 'Betriebstyp-Tierart',
    'Revisionsplan']) of
    0:
      Betriebe;
    1:
      Tierarten;
    2:
      Tierartkategorie;
    3:
      Taetigkeiten;
    4:
      BetrtypTat;
    5:
      BetrtypTA;
    6:
      InitRevisionsplan;
  end;
end;

procedure TFormStammdaten.Betriebe;
begin
  EditBetriebSuche.Text := '';
  DM.QBetriebeSuche.Close;
end;

procedure TFormStammdaten.Tierarten;
begin
  TierartenFrame.TierartenAbfragen;
end;

procedure TFormStammdaten.Tierartkategorie;
begin
  Edit_TAkat_kategorie.Text := '';
  dm_main.qu_StDat_TAKat.Active := false;
  iwl_TAkat_count.Caption := '-';
end;

procedure TFormStammdaten.UpdateButtons;
begin
  ButtonBetriebNeu.Enabled := DSBetriebeSuche.DataSet.Active and not(DSBetriebeEdit.State in dsEditModes);
  ButtonBetriebAendern.Enabled := DSBetriebeSuche.DataSet.Active and not(DSBetriebeEdit.State in dsEditModes);
  ButtonBetriebSpeichern.Enabled := DSBetriebeSuche.DataSet.Active and (DSBetriebeEdit.State in dsEditModes);
  ButtonBetriebAbbrechen.Enabled := DSBetriebeSuche.DataSet.Active and (DSBetriebeEdit.State in dsEditModes);
end;

procedure TFormStammdaten.Taetigkeiten;
begin
  Edit_tat_kurzbez.Text := '';
  Edit_tat_bezeichnung.Text := '';
  DM.QTaetigkeiten.Active := false;
  iwl_tat_count.Caption := '-';
end;

procedure TFormStammdaten.BetrtypTat;
begin
  Edit_Tatbetrtyp_taetigkeit.Text := '';
  Edit_tatbetrtyp_betriebstyp.Text := '';
  dm_main.qu_StDat_TaetBetrtyp.Active := false;
  iwl_tatbetrtyp_count.Caption := '-';
end;

procedure TFormStammdaten.BetrtypTA;
begin
  Edit_betrtypta_betrtyp.Text := '';
  Edit_betrtypTA_TA.Text := '';
  dm_main.qu_StDat_BetrtypTA.Active := false;
end;

procedure TFormStammdaten.DSBetriebeEditStateChange(Sender: TObject);
begin
  UpdateButtons;
end;

procedure TFormStammdaten.InitRevisionsplan;
begin
  revPlanJahr := -1;
end;

// ******************************************************************************
// ****************************************************************
// ******************************************************************************

procedure TFormStammdaten.iwcgbtn_betrtTA_neuJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  iwcgbtn_betrtTA_speichern.Visible := true;
  iwcgbtn_betrtTA_abbrechen.Visible := true;
  Edit_betrtTA_Betrtyp.Editable := true;
  Edit_betrtTA_Tierart.Editable := true;
  neuBetrTypenTA := true;
end;

procedure TFormStammdaten.iwcgbtn_betrtTA_speichernJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  if neuBetrTypenTA = true then
  begin
    dm_main.qu_Ins_Stammdaten.SQL.Text := '';
    dm_main.qu_Ins_Stammdaten.SQL.Add('INSERT INTO Stammdaten.Betriebstypen_tierart(Betriebstyp, TIERART)');
    dm_main.qu_Ins_Stammdaten.SQL.Add('VALUES(' + #39 + Edit_betrtTA_Betrtyp.Text + #39 + ', ' + #39 +
      Edit_betrtTA_Tierart.Text + #39 + ')');
    dm_main.qu_Ins_Stammdaten.ExecSQL;
  end
  else
  begin
    dm_main.qu_Ins_Stammdaten.SQL.Text := '';
    dm_main.qu_Ins_Stammdaten.SQL.Add('UPDATE Stammdaten.Betriebstypen_tierart');
    dm_main.qu_Ins_Stammdaten.SQL.Add('SET Betriebstyp=' + #39 + Edit_betrtTA_Betrtyp.Text + #39 + ', ' + #39 +
      Edit_betrtTA_Tierart.Text + #39);
    dm_main.qu_Ins_Stammdaten.SQL.Add('WHERE Betriebstyp=' + #39 + idbetrtypTA + #39);
    dm_main.qu_Ins_Stammdaten.ExecSQL;
  end;
end;

procedure TFormStammdaten.ButtonBetriebAbbrechenClick(Sender: TObject; AParams: TStringList);
begin
  DM.BetriebeCancel
end;

procedure TFormStammdaten.ButtonBetriebAendernClick(Sender: TObject; AParams: TStringList);
begin
  DM.BetriebeEdit;
end;

procedure TFormStammdaten.ButtonBetriebNeuClick(Sender: TObject; AParams: TStringList);
begin
  GridBetriebe.JQGridOptions.SelRow := '';
  DM.BetriebeNew;
end;

procedure TFormStammdaten.ButtonBetriebSpeichernClick(Sender: TObject; AParams: TStringList);
begin
  var
  LBookmark := GridBetriebe.JQGridOptions.SelRow;
  DM.BetriebeSave;
  GridBetriebe.JQGridOptions.ReloadGrid(true);
  GridBetriebe.JQGridOptions.SelRow := LBookmark;
end;

procedure TFormStammdaten.iwcgbtn_loesche_BetriebssucheJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  Betriebe;
end;

procedure TFormStammdaten.iwcgbtn_loesche_BetrtypTAsucheJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  BetrtypTA;
end;

procedure TFormStammdaten.iwcgbtn_loesche_BetrtypTatsucheJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  BetrtypTat;
end;

procedure TFormStammdaten.iwcgbtn_loesche_TaetigkeitssucheJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  Taetigkeiten;
end;

procedure TFormStammdaten.iwcgbtn_Loesche_TierartsucheJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  Tierarten;
end;

procedure TFormStammdaten.iwcgbtn_TAkat_suchebeendenJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
begin
  Tierartkategorie;
end;

procedure TFormStammdaten.iwcgb_betrtyp_sucheJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  try
    dm_main.qu_StDat_BetrtypTA.Close;

    // Todo: reimplement
    {
      dm_main.qu_StDat_BetrtypTA.SQL.Text := '';
      SQL := 'SELECT Top 200 * from stammdaten.betriebstypen_tierart where ';
      SQL := SQL + 'upper(Betriebstyp) like ' + '''%' + UpperCase(trim(Edit_betrtypta_betrtyp.Text)) + '%''  ';
      if trim(Edit_betrtypTA_TA.Text) <> '' then
      SQL := SQL + 'and upper(tierart) like ' + '''%' + UpperCase(trim(Edit_betrtypTA_TA.Text)) + '%''  ';
      dm_main.qu_StDat_BetrtypTA.SQL.Add(SQL);
      dm_main.qu_StDat_BetrtypTA.Active := true;
    }
  except
    jqsaStammdaten.Error('Fehler bei SQL-Abfrage!',
      'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
  end;
end;

procedure TFormStammdaten.iwcgb_sucheBetriebJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  try
    DM.BetriebeSuchen(EditBetriebSuche.Text);
  except
    jqsaStammdaten.Error('Fehler bei Datenabfrage!', 'Bitte probieren Sie es erneut!');
    abort;
  end;
end;

procedure TFormStammdaten.iwcgb_suche_taetigbetrtypJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  try
    dm_main.qu_StDat_TaetBetrtyp.Close;
    // Todo: reimplement
    {
      dm_main.qu_StDat_TaetBetrtyp.SQL.Text := '';
      SQL := 'SELECT Top 200 t.bezeichnung, b.betriebstyp from stammdaten.taetig_betrtyp b left join stammdaten.taetigkeiten t on b.id_taetigkeit = t.id where ';
      SQL := SQL + 'upper(t.bezeichnung) like ' + '''%' + UpperCase(trim(Edit_Tatbetrtyp_taetigkeit.Text)) + '%''  ';
      if trim(Edit_tatbetrtyp_betriebstyp.Text) <> '' then
      SQL := SQL + 'and upper(b.betriebstyp) like ' + '''%' + UpperCase(trim(Edit_tatbetrtyp_betriebstyp.Text))
      + '%''  ';
      SQL := SQL + ' and t.Bldcode = ' + IntToStr(dm_main.BLDCODE);
      dm_main.qu_StDat_TaetBetrtyp.SQL.Add(SQL);
      dm_main.qu_StDat_TaetBetrtyp.Active := true;
      iwl_tatbetrtyp_count.Caption := IntToStr(dm_main.qu_StDat_TaetBetrtyp.RecordCount);
    }
  except
    jqsaStammdaten.Error('Fehler bei SQL-Abfrage!',
      'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
  end;
end;

procedure TFormStammdaten.iwcgb_suche_TAKategJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  try
    dm_main.qu_StDat_TAKat.Close;
    // Todo: reimplement
    {
      dm_main.qu_StDat_TAKat.SQL.Text := '';
      SQL := 'Select k.vis_kateg, t.KZ, t.bezeichnung, t.vis_tierart from stammdaten.tierarten_kateg k left join stammdaten.tierarten t on k.id_tierart = t.id where ';
      SQL := SQL + 'upper(vis_kateg) like ' + '''%' + UpperCase(trim(Edit_TAkat_kategorie.Text)) + '%''  ';
      SQL := SQL + ' and t.Bldcode = ' + IntToStr(dm_main.BLDCODE);
      dm_main.qu_StDat_TAKat.SQL.Add(SQL);
      dm_main.qu_StDat_TAKat.Active := true;
      iwl_TAkat_count.Caption := IntToStr(dm_main.qu_StDat_TAKat.RecordCount);
    }
  except
    jqsaStammdaten.Error('Fehler bei SQL-Abfrage!',
      'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
  end;
end;

procedure TFormStammdaten.iwcgb_Taetigkeit_abbrechenJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  neueTaetigkeit := false;
  aendereTaetigkeit := false;
  Edit_Taetigkeit_Kurzbez.Text := '';
  Edit_Taetigkeit_Bezeichnung.Text := '';
  iwcb_Taetigkeit_aktiv.Checked := false;
  iwcb_Taetigkeit_zulassungspfl.Checked := false;
  iwcgb_Taetigkeit_speichern.Visible := false;
  iwcgb_Taetigkeit_abbrechen.Visible := false;
  iwcgb_Taetigkeit_neu.Visible := true;
  iwcgb_Taetigkeit_aendern.Visible := true;

  Edit_Taetigkeit_Kurzbez.Editable := false;
  Edit_Taetigkeit_Bezeichnung.Editable := false;
  iwcb_Taetigkeit_aktiv.Editable := false;
  iwcb_Taetigkeit_zulassungspfl.Editable := false;
  TaetigkeitenAusblenden;
end;

procedure TFormStammdaten.iwcgb_Taetigkeit_aendernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  aendereTaetigkeit := true;
  neueTaetigkeit := false;
  iwcgb_Taetigkeit_speichern.Visible := true;
  iwcgb_Taetigkeit_abbrechen.Visible := true;
  iwcgb_Taetigkeit_neu.Visible := false;
  iwcgb_Taetigkeit_aendern.Visible := false;

  Edit_Taetigkeit_Kurzbez.Editable := true;
  Edit_Taetigkeit_Bezeichnung.Editable := true;
  iwcb_Taetigkeit_aktiv.Editable := true;
  iwcb_Taetigkeit_zulassungspfl.Editable := true;
  TaetigkeitEinblenden;
end;

procedure TFormStammdaten.iwcgb_Taetigkeit_neuJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  neueTaetigkeit := true;
  aendereTaetigkeit := false;
  Edit_Taetigkeit_Kurzbez.Text := '';
  Edit_Taetigkeit_Bezeichnung.Text := '';
  iwcb_Taetigkeit_aktiv.Checked := false;
  iwcb_Taetigkeit_zulassungspfl.Checked := false;

  iwcgb_Taetigkeit_speichern.Visible := true;
  iwcgb_Taetigkeit_abbrechen.Visible := true;
  iwcgb_Taetigkeit_neu.Visible := false;
  iwcgb_Taetigkeit_aendern.Visible := false;

  Edit_Taetigkeit_Kurzbez.Editable := true;
  Edit_Taetigkeit_Bezeichnung.Editable := true;
  iwcb_Taetigkeit_aktiv.Editable := true;
  iwcb_Taetigkeit_zulassungspfl.Editable := true;
end;

procedure TFormStammdaten.iwcgb_Taetigkeit_speichernJQButtonOptionsClick(Sender: TObject; AParams: TStringList);
var
  kurzbez, bez, SQL: String;
  aktiv, zulassungspfl: Boolean;
  akt, zul: Integer;
begin
  aktiv := false;
  try
    kurzbez := Edit_Taetigkeit_Kurzbez.Text;
    bez := Edit_Taetigkeit_Bezeichnung.Text;
    Edit_Taetigkeit_Bezeichnung.Editable := true;
    Edit_Taetigkeit_Bezeichnung.Editable := true;
    zulassungspfl := iwcb_Taetigkeit_zulassungspfl.Checked;
    akt := 0;
    zul := 0;
    if aktiv = true then
      akt := 1;
    if zulassungspfl = true then
      zul := 1;
    if neueTaetigkeit = true then
    begin
      SQL := 'INSERT INTO TAETIGKEITEN(KURZ_BEZ,BEZEICHNUNG,ZULASSUNGSPFL,BLDCODE,AKTIV)';

      SQL := SQL + 'VALUES(' + #39 + kurzbez + #39 + ',' + #39 + bez + #39 + ',' + #39 + IntToStr(zul) + #39 + ', ' +
        IntToStr(dm_main.BLDCODE) + ', ' + #39 + IntToStr(akt) + #39 + ',' + ')';

    end;
    if aendereTaetigkeit = true then
    begin
      SQL := 'UPDATE TAETIGKEITEN ';
      SQL := SQL + 'SET bezeichnung=' + #39 + bez + #39 + ',kurz_bez=' + #39 + kurzbez + #39 + ',BLDCODE=' +
        IntToStr(dm_main.BLDCODE);
      SQL := SQL + ' where id = ' + IntToStr(idtaetigkeit);
      dm_main.qu_Ins_Stammdaten.SQL.Text := '';
      dm_main.qu_Ins_Stammdaten.SQL.Add(SQL);
      dm_main.qu_Ins_Stammdaten.ExecSQL;
    end;
    DM.QTaetigkeiten.Close;
    // dm_main.qu_Ins_Stammdaten.SQL.Text := '';
    // dm_main.qu_Ins_Stammdaten.SQL.Add(sql);
    DM.QTaetigkeiten.Active := true;
    // dm_main.qu_Ins_Stammdaten.ExecSQL;

    neueTaetigkeit := false;
    Edit_Taetigkeit_Kurzbez.Text := '';
    Edit_Taetigkeit_Bezeichnung.Text := '';
    iwcb_Taetigkeit_aktiv.Checked := false;
    iwcb_Taetigkeit_zulassungspfl.Checked := false;

    iwcgb_Taetigkeit_speichern.Visible := false;
    iwcgb_Taetigkeit_abbrechen.Visible := false;
    iwcgb_Taetigkeit_neu.Visible := true;
    iwcgb_Taetigkeit_aendern.Visible := true;

    Edit_Taetigkeit_Kurzbez.Editable := false;
    Edit_Taetigkeit_Bezeichnung.Editable := false;
    iwcb_Taetigkeit_aktiv.Editable := false;
    iwcb_Taetigkeit_zulassungspfl.Editable := false;
    TaetigkeitenAusblenden;
  except
    jqsaStammdaten.Error('SQL Error, Operation fehlgeschlagen',
      'Bitte versuchen Sie es erneut oder starten Sie eine neue Sitzung!');
  end;
end;

procedure TFormStammdaten.iwcgb_zurueckJQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
  Free;
end;

procedure TFormStammdaten.iwcggrid_BetrtypTAJQGridOptionsSelectRow(
  Sender: TObject; AParams: TStringList);
var
  index, i: Integer;
begin
  Edit_betrtTA_Betrtyp.Text := '';
  Edit_betrtTA_Tierart.Text := '';
  index := strtoint(AParams.Values['rowid']);
  DM.QTaetigkeiten.First;
  if index > 0 then
  begin
    iwcgbtn_betrtTA_aendern.Enabled := true;
  end
  else
  begin
    iwcgbtn_betrtTA_aendern.Enabled := false;
  end;
  for i := 0 to index - 1 do
  begin
    if i = index - 1 then
    begin
      Edit_Taetigkeit_Kurzbez.Text := dm_main.qu_StDat_BetrtypTA.FieldByName('Betriebstyp').AsString;
      Edit_Taetigkeit_Bezeichnung.Text := dm_main.qu_StDat_BetrtypTA.FieldByName('Tierart').AsString;
      idbetrtypTA := dm_main.qu_StDat_BetrtypTA.FieldByName('Betriebstyp').AsString;
    end;
    dm_main.qu_StDat_BetrtypTA.Next;
    iwcgbtn_betrtTA_aendern.Enabled := true;
    iwcgbtn_betrtTA_speichern.Enabled := false;
    iwcgbtn_betrtTA_abbrechen.Enabled := false;
  end;
end;

procedure TFormStammdaten.GridBetriebeJQGridOptionsLoadComplete(Sender: TObject; AParams: TStringList);
begin
  inherited;
  LabelCount.Caption := GridBetriebe.JQGridOptions.Records.ToString + ' Ergebnisse gefunden.';
end;

procedure TFormStammdaten.GridBetriebeOnSelectRow(Sender: TObject; AParams: TStringList);
var
  LSelectedID: Integer;
begin
  if DM.QBetriebeSuche.Active then
  begin
    LSelectedID := strtoint(AParams.Values['rowid']);
    DM.QBetriebeSuche.Locate('ID', LSelectedID);
  end;
end;

procedure TFormStammdaten.iwcggrid_TaetigkeitenJQGridOptionsSelectRow(Sender: TObject; AParams: TStringList);
var
  index, i: Integer;
begin
  index := strtoint(AParams.Values['rowid']);
  DM.QTaetigkeiten.First;
  if index > 0 then
  begin
    iwcgb_Taetigkeit_aendern.Enabled := true;
    TaetigkeitEinblenden;
  end
  else
  begin
    iwcgb_Taetigkeit_aendern.Enabled := false;
  end;
  for i := 0 to index - 1 do
  begin
    if i = index - 1 then
    begin
      idtaetigkeit := DM.QTaetigkeiten.FieldByName('id').AsInteger;
      Edit_Taetigkeit_Kurzbez.Text := DM.QTaetigkeiten.FieldByName('kurz_bez').AsString;
      Edit_Taetigkeit_Bezeichnung.Text := DM.QTaetigkeiten.FieldByName('bezeichnung').AsString;
      if DM.QTaetigkeiten.FieldByName('aktiv').AsInteger > 0 then
      begin
        iwcb_Taetigkeit_aktiv.Checked := true
      end
      else
      begin
        iwcb_Taetigkeit_aktiv.Checked := false;
      end;
      if DM.QTaetigkeiten.FieldByName('zulassungspfl').AsInteger > 0 then
      begin
        iwcb_Taetigkeit_zulassungspfl.Checked := true;
      end
      else
      begin
        iwcb_Taetigkeit_zulassungspfl.Checked := false;
      end;
    end;
    DM.QTaetigkeiten.Next;
  end;
end;

procedure TFormStammdaten.TaetigkeitEinblenden;
begin
  Edit_Taetigkeit_Bezeichnung.Visible := true;
  iwl_taetigkeit_bezeichnung.Visible := true;
  Edit_Taetigkeit_Kurzbez.Visible := true;
  iwl_Taetigkeit_kurzbez.Visible := true;
  iwcb_Taetigkeit_aktiv.Visible := true;
  iwcb_Taetigkeit_zulassungspfl.Visible := true;
end;

procedure TFormStammdaten.TaetigkeitenAusblenden;
begin
  Edit_Taetigkeit_Bezeichnung.Visible := false;
  iwl_taetigkeit_bezeichnung.Visible := false;
  Edit_Taetigkeit_Kurzbez.Visible := false;
  iwl_Taetigkeit_kurzbez.Visible := false;
  iwcb_Taetigkeit_aktiv.Visible := false;
  iwcb_Taetigkeit_zulassungspfl.Visible := false;
end;

procedure TFormStammdaten.IWCGJQButton3JQButtonOptionsClick(Sender: TObject; AParams: TStringList);
begin
  DM.QTaetigkeiten.Close;
  // Todo: Reimplement
  {
    DM.QTaetigkeiten.SQL.Text := '';
    SQL := 'SELECT Top 200 * from stammdaten.taetigkeiten where ';
    SQL := SQL + 'upper(bezeichnung) like ' + '''%' + UpperCase(trim(Edit_tat_kurzbez.Text)) + '%''  ';
    // Todo: Betrieb Regnr?
    // if trim(Edit_Betriebe_regnr.Text) <> '' then
    // SQL := SQL + 'and upper(bezeichnung) like ' + '''%' + UpperCase(trim(Edit_tat_bezeichnung.Text)) + '%''  ';
    SQL := SQL + ' and Bldcode = ' + IntToStr(dm_main.BLDCODE);
    DM.QTaetigkeiten.SQL.Add(SQL);
    DM.QTaetigkeiten.Active := true;
  }
end;

procedure TFormStammdaten.iwi_loesche_BetriebssucheClick(Sender: TObject);
begin
  Betriebe;
end;

procedure TFormStammdaten.iwi_loesche_BetrtypTAsucheClick(Sender: TObject);
begin
  BetrtypTA;
end;

procedure TFormStammdaten.iwi_loesche_BetrtypTatsucheClick(Sender: TObject);
begin
  BetrtypTat;
end;

procedure TFormStammdaten.iwi_loesche_TaetigkeitssucheClick(Sender: TObject);
begin
  Taetigkeiten;
end;

procedure TFormStammdaten.iwi_Loesche_TierartsucheClick(Sender: TObject);
begin
  Tierarten;
end;

procedure TFormStammdaten.iwi_TAkat_suchebeendenClick(Sender: TObject);
begin
  Tierartkategorie;
end;

function TFormStammdaten.getMd5HashString(value: string): string;
var
  hashMessageDigest5: TIdHashMessageDigest5;
begin
  hashMessageDigest5 := nil;
  try
    hashMessageDigest5 := TIdHashMessageDigest5.Create;
    result := UpperCase(IdGlobal.IndyLowerCase(hashMessageDigest5.HashStringAsHex(value)));
  finally
    hashMessageDigest5.Free;
  end;
end;

// ******************************************************************************
// ************************Revisionsstamm****************************************
// ******************************************************************************

procedure TFormStammdaten.jqbSucheRevisionsstammOnClick(Sender: TObject; AParams: TStringList);
begin
  revstammAbfragen;
end;

{ Schickt eine Query an die DB in der alle relevanten revisionsstämme mit dem
  BLDCode des Users angezeigt werden. }
procedure TFormStammdaten.revstammAbfragen;
begin
  try
    dm_main.qu_StDat_RevisionsStamm.Close;
    dm_main.qu_StDat_RevisionsStamm.Prepare;
    dm_main.qu_StDat_RevisionsStamm.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
    dm_main.qu_StDat_RevisionsStamm.Active := true;
  except
    jqsaStammdaten.Error('Fehler bei SQL-Abfrage!',
      'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
  end;
end;

{ Öffnet ein leeres Modal Window damit der User einen neuen Revisionsstamm
  hinzufügen kann. }
procedure TFormStammdaten.jqbRevNeuOnClick(Sender: TObject;
  AParams: TStringList);
begin
  resetRevstammModal;
  with iwmStammdaten do
  begin
    Title := 'Neuer Revisionsstamm';
    ContentElement := iwrRevisionsstamm;
    Buttons.CommaText := '&Erstellen,&Abbrechen';
    OnAsyncClick := RevisionsstammNeu;
    CloseOnEscKey := false;
    CloseOnClick := false;
    ContentElement.Visible := true;
    ContentElement.Enabled := true;
    Show;
  end;
end;

{ Button mit dem der User einen Eintrag ändern kann. Füllt das Modal Window mit
  den vorhandenen Daten aus und öffnet es. }
procedure TFormStammdaten.jqbRevAendernOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  jqgRevisionsstamm.SelectRecordFromCurrentRow;
  resetRevstammModal;
  query := dm_main.qu_StDat_RevisionsStamm;
  EditRevstammSektion.Text := query.FieldByName('Sektion').AsString;
  EditRevstammBG.Text := query.FieldByName('Betriebsgruppe_Lm').AsString;
  EditRevstammBGDetail.Text := query.FieldByName('Betriebsgruppe_Detail').AsString;
  iwmRevstammBetriebsart.Text := query.FieldByName('Betriebsart').AsString;

  with iwmStammdaten do
  begin
    Title := 'Revisionsstamm ändern';
    ContentElement := iwrRevisionsstamm;
    iwrRevisionsstamm.Visible := true;
    Buttons.CommaText := '&Ändern,&Abbrechen';
    OnAsyncClick := RevisionsstammAendern;
    CloseOnEscKey := false;
    CloseOnClick := false;
    Show;
  end;
end;

{ Fragt den User mit einem Alert Window ob er wirklich löschen möchte. }
procedure TFormStammdaten.jqbRevLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
begin
  jqgRevisionsstamm.SelectRecordFromCurrentRow;
  jqsaStammdaten.JQSweetAlertOptions.Title := 'Wollen Sie den Revisionsstamm löschen?';
  jqsaStammdaten.JQSweetAlertOptions.AlertType := jqsatWarning;
  jqsaStammdaten.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaStammdaten.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  jqsaStammdaten.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaStammdaten.JQSweetAlertOptions.OnBtnClick.OnEvent := revstammLoeschenConfirmed;
  jqsaStammdaten.Show;
end;

{ Überprüft, ob der User das Löschen Bestätigt hat und löscht dann das ausgewählte
  Objekt aus der DB. }
procedure TFormStammdaten.revstammLoeschenConfirmed(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  id: Integer;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    abort;
  end;
  try
    id := dm_main.qu_StDat_RevisionsStamm.FieldByName('ID').AsInteger;
    query := dm_main.qu_stdat_revisionsstamm_loeschen;
    query.Close;
    query.ParamByName('Id').AsInteger := id;
    query.Execute;
  except
    on E: Exception do
    begin
      ELKELogError('Revisionsstamm kann nicht gelöscht werden: ' + E.Message);
      raise Exception.Create('Der Revisionsstamm kann nicht gelöscht werden!');
    end;
  end;
  revstammAbfragen;
end;

{ Wird aufgerufen, wenn das Ändern Modal geschlossen wird. Überprüft zuerst, ob
  die Rückmeldung positiv ist und erstellt dann einen neuen Eintrag in der DB. }
procedure TFormStammdaten.RevisionsstammNeu(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
begin
  if iwmStammdaten.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  query := dm_main.qu_stdat_revstamm_ins;
  query.Close;
  query.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  query.ParamByName('Sektion').AsString := EditRevstammSektion.Text;
  query.ParamByName('Betriebsgruppe_Lm').AsString := EditRevstammBG.Text;
  query.ParamByName('Betriebsgruppe_Detail').AsString := EditRevstammBGDetail.Text;
  query.ParamByName('Betriebsart').AsString := iwmRevstammBetriebsart.Text;
  query.Execute;
  revstammAbfragen;
end;

{ Wird aufgerufen, wenn das Ändern Modal geschlossen wird. Überprüft zuerst, ob
  die Rückmeldung positiv ist und schickt dann die Änderungen an die DB und ladet
  neu. }
procedure TFormStammdaten.RevisionsstammAendern(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
  id: Integer;
begin
  if iwmStammdaten.ButtonIndex <> 1 then
  begin
    Exit;
  end;

  id := dm_main.qu_StDat_RevisionsStamm.FieldByName('ID').AsInteger;
  query := dm_main.qu_stdat_revstamm_update;
  query.Close;
  query.ParamByName('Id').AsInteger := id;
  query.ParamByName('Sektion').AsString := EditRevstammSektion.Text;
  query.ParamByName('Betriebsgruppe_Lm').AsString := EditRevstammBG.Text;
  query.ParamByName('Betriebsgruppe_Detail').AsString := EditRevstammBGDetail.Text;
  query.ParamByName('Betriebsart').AsString := iwmRevstammBetriebsart.Text;
  query.Execute;
  revstammAbfragen;
end;

procedure TFormStammdaten.resetRevstammModal;
begin
  EditRevstammSektion.Text := '';
  EditRevstammBG.Text := '';
  EditRevstammBGDetail.Text := '';
  iwmRevstammBetriebsart.Text := '';
end;


// ******************************************************************************
// ************************Revisionsplan*****************************************
// ******************************************************************************

{ Button mit dem der Revisionsplan angezeigt wird. Ist kein Jahr ausgewählt,
  wird dem User eine Meldung ausgegeben. }
procedure TFormStammdaten.jqbRevplanAbfragenOnClick(Sender: TObject;
  AParams: TStringList);
var
  LJahr: Integer;
begin
  LJahr := StrToIntDef(jqcbRevplanJahr.SelectedText, 0);
  if LJahr = 0 then
  begin
    jqsaStammdaten.ResetJSonProps;
    jqsaStammdaten.Error('Sie müssen ein Jahr auswählen!');
    abort;
  end;
  revPlanJahr := LJahr;
  revplanAbfragen;
end;

{ Versucht den Revisionsplan eines Jahres anzuzeigen. Wenn es für dieses Jahr
  noch keinen gibt, wird der User gefragt ob er einen erstellen möchte. }
procedure TFormStammdaten.revplanAbfragen;
var
  query: TFDQuery;
begin
  query := dm_main.qu_stdat_revplan;
  query.Close;
  query.ParamByName('Jahr').AsSmallInt := revPlanJahr;
  query.Open;

  // Der User wird nur gefragt, wenn er die Berechtigung dazu hat
  if (query.RecordCount = 0) and (revplanBearbeitenErlaubt) then
  begin
    jqsaStammdaten.JQSweetAlertOptions.Title := 'Plan erstellen';
    jqsaStammdaten.JQSweetAlertOptions.Text := 'Für dieses Jahr ist noch kein ' +
      'Revisionsplan vorhanden. Möchten Sie jetzt einen erstellen?';
    jqsaStammdaten.JQSweetAlertOptions.AlertType := jqsatInfo;
    jqsaStammdaten.JQSweetAlertOptions.ShowCancelButton := true;
    jqsaStammdaten.JQSweetAlertOptions.ConfirmButtonText := 'Jetzt erstellen';
    jqsaStammdaten.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
    jqsaStammdaten.JQSweetAlertOptions.OnBtnClick.OnEvent := revplanErstellen;
    jqsaStammdaten.Show;
  end;
end;

{ Der Revisionsplan für das ausgewählt Jahr wird mit einer Stored Procedure erzeugt
  Sie überprüft noch einmal, ob es den Revplan noch nicht gibt und erstellt dann
  alle Reihen mit dem richtigen BLDCode aus dem Revisionsstamm. }
procedure TFormStammdaten.revplanErstellen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  BLDCODE: SmallInt;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  BLDCODE := dm_main.BLDCODE;
  query := dm_main.qu_stdat_revplan_erzeugen;
  query.Close;
  query.ParamByName('Jahr').AsSmallInt := revPlanJahr;
  query.ParamByName('BLDCode').AsSmallInt := BLDCODE;
  query.Execute;
  revplanAbfragen;
end;

{ Wird ein Eintrag im Grid ausgewählt, erscheint das Ändern-Modal }
procedure TFormStammdaten.GridRevisionsplanOnSelectRow(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_stdat_revplan;
  GridRevisionsplan.SelectRecordFromCurrentRow;
  resetRevplanModal;

  if query.FieldByName('GESPERRT').AsBoolean then
  begin
    jqsaStammdaten.ResetJSonProps;
    jqsaStammdaten.Error('Der Revisionsplan ist gesperrt und kann nicht mehr geändert werden.');
    Exit;
  end;
  if not query.FieldByName('Risiko_Kategorie').IsNull then
  begin
    EditRevplanRisikoKategorie.Text := IntToStr(query.FieldByName('Risiko_Kategorie').AsInteger);
  end;
  if not query.FieldByName('J_MINDEST_KONTROLL_FREQUENZ').IsNull then
  begin
    EditRevplanMindestkontrollfrequenz.Text := SimpleRoundTo(query.FieldByName('J_MINDEST_KONTROLL_FREQUENZ').AsFloat,
      -2).ToString;
  end;

  with iwmStammdaten do
  begin
    Title := 'Ändern';
    ContentElement := iwrRevplan;
    Buttons.CommaText := '&Ändern,&Abbrechen';
    OnAsyncClick := revplanAendern;
    Show;
  end;
end;

{ Aufgerufen nachdem der User den Ändern dialog Bestätigt.
  Ändert die Risiko Kategorie und die Mindestkontrollfrequenz eines Eintrags. }
procedure TFormStammdaten.revplanAendern(Sender: TObject; EventParams: TStringList);
var
  query: TFDQuery;
  id: Integer;
  temp: String;
  risikoKategorie: SmallInt;
  mindestkontrollfrequenz: Double;
begin
  // Hat der User bestätigt?
  GridRevisionsplan.SelectRecordFromCurrentRow;
  if iwmStammdaten.ButtonIndex <> 1 then
  begin
    Exit;
  end;
  // Sind die Variablen konvertierbar?
  try
    risikoKategorie := strtoint(EditRevplanRisikoKategorie.Text);
    temp := EditRevplanMindestkontrollfrequenz.Text;
    mindestkontrollfrequenz := StrToFloat(temp);
  except
    on E: Exception do
    begin
      jqsaStammdaten.ResetJSonProps;
      jqsaStammdaten.Error('Sie müssen eine Zahl eingeben. Trennzeichen ist ein '',''.');
      Exit;
    end;
  end;

  // Der cursor von qu_stdat_revplan zeigt schon auf die richtige Query. Siehe
  // jqbRevplanAendernOnClick

  id := dm_main.qu_stdat_revplan.FieldByName('ID').AsInteger;
  query := dm_main.qu_stdat_revplan_update;
  query.Close;
  query.ParamByName('Id').AsInteger := id;
  query.ParamByName('Risiko_Kategorie').AsSmallInt := risikoKategorie;
  query.ParamByName('Mindestkontrollfrequenz').AsFloat := mindestkontrollfrequenz;
  query.ParamByName('BLDCODE').AsInteger := dm_main.BLDCODE;
  query.Execute;
  revplanAbfragen;
end;

procedure TFormStammdaten.resetRevplanModal;
begin
  EditRevplanRisikoKategorie.Text := '';
  EditRevplanMindestkontrollfrequenz.Text := '';
end;

{ Button mit dem der Revisionsplan abgeschlossen werden kann. Ein Alert Window
  wird erzeugt welches den User um Bestätigung fragt. }
procedure TFormStammdaten.jqbRevplanAbschliessenOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  query := dm_main.qu_stdat_revplan;
  jqsaStammdaten.ResetJSonProps;
  if query.RecordCount = 0 then
  begin
    jqsaStammdaten.Error('Sie müssen zuerst einen Revisionsplan auswählen!');
    Exit;
  end;

  query.First;
  while not query.Eof do
  begin
    if query.FieldByName('GESPERRT').AsBoolean then
    begin
      jqsaStammdaten.Error('Der Revisionsplan ist bereits gesperrt.');
      Exit;
    end;
    query.Next;
  end;

  jqsaStammdaten.JQSweetAlertOptions.Title := 'Revisionspläne abschließen';
  jqsaStammdaten.JQSweetAlertOptions.Text := 'Wollen Sie die Revisionspläne für ' +
    revPlanJahr.ToString +
    ' abschließen und die Kontrollen erstellen?';
  jqsaStammdaten.JQSweetAlertOptions.AlertType := jqsatInfo;
  jqsaStammdaten.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaStammdaten.JQSweetAlertOptions.ConfirmButtonText := 'Jetzt abschließen';
  jqsaStammdaten.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaStammdaten.JQSweetAlertOptions.OnBtnClick.OnEvent := revplanAbschliessen;
  jqsaStammdaten.Show;
end;

{ Erzeugt ungeplante Kontrollen aus dem Revisionsplan. }
procedure TFormStammdaten.revplanAbschliessen(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  BLDCODE: SmallInt;
  query: TFDQuery;
  gruppen: TList<TGruppe>;
  berichtstypen: TList<TBkbTyp>;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;

  // Es werden aktuell nur Hygienekontrollen erstellt
  var
  LCount := Usersession.ELKERest.RevisionsplanAbschliessen(revPlanJahr, 'HYK');
  Alert.Success('Es wurden ' + LCount.ToString + ' Revisonspläne erfolgreich verarbeitet.');
end;

procedure TFormStammdaten.ungeplanteKontrolleAnlegen(ABerichtstypen: TList<TBkbTyp>; AGruppe: TGruppe; ABetriebID:
  Integer; AKontrolltyp, ABkbtyp: String; AFrequenz: Integer);
begin
  var
  LKontrolle := TUngeplanteKontrolle.Create;
  try
    for var i := 1 to AFrequenz do
    begin;
      var
      month := Trunc((AFrequenz - i + 1) * (12 / AFrequenz));
      LKontrolle.Faellig := EncodeDate(revPlanJahr, month, 1);
      LKontrolle.Titel := 'Revisionsplan';
      LKontrolle.GruppeID := AGruppe.id;
      LKontrolle.BkbTyp := ABkbtyp;
      LKontrolle.kontrolltyp := AKontrolltyp;
      LKontrolle.BetriebID := ABetriebID;
      // Der Kontrollbericht wird im REST Service erzeugt
      Usersession.ELKERest.GetMeService.NeuerUngeplanterKontrollBericht2(LKontrolle);
    end;
  finally
    FreeAndNil(LKontrolle);
  end;
end;

{ Button mit dem der Revisionsplan gelöscht werden kann. Ein Alert Window
  wird erzeugt welches den User um Bestätigung fragt. }
procedure TFormStammdaten.jqbRevplanLoeschenOnClick(Sender: TObject;
  AParams: TStringList);
var
  query: TFDQuery;
begin
  jqsaStammdaten.ResetJSonProps;
  query := dm_main.qu_stdat_revplan;
  if query.RecordCount = 0 then
  begin
    jqsaStammdaten.Error('Sie müssen zuerst einen Revisionsplan auswählen!');
    Exit;
  end;

  query.First;
  while not query.Eof do
  begin
    if query.FieldByName('GESPERRT').AsBoolean then
    begin
      jqsaStammdaten.Error('Der Revisionsplan ist gesperrt und kann nicht mehr gelöscht werden.');
      Exit;
    end;
    query.Next;
  end;

  jqsaStammdaten.JQSweetAlertOptions.Title := 'Wollen Sie den Revisionsplan löschen?';
  jqsaStammdaten.JQSweetAlertOptions.AlertType := jqsatWarning;
  jqsaStammdaten.JQSweetAlertOptions.ShowCancelButton := true;
  jqsaStammdaten.JQSweetAlertOptions.ConfirmButtonText := 'Ja, Löschen';
  jqsaStammdaten.JQSweetAlertOptions.CancelButtonText := 'Abbrechen';
  jqsaStammdaten.JQSweetAlertOptions.OnBtnClick.OnEvent := revplanLoeschenConfirmed;
  jqsaStammdaten.Show;
end;

{ Wenn der User das Alert Window positiv schließt wird überprüft ob er bestätigt hat
  und dann werden alle Einträge aus der Tabelle aus dem ausgewählten Jahr und aus
  dem Bundesland des Benutzers gelöscht. Danach wird neu geladen. }
procedure TFormStammdaten.revplanLoeschenConfirmed(Sender: TObject; AParams: TStringList);
var
  isConfirmButton: Boolean;
  query: TFDQuery;
begin
  isConfirmButton := StrToBool(AParams.Values['isConfirm']);
  if not isConfirmButton then
  begin
    Exit;
  end;
  query := dm_main.qu_stdat_revplan_loeschen;
  query.Close;
  query.ParamByName('Jahr').AsSmallInt := revPlanJahr;
  query.ParamByName('Bldcode').AsSmallInt := dm_main.BLDCODE;
  query.Execute;
  revplanAbfragen;
end;

end.
