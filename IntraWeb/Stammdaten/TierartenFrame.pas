﻿unit TierartenFrame;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, CRUDGridFrame, IWCGJQGridCustomProvider,
  IWCGJQGridDataSetProvider, IWCGJQDialog, IWCGJQButton, IWCGJQControl,
  IWCGJQGrid, IWVCLBaseContainer, IWContainer, IWHTMLContainer,
  IWHTML40Container, IWRegion, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Param, FireDAC.Stan.Error, FireDAC.DatS, FireDAC.Phys.Intf,
  FireDAC.DApt.Intf, FireDAC.Stan.Async, FireDAC.DApt, Data.DB,
  FireDAC.Comp.DataSet, FireDAC.Comp.Client, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWApplication, IWControl, IWCompLabel, IWCGJQDatePicker, IWCompCheckbox,
  IWCGJQEdit, IWBaseComponent, IWBaseHTMLComponent,
  IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert, IWCGJQRegion, IWCGJQLabel;

type
  TTierarten = class(TCRUDGrid)
    quTierarten: TFDQuery;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    jqeKZ: TIWCGJQEdit;
    jqeBezeichnung: TIWCGJQEdit;
    jqeVisTierart: TIWCGJQEdit;
    iwcSichtbar: TIWCheckBox;
    jqdBegdat: TIWCGJQDatePicker;
    jqdEnddat: TIWCGJQDatePicker;
    quNeu: TFDQuery;
    quAendern: TFDQuery;
    quLoeschen: TFDQuery;
  private
    { Private declarations }
    procedure NeuBestaetigt;
    procedure InitAendern;
    procedure AendernBestaetigt;
    procedure LoeschenBestaetigt;
  protected
    procedure ResetModal; Override;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
    procedure TierartenAbfragen;
    procedure BearbeitenErlauben;
  end;

var
  Tierarten: TTierarten;

implementation

uses dmmain, Utility;

{$R *.dfm}


constructor TTierarten.Create(AOwner: TComponent; alert: TIWCGJQSweetAlert; bearbeitbar: boolean);
begin
  inherited Create(AOwner, alert);

  EnableAbfragen(quTierarten, TierartenAbfragen);
  if bearbeitbar then
  begin
    BearbeitenErlauben;
  end;
end;

procedure TTierarten.TierartenAbfragen;
begin
  RefreshQuery(quTierarten);
end;

procedure TTierarten.BearbeitenErlauben;
begin
  EnableNeu('Neuen Tierart erstellen', Nil, NeuBestaetigt);
  EnableAendern('Tierart ändern', InitAendern, AendernBestaetigt);
  EnableLoeschen('Tierart löschen', LoeschenBestaetigt);
end;

procedure TTierarten.NeuBestaetigt;
begin
  quNeu.Close;
  quNeu.ParamByName('kz').AsString := jqeKZ.Text;
  quNeu.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quNeu.ParamByName('vis_tierart').AsString := jqeVisTierart.Text;
  quNeu.ParamByName('sichtbar').AsBoolean := iwcSichtbar.Checked;
  quNeu.ParamByName('begdat').AsDate := jqdBegdat.Date;
  quNeu.ParamByName('enddat').AsDate := jqdEnddat.Date;
  quNeu.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quNeu.Execute;
end;

procedure TTierarten.InitAendern;
begin
  jqeKZ.Text := quTierarten.FieldByName('kz').AsString;
  jqeBezeichnung.Text := quTierarten.FieldByName('bezeichnung').AsString;
  jqeVisTierart.Text := quTierarten.FieldByName('vis_tierart').AsString;
  jqdBegdat.Date := quTierarten.FieldByName('begdat').AsDateTime;
  jqdEnddat.Date := quTierarten.FieldByName('enddat').AsDateTime;
  if quTierarten.FieldByName('sichtbar').AsInteger = 0 then
    iwcSichtbar.Checked := false
  else
    iwcSichtbar.Checked := true;
end;

procedure TTierarten.AendernBestaetigt;
begin
  quAendern.Close;
  quAendern.ParamByName('id').AsInteger := quTierarten.FieldByName('id').AsInteger;
  quAendern.ParamByName('kz').AsString := jqeKZ.Text;
  quAendern.ParamByName('bezeichnung').AsString := jqeBezeichnung.Text;
  quAendern.ParamByName('vis_tierart').AsString := jqeVisTierart.Text;
  quAendern.ParamByName('sichtbar').AsBoolean := iwcSichtbar.Checked;
  quAendern.ParamByName('begdat').AsDate := jqdBegdat.Date;
  quAendern.ParamByName('enddat').AsDate := jqdEnddat.Date;
  quAendern.ParamByName('bldcode').AsSmallInt := dm_main.BLDCODE;
  quAendern.Execute;
end;

procedure TTierarten.LoeschenBestaetigt;
begin
  quLoeschen.Close;
  quLoeschen.ParamByName('id').AsInteger := quTierarten.FieldByName('id').AsInteger;
  quLoeschen.Execute;
end;

procedure TTierarten.ResetModal;
begin
  jqeKZ.Text := '';
  jqeBezeichnung.Text := '';
  jqeVisTierart.Text := '';
  jqdBegdat.Date := Now;
  jqdEnddat.Date := IncMonth(Now, 12);
  iwcSichtbar.Checked := true;
end;

end.
