object DMStammdaten: TDMStammdaten
  OldCreateOrder = False
  Height = 555
  Width = 565
  object QBetriebeSuche: TFDQuery
    ActiveStoredUsage = []
    OnCalcFields = QBetriebeSucheCalcFields
    Connection = dm_main.FBC_MAIN
    UpdateObject = UBetriebeSuche
    SQL.Strings = (
      'declare @searchcount int;'
      'set @searchcount = :searchcount;'
      ''
      'declare @search1 varchar(100);'
      'set @search1 = :search1;'
      'declare @search2 varchar(100);'
      'set @search2 = :search2;'
      ''
      ''
      'select distinct top 200 case'
      '                   when M.WERT is null then 0'
      '                   else 1'
      '                   end        as Flag,'
      ''
      '               M.WERT         as <PERSON><PERSON><PERSON><PERSON><PERSON>,'
      '               M.BESCHREIBUNG as MarkierungBeschreibung,'
      ''
      ''
      '               b.ID,'
      '               b.REGNR,'
      '               b.NAME,'
      '               b.ID_ADRESSE,'
      '               b.ID_MARKIERUNG,'
      '               a.STRASSE,'
      '               a.PLZ,'
      '               a.ORT,'
      '               g.<PERSON><PERSON>IN<PERSON>KENNZIFFER,'
      '               g.<PERSON>,'
      '               b.AUFSICHTSORGAN,'
      '               b.<PERSON>EL<PERSON>,'
      '               b.EMAIL,'
      '               b.VERGEBUEHRUNG,'
      '               b.BLDCODE,'
      '               b.VULGO,'
      '               b.ANMERKUNG,'
      '               b.BVBKZ,'
      '               b.BBKNR,'
      '               z.ZULNR        as ZulassungsNr,'
      '               z.AKTIV        as ZulassungAktiv,'
      
        '               concat(b.ID ,'#39'-'#39', Coalesce(z.ZULNR, '#39'00000'#39')) as ' +
        'FullID'
      'from STAMMDATEN.BETRIEBE b'
      
        '         left outer join STAMMDATEN.ADRESSEN a on b.ID_ADRESSE =' +
        ' a.ID'
      
        '         left outer join STAMMDATEN.ZULASSUNGEN z on b.REGNR = z' +
        '.REGNR'
      
        '         left outer join STAMMDATEN.GEMEINDEN g on a.ID_GEMEINDE' +
        ' = g.ID'
      
        '         left join SYSTEMSTAMMDATEN.MARKIERUNGEN M on b.ID_MARKI' +
        'ERUNG = M.ID'
      'where b.BLDCODE = :BLDCODE'
      
        '--and  b.REGNR = z.REGNR and z.SICHTBAR = 1 and z.BEGINNDATUM <=' +
        ' GETDATE() and z.ENDDATUM >= GETDATE()'
      '  and case'
      '          when (@searchcount = 1) and'
      
        '               (charindex(@search1, b.NAME + '#39' '#39' + a.ORT + '#39' '#39' +' +
        ' a.STRASSE + '#39' '#39' + a.PLZ + '#39' '#39' + b.REGNR + '#39' '#39' +'
      '                                    coalesce(z.ZULNR, '#39#39')) > 0)'
      '              then 1'
      '          when (@searchcount = 2) and'
      
        '               (charindex(@search1, b.NAME + '#39' '#39' + a.ORT + '#39' '#39' +' +
        ' a.STRASSE + '#39' '#39' + a.PLZ + '#39' '#39' + b.REGNR + '#39' '#39' +'
      '                                    coalesce(z.ZULNR, '#39#39')) > 0)'
      
        '              and (charindex(@search2, b.NAME + '#39' '#39' + a.ORT + '#39' ' +
        #39' + a.STRASSE + '#39' '#39' + a.PLZ + '#39' '#39' + b.REGNR + '#39' '#39' +'
      
        '                                       coalesce(z.ZULNR, '#39#39')) > ' +
        '0)'
      '              then 1'
      '          else 0'
      '          end = 1'
      'order by name')
    Left = 88
    Top = 36
    ParamData = <
      item
        Name = 'SEARCHCOUNT'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 1
      end
      item
        Name = 'SEARCH1'
        DataType = ftString
        ParamType = ptInput
        Value = 'mayer'
      end
      item
        Name = 'SEARCH2'
        DataType = ftString
        ParamType = ptInput
        Value = ''
      end
      item
        Name = 'BLDCODE'
        DataType = ftSmallint
        ParamType = ptInput
        Value = 300
      end>
    object QBetriebeSucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ReadOnly = True
    end
    object QBetriebeSucheREGNR: TStringField
      FieldName = 'REGNR'
      Origin = 'REGNR'
      FixedChar = True
      Size = 7
    end
    object QBetriebeSucheNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Required = True
      Size = 255
    end
    object QBetriebeSucheID_ADRESSE: TIntegerField
      FieldName = 'ID_ADRESSE'
      Origin = 'ID_ADRESSE'
      Required = True
    end
    object QBetriebeSucheGEMEINDEKENNZIFFER: TIntegerField
      FieldName = 'GEMEINDEKENNZIFFER'
      Origin = 'GEMEINDEKENNZIFFER'
    end
    object QBetriebeSucheGEMEINDENAME: TStringField
      FieldName = 'GEMEINDENAME'
      Origin = 'GEMEINDENAME'
      Size = 80
    end
    object QBetriebeSucheAUFSICHTSORGAN: TStringField
      FieldName = 'AUFSICHTSORGAN'
      Origin = 'AUFSICHTSORGAN'
      FixedChar = True
      Size = 2
    end
    object QBetriebeSucheTELEFON: TStringField
      FieldName = 'TELEFON'
      Origin = 'TELEFON'
      Size = 50
    end
    object QBetriebeSucheEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 50
    end
    object QBetriebeSucheVERGEBUEHRUNG: TStringField
      FieldName = 'VERGEBUEHRUNG'
      Origin = 'VERGEBUEHRUNG'
      FixedChar = True
      Size = 2
    end
    object QBetriebeSucheBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QBetriebeSucheVULGO: TStringField
      FieldName = 'VULGO'
      Origin = 'VULGO'
      Size = 30
    end
    object QBetriebeSucheANMERKUNG: TWideStringField
      FieldName = 'ANMERKUNG'
      Origin = 'ANMERKUNG'
      Size = 500
    end
    object QBetriebeSucheBVBKZ: TStringField
      FieldName = 'BVBKZ'
      Origin = 'BVBKZ'
      Size = 3
    end
    object QBetriebeSucheBBKNR: TStringField
      FieldName = 'BBKNR'
      Origin = 'BBKNR'
      Size = 15
    end
    object QBetriebeSucheZulassungsNr: TStringField
      FieldName = 'ZulassungsNr'
      Origin = 'ZulassungsNr'
      Size = 9
    end
    object QBetriebeSucheZulassungAktiv: TSmallintField
      FieldName = 'ZulassungAktiv'
      Origin = 'ZulassungAktiv'
    end
    object QBetriebeSucheSTRASSE: TWideStringField
      FieldName = 'STRASSE'
      Origin = 'STRASSE'
      Size = 150
    end
    object QBetriebeSuchePLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Size = 7
    end
    object QBetriebeSucheORT: TWideStringField
      FieldName = 'ORT'
      Origin = 'ORT'
      Size = 150
    end
    object QBetriebeSucheFlag: TIntegerField
      FieldName = 'Flag'
      Origin = 'Flag'
      ReadOnly = True
      Required = True
    end
    object QBetriebeSucheFlagHTML: TStringField
      FieldKind = fkCalculated
      FieldName = 'FlagHTML'
      Size = 255
      Calculated = True
    end
    object QBetriebeSucheMarkierungWert: TWideStringField
      FieldName = 'MarkierungWert'
      Origin = 'MarkierungWert'
      Size = 250
    end
    object QBetriebeSucheMarkierungBeschreibung: TWideStringField
      FieldName = 'MarkierungBeschreibung'
      Origin = 'MarkierungBeschreibung'
      Size = 250
    end
    object QBetriebeSucheID_MARKIERUNG: TGuidField
      FieldName = 'ID_MARKIERUNG'
      Origin = 'ID_MARKIERUNG'
      Size = 38
    end
    object QBetriebeSucheFullID: TStringField
      FieldName = 'FullID'
      Origin = 'FullID'
      ReadOnly = True
      Required = True
      Size = 22
    end
  end
  object QTaetigkeiten: TFDQuery
    ActiveStoredUsage = []
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select *'
      'from stammdaten.taetigkeiten')
    Left = 432
    Top = 36
    object QTaetigkeitenID: TIntegerField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
    end
    object QTaetigkeitenKURZ_BEZ: TStringField
      FieldName = 'KURZ_BEZ'
      Origin = 'KURZ_BEZ'
      Required = True
      Size = 10
    end
    object QTaetigkeitenBEZEICHNUNG: TStringField
      FieldName = 'BEZEICHNUNG'
      Origin = 'BEZEICHNUNG'
      Size = 50
    end
    object QTaetigkeitenZULASSUNGSPFL: TSmallintField
      FieldName = 'ZULASSUNGSPFL'
      Origin = 'ZULASSUNGSPFL'
    end
    object QTaetigkeitenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QTaetigkeitenAKTIV: TSmallintField
      FieldName = 'AKTIV'
      Origin = 'AKTIV'
    end
    object QTaetigkeitenSICHTBAR: TSmallintField
      FieldName = 'SICHTBAR'
      Origin = 'SICHTBAR'
    end
    object QTaetigkeitenVISQUELLE: TSmallintField
      FieldName = 'VISQUELLE'
      Origin = 'VISQUELLE'
    end
  end
  object UBetriebeSuche: TFDUpdateSQL
    Connection = dm_main.FBC_MAIN
    InsertSQL.Strings = (
      'INSERT INTO stammdaten.betriebe'
      '(REGNR, NAME, ID_ADRESSE, STRASSE, PLZ, '
      '  ORT, GEMNR, KATASTRALGEMNAME, AUFSICHTSORGAN, '
      '  TELEFON, EMAIL, VERGEBUEHRUNG, BLDCODE, '
      '  VULGO, ANMERKUNG, BVBKZ, BBKNR)'
      
        'VALUES (:NEW_REGNR, :NEW_NAME, :NEW_ID_ADRESSE, :NEW_STRASSE, :N' +
        'EW_PLZ, '
      
        '  :NEW_ORT, :NEW_GEMNR, :NEW_KATASTRALGEMNAME, :NEW_AUFSICHTSORG' +
        'AN, '
      '  :NEW_TELEFON, :NEW_EMAIL, :NEW_VERGEBUEHRUNG, :NEW_BLDCODE, '
      '  :NEW_VULGO, :NEW_ANMERKUNG, :NEW_BVBKZ, :NEW_BBKNR);'
      'SELECT SCOPE_IDENTITY() AS ID')
    ModifySQL.Strings = (
      'UPDATE stammdaten.betriebe'
      
        'SET REGNR = :NEW_REGNR, NAME = :NEW_NAME, ID_ADRESSE = :NEW_ID_A' +
        'DRESSE, '
      '  STRASSE = :NEW_STRASSE, PLZ = :NEW_PLZ, ORT = :NEW_ORT, '
      '  AUFSICHTSORGAN = :NEW_AUFSICHTSORGAN, TELEFON = :NEW_TELEFON, '
      '  EMAIL = :NEW_EMAIL, VERGEBUEHRUNG = :NEW_VERGEBUEHRUNG, '
      
        '  BLDCODE = :NEW_BLDCODE, VULGO = :NEW_VULGO, ANMERKUNG = :NEW_A' +
        'NMERKUNG, '
      '  BVBKZ = :NEW_BVBKZ, BBKNR = :NEW_BBKNR,'
      '  ID_MARKIERUNG = :NEW_ID_MARKIERUNG'
      'WHERE ID = :OLD_ID;'
      'SELECT ID'
      'FROM stammdaten.betriebe'
      'WHERE ID = :NEW_ID')
    DeleteSQL.Strings = (
      'DELETE FROM stammdaten.betriebe'
      'WHERE ID = :OLD_ID')
    FetchRowSQL.Strings = (
      
        'SELECT ID, REGNR, NAME, ID_ADRESSE, STRASSE, PLZ, ORT, GEMNR, KA' +
        'TASTRALGEMNAME, '
      '  AUFSICHTSORGAN, TELEFON, EMAIL, VERGEBUEHRUNG, BLDCODE, '
      '  VULGO, ANMERKUNG, BVBKZ, BBKNR'
      'FROM ('
      'select * from stammdaten.betriebe'
      ') '
      'WHERE ID = :OLD_ID')
    Left = 184
    Top = 40
  end
  object DSBetriebeSuche: TDataSource
    DataSet = QBetriebeSuche
    Left = 88
    Top = 104
  end
  object QBetriebeAdressen: TFDQuery
    ActiveStoredUsage = []
    MasterSource = DSBetriebeEdit
    MasterFields = 'ID_ADRESSE'
    Connection = dm_main.FBC_MAIN
    FetchOptions.AssignedValues = [evCache]
    FetchOptions.Cache = [fiBlobs, fiMeta]
    SQL.Strings = (
      'select * from Stammdaten.Adressen a'
      'where a.Id = :ID_Adresse')
    Left = 80
    Top = 288
    ParamData = <
      item
        Name = 'ID_ADRESSE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 10041
      end>
    object QBetriebeAdressenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QBetriebeAdressenORT: TWideStringField
      FieldName = 'ORT'
      Origin = 'ORT'
      Required = True
      Size = 150
    end
    object QBetriebeAdressenPLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Required = True
      Size = 7
    end
    object QBetriebeAdressenSTRASSE: TWideStringField
      FieldName = 'STRASSE'
      Origin = 'STRASSE'
      Required = True
      Size = 150
    end
    object QBetriebeAdressenADRESSZUSATZ: TWideStringField
      FieldName = 'ADRESSZUSATZ'
      Origin = 'ADRESSZUSATZ'
      Size = 150
    end
    object QBetriebeAdressenBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
      Required = True
    end
    object QBetriebeAdressenxKoord_31287: TFMTBCDField
      FieldName = 'xKoord_31287'
      Origin = 'xKoord_31287'
      Precision = 31
    end
    object QBetriebeAdressenyKoord_31287: TFMTBCDField
      FieldName = 'yKoord_31287'
      Origin = 'yKoord_31287'
      Precision = 31
    end
    object QBetriebeAdressenxKoord_4326: TFMTBCDField
      FieldName = 'xKoord_4326'
      Origin = 'xKoord_4326'
      Precision = 31
    end
    object QBetriebeAdressenyKoord_4326: TFMTBCDField
      FieldName = 'yKoord_4326'
      Origin = 'yKoord_4326'
      Precision = 31
    end
    object QBetriebeAdressenID_GEMEINDE: TIntegerField
      FieldName = 'ID_GEMEINDE'
      Origin = 'ID_GEMEINDE'
    end
  end
  object DSBetriebeAdressen: TDataSource
    DataSet = QBetriebeAdressen
    Left = 80
    Top = 352
  end
  object QBetriebeGemeinden: TFDQuery
    ActiveStoredUsage = []
    MasterSource = DSBetriebeAdressen
    MasterFields = 'ID_GEMEINDE'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select * from Stammdaten.Gemeinden g'
      'where g.Id = :ID_Gemeinde')
    Left = 80
    Top = 416
    ParamData = <
      item
        Name = 'ID_GEMEINDE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 1
      end>
    object QBetriebeGemeindenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QBetriebeGemeindenGEMEINDEKENNZIFFER: TIntegerField
      FieldName = 'GEMEINDEKENNZIFFER'
      Origin = 'GEMEINDEKENNZIFFER'
      Required = True
    end
    object QBetriebeGemeindenGEMEINDENAME: TStringField
      FieldName = 'GEMEINDENAME'
      Origin = 'GEMEINDENAME'
      Required = True
      Size = 80
    end
    object QBetriebeGemeindenGEMEINDECODE: TIntegerField
      FieldName = 'GEMEINDECODE'
      Origin = 'GEMEINDECODE'
      Required = True
    end
    object QBetriebeGemeindenAMTSPLZ: TStringField
      FieldName = 'AMTSPLZ'
      Origin = 'AMTSPLZ'
      Required = True
      Size = 10
    end
    object QBetriebeGemeindenPOL_BEZ_KENNZIF: TIntegerField
      FieldName = 'POL_BEZ_KENNZIF'
      Origin = 'POL_BEZ_KENNZIF'
      Required = True
    end
    object QBetriebeGemeindenPOL_BEZIRK: TStringField
      FieldName = 'POL_BEZIRK'
      Origin = 'POL_BEZIRK'
      Required = True
      Size = 80
    end
    object QBetriebeGemeindenPOL_BEZ_CODE: TIntegerField
      FieldName = 'POL_BEZ_CODE'
      Origin = 'POL_BEZ_CODE'
      Required = True
    end
    object QBetriebeGemeindenLAND_ISO2: TStringField
      FieldName = 'LAND_ISO2'
      Origin = 'LAND_ISO2'
      Required = True
      FixedChar = True
      Size = 2
    end
    object QBetriebeGemeindenBUNDESLANDCODE: TSmallintField
      FieldName = 'BUNDESLANDCODE'
      Origin = 'BUNDESLANDCODE'
    end
  end
  object QBetriebeEdit: TFDQuery
    ActiveStoredUsage = []
    MasterSource = DSBetriebeSuche
    MasterFields = 'ID'
    DetailFields = 'ID'
    Connection = dm_main.FBC_MAIN
    FetchOptions.AssignedValues = [evCache]
    FetchOptions.Cache = [fiBlobs, fiMeta]
    SQL.Strings = (
      'select * from Stammdaten.Betriebe b'
      'where b.Id = :ID')
    Left = 80
    Top = 176
    ParamData = <
      item
        Name = 'ID'
        DataType = ftAutoInc
        ParamType = ptInput
        Value = 3256
      end>
    object QBetriebeEditID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QBetriebeEditREGNR: TStringField
      FieldName = 'REGNR'
      Origin = 'REGNR'
      FixedChar = True
      Size = 7
    end
    object QBetriebeEditNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Required = True
      Size = 255
    end
    object QBetriebeEditID_ADRESSE: TIntegerField
      FieldName = 'ID_ADRESSE'
      Origin = 'ID_ADRESSE'
      Required = True
    end
    object QBetriebeEditSTRASSE: TStringField
      FieldName = 'STRASSE'
      Origin = 'STRASSE'
      Size = 150
    end
    object QBetriebeEditPLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Size = 7
    end
    object QBetriebeEditORT: TStringField
      FieldName = 'ORT'
      Origin = 'ORT'
      Size = 50
    end
    object QBetriebeEditGEMNR: TStringField
      FieldName = 'GEMNR'
      Origin = 'GEMNR'
      Size = 5
    end
    object QBetriebeEditKATASTRALGEMNAME: TStringField
      FieldName = 'KATASTRALGEMNAME'
      Origin = 'KATASTRALGEMNAME'
      Size = 50
    end
    object QBetriebeEditAUFSICHTSORGAN: TStringField
      FieldName = 'AUFSICHTSORGAN'
      Origin = 'AUFSICHTSORGAN'
      FixedChar = True
      Size = 2
    end
    object QBetriebeEditTELEFON: TStringField
      FieldName = 'TELEFON'
      Origin = 'TELEFON'
      Size = 50
    end
    object QBetriebeEditEMAIL: TStringField
      FieldName = 'EMAIL'
      Origin = 'EMAIL'
      Size = 50
    end
    object QBetriebeEditVERGEBUEHRUNG: TStringField
      FieldName = 'VERGEBUEHRUNG'
      Origin = 'VERGEBUEHRUNG'
      FixedChar = True
      Size = 2
    end
    object QBetriebeEditBLDCODE: TSmallintField
      FieldName = 'BLDCODE'
      Origin = 'BLDCODE'
    end
    object QBetriebeEditVULGO: TStringField
      FieldName = 'VULGO'
      Origin = 'VULGO'
      Size = 30
    end
    object QBetriebeEditANMERKUNG: TWideStringField
      FieldName = 'ANMERKUNG'
      Origin = 'ANMERKUNG'
      Size = 500
    end
    object QBetriebeEditBVBKZ: TStringField
      FieldName = 'BVBKZ'
      Origin = 'BVBKZ'
      Size = 3
    end
    object QBetriebeEditBBKNR: TStringField
      FieldName = 'BBKNR'
      Origin = 'BBKNR'
      Size = 15
    end
  end
  object DSBetriebeEdit: TDataSource
    DataSet = QBetriebeEdit
    Left = 80
    Top = 240
  end
  object QGemeindesuche: TFDQuery
    ActiveStoredUsage = []
    FilterOptions = [foCaseInsensitive]
    Connection = dm_main.FBC_MAIN
    FetchOptions.AssignedValues = [evMode, evRecordCountMode]
    FetchOptions.RecordCountMode = cmTotal
    SQL.Strings = (
      
        'select ID, GemeindeKennziffer, GemeindeName, AmtsPlz as PLZ, Amt' +
        'sPLZ +'#39' '#39'+GemeindeName as PlzOrt from Stammdaten.Gemeinden'
      'where BundeslandCode = :BLDCODE'
      'order by AmtsPlz')
    Left = 296
    Top = 40
    ParamData = <
      item
        Name = 'BLDCODE'
        DataType = ftInteger
        ParamType = ptInput
        Value = 300
      end>
    object QGemeindesucheID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QGemeindesucheGemeindeKennziffer: TIntegerField
      FieldName = 'GemeindeKennziffer'
      Origin = 'GemeindeKennziffer'
      Required = True
    end
    object QGemeindesucheGemeindeName: TStringField
      FieldName = 'GemeindeName'
      Origin = 'GemeindeName'
      Required = True
      Size = 80
    end
    object QGemeindesuchePLZ: TStringField
      FieldName = 'PLZ'
      Origin = 'PLZ'
      Required = True
      Size = 10
    end
    object QGemeindesuchePlzOrt: TStringField
      FieldName = 'PlzOrt'
      Origin = 'PlzOrt'
      ReadOnly = True
      Required = True
      Size = 91
    end
  end
  object QBetriebTierartenTaetigkeiten: TFDQuery
    MasterSource = DSBetriebeSuche
    MasterFields = 'REGNR'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select'
      '-- z.REGNR,'
      'distinct t.TIERART, tae.BEZEICHNUNG as Taetigkeit --, *'
      'from VISDATEN.VIS_ZULASSUNGEN z'
      
        '         left join STAMMDATEN.BETRIEBSTYPEN_TIERART b on b.BETRI' +
        'EBSTYP = z.BETRIEBSTYP'
      
        '         left join VISDATEN.VIS_TIERARTEN t on t.WERT = b.TIERAR' +
        'T'
      ''
      
        '         left join STAMMDATEN.TAETIG_BETRTYP tb on b.BETRIEBSTYP' +
        ' = tb.BETRIEBSTYP'
      
        '         left join STAMMDATEN.TAETIGKEITEN tae on tae.ID = tb.ID' +
        '_TAETIGKEIT'
      'where b.TIERART <> '#39'XX'#39
      '  and not t.TIERART is null'
      '  and z.REGNR = :REGNR'
      '  and z.BEGINNDATUM <= :datum'
      '  and z.ENDDATUM >= :datum')
    Left = 208
    Top = 176
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DATUM'
        DataType = ftDate
        ParamType = ptInput
        Value = Null
      end>
    object QBetriebTierartenTaetigkeitenTIERART: TStringField
      FieldName = 'TIERART'
      Origin = 'TIERART'
      Size = 50
    end
    object QBetriebTierartenTaetigkeitenTaetigkeit: TStringField
      FieldName = 'Taetigkeit'
      Origin = 'Taetigkeit'
      Size = 50
    end
  end
  object QBetriebZulassungen: TFDQuery
    MasterSource = DSBetriebeSuche
    MasterFields = 'REGNR'
    Connection = dm_main.FBC_MAIN
    SQL.Strings = (
      'select zn.ZULASSUNGSNUMMER,'
      '       zlt.BEZEICHNUNG as Zulassungstyp,'
      '       btt.BEZEICHNUNG as Betriebstyp,'
      '       zn.BEGINNDATUM,'
      '       zn.ENDDATUM'
      'from VISDATEN.VIS_ZULASSUNGSNUMMERN zn'
      '         left join VISDATEN.VIS_ZULASSUNGEN z'
      
        '                   on (z.REGNR = zn.REGNR and z.BEGINNDATUM = zn' +
        '.BEGINNDATUM and z.ENDDATUM = zn.ENDDATUM)'
      
        '         left join VISDATEN.VIS_BETRIEBSTYPEN btt on btt.ID = z.' +
        'BETRIEBSTYP'
      
        '         left join VISDATEN.VIS_ZULASSUNGSTYPEN zlt on zlt.ID = ' +
        'z.ZULASSUNG'
      'where zn.REGNR = :REGNR'
      '  and zn.BEGINNDATUM <= :DATUM'
      '  and zn.ENDDATUM >= :DATUM'
      'order by ZULASSUNGSNUMMER, Zulassungstyp, Betriebstyp')
    Left = 368
    Top = 176
    ParamData = <
      item
        Name = 'REGNR'
        DataType = ftString
        ParamType = ptInput
        Value = Null
      end
      item
        Name = 'DATUM'
        DataType = ftDateTime
        ParamType = ptInput
        Value = Null
      end>
  end
end
