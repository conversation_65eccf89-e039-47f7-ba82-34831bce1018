﻿unit Forms.VersionInfo;

interface

uses
  Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, IWCGJQButton,
  Vcl.Imaging.jpeg, IWCompExtCtrls, IWVCLBaseControl, IWBaseControl,
  IWBaseHTMLControl, IWControl, IWCompButton, Vcl.Controls, Vcl.Forms,
  IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion,
  IWCGJQControl, IWCGJQRegion, IWCompLabel, IWCGJQLabel, IWCGJQTabs,
  Forms.Base, IWHTMLControls, Vcl.Graphics, IWCGJQImage,
  IWBaseComponent, IWBaseHTMLComponent, IWBaseHTML40Component, IWCGJQComp, IWCGJQSweetAlert ;

type
  TFormVersionInfo = class(TFormBase)
    jqrMid: TIWCGJQRegion;
    IWLabel1: TIWCGJQLabel;
    IWLabel2: TIWCGJQLabel;
    IWCGJQRegion4: TIWCGJQRegion;
    IWLabel3: TIWCGJQLabel;
    IWLabel4: TIWCGJQLabel;
    IWLabel5: TIWCGJQLabel;
    IWLabel6: TIWCGJQLabel;
    IWLabel7: TIWCGJQLabel;
    IWLabel8: TIWCGJQLabel;
    IWLabel9: TIWCGJQLabel;
    IWLabel10: TIWCGJQLabel;
    IWLabel22: TIWCGJQLabel;
    jqrVersion: TIWCGJQRegion;
    jqrPvpToken: TIWCGJQRegion;
    IWCGJQRegion5: TIWCGJQRegion;
    iwrLinks: TIWCGJQRegion;
    iwrRechts: TIWCGJQRegion;
    IWLabel23: TIWCGJQLabel;
    iwlIntrawebVersion: TIWCGJQLabel;
    iwlApiVersion: TIWCGJQLabel;
    iwlBenutzer: TIWCGJQLabel;
    iwlUserId: TIWCGJQLabel;
    iwlGid: TIWCGJQLabel;
    iwlSicherheitsklasse: TIWCGJQLabel;
    iwlOrganistation: TIWCGJQLabel;
    iwlParticipantId: TIWCGJQLabel;
    iwlPvpRollen: TIWCGJQLabel;
    iwliGruppenzugehoerigkeiten: TIWList;
    jqiEsculenta: TIWCGJQImage;
    jqrBottom: TIWCGJQRegion;
    jqiElke: TIWCGJQImage;
    procedure esculentaLogoZurueckOnClick(Sender: TObject);
    procedure onVersionInfoFormCreated(Sender: TObject);
    procedure jqiElkeOnClick(Sender: TObject; AParams: TStringList);
end;

implementation

uses
  System.Generics.Collections,
  dmmain, Teamfenster, ServerController, ELKE.Classes.Admin,
  ELKE.Classes.PVP.Token, ELKE.Classes.Generated, DX.Utils.Windows;

{$R *.dfm}


procedure TFormVersionInfo.esculentaLogoZurueckOnClick(Sender: TObject);
begin
  Release;
end;

procedure TFormVersionInfo.jqiElkeOnClick(Sender: TObject;
  AParams: TStringList);
begin
  TTeamFensterForm.Create(Self).Show;
end;

procedure TFormVersionInfo.onVersionInfoFormCreated(Sender: TObject);
var
  LPVPToken: TPVPToken;
  LMe: TUser;
  LGruppen: TList<TGruppe>;
  LRestVersion: TVersionInfo;
  Gruppe: TGruppe;
  rest: TDateTime;
begin
  LGruppen := Nil;
  try
    // Rest-Queries abschicken
    LMe := Usersession.ELKERest.GetUser;
    LPVPToken := Usersession.ELKERest.GetPVPToken;
    LRestVersion := UserSession.ELKERest.GetAdminVersion;
    LGruppen := Usersession.ELKERest.GetGruppenzugehoerigkeiten;

    // Labels befüllen
    iwlIntrawebVersion.Caption := DX.Utils.Windows.GetExeVersion + ' / ' +
      FormatDateTime('yyyy-mm-dd', DX.Utils.Windows.GetExeBuildTimestamp) + 'T'+
      FormatDateTime('hh:nn:ss', DX.Utils.Windows.GetExeBuildTimestamp);

    rest := LRestVersion.BuildTimestamp;
    iwlApiVersion.Caption := LRestVersion.Version + ' / ' +
      FormatDateTime('yyyy-mm-dd', rest) + 'T' +
      FormatDateTime('hh:nn:ss', rest);

    // Nullables
    if LMe.Username.HasValue then
      iwlBenutzer.Caption := LMe.Username;
    if LPVPToken.USERID.HasValue then
      iwlUserId.Caption := LPVPToken.USERID;
    if LPVPToken.GID.HasValue then
      iwlGid.Caption := LPVPToken.GID;
    if LPVPToken.OU.HasValue then
      iwlOrganistation.Caption := LPVPToken.OU;
    if LPVPToken.PARTICIPANT_ID.HasValue then
      iwlParticipantId.Caption := LPVPToken.PARTICIPANT_ID;
    if LPVPToken.Roles.HasValue then
      iwlPvpRollen.Caption := LPVPToken.Roles;

    iwlSicherheitsklasse.Caption := IntToStr(LPVPToken.SECCLASS);
    // Gruppenzugehörigkeiten befüllen
    for Gruppe in LGruppen do
    begin
      if Gruppe.Persoenlich then begin
        iwliGruppenzugehoerigkeiten.Items.Add('PG: ' + Gruppe.Bezeichnung);
      end else begin
        iwliGruppenzugehoerigkeiten.Items.Add(Gruppe.Bezeichnung);
      end;
    end;
  finally
    LGruppen.Free;
  end;
end;

end.
