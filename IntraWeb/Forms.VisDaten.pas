﻿unit Forms.VisDaten;

interface

uses
	Classes, SysUtils, IWAppForm, IWApplication, IWColor, IWTypes, Vcl.Graphics,
	IWCompLabel, IWCompEdit, IWCompGrids, IWDBGrids, IWCompTabControl,
	IWCompButton, Vcl.Imaging.jpeg, IWVCLBaseControl, IWBaseControl,
	IWBaseHTMLControl, IWControl, IWCompExtCtrls, Vcl.Controls, Vcl.Forms,
	IWVCLBaseContainer, IWContainer, IWHTMLContainer, IWHTML40Container, IWRegion,
	Data.DB, IWDBStdCtrls, IWCGJQControl, IWCGJQButton,
  IWCGJQGridCustomProvider, IWCGJQGridDataSetProvider, IWCGJQGrid, IWCGJQLabel,
  IWCGJQEdit, IWCGJQRegion, IWBaseComponent, IWBaseHTMLComponent,
  IWBase<PERSON>ML<PERSON>Component, IWCGJQ<PERSON>omp, IWCGJQ<PERSON><PERSON><PERSON><PERSON>t, Forms.Base,
  System.Generics.Collections ;

type
	TFormVisDaten = class(TFormBase)
		IWRegion5: TIWCGJQRegion;
    iwtVisdaten: TIWTabControl;
    TabBetriebsstammdaten: TIWTabPage;
		iwdbg_Betriebsstammdaten: TIWDBGrid;
    TabZulassungen: TIWTabPage;
		iwdbg_Zulassungen: TIWDBGrid;
		IWRegion2: TIWCGJQRegion;
	 Edit_zulassung_sucheRegnr: TIWCGJQEdit;
		iwl_zulassungen_regnr: TIWCGJQLabel;
		iwi_loesche_Zulassungssuche: TIWImage;
		IWRegion3: TIWCGJQRegion;
	 Edit_su_Name1_betrest: TIWCGJQEdit;
		iwl_bstammdaten_name: TIWCGJQLabel;
		iwi_loeschen_Stammdatensuche: TIWImage;
		ds_VisBStammdaten: TDataSource;
		ds_VisZulassungen: TDataSource;
    TabBetriebstypen: TIWTabPage;
		iwdbg_Betriebstypen: TIWDBGrid;
		IWRegion7: TIWCGJQRegion;
	 Edit_betriebstypen_sucheBezeichnung: TIWCGJQEdit;
		iwl_betriebstyp_bezeichnung: TIWCGJQLabel;
		iwi_loesche_Betriebstypensuche: TIWImage;
		ds_VisBetriebstypen: TDataSource;
    TabDOMWerte: TIWTabPage;
		iwdbg_Domwerte: TIWDBGrid;
		IWRegion8: TIWCGJQRegion;
	 Edit_Domwert_Textsuche: TIWCGJQEdit;
		iwl_domwert_text: TIWCGJQLabel;
		iwi_loesche_DOMwertsuche: TIWImage;
		ds_VisDOM: TDataSource;
    TabZulassungslistencodes: TIWTabPage;
		iwdbg_Zulassungslistencodes: TIWDBGrid;
		IWRegion9: TIWCGJQRegion;
	 Edit_Listencodes_Betriebstypsuche: TIWCGJQEdit;
		iwl_listencodes_betriebstyp: TIWCGJQLabel;
		iwi_loesche_Listencodesuche: TIWImage;
		ds_VisCodes: TDataSource;
    TabTierarten: TIWTabPage;
		iwdbg_Tierarten: TIWDBGrid;
		ds_Tierarten: TDataSource;
    TabZulassungstypen: TIWTabPage;
		iwdbg_Zulassungstypen: TIWDBGrid;
		ds_Zulassungstypen: TDataSource;
    TabSchluesseltypen: TIWTabPage;
		iwdbg_Schluesseltypen: TIWDBGrid;
		ds_Schluesseltypen: TDataSource;
	 Edit_su_regnr_betrest: TIWCGJQEdit;
		iwl_bstammdaten_regnr: TIWCGJQLabel;
		IWL_Betriebsst_count: TIWCGJQLabel;
	 Edit_Betriebstypen_IDsuche: TIWCGJQEdit;
		iwl_betriebstyp_id: TIWCGJQLabel;
		iwl_zulassungen_count: TIWCGJQLabel;
		iwl_betriebstypen_count: TIWCGJQLabel;
		iwl_domwert_count: TIWCGJQLabel;
		iwl_listencodes_count: TIWCGJQLabel;
		iwl_Listencodes_codesuche: TIWCGJQLabel;
	 Edit_Listencodes_Codesuche: TIWCGJQEdit;
		iwl_bstammdaten_Strasse: TIWCGJQLabel;
		iwl_bstammdaten_Hnr: TIWCGJQLabel;
	 Edit_BStammdaten_sucheStrasse: TIWCGJQEdit;
	 Edit_BStammdaten_sucheHnr: TIWCGJQEdit;
		IWRegion10: TIWCGJQRegion;
	 Edit_tierartsuche: TIWCGJQEdit;
		iwl_suche_Tierart: TIWCGJQLabel;
		iwi_Loesche_Tierartsuche: TIWImage;
		iwl_tierart_count: TIWCGJQLabel;
		IWRegion13: TIWCGJQRegion;
	 Edit_Zulassungstypen_sucheBezeichnung: TIWCGJQEdit;
		iwl_Zulassungstypen_Bezeichnung: TIWCGJQLabel;
		iwi_Loesche_Zulassungstypensuche: TIWImage;
		iwl_Zulassungstypen_count: TIWCGJQLabel;
		IWRegion12: TIWCGJQRegion;
	 Edit_Schluesseltypen_sucheBezeichnung: TIWCGJQEdit;
		iwl_Schluesseltypen_bezeichnung: TIWCGJQLabel;
		iwi_schluesseltypensuche_loeschen: TIWImage;
		iwl_Schluesseltypen_count: TIWCGJQLabel;
    iwcgb_sucheStammdaten: TIWCGJQButton;
    iwcgb_sucheZulassung: TIWCGJQButton;
    iwcgb_sucheBetriebstyp: TIWCGJQButton;
    iwcgb_sucheListencodes: TIWCGJQButton;
    iwcgb_sucheDOMwert: TIWCGJQButton;
    iwcgb_sucheTierart: TIWCGJQButton;
    iwcgb_sucheZulassungstypen: TIWCGJQButton;
    iwcgb_sucheSchluesseltypen: TIWCGJQButton;
    iwcgprov_Zulassungen: TIWCGJQGridDataSetProvider;
    iwcggrid_Zulassungen: TIWCGJQGrid;
    iwcgprov_Stammdaten: TIWCGJQGridDataSetProvider;
    jqgStammdaten: TIWCGJQGrid;
    iwcggrid_Betriebstypen: TIWCGJQGrid;
    iwcgprov_Betriebstypen: TIWCGJQGridDataSetProvider;
    iwcggrid_DOMwert: TIWCGJQGrid;
    iwcgprov_DOMwerte: TIWCGJQGridDataSetProvider;
    iwcggrid_Listencodes: TIWCGJQGrid;
    iwcgprov_Listencodes: TIWCGJQGridDataSetProvider;
    iwcggrid_Tierarten: TIWCGJQGrid;
    iwcgprov_Tierarten: TIWCGJQGridDataSetProvider;
    iwcggrid_Zulassungstypen: TIWCGJQGrid;
    iwcgprov_Zulassungstypen: TIWCGJQGridDataSetProvider;
    iwcggrid_Schluesseltypen: TIWCGJQGrid;
    iwcgprov_Schluesseltypen: TIWCGJQGridDataSetProvider;
    TabPrbkat: TIWTabPage;
    iwcggrid_prbkat: TIWCGJQGrid;
    IWRegion11: TIWCGJQRegion;
    iwi_loesche_prbkatsuche: TIWImage;
    iwcgb_suche_prbkat: TIWCGJQButton;
    iwcgprov_prbkat: TIWCGJQGridDataSetProvider;
    ds_Prbkat: TDataSource;
    iwcgla_Tierart: TIWCGJQLabel;
    Kategorie: TIWCGJQLabel;
    iwcgl_prbkat_count: TIWCGJQLabel;
    TabZulassungsnummern: TIWTabPage;
    iwcggrid_ZulassungsNr: TIWCGJQGrid;
    IWRegion1: TIWCGJQRegion;
    iwi_loesche_zulnrsuche: TIWImage;
    iwcgl_zulnr_regnr: TIWCGJQLabel;
    iwcgl_zulnr_zulnr: TIWCGJQLabel;
    iwl_zulassungsnr_count: TIWCGJQLabel;
    iwcgb_suche_zulassungsnr: TIWCGJQButton;
    ds_ZulassungsNr: TDataSource;
    iwcgprov_ZulassungsNr: TIWCGJQGridDataSetProvider;
    Edit_prbkat_Tierart: TIWCGJQEdit;
    Edit_prbkat_kateg: TIWCGJQEdit;
    Edit_zulnr_regnr: TIWCGJQEdit;
    Edit_zulnr_zulnr: TIWCGJQEdit;
    IWRegion6: TIWCGJQRegion;
    iwcgsa_Visdaten: TIWCGJQSweetAlert;
		procedure iwtVisdatenChange(Sender: TObject);
		procedure IWB_suchenBetriebsstammdatenClick(Sender: TObject);
		procedure iwb_zurueckClick(Sender: TObject);
		procedure iwb_sucheZulassungClick(Sender: TObject);
		procedure iwb_suche_betriebstypenClick(Sender: TObject);
		procedure IWAppFormCreate(Sender: TObject);
		procedure IWB_suche_DomwertClick(Sender: TObject);
		procedure iwb_suche_ListencodesClick(Sender: TObject);
		procedure iwi_loeschen_StammdatensucheClick(Sender: TObject);
		procedure iwb_tierart_suchenClick(Sender: TObject);
		procedure iwb_suche_schluesseltypenClick(Sender: TObject);
		procedure iwb_Zulassungstypen_sucheClick(Sender: TObject);
		procedure iwi_loesche_ZulassungssucheClick(Sender: TObject);
		procedure iwi_loesche_BetriebstypensucheClick(Sender: TObject);
		procedure iwi_loesche_ListencodesucheClick(Sender: TObject);
		procedure iwi_Loesche_TierartsucheClick(Sender: TObject);
		procedure iwi_Loesche_ZulassungstypensucheClick(Sender: TObject);
		procedure iwi_schluesseltypensuche_loeschenClick(Sender: TObject);
    procedure iwi_loesche_DOMwertsucheClick(Sender: TObject);
    procedure IWCGJQButton1JQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheStammdatenJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheZulassungJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheBetriebstypJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheDOMwertJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheListencodesJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheTierartJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheZulassungstypenJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_sucheSchluesseltypenJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_suche_prbkatJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwcgb_suche_zulassungsnrJQButtonOptionsClick(Sender: TObject;
      AParams: TStringList);
    procedure iwi_loesche_zulnrsucheClick(Sender: TObject);
    procedure iwi_loesche_prbkatsucheClick(Sender: TObject);
	private
    aktiveTabs: TList<String>;

		procedure Betriebsstammdaten;
		procedure Zulassungen;
		procedure Betriebstypen;
		procedure DomWerte;
		procedure Zulassungslistencodes;
		procedure Tierarten;
		procedure Zulassungstypen;
		procedure Schluesseltypen;
    procedure prbkat;
    procedure zulassungsnr;
    procedure FunktionenKonfigurieren;
	public
    destructor Destroy; override;
	end;

implementation

uses dmmain, Funktionen, ServerController, StrUtils, ELKE.Classes.RESTError;

{$R *.dfm}

procedure TFormVisDaten.IWAppFormCreate(Sender: TObject);
begin
	iwtVisdaten.ActivePage := 0;
  FunktionenKonfigurieren;
end;

destructor TFormVisDaten.Destroy;
begin
  aktiveTabs.Free;
  inherited;
end;

/// Blendet nur die Tabs ein für die der Benutzer eine Berechtigung hat
procedure TFormVisDaten.FunktionenKonfigurieren;
var
  funktionen: TFunktionenManager;
begin
  funktionen := Usersession.FunktionenManager;
  aktiveTabs := TList<String>.Create;

  // Anzeigen
  if funktionen.HatFunktion(Betriebsstammdaten_anzeigen) then
  begin
    TabBetriebsstammdaten.Visible := true;
    aktiveTabs.Add('0');
  end;
  if funktionen.HatFunktion(Zulassungen_anzeigen) then
  begin
    TabZulassungen.Visible := true;
    aktiveTabs.Add('1');
  end;
  if funktionen.HatFunktion(Betriebstypen_anzeigen) then
  begin
    TabBetriebstypen.Visible := true;
    aktiveTabs.Add('2');
  end;
  if funktionen.HatFunktion(DOM_Werte_anzeigen) then
  begin
    TabDOMWerte.Visible := true;
    aktiveTabs.Add('3');
  end;
  if funktionen.HatFunktion(Zulassungslistencodes_anzeigen) then
  begin
    TabZulassungslistencodes.Visible := true;
    aktiveTabs.Add('4');
  end;
  if funktionen.HatFunktion(Tierarten_VIS_anzeigen) then
  begin
    TabTierarten.Visible := true;
    aktiveTabs.Add('5');
  end;
  if funktionen.HatFunktion(Zulassungstypen_anzeigen) then
  begin
    TabZulassungstypen.Visible := true;
    aktiveTabs.Add('6');
  end;
  if funktionen.HatFunktion(Schlüsseltypen_anzeigen) then
  begin
    TabSchluesseltypen.Visible := true;
    aktiveTabs.Add('7');
  end;
  if funktionen.HatFunktion(PRBKAT_anzeigen) then
  begin
    TabPrbkat.Visible := true;
    aktiveTabs.Add('8');
  end;
  if funktionen.HatFunktion(Zulassungsnummern_anzeigen) then
  begin
    TabZulassungsnummern.Visible := true;
    aktiveTabs.Add('9');
  end;
end;

procedure TFormVisDaten.iwtVisdatenChange(Sender: TObject);
begin
	case IndexText(aktiveTabs[iwtVisdaten.ActivePage],
    ['0', '1', '2',
    '3', '4', '5', '6',
    '7', '8', '9']) of
		0: Betriebsstammdaten;
		1: Zulassungen;
		2: Betriebstypen;
		3: DomWerte;
		4: Zulassungslistencodes;
		5: Tierarten;
		6: Zulassungstypen;
		7: Schluesseltypen;
    8: prbkat;
    9: zulassungsnr;
	end;
end;

procedure TFormVisDaten.iwb_zurueckClick(Sender: TObject);
begin
	Release;
end;

procedure TFormVisDaten.iwcgb_sucheBetriebstypJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: String;
begin
try
	dm_main.qu_VisBetriebstypen.close;
	dm_main.qu_VisBetriebstypen.sql.Clear;
	sql := 'SELECT top 200 * FROM visdaten.VIS_Betriebstypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_betriebstypen_sucheBezeichnung.Text)) + '%''  ';
	if trim(Edit_Betriebstypen_IDsuche.Text) <> '' then
		sql := sql + 'and upper(ID) like ' + '''%' + UpperCase(trim(Edit_Betriebstypen_IDsuche.Text)) + '%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_VisBetriebstypen.sql.Add(sql);
	dm_main.qu_VisBetriebstypen.Prepare;
	dm_main.qu_VisBetriebstypen.Active := true;
	iwl_betriebstypen_count.Caption := IntToStr(dm_main.qu_VisBetriebstypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheDOMwertJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: String;
begin
try
	dm_main.qu_VisDOM.close;
	dm_main.qu_VisDOM.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_DOMWert where ';
	sql := sql + 'upper(TEXT) like ' + '''%' + UpperCase(trim(Edit_Domwert_Textsuche.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_betriebstyp_idsuche.Text))+'%''  ';
	sql := sql + ' order by text ';
	dm_main.qu_VisDOM.sql.Add(sql);
	dm_main.qu_VisDOM.Prepare;
	dm_main.qu_VisDOM.Active := true;
	iwl_domwert_count.Caption := IntToStr(dm_main.qu_VisDOM.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheListencodesJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: string;
begin
try
	dm_main.qu_Vislistencodes.close;
	dm_main.qu_Vislistencodes.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Zulassungslistencodes where ';
	sql := sql + 'upper(betriebstyp) like ' + '''%' + UpperCase(trim(Edit_Listencodes_Betriebstypsuche.Text)) + '%''  ';
	if trim(Edit_Listencodes_Codesuche.Text) <> '' then
		sql := sql + 'and upper(Code) like ' + '''%' + UpperCase(trim(Edit_Listencodes_Codesuche.Text)) + '%''  ';
	sql := sql + ' order by betriebstyp ';
	dm_main.qu_Vislistencodes.sql.Add(sql);
	dm_main.qu_Vislistencodes.Prepare;
	dm_main.qu_Vislistencodes.Active := true;
	iwl_listencodes_count.Caption := IntToStr(dm_main.qu_Vislistencodes.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheSchluesseltypenJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: string;
begin
try
	dm_main.qu_visSchluesseltypen.close;
	dm_main.qu_visSchluesseltypen.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Schluesseltypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_Schluesseltypen_sucheBezeichnung.Text)) + '%''  ';
	// if trim(Edit_listencodes_codesuche.Text) <> '' then
	// sql := sql + 'and upper(Code) like '+'''%'+ UpperCase(trim(Edit_listencodes_codesuche.Text))+'%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_visSchluesseltypen.sql.Add(sql);
	dm_main.qu_visSchluesseltypen.Prepare;
	dm_main.qu_visSchluesseltypen.Active := true;
	iwl_Schluesseltypen_count.Caption := IntToStr(dm_main.qu_visSchluesseltypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheStammdatenJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: string;
begin
try
	dm_main.qu_VisBStammdaten.close;
	dm_main.qu_VisBStammdaten.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_BETRIEBSSTAMMDATEN where ';
	sql := sql + 'upper(name) like ' + '''%' + UpperCase(trim(Edit_su_Name1_betrest.Text)) + '%''  ';
	if trim(Edit_su_regnr_betrest.Text) <> '' then
		sql := sql + 'and upper(REGNR) like ' + '''%' + UpperCase(trim(Edit_su_regnr_betrest.Text)) + '%''  ';
	if trim(Edit_BStammdaten_sucheStrasse.Text) <> '' then
		sql := sql + 'and upper(strasse) like ' + '''%' + UpperCase(trim(Edit_BStammdaten_sucheStrasse.Text)) + '%''  ';
	if trim(Edit_BStammdaten_sucheHnr.Text) <> '' then
		sql := sql + 'and upper(hnr) like ' + '''%' + UpperCase(trim(Edit_BStammdaten_sucheHnr.Text)) + '%''  ';
	sql := sql + ' order by regnr ';
	dm_main.qu_VisBStammdaten.sql.Add(sql);
 	dm_main.qu_VisBStammdaten.Prepare;
 	dm_main.qu_VisBStammdaten.Active := true;
	IWL_Betriebsst_count.Caption := IntToStr(dm_main.qu_VisBStammdaten.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheTierartJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: string;
begin
try
	dm_main.qu_vistierarten.close;
	dm_main.qu_vistierarten.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Tierarten where ';
	sql := sql + 'upper(tierart) like ' + '''%' + UpperCase(trim(Edit_tierartsuche.Text)) + '%''  ';
	// if trim(Edit_listencodes_codesuche.Text) <> '' then
	// sql := sql + 'and upper(Code) like '+'''%'+ UpperCase(trim(Edit_listencodes_codesuche.Text))+'%''  ';
	sql := sql + ' order by tierart ';
	dm_main.qu_vistierarten.sql.Add(sql);
	dm_main.qu_vistierarten.Prepare;
	dm_main.qu_vistierarten.Active := true;
	iwl_tierart_count.Caption := IntToStr(dm_main.qu_vistierarten.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_sucheZulassungJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: string;
begin
try
	dm_main.qu_VisZulassungen.close;
	dm_main.qu_VisZulassungen.sql.Clear;
	sql := 'SELECT TOP 200 * FROM visdaten.VIS_Zulassungen where ';
	sql := sql + 'upper(regnr) like ' + '''%' + UpperCase(trim(Edit_zulassung_sucheRegnr.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_su_regnr_betrest.Text))+'%''  ';
	sql := sql + ' order by regnr ';
	dm_main.qu_VisZulassungen.sql.Add(sql);
 //	dm_main.qu_VisZulassungen.Prepare;
	//dm_main.qu_VisZulassungen.Active := true;
  dm_main.qu_VisZulassungen.Open;
	iwl_zulassungen_count.Caption := IntToStr(dm_main.qu_VisZulassungen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;

end;

procedure TFormVisDaten.iwcgb_sucheZulassungstypenJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: String;
begin
try
	dm_main.qu_visZulassungstypen.close;
	dm_main.qu_visZulassungstypen.sql.Clear;
	sql := 'SELECT TOP 200 * FROM visdaten.VIS_Zulassungstypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_Zulassungstypen_sucheBezeichnung.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_betriebstyp_idsuche.Text))+'%''  ';
	sql := sql + ' order by bezeichnung ';
	dm_main.qu_visZulassungstypen.sql.Add(sql);
	dm_main.qu_visZulassungstypen.Prepare;
	dm_main.qu_visZulassungstypen.Active := true;
	iwl_Zulassungstypen_count.Caption := IntToStr(dm_main.qu_visZulassungstypen.RecordCount);
  iwcgprov_zulassungen.DataSet:=dm_main.qu_VisZulassungen;
  iwcggrid_zulassungen.JQGridProvider:=iwcgprov_zulassungen;
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_suche_prbkatJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: String;
begin
try
	dm_main.qu_visprbkat.close;
	dm_main.qu_visprbkat.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_prbkat where ';
	sql := sql + 'upper(tierart) like ' + '''%' + UpperCase(trim(Edit_prbkat_Tierart.Text)) + '%''  ';
	if trim(Edit_Betriebstypen_IDsuche.Text) <> '' then
		sql := sql + 'and upper(kateg) like ' + '''%' + UpperCase(trim(Edit_prbkat_kateg.Text)) + '%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_visprbkat.sql.Add(sql);
	dm_main.qu_visprbkat.Prepare;
	dm_main.qu_visprbkat.Active := true;
	iwcgl_prbkat_count.Caption := IntToStr(dm_main.qu_VisBetriebstypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwcgb_suche_zulassungsnrJQButtonOptionsClick(
  Sender: TObject; AParams: TStringList);
var
	sql: String;
begin
try
	dm_main.qu_visZulassungsnr.close;
	dm_main.qu_visZulassungsnr.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Zulassungsnummern where ';
	sql := sql + 'upper(regnr) like ' + '''%' + UpperCase(trim(Edit_zulnr_regnr.Text)) + '%''  ';
	if trim(Edit_Betriebstypen_IDsuche.Text) <> '' then
		sql := sql + 'and upper(zulassungsnummer) like ' + '''%' + UpperCase(trim(Edit_zulnr_zulnr.Text)) + '%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_visZulassungsnr.sql.Add(sql);
	dm_main.qu_visZulassungsnr.Prepare;
	dm_main.qu_visZulassungsnr.Active := true;
	iwl_zulassungsnr_count.Caption := IntToStr(dm_main.qu_visZulassungsnr.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.IWCGJQButton1JQButtonOptionsClick(Sender: TObject;
  AParams: TStringList);
begin
	release;
end;

procedure TFormVisDaten.iwi_loesche_zulnrsucheClick(Sender: TObject);
begin
	zulassungsnr;
end;

procedure TFormVisDaten.iwb_Zulassungstypen_sucheClick(Sender: TObject); // Zulassungstypen
var
	sql: String;
begin
try
	dm_main.qu_visZulassungstypen.close;
	dm_main.qu_visZulassungstypen.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Zulassungstypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_Zulassungstypen_sucheBezeichnung.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_betriebstyp_idsuche.Text))+'%''  ';
	sql := sql + ' order by bezeichnung ';
	dm_main.qu_visZulassungstypen.sql.Add(sql);
	dm_main.qu_visZulassungstypen.Prepare;
	dm_main.qu_visZulassungstypen.Active := true;
	iwl_Zulassungstypen_count.Caption := IntToStr(dm_main.qu_visZulassungstypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;

end;

procedure TFormVisDaten.IWB_suchenBetriebsstammdatenClick(Sender: TObject); // Stammdaten
var
	sql: string;
begin
try
	dm_main.qu_VisBStammdaten.close;
	dm_main.qu_VisBStammdaten.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_BETRIEBSSTAMMDATEN where ';
	sql := sql + 'upper(name) like ' + '''%' + UpperCase(trim(Edit_su_Name1_betrest.Text)) + '%''  ';
	if trim(Edit_su_regnr_betrest.Text) <> '' then
		sql := sql + 'and upper(REGNR) like ' + '''%' + UpperCase(trim(Edit_su_regnr_betrest.Text)) + '%''  ';
	if trim(Edit_BStammdaten_sucheStrasse.Text) <> '' then
		sql := sql + 'and upper(strasse) like ' + '''%' + UpperCase(trim(Edit_BStammdaten_sucheStrasse.Text)) + '%''  ';
	if trim(Edit_BStammdaten_sucheHnr.Text) <> '' then
		sql := sql + 'and upper(hnr) like ' + '''%' + UpperCase(trim(Edit_BStammdaten_sucheHnr.Text)) + '%''  ';
	sql := sql + ' order by regnr ';
	dm_main.qu_VisBStammdaten.sql.Add(sql);
 	dm_main.qu_VisBStammdaten.Prepare;
 	dm_main.qu_VisBStammdaten.Active := true;
	IWL_Betriebsst_count.Caption := IntToStr(dm_main.qu_VisBStammdaten.RecordCount);
  refresh;
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwb_suche_betriebstypenClick(Sender: TObject); // Betriebstypen
var
	sql: String;
begin
try
	dm_main.qu_VisBetriebstypen.close;
	dm_main.qu_VisBetriebstypen.sql.Clear;
	sql := 'SELECT Top 200 * FROM Visdaten.VIS_Betriebstypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_betriebstypen_sucheBezeichnung.Text)) + '%''  ';
	if trim(Edit_Betriebstypen_IDsuche.Text) <> '' then
		sql := sql + 'and upper(ID) like ' + '''%' + UpperCase(trim(Edit_Betriebstypen_IDsuche.Text)) + '%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_VisBetriebstypen.sql.Add(sql);
	dm_main.qu_VisBetriebstypen.Prepare;
	dm_main.qu_VisBetriebstypen.Active := true;
	iwl_betriebstypen_count.Caption := IntToStr(dm_main.qu_VisBetriebstypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.IWB_suche_DomwertClick(Sender: TObject); // Domwerte
var
	sql: String;
begin
try
	dm_main.qu_VisDOM.close;
	dm_main.qu_VisDOM.sql.Clear;
	sql := 'SELECT TOP 200 * FROM visdaten.VIS_DOMWert where ';
	sql := sql + 'upper(TEXT) like ' + '''%' + UpperCase(trim(Edit_Domwert_Textsuche.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_betriebstyp_idsuche.Text))+'%''  ';
	sql := sql + ' order by text ';
	dm_main.qu_VisDOM.sql.Add(sql);
	dm_main.qu_VisDOM.Prepare;
	dm_main.qu_VisDOM.Active := true;
	iwl_domwert_count.Caption := IntToStr(dm_main.qu_VisDOM.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwi_loesche_BetriebstypensucheClick(Sender: TObject);
begin
	Betriebstypen;
end;

procedure TFormVisDaten.iwi_loesche_DOMwertsucheClick(Sender: TObject);
begin
	DomWerte;
end;

procedure TFormVisDaten.iwi_loesche_ListencodesucheClick(Sender: TObject);
begin
	Zulassungslistencodes;
end;

procedure TFormVisDaten.iwi_loesche_prbkatsucheClick(Sender: TObject);
begin
	prbkat;
end;

procedure TFormVisDaten.iwi_Loesche_TierartsucheClick(Sender: TObject);
begin
	Tierarten;
end;

procedure TFormVisDaten.iwi_loesche_ZulassungssucheClick(Sender: TObject);
begin
	Zulassungen;
end;

procedure TFormVisDaten.iwi_Loesche_ZulassungstypensucheClick(Sender: TObject);
begin
	Zulassungstypen;
end;

procedure TFormVisDaten.iwi_schluesseltypensuche_loeschenClick(Sender: TObject);
begin
	Schluesseltypen;
end;

procedure TFormVisDaten.iwb_suche_ListencodesClick(Sender: TObject);
var
	sql: string;
begin
try
	dm_main.qu_Vislistencodes.close;
	dm_main.qu_Vislistencodes.sql.Clear;
	sql := 'SELECT Top 200 * FROM visdaten.VIS_Zulassungslistencodes where ';
	sql := sql + 'upper(betriebstyp) like ' + '''%' + UpperCase(trim(Edit_Listencodes_Betriebstypsuche.Text)) + '%''  ';
	if trim(Edit_Listencodes_Codesuche.Text) <> '' then
		sql := sql + 'and upper(Code) like ' + '''%' + UpperCase(trim(Edit_Listencodes_Codesuche.Text)) + '%''  ';
	sql := sql + ' order by betriebstyp ';
	dm_main.qu_Vislistencodes.sql.Add(sql);
	dm_main.qu_Vislistencodes.Prepare;
	dm_main.qu_Vislistencodes.Active := true;
	iwl_listencodes_count.Caption := IntToStr(dm_main.qu_Vislistencodes.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwb_suche_schluesseltypenClick(Sender: TObject);
var
	sql: string;
begin
try
	dm_main.qu_visSchluesseltypen.close;
	dm_main.qu_visSchluesseltypen.sql.Clear;
	sql := 'SELECT TOP 200 * FROM visdaten.VIS_Schluesseltypen where ';
	sql := sql + 'upper(Bezeichnung) like ' + '''%' + UpperCase(trim(Edit_Schluesseltypen_sucheBezeichnung.Text)) + '%''  ';
	// if trim(Edit_listencodes_codesuche.Text) <> '' then
	// sql := sql + 'and upper(Code) like '+'''%'+ UpperCase(trim(Edit_listencodes_codesuche.Text))+'%''  ';
	sql := sql + ' order by id ';
	dm_main.qu_visSchluesseltypen.sql.Add(sql);
	dm_main.qu_visSchluesseltypen.Prepare;
	dm_main.qu_visSchluesseltypen.Active := true;
	iwl_Schluesseltypen_count.Caption := IntToStr(dm_main.qu_visSchluesseltypen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwb_tierart_suchenClick(Sender: TObject);
var
	sql: string;
begin
try
	dm_main.qu_vistierarten.close;
	dm_main.qu_vistierarten.sql.Clear;
	sql := 'SELECT TOP 200 * FROM visdaten.VIS_Tierarten where ';
	sql := sql + 'upper(tierart) like ' + '''%' + UpperCase(trim(Edit_tierartsuche.Text)) + '%''  ';
	// if trim(Edit_listencodes_codesuche.Text) <> '' then
	// sql := sql + 'and upper(Code) like '+'''%'+ UpperCase(trim(Edit_listencodes_codesuche.Text))+'%''  ';
	sql := sql + ' order by tierart ';
	dm_main.qu_vistierarten.sql.Add(sql);
	dm_main.qu_vistierarten.Prepare;
	dm_main.qu_vistierarten.Active := true;
	iwl_tierart_count.Caption := IntToStr(dm_main.qu_vistierarten.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.iwi_loeschen_StammdatensucheClick(Sender: TObject);
begin
	Betriebsstammdaten
end;

procedure TFormVisDaten.iwb_sucheZulassungClick(Sender: TObject);
var
	sql: string;
begin
try
	dm_main.qu_VisZulassungen.close;
	dm_main.qu_VisZulassungen.sql.Clear;
	sql := 'SELECT top 200 * FROM visdaten.VIS_Zulassungen where ';
	sql := sql + 'upper(regnr) like ' + '''%' + UpperCase(trim(Edit_zulassung_sucheRegnr.Text)) + '%''  ';
	// if trim(Edit_su_regnr_betrest.Text) <> '' then
	// sql := sql + 'and upper(REGNR) like '+'''%'+ UpperCase(trim(Edit_su_regnr_betrest.Text))+'%''  ';
	sql := sql + ' order by regnr ';
	dm_main.qu_VisZulassungen.sql.Add(sql);
	dm_main.qu_VisZulassungen.Prepare;
	dm_main.qu_VisZulassungen.Active := true;
	iwl_zulassungen_count.Caption := IntToStr(dm_main.qu_VisZulassungen.RecordCount);
except
		iwcgsa_Visdaten.Error('Fehler bei SQL-Abfrage!', 'Bitte probieren Sie es erneut oder Starten Sie eine neue Sitzung!');
end;
end;

procedure TFormVisDaten.Betriebsstammdaten;
begin
Edit_su_Name1_betrest.Text := '';
Edit_su_regnr_betrest.Text := '';
Edit_BStammdaten_sucheStrasse.Text := '';
Edit_BStammdaten_sucheHnr.Text := '';
	IWL_Betriebsst_count.Caption := '-';
 Edit_su_Name1_betrest.Text := '#+-';
  IWB_suchenBetriebsstammdatenClick(self);
  Edit_su_Name1_betrest.Text := '';
end;

procedure TFormVisDaten.Zulassungen;
begin
Edit_zulassung_sucheRegnr.Text := '';
	dm_main.qu_VisZulassungen.close;
	iwl_zulassungen_count.Caption := '-';
end;

procedure TFormVisDaten.Betriebstypen;
begin
Edit_betriebstypen_sucheBezeichnung.Text := '';
Edit_Betriebstypen_IDsuche.Text := '';
	dm_main.qu_VisBetriebstypen.close;
	iwl_betriebstypen_count.Caption := '-';
end;

procedure TFormVisDaten.DomWerte;
begin
Edit_Domwert_Textsuche.Text := '';
	dm_main.qu_VisDOM.close;
	iwl_domwert_count.Caption := '-';
end;

procedure TFormVisDaten.Zulassungslistencodes;
begin
Edit_Listencodes_Betriebstypsuche.Text := '';
	dm_main.qu_Vislistencodes.close;
	iwl_listencodes_count.Caption := '-';
end;

procedure TFormVisDaten.Tierarten;
begin
Edit_tierartsuche.Text := '';
	dm_main.qu_vistierarten.close;
	iwl_tierart_count.Caption := '-';
end;

procedure TFormVisDaten.Zulassungstypen;
begin
Edit_Zulassungstypen_sucheBezeichnung.Text := '';
	dm_main.qu_visZulassungstypen.close;
	iwl_Zulassungstypen_count.Caption := '-'
end;

procedure TFormVisDaten.Schluesseltypen;
begin
Edit_Schluesseltypen_sucheBezeichnung.Text := '';
	dm_main.qu_visSchluesseltypen.close;
	iwl_Schluesseltypen_count.Caption := '-';
end;

procedure TFormVisDaten.prbkat;
begin
   Edit_prbkat_tierart.Text:='';
    Edit_prbkat_kateg.Text:='';
	dm_main.qu_visprbkat.close;
	iwl_Zulassungstypen_count.Caption := '-'
end;

procedure TFormVisDaten.zulassungsnr;
begin
 Edit_zulnr_regnr.Text := '';
 Edit_zulnr_zulnr.Text := '';
	dm_main.qu_viszulassungsnr.close;
	iwl_Zulassungsnr_count.Caption := '-'
end;

end.
