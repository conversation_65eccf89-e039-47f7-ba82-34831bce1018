object FormMain: TFormMain
  Left = 0
  Top = 0
  Caption = 'ELKE - Bilder'
  ClientHeight = 658
  ClientWidth = 920
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  PixelsPerInch = 96
  TextHeight = 13
  object Panel1: TPanel
    Left = 0
    Top = 617
    Width = 920
    Height = 41
    Align = alBottom
    Caption = 'Panel1'
    ShowCaption = False
    TabOrder = 0
    object Button1: TButton
      Left = 786
      Top = 1
      Width = 133
      Height = 39
      Align = alRight
      Caption = 'Laden ...'
      TabOrder = 0
      OnClick = Button1Click
    end
  end
  object PageControl1: TPageControl
    Left = 0
    Top = 0
    Width = 920
    Height = 617
    ActivePage = TabSheet4
    Align = alClient
    TabOrder = 1
    object TabSheet1: TTabSheet
      Caption = 'Kontrollbericht Bilder'
      object DBGrid1: TDBGrid
        Left = 0
        Top = 0
        Width = 514
        Height = 589
        Align = alClient
        DataSource = DSBilder
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Columns = <
          item
            Expanded = False
            FieldName = 'ID'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'FORMAT'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_BEWERTETEFRAGE'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_MANGEL'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_PROBE'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'AUFNAHMEDATUM'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_AUFGENOMMEN_VON'
            Width = 100
            Visible = True
          end>
      end
      object Panel2: TPanel
        Left = 514
        Top = 0
        Width = 398
        Height = 589
        Align = alRight
        Caption = 'Panel2'
        TabOrder = 1
        object DBImage1: TDBImage
          Left = 1
          Top = 90
          Width = 396
          Height = 498
          Align = alClient
          DataField = 'BILD'
          DataSource = DSBilder
          Proportional = True
          Stretch = True
          TabOrder = 0
        end
        object DBMemo1: TDBMemo
          Left = 1
          Top = 1
          Width = 396
          Height = 89
          Align = alTop
          DataField = 'BEMERKUNG'
          DataSource = DSBilder
          TabOrder = 1
        end
      end
    end
    object TabSheet2: TTabSheet
      Caption = 'Proben'
      ImageIndex = 1
      object DBGrid2: TDBGrid
        Left = 0
        Top = 0
        Width = 912
        Height = 145
        Align = alTop
        DataSource = DSProben
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Columns = <
          item
            Expanded = False
            FieldName = 'ID'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'GUID'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_KONTROLLBERICHT'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'BKBTYP'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'PROBENBKB'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'PROBENART'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'BEMERKUNG'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'DATUM'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_EINSENDER'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VORG_MENGE'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'BESCHAFFENHEIT'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'FUTTERTYP'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VERWENDUNGSZWECK'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'TIER_ART_LISA'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'TIER_KATEGORIE'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'BEIMISCHRATE'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VERPACKUNG'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VERSCHLUSS'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VERSIEGELT'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'HERK_ZUKAUF'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'UNTERSUCHUNGSAUFTRAG'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'STATUS'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'VERDACHT'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'GEGENPROBE_BELASSEN'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'EXPORTNAME'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'EXPORTTIME'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'AGESAUFTRAGSNUMMER'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'AGESPROBENNUMMER'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'AGES_AUFTRAGSSTATUS'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'AGES_PROBENSTATUS'
            Width = 100
            Visible = True
          end>
      end
      object MemoXML: TDBMemo
        Left = 0
        Top = 145
        Width = 425
        Height = 444
        Align = alLeft
        DataField = 'Ergebnis_XML'
        DataSource = DSProbenDetails
        TabOrder = 1
      end
    end
    object TabSheet3: TTabSheet
      Caption = 'Unterschriften'
      ImageIndex = 2
      object DBGrid3: TDBGrid
        Left = 0
        Top = 0
        Width = 912
        Height = 249
        Align = alTop
        DataSource = DSUnterschriften
        TabOrder = 0
        TitleFont.Charset = DEFAULT_CHARSET
        TitleFont.Color = clWindowText
        TitleFont.Height = -11
        TitleFont.Name = 'Tahoma'
        TitleFont.Style = []
        Columns = <
          item
            Expanded = False
            FieldName = 'GUID'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'DATUM'
            Width = 100
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'NAME'
            Width = 200
            Visible = True
          end
          item
            Expanded = False
            FieldName = 'ID_PERSON'
            Width = 100
            Visible = True
          end>
      end
      object DBImage2: TDBImage
        Left = 0
        Top = 249
        Width = 912
        Height = 340
        Align = alClient
        DataField = 'Bild'
        DataSource = DSUnterschriftenDetails
        TabOrder = 1
      end
    end
    object TabSheet4: TTabSheet
      Caption = 'Dokumente'
      ImageIndex = 3
    end
  end
  object FDConnection1: TFDConnection
    Params.Strings = (
      'Server=10.10.0.102'
      'User_Name=BBO'
      'Password=Esc1234!'
      'Database=ELKEDB'
      'DriverID=MSSQL')
    FetchOptions.AssignedValues = [evItems, evCache]
    FetchOptions.Items = [fiDetails, fiMeta]
    FetchOptions.Cache = [fiDetails, fiMeta]
    ConnectedStoredUsage = []
    Connected = True
    Left = 88
    Top = 96
  end
  object QBilder: TFDQuery
    AfterScroll = QBilderAfterScroll
    Connection = FDConnection1
    SQL.Strings = (
      'select * from BEWEGUNGSDATEN.KONTROLLBERICHT_BILDER'
      'order by AUFNAHMEDATUM desc')
    Left = 224
    Top = 96
    object QBilderID: TGuidField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QBilderBILD: TBlobField
      FieldName = 'BILD'
      Origin = 'BILD'
      Required = True
      Size = **********
    end
    object QBilderFORMAT: TStringField
      FieldName = 'FORMAT'
      Origin = 'FORMAT'
      Required = True
      FixedChar = True
      Size = 3
    end
    object QBilderID_BEWERTETEFRAGE: TIntegerField
      FieldName = 'ID_BEWERTETEFRAGE'
      Origin = 'ID_BEWERTETEFRAGE'
    end
    object QBilderID_MANGEL: TIntegerField
      FieldName = 'ID_MANGEL'
      Origin = 'ID_MANGEL'
    end
    object QBilderID_PROBE: TIntegerField
      FieldName = 'ID_PROBE'
      Origin = 'ID_PROBE'
    end
    object QBilderBEMERKUNG: TMemoField
      FieldName = 'BEMERKUNG'
      Origin = 'BEMERKUNG'
      Required = True
      BlobType = ftMemo
      Size = **********
    end
    object QBilderAUFNAHMEDATUM: TSQLTimeStampField
      FieldName = 'AUFNAHMEDATUM'
      Origin = 'AUFNAHMEDATUM'
      Required = True
    end
    object QBilderID_AUFGENOMMEN_VON: TIntegerField
      FieldName = 'ID_AUFGENOMMEN_VON'
      Origin = 'ID_AUFGENOMMEN_VON'
      Required = True
    end
  end
  object DSBilder: TDataSource
    DataSet = QBilder
    Left = 224
    Top = 168
  end
  object QProben: TFDQuery
    Connection = FDConnection1
    SQL.Strings = (
      'SELECT [ID]'
      '      ,[GUID]'
      '      ,[ID_KONTROLLBERICHT]'
      '      ,[BKBTYP]'
      '      ,[PROBENBKB]'
      '      ,[PROBENART]'
      '      ,[BEMERKUNG]'
      '      ,[DATUM]'
      '      ,[ID_EINSENDER]'
      '      ,[VORG_MENGE]'
      '      ,[BESCHAFFENHEIT]'
      '      ,[FUTTERTYP]'
      '      ,[VERWENDUNGSZWECK]'
      '      ,[TIER_ART_LISA]'
      '      ,[TIER_KATEGORIE]'
      '      ,[BEIMISCHRATE]'
      '      ,[VERPACKUNG]'
      '      ,[VERSCHLUSS]'
      '      ,[VERSIEGELT]'
      '      ,[HERK_ZUKAUF]'
      '      ,[UNTERSUCHUNGSAUFTRAG]'
      '      ,[STATUS]'
      '      ,[VERDACHT]'
      '      ,[GEGENPROBE_BELASSEN]'
      '      ,[EXPORTNAME]'
      '      ,[EXPORTTIME]'
      '      ,[AGESAUFTRAGSNUMMER]'
      '      ,[AGESPROBENNUMMER]'
      '      ,[AGES_AUFTRAGSSTATUS]'
      '      ,[AGES_PROBENSTATUS]'
      '  FROM [BEWEGUNGSDATEN].[KB_PROBEN]'
      '')
    Left = 344
    Top = 96
    object QProbenID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QProbenGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      Required = True
      Size = 38
    end
    object QProbenID_KONTROLLBERICHT: TIntegerField
      FieldName = 'ID_KONTROLLBERICHT'
      Origin = 'ID_KONTROLLBERICHT'
      Required = True
    end
    object QProbenBKBTYP: TStringField
      FieldName = 'BKBTYP'
      Origin = 'BKBTYP'
      Size = 10
    end
    object QProbenPROBENBKB: TStringField
      FieldName = 'PROBENBKB'
      Origin = 'PROBENBKB'
      Size = 26
    end
    object QProbenPROBENART: TStringField
      FieldName = 'PROBENART'
      Origin = 'PROBENART'
      Size = 50
    end
    object QProbenBEMERKUNG: TStringField
      FieldName = 'BEMERKUNG'
      Origin = 'BEMERKUNG'
      Size = 250
    end
    object QProbenDATUM: TDateField
      FieldName = 'DATUM'
      Origin = 'DATUM'
    end
    object QProbenID_EINSENDER: TIntegerField
      FieldName = 'ID_EINSENDER'
      Origin = 'ID_EINSENDER'
    end
    object QProbenVORG_MENGE: TStringField
      FieldName = 'VORG_MENGE'
      Origin = 'VORG_MENGE'
      Size = 50
    end
    object QProbenBESCHAFFENHEIT: TStringField
      FieldName = 'BESCHAFFENHEIT'
      Origin = 'BESCHAFFENHEIT'
      Size = 50
    end
    object QProbenFUTTERTYP: TStringField
      FieldName = 'FUTTERTYP'
      Origin = 'FUTTERTYP'
      Size = 50
    end
    object QProbenVERWENDUNGSZWECK: TStringField
      FieldName = 'VERWENDUNGSZWECK'
      Origin = 'VERWENDUNGSZWECK'
      Size = 80
    end
    object QProbenTIER_ART_LISA: TStringField
      FieldName = 'TIER_ART_LISA'
      Origin = 'TIER_ART_LISA'
      Size = 50
    end
    object QProbenTIER_KATEGORIE: TStringField
      FieldName = 'TIER_KATEGORIE'
      Origin = 'TIER_KATEGORIE'
      Size = 50
    end
    object QProbenBEIMISCHRATE: TBCDField
      FieldName = 'BEIMISCHRATE'
      Origin = 'BEIMISCHRATE'
      Precision = 18
    end
    object QProbenVERPACKUNG: TStringField
      FieldName = 'VERPACKUNG'
      Origin = 'VERPACKUNG'
      Size = 50
    end
    object QProbenVERSCHLUSS: TStringField
      FieldName = 'VERSCHLUSS'
      Origin = 'VERSCHLUSS'
      Size = 50
    end
    object QProbenVERSIEGELT: TStringField
      FieldName = 'VERSIEGELT'
      Origin = 'VERSIEGELT'
      Size = 5
    end
    object QProbenHERK_ZUKAUF: TStringField
      FieldName = 'HERK_ZUKAUF'
      Origin = 'HERK_ZUKAUF'
      Size = 250
    end
    object QProbenUNTERSUCHUNGSAUFTRAG: TStringField
      FieldName = 'UNTERSUCHUNGSAUFTRAG'
      Origin = 'UNTERSUCHUNGSAUFTRAG'
      Size = 250
    end
    object QProbenSTATUS: TStringField
      FieldName = 'STATUS'
      Origin = 'STATUS'
      Required = True
      FixedChar = True
      Size = 1
    end
    object QProbenVERDACHT: TStringField
      FieldName = 'VERDACHT'
      Origin = 'VERDACHT'
      Size = 250
    end
    object QProbenGEGENPROBE_BELASSEN: TBooleanField
      FieldName = 'GEGENPROBE_BELASSEN'
      Origin = 'GEGENPROBE_BELASSEN'
      Required = True
    end
    object QProbenEXPORTNAME: TStringField
      FieldName = 'EXPORTNAME'
      Origin = 'EXPORTNAME'
      Size = 50
    end
    object QProbenEXPORTTIME: TSQLTimeStampField
      FieldName = 'EXPORTTIME'
      Origin = 'EXPORTTIME'
    end
    object QProbenAGESAUFTRAGSNUMMER: TStringField
      FieldName = 'AGESAUFTRAGSNUMMER'
      Origin = 'AGESAUFTRAGSNUMMER'
      Size = 50
    end
    object QProbenAGESPROBENNUMMER: TStringField
      FieldName = 'AGESPROBENNUMMER'
      Origin = 'AGESPROBENNUMMER'
      Size = 50
    end
    object QProbenAGES_AUFTRAGSSTATUS: TStringField
      FieldName = 'AGES_AUFTRAGSSTATUS'
      Origin = 'AGES_AUFTRAGSSTATUS'
      Size = 50
    end
    object QProbenAGES_PROBENSTATUS: TStringField
      FieldName = 'AGES_PROBENSTATUS'
      Origin = 'AGES_PROBENSTATUS'
      Size = 50
    end
  end
  object DSProben: TDataSource
    DataSet = QProben
    Left = 344
    Top = 176
  end
  object QProbenDetails: TFDQuery
    MasterSource = DSProben
    MasterFields = 'ID'
    Connection = FDConnection1
    SQL.Strings = (
      
        'select ID, Ergebnis_XML, Ergebnis_PDF from BEWEGUNGSDATEN.KB_PRO' +
        'BEN'
      'where ID = :ID')
    Left = 344
    Top = 248
    ParamData = <
      item
        Name = 'ID'
        DataType = ftString
        ParamType = ptInput
        Value = '-1'
      end>
    object QProbenDetailsID: TFDAutoIncField
      FieldName = 'ID'
      Origin = 'ID'
      ProviderFlags = [pfInWhere, pfInKey]
      ReadOnly = True
    end
    object QProbenDetailsErgebnis_XML: TWideMemoField
      FieldName = 'Ergebnis_XML'
      Origin = 'Ergebnis_XML'
      ProviderFlags = [pfInUpdate]
      BlobType = ftWideMemo
      Size = **********
    end
    object QProbenDetailsErgebnis_PDF: TBlobField
      FieldName = 'Ergebnis_PDF'
      Origin = 'Ergebnis_PDF'
      ProviderFlags = [pfInUpdate]
      Size = **********
    end
  end
  object DSProbenDetails: TDataSource
    DataSet = QProbenDetails
    Left = 344
    Top = 328
  end
  object OpenDialog1: TOpenDialog
    Filter = 'Alle Dokumente|*.pdf;*.jpg;*.png;*.xml'
    Left = 704
    Top = 320
  end
  object QUnterschriften: TFDQuery
    Connection = FDConnection1
    SQL.Strings = (
      'select * from bewegungsdaten.unterschriften'
      'order by Datum desc')
    Left = 456
    Top = 96
    object QUnterschriftenGUID: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QUnterschriftenDATUM: TSQLTimeStampField
      FieldName = 'DATUM'
      Origin = 'DATUM'
      Required = True
    end
    object QUnterschriftenNAME: TStringField
      FieldName = 'NAME'
      Origin = 'NAME'
      Required = True
      Size = 255
    end
    object QUnterschriftenID_PERSON: TIntegerField
      FieldName = 'ID_PERSON'
      Origin = 'ID_PERSON'
    end
  end
  object DSUnterschriften: TDataSource
    DataSet = QUnterschriften
    Left = 456
    Top = 176
  end
  object QUnterschriftenDetails: TFDQuery
    MasterSource = DSUnterschriften
    MasterFields = 'GUID'
    DetailFields = 'GUID'
    Connection = FDConnection1
    FetchOptions.AssignedValues = [evCache]
    FetchOptions.Cache = [fiMeta]
    SQL.Strings = (
      'select Guid, Bild from bewegungsdaten.unterschriften'
      'where guid = :guid')
    Left = 456
    Top = 240
    ParamData = <
      item
        Name = 'GUID'
        DataType = ftGuid
        ParamType = ptInput
        Size = 38
        Value = '{D636FB72-09EF-44C7-B01D-01705F470DCD}'
      end>
    object GuidField1: TGuidField
      FieldName = 'GUID'
      Origin = 'GUID'
      ProviderFlags = [pfInUpdate, pfInWhere, pfInKey]
      Required = True
      Size = 38
    end
    object QUnterschriftenDetailsBild: TBlobField
      FieldName = 'Bild'
      Origin = 'Bild'
      Required = True
      Size = **********
    end
  end
  object DSUnterschriftenDetails: TDataSource
    DataSet = QUnterschriftenDetails
    Left = 456
    Top = 328
  end
  object QDokumente: TFDQuery
    Connection = FDConnection1
    SQL.Strings = (
      'select * from bewegungsdaten.Dokumente')
    Left = 624
    Top = 96
  end
end
