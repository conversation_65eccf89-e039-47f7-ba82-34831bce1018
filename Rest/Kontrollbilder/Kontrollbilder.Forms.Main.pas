unit Kontrollbilder.Forms.Main;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Data.DB, Vcl.DBCtrls, Vcl.Grids, Vcl.DBGrids, FireDAC.Stan.Intf,
  FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.MSSQL, FireDAC.Phys.MSSQLDef, FireDAC.VCLUI.Wait, FireDAC.Comp.Client,
  FireDAC.Stan.Param, FireDAC.DatS, FireDAC.DApt.Intf, FireDAC.DApt, FireDAC.Comp.DataSet, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls;

type
  TFormMain = class(TForm)
    DBGrid1: TDBGrid;
    DBImage1: TDBImage;
    FDConnection1: TFDConnection;
    QBilder: TFDQuery;
    DSBilder: TDataSource;
    Button1: TButton;
    Panel1: TPanel;
    Panel2: TPanel;
    DBMemo1: TDBMemo;
    QBilderID: TGuidField;
    QBilderBILD: TBlobField;
    QBilderFORMAT: TStringField;
    QBilderID_BEWERTETEFRAGE: TIntegerField;
    QBilderID_MANGEL: TIntegerField;
    QBilderID_PROBE: TIntegerField;
    QBilderBEMERKUNG: TMemoField;
    QBilderAUFNAHMEDATUM: TSQLTimeStampField;
    QBilderID_AUFGENOMMEN_VON: TIntegerField;
    QProben: TFDQuery;
    DSProben: TDataSource;
    DBGrid2: TDBGrid;
    QProbenID: TFDAutoIncField;
    QProbenGUID: TGuidField;
    QProbenID_KONTROLLBERICHT: TIntegerField;
    QProbenBKBTYP: TStringField;
    QProbenPROBENBKB: TStringField;
    QProbenPROBENART: TStringField;
    QProbenBEMERKUNG: TStringField;
    QProbenDATUM: TDateField;
    QProbenID_EINSENDER: TIntegerField;
    QProbenVORG_MENGE: TStringField;
    QProbenBESCHAFFENHEIT: TStringField;
    QProbenFUTTERTYP: TStringField;
    QProbenVERWENDUNGSZWECK: TStringField;
    QProbenTIER_ART_LISA: TStringField;
    QProbenTIER_KATEGORIE: TStringField;
    QProbenBEIMISCHRATE: TBCDField;
    QProbenVERPACKUNG: TStringField;
    QProbenVERSCHLUSS: TStringField;
    QProbenVERSIEGELT: TStringField;
    QProbenHERK_ZUKAUF: TStringField;
    QProbenUNTERSUCHUNGSAUFTRAG: TStringField;
    QProbenSTATUS: TStringField;
    QProbenVERDACHT: TStringField;
    QProbenGEGENPROBE_BELASSEN: TBooleanField;
    QProbenEXPORTNAME: TStringField;
    QProbenEXPORTTIME: TSQLTimeStampField;
    QProbenAGESAUFTRAGSNUMMER: TStringField;
    QProbenAGESPROBENNUMMER: TStringField;
    QProbenAGES_AUFTRAGSSTATUS: TStringField;
    QProbenAGES_PROBENSTATUS: TStringField;
    PageControl1: TPageControl;
    TabSheet1: TTabSheet;
    TabSheet2: TTabSheet;
    TabSheet3: TTabSheet;
    QProbenDetails: TFDQuery;
    QProbenDetailsErgebnis_XML: TWideMemoField;
    QProbenDetailsErgebnis_PDF: TBlobField;
    MemoXML: TDBMemo;
    DSProbenDetails: TDataSource;
    OpenDialog1: TOpenDialog;
    QProbenDetailsID: TFDAutoIncField;
    QUnterschriften: TFDQuery;
    QUnterschriftenGUID: TGuidField;
    QUnterschriftenDATUM: TSQLTimeStampField;
    QUnterschriftenNAME: TStringField;
    QUnterschriftenID_PERSON: TIntegerField;
    DSUnterschriften: TDataSource;
    DBGrid3: TDBGrid;
    DBImage2: TDBImage;
    QUnterschriftenDetails: TFDQuery;
    GuidField1: TGuidField;
    DSUnterschriftenDetails: TDataSource;
    QUnterschriftenDetailsBild: TBlobField;
    TabSheet4: TTabSheet;
    QDokumente: TFDQuery;
    procedure Button1Click(Sender: TObject);
    procedure QBilderAfterScroll(DataSet: TDataSet);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

uses
  Vcl.Imaging.jpeg, Vcl.Imaging.pngimage, System.IOUtils;

{$R *.dfm}


procedure TFormMain.Button1Click(Sender: TObject);
begin
  QBilder.Close;
  QBilder.Open;

  QProben.Close;
  QProben.Open;
  QProbenDetails.Open;

  QUnterschriften.Close;
  QUnterschriften.Open;
  QUnterschriftenDetails.Open;
end;

procedure TFormMain.QBilderAfterScroll(DataSet: TDataSet);
begin
  QBilderBILD.SaveToFile('c:\temp\Kontrollbild.jpg');
end;

end.
