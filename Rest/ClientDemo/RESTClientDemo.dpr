program RESTClientDemo;

uses
  Vcl.Forms,
  RESTClientDemo.Forms.Main in 'RESTClientDemo.Forms.Main.pas' {Form86},
  ELKE.Classes.Generated in '..\Classes\ELKE.Classes.Generated.pas',
  ELKE.Services.Me.Intf in '..\Classes\ELKE.Services.Me.Intf.pas',
  ELKE.Classes in '..\Classes\ELKE.Classes.pas',
  ELKE.Classes.Request in '..\Classes\ELKE.Classes.Request.pas',
  ELKE.Classes.PVP.Roles in '..\Classes\ELKE.Classes.PVP.Roles.pas',
  ELKE.Classes.PVP.Token in '..\Classes\ELKE.Classes.PVP.Token.pas',
  DX.Classes.ObjectFactory in '..\dx-library\DX.Classes.ObjectFactory.pas';

{$R *.res}

begin
  ReportMemoryLeaksOnShutdown := true;
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TF<PERSON><PERSON>, Form86);
  Application.Run;
end.
