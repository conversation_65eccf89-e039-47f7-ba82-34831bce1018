﻿program ELKEApiServer;
uses
  FastMM4,
  madExcept,
  madLinkDisAsm,
  madListHardware,
  madListProcesses,
  madListModules,
  WinAPI.Windows,
  Vcl.Forms,
  DX.Utils.Logger.madExcept,
  ELKE.Server.Modules.ServerContainer in 'ELKE.Server.Modules.ServerContainer.pas' {ServerContainer: TDataModule},
  ELKE.Server.Forms.Main in 'ELKE.Server.Forms.Main.pas' {FormMainServer},
  ELKE.Server.Modules.Data in 'ELKE.Server.Modules.Data.pas' {DataContainer: TDataModule},
  Modules.Reports.AMA in '..\..\ReportBuilder\Modules.Reports.AMA.pas',
  Modules.Reports.Base in '..\..\ReportBuilder\Modules.Reports.Base.pas',
  Modules.Reports.ELKE in '..\..\ReportBuilder\Modules.Reports.ELKE.pas',
  ELKE.Classes.Admin in '..\..\ESCore\classes\ELKE.Classes.Admin.pas',
  ELKE.Classes.Generated.Dictionary in '..\..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas',
  ELKE.Classes.Generated in '..\..\ESCore\classes\ELKE.Classes.Generated.pas',
  ELKE.Classes.Logging in '..\..\ESCore\classes\ELKE.Classes.Logging.pas',
  ELKE.Classes in '..\..\ESCore\classes\ELKE.Classes.pas',
  ELKE.Classes.PVP.Roles in '..\..\ESCore\classes\ELKE.Classes.PVP.Roles.pas',
  ELKE.Classes.PVP.Token in '..\..\ESCore\classes\ELKE.Classes.PVP.Token.pas',
  ELKE.Classes.Request in '..\..\ESCore\classes\ELKE.Classes.Request.pas',
  ELKE.Classes.RESTError in '..\..\ESCore\classes\ELKE.Classes.RESTError.pas',
  ELKE.Mail in '..\..\ESCore\classes\ELKE.Mail.pas',
  ELKE.Server.Configuration.Base in '..\..\ESCore\classes\ELKE.Server.Configuration.Base.pas',
  ELKE.Server.Configuration in '..\..\ESCore\classes\ELKE.Server.Configuration.pas',
  ELKE.Server.Logger in '..\..\ESCore\classes\ELKE.Server.Logger.pas',
  ELKE.Server.Modules.Main in '..\..\ESCore\classes\ELKE.Server.Modules.Main.pas' {DMMain: TDataModule},
  ELKE.Server.Modules.ServerContainer.Base in '..\..\ESCore\classes\ELKE.Server.Modules.ServerContainer.Base.pas' {ServerContainerBase: TDataModule},
  ELKE.Service in '..\..\ESCore\classes\ELKE.Service.pas' {ELKEService: TService},
  ELKE.Services.Admin.Impl in '..\..\ESCore\classes\ELKE.Services.Admin.Impl.pas',
  ELKE.Services.Admin.Intf in '..\..\ESCore\classes\ELKE.Services.Admin.Intf.pas',
  ELKE.Services.Me.Impl in '..\..\ESCore\classes\ELKE.Services.Me.Impl.pas',
  ELKE.Services.Me.Intf in '..\..\ESCore\classes\ELKE.Services.Me.Intf.pas',
  ELKE.Sparkle.Middleware.PVPAuth in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas',
  ELKE.Sparkle.Middleware.ReverseProxy in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas',
  ELKE.Sparkle.Middleware.TokenAuthGenEndpoints in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas';

{$R *.res}

// See:
// WinAPI.Windows.pas
// http://docwiki.embarcadero.com/RADStudio/Sydney/en/PE_(portable_executable)_header_flags_(Delphi)
// https://blog.dummzeuch.de/2017/11/02/using-pe-flags-in-delphi/

{$SETPEFLAGS IMAGE_FILE_LARGE_ADDRESS_AWARE}

begin
  // Leaks werden im Debug Modus per madExcept gecheckt.
  //ReportMemoryLeaksOnShutdown := DebugHook <> 0;

  if TELKEService.IsService then
  begin
    TELKEService.Run(TServerContainer);
  end
  else
  begin
    Application.Initialize;
    Application.MainFormOnTaskbar := True;
    Application.CreateForm(TServerContainer, ServerContainer);
    Application.CreateForm(TFormMainServer, FormMainServer);
    Application.Run;
  end;

end.
