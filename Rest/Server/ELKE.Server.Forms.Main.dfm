object FormMainServer: TFormMainServer
  Left = 0
  Top = 0
  Caption = 'ELKE API Server '#169' EsCulenta GmbH'
  ClientHeight = 440
  ClientWidth = 892
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  DesignSize = (
    892
    440)
  PixelsPerInch = 96
  TextHeight = 13
  object MemoInfo: TMemo
    Left = 8
    Top = 40
    Width = 876
    Height = 334
    Anchors = [akLeft, akTop, akRight, akBottom]
    ReadOnly = True
    ScrollBars = ssVertical
    TabOrder = 0
    WordWrap = False
  end
  object ButtonStart: TButton
    Left = 8
    Top = 8
    Width = 75
    Height = 25
    Caption = 'Start'
    TabOrder = 1
    OnClick = ButtonStartClick
  end
  object ButtonStop: TButton
    Left = 90
    Top = 8
    Width = 75
    Height = 25
    Caption = 'Stop'
    TabOrder = 2
    OnClick = ButtonStopClick
  end
  object ButtonSwagger: TButton
    Left = 796
    Top = 8
    Width = 88
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Open Swagger'
    Enabled = False
    TabOrder = 3
    OnClick = ButtonSwaggerClick
  end
  object StatusBar1: TStatusBar
    Left = 0
    Top = 421
    Width = 892
    Height = 19
    Panels = <
      item
        Width = 100
      end
      item
        Width = 400
      end
      item
        Width = 200
      end
      item
        Width = 50
      end>
  end
  object ButtonIntraweb: TButton
    Left = 702
    Top = 8
    Width = 88
    Height = 25
    Anchors = [akTop, akRight]
    Caption = 'Open Intraweb'
    Enabled = False
    TabOrder = 5
    OnClick = ButtonIntrawebClick
  end
  object PanelDebug: TPanel
    Left = 171
    Top = 8
    Width = 525
    Height = 26
    Anchors = [akLeft, akTop, akRight]
    Caption = 'DEBUG MODE'
    ParentShowHint = False
    ShowCaption = False
    ShowHint = False
    TabOrder = 6
    object Label1: TLabel
      AlignWithMargins = True
      Left = 4
      Top = 1
      Width = 71
      Height = 24
      Margins.Top = 0
      Align = alLeft
      Caption = 'Debug UserID:'
      Layout = tlCenter
    end
    object EditDebugUser: TEdit
      AlignWithMargins = True
      Left = 81
      Top = 4
      Width = 56
      Height = 18
      Align = alLeft
      Alignment = taRightJustify
      NumbersOnly = True
      TabOrder = 0
      Text = '0'
      OnChange = EditDebugUserChange
    end
  end
  object Panel1: TPanel
    Left = 8
    Top = 380
    Width = 876
    Height = 35
    Anchors = [akLeft, akRight, akBottom]
    Caption = 'Panel1'
    ShowCaption = False
    TabOrder = 7
    object LabelServerActive: TLabel
      Left = 16
      Top = 9
      Width = 101
      Height = 13
      Caption = 'Server nicht aktiv'
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = [fsBold]
      ParentFont = False
    end
    object CheckBoxLiveLog: TCheckBox
      Left = 144
      Top = 8
      Width = 141
      Height = 17
      Caption = 'Live Log'
      TabOrder = 0
      OnClick = CheckBoxLiveLogClick
    end
    object CheckboxSqlLog: TCheckBox
      Left = 264
      Top = 8
      Width = 141
      Height = 17
      Caption = 'SQL Log'
      TabOrder = 1
      OnClick = CheckboxSqlLogClick
    end
    object CheckBoxAutoScroll: TCheckBox
      Left = 384
      Top = 10
      Width = 141
      Height = 17
      Caption = 'Auto Scroll'
      Checked = True
      State = cbChecked
      TabOrder = 2
      OnClick = CheckboxSqlLogClick
    end
  end
  object ApplicationEvents1: TApplicationEvents
    OnIdle = ApplicationEvents1Idle
    Left = 232
    Top = 128
  end
end
