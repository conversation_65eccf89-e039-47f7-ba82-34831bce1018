﻿unit ELKE.Server.Forms.Main;

interface

uses
  System.Classes, System.SysUtils, System.UITypes,
  WinApi.Messages,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls,
  Vcl.AppEvnts, Vcl.WinXPickers, Vcl.ComCtrls, Vcl.ExtCtrls;

type
  TFormMainServer = class(TForm)
    MemoInfo: TMemo;
    ButtonStart: TButton;
    ButtonStop: TButton;
    ButtonSwagger: TButton;
    ApplicationEvents1: TApplicationEvents;
    StatusBar1: TStatusBar;
    ButtonIntraweb: TButton;
    PanelDebug: TPanel;
    EditDebugUser: TEdit;
    Label1: TLabel;
    Panel1: TPanel;
    CheckBoxLiveLog: TCheckBox;
    CheckboxSqlLog: TCheckBox;
    LabelServerActive: TLabel;
    CheckBoxAutoScroll: TCheckBox;
    procedure FormDestroy(Sender: TObject);
    procedure ApplicationEvents1Idle(
      Sender: TObject;
      var Done: Boolean);
    procedure ButtonIntrawebClick(Sender: TObject);
    procedure ButtonRecreateClick(Sender: TObject);
    procedure ButtonSwaggerClick(Sender: TObject);
    procedure ButtonStartClick(ASender: TObject);
    procedure ButtonStopClick(ASender: TObject);
    procedure CheckBoxLiveLogClick(Sender: TObject);
    procedure CheckboxSqlLogClick(Sender: TObject);
    procedure EditDebugUserChange(Sender: TObject);
    procedure FormCreate(ASender: TObject);
  private
    FCreating: Boolean;
  protected
    procedure UpdateGUI;
    procedure UpdateLogOptions;
    procedure UpdateSqlLog;
    procedure UpdateLiveLog;
  end;

var
  FormMainServer: TFormMainServer;

implementation

uses
  DX.Utils.Windows, DX.Utils.Logger, ELKE.Server.Logger, ELKE.Server.Configuration,
  ELKE.Server.Modules.ServerContainer, ELKE.Mail, Modules.Reports.ELKE, ELKE.Services.Me.Impl,
  ELKE.Server.Modules.Main;

{$R *.dfm}

procedure TFormMainServer.FormDestroy(Sender: TObject);
begin
  TDXLogger.Instance.ExternalStrings := nil;
end;

procedure TFormMainServer.ApplicationEvents1Idle(
  Sender: TObject;
  var Done: Boolean);
begin
  UpdateGUI;
end;

procedure TFormMainServer.ButtonIntrawebClick(Sender: TObject);
begin
  OpenBrowser(ServerContainer.BaseURLIntraweb.Replace('+', '127.0.0.1'));
end;

procedure TFormMainServer.ButtonRecreateClick(Sender: TObject);
begin
  if MessageDlg('Achtung: LOG Daten werden gelöscht! Forfahren?', mtConfirmation, mbOKCancel, 0) = mrOk then
  begin
    ServerContainer.DeleteDatabaseLOG;
    ServerContainer.UpdateDatabaseLOG;
  end;
end;

procedure TFormMainServer.ButtonSwaggerClick(Sender: TObject);
begin
  OpenBrowser(ServerContainer.BaseURLSwagger.Replace('+', '127.0.0.1'));
end;

{ TMainForm }

procedure TFormMainServer.ButtonStartClick(ASender: TObject);
begin
  ServerContainer.StartServer;
end;

procedure TFormMainServer.ButtonStopClick(ASender: TObject);
begin
  ServerContainer.StopServer;
end;

procedure TFormMainServer.CheckBoxLiveLogClick(Sender: TObject);
begin
  //Im OnCreate steuern wir das gezielt
  if not FCreating then
    UpdateLiveLog;
end;

procedure TFormMainServer.CheckboxSqlLogClick(Sender: TObject);
begin
  //Im OnCreate steuern wir das gezielt
  if not FCreating then
    UpdateSqlLog;
end;

procedure TFormMainServer.EditDebugUserChange(Sender: TObject);
begin
  GDebugUserID := StrToIntDef(EditDebugUser.Text, 0);
end;

procedure TFormMainServer.FormCreate(ASender: TObject);
begin
  FCreating := true;
{$IFDEF DEBUG}
  CheckBoxLiveLog.Checked := true;
  CheckboxSqlLog.Checked := true;
{$ELSE}
  CheckBoxLiveLog.Checked := false;
  CheckboxSqlLog.Checked := false;
{$ENDIF}
  UpdateLogOptions;

  StatusBar1.Panels[1].Text := ParamStr(0);
  StatusBar1.Panels[0].Text := 'Version: ' + DX.Utils.Windows.GetExeVersionData.ProductVersion;
  var
  LConnection := TStringList.Create;
  try
    LConnection.Delimiter := ';';
    LConnection.DelimitedText := TConfiguration.Default.DBConnectionString;
    StatusBar1.Panels[2].Text := Format('DB: %s/%s', [LConnection.Values['Server'], LConnection.Values['Database']]);
  finally
    FreeAndNil(LConnection);
  end;

  PanelDebug.Visible := false;
  StatusBar1.Panels[3].Text := '';
{$IFDEF DEBUG}
  PanelDebug.Visible := true;
  StatusBar1.Panels[3].Text := 'DEBUG';
{$ENDIF}
  FCreating := false;
end;

procedure TFormMainServer.UpdateGUI;
begin
  ButtonStart.Enabled := not ServerContainer.SparkleHttpSysDispatcher.Active;
  ButtonStop.Enabled := not ButtonStart.Enabled;
  ButtonSwagger.Enabled := ButtonStop.Enabled;
  ButtonIntraweb.Enabled := ButtonSwagger.Enabled;
  EditDebugUser.Enabled := ButtonStart.Enabled;

  if ServerContainer.SparkleHttpSysDispatcher.Active then
  begin
    LabelServerActive.Caption := 'Server active';
  end
  else
  begin
    LabelServerActive.Caption := 'Server not active';
  end;

  if CheckBoxAutoScroll.Checked then
  begin
    //Automatisch ans Ende scrollen
  // 1) Caret ans Ende des Textes setzen
    MemoInfo.SelStart := Length(MemoInfo.Text);
    MemoInfo.SelLength := 0;
    // 2) EM_SCROLLCARET senden, damit der Caret sichtbar wird
    MemoInfo.Perform(EM_SCROLLCARET, 0, 0);
  end;
end;

procedure TFormMainServer.UpdateLogOptions;
begin
  UpdateLiveLog;
  UpdateSqlLog;
end;

procedure TFormMainServer.UpdateLiveLog;
begin
  if CheckBoxLiveLog.Checked then
  begin
    TDXLogger.Instance.ExternalStrings := MemoInfo.Lines;
    TDXLogger.Instance.ExternalStringsAppendOnTop := false;
    MemoInfo.Lines.Add('Live Log active');
  end
  else
  begin
    TDXLogger.Instance.ExternalStrings := nil;
    MemoInfo.Lines.Add('Live Log inactive');
  end;
end;

procedure TFormMainServer.UpdateSqlLog;
begin
  if CheckboxSqlLog.Checked then
  begin
    TDMMain.SQLLogActive := true;
  end
  else
  begin
    TDMMain.SQLLogActive := false;
  end;
end;

end.

