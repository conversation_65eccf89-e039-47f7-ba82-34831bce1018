﻿unit ELKE.Server.Modules.ServerContainer;

interface

uses
  System.SysUtils, System.Classes,

  FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.MSSQL, FireDAC.Phys.MSSQLDef, FireDAC.VCLUI.Wait, Data.DB,
  FireDAC.Comp.Client, FireDAC.Stan.Intf,

  Sparkle.HttpServer.Module, Sparkle.HttpServer.Context, Sparkle.Comp.HttpSysDispatcher,
  Sparkle.Comp.Server,

  Aurelius.Drivers.Interfaces, Aurelius.Sql.MSSQL, Aurelius.Schema.MSSQL, Aurelius.Drivers.FireDAC,
  Aurelius.Comp.Connection,

  // Todo: Aurelius.Dictionary.Generator,

  XData.Server.Module, XData.Comp.ConnectionPool, XData.Comp.Server,

  ELKE.Server.Modules.ServerContainer.Base, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  Sparkle.Module.Static, Sparkle.Comp.StaticServer, FireDAC.Moni.Base, FireDAC.Moni.FlatFile, FireDAC.Moni.RemoteClient,
  FireDAC.Stan.Param, FireDAC.DatS, FireDAC.DApt.Intf, FireDAC.DApt, FireDAC.Comp.DataSet;

type
  TServerContainer = class(TServerContainerBase)
    procedure DataModuleCreate(Sender: TObject);
    procedure XDataServerModuleCreate(
      Sender: TObject;
      Module: TXDataServerModule);
  protected
  public
  end;

var
  ServerContainer: TServerContainer;

implementation

uses
  ELKE.Classes, ELKE.Classes.Generated, ELKE.Server.Logger, System.DateUtils;

{%CLASSGROUP 'Vcl.Controls.TControl'}
{$R *.dfm}


procedure TServerContainer.DataModuleCreate(Sender: TObject);
begin
  inherited;

  XDataServer.Model.Title := 'ELKE REST API';
  // This is Markdown
  XDataServer.Model.Description :=
    '### Überblick'#13#10 +
    'Diese API wird von der ELKE Architektur verwendet.'#13#10#13#10 +
    '[©2020-' + YearOf(Today).ToString + ' - EsCulenta GmbH](https://www.esculenta.at)'#13#10 +
    '<img align="left" src="https://www.esculenta.at/wp-content/themes/restored316-refined-pro/images/Esculenta-Logo.png"/>';

{$IFDEF DEBUG}
  // Todo: TDictionaryGenerator.GenerateFile('ELKE.Classes.Generated.Dictionary.pas');
{$ENDIF}
end;

procedure TServerContainer.XDataServerModuleCreate(
  Sender: TObject;
  Module: TXDataServerModule);
begin
  inherited;
  // Todo: Aktuell/vorrübergehen sind per default alle Berechtigungen gesetzt
  (*
    SetPermissions(Module, TBetrieb, EntitySetPermissionsAll);
    SetPermissions(Module, TBkbTyp, EntitySetPermissionsRead);
    SetPermissions(Module, TBewertung, EntitySetPermissionsRead);
    SetPermissions(Module, TBundesland, EntitySetPermissionsRead);
    SetPermissions(Module, TCheckliste, EntitySetPermissionsRead);
    SetPermissions(Module, TMassnahme, EntitySetPermissionsRead);
    SetPermissions(Module, TMangel, EntitySetPermissionsRead);
    SetPermissions(Module, TFrage, EntitySetPermissionsRead);
    SetPermissions(Module, TFragenGruppe, EntitySetPermissionsRead);
    SetPermissions(Module, TFragenGruppe, EntitySetPermissionsRead);
    SetPermissions(Module, TGruppe, EntitySetPermissionsRead);
    SetPermissions(Module, TKontrollbericht, EntitySetPermissionsAll);
    SetPermissions(Module, TLand, EntitySetPermissionsRead);
    SetPermissions(Module, TModul, EntitySetPermissionsRead);
    SetPermissions(Module, TNachricht, EntitySetPermissionsRead);
    SetPermissions(Module, TPerson, EntitySetPermissionsAll);
  *)
end;

end.
