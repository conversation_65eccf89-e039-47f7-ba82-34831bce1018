﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Application</AppType>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>ELKEApiServer.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{B1CB67B7-6F2F-4DE8-AB83-17AFFCB2B759}</ProjectGuid>
        <ProjectName Condition="'$(ProjectName)'==''">ELKEApiServer</ProjectName>
        <ProjectVersion>20.2</ProjectVersion>
        <TargetedPlatforms>3</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Base)'=='true') or '$(Base_Win64)'!=''">
        <Base_Win64>true</Base_Win64>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win64)'!=''">
        <Cfg_1_Win64>true</Cfg_1_Win64>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='FASTMM Fulldebug' or '$(Cfg_3)'!=''">
        <Cfg_3>true</Cfg_3>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='RemoteDebug' or '$(Cfg_4)'!=''">
        <Cfg_4>true</Cfg_4>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win64)'!=''">
        <Cfg_2_Win64>true</Cfg_2_Win64>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>ELKEApiServer</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)\DCU</DCC_DcuOutput>
        <DCC_Define>REST;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_OutputXMLDocumentation>true</DCC_OutputXMLDocumentation>
        <DCC_SYMBOL_PLATFORM>false</DCC_SYMBOL_PLATFORM>
        <DCC_UNIT_PLATFORM>false</DCC_UNIT_PLATFORM>
        <DCC_UnitSearchPath>..\..\ReportBuilder;..\..\ESCore\classes;..\..\ESCore\model;..\..\_libs\dx-library;..\..\_libs\FastMM4;..\..\_libs\FastMM5;..\..\_libs\TMSAurelius\source;..\..\_libs\TMSAurelius\source\core;..\..\_libs\TMSAurelius\source\drivers;..\..\_libs\TMSBCL\source;..\..\_libs\TMSBCL\source\core;..\..\_libs\TMSBCL\source\core\common;..\..\_libs\TMSBCL\source\extra;..\..\_libs\TMSXData\source;..\..\_libs\TMSXData\source\core;..\..\_libs\TMSXData\source\core\common;..\..\_libs\TMSSparkle\source;..\..\_libs\TMSSparkle\source\core;..\..\_libs\TMSSparkle\source\core\common;..\..\_libs\TMSSparkle\source\extra;..\..\_libs\TMSSparkle\source\app;..\..\_libs\FastReport\FastCore\VCL\Sources;..\..\_libs\FastReport\FastGraphics\VCL\Sources;..\..\_libs\FastReport\FastLocalization\VCL\Sources;..\..\_libs\FastReport\FastQueryBuilder\VCL\Sources;..\..\_libs\FastReport\FastReport\VCL\Sources;..\..\_libs\FastReport\FastScript\VCL\Sources;..\..\_libs\\FastReport\FastGraphics\VCL\Sources;..\..\_libs\madCollection\madBasic\sources;..\..\_libs\madCollection\madDisAsm\sources;..\..\_libs\madCollection\madExcept\sources;..\..\_libs\madCollection\madKernel\sources;..\..\_libs\madCollection\madSecurity\sources;..\..\_libs\madCollection\madShell\sources;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_XML_CREF_NO_RESOLVE>false</DCC_XML_CREF_NO_RESOLVE>
        <DCC_XML_EXPECTED_CHARACTER>false</DCC_XML_EXPECTED_CHARACTER>
        <DCC_XML_INVALID_NAME>false</DCC_XML_INVALID_NAME>
        <DCC_XML_INVALID_NAME_START>false</DCC_XML_INVALID_NAME_START>
        <DCC_XML_NO_MATCHING_PARM>false</DCC_XML_NO_MATCHING_PARM>
        <DCC_XML_NO_PARM>false</DCC_XML_NO_PARM>
        <DCC_XML_UNKNOWN_ENTITY>false</DCC_XML_UNKNOWN_ENTITY>
        <DCC_XML_WHITESPACE_NOT_ALLOWED>false</DCC_XML_WHITESPACE_NOT_ALLOWED>
        <DCC_XmlOutput>.\$(Platform)\$(Config)\XMLDoc\ELKERest</DCC_XmlOutput>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=EsCulenta GmbH;FileDescription=Implementiert einen  Http.sys basierenden REST-Server für die ELKE Kommunikation;FileVersion=********;InternalName=ELKERest;LegalCopyright=;LegalTrademarks=;OriginalFilename=$(MSBuildProjectName).exe;ProgramID=at.esculenta.$(MSBuildProjectName);ProductName=ELKE API Server;ProductVersion=1.64.2;Comments=</VerInfo_Keys>
        <VerInfo_Locale>3079</VerInfo_Locale>
        <VerInfo_MinorVer>64</VerInfo_MinorVer>
        <VerInfo_Release>2</VerInfo_Release>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <AppEnableRuntimeThemes>true</AppEnableRuntimeThemes>
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_Define>madExcept;$(DCC_Define)</DCC_Define>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>DBXSqliteDriver;RESTComponents;fmxase;DBXDb2Driver;DBXInterBaseDriver;VCLTMSFNCChartPkgDXE12;CData.Gmail.D26;vclactnband;vclFireDAC;emsclientfiredac;tethering;svnui;DataSnapFireDAC;FireDACADSDriver;tmswizdXE12;DBXMSSQLDriver;DatasnapConnectorsFreePascal;FireDACMSSQLDriver;FMXTMSFNCUIPackPkgDXE12;vcltouch;vcldb;bindcompfmx;svn;TMSWEBCorePkgLibDXE12;DBXOracleDriver;inetdb;FmxTeeUI;emsedge;fmx;FireDACIBDriver;fmxdae;FireDACDBXDriver;dbexpress;IndyCore;xdata;vclx;dsnap;emsclient;DataSnapCommon;FireDACCommon;VCLTMSFNCUIPackPkgDXE12;VCLTMSFNCCloudPackPkgDXE12;RESTBackendComponents;DataSnapConnectors;VCLRESTComponents;CData.Twilio.D26;soapserver;CData.WordPress.D26;vclie;bindengine;DBXMySQLDriver;CloudService;FireDACOracleDriver;FireDACMySQLDriver;DBXFirebirdDriver;FireDACCommonODBC;FireDACCommonDriver;VCLTMSFNCCorePkgDXE12;DataSnapClient;TMSFMXWebGMapsPkgDXE12;inet;IndyIPCommon;bindcompdbx;vcl;IndyIPServer;DBXSybaseASEDriver;sparkle;tmsbcl;IndySystem;FireDACDb2Driver;dsnapcon;madExcept_;VirtualTreesR;FMXTMSFNCCorePkgDXE12;tmsxlsdXE12;FireDACMSAccDriver;fmxFireDAC;FireDACInfxDriver;vclimg;madBasic_;TeeDB;FireDAC;emshosting;tmsdXE12;FireDACSqliteDriver;FireDACPgDriver;FireDACASADriver;DBXOdbcDriver;FireDACTDataDriver;FMXTee;soaprtl;DbxCommonDriver;Tee;DataSnapServer;xmlrtl;soapmidas;DataSnapNativeClient;fmxobj;vclwinx;FireDACDSDriver;rtl;emsserverresource;DbxClientDriver;madDisAsm_;DBXSybaseASADriver;tmsexdXE12;CustomIPTransport;vcldsnap;bindcomp;appanalytics;DBXInformixDriver;FMXTMSFNCCloudPackPkgDXE12;IndyIPClient;TMSWEBCorePkgDXE12;bindcompvcl;CData.Bing.D26;TeeUI;dbxcds;VclSmp;adortl;FireDACODBCDriver;FixInsight_10_3;DataSnapIndy10ServerTransport;aurelius;dsnapxml;DataSnapProviderClient;dbrtl;IndyProtocols;inetdbxpress;FireDACMongoDBDriver;FMXTMSFNCChartPkgDXE12;DataSnapServerMidas;$(DCC_UsePackage)</DCC_UsePackage>
        <Icon_MainIcon>icons8-api.ico</Icon_MainIcon>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win64)'!=''">
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;$(DCC_Namespace)</DCC_Namespace>
        <Icon_MainIcon>icons8-api.ico</Icon_MainIcon>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_Inlining>off</DCC_Inlining>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_OutputXMLDocumentation>false</DCC_OutputXMLDocumentation>
        <VerInfo_Debug>true</VerInfo_Debug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_Define>LeakChecking;$(DCC_Define)</DCC_Define>
        <DCC_IntegerOverflowCheck>true</DCC_IntegerOverflowCheck>
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_RangeChecking>true</DCC_RangeChecking>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win64)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_3)'!=''">
        <DCC_Define>FULLDEBUGMODE;$(DCC_Define)</DCC_Define>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_4)'!=''">
        <DCC_MapFile>3</DCC_MapFile>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>..\..\_Release-Binaries\$(Platform)</DCC_ExeOutput>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_XmlOutput>..\..\_Release-Binaries\$(Platform)\XMLDoc\ELKERest</DCC_XmlOutput>
        <PostBuildEvent>
            <![CDATA[$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -copy
$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -git
$(PostBuildEvent)]]>
        </PostBuildEvent>
        <PreBuildEvent>
            <![CDATA[del ($OUTPUTPATH)
if not exist $(OUTPUTDIR)XMLDoc\ELKERest\nul md $(OUTPUTDIR)XMLDoc\ELKERest
$(PreBuildEvent)]]>
        </PreBuildEvent>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <DCC_DebugInformation>2</DCC_DebugInformation>
        <DCC_LocalDebugSymbols>true</DCC_LocalDebugSymbols>
        <DCC_MapFile>3</DCC_MapFile>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win64)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="ELKE.Server.Modules.ServerContainer.pas">
            <Form>ServerContainer</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="ELKE.Server.Forms.Main.pas">
            <Form>FormMainServer</Form>
            <FormType>dfm</FormType>
        </DCCReference>
        <DCCReference Include="ELKE.Server.Modules.Data.pas">
            <Form>DataContainer</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\..\ReportBuilder\Modules.Reports.AMA.pas"/>
        <DCCReference Include="..\..\ReportBuilder\Modules.Reports.Base.pas"/>
        <DCCReference Include="..\..\ReportBuilder\Modules.Reports.ELKE.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Admin.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Generated.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Logging.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.PVP.Roles.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.PVP.Token.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Request.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.RESTError.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Mail.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Configuration.Base.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Configuration.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Logger.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Modules.Main.pas">
            <Form>DMMain</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Modules.ServerContainer.Base.pas">
            <Form>ServerContainerBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Service.pas">
            <Form>ELKEService</Form>
            <FormType>dfm</FormType>
            <DesignClass>TService</DesignClass>
        </DCCReference>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Admin.Impl.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Admin.Intf.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Me.Impl.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Me.Intf.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas"/>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="FASTMM Fulldebug">
            <Key>Cfg_3</Key>
            <CfgParent>Cfg_1</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="RemoteDebug">
            <Key>Cfg_4</Key>
            <CfgParent>Cfg_1</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Application</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">ELKEApiServer.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">False</Platform>
                <Platform value="Linux64">False</Platform>
                <Platform value="OSX64">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">True</Platform>
                <Platform value="iOSDevice64">False</Platform>
            </Platforms>
            <MMX>
                <UsesClauseFormatter AutoFormat="1">
                    <GroupNames>Winapi;System.Win;System;Data;REST;Xml;Vcl;FMX</GroupNames>
                </UsesClauseFormatter>
                <Parser>
                    <Defines/>
                    <IfExpressions/>
                </Parser>
            </MMX>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
    <PropertyGroup Condition="'$(Config)'=='Release' And '$(Platform)'=='Win32'">
        <PreBuildEvent>del ($OUTPUTPATH)
&amp;&amp;if not exist $(OUTPUTDIR)XMLDoc\ELKERest\nul md $(OUTPUTDIR)XMLDoc\ELKERest
</PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent>$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -copy
&amp;&amp;$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -git
</PostBuildEvent>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' And '$(Platform)'=='Win64'">
        <PreBuildEvent>del ($OUTPUTPATH)
&amp;&amp;if not exist $(OUTPUTDIR)XMLDoc\ELKERest\nul md $(OUTPUTDIR)XMLDoc\ELKERest
</PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent>$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -copy
&amp;&amp;$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -git
</PostBuildEvent>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
</Project>
