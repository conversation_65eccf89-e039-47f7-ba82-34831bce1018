unit AMA.Kis.Server.Service.Impl;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections, System.NetEncoding,
  Aurelius.Types.Nullable,
  XData.Server.Module,
  XData.Service.Common,
  XData.Sys.Exceptions,
  ELKE.Classes,
  ELKE.Classes.Generated,
  ELKE.Classes.Generated.Dictionary,
  ELKE.Classes.Request,
  ELKE.REST.Service.Base,
  AMA.Kis.Server.Service.Intf, AMA.Kis.Classes, AMA.Classes.Pruefergebnisse;

type

  [ServiceImplementation]
  TKontrollen = class(TRESTServiceBase, IKisKontrollen)

  public
    [HttpGet]
    [URIPathSegment('kontrollenAbholen')]
    function KontrollenAbholen(regNr: string; abDatumUhrzeit: TDateTime; abDatumFreigabe: TDateTime;
      Bundesland: Integer): TKisKontrollen;

    [HttpGet]
    [URIPathSegment('kontrollPDF')]
    function KontrollPDF(dateiId: string): TPruefErgebnisPdf;
  public

  end;

implementation

const
  ROLE_KIS = 'ELKE-AMAinfo';

function TKontrollen.KontrollenAbholen(regNr: string; abDatumUhrzeit: TDateTime; abDatumFreigabe: TDateTime;
  Bundesland: Integer): TKisKontrollen;
begin
  Requires(ROLE_KIS);

  var
  LKontrollen := TXDataOperationContext.Current.GetManager.Find<TKontrollbericht>
    .Where(M.Kontrollbericht.Endezeit >= abDatumUhrzeit)
    .Where(M.Kontrollbericht.Betrieb.Registrierung.regNr = regNr)
    .Where(M.Kontrollbericht.Betrieb.Bundesland.Bldcode = Bundesland)
  // Todo: .Where(AbDatumFreigabe)
    .List;

  Result := TKisKontrollen.Create(LKontrollen.Count);
  var
  i := 0;
  for var LKontrolle in LKontrollen do
  begin
    Result.Ergebnisse[i].BerichtsTyp := LKontrolle.Kontrolltyp.Bkbtyp.Typ;
    Result.Ergebnisse[i].BkbNr := LKontrolle.Bkb;
    Result.Ergebnisse[i].Bundesland := Bundesland;

    // Aktuell gibt es nur eine Datei - den Kontrollbericht
    var
    LDatei := TDatei.Create;
    LDatei.dateiId := LKontrolle.Dokument.Guid.ToString;
    LDatei.Dateiname := LKontrolle.Dokument.Dateiname;
    Result.Ergebnisse[i].Dateien.Add(LDatei);

    Result.Ergebnisse[i].HauptBetrnr := LKontrolle.Betrieb.Bbknr.ValueOrDefault;
    Result.Ergebnisse[i].KontrollEnde := LKontrolle.Endezeit;
    Result.Ergebnisse[i].KontrollStatus := LKontrolle.Status;
    Result.Ergebnisse[i].Kontrolltyp := LKontrolle.Kontrolltyp.Kontrolltyp;

    inc(i);
  end;
end;

function TKontrollen.KontrollPDF(dateiId: string): TPruefErgebnisPdf;
begin
  Requires(ROLE_KIS);
  var
  LDokument := TXDataOperationContext.Current.GetManager.Find<TDokument>
    .Where(M.Dokument.Guid = dateiId).UniqueResult;

  if LDokument = nil then
    raise EXDataHttpException.Create(404, 'Bericht nicht gefunden!');

  Result := TPruefErgebnisPdf.Create;
  Result.dateiId := LDokument.Guid.ToString;
  Result.Dateiname := LDokument.Dateiname;

  var
  LBase64Encoder := TBase64Encoding.Create(MaxInt);
  try
    Result.Datei := LBase64Encoder.EncodeBytesToString(LDokument.Dokument.AsBytes);
  finally
    FreeAndNil(LBase64Encoder);
  end;

end;

initialization

RegisterServiceType(TKontrollen);

end.
