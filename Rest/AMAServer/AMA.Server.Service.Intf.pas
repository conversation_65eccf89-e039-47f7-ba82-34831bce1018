﻿unit AMA.Server.Service.Intf;

interface

uses
  System.Generics.Collections,
  Aurelius.Types.Nullable, Aurelius.Mapping.Attributes,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  XData.Service.Common,
  AMA.Classes,  AMA.Classes.Pruefergebnisse, AMA.Classes.Pruefbetriebe;

// Die AMA HTTP Status Codes sind im Bereich 481-490

type

  /// <summary>
  /// CC Kontrollen-Abfrage <br />Erforderliche Rolle: ELKE-CCK
  /// </summary>
  [Model('AMA')]
  [ServiceContract]
  [URIPathSegment('ccKontrollen')]
  IKontrollen = interface(IInvokable)
    ['{97A5C5B7-D77D-4EEA-9C20-65E31B42C77B}']

    [HttpGet]
    [URIPathSegment('ergebnisseAbholen')]
    function PruefErgebnisseAbholen(abDatumUhrzeit: TDateTime): TPruefErgebnisse;

    [HttpGet]
    [URIPathSegment('ergebnisPDF')]
    function PruefErgebnisPDF(dateiId: string): TPruefErgebnisPdf;

    [HttpPost]
    [URIPathSegment('meldeKontrollBetriebe')]
    function meldeKontrollBetriebe(Pruefmeldung: TPruefbetriebe): TErzeugteAuftraege;

    /// <summary>
    ///   Übermittelt den AMA-Bearbeitungsstatus zu einer Kontrolle
    /// </summary>
    /// <param name="StatusMeldung">
    ///   <b>bkb:</b> BKB-Nummer des entsprechenden Kontrollberichts
    ///   <b>kommentar:</b> Korrekturbedarf
    ///   <b>kontakt:</b> Name des User Erstkontrolle
    ///   <b>status:</b>
    ///   "ERR" : Kontrollbericht enthält einen Fehler
    ///   "DAT" : Daten erhalten
    ///   "OK" : Kontrollbericht erledigt
    ///   "INFO" : Kontrollbericht enthält einen Fehler
    /// </param>
    [HttpPost]
    [URIPathSegment('kontrollStatus')]
    procedure kontrollStatus(StatusMeldung: TAmaKontrollStatus);

  end;

implementation

initialization

RegisterServiceType(TypeInfo(IKontrollen));

end.
