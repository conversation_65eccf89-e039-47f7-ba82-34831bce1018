unit AMA.Classes.Pruefergebnisse;

interface

uses
  System.Classes, System.SysUtils,    System.Generics.Collections,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  Aurelius.Types.Nullable, Aurelius.Mapping.Attributes ;

{$M+}


type

  TDatei = class;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TVerstossDaten = class(TObject)
  private
    FAnforderung: Nullable<string>;
    FBeanstandungAusmass: Nullable<integer>;
    FBeanstandungDauer: Nullable<integer>;
    FBeanstandungSchwere: Nullable<integer>;
    FKommentarAnf: Nullable<string>;
    FKzHv: boolean;
    FStatusAnf: Nullable<string>;
    FVorsatzKz: Nullable<boolean>;
    procedure SetVorsatzKz(const Value: Nullable<boolean>);
  published
    property Anforderung: Nullable<string> read FAnforderung write FAnforderung;
    property BeanstandungAusmass: Nullable<integer> read FBeanstandungAusmass write FBeanstandungAusmass;
    property BeanstandungDauer: Nullable<integer> read F<PERSON>ean<PERSON>ung<PERSON>auer write FBeanstandungDauer;
    property BeanstandungSchwere: Nullable<integer> read FBeanstandungSchwere write FBeanstandungSchwere;
    property KommentarAnf: Nullable<string> read FKommentarAnf write FKommentarAnf;
    property StatusAnf: Nullable<string> read FStatusAnf write FStatusAnf;
    property VorsatzKz: Nullable<boolean> read FVorsatzKz write SetVorsatzKz;
    property KzHv: boolean read FKzHv write FKzHv;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TModul = class(TObject)
  private
    FAuswahlDatum: Nullable<TDate>;
    FAuswahlGrund: Nullable<string>;
    FDatStorno: Nullable<TDate>;
    FKommentarModul: Nullable<string>;
    FKontrollDatum: Nullable<TDate>;
    FModul: Nullable<string>;
    FPruefer: Nullable<string>;
    FStatusKontrolle: Nullable<string>;
    FVerstossDaten: TArray<TVerstossDaten>;
  public
    property AuswahlDatum: Nullable<TDate> read FAuswahlDatum write FAuswahlDatum;
    property AuswahlGrund: Nullable<string> read FAuswahlGrund write FAuswahlGrund;
    property DatStorno: Nullable<TDate> read FDatStorno write FDatStorno;
    property KommentarModul: Nullable<string> read FKommentarModul write FKommentarModul;
    property KontrollDatum: Nullable<TDate> read FKontrollDatum write FKontrollDatum;
    property Modul: Nullable<string> read FModul write FModul;
    property Pruefer: Nullable<string> read FPruefer write FPruefer;
    property StatusKontrolle: Nullable<string> read FStatusKontrolle write FStatusKontrolle;
    property VerstossDaten: TArray<TVerstossDaten> read FVerstossDaten write FVerstossDaten;
    function AddVerstoss: TVerstossDaten;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TErgebnis = class(TObject)
  private
    FBbkNr: Nullable<string>;
    FBkbNr: Nullable<string>;
    FBegruendung: Nullable<string>;
    FBundesland: integer;
    FDateien: TObjectList<TDatei>;
    FHauptBetrnr: Nullable<string>;
    FJahr: Nullable<integer>;
    FModule: TArray<TModul>;
    FMeldedatum: Nullable<TDate>;
    FVersion: Nullable<string>;
    FVorankuendigungKz: boolean;
    FVorankuendigungDatum: Nullable<TDateTime>;
  public
    constructor Create;
    property BkbNr: Nullable<string> read FBkbNr write FBkbNr;
    property BbkNr: Nullable<string> read FBbkNr write FBbkNr;
    property Begruendung: Nullable<string> read FBegruendung write FBegruendung;
    property Bundesland: integer read FBundesland write FBundesland;
    property HauptBetrnr: Nullable<string> read FHauptBetrnr write FHauptBetrnr;
    property Dateien: TObjectList<TDatei> read FDateien write FDateien;
    property Jahr: Nullable<integer> read FJahr write FJahr;
    property Module: TArray<TModul> read FModule write FModule;
    property Meldedatum: Nullable<TDate> read FMeldedatum write FMeldedatum;
    property Version: Nullable<string> read FVersion write FVersion;
    property VorankuendigungKz: boolean read FVorankuendigungKz write FVorankuendigungKz;
    property VorankuendigungDatum: Nullable<TDateTime> read FVorankuendigungDatum write FVorankuendigungDatum;

    function AddModul: TModul;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TPruefErgebnisse = class(TObject)
  private
    FAnzahlDateien: integer;
    FErgebnisse: TArray<TErgebnis>;
    procedure SetAnzahlDateien(const Value: integer);
  public
    constructor Create(AAnzahl: integer);
    property AnzahlDateien: integer read FAnzahlDateien write SetAnzahlDateien;
    property Ergebnisse: TArray<TErgebnis> read FErgebnisse write FErgebnisse;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TPruefErgebnisPdf = class(TObject)
  private
    FDatei: string;
    FDateiId: string;
    FDateiname: string;
    procedure SetDatei(const Value: string);
  public
    property DateiId: string read FDateiId write FDateiId;
    property Datei: string read FDatei write SetDatei;
    property Dateiname: string read FDateiname write FDateiname;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TDatei = class(TObject)
  private
    FDateiId: Nullable<string>;
    FDateiname: Nullable<string>;
  public
    property DateiId: Nullable<string> read FDateiId write FDateiId;
    property Dateiname: Nullable<string> read FDateiname write FDateiname;
  end;

implementation

function TModul.AddVerstoss: TVerstossDaten;
begin
  SetLength(FVerstossDaten, Length(FVerstossDaten) + 1);
  result := TVerstossDaten.Create;
  FVerstossDaten[Length(FVerstossDaten) - 1] := result;
end;

function TErgebnis.AddModul: TModul;
begin
  SetLength(FModule, Length(FModule) + 1);
  result := TModul.Create;
  FModule[Length(FModule) - 1] := result;
end;

constructor TErgebnis.Create;
begin
  inherited;
  FDateien := TObjectList<TDatei>.Create;
end;

constructor TPruefErgebnisse.Create(AAnzahl: integer);
begin
  inherited Create;
  FAnzahlDateien := 0;
  SetLength(FErgebnisse, AAnzahl);
end;

procedure TPruefErgebnisse.SetAnzahlDateien(const Value: integer);
begin
  FAnzahlDateien := Value;
end;

procedure TVerstossDaten.SetVorsatzKz(const Value: Nullable<boolean>);
begin
  FVorsatzKz := Value;
end;

procedure TPruefErgebnisPdf.SetDatei(const Value: string);
begin
  FDatei := Value;
end;

end.
