unit AMA.Server.Forms.Main;

interface

uses
  System.Classes, System.SysUtils, System.UITypes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.AppEvnts,
  AMA.Server.Modules.ServerContainer;

type
  TFormMainServer = class(TForm)
    MemoInfo: TMemo;
    ButtonStart: TButton;
    ButtonStop: TButton;
    ButtonOpenBrowser: TButton;
    ApplicationEvents1: TApplicationEvents;
    procedure FormDestroy(Sender: TObject);
    procedure ApplicationEvents1Idle(
      Sender:   TObject;
      var Done: Boolean);
    procedure ButtonOpenBrowserClick(Sender: TObject);
    procedure ButtonStartClick(ASender: TObject);
    procedure ButtonStopClick(ASender: TObject);
    procedure FormCreate(ASender: TObject);
  protected
    procedure UpdateGUI;
  end;

var
  FormMainServer: TFormMainServer;

implementation

uses
  DX.Utils.Windows, DX.Utils.Logger;

{$R *.dfm}


procedure TFormMainServer.FormDestroy(Sender: TObject);
begin
  TDXLogger.Instance.ExternalStrings := nil;
end;

procedure TFormMainServer.ApplicationEvents1Idle(
  Sender:   TObject;
  var Done: Boolean);
begin
  UpdateGUI;
end;

procedure TFormMainServer.ButtonOpenBrowserClick(Sender: TObject);
begin
  OpenBrowser(ServerContainer.BaseURLSwagger.Replace('+', '127.0.0.1'));
end;

{ TMainForm }

procedure TFormMainServer.ButtonStartClick(ASender: TObject);
begin
  ServerContainer.StartServer;
end;

procedure TFormMainServer.ButtonStopClick(ASender: TObject);
begin
  ServerContainer.StopServer;
end;

procedure TFormMainServer.FormCreate(ASender: TObject);
begin
  TDXLogger.Instance.ExternalStrings := MemoInfo.Lines;
  TDXLogger.Instance.ExternalStringsAppendOnTop := true;
end;

procedure TFormMainServer.UpdateGUI;
begin
  ButtonStart.Enabled := not ServerContainer.SparkleHttpSysDispatcher.Active;
  ButtonStop.Enabled := not ButtonStart.Enabled;
  ButtonOpenBrowser.Enabled := ButtonStop.Enabled;
end;

end.
