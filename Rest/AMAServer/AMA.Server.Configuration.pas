unit AMA.Server.Configuration;

interface

uses
  System.Classes, System.SysUtils,
  DX.Classes.Configuration,
  ELKE.Server.Configuration.Base;

type

  [ConfigDescription('AMA Server Configuration')]
  TConfiguration = class(TConfigurationBase)
  public
    [ConfigValue('Server', 'http://+:80/AMA/v1')]
    property BaseURL: string read GetBaseURL;
    [ConfigValue('DB',
      'Database=ELKE;ApplicationName=AMAServer;OSAuthent=Yes;Server=localhost\SQLEXPRESS;DriverID=MSSQL')]
    property DBConnectionString: string read GetDBConnectionString;
    [ConfigValue('ELKE_REST_API', 'https://stp.esculenta.at/at.esculenta.elkerest-t/v1/')]
    property RESTURL: string read GetRESTURL;

  end;


implementation


end.
