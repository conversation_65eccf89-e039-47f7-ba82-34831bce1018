﻿unit AMA.Classes;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  REST.Json.Types,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  Aurelius.Mapping.Attributes,
  ELKE.Classes.Generated;

type

  /// <summary>
  /// Der AMA-Bearbeitungsstatus
  /// </summary>
  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TAmaKontrollStatus = class(TObject)
  private
    FBewertungsversion: string;
    FBkb: string;
    FKommentar: string;
    FKontakt: string;
    FStatus: string;
  public
    /// <summary>
    /// &lt;Bkb-Nummer&gt; des entsprechenden Kontrollberichts
    /// </summary>
    property Bkb: string read FBkb write FBkb;
    /// <summary>
    /// ERR : Kontrollbericht enthält einen Fehler <br />DAT : Daten erhalten <br />OK : Kontrollbericht erledigt <br />
    /// INFO : Kontrollberichtenthält einen Fehler
    /// </summary>
    property Status: string read FStatus write FStatus;
    /// <summary>
    /// Der Name des Users der Erstkontrolle
    /// </summary>
    property Kontakt: string read FKontakt write FKontakt;
    /// <summary>
    /// Der Korrekturbedarf im Detail
    /// </summary>
    property Kommentar: string read FKommentar write FKommentar;

    property Bewertungsversion: string read FBewertungsversion write FBewertungsversion;

  end;

const
  AMA_STATUS_NEU = 'NEU';
  AMA_STATUS_OK = 'OK';
  AMA_STATUS_ERR = 'ERR';
  AMA_STATUS_INFO = 'INFO';
  AMA_STATUS_DAT = 'DAT';

  AMA_STATI: ARRAY [0 .. 4] of string = (AMA_STATUS_NEU, AMA_STATUS_OK, AMA_STATUS_ERR, AMA_STATUS_INFO, AMA_STATUS_DAT);

implementation

end.
