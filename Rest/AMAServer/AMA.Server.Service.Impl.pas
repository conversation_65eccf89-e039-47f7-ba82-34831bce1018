﻿unit AMA.Server.Service.Impl;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  XData.Server.Module,
  XData.Service.Common,
  ELKE.Classes,
  ELKE.Classes.Generated,
  ELKE.Classes.Request,
  Elke.Classes.Rest.Client,
  Elke.Rest.Service.Base,
  AMA.Server.Service.Intf, AMA.Classes.Pruefergebnisse, AMA.Classes.Pruefbetriebe, Aurelius.Types.Nullable,
  AMA.Classes;

type

  [ServiceImplementation]
  TKontrollen = class(TRESTServiceBase, IKontrollen)

  private
    ELKERest: TELKEClient;
  protected
    procedure meldeBetriebe_Zuordnen(APruefbetriebe: TPruefbetriebe);
    procedure meldeBetriebe_Speichern(APruefbetriebe: TPruefbetriebe; AErzeugteAuftraege: TErzeugteAuftraege);
  public
    constructor Create;
    destructor Destroy; override;
    [HttpGet]
    [URIPathSegment('ergebnisPdf')]
    function PruefErgebnisPDF(dateiId: string): TPruefErgebnisPdf;

    [HttpGet]
    [URIPathSegment('ergebnisseAbholen')]
    function PruefErgebnisseAbholen(abDatumUhrzeit: TDateTime): TPruefErgebnisse;

    [URIPathSegment('meldeKontrollBetriebe')]
    function meldeKontrollBetriebe(Pruefmeldung: TPruefbetriebe): TErzeugteAuftraege;

    [URIPathSegment('kontrollStatus')]
    procedure kontrollStatus(StatusMeldung: TAmaKontrollStatus);

  end;

implementation

uses
  System.DateUtils, System.NetEncoding, System.Types,
  Sparkle.Http.Headers,
  Aurelius.Linq,
  XData.Sys.Exceptions, ELKE.Classes.Generated.Dictionary, ELKE.Server.Logger,
  ELKE.Classes.Logging;

const
  ROLE_CCK = 'ELKE-CCK';

  // Sucht in der DB nach den Betrieben mit der importierten RegNr und ordnet diese den Importdaten zu
  // Der Hauptbetrieb des Bewirtschafters wird ebenfalls zugeordnet
procedure TKontrollen.meldeBetriebe_Zuordnen(APruefbetriebe: TPruefbetriebe);
var
  LBewirtschafter: TBewirtschafter;
  LBetriebsdatum: TBetriebsdaten;
  LBetrieb: TBetrieb;
  LBundesland: TBundesland;
begin
  // Importdaten vorbereiten, Betriebe aus Datenbank suchen
  ELKELog('Betriebe zuordnen...');
  for LBewirtschafter in APruefbetriebe.bewirtschafter do
  begin

    for LBetriebsdatum in LBewirtschafter.Betriebsdaten do
    begin
      // Achtung! Wir haben die Betriebsnummern 7-stellig mit führenden Nullen
      LBetriebsdatum.Betrnr := LBetriebsdatum.Betrnr.PadLeft(7, '0');
      LBetrieb := Context.Find<TBetrieb>
        .where(M.Betrieb.Registrierung.Regnr = LBetriebsdatum.Betrnr).UniqueResult;

      if LBetrieb = nil then
      begin
        ELKELog('Betrieb %s nicht gefunden', [LBetriebsdatum.Betrnr]);
      end;

      // Hier merken wir uns den effektiven, zugehoerigen Betrieb
      LBetriebsdatum.Betrieb := LBetrieb;

      // Hier ggfs den Betrieb als Hauptbetrieb eintragen
      if LBetriebsdatum.betriebstyp = 'HB' then
      begin
        LBewirtschafter.Hauptbetrieb := LBetrieb; // Ist NIL wenn der Betrieb noch nicht in der DB ist
        LBewirtschafter.HauptBetriebRegNr := LBetriebsdatum.Betrnr;
      end;
    end;

    LBewirtschafter.Auftragsjahr := APruefbetriebe.Jahr;

    // in den AMA-Daten kommt der Regions-Code als STRING. Also zB "3" für NOE
    var
    LBundeslandCode := StrToIntDef(LBewirtschafter.Bundesland, -1);
    LBundesland := Context.Find<TBundesland>
      .where(
      (M.Bundesland.Region = LBundeslandCode)
      and
      (M.Bundesland.Land.Landkz = 'AT') // AMA ist nur in AT tätig
      ).UniqueResult;
    LBewirtschafter.BLD := LBundesland;

    var
    LMe := ELKERest.Me;
    if not Assigned(LMe) then
      raise EXDataHttpException.Create(482, 'ELKE REST Service NOT assigned');
    LBewirtschafter.BKBNr := LMe.NeueBKBNummer('AMA-CCK');
  end;

end;

// Speichert die gemeldeten Betriebsdaten
constructor TKontrollen.Create;
begin
  ELKERest := TELKERest.Create;
end;

destructor TKontrollen.Destroy;
begin
  FreeAndNil(ELKERest);
  inherited;
end;

procedure TKontrollen.kontrollStatus(StatusMeldung: TAmaKontrollStatus);
begin
  // https://esculenta.atlassian.net/browse/ERT-242
  ELKELog('Kontroll Statusmeldung verarbeiten ...');
  (*
    BKB:       <BKB-Nummer> des entsprechenden Kontrollberichts
    STATUS:    ERR  (Kontrollbericht enthält einen Fehler)
    DAT  (Daten erhalten)
    OK   (Kontrollbericht erledigt)
    INFO (Kontrollbericht enthält einen Fehler)
    Kontakt:   <Name des User Erstkontrolle>
    KOMMENTAR: <Korrekturbedarf>
    BewertungsVersion: Laufende Versionsnummer
  *)

  // StatusMeldung auf Gültigkeit prüfen
  if not Assigned(StatusMeldung) then
  begin
    raise EXDataHttpException.Create(400, 'Status fehlt');
  end;

  // Status-String
  var
  LStatusString := StatusMeldung.Status.Trim.ToUpper;
  var
  LFound := false;
  for var s in AMA_STATI do
  begin
    if LStatusString = s then
    begin
      LFound := true;
      break;
    end;
  end;
  if not LFound then
    raise EXDataHttpException.Create(404, 'Status ' + LStatusString + ' nicht gefunden');

  // BKB prüfen
  var
  LBkbNummer := StatusMeldung.BKB.Trim.ToUpper;

  var
    LBKBKontrolle: TBkbnummer := nil;
  var
    LBKBAuftrag: TBkbnummer := nil;

    // Bkb muss vergeben sein
  var
  LBkb := Context.Find<TBkbnummer>
    .where(M.Bkbnummer.Nummer = LBkbNummer).UniqueResult;
  if not Assigned(LBkb) then
    raise EXDataHttpException.CreateFmt(404, 'Bkb %s nicht vergeben', [LBkbNummer]);

  // Wenn ein Kontrollbericht existiert, dann muss das ein AMA-CCK Bericht sein
  var
  LKontrolle := Context.Find<TKontrollbericht>
    .where(M.Kontrollbericht.BKB = LBkbNummer).UniqueResult;

  if Assigned(LKontrolle) then
  begin
    ELKELog('Kontrolle %d existiert bereits.', [LKontrolle.Id]);
    LBKBKontrolle := LBkb;
    if LKontrolle.Kontrolltyp.Bkbtyp.Typ <> 'AMA-CCK' then
      raise EXDataHttpException.CreateFmt(403,
        'Kontrolle %s ist keine AMA-CCK Kontrolle. Statusmeldung verweigert.', [LBkb.Nummer]);
    if LKontrolle.RefBkb.IsNull then
      raise EXDataHttpException.CreateFmt(403,
        'Kontrolle %s hat keinen zugeortneten AMA-CCK Auftrag (RefBKB). Statusmeldung verweigert.', [LBkb.Nummer]);

    LBKBAuftrag := Context.Find<TBkbnummer>
    .where(M.Bkbnummer.Nummer =  LKontrolle.RefBkb.ValueOrDefault).UniqueResult;
    if LBKBAuftrag = nil then
    raise EXDataHttpException.CreateFmt(403,
        'Kontrolle %s hat keinen keinen gültigen AMA-CCK Auftrag (RefBKB). Statusmeldung verweigert.', [LBkb.Nummer]);
  end
  else
  begin
    // BKB ist keine Kontrolle. Ist BKB ein Auftrag?
    var LAuftrag := Context.Find<TCckAuftrag>
      .where(M.CckAuftrag.BKB = LBkbNummer).UniqueResult;
    if not Assigned(LAuftrag) then
      raise EXDataHttpException.CreateFmt(403,
        'Auftrag %s existiert nicht. Statusmeldung verweigert.', [LBkb.Nummer]);
    //BKB zum Auftrag ist nur bei Status NEU zulässig.
    LBKBAuftrag := LBkb;
    ELKELog('Auftrag %s gefunden.', [LBkb.Nummer]);
  end;

  // Je nach StatusMeldung verarbeiten
  var
  LStatusMeldung := TCckStatusMeldung.Create;
  LStatusMeldung.BKB := LBKBKontrolle;
  LStatusMeldung.BKBAuftrag := LBKBAuftrag;
  LStatusMeldung.Status := Context.Find<TCckStatus>(LStatusString);
  LStatusMeldung.Meldedatum := now;
  LStatusMeldung.Kommentar := StatusMeldung.Kommentar.Trim;
  LStatusMeldung.Kontakt := StatusMeldung.Kontakt.Trim;
  if StatusMeldung.BewertungsVersion > '' then
  begin
    LStatusMeldung.BewertungsVersion := StatusMeldung.BewertungsVersion;
  end
  else
  begin
    LStatusMeldung.BewertungsVersion := SNull;
  end;

  if LStatusString = AMA_STATUS_NEU then
  begin
    // nichts weiter
  end
  else if LStatusString = AMA_STATUS_OK then
  begin
    // nichts weiter
  end
  else if LStatusString = AMA_STATUS_ERR then
  begin
    if (LStatusMeldung.Kommentar = '') or (LStatusMeldung.Kontakt = '') then
      raise EXDataHttpException.CreateFmt(400, 'Der Status %s benötigt einen Kommentar und Kontakt',
        [StatusMeldung.Status]);
  end
  else if LStatusString = AMA_STATUS_INFO then
  begin
    if (LStatusMeldung.Kommentar = '') then
      raise EXDataHttpException.CreateFmt(400, 'Der Status %s benötigt einen Kommentar', [StatusMeldung.Status]);
  end
  else if LStatusString = AMA_STATUS_DAT then
  begin
    // Die Version ist nun in "Bewertungsversion" einzufüllen - wir prüfen das aber nicht!
    // if (LStatusMeldung.Kommentar = '') then
    // raise EXDataHttpException.CreateFmt(400, 'Der Status %s benötigt einen Kommentar', [StatusMeldung.Status]);
  end
  else
    raise EXDataHttpException.Create(400, 'Status ' + LStatusString + ' kann nicht verarbeitet werden');
  Context.Save(LStatusMeldung);
  ELKELog('Status %s für %s wurde erfolgreich gemeldet', [LStatusString, LBkb.Nummer]);
end;

procedure TKontrollen.meldeBetriebe_Speichern(APruefbetriebe: TPruefbetriebe; AErzeugteAuftraege: TErzeugteAuftraege);
begin
  // Holt die Zuordnung von AMA zu VIS Tierarten
  // RefreshQuery(quVisTierarten);
  ELKELog('Auftraege erzeugen ...');
  for var LBewirtschafter in APruefbetriebe.bewirtschafter do
  begin
    var
    LTransaction := Context.Connection.BeginTransaction;
    try
      // Pro Bewirtschafter entsteht ein Auftrag
      var
      LAuftrag := TCckAuftrag.Create;
      with LAuftrag do
      begin
        // Todo: sinnvolle AuftragsID vergeben
        // AuftragsId  := LBewirtschafter.auftragsid;
        Auftragsjahr := LBewirtschafter.Auftragsjahr;

        // Auftragsart vergeben
        // NM: Am leichtesten waeren einfach 'AMA-Direktmeldung' + JAHR + Monat
        var
        LAuftragsart := Format('AMA-Direktmeldung %d-%2.2d', [YearOf(Today), MonthOf(Today)]);
        LAuftrag.auftragsart := Context.Find<TCckAuftragsart>
          .where(M.CckAuftragsart.Bezeichnung = LAuftragsart).UniqueResult;
        if LAuftrag.auftragsart = nil then
        begin
          LAuftrag.auftragsart := TCckAuftragsart.Create;
          LAuftrag.auftragsart.Bezeichnung := LAuftragsart;
          LAuftrag.auftragsart.Gesperrt := 0;
          Context.Save(auftragsart);
        end;

        LfbisHauptbetrieb := LBewirtschafter.HauptBetriebRegNr;
        ELKELog('Erzeuge Auftrag fuer Betrieb: %s', [LfbisHauptbetrieb]);

        BKB := LBewirtschafter.BKBNr;
        Bbknr := LBewirtschafter.Bbknr;
        Bldcode := LBewirtschafter.BLD.Bldcode;
        Vorname := LBewirtschafter.Vorname;
        Nachname := LBewirtschafter.Nachname;
        PlzBew := LBewirtschafter.Plz_Bew.ToString;
        OrtBew := LBewirtschafter.Ort_Bew;
        AdresseBew := LBewirtschafter.Adresse_Bew;
        GemeindekzBew := LBewirtschafter.Gemeindekennzahl_Bew;
        TelFestnetz := LBewirtschafter.Tel_Festnetz;
        Flag1 := LBewirtschafter.Flag_1;
        Flag2 := LBewirtschafter.Flag_2;
        Flag3 := LBewirtschafter.Flag_3;
        Info1 := LBewirtschafter.Info_1;
        Info2 := LBewirtschafter.Info_2;
        Info3 := LBewirtschafter.Info_3;
        IdBearbeiter := Me.Id;
      end;

      // Alle Betriebsdaten des Bewirtschafters im Auftrag speichern
      for var LBetriebsdatum in LBewirtschafter.Betriebsdaten do
      begin
        var
        LCCKBetriebsDatum := TCckBetrieb.Create;
        LAuftrag.Betriebsdaten.Add(LCCKBetriebsDatum);
        // Betriebsdaten uebernehmen
        with LCCKBetriebsDatum do
        begin
          CckAuftrag := LAuftrag;
          betriebstyp := LBetriebsdatum.betriebstyp;
          Betriebsart := LBetriebsdatum.Betriebsart;
          Lfbis := LBetriebsdatum.Betrnr;
          PlzBetr := LBetriebsdatum.Plz_Betr.ToString;
          OrtBetr := LBetriebsdatum.Ort_Betr;
          AdresseBetr := LBetriebsdatum.Adresse_Betr;
          GemeindekzBetr := LBetriebsdatum.Gemeindekennzahl_Betr;
          LnFlaeche := LBetriebsdatum.Ln_Flaeche;
          Tgd := LBetriebsdatum.Tgd;
          var
          LBetrieb := Context.Find<TBetrieb>
            .where(M.Betrieb.Registrierung.Regnr = Lfbis).UniqueResult;
          if Assigned(LBetrieb) then
          begin
            LCCKBetriebsDatum.IdBetrieb := LBetrieb.Id;
          end;
          Tierhalter := LBetriebsdatum.Tierhalter;
        end;
        // Hier schon speichern, weil wir die ID im Verlauf brauchen
        Context.Save(LCCKBetriebsDatum);

        // Auswahldaten fuer jeden Betrieb uebernehmen
        var
        i := 0;
        for var LAuswahlDatum in LBetriebsdatum.Auswahldaten do
        begin
          var
          LAuswahl := TCckAuswahl.Create;
          LCCKBetriebsDatum.CckAuswahldaten.Add(LAuswahl);
          LAuswahl.CckBetriebsdaten := LCCKBetriebsDatum;
          LAuswahl.Auswahldatum := LAuswahlDatum.Auswahldatum;
          LAuswahl.Auswahlgrund := LAuswahlDatum.Auswahlgrund;
          LAuswahl.AuswahlId := i; // Reihenfolge im Array
          inc(i);
          // Modul suchen
          var
          LModul := Context.Find<TCckModul>.where(M.Modul.Modul.ILike(LAuswahlDatum.Modul)).UniqueResult;
          if LModul = nil then
            raise EXDataHttpException.CreateFmt(404, 'Modul "%s" wurde nicht gefunden!', [LAuswahlDatum.Modul]);
          LAuswahl.Modul := LModul;
        end;

        // VOK Sanktionen uebernehmen
        i := 0;
        for var LSanktion in LBetriebsdatum.Vok_Sanktionen do
        begin
          var
          LVOKSanktion := TCckVokSanktion.Create;
          LVOKSanktion.IdCckBetriebsdaten := LCCKBetriebsDatum.Id;
          LVOKSanktion.VokSank := LSanktion.Vok_Sank;
          LVOKSanktion.Jahr := LSanktion.Jahr;
          LVOKSanktion.VokSanktionenId := i; // Reihenfolge im Array
          inc(i);
          Context.SaveOrUpdate(LVOKSanktion); // Todo: Im Modell fehlt aktuell die Beziehung zum Betrieb
        end;

        // Tierdaten uebernehmen
        i := 0;
        for var LTier in LBetriebsdatum.Tierdaten do
        begin
          var
          LCCKTier := TCckTierdaten.Create;
          LCCKTier.IdCckBetriebsdaten := LCCKBetriebsDatum.Id;
          LCCKTier.Tierkategorie := LTier.Tierkategorie;
          LCCKTier.Anzahl := LTier.Anzahl;
          LCCKTier.TierdatenId := i;
          inc(i); // Reihenfolge im Array
          Context.SaveOrUpdate(LCCKTier); // Todo: Im Modell fehlt aktuell die Beziehung zum Betrieb
        end;
      end;

      Context.SaveOrUpdate(LAuftrag);
      AErzeugteAuftraege.Add(LAuftrag, LBewirtschafter);
      // Statusmeldung "NEU"
      var
      LStatusMeldung := TAmaKontrollStatus.Create;
      LStatusMeldung.BKB := LAuftrag.BKB;
      LStatusMeldung.Status := AMA_STATUS_NEU;
      // Todo: Was ist die initiale Bewertungsversion?
      LStatusMeldung.BewertungsVersion := '';
      kontrollStatus(LStatusMeldung);

      LTransaction.Commit;
    except
      LTransaction.Rollback;
      raise;
    end;
  end;
end;

function TKontrollen.meldeKontrollBetriebe(Pruefmeldung: TPruefbetriebe): TErzeugteAuftraege;
begin
  result := TErzeugteAuftraege.Create;
  try
    Requires(ROLE_CCK);
    if Assigned(Pruefmeldung.bewirtschafter) then
    begin
      meldeBetriebe_Zuordnen(Pruefmeldung);
      meldeBetriebe_Speichern(Pruefmeldung, result);
      Context.Flush;
    end
    else
      raise EXDataHttpException.Create(484, 'Keine Pruefauftraege/Bewirtschafter gefunden!');
  except
    on E: Exception do
    begin
      if E is EXDataHttpException then
        raise
      else
        raise EXDataHttpException.Create(481, 'Fehler beim Melden von Betriebsdaten! ' + E.Message);
    end;
  end;
end;

function TKontrollen.PruefErgebnisPDF(dateiId: string): TPruefErgebnisPdf;
begin
  // Todo: DateiID
  Requires(ROLE_CCK);
  var
  LDokument := Context.Find<TDokument>
    .where(M.Dokument.Guid = dateiId)
  // Todo: User/Bundesland and (Linq[M.Dokument.Bldcode] = self.User.Bundesland.Bldcode))
    .UniqueResult;

  if (LDokument = nil) then
    raise EXDataHttpException.CreateFmt(404, 'PDF %s nicht gefunden!', [dateiId])
  else
  begin
    result := TPruefErgebnisPdf.Create;
    result.dateiId := LDokument.Guid.ToString;
    result.dateiName := LDokument.dateiName;
    var
    LBase64Encoder := TBase64Encoding.Create(MaxInt);
    try
      result.Datei := LBase64Encoder.EncodeBytesToString(LDokument.Dokument.AsBytes);
    finally
      FreeAndNil(LBase64Encoder);
    end;
  end;
end;

function TKontrollen.PruefErgebnisseAbholen(abDatumUhrzeit: TDateTime): TPruefErgebnisse;
var
  LDatei: TDatei;
  LErgebnis: TErgebnis;
begin
  Requires(ROLE_CCK);
  var
  LAuftraege := Context.Find<TCckAuftrag>
    .where(M.CckAuftrag.AbgeschlossenAm >= abDatumUhrzeit).List;
  ManagedObjects.Add(LAuftraege);

  // Die Anzahl der Ergebnisse entspricht den Auftraegen
  result := TPruefErgebnisse.Create(LAuftraege.Count);
  // Die (PDF-)Dateien werden beim Durchlaufen der Auftraege ermittelt und gezaehlt
  result.AnzahlDateien := 0;

  var
  i := -1;
  for var LAuftrag in LAuftraege do
  begin
    inc(i);
    LErgebnis := TErgebnis.Create;
    result.Ergebnisse[i] := LErgebnis;
    // Bewertungsblatt PDF
    if LAuftrag.Dokument <> nil then
    begin
      LDatei := TDatei.Create;
      LDatei.dateiId := LAuftrag.Dokument.Guid.ToString;
      LDatei.dateiName := LAuftrag.Dokument.dateiName;
      LErgebnis.Dateien.Add(LDatei);
      result.AnzahlDateien := result.AnzahlDateien + 1;
    end;

    // Das Abholdatum bei uns im System
    LErgebnis.Meldedatum := Today;
    // Das Auftragsjahr
    LErgebnis.Jahr := LAuftrag.Auftragsjahr;

    for var LBetrieb in LAuftrag.Betriebsdaten do
    begin

      // Kontrollbericht ermitteln
      var
      LKontrollbericht := LBetrieb.Kontrollbericht;
      if (LKontrollbericht = nil) or (LKontrollbericht.Dokument = nil) then
        continue;
      // Wenn es noch keinen Kontrollbericht gibt, dann wird hier nichts weiter gemacht

      // Wenn es den Kontrollbericht (als PDF) bereits gibt, aber die CC Version noch nicht, dann generieren
      // wir diese hier

      if LKontrollbericht.DokumentCC = nil then
      begin
        Assert((LKontrollbericht.Kontrollorgan <> nil) and (LKontrollbericht.Kontrollorgan.Users.Count > 0),
          'Kontrollorgan (User) des Kontrolle ' + LKontrollbericht.Id.ToString + ' nicht zugeordnet!');
        TModuleReports.PdfKontrollberichtCCErzeugenUndSpeichern(LKontrollbericht,
          LKontrollbericht.Kontrollorgan.Users.First)
      end;

      // Kontrollbericht CC PDF
      if LKontrollbericht.DokumentCC = nil then
      begin
        // Wenn es für die Kontrolle keinen CC Bericht gibt, dann gehen wir in den Betriebsdaten weiter
        ELKELogError('Für Kontrolle ' + LKontrollbericht.Id.ToString +
          ' konnte kein Kontrollbericht CC PDF erstellt werden! Kontrolle wurde aber in den Betriebsdaten angefordert!');
        continue;
      end
      else
      begin

        LDatei := TDatei.Create;
        LDatei.dateiId := LKontrollbericht.DokumentCC.Guid.ToString;
        LDatei.dateiName := LKontrollbericht.DokumentCC.dateiName;
        LErgebnis.Dateien.Add(LDatei);
        result.AnzahlDateien := result.AnzahlDateien + 1;

        for var LAuswahl in LBetrieb.CckAuswahldaten do
        begin

          if not LKontrollbericht.Endezeit.HasValue then
          begin
            ELKELog('Abgeschlossener CCK Auftrag %d enthält nicht beendete Kontrolle %d. Kontrolle/Auswahldaten werden ignoriert.',
              [LAuftrag.Id, LKontrollbericht.Id], TLoglevel.Warning);
            continue;
          end;
          LErgebnis.BKBNr := LKontrollbericht.BKB;
          if Assigned(LKontrollbericht.Betrieb) then
          begin
            LErgebnis.Bbknr := LKontrollbericht.Betrieb.Bbknr;
            // Todo: Haupt vs Teilbetrieb
            LErgebnis.HauptBetrnr := LKontrollbericht.Betrieb.Registrierung.Regnr;
            if LKontrollbericht.Betrieb.Bundesland <> nil then
            begin
              LErgebnis.Bundesland := LKontrollbericht.Betrieb.Bundesland.Region;
            end;
          end;
          LErgebnis.Version := LAuftrag.Version.ToString;

          LErgebnis.VorankuendigungKz := LKontrollbericht.AngemeldetUm.ValueOrDefault > 0;
          LErgebnis.VorankuendigungDatum := LKontrollbericht.AngemeldetUm;

          // Derzeit nicht vorgesehen
          // LErgebnis.Begruendung := LAuswahl.Auswahlgrund

          var
          LModul := LErgebnis.AddModul;

          LModul.Modul := LAuswahl.Modul.Modul;

          if LAuswahl.Auswahldatum.HasValue then
          begin
            LModul.Auswahldatum := TDate(LAuswahl.Auswahldatum);
          end;
          LModul.Auswahlgrund := LAuswahl.Auswahlgrund;

          if LKontrollbericht.Stati.First.Status = 'S' then
          begin
            // Kontrolle wurde storniert
            LModul.KontrollDatum := SNull;
            LModul.DatStorno := ToDate(LKontrollbericht.StorniertAm);
            LModul.KommentarModul := LKontrollbericht.Stornogrund;
          end
          else if LKontrollbericht.Stati.First.Status = 'V' then
          begin
            // Kontrolle wurde verweigert
            LModul.KontrollDatum := ToDate(LKontrollbericht.VerweigertAm);
            LModul.DatStorno := SNull;
            LModul.KommentarModul := LKontrollbericht.Verweigerungsgrund;
          end
          else
          begin
            // Kontrolle wurde durchgeführt
            LModul.KontrollDatum := ToDate(LKontrollbericht.Endezeit);
            LModul.DatStorno := SNull;
            LModul.KommentarModul := SNull;
          end;

          if LKontrollbericht.Kontrollorgan = nil then
          begin
            ELKELog('Abgeschlossener CCK Auftrag %d enthält Kontrolle %d ohne Kontrollorgan. Kontrolle/Auswahldaten werden ignoriert.',
              [LAuftrag.Id, LKontrollbericht.Id], TLoglevel.Warning);
            continue;
          end;

          LModul.Pruefer := Format('%s %s %s',
            [LKontrollbericht.Kontrollorgan.Titel.ValueOrDefault,
            LKontrollbericht.Kontrollorgan.Vorname,
            LKontrollbericht.Kontrollorgan.Nachname]).Trim.Replace('  ', ' ');

          // Zunächst OK oder Storniert /Verweigert
          if LKontrollbericht.Stati.First.Status = 'S' then
            LModul.StatusKontrolle := 'STO'
          else if LKontrollbericht.Stati.First.Status = 'V' then
            LModul.StatusKontrolle := 'VERW'
          else
            LModul.StatusKontrolle := 'OK';

          // Wenn die Kontrolle verweigert oder storniert wurde, dann hängen wir die Verstoß/Anforderungsdaten NICHT an!
          if not((LKontrollbericht.Stati.First.Status = 'V')
            or (LKontrollbericht.Stati.First.Status = 'S')) then
          begin
            // Nun die Verstoß/Anforderungsdaten aus den Bewertungen anhängen
            for var LBewertung in LAuswahl.CckAuftragsbewertungen do
            begin
              // Neu ab v1.40 - ueber kontrolliert = false erkennen wir vorlaeufig ein Storno fuer ein Modul
              if LBewertung.Kontrolliert = false then
              begin
                // Modul stornieren
                LModul.StatusKontrolle := 'STO';
                LModul.DatStorno := ToDate(LBewertung.Bewertetam);
                LModul.KommentarModul := LBewertung.Bemerkung;
                // Wenn das Modul storniert wurde, gehen wir hier direkt raus und ignorieren die weiteren Bewertungen
                break;
              end;

              var
              LVerstoss := LModul.AddVerstoss;
              LVerstoss.Anforderung := LBewertung.Anforderung.Anforderung;
              LVerstoss.BeanstandungAusmass := LBewertung.Ausmass;
              LVerstoss.BeanstandungDauer := LBewertung.Dauer;
              LVerstoss.BeanstandungSchwere := LBewertung.Schwere;

              // Status_Anf : Mögliche Ausprägungen sind: OK, NOK, NOK_GV, NOK_BEH,  NOT - und beziehen sich auf die einzelnen Anforderungen der Maßnahmen.
              // Wir verwenden vorläufig nur  OK, NOK, NOK_GV
              if LBewertung.Ok then
              begin
                LVerstoss.StatusAnf := 'OK'
              end
              else if not LBewertung.Gerverstossok then
              begin
                LVerstoss.StatusAnf := 'NOK';
                // Hier auch den Gesamtstatus setzen auf Nicht-OK!
                LModul.StatusKontrolle := 'NOK';
              end
              else if LBewertung.Gerverstossok then
              begin
                LVerstoss.StatusAnf := 'NOK_GV';
                // Hier auch den Gesamtstatus setzen auf Nicht-OK!
                LModul.StatusKontrolle := 'NOK';
              end;

              // Historischer Verstoß?
              if LBewertung.KzHv then
              begin
                LVerstoss.StatusAnf := 'NOK_H';
                // Hier auch den Gesamtstatus setzen auf Nicht-OK!
                LModul.StatusKontrolle := 'NOK';
              end;
              LVerstoss.KzHv := LBewertung.KzHv;

              // else
              // raise Exception.Create('Logikfehler "StatusAnf"');

              LVerstoss.VorsatzKz := LBewertung.Vorsatz;
              LVerstoss.KommentarANF := LBewertung.Bemerkung;
            end;
          end;
        end;
      end;
    end;
  end;
end;

initialization

RegisterServiceType(TKontrollen);

end.
