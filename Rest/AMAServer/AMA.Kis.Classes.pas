unit AMA.Kis.Classes;

interface

uses
  System.Classes, System.SysUtils, System.Generics.Collections,
  REST.Json.Types,
  Bcl.Json.Attributes, Bcl.Json.NamingStrategies,
  Aurelius.Types.Nullable, Aurelius.Mapping.Attributes,
  AMA.Classes.Pruefergebnisse;

type

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TKisErgebnis = class(TObject)
  private
    FBerichtsTyp: string;
    FBkbNr: string;
    FBundesland: integer;
    FDateien: TObjectList<TDatei>;
    FHauptBetrnr: string;
    FKontrollEnde: Nullable<TDatetime>;
    FKontrollStatus: string;
    FKontrollTyp: string;
  public
    constructor Create;
    destructor Destroy; override;
    property BerichtsTyp: string read FBerichtsTyp write FBerichtsTyp;
    property BkbNr: string read FBkbNr write FBkbNr;
    property Bundesland: integer read FBundesland write FBundesland;
    property Dateien: TObjectList<TDatei> read FDateien write FDateien;
    property HauptBetrnr: string read FHauptBetrnr write FHauptBetrnr;
    property KontrollEnde: Nullable<TDatetime> read FKontrollEnde write FKontrollEnde;
    property KontrollStatus: string read FKontrollStatus write FKontrollStatus;
    property KontrollTyp: string read FKontrollTyp write FKontrollTyp;
  end;

  [Model('AMA')]
  [JsonNamingStrategy(TCamelCaseNamingStrategy)]
  TKisKontrollen = class(TObject)
  private
    FAnzahlDateien: integer;
    FErgebnisse: TArray<TKisErgebnis>;
  public
    constructor Create(AAnzahl: integer);
    destructor Destroy; override;
    property AnzahlDateien: integer read FAnzahlDateien write FAnzahlDateien;
    property Ergebnisse: TArray<TKisErgebnis> read FErgebnisse write FErgebnisse;
  end;

implementation

constructor TKisKontrollen.Create(AAnzahl: integer);
begin
  inherited Create;
  FAnzahlDateien := 0;
  SetLength(FErgebnisse, AAnzahl);
  for var i := 0 to AAnzahl - 1 do
  begin
    FErgebnisse[i] := TKisErgebnis.Create;
  end;
end;

destructor TKisKontrollen.Destroy;
var
  LItem: TObject;
begin
  for LItem in FErgebnisse do
    LItem.Free;
  inherited;
end;

constructor TKisErgebnis.Create;
begin
  inherited;
  FDateien := TObjectList<TDatei>.Create;
end;

destructor TKisErgebnis.Destroy;
begin
  FreeAndNil(FDateien);
  inherited;
end;

end.
