﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <Base>True</Base>
        <AppType>Application</AppType>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>VCL</FrameworkType>
        <MainSource>AMAApiServer.dpr</MainSource>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <ProjectGuid>{F9989BBA-79E3-4A2F-8F3D-89C02ED04C79}</ProjectGuid>
        <ProjectName Condition="'$(ProjectName)'==''">AMAApiServer</ProjectName>
        <ProjectVersion>20.2</ProjectVersion>
        <TargetedPlatforms>1</TargetedPlatforms>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>AMAApiServer</SanitizedProjectName>
        <DCC_DcuOutput>.\$(Platform)\$(Config)\DCU</DCC_DcuOutput>
        <DCC_Define>REST;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>.\$(Platform)\$(Config)</DCC_ExeOutput>
        <DCC_Namespace>System;Xml;Data;Datasnap;Web;Soap;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;$(DCC_Namespace)</DCC_Namespace>
        <DCC_SYMBOL_PLATFORM>false</DCC_SYMBOL_PLATFORM>
        <DCC_UNIT_PLATFORM>false</DCC_UNIT_PLATFORM>
        <DCC_UnitSearchPath>..\..\ESCore;..\..\ESCore\classes;..\..\ESCore\IntraWeb;..\..\ESCore\model;..\..\_libs;..\..\_libs\dx-library;..\..\_libs\FastMM4;..\..\_libs\FastReport\FastCore\VCL\Sources;..\..\_libs\FastReport\FastGraphics\VCL\Sources;..\..\_libs\FastReport\FastLocalization\VCL\Sources;..\..\_libs\FastReport\FastQueryBuilder\VCL\Sources;..\..\_libs\FastReport\FastReport\VCL\Sources;..\..\_libs\FastReport\FastScript\VCL\Sources;..\..\_libs\TMSAurelius\source;..\..\_libs\TMSAurelius\source\core;..\..\_libs\TMSAurelius\source\drivers;..\..\_libs\TMSBCL\source;..\..\_libs\TMSBCL\source\core;..\..\_libs\TMSBCL\source\core\common;..\..\_libs\TMSBCL\source\extra;..\..\_libs\TMSSparkle\source;..\..\_libs\TMSSparkle\source\app;..\..\_libs\TMSSparkle\source\core;..\..\_libs\TMSSparkle\source\core\common;..\..\_libs\TMSSparkle\source\extra;..\..\_libs\TMSXData\source;..\..\_libs\TMSXData\source\core;..\..\_libs\TMSXData\source\core\common;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
        <DCC_XML_CREF_NO_RESOLVE>false</DCC_XML_CREF_NO_RESOLVE>
        <DCC_XML_EXPECTED_CHARACTER>false</DCC_XML_EXPECTED_CHARACTER>
        <DCC_XML_INVALID_NAME>false</DCC_XML_INVALID_NAME>
        <DCC_XML_INVALID_NAME_START>false</DCC_XML_INVALID_NAME_START>
        <DCC_XML_NO_MATCHING_PARM>false</DCC_XML_NO_MATCHING_PARM>
        <DCC_XML_NO_PARM>false</DCC_XML_NO_PARM>
        <DCC_XML_UNKNOWN_ENTITY>false</DCC_XML_UNKNOWN_ENTITY>
        <DCC_XML_WHITESPACE_NOT_ALLOWED>false</DCC_XML_WHITESPACE_NOT_ALLOWED>
        <Icon_MainIcon>$(BDS)\bin\delphi_PROJECTICON.ico</Icon_MainIcon>
        <PreBuildEvent>
            <![CDATA[del ($OUTPUTPATH)
if not exist $(OUTPUTDIR)XMLDoc\AMAApiServer\nul md $(OUTPUTDIR)XMLDoc\AMAApiServer 
$(PreBuildEvent)]]>
        </PreBuildEvent>
        <VerInfo_AutoGenVersion>true</VerInfo_AutoGenVersion>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Keys>CompanyName=EsCulenta GmbH;FileDescription=Implementiert den AMA CCK &amp; KIS REST Server;FileVersion=********;InternalName=$(MSBuildProjectName);OriginalFilename=AMAApiServer.exe;ProgramID=at.esculenta.$(MSBuildProjectName);ProductName=AMA CCK &amp; KIS API Server;ProductVersion=1.64.2;LegalCopyright=©2020 - 2025 EsCulenta GmbH</VerInfo_Keys>
        <VerInfo_Locale>3079</VerInfo_Locale>
        <VerInfo_MinorVer>64</VerInfo_MinorVer>
        <VerInfo_Release>2</VerInfo_Release>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <AppDPIAwarenessMode>PerMonitorV2</AppDPIAwarenessMode>
        <AppEnableRuntimeThemes>true</AppEnableRuntimeThemes>
        <BT_BuildType>Debug</BT_BuildType>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <DCC_UsePackage>DBXSqliteDriver;RESTComponents;fmxase;DBXDb2Driver;DBXInterBaseDriver;CData.Gmail.D26;VCLTMSFNCChartPkgDXE12;vclactnband;vclFireDAC;emsclientfiredac;IWBootstrapD103;tethering;svnui;DataSnapFireDAC;FireDACADSDriver;SoapServerEntities;DBXMSSQLDriver;TMSScripter;DatasnapConnectorsFreePascal;FireDACMSSQLDriver;FMXTMSFNCUIPackPkgDXE12;vcltouch;vcldb;bindcompfmx;svn;TMSWEBCorePkgLibDXE12;DBXOracleDriver;inetdb;FmxTeeUI;emsedge;TMSScripter_Memo;fmx;FireDACIBDriver;fmxdae;office2K;FireDACDBXDriver;dbexpress;IndyCore;xdata;vclx;dsnap;emsclient;DataSnapCommon;FireDACCommon;FireDACX_D103;VCLTMSFNCUIPackPkgDXE12;VCLTMSFNCCloudPackPkgDXE12;RESTBackendComponents;DataSnapConnectors;VCLRESTComponents;CData.Twilio.D26;soapserver;TMSVCLUIPackPkgWizDXE12;TMSScripter_Legacy;CData.WordPress.D26;vclie;TMSVCLUIPackPkgDXE12;bindengine;DBXMySQLDriver;CloudService;FireDACOracleDriver;FireDACMySQLDriver;DBXFirebirdDriver;TMSScripter_VCL;FireDACCommonODBC;FireDACCommonDriver;TMSFMXWebGMapsPkgDXE12;DataSnapClient;VCLTMSFNCCorePkgDXE12;inet;IndyIPCommon;bindcompdbx;vcl;IndyIPServer;DBXSybaseASEDriver;sparkle;tmsbcl;IndySystem;FAS.MyComponents;FireDACDb2Driver;ElkeClasses;dsnapcon;madExcept_;VirtualTreesR;FMXTMSFNCCorePkgDXE12;FireDACMSAccDriver;fmxFireDAC;FireDACInfxDriver;vclimg;madBasic_;TeeDB;FireDAC;emshosting;FireDACSqliteDriver;FireDACPgDriver;FireDACASADriver;DBXOdbcDriver;FireDACTDataDriver;FMXTee;TMSVCLUIPackPkgXlsDXE12;IWBootstrap4D103;soaprtl;DbxCommonDriver;Tee;DataSnapServer;xmlrtl;soapmidas;DataSnapNativeClient;fmxobj;vclwinx;FireDACDSDriver;rtl;emsserverresource;DbxClientDriver;madDisAsm_;DBXSybaseASADriver;TMSScripter_IDE;CustomIPTransport;vcldsnap;bindcomp;appanalytics;TMSVCLUIPackPkgExDXE12;DBXInformixDriver;FMXTMSFNCCloudPackPkgDXE12;IndyIPClient;TMSWEBCorePkgDXE12;bindcompvcl;CData.Bing.D26;TMSCryptoPkgDXE12;TeeUI;dbxcds;VclSmp;KernowSoftwareFMX;adortl;FireDACODBCDriver;FixInsight_10_3;Intraweb_15_D10_3;DataSnapIndy10ServerTransport;aurelius;dsnapxml;DataSnapProviderClient;dbrtl;IndyProtocols;inetdbxpress;FireDACMongoDBDriver;TMSCryptoPkgDEDXE12;FMXTMSFNCChartPkgDXE12;DataSnapServerMidas;$(DCC_UsePackage)</DCC_UsePackage>
        <Icon_MainIcon>icons8-api 2.ico</Icon_MainIcon>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <DCC_DebugDCUs>true</DCC_DebugDCUs>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <DCC_Define>DEBUG;OM;$(DCC_Define)</DCC_Define>
        <DCC_GenerateStackFrames>true</DCC_GenerateStackFrames>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_RemoteDebug>true</DCC_RemoteDebug>
        <VerInfo_Debug>true</VerInfo_Debug>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <DCC_OutputXMLDocumentation>true</DCC_OutputXMLDocumentation>
        <DCC_RemoteDebug>false</DCC_RemoteDebug>
        <DCC_XmlOutput>.\$(Platform)\$(Config)\XMLDoc\AMAApiServer</DCC_XmlOutput>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <DCC_DebugInformation>0</DCC_DebugInformation>
        <DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
        <DCC_ExeOutput>..\..\_Release-Binaries\$(Platform)</DCC_ExeOutput>
        <DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
        <DCC_OutputXMLDocumentation>true</DCC_OutputXMLDocumentation>
        <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
        <DCC_XmlOutput>..\..\_Release-Binaries\$(Platform)\XMLDoc\AMAApiServer</DCC_XmlOutput>
        <PostBuildEvent>
            <![CDATA[$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -copy
$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -git
$(PostBuildEvent)]]>
        </PostBuildEvent>
        <PreBuildEvent>
            <![CDATA[if not exist $(OUTPUTDIR)XMLDoc\AMAApiServer\nul md $(OUTPUTDIR)XMLDoc\AMAApiServer
$(PreBuildEvent)]]>
        </PreBuildEvent>
    </PropertyGroup>
    <ItemGroup>
        <DelphiCompile Include="$(MainSource)">
            <MainSource>MainSource</MainSource>
        </DelphiCompile>
        <DCCReference Include="AMA.Server.Service.Intf.pas"/>
        <DCCReference Include="AMA.Server.Configuration.pas"/>
        <DCCReference Include="AMA.Server.Forms.Main.pas"/>
        <DCCReference Include="AMA.Server.Modules.ServerContainer.pas"/>
        <DCCReference Include="AMA.Server.Service.Impl.pas"/>
        <DCCReference Include="AMA.Classes.Pruefergebnisse.pas"/>
        <DCCReference Include="AMA.Classes.Pruefbetriebe.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Configuration.pas"/>
        <DCCReference Include="..\..\Reports\Modules.Reports.ELKE.pas"/>
        <DCCReference Include="..\..\Reports\Modules.Reports.AMA.pas"/>
        <DCCReference Include="..\..\Reports\Modules.Reports.Base.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Admin.Intf.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Admin.Impl.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Modules.ServerContainer.Base.pas">
            <Form>ServerContainerBase</Form>
            <FormType>dfm</FormType>
            <DesignClass>TDataModule</DesignClass>
        </DCCReference>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.RESTError.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Request.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.PVP.Token.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.PVP.Roles.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Logging.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Generated.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Classes.Admin.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Service.pas">
            <Form>ELKEService</Form>
            <FormType>dfm</FormType>
            <DesignClass>TService</DesignClass>
        </DCCReference>
        <DCCReference Include="AMA.Kis.Classes.pas"/>
        <DCCReference Include="AMA.Kis.Server.Service.Intf.pas"/>
        <DCCReference Include="AMA.Kis.Server.Service.Impl.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Services.Me.Intf.pas"/>
        <DCCReference Include="AMA.Classes.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Logger.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Server.Configuration.Base.pas"/>
        <DCCReference Include="..\..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas"/>
        <DCCReference Include="..\ELKE.REST.Service.Base.pas"/>
        <RcItem Include="CCKTEST.pdf">
            <ContainerId>ResourceItem</ContainerId>
            <ResourceType>RCDATA</ResourceType>
            <ResourceId>CCKTEST</ResourceId>
        </RcItem>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <ProjectExtensions>
        <Borland.Personality>Delphi.Personality.12</Borland.Personality>
        <Borland.ProjectType>Application</Borland.ProjectType>
        <BorlandProject>
            <Delphi.Personality>
                <Source>
                    <Source Name="MainSource">AMAApiServer.dpr</Source>
                </Source>
                <Excluded_Packages/>
            </Delphi.Personality>
            <Platforms>
                <Platform value="Android">False</Platform>
                <Platform value="Android64">False</Platform>
                <Platform value="Linux64">False</Platform>
                <Platform value="OSX64">False</Platform>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
                <Platform value="iOSDevice64">False</Platform>
            </Platforms>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Project="$(BDS)\Bin\CodeGear.Delphi.Targets" Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')"/>
    <Import Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj" Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
    <PropertyGroup Condition="'$(Config)'=='Debug' And '$(Platform)'=='Win32'">
        <PreBuildEvent>del ($OUTPUTPATH)&amp;&amp;if not exist $(OUTPUTDIR)XMLDoc\AMAApiServer\nul md $(OUTPUTDIR)XMLDoc\AMAApiServer </PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent/>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' And '$(Platform)'=='Win32'">
        <PreBuildEvent>if not exist $(OUTPUTDIR)XMLDoc\AMAApiServer\nul md $(OUTPUTDIR)XMLDoc\AMAApiServer&amp;&amp;del ($OUTPUTPATH)&amp;&amp;if not exist $(OUTPUTDIR)XMLDoc\AMAApiServer\nul md $(OUTPUTDIR)XMLDoc\AMAApiServer </PreBuildEvent>
        <PreBuildEventIgnoreExitCode>False</PreBuildEventIgnoreExitCode>
        <PreLinkEvent/>
        <PreLinkEventIgnoreExitCode>False</PreLinkEventIgnoreExitCode>
        <PostBuildEvent>$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -copy
&amp;&amp;$(PROJECTDIR)\..\DXVersion $(OUTPUTPATH) -git
</PostBuildEvent>
        <PostBuildEventIgnoreExitCode>False</PostBuildEventIgnoreExitCode>
    </PropertyGroup>
</Project>
