program AMAApiServer;

{$R *.dres}

uses
  FastMM4,
  Vcl.Forms,
  AMA.Server.Service.Intf in 'AMA.Server.Service.Intf.pas',
  AMA.Server.Configuration in 'AMA.Server.Configuration.pas',
  AMA.Server.Forms.Main in 'AMA.Server.Forms.Main.pas',
  AMA.Server.Modules.ServerContainer in 'AMA.Server.Modules.ServerContainer.pas',
  AMA.Server.Service.Impl in 'AMA.Server.Service.Impl.pas',
  AMA.Classes.Pruefergebnisse in 'AMA.Classes.Pruefergebnisse.pas',
  AMA.Classes.Pruefbetriebe in 'AMA.Classes.Pruefbetriebe.pas',
  ELKE.Server.Configuration in '..\..\ESCore\classes\ELKE.Server.Configuration.pas',
  Modules.Reports.ELKE in '..\..\Reports\Modules.Reports.ELKE.pas',
  Modules.Reports.AMA in '..\..\Reports\Modules.Reports.AMA.pas',
  Modules.Reports.Base in '..\..\Reports\Modules.Reports.Base.pas',
  ELKE.Sparkle.Middleware.ReverseProxy in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.ReverseProxy.pas',
  ELKE.Sparkle.Middleware.PVPAuth in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.PVPAuth.pas',
  ELKE.Services.Admin.Intf in '..\..\ESCore\classes\ELKE.Services.Admin.Intf.pas',
  ELKE.Services.Admin.Impl in '..\..\ESCore\classes\ELKE.Services.Admin.Impl.pas',
  ELKE.Server.Modules.ServerContainer.Base in '..\..\ESCore\classes\ELKE.Server.Modules.ServerContainer.Base.pas' {ServerContainerBase: TDataModule},
  ELKE.Classes.RESTError in '..\..\ESCore\classes\ELKE.Classes.RESTError.pas',
  ELKE.Classes.Request in '..\..\ESCore\classes\ELKE.Classes.Request.pas',
  ELKE.Classes.PVP.Token in '..\..\ESCore\classes\ELKE.Classes.PVP.Token.pas',
  ELKE.Classes.PVP.Roles in '..\..\ESCore\classes\ELKE.Classes.PVP.Roles.pas',
  ELKE.Classes in '..\..\ESCore\classes\ELKE.Classes.pas',
  ELKE.Classes.Logging in '..\..\ESCore\classes\ELKE.Classes.Logging.pas',
  ELKE.Classes.Generated in '..\..\ESCore\classes\ELKE.Classes.Generated.pas',
  ELKE.Classes.Generated.Dictionary in '..\..\ESCore\classes\ELKE.Classes.Generated.Dictionary.pas',
  ELKE.Classes.Admin in '..\..\ESCore\classes\ELKE.Classes.Admin.pas',
  ELKE.Service in '..\..\ESCore\classes\ELKE.Service.pas' {ELKEService: TService},
  AMA.Kis.Classes in 'AMA.Kis.Classes.pas',
  AMA.Kis.Server.Service.Intf in 'AMA.Kis.Server.Service.Intf.pas',
  AMA.Kis.Server.Service.Impl in 'AMA.Kis.Server.Service.Impl.pas',
  ELKE.Services.Me.Intf in '..\..\ESCore\classes\ELKE.Services.Me.Intf.pas',
  AMA.Classes in 'AMA.Classes.pas',
  ELKE.Server.Logger in '..\..\ESCore\classes\ELKE.Server.Logger.pas',
  ELKE.Server.Configuration.Base in '..\..\ESCore\classes\ELKE.Server.Configuration.Base.pas',
  ELKE.Sparkle.Middleware.TokenAuthGenEndpoints in '..\..\ESCore\classes\ELKE.Sparkle.Middleware.TokenAuthGenEndpoints.pas',
  ELKE.REST.Service.Base in '..\ELKE.REST.Service.Base.pas';

{$R *.res}

begin
  ReportMemoryLeaksOnShutdown := DebugHook <> 0;
  if TELKEService.IsService then
  begin
    TELKEService.Run(TServerContainer);
  end
  else
  begin
    Application.Initialize;
    Application.MainFormOnTaskbar := True;
    Application.CreateForm(TServerContainer, ServerContainer);
  Application.CreateForm(TFormMainServer, FormMainServer);
  Application.Run;
  end;

end.

